/*
 * FilePath     : \src\api\VirtualBed.js
 * Author       : 张现忠
 * Date         : 2020-10-24 11:44
 * LastEditors  : 胡洋
 * LastEditTime : 2020-11-04 10:37
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/VirtualBedList";

export const urls = {
  // 获取单个虚拟病区内的所有床位
  GetAllVirtualBedList: baseUrl + "/GetAllVirtualBedList",
  // 删除虚拟床位
  DeleteVirtualBedList: baseUrl + "/DeleteVirtualBedList",
  // 新增虚拟床位
  SaveVirtualBedList: baseUrl + "/SaveVirtualBedList"
};
// 获取单个病区内的虚拟床位
export const GetAllVirtualBedList = params => {
  return http.get(urls.GetAllVirtualBedList, params);
};
// 删除虚拟床位
export const DeleteVirtualBedList = params => {
  return http.post(urls.DeleteVirtualBedList, qs.stringify(params));
};
// 新增虚拟床位
export const SaveVirtualBedList = params => {
  return http.post(urls.SaveVirtualBedList, params);
};
