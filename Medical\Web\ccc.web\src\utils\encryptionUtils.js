/*
 * FilePath     : \src\utils\encryptionUtils.js
 * Author       : 苏军志
 * Date         : 2022-11-18 16:09
 * LastEditors  : 来江禹
 * LastEditTime : 2022-11-25 16:51
 * Description  : 加密/解密 工具类
 * CodeIterationRecord: 
 */

/**
 * description: 加密方法
 * param {*} str
 * return {*}
 */
export const encryption = (str) => {
  if (!str) {
    return str;
  }
  let newStr = "";
  let charArray = Array.from(str);
  let newChar = 0;
  for (let i = 0; i < charArray.length; i++) {
    let ascii = str.charCodeAt(i)
    if (i % 2 == 0) {
      newChar = ascii + i - 32;
    }
    else {
      newChar = ascii - i + 8;
    }
    newStr = newStr + String.fromCharCode(newChar);
  }

  //转utf-8编码
  let buffer = Buffer.from(newStr, 'utf-8');
  //转Base64
  let base64Str = buffer.toString('base64')
  let encryptionStr = base64Str.replace("+", "-").replace("/", "_");
  return encryptionStr
}

/**
 * description: 解密方法
 * param {*} encryptionStr
 * return {*}
 */
export const decryption = (encryptionStr) => {
  if (!encryptionStr) {
    return encryptionStr
  }
  //解析Base64
  let str = Buffer.from(encryptionStr, 'base64').toString('utf-8')
  var newStr = "";
  let charArray = Array.from(str);
  let newChar = 0;
  for (let i = 0; i < charArray.length; i++) {
    let ascii = str.charCodeAt(i)
    if (i % 2 == 0) {
      newChar = ascii - i + 32;
    }
    else {
      newChar = ascii + i - 8;
    }
    newStr = newStr + String.fromCharCode(newChar);
  }
  return newStr;
}