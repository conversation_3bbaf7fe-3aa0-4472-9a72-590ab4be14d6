<!--
 * FilePath     : \src\pages\transferHisCommon\transferTestCollection.vue
 * Author       : 张现忠
 * Date         : 2022-01-19 08:46
 * LastEditors  : 张现忠
 * LastEditTime : 2022-01-24 09:44
 * Description  : 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { hisHisCommonUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
    }),
  },
  created() {
    this.url = hisHisCommonUrl() + "testCollection?token=" + this.token;
  },
};
</script>

