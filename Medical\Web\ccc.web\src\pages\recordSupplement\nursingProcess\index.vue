<!--
 * FilePath     : \src\pages\recordSupplement\nursingProcess\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-08-10 09:41
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-17 14:15
 * Description  : 护理流程补录主页面
-->
<template>
  <base-layout header-height="auto" class="nursing-process">
    <search-patient-data
      class="patient-info"
      slot="header"
      @selectPatientData="selectPatientData"
      @change="change"
    ></search-patient-data>
    <div class="nursing-process-tabs">
      <el-tabs class="tabs" v-if="showFlag" v-model="activeName">
        <el-tab-pane
          v-for="(component, index) in components"
          :key="index"
          :label="component.label"
          :name="component.name"
        >
          <component :is="component.name" :patient="patient" :index="randomIndex"></component>
        </el-tab-pane>
      </el-tabs>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData";
import risk from "./components/risk";
import nursingPlan from "./components/nursingPlan";
import assessRecord from "@/pages/recordSupplement/assessRecord/index";
export default {
  components: {
    baseLayout,
    searchPatientData,
    risk,
    nursingPlan,
    assessRecord,
  },
  data() {
    return {
      patient: undefined,
      randomIndex: 1,
      showFlag: false,
      activeName: "assessRecord",
      components: [
        {
          label: "护理评估",
          name: "assessRecord",
        },
        {
          label: "风险",
          name: "risk",
        },
        {
          label: "护理计划",
          name: "nursingPlan",
        },
      ],
    };
  },
  methods: {
    change() {
      this.patient = undefined;
      this.randomIndex = 1;
      this.activeName = "assessRecord";
      this.showFlag = false;
    },
    //查询病人
    async selectPatientData(patient) {
      this.showFlag = true;
      this.patient = patient;
      this.randomIndex = Math.random();
    },
  },
};
</script>

<style lang="scss" >
.nursing-process {
  height: 100%;
  .nursing-process-tabs {
    height: 100%;
    .tabs {
      height: 100%;
      .el-tabs__content {
        height: calc(100% - 56px);
        .el-tab-pane {
          height: 100%;
          .el-tabs__content {
            height: calc(100% - 80px);
          }
        }
      }
    }
  }
}
</style>