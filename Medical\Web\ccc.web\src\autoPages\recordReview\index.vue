<!--
 * FilePath     : \src\autoPages\recordReview\index.vue
 * Author       : 孟昭永
 * Date         : 2023-09-20 09:56
 * LastEditors  : 孟昭永
 * LastEditTime : 2024-03-12 11:57
 * Description  : 病历审核
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="record-review" header-height="auto">
    <span class="record-review-top" slot="header">
      <span>开始日期:</span>
      <el-date-picker
        class="start-date"
        v-model="startDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="开始日期"
      ></el-date-picker>
      <span>结束日期:</span>
      <el-date-picker
        class="end-date"
        v-model="endDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="结束日期"
      ></el-date-picker>
      <span>住院号:</span>
      <el-input
        class="search-input"
        placeholder="请输入内容"
        v-model="localCaseNumber"
        @keyup.enter.native="getRecordReviewList"
      >
        <i slot="append" class="iconfont icon-search" @click="getRecordReviewList"></i>
      </el-input>
      <span>待审核:</span>
      <el-switch v-model="needReviewFlag"></el-switch>
      <span v-if="recordsVerifierSignVersion == 'RecordsVerifierSignV1'">
        <span>出科患者:</span>
        <el-switch v-model="dischargedFlag"></el-switch>
      </span>
      <span v-if="recordsVerifierSignVersion == 'RecordsVerifierSignV2'">
        <span>转出患者:</span>
        <el-switch v-model="transferPatientFlag"></el-switch>
        <span>出院患者:</span>
        <el-switch v-model="dischargedPatientFlag"></el-switch>
      </span>

      <el-button class="query-button" icon="iconfont icon-search" @click="getRecordReviewList">查询</el-button>
    </span>
    <!-- 动态表头 -->
    <packaging-table v-model="recordReviewList" :headerList="mainTableHeaderList" ref="recordReviewTable">
      <!-- 操作 插槽-->
      <div slot="operate" slot-scope="scope">
        <el-tooltip content="病案审核">
          <div class="iconfont icon-preview" @click="recordReview(scope.row)"></div>
        </el-tooltip>
      </div>
    </packaging-table>
    <!-- 病案审核对话框 -->
    <el-dialog
      v-dialogDrag
      fullscreen
      v-loading="dialogLoading"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      :title="dialogTitle"
      @close="closeDialog"
      custom-class="link no-footer"
      element-loading-text="加载中……"
    >
      <el-container class="record-review-container">
        <el-aside class="record-review-aside">
          <el-button class="review-button" type="primary" @click="batchReviewFile">批量审核</el-button>
          <el-table
            ref="fileData"
            :data="emrFileList"
            border
            stripe
            highlight-current-row
            height="calc(100% - 45px)"
            @row-click="loadFile"
            v-loading="fileTableLoading"
          >
            <el-table-column type="selection" :width="convertPX(40)"></el-table-column>
            <el-table-column prop="showName" label="病历名称" :max-width="convertPX(300)"></el-table-column>
            <el-table-column prop="reviewersName" label="审核人" :width="convertPX(120)">
              <template slot-scope="scope">
                <div v-html="scope.row.reviewersName"></div>
              </template>
            </el-table-column>
          </el-table>
        </el-aside>
        <el-main class="record-review-main">
          <iframe
            class="record-iframe"
            :src="urlPDF"
            type="application/x-google-chrome-pdf"
            frameborder="0"
            scrolling="auto"
          />
          <el-button class="review-button" v-if="approvedFlag && selectFlag" type="primary" @click="reviewFileRefuse">
            取消审核
          </el-button>
          <el-button
            class="review-button"
            v-if="!approvedFlag && selectFlag"
            type="primary"
            @click="reviewFileApproved"
          >
            审核通过
          </el-button>
        </el-main>
        <progress-view v-show="progressFlag" @closeProgress="restoreProgress" :tableData="messageData"></progress-view>
      </el-container>
    </el-dialog>
  </base-layout>
</template>
<script>
import { GetPatientDocumentByID } from "@/api/Document";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { GetOneSettingByTypeAndCode } from "@/api/Setting";
import { GetEmrFileList, GetRecordReviewList, ReviewFileApproved, ReviewFileRefuse } from "@/api/VerifyRecord";
import baseLayout from "@/components/BaseLayout";
import progressView from "@/components/progressView";
import packagingTable from "@/components/table/index";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    progressView,
    packagingTable,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      //开始日期
      startDate: this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -7, "yyyy-MM-dd"),
      //结束日期
      endDate: this._datetimeUtil.getNowDate(),
      //病区ID
      stationID: undefined,
      //住院号
      localCaseNumber: undefined,
      //待审核
      needReviewFlag: true,
      //出科患者标记
      dischargedFlag: true,
      //表格遮罩
      tableLoading: false,
      //文件列表表格遮罩
      fileTableLoading: false,
      //审核清单
      recordReviewList: [],
      //对话框
      dialogVisible: false,
      //电子病历文件列表
      emrFileList: [],
      //pdf文件地址
      urlPDF: undefined,
      //对话框标题
      dialogTitle: undefined,
      //选中病历
      nurseEMRFileListID: undefined,
      //审核通过标记
      approvedFlag: false,
      //患者ID
      inpatientID: undefined,
      //当前选中的文件
      currentFile: undefined,
      //选中文件标记
      selectFlag: false,
      //进度条标志
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      //加载弹框
      dialogLoading: false,
      //转出待审核
      transferPatientFlag: false,
      //出院待审核
      dischargedPatientFlag: false,
      //表格列
      mainTableHeaderList: [],
      //表格数据
      careMainTableData: [],
      //病历审核版本
      recordsVerifierSignVersion: undefined,
    };
  },
  async created() {
    await this.getRecordsVerifierSignVersion();
    await this.getMainTableHeaderList();
    this.getRecordReviewList();
  },
  methods: {
    /**
     * description: 获取使用的病历审核版本
     * return {*}
     */
    async getRecordsVerifierSignVersion() {
      let param = {
        settingType: 308,
        settingCode: "GetRecordsVerifierSignVersion",
      };
      await GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.recordsVerifierSignVersion = response.data.typeValue;
        }
      });
    },
    /**
     * description: 获取动态表头
     * return {*}
     */
    async getMainTableHeaderList() {
      this.mainTableHeaderList = [];
      let params = {
        fileClassID: 100000,
        fileClassSub: this.recordsVerifierSignVersion,
        useDescription: "1||Table",
      };
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.mainTableHeaderList = res.data;
        }
      });
    },
    /**
     * description: 获取需要审核人员清单
     * return {*}
     */
    getRecordReviewList() {
      this.tableLoading = true;
      let params = {
        startDate: this.startDate,
        endDate: this.endDate,
        localCaseNumber: this.localCaseNumber,
        needReviewFlag: this.needReviewFlag,
        dischargedFlag: this.dischargedFlag,
        transferPatientFlag: this.transferPatientFlag,
        dischargedPatientFlag: this.dischargedPatientFlag,
      };
      // 使用RecordsVerifierSignV2时，dischargedFlag为false
      if (this.recordsVerifierSignVersion == "RecordsVerifierSignV2") {
        params.dischargedFlag = false;
      }
      // 使用RecordsVerifierSignV1时，transferPatientFlag和dischargedPatientFlag为false
      if (this.recordsVerifierSignVersion == "RecordsVerifierSignV1") {
        params.transferPatientFlag = false;
        params.dischargedPatientFlag = false;
      }
      GetRecordReviewList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.recordReviewList = res.data;
          this.$nextTick(() => {
            this.$refs.recordReviewTable?.doLayout();
          });
        }
        this.tableLoading = false;
      });
    },
    /**
     * description: 点击审核打开弹窗
     * param {*} item 当前行
     * return {*}
     */
    async recordReview(item) {
      this.urlPDF = undefined;
      this.selectFlag = false;
      this.dialogVisible = true;
      this.dialogLoading = true;
      this.dialogTitle =
        item.bedNumber + "-" + item.patientName + "【" + item.gender + "-" + item.ageDetail + "】" + "-- 病历审核";
      this.inpatientID = item.inpatientID;
      await this.getEmrFileList();
      await this.setSelectRow();
      this.dialogLoading = false;
    },
    /**
     * description: 获取需要审核的文件列表
     * return {*}
     */
    async getEmrFileList() {
      this.fileTableLoading = true;
      this.emrFileList = [];
      let params = {
        inpatientID: this.inpatientID,
      };
      await GetEmrFileList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.emrFileList = res.data;
        }
        this.fileTableLoading = false;
      });
    },
    /**
     * description: 设置选中行
     * return {*}
     */
    async setSelectRow() {
      if (this.emrFileList && this.emrFileList.length > 0) {
        if (!this.currentFile) {
          this.$nextTick(() => {
            this.$refs.fileData.setCurrentRow(this.emrFileList[0]);
          });
          await this.loadFile(this.emrFileList[0]);
          return;
        }
        let row = this.emrFileList.find((item) => item.nurseEMRFileListID == this.currentFile.nurseEMRFileListID);
        if (row) {
          this.$nextTick(() => {
            this.$refs.fileData.setCurrentRow(row);
          });
          await this.loadFile(row);
        } else {
          this.$nextTick(() => {
            this.$refs.fileData.setCurrentRow(this.emrFileList[0]);
          });
          await this.loadFile(this.emrFileList[0]);
        }
      }
    },
    /**
     * description: 加载选中文件
     * param {*} item 当前行
     * return {*}
     */
    async loadFile(item) {
      this.dialogLoading = true;
      this.urlPDF = undefined;
      this.currentFile = item;
      this.selectFlag = true;
      this.approvedFlag = false;
      if (
        this.user &&
        item.reviewersID &&
        item.reviewersID.includes(this.user.userID) &&
        item.reviewersStationID &&
        item.reviewersStationID.includes(this.user.stationID.toString())
      ) {
        this.approvedFlag = true;
      }
      this.nurseEMRFileListID = item.nurseEMRFileListID;
      let params = {
        ID: item.nurseEMRFileListID,
      };
      await GetPatientDocumentByID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.urlPDF = result.data.fileUrl;
        }
      });
      this.dialogLoading = false;
    },
    /**
     * description: 审核驳回
     * return {*}
     */
    async reviewFileRefuse() {
      let params = {
        nurseEMRFileListID: this.nurseEMRFileListID,
      };
      await ReviewFileRefuse(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "操作成功");
        }
      });
      await this.getEmrFileList();
      await this.setSelectRow();
    },
    /**
     * description: 审核通过
     * return {*}
     */
    async reviewFileApproved() {
      let params = {
        nurseEMRFileListID: this.nurseEMRFileListID,
      };
      await ReviewFileApproved(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "操作成功");
        }
      });
      await this.getEmrFileList();
      await this.setSelectRow();
    },
    /**
     * description: 批量审核通过
     * return {*}
     */
    async batchReviewFile() {
      let table = this.$refs.fileData;
      let selectRows = table && this._common.clone(table.selection);
      if (selectRows.length == 0) {
        this._showTip("warning", "请选择需要审核的病历");
        return;
      }
      let successCount = 0;
      let failCount = 0;
      // 获取进度百分比
      let getPercent = (index) => Number((((index + 1) / selectRows.length) * 100).toFixed());
      for (let index = 0; index < selectRows.length; index++) {
        let selectRow = selectRows[index];
        if (
          this.user &&
          selectRow.reviewersID &&
          selectRow.reviewersID.includes(this.user.userID) &&
          selectRow.reviewersStationID &&
          selectRow.reviewersStationID.includes(this.user.stationID.toString())
        ) {
          continue;
        }
        let params = {
          nurseEMRFileListID: selectRow.nurseEMRFileListID,
        };
        await ReviewFileApproved(params).then((result) => {
          if (this._common.isSuccess(result)) {
            if (result.data == true) {
              successCount++;
              this.messageData[0].value = getPercent(index);
            } else {
              failCount++;
              this.messageData[0].value = getPercent(index);
              this.messageData[3].value += `${selectRow.showName}保存失败${result.message}<br/>`;
            }
          }
        });
      }
      this.progressFlag = true;
      if (successCount == 0 && failCount == 0) {
        this.progressFlag = false;
      }
      this.messageData[1].value = `${successCount}条保存成功`;
      this.messageData[2].value = `${failCount}条保存失败`;
      await this.getEmrFileList();
      await this.setSelectRow();
    },
    /**
     * description: 进度条关闭函数，重置进度条内的数据内容
     * return {*}
     */
    async restoreProgress() {
      this.progressFlag = false;
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
      await this.getEmrFileList();
      await this.setSelectRow();
    },
    /**
     * description: 关闭对话框
     * return {*}
     */
    closeDialog() {
      this.getRecordReviewList();
    },
  },
};
</script>
<style lang="scss">
.record-review {
  .record-review-top {
    .start-date {
      width: 140px;
    }

    .end-date {
      width: 140px;
    }

    .search-input {
      width: 170px;

      .el-input-group__append {
        padding: 0 5px;
      }

      i {
        color: #8cc63e;
      }
    }
  }

  .record-review-container {
    height: 100%;

    .record-review-aside {
      height: 100%;

      .review-button {
        float: right;
        margin-bottom: 10px;
      }
    }

    .record-review-main {
      height: 100%;

      .record-iframe {
        height: calc(100% - 18px);
        width: 100%;
      }

      .review-button {
        float: right;
      }
    }
  }
}
</style>
