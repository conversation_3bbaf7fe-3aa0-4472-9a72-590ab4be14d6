/*
 * FilePath     : \src\api\DepartmentJob.js
 * Author       : xml
 * Date         : 2020-09-17 10:04
 * LastEditors  : xml
 * LastEditTime : 2021-08-05 19:40
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/Job/Department";

export const urls = {
  GetDepartmentJob: baseUrl + "/GetDepartmentJob",
  SaveDepartmentJob: baseUrl + "/SaveDepartmentJob",
  UpdateDepartmentJob: baseUrl + "/UpdateDepartmentJob",
  DeleteDepartmentJob: baseUrl + "/DeleteDepartmentJob",
  CheckDepartmentJob: baseUrl + "/CheckDepartmentJob",
  GetCareGroupList: baseUrl + "/GetCareGroupList",
  SaveCareGroupListInfo: baseUrl + "/SaveCareGroupListInfo",
  GetPostAdditionButtonSwitch: baseUrl + "/GetPostAdditionButtonSwitch"
};

// 获取科室岗位
export const GetDepartmentJob = params => {
  return http.get(urls.GetDepartmentJob, params);
};
// 新增科室岗位
export const SaveDepartmentJob = params => {
  return http.post(urls.SaveDepartmentJob, params);
};
// 修改科室岗位
export const UpdateDepartmentJob = params => {
  return http.post(urls.UpdateDepartmentJob, params);
};
// 删除科室岗位
export const DeleteDepartmentJob = params => {
  return http.get(urls.DeleteDepartmentJob, params);
};

// 检核科室岗位
export const CheckDepartmentJob = params => {
  return http.get(urls.CheckDepartmentJob, params);
};
// 获取病区岗位
export const GetCareGroupList = params => {
  return http.get(urls.GetCareGroupList, params);
};

// 保存病区岗位
export const SaveCareGroupListInfo = params => {
  return http.post(urls.SaveCareGroupListInfo, params);
};
// 获取岗位新增按钮开关
export const GetPostAdditionButtonSwitch = params => {
  return http.get(urls.GetPostAdditionButtonSwitch);
};
