/*
 * FilePath     : \ccc.web\src\api\Handover\HandoverDischarge.js
 * Author       : 郭鹏超
 * Date         : 2023-02-24 16:00
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-03-28 19:06
 * Description  :出院小结API
 * CodeIterationRecord:
 */
import http from "../../utils/ajax";
const baseUrl = "/HandoverDischarge";
const urls = {
  GetHandOverDischargeAssessTemplate:
    baseUrl + "/GetHandOverDischargeAssessTemplate",
  GetHandoverDischargeSBAR: baseUrl + "/GetHandoverDischargeSBAR",
  SaveDischargeHandoverSBAR: baseUrl + "/SaveDischargeHandoverSBAR",
  GetHandoverDischargeList: baseUrl + "/GetHandoverDischargeList",
  DeleteHandoverDischarge: baseUrl + "/DeleteHandoverDischarge",
  SaveDischargeHandoverAssess: baseUrl + "/SaveDischargeHandoverAssess"
};
//获取交班类型
export const GetHandOverDischargeAssessTemplate = params => {
  return http.get(urls.GetHandOverDischargeAssessTemplate, params);
};
//获取交班页签
export const GetHandoverDischargeSBAR = params => {
  return http.get(urls.GetHandoverDischargeSBAR, params);
};
//sbar保存
export const SaveDischargeHandoverSBAR = params => {
  return http.post(urls.SaveDischargeHandoverSBAR, params);
};
//获取出院小结列表
export const GetHandoverDischargeList = params => {
  return http.get(urls.GetHandoverDischargeList, params);
};
//删除交接记录
export const DeleteHandoverDischarge = params => {
  return http.get(urls.DeleteHandoverDischarge, params);
};
//出院小结评估保存
export const SaveDischargeHandoverAssess = params => {
  return http.post(urls.SaveDischargeHandoverAssess, params);
};
