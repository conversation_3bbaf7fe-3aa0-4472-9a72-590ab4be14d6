<!--
 * FilePath     : \src\autoPages\wound\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-08-17 14:47
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-16 17:29
 * Description  : 增加带入护理记录选项
 * CodeIterationRecord:
    2022-07-06 BugFix：修复-伤口停止时,记录的病区不是本病区和本科室
    2022-07-06 BugFix：修复-伤口停止后，依旧显示新增维护记录的按钮
    3064-作为护理人员，我需要在伤口专项列表处增加最后一次维护时间，以利伤口维护
    2023-06-27 3587-护理评估按钮跳转专项录入的内容评估时间跟护理评估保持一致 -杨欣欣
-->
<template>
  <specific-care
    class="patient-wound"
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    :editFlag="checkResult"
    :careMainAddFlag="careMainAddFlag"
    :mainTableHeight="tableOneRowHeight"
    :drawerSize="supplementFlag ? '80%' : ''"
    :recordTitleSlotFalg="showRecordTitleFlag"
    :previewFlag="saveButtonShowFlag"
    @mainAdd="recordAdd"
    @maintainAdd="careMainAdd"
    @save="saveWound"
    @getMainFlag="getMainFlag"
    @cancel="drawerClose"
    @getHandOverFlag="getHandOverFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <template slot="record-title">
      <div class="title">
        <el-radio-group
          :min-width="convertPX(200)"
          v-if="statusList && statusList.length > 0"
          class="title-radio"
          v-model="chooseOutcome"
          @change="filterWoundRecordList(true, true)"
        >
          <el-radio-button v-for="(item, index) in statusList" :key="index" :label="item.label"></el-radio-button>
        </el-radio-group>
        <span class="record-title-span">
          伤口类型：
          <el-select
            v-model="chooseWoundType"
            clearable
            placeholder="请选择伤口类型"
            @change="filterWoundRecordList(false, true)"
          >
            <el-option
              v-for="(item, index) in patientWoundTypeList"
              :key="index"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </span>
      </div>
    </template>
    <!-- 主记录 -->
    <div slot="main-record">
      <packaging-table
        v-model="recordTableData"
        ref="recordTable"
        :headerList="recordTableHeaderList"
        @rowClick="recordClick"
      >
        <div slot="sourceFlag" slot-scope="scope">
          <el-tooltip v-if="scope.row.sourceFlag" :content="scope.row.sourceContent" placement="top">
            <div :class="['flag', scope.row.sourceFlag]">
              <span v-if="scope.row.sourceFlag == 'A'">护</span>
              <span v-if="scope.row.sourceFlag == 'H'">班</span>
              <span v-if="scope.row.sourceFlag == 'O'">术</span>
            </div>
          </el-tooltip>
        </div>
        <div slot="operate" slot-scope="scope">
          <el-tooltip content="修改" v-show="scope.row.endDateTime === null">
            <div @click.stop="recordAdd(scope.row)" class="iconfont icon-edit"></div>
          </el-tooltip>
          <el-tooltip content="停止" v-show="scope.row.endDateTime === null">
            <div @click.stop="recordEnd(scope.row)" class="iconfont icon-stop"></div>
          </el-tooltip>
          <el-tooltip content="删除" v-show="scope.row.endDateTime === null">
            <div @click.stop="deleteRecord(scope.row)" class="iconfont icon-del"></div>
          </el-tooltip>
          <el-tooltip v-if="scope.row.imgPreviewButtonFlag" content="伤口图片预览">
            <div @click.stop="openImgPreview(scope.row)" class="iconfont icon-img-preview"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <packaging-table v-model="careMainTableData" :headerList="mainTableHeaderList">
        <!-- 评估类型 插槽 -->
        <div slot="assessType" slot-scope="row">
          <span v-if="row.row.recordsCode.indexOf('Start') != -1">开始评估</span>
          <span v-else-if="row.row.recordsCode.indexOf('End') != -1 || row.row.recordsCode.indexOf('UEX') != -1">
            结束评估
          </span>
          <span v-else>例行评估</span>
        </div>
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope">
          <el-tooltip content="修改" v-if="scope.row.recordsCode.indexOf('Start') == -1" placement="top">
            <div class="iconfont icon-edit" @click="careMainAdd(scope.row)"></div>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <div
              class="iconfont icon-del"
              v-if="scope.row.recordsCode.indexOf('Start') == -1"
              @click="deleteCareMain(scope.row)"
            ></div>
          </el-tooltip>
          <el-tooltip v-if="scope.row.imgPreviewButtonFlag" content="伤口图片预览">
            <div @click.stop="openImgPreview(scope.row)" class="iconfont icon-img-preview"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 弹窗内容 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
    >
      <div slot="header">
        <span class="label">日期:</span>
        <el-date-picker
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          class="drawer-content-header-date"
        ></el-date-picker>
        <el-time-picker
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          class="drawer-content-header-time"
        ></el-time-picker>
        <span class="label">病区:</span>
        <station-selector v-model="stationID" label="" width="170"></station-selector>
        <dept-selector label="" width="150" v-model="departmentListID" :stationID="stationID"></dept-selector>
        <div class="header-latter-selector">
          <span v-if="type && type.indexOf('Start') != -1" class="label">记录人员：</span>
          <nurse-selector
            v-if="type && type.indexOf('Start') != -1"
            :disabled="!selectUserFlag"
            label=""
            width="100"
            v-model="userID"
            :stationID="stationID"
            :filterable="true"
            :allowCreate="nurseSelectCanManualEntryFlag"
          ></nurse-selector>
          <span class="label">伤口种类:</span>
          <el-select
            :disabled="!(type == 'Start') || !addFlag || (woundKindCode ? true : false)"
            placeholder="请选择种类"
            v-model="woundTypeKind"
            :popper-append-to-body="false"
            class="header-wound-type-selector"
          >
            <el-option
              v-for="(item, index) in woundTypeList"
              :key="index"
              :label="item.description"
              :value="item.settingValue"
              :disabled="GetNotUseTypeFlag(item)"
              @click.native="!GetNotUseTypeFlag(item) && woundTypeSelect(item)"
            ></el-option>
          </el-select>
        </div>
      </div>
      <body-and-assess-layout :link="link" :bodyShowFlag="type == 'Start'">
        <tabs-layout
          ref="tabsLayout"
          :template-list="templateDatas"
          :image-max-num="woundImageMaxNum"
          @change-values="changeValues"
          @upload-img="woundImg"
          @button-click="buttonClick"
          @button-record-click="buttonRecordClick"
          @checkTN="checkTN"
        />
      </body-and-assess-layout>
    </base-layout>
    <div slot="drawer-dialog">
      <el-dialog :title="imgPreviewTitle" :visible.sync="imgPreviewFlag" fullscreen>
        <img-preview :imgPreviewData="imgPreviewData"></img-preview>
      </el-dialog>
      <!--跳转出入量-->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="woundName"
        :visible.sync="showSpecificCareFlag"
        fullscreen
        custom-class="no-footer"
      >
        <iframe v-if="showSpecificCareFlag" ref="buttonDialog" width="100%" height="100%"></iframe>
      </el-dialog>
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonRecordTitle"
        :visible.sync="showButtonRecordDialog"
        custom-class="no-footer"
      >
        <risk-component :params="componentParams" @result="result"></risk-component>
      </el-dialog>
    </div>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import nurseSelector from "@/components/selector/nurseSelector";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import imgPreview from "@/components/imgPreview";
import bodyAndAssessLayout from "@/components/bodyAndAssessLayout";

import { GetButtonData } from "@/api/Assess";
import { mapGetters } from "vuex";
import {
  GetWoundRecordsCodeInfo,
  GetWoundAssessView,
  SavePatientWound,
  GetWoundRecordList,
  GetNewWoundCode,
  DeleteWoundByID,
  EndWound,
  GetWoundCareMainsByID,
  SavePatientWoundCare,
  DeleteWoundCare,
  GetImgPreviewData,
} from "@/api/WoundRecord";
import {
  GetWoundTypes,
  GetWoundImageNum,
  GetBringToShiftSetting,
  GetClinicalSettingByTypeCode,
  GetSelectSetting,
  GetClinicSettingByTypeCode,
} from "@/api/Setting";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { GetBringToNursingRecordFlagSetting, GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
export default {
  components: {
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    baseLayout,
    nurseSelector,
    packagingTable,
    imgPreview,
    riskComponent,
    bodyAndAssessLayout,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      loading: false,
      patient: undefined,
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      //主记录变量
      recordTableData: [],
      patientWoundRecordID: undefined,
      currentRecord: undefined,
      woundImages: [],
      //维护记录变量
      careMainTableData: [],
      patientWoundCareMainID: "",
      woundCode: undefined,
      addFlag: true,
      //弹窗变量
      drawerLoading: false,
      drawerLoadingText: undefined,
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      departmentListID: undefined,
      userID: undefined,
      woundTypeList: [],
      woundTypeKind: undefined,
      templateDatas: [],
      recordsCodeInfo: {},
      assessDatas: [],
      checkTNFlag: true,
      link: "",
      type: undefined,
      pressureKinds: ["1397", "1798", "1799"],
      woundPinkAssessListID: 30016071, //伤口粉红占比
      woundYellowAssessListID: 30016077, //伤口黄(腐肉)占比
      woundBlackAssessListID: 30016083, //伤口黑色结痂占比
      handOverArr: [true, false],
      informPhysicianArr: [true, false],
      bringToNursingRecordArr: [true, false],
      settingHandOver: false,
      settingNursingRecord: false,
      woundImageMaxNum: 4,
      //路由变量
      patientScheduleMainID: undefined,
      assessMainID: undefined,
      assessSort: 0,
      handoverID: undefined,
      hisOperationNo: undefined,
      sourceID: undefined,
      sourceType: undefined,
      supplementFlag: undefined,
      checkResult: true,
      selectUserFlag: false,
      mainTableHeaderList: [],
      woundType: undefined,
      careMainAddFlag: true,
      woundKindCode: undefined,
      autoAddFlag: false,
      //主记录表格表头加第一行的高度
      tableOneRowHeight: undefined,
      //宏力伤口新旧模板切换时间点
      criticalTime: "",
      imgPreviewFlag: false,
      imgPreviewData: {},
      imgPreviewTitle: "",
      copyWoundRecordList: [],
      // 伤口类别
      patientWoundTypeList: ["全部"],
      // 选择的伤口类别
      chooseWoundType: "全部",
      // 主记录表格头部配置
      recordTableHeaderList: [],
      // 选择伤口是否愈合
      chooseOutcome: "现有伤口",
      // 伤口是否愈合单选按钮配置数据
      statusList: [],
      // recordTitle插槽显示开关
      showRecordTitleFlag: false,
      woundName: "伤口",
      showSpecificCareFlag: false,
      buttonAssessListID: "",
      nurseSelectCanManualEntryFlag: false,
      canManualEntryStationList: [],
      saveButtonShowFlag: false,
      // BR风险类弹窗标题
      buttonRecordTitle: "",
      // BR风险类弹窗开关
      showButtonRecordDialog: false,
      // BR类弹窗所需参数
      componentParams: undefined,
      // BR项ID
      brAssessListID: undefined,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      localHospitalInfo: "getHospitalInfo",
      token: "getToken",
    }),
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.supplementFlag = undefined;
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.supplementFlag = "*";
          this.bringToNursingRecordArr = [false, false];
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
    showSpecificCareFlag(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
    stationID: {
      handler(newVal) {
        if (newVal) {
          this.filterManualEntryStationList();
        }
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 初始化
     * return {*}
     */
    async init() {
      localStorage.setItem("selectPart", JSON.stringify({}));
      localStorage.setItem("bodyPart", JSON.stringify({}));
      if (this.$route.query.patientScheduleMainID) {
        this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
      }
      this.assessMainID = this.$route.query.num;
      this.assessSort = this.$route.query.sort;
      this.handoverID = this.$route.query.handoverID;
      this.hisOperationNo = this.$route.query.hisOperationNo;
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
      this.woundKindCode = this.$route.query.woundKind;
      this.autoAddFlag = this.$route.query.autoAddFlag;
      this.showRecordArr = [true, false];
      if (this.patientScheduleMainID || this.assessMainID || this.handoverID || this.hisOperationNo || this.sourceID) {
        this._sendBroadcast("setPatientSwitch", false);
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
      await this.getRecordTableHeaderList();
      await this.getShowRecordFlagSetting();
      this.getAssessContentCriticalTime();
      await this.getWoundTypeData();
      await this.getStatusList();
      await this.getRecordTableData();
      this.GetWoundImageNum();
      this.GetBringHandOverSetting();
      this.GetBringToNursingRecord();
      this.getSetting();
      this.getManualEntryFlag();
    },
    /**
     * description: 获取动态表头
     * return {*}
     */
    async getMainTableHeaderList() {
      this.mainTableHeaderList = [];
      let params = {
        fileClassID: 7,
        fileClassSub: this.woundType,
        useDescription: "1||Table",
      };
      this.mainTableHeaderList = [];
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.mainTableHeaderList = res.data;
        }
      });
    },
    /**
     * description: 保存选择
     * return {*}
     */
    async saveWound() {
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      if (this.recordsCodeInfo.recordsCode.indexOf("Start") != -1) {
        await this.recordSave();
      }
      if (this.recordsCodeInfo.recordsCode.indexOf("Maintain") != -1) {
        await this.careMainSave();
      }
      if (this.recordsCodeInfo.recordsCode.indexOf("End") != -1) {
        await this.recordEndSave();
      }
      this.drawerLoading = false;
    },

    /*-------------主记录CRUD-------------*/

    /**
     * description: 获取伤口主记录数据
     * param {*} recordID 主记录ID
     * return {*}
     */
    async getRecordTableData(recordID = undefined) {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        recordID,
      };
      this.loading = true;
      //获取病人伤口记录列表
      await GetWoundRecordList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result) && result.data) {
          if (this.woundKindCode) {
            this.recordTableData = result.data.filter((data) => data.woundKindCode == this.woundKindCode);
          } else {
            this.recordTableData = result.data;
          }
          this.copyWoundRecordList = this.recordTableData;
          if (this.showRecordTitleFlag) {
            this.filterWoundRecordList();
          }
          this.currentRecord = recordID && this.recordTableData?.length == 1 ? this.recordTableData[0] : undefined;
          this.$nextTick(() => {
            if (this.autoAddFlag) {
              if (!this.recordTableData || this.recordTableData.length <= 0) {
                this.recordAdd();
                this.autoAddFlag = false;
              } else if (this.recordTableData.length == 1) {
                this.recordClick(this.recordTableData[0]);
              }
            }
          });
        }
      });
    },
    /**
     * description: 主记录新增修改
     * param {*} item 修改数据
     * return {*}
     */
    async recordAdd(item) {
      this.type = "Start";
      if (item) {
        this.checkResult = await this._common.checkActionAuthorization(this.user, item.nurseID);
        if (this.checkResult) {
          let ret = await this._common.getEditAuthority(
            item.patientWoundRecordID,
            "PatientWoundRecord",
            !!this.supplementFlag
          );
          if (ret) {
            this.checkResult = false;
            this._showTip("warning", ret);
          } else {
            this.checkResult = true;
          }
        }
        if (this.supplementFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            item.nurseID
          );
          this.saveButtonShowFlag = !saveButtonFlag;
        }
        this.addFlag = false;
        this.assessDate = this._datetimeUtil.formatDate(item.assessDateTime, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.assessDateTime, "hh:mm");
        this.stationID = item.occuredStationID;
        this.departmentListID = item.occuredDepartmentID;
        this.woundTypeKind = item.woundKindCode;
        this.userID = item.nurseID;
        this.patientWoundRecordID = item.patientWoundRecordID;
        this.patientWoundCareMainID = item.patientWoundCareMainID;
        this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
        this.bodyPart = item.bodyPart;
        this.woundCode = parseInt(item.woundCode);
        //用于人体图重置更新
        localStorage.setItem("selectPart", this.bodyPart);
      } else {
        localStorage.setItem("selectPart", JSON.stringify({}));
        localStorage.setItem("bodyPart", JSON.stringify({}));
        this.templateDatas = [];
        this.addFlag = true;
        this.assessDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.woundTypeKind = this.woundKindCode ? this.woundKindCode : -1;
        this.userID = this.user.userID;
        this.patientWoundRecordID = "temp" + this._common.guid();
        this.patientWoundCareMainID = "temp" + this._common.guid();
        this.bodyPart = {};
      }
      this.link = "../../static/body/mobileBody.html?type=Common&recordsCode=''&gender=" + this.patient.genderCode;
      this.openOrCloseDrawer(true, "伤口主记录");
      if (this.woundTypeKind != -1) {
        await this.woundTypeSelect(this.getWoundType(item));
      }
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingNursingRecord);
    },
    /**
     * description: 主记录保存
     * return {*}
     */
    async recordSave() {
      // 数据验证
      if (!this.recordSaveCheck()) {
        return;
      }
      //新增才获取，修改不获取
      if (this.addFlag) {
        await this.getWoundCode(localStorage.getItem("bodyPart"));
      }
      // 新增保存数据
      let saveData = this.getRecordSaveModel();
      if (this.woundImages.length > 0 && saveData.Details.length === 0) {
        this._showTip("warning", "不能只传图片，请评估其他项目！");
        return;
      }
      // 判断伤口/压力性损伤的伤口床是否为100%
      if (!this.woundBedCheck(saveData.Details)) {
        return;
      }
      await SavePatientWound(saveData).then((result) => {
        if (this._common.isSuccess(result)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", "保存成功！");
          this.fixTable();
          this.getRecordTableData();
        }
      });
    },
    /**
     * description: 主记录保存model
     * return {*}
     */
    getRecordSaveModel() {
      let saveData = {
        Details: this.getDetails(),
        WoundImages: this.getImages(),
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        NursingLevel: this.patient.nursingLevelCode,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        AssessSort: this.assessSort,
        PatientScheduleMainID: this.patientScheduleMainID ? this.patientScheduleMainID : undefined,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        PatientWoundCareMainID: this.patientWoundCareMainID
          ? this.patientWoundCareMainID
          : "temp" + this._common.guid(),
        Main: {
          AddEmployeeID: this.userID,
          PatientWoundRecordID: this.patientWoundRecordID ? this.patientWoundRecordID : "temp" + this._common.guid(),
          BodyPartID: localStorage.getItem("bodyPart"),
          BodyShowName: this.getBodyPartName(localStorage.getItem("bodyPart")),
          InpatientID: this.patient.inpatientID,
          StartDate: this.assessDate,
          StartTime: this.assessTime,
          WoundCode: this.woundCode,
          WoundKind: this.woundTypeKind,
          OccuredStationID: this.stationID,
          OccuredDepartmentID: this.departmentListID,
          HandoverID: this.handoverID,
          SourceDataID: this.hisOperationNo,
          SupplemnentFlag: this.supplementFlag,
        },
      };
      return saveData;
    },
    /**
     * description: 主记录保存检核
     * return {*}
     */
    recordSaveCheck() {
      if (this.woundTypeKind == -1) {
        this._showTip("warning", "请选择伤口种类！");
        return false;
      }
      var selectPart = localStorage.getItem("bodyPart");
      if (selectPart == "{}" || selectPart == "[]") {
        this._showTip("warning", "请选择部位！");
        return false;
      }
      if (this.stationID == "") {
        this._showTip("warning", "请选择发生病区");
        return false;
      }
      if (this.departmentListID == "") {
        this._showTip("warning", "请选择发生科室");
        return false;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      let item = this.assessDatas.find((item) => {
        let result = this._common.checkAssessTN(item);
        return !result.flag;
      });
      return item ? false : true;
    },
    /**
     * description: 主记录停止
     * param {*} item 主记录
     * return {*} void
     */
    async recordEnd(item) {
      this.currentRecord = item;
      this.type = "End";
      this.patientWoundRecordID = item.patientWoundRecordID;
      this.assessDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
      this.stationID = this.patient.stationID;
      this.departmentListID = this.patient.departmentListID;
      this.woundTypeKind = item.woundKindCode;
      this.patientWoundCareMainID = item.patientWoundCareMainID ?? "temp" + this._common.guid();
      this.$set(this.handOverArr, 1, this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, this.settingNursingRecord);
      this.openOrCloseDrawer(true, "伤口停止");
      if (this.woundTypeKind != -1) {
        await this.woundTypeSelect(this.getWoundType(item));
      }
      //是否仅本人操作
      this.checkResult = true;
    },
    /**
     * description: 主记录停止保存
     * return {*}
     */
    async recordEndSave() {
      if (!this.recordEndSaveCheck()) {
        return;
      }
      let saveData = {
        InpatientID: this.patient.inpatientID,
        PatientWoundCareMainID: this.patientWoundCareMainID,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        NursingLevel: this.patient.nursingLevelCode,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        Details: this.getDetails(),
        WoundRecordID: this.patientWoundRecordID,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        stationID: this.stationID,
        departmentListID: this.departmentListID,
        WoundImages: this.getImages(),
        BringToShift: this.handOverArr[1],
        SupplemnentFlag: this.supplementFlag,
        informPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        PatientScheduleMainID: this.patientScheduleMainID,
      };
      await EndWound(saveData).then((result) => {
        this.stopLoading = false;
        if (this._common.isSuccess(result)) {
          this.openOrCloseDrawer(false);
          if (!this.currentRecord.endDateTime) {
            this._showTip("success", "停止成功！");
          } else {
            this._showTip("success", "保存成功！");
          }
          this.fixTable();
          this.getRecordTableData();
        }
      });
    },
    /**
     * description: 主记录停止保存检核
     * return {*}
     */
    recordEndSaveCheck() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区！");
        return false;
      }
      if (!this.departmentListID) {
        this._showTip("warning", "请选择科室！");
        return false;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      let item = this.assessDatas.find((item) => {
        let result = this._common.checkAssessTN(item);
        return !result.flag;
      });
      return item ? false : true;
    },
    /**
     * description: 主记录删除
     * param {*} row 待删除行
     * return {*}
     */
    async deleteRecord(row) {
      if (!row.patientWoundRecordID) {
        this._showTip("删除失败");
        return;
      }
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, row.nurseID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (this.supplementFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.nurseID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(
        row.patientWoundRecordID,
        "PatientWoundRecord",
        !!this.supplementFlag
      );
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            ID: row.patientWoundRecordID,
          };
          this.loading = true;
          DeleteWoundByID(params).then((result) => {
            this.loading = true;
            if (this._common.isSuccess(result)) {
              this.fixTable();
              this._showTip("success", "删除成功！");
              this.getRecordTableData();
            }
          });
        }
      });
    },

    /*-------------维护记录CRUD-------------*/
    /**
     * description: 伤口主记录点击
     * param {*} row 点击行
     * return {*}
     */
    async recordClick(row) {
      this.woundType = row.woundType;
      this.currentRecord = row;
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.recordTableData = [row];
        this.careMainAddFlag = !row.endDate;
        this.getCareMainTableData();
        await this.getMainTableHeaderList();
        await this.getMainTableOneRowHeight();
        if (this.autoAddFlag) {
          this.$nextTick(() => {
            this.autoAddFlag = false;
            this.careMainAdd();
          });
        }
      }
    },
    /**
     * description: 获取伤口维护记录
     * return {*}
     */
    async getCareMainTableData() {
      let params = {
        ID: this.currentRecord.patientWoundRecordID,
      };
      this.loading = true;
      await GetWoundCareMainsByID(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.careMainTableData = result.data;
        }
      });
    },
    /**
     * description: 维护记录新增修改
     * param {*} item 维护记录
     * return {*}
     */
    async careMainAdd(item) {
      this.checkResult = true;
      if (item) {
        //是否仅本人操作
        this.checkResult = await this._common.checkActionAuthorization(this.user, item.addEmployeeID);
        if (this.checkResult) {
          let ret = await this._common.getEditAuthority(
            item.patientWoundCareMainID,
            "PatientWoundCareMain",
            !!this.supplementFlag
          );
          if (ret) {
            this.checkResult = false;
            this._showTip("warning", ret);
          } else {
            this.checkResult = true;
          }
        }
        if (this.supplementFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            item.userID
          );
          this.saveButtonShowFlag = !saveButtonFlag;
        }
      }
      const defaultAssessDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
      const defaultAssessTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
      this.assessDate = item ? this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd") : defaultAssessDate;
      this.assessTime = item ? this._datetimeUtil.formatDate(item.assessTime, "hh:mm") : defaultAssessTime;
      this.stationID = item ? item.stationID : this.patient.stationID;
      this.departmentListID = item ? item.departmentListID : this.patient.departmentListID;
      this.recordsCodeInfo.recordsCode = item ? item.recordsCode : "SurgeryWoundMaintain";
      this.woundTypeKind = this.currentRecord.woundKindCode;
      this.patientWoundCareMainID = item ? item.patientWoundCareMainID : "temp" + this._common.guid();
      this.patientWoundRecordID = this.currentRecord.patientWoundRecordID;
      this.numberOfAssessment = item ? item.numberOfAssessment : 1;
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.bringToNursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingNursingRecord);
      this.type = "Maintain";
      if (this.recordsCodeInfo.recordsCode.indexOf("End") != -1) {
        this.type = "End";
      }
      this.openOrCloseDrawer(true, "伤口维护记录");
      await this.getAssessTemplate();
    },
    /**
     * description: 维护记录保存
     * return {*}
     */
    async careMainSave() {
      if (!this.careMainSaveCheck()) {
        return;
      }
      let saveData = this.getCareMainSaveModel();
      // 判断伤口/压力性损伤的伤口床是否为100%
      if (!this.woundBedCheck(saveData.Details)) {
        return;
      }
      this.loadingText = "保存中……";
      await SavePatientWoundCare(saveData).then((result) => {
        if (this._common.isSuccess(result)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", "保存成功！");
          this.getRecordTableData(this.currentRecord.patientWoundRecordID);
          this.getCareMainTableData();
        }
      });
    },
    /**
     * description: 维护记录保存检核
     * return {*}
     */
    careMainSaveCheck() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return false;
      }
      if (!this.departmentListID) {
        this._showTip("warning", "请选择科室");
        return false;
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        return false;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      let item = this.assessDatas.find((item) => {
        let result = this._common.checkAssessTN(item);
        return !result.flag;
      });
      return item ? false : true;
    },
    /**
     * description: 维护记录保存model
     * return {*}
     */
    getCareMainSaveModel() {
      let saveData = {
        InpatientID: this.patient.inpatientID,
        PatientWoundCareMainID: this.patientWoundCareMainID
          ? this.patientWoundCareMainID
          : "temp" + this._common.guid(),
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        NursingLevel: this.patient.nursingLevelCode,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        Details: this.getDetails(),
        WoundRecordID: this.currentRecord.patientWoundRecordID,
        NumberOfAssessment: this.numberOfAssessment,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        stationID: this.stationID,
        departmentListID: this.departmentListID,
        WoundImages: this.getImages(),
        BringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        HandoverID: this.handoverID,
        SupplemnentFlag: this.supplementFlag,
        PatientScheduleMainID: this.patientScheduleMainID,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
      };
      return saveData;
    },
    /**
     * description: 维护记录删除
     * param {*} row 待删除维护记录
     * return {*}
     */
    async deleteCareMain(row) {
      if (!row) {
        this._showTip("warning", "删除失败");
      }
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, row.addEmployeeID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (this.supplementFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.userID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(
        row.patientWoundCareMainID,
        "PatientWoundCareMain",
        !!this.supplementFlag
      );
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let param = {
            mainID: row.patientWoundCareMainID,
          };
          this.loading = true;
          DeleteWoundCare(param).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              this.patientWoundCareMainID = undefined;
              this._showTip("success", "删除成功！");
              if (row.recordsCode.includes("End")) {
                this.fixTable();
              }
              this.getRecordTableData(row.patientWoundRecordID);
              !row.recordsCode.includes("End") && this.getCareMainTableData();
            }
          });
        }
      });
    },
    /**
     * description: 重置选中状态
     * return {*}
     */
    fixTable() {
      this.showRecordArr = [true, false];
      this.currentRecord = undefined;
      this.careMainTableData = [];
    },
    /**
     * description: 获取评估模板
     * return {*}
     */
    async getAssessTemplate() {
      let params = {
        settingValue: this.woundTypeKind,
        typeValue: this.woundType,
        departmentListID: this.patient.departmentListID,
        type: this.type,
      };
      this.drawerLoading = true;
      this.drawerLoadingText = "加载中……";
      await GetWoundRecordsCodeInfo(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo) {
        return;
      }
      if (this.type == "Start") {
        let link =
          "../../static/body/mobileBody.html?recordsCode=" +
          this.recordsCodeInfo.recordsCode +
          "&gender=" +
          this.patient.genderCode;
        localStorage.setItem("selectPart", this.bodyPart);
        // 压力性损伤 身体部位只能单选
        if (this.pressureKinds.indexOf(this.woundTypeKind) != -1) {
          if (this.addFlag) {
            localStorage.setItem("selectPart", JSON.stringify({}));
            localStorage.setItem("bodyPart", JSON.stringify({}));
          }
          this.$set(this, "link", link + "&type=Common");
        } else {
          // 非压力性损伤部位可多选
          if (this.addFlag) {
            localStorage.setItem("selectPart", JSON.stringify([]));
            localStorage.setItem("bodyPart", JSON.stringify([]));
          }
          this.$set(this, "link", link + "&type=CommonMulti");
        }
      }
      //获取压力性损伤评估信息
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        dateOfBirth: this.patient.dateOfBirth,
        patientWoundRecordID: this.patientWoundRecordID ? this.patientWoundRecordID : undefined,
        mainID: this.patientWoundCareMainID ? this.patientWoundCareMainID : "temp" + this._common.guid(),
        inpatientID: this.patient.inpatientID,
        sourceType: "WoundBR",
      };
      await GetWoundAssessView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.$set(this, "templateDatas", result.data);
        }
      });
      this.drawerLoading = false;
    },
    /**
     * description: 评估组件回传数据
     * param {*} data 回传数据
     * return {*}
     */
    changeValues(data) {
      this.assessDatas = data;
    },
    /**
     * description: 评估组件回传图片数据
     * param {*} imgs 图片数据
     * return {*}
     */
    woundImg(imgs) {
      this.woundImages = imgs;
    },
    /**
     * description: 接收TN项检核结果
     * param {*} flag 检核结果
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },

    /*-------------获取页面配置-------------*/
    /**
     * description: 获取伤口种类
     * return {*}
     */
    async getWoundTypeData() {
      await GetWoundTypes().then((res) => {
        if (this._common.isSuccess(res)) {
          this.woundTypeList = res.data;
          this.woundTypeList.unshift({
            settingValue: -1,
            description: "请选择",
          });
          this.woundTypeKind = this.woundTypeList[0].settingValue;
        }
      });
    },
    /**
     * description: 获取伤口图片最大可传数量
     * return {*}
     */
    GetWoundImageNum() {
      GetWoundImageNum().then((result) => {
        if (this._common.isSuccess(result)) {
          this.woundImageMaxNum = parseInt(result.data);
        }
      });
    },
    /**
     * description: 获取是否带入交班配置
     * return {*}
     */
    GetBringHandOverSetting() {
      let params = {
        special: "Wound",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingHandOver = res.data;
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * return {*}
     */
    GetBringToNursingRecord() {
      let params = {
        settingTypeCode: "WoundAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },
    /**
     * description: 获取是否可以更改记录人
     * return {*}
     */
    getSetting() {
      let param = {
        settingTypeCode: "WoundMaintainer",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.selectUserFlag = response.data;
        }
      });
    },
    /**
     * description: 获取伤口代码
     * param {*} selectPart 选中的身体部位
     * return {*}
     */
    async getWoundCode(selectPart) {
      let params = {
        stationID: this.patient.stationID,
        inpatientID: this.patient.inpatientID,
        bodyPartID: selectPart,
      };
      //伤口代号
      await GetNewWoundCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.woundCode = result.data;
        }
      });
    },
    /**
     * description: 伤口种类选择
     * param {*} item 选择的伤口种类
     * return {*}
     */
    async woundTypeSelect(item) {
      if (this.woundTypeKind == -1) {
        this.recordsCodeInfo = {};
        this.templateDatas = [];
        this.assessDatas = [];
        return;
      }
      this.woundType = item.typeValue;
      this.woundName = item.description;
      await this.getAssessTemplate();
    },

    /*-------------配合保存方法-------------*/
    /**
     * description: 回显部位名称
     * param {*} bodyPart 当前身体部位
     * return {*}
     */
    getBodyPartName(bodyPart) {
      var selectPart = JSON.parse(bodyPart);
      let name = "";
      if (selectPart instanceof Array) {
        // 多选
        if (selectPart && selectPart.length > 0) {
          selectPart.forEach((part) => {
            if (name) {
              name += "，" + part.bodyPartName;
            } else {
              name = part.bodyPartName;
            }
          });
        }
      } else {
        // 单选
        name = selectPart.bodyPartName;
      }
      return name;
    },
    /**
     * description: 获取保存明细
     * return {*}
     */
    getDetails() {
      let details = [];
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          specialListType: content.specialListType,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        if (content.disableGroup != -1) {
          details.push(detail);
        }
      });
      return details;
    },
    /**
     * description: 组装伤口图片数据
     * return {*}
     */
    getImages() {
      let imgs = [];
      // 组装伤口图片表数据
      for (let i = 0; i < this.woundImages.length; i++) {
        let image = this.woundImages[i].split(",");
        let type = image[0].match(/:(.*?);/)[1].split("/")[1];
        let imageInfo = {
          InpatientID: this.patient.inpatientID,
          PatientID: this.patient.patientID,
          SpecialListImageNumber: i + 1,
          SpecialListImage: image[1],
          ImageType: type,
        };
        imgs.push(imageInfo);
      }
      return imgs;
    },
    /**
     * description: 基底颜色占比
     * param {*} details 明细数据
     * return {*}
     */
    woundBedCheck(details) {
      let woundBed = [];
      this.assessDatas.forEach((content) => {
        if (
          content.assessListGroupID == this.woundPinkAssessListID ||
          content.assessListGroupID == this.woundYellowAssessListID ||
          content.assessListGroupID == this.woundBlackAssessListID
        ) {
          woundBed.push(content);
        }
      });
      if (
        details.length > 0 &&
        woundBed.length > 0 &&
        this.recordsCodeInfo.recordsCode.indexOf("IADWound") == -1 &&
        (this.recordsCodeInfo.recordsCode.indexOf("Wound") != -1 ||
          this.recordsCodeInfo.recordsCode.indexOf("PressureSore") != -1)
      ) {
        if (woundBed.length == 3) {
          // 判断是不是100%
          let sum = 0;
          woundBed.forEach((item) => {
            let value = item.itemName.trim();
            sum += parseInt(value);
          });
          if (sum != 100) {
            this._showTip("warning", "基底颜色占比错误(非100%)");
            return false;
          }
        } else {
          this._showTip("warning", "基底颜色占比必须选择！");
          return false;
        }
      }
      return true;
    },

    /*-------------专项护理组件逻辑-------------*/

    /**
     * description: 主记录勾选
     * param {*} flag
     * return {*}
     */
    getMainFlag(flag) {
      if (flag) {
        //避免多次调用
        this.woundTypeList?.length && this.getRecordTableData();
      } else {
        if (this.recordTableData.length) {
          this.recordTableData = this.currentRecord ? [this.currentRecord] : [this.recordTableData[0]];
          this.currentRecord = this.currentRecord ? this.currentRecord : this.recordTableData[0];
        }
        this.getCareMainTableData();
      }
    },
    /**
     * description: 组件回传交班flag
     * param {*} flag
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 通知医师标记
     * return {*}
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记
     * param {*} flag
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.bringToNursingRecordArr[1] = flag;
    },
    /**
     * description: 弹窗关闭
     * return {*}
     */
    drawerClose() {
      this.patientWoundRecordID = undefined;
      this.patientWoundCareMainID = undefined;
      this.templateDatas = [];
      this.woundImages = [];
      this.assessDatas = [];
      this.showTemplateFlag = false;
    },
    /**
     * description: 弹窗开关
     * param {*} flag
     * param {*} title
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 获取表格第一行加表头高度
     * param {*}
     * return {*}
     */
    getMainTableOneRowHeight() {
      this.$nextTick(() => {
        this.tableOneRowHeight = this._common.getTableOneRowHeight(
          this.$refs.recordTable?.$el,
          ".main-record-row",
          ".main-record-header-row"
        );
      });
    },
    /**
     * description: 宏力旧伤口类型判断是否可选(暂时处理 后续调整去除相关逻辑)
     * param {*} type
     * return {*}
     */
    GetNotUseTypeFlag(type) {
      let newType = this.woundTypeList.find((item) => item.settingTypeCode == "WoundCategoryNewGroup");
      if (newType && type.settingTypeCode == "WoundCategory") {
        return true;
      }
      return false;
    },
    /**
     * description: 配合宏力新旧模板切换 后续删除
     * return {*}
     */
    getWoundType(item) {
      let typeItem = {};
      //兼顾宏力历史数据问题 先写死
      let assessDate = item?.startDate ?? this.assessDate;
      assessDate = this._datetimeUtil.formatDate(assessDate, "yyyy-MM-dd");
      let assessTime = item?.startTime ?? this.assessTime;
      let startDateTime = this._datetimeUtil.formatDate(assessDate + " " + assessTime, "yyyy-MM-dd hh:mm");
      let oldFlag =
        this.localHospitalInfo?.hospitalID != "1" ||
        (this.criticalTime &&
          startDateTime <= this._datetimeUtil.formatDate(this.criticalTime, "yyyy-MM-dd hh:mm") &&
          (item?.woundKindCode == "1403" || item?.woundKindCode == "1800"));
      typeItem = this.woundTypeList.find(
        (m) =>
          m.settingValue == this.woundTypeKind &&
          m.settingTypeCode == (oldFlag ? "WoundCategory" : "WoundCategoryNewGroup")
      );
      return typeItem ?? {};
    },
    /**
     * description: 获取宏力新旧模板切换时间点
     * return {*}
     */
    getAssessContentCriticalTime() {
      let params = {
        settingType: "AssessContentCriticalTime",
        settingCode: "Wound",
      };
      GetClinicalSettingByTypeCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.criticalTime = result?.data?.settingValue;
        }
      });
    },
    /**
     * description: 打开伤口图片预览组件
     * param {*} row
     * return {*}
     */
    async openImgPreview(row) {
      this.imgPreviewTitle = `伤口图片-${this.patient.patientName}-${this.patient.bedNumber}床`;
      this.imgPreviewData = {};
      await this.getImgPreviewData(row);
    },
    /**
     * description: 获取伤口预览图片组件需要数据
     * param {*} row
     * return {*}
     */
    async getImgPreviewData(row) {
      let params = {
        inpatientID: this.patient.inpatientID,
        sourceType: "Special",
        recordID: row.patientWoundRecordID,
        fileClassID: 7,
        patientWoundCareMainID: row.patientWoundCareMainID ? row.patientWoundCareMainID : "",
      };
      await GetImgPreviewData(params).then((result) => {
        this.imgPreviewFlag = true;
        if (this._common.isSuccess(result)) {
          this.imgPreviewData = result.data;
        }
      });
    },
    /**
     * @description: 获取伤口类型下拉框数据
     * @param chooseOutcomeValue
     * @return
     */
    getPatientWoundTypeList(chooseOutcomeValue) {
      this.patientWoundTypeList = ["全部"];
      let tableData = [];
      if (chooseOutcomeValue.includes("End")) {
        tableData = this.copyWoundRecordList.filter((record) => record.endDateTime);
      } else {
        tableData = this.copyWoundRecordList.filter((record) => !record.endDateTime);
      }
      tableData.forEach((element) => {
        if (this.patientWoundTypeList.filter((m) => m === element.woundKindName).length <= 0) {
          this.patientWoundTypeList.push(element.woundKindName);
        }
      });
    },
    /**
     * @description: 根据伤口类型和伤口结局筛选伤口列表
     * @return
     */
    filterWoundRecordList(changeOutcomeFlag = false, onlyShowRecordFlag = false) {
      if (onlyShowRecordFlag) {
        this.fixTable();
      }
      if (!this.chooseWoundType && !this.chooseOutcome) {
        this.recordTableData = this.copyWoundRecordList;
        return;
      }
      if (changeOutcomeFlag) {
        // 切换伤口是否愈合，下拉款选择内容情况
        this.chooseWoundType = "全部";
      }
      if (this.chooseWoundType) {
        // 筛选选择的伤口类型
        this.recordTableData = this.copyWoundRecordList.filter(
          (record) => this.chooseWoundType == record.woundKindName
        );
      }
      if (this.chooseWoundType == "全部") {
        this.recordTableData = this.copyWoundRecordList;
      }
      // 找到伤口是否愈合的配置
      let chooseOutcomeValue = this.statusList.find((outcome) => outcome.label == this.chooseOutcome)?.value;
      // 筛选数据
      if (chooseOutcomeValue && !this.chooseWoundType) {
        if (chooseOutcomeValue.includes("End")) {
          this.recordTableData = this.copyWoundRecordList.filter((record) => record.endDateTime);
        } else {
          this.recordTableData = this.copyWoundRecordList.filter((record) => !record.endDateTime);
        }
      }
      if (chooseOutcomeValue && this.chooseWoundType) {
        if (chooseOutcomeValue.includes("End")) {
          this.recordTableData = this.recordTableData.filter((record) => record.endDateTime);
        } else {
          this.recordTableData = this.recordTableData.filter((record) => !record.endDateTime);
        }
      }
      if (chooseOutcomeValue) {
        this.getPatientWoundTypeList(chooseOutcomeValue);
      }
    },
    /**
     * @description: 获取主记录动态表头配置
     * @return
     */
    async getRecordTableHeaderList() {
      this.recordTableHeaderList = [];
      let params = {
        fileClassID: 7,
        fileClassSub: "WoundRecord",
        useDescription: "1||Table",
      };
      this.recordTableHeaderList = [];
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.recordTableHeaderList = res.data;
        }
      });
    },
    /**
     * description: 串专项
     * param {*} content
     * return {*}
     */
    async buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      //修改伤口的开始评估获取不到CareMainID,使用recordsCode获取到开始评估的careMainID
      if (!this.patientWoundCareMainID) {
        this.patientWoundCareMainID = this.careMainTableData.find(
          (m) => m.recordsCode == this.recordsCodeInfo.recordsCode
        )?.patientWoundCareMainID;
      }
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += (url.includes("?") ? "&" : "?") + `bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        `&inpatientID=${this.patient.inpatientID}` +
        `&sourceID=${this.patientWoundCareMainID}` +
        `&patientWoundCareMainID=${this.patientWoundCareMainID}` +
        `&patientWoundRecordID=${this.patientWoundRecordID}` +
        `&woundName=${this.woundName}` +
        `&woundKind=${this.woundTypeKind}` +
        "&sourceType=Wound" +
        "&isDialog=true";
      this.showSpecificCareFlag = true;
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },

    /**
     * description: 更新按钮角标
     * param {*} assessListID 按钮评估流水号
     * return {*}
     */
    async updateButton(assessListID) {
      let item = await this.getWoundButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    /**
     * @description: 获取伤口是否愈合单选按钮配置数据
     * @return
     */
    async getStatusList() {
      let params = {
        typeCode: "WoundEndingCategory",
        typeValue: "WoundEnding",
      };
      await GetSelectSetting(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.statusList = [].concat(result.data);
        }
      });
    },
    /**
     * @description: 获取标题插槽是否显示开关
     * @return
     */
    async getShowRecordFlagSetting() {
      let param = {
        settingTypeCode: "WoundRecordTitleSlotFlag",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.showRecordTitleFlag = response.data;
        }
      });
    },
    /**
     * @description: 获取按钮值
     * @return
     * @param assessListID
     */
    async getWoundButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessListID: assessListID,
        sourceID: this.getSourceID(),
        sourceType: this.brAssessListID ? "WoundBR_" + this.brAssessListID : "",
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: 获取记录人员下拉框是否可以输入文字配置
     * return {*}
     */
    getManualEntryFlag() {
      let param = {
        settingTypeCode: "NurseSelectCanManualEntryFlag",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.nurseSelectCanManualEntryFlag = response.data;
          this.getManualEntryStationSetting();
        }
      });
    },
    /**
     * @description: 获取记录人员可手工输入权限配置
     * @return
     */
    getManualEntryStationSetting() {
      let params = {
        settingTypeCode: "NurseSelectCanManualEntryStation",
      };
      GetClinicSettingByTypeCode(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.canManualEntryStationList = response.data;
          this.filterManualEntryStationList();
        }
      });
    },
    /**
     * @description: 过滤记录人员可手工输入病区权限
     * @return
     */
    filterManualEntryStationList() {
      let filterStation = this.canManualEntryStationList.find((setting) => setting.typeValue == this.stationID);
      if (!filterStation) {
        this.nurseSelectCanManualEntryFlag = false;
      } else {
        this.nurseSelectCanManualEntryFlag = true;
      }
    },
    /**
     * description: 风险按钮点击回调
     * param {*} content 当前点击项目
     * return {*}
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      this.componentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: "WoundBR_" + this.brAssessListID,
        sourceID: this.getSourceID(),
        assessTime:
          this._datetimeUtil.formatDate(this.assessDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.assessTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 获取跳转BR sourceID
     * return {*} sourceID
     * param {*}
     */
    getSourceID() {
      if (this.recordsCodeInfo.recordsCode.includes("Maintain")) {
        return this.patientWoundCareMainID?.replace("temp", "");
      }
      return this.patientWoundRecordID?.replace("temp", "");
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * return {*}
     */
    result(resultFlag) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brAssessListID);
      }
    },
  },
};
</script>

<style lang="scss" >
.patient-wound {
  .title {
    display: flex;
    flex-direction: row;

    .title-radio {
      margin: -10px 10px 0px 0px;
    }

    .record-title-span {
      font-size: 14px;
      font-weight: normal;
      margin: -15px 0px 0px 0px;
    }
  }

  .header-latter-selector {
    display: inline-block;

    .header-wound-type-selector {
      width: 200px;
    }
  }

  .not-use-type {
    color: #dcdfe6;
  }

  .drawer-content-header-date {
    width: 150px;
  }

  .drawer-content-header-time {
    width: 110px;
  }

  .icon-img-preview:before {
    color: #1cc6a3;
  }
}
</style>