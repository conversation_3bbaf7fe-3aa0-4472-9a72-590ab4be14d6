<!--
 * FilePath     : d:\ccc\web\ccc.web\src\pages\transferHisCommon\queryClosingControlLog.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 曹恩
 * LastEditTime : 2020-12-03 16:20
 * Description  : 串检验条码打印
--> 
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { hisHisCommonUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
    }),
  },
  created() {
    this.url = hisHisCommonUrl() + "queryClosingControlLog?token=" + this.token;
  },
};
</script>
