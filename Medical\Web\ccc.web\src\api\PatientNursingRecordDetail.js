/*
 * FilePath     : \ccc.web\src\api\PatientNursingRecordDetail.js
 * Author       : LX
 * Date         : 2024-11-04 09:19
 * LastEditors  : LX
 * LastEditTime : 2024-11-04 11:41
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/NewNursingRecord";

//生成API
export const urls = {
  //取得病人数据
  GetPatientNursingRecordDetail: baseUrl + "/GetPatientNursingRecordDetail",
  //删除护理记录
  DeletePatientNursingRecordDetail:
    baseUrl + "/DeletePatientNursingRecordDetail",
  //更新护理记录
  UpdatePatientNursingRecordDetail:
    baseUrl + "/UpdatePatientNursingRecordDetail",
  //新增护理记录
  AddPatientNursingRecordDetail: baseUrl + "/AddPatientNursingRecordDetail",
};

//取得病人数据
export const GetPatientNursingRecordDetail = (params) => {
  return http.get(urls.GetPatientNursingRecordDetail, params);
};
// 删除补录记录
export const DeletePatientNursingRecordDetail = (params) => {
  return http.post(urls.DeletePatientNursingRecordDetail, qs.stringify(params));
};
// 修改补录记录
export const UpdatePatientNursingRecordDetail = (params) => {
  return http.post(urls.UpdatePatientNursingRecordDetail, params);
};
// 新增补录记录
export const AddPatientNursingRecordDetail = (params) => {
  return http.post(urls.AddPatientNursingRecordDetail, params);
};
