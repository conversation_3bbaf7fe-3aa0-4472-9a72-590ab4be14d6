/*
 * FilePath     : \src\api\Attendance.js
 * Author       : 李青原
 * Date         : 2020-04-07 19:23
 * LastEditors  : 马超
 * LastEditTime : 2024-01-25 17:23
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/Attendance";
export const urls = {
  GetPatientList: baseUrl + "/GetPatientList",
  GetAttendanceShiftNurse: baseUrl + "/GetAttendanceShiftNurse",
  GetNurseCareList: baseUrl + "/GetNurseCareList",
  GetWeb: baseUrl + "/GetWeb",
  GetHandoffAttendanceShiftNurse: baseUrl + "/GetHandoffAttendanceShiftNurse",
  GetAttendanceByNurseShift: baseUrl + "/GetAttendanceByNurseShift",
  GetAttendanceCount: baseUrl + "/GetAttendanceCount",
  ClearCare: baseUrl + "/ClearCare",
  GetAttendanceBed: baseUrl + "/GetAttendanceBed",
  GetOneNurseCarePatient: baseUrl + "/GetOneNurseCarePatient",
  MultiSave: baseUrl + "/MultiSave",
  AttendanceFast: baseUrl + "/AttendanceFast",
  GetNurseJob: "/job/nurse/GetNurseJob",
  GetDeptmentJobGroupList: "/job/nurse/GetDeptmentJobGroupList",
  LoadingYesterdayShiftByNurseID: baseUrl + "/LoadingYesterdayShiftByNurseID",
  LoadingYesterdayShift: baseUrl + "/LoadingYesterdayShift",
  GetPatientDetail: baseUrl + "/GetPatientDetail",
  GetAttendanceExplainConfig: baseUrl + "/GetAttendanceExplainConfig",
  GetUserAttenceAuthority: baseUrl + "/GetUserAttendanceAuthority"
};

export const GetPatientList = params => {
  return http.get(urls.GetPatientList, params);
};

export const GetAttendanceShiftNurse = params => {
  return http.get(urls.GetAttendanceShiftNurse, params);
};

//交班用,获取主责护士
export const GetHandoffAttendanceShiftNurse = params => {
  return http.get(urls.GetHandoffAttendanceShiftNurse, params);
};
//交班用,获取主责护士
export const GetNurseCareList = params => {
  return http.get(urls.GetNurseCareList, params);
};
//获取排班数据
export const GetWeb = params => {
  return http.get(urls.GetWeb, params);
};
//根据日期、班别ID获取上班人员
export const GetAttendanceByNurseShift = params => {
  return http.get(urls.GetAttendanceByNurseShift, params);
};

// 取得派班病人总数
export const GetAttendanceCount = params => {
  return http.get(urls.GetAttendanceCount, params);
};

//根据派班ID删除派班信息
export const ClearCare = params => {
  return http.post(urls.ClearCare, params);
};

// 取得未派班床位信息
export const GetAttendanceBed = params => {
  return http.get(urls.GetAttendanceBed, params);
};

// 取得已派一名责护病人信息
export const GetOneNurseCarePatient = params => {
  return http.get(urls.GetOneNurseCarePatient, params);
};

//保存责护派班
export const MultiSave = params => {
  return http.post(urls.MultiSave, params);
};

//保存岗位派班
export const AttendanceFast = params => {
  return http.post(urls.AttendanceFast, params);
};

//获取岗位派班
export const GetNurseJob = params => {
  return http.get(urls.GetNurseJob, params);
};
//获取科室岗位分组
export const GetDeptmentJobGroupList = params => {
  return http.get(urls.GetDeptmentJobGroupList, params);
};

//根据护士ID带入前日派班
export const LoadingYesterdayShiftByNurseID = params => {
  return http.post(urls.LoadingYesterdayShiftByNurseID, qs.stringify(params));
};

//根据病区班别带入前日派班
export const LoadingYesterdayShift = params => {
  return http.post(urls.LoadingYesterdayShift, qs.stringify(params));
};

//获取患者详情
export const GetPatientDetail = params => {
  return http.get(urls.GetPatientDetail, params);
};

//通过配置取派班标识说明
export const GetAttendanceExplainConfig = params => {
  return http.get(urls.GetAttendanceExplainConfig, params);
};
//获取用户派班权限
export const GetUserAttenceAuthority = params => {
  return http.get(urls.GetUserAttenceAuthority, params);
};
