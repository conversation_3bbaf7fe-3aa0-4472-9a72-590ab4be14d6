<!--
 * FilePath     : \ccc.web\src\pages\transferPages\transferIndicatorFactorNurse.vue
 * Author       : 曹恩
 * Date         : 2023-06-22 10:58
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-07-12 15:13
 * Description  : 护士工作量统计跳转
 * CodeIterationRecord: 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getStatisticsUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
      user: "getUser",
    }),
  },
  watch: {
    // 单页面多路由
    $route: function (to, from) {
      if (to.path == from.path) {
        this.initPage(to.query.statisticsType);
      }
    },
  },
  created() {
    this.initPage(this.$route.query.statisticsType);
  },
  methods: {
    /**
     * description: 初始化请求地址
     * return {*}
     * param {*} statisticsType
     */
    initPage(statisticsType) {
      if (!statisticsType) {
        this._showTip("warning", "缺少statisticsType参数，请确认菜单配置！");
        return;
      }
      this.url =
        getStatisticsUrl() +
        "indicatorFactorNurse?statisticsType=" +
        this.$route.query.statisticsType +
        "&token=" +
        this.token +
        "&hospitalID=" +
        this.hospitalInfo.hospitalID +
        "&stationID=" +
        this.user.stationID +
        "&empID=" +
        this.user.userID;
    },
  },
};
</script>
<style lang="scss"></style>