<!--
 * FilePath     : \src\pages\patientObserve\index.vue
 * Author       : 郭鹏超
 * Date         : 2020-07-08 20:11
 * LastEditors  : 来江禹
 * LastEditTime : 2024-10-18 15:34
 * Description  : 病情观察
 * CodeIterationRecord:
                  2022-01-08 2224 因应观察措施要可以评价加上字段(预计评价日期、实际评价日期、结果及评价人员) -正元
                  2023-06-12 3573 作为护理人员，我需要护理执行/病情观察列表默认近3天数据，并可以快速查看全部数据，以利记录快速查看 -杨欣欣
-->
<template>
  <base-layout
    class="measures-list"
    v-loading="loading"
    :element-loading-text="loadingText"
  >
    <div slot="header">
      <div class="header-left">
        <span v-if="queryAllEnable">
          <label>查看全部：</label>
          <el-switch v-model="queryAll" @change="changeDatePicker" />
        </span>
        日期：
        <el-date-picker
          v-model="startTime"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          style="width: 120px"
          placeholder="日期"
          :disabled="queryAll"
        ></el-date-picker>
        至
        <el-date-picker
          v-model="endTime"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          style="width: 120px"
          placeholder="日期"
          :disabled="queryAll"
        ></el-date-picker>
        <el-button
          class="query-button"
          icon="iconfont icon-search"
          @click="queryData"
          >查询</el-button
        >
      </div>
      <div class="header-right">
        <el-button class="add-button" icon="iconfont icon-add" @click="create()"
          >新增</el-button
        >
      </div>
    </div>
    <el-table
      ref="measuresTable"
      :data="measuresData"
      border
      stripe
      height="100%"
    >
      <el-table-column label="日期" width="120" align="center">
        <template slot-scope="measures">
          <span
            v-formatTime="{ value: measures.row.date, type: 'date' }"
          ></span>
        </template>
      </el-table-column>
      <el-table-column label="时间" width="80" align="center">
        <template slot-scope="measures">
          <span
            v-formatTime="{ value: measures.row.time, type: 'time' }"
          ></span>
        </template>
      </el-table-column>
      <el-table-column label="内容" min-width="150" header-align="center">
        <template slot-scope="measures">
          {{ measures.row.text }}
        </template>
      </el-table-column>
      <el-table-column label="记录人" width="80" align="center">
        <template slot-scope="measures">
          {{ measures.row.userName }}
        </template>
      </el-table-column>
      <el-table-column label="预计评价日期" width="140" align="center">
        <template slot-scope="measures">
          <span
            v-formatTime="{
              value: measures.row.expectEvalutionDate,
              type: 'datetime',
              format: 'yyyy-MM-dd HH:mm',
            }"
          ></span>
        </template>
      </el-table-column>
      <el-table-column label="实际评价日期" width="140" align="center">
        <template slot-scope="measures">
          <span
            v-formatTime="{
              value: measures.row.evalutionDate,
              type: 'datetime',
              format: 'yyyy-MM-dd HH:mm',
            }"
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        label="评价结果"
        prop="outCome"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column label="评价人员" width="80" align="center">
        <template slot-scope="measures">
          {{ measures.row.evalutionName }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" header-align="center">
        <template slot-scope="measures">
          <el-tooltip content="修改">
            <div class="iconfont icon-edit" @click="modify(measures.row)"></div>
          </el-tooltip>
          <el-tooltip v-if="measures.row.evalutaionRecordsCode" content="评价">
            <div
              class="iconfont icon-evaluation"
              @click="evaluation(measures.row)"
            ></div>
          </el-tooltip>
          <el-tooltip content="删除">
            <div class="iconfont icon-del" @click="del(measures.row)"></div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 显示观察措施 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="dialogTitle"
      :visible.sync="showMeasuresFlag"
      fullscreen
      :modal-append-to-body="false"
      custom-class="no-footer"
    >
      <component
        v-if="showMeasuresFlag"
        :is="dialogComponent"
        :params="dialogComponentParams"
        @close="showMeasuresFlag = false"
      ></component>
    </el-dialog>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import bedsideObservation from "@/pages/patientObserve/bedsideObservation";
import observeEvalutaion from "@/pages/patientObserve/observeEvalutaion";
import {
  GetPatientObservationList,
  DeletePatientObservation,
} from "@/api/PatientObservation";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { GetClinicalSettingByTypeCode } from "@/api/Setting";
import { mapGetters } from "vuex";
export default {
  props: {
    dialogFlag: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    baseLayout,
    bedsideObservation,
    observeEvalutaion,
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      user: "getUser",
    }),
  },
  watch: {
    "patientInfo.inpatientID": {
      handler(newValue) {
        if (!newValue) return;
        this.setTimeRange();
        this.queryData();
      },
    },
    showMeasuresFlag(newValue) {
      if (!newValue) {
        this.queryData();
      }
    },
  },
  data() {
    return {
      //开始日期
      startTime: undefined,
      //结束日期
      endTime: undefined,
      //观察措施清单
      measuresData: [],
      //加载中
      loading: false,
      //加载呈现内容
      loadingText: "加载中……",
      showMeasuresFlag: false,
      dialogTitle: "",
      isDialog: false,
      dialogComponent: "",
      dialogComponentParams: undefined,
      queryAllEnable: false,
      queryAll: false,
      startTimeRange: -2,
    };
  },
  async created() {
    await this.init();
    this.queryData();
  },
  // keepAlive时触发
  async activated() {
    //判断是否需要初始化画面
    if (this.$route.meta.refreshFlag) {
      // 需要刷新页面时，刷新病人头
      this._sendBroadcast("refreshInpatient");
      await this.init();
    }
    this.queryData();
  },
  methods: {
    async init() {
      await this.getShowQueryAllEnable();
      await this.getDefaultTimeRange();
      this.setTimeRange();
      this.isDialog = this.dialogFlag;
      if (!this.isDialog && this.$route.query) {
        this.isDialog = this.$route.query.dialogFlag;
      }
    },
    /**
     * description: 获取查询全部功能是否开启的配置
     * return {*}
     */
    async getShowQueryAllEnable() {
      const params = {
        settingTypeCode: "QueryAllObservation",
      };
      await GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.queryAllEnable = res.data;
        }
      });
    },
    /**
     * description: 查询病人的病情观察数据
     * return {*}
     */
    queryData() {
      if (!this.patientInfo) {
        return;
      }
      this.loading = true;
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        startTime: this.startTime,
        endTime: this.endTime,
      };
      GetPatientObservationList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.measuresData = result.data;
          this.$nextTick(() => this.$refs.measuresTable?.doLayout());
        }
        this.loading = false;
      });
    },
    /**
     * description: 查询全部时，调整日期选择器区间值
     * param {*} newVal 切换后的状态
     * return {*}
     */
    changeDatePicker() {
      this.setTimeRange();
      this.queryData();
    },
    /**
     * description: 新增病情观察
     * return {*}
     */
    async create() {
      if (this.isDialog) {
        this.showMeasures("病情观察", "add", "bedsideObservation", undefined);
      } else {
        this.$router.push({ name: "bedsideObservation" });
      }
    },
    /**
     * description: 删除病情观察
     * param {*} row 当前行数据
     * return {*}
     */
    async del(row) {
      if (row.evalutionDate) {
        this._showTip("warning", "以评价过不得删除");
        return;
      }
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(
        this.user,
        row.performEmployeeID
      );
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            id: row.id,
            inpatientID: this.patientInfo.inpatientID,
            performDate: row.addDate,
            performTime: row.addTime,
          };
          DeletePatientObservation(params).then((result) => {
            if (_this._common.isSuccess(result)) {
              _this._showTip("success", "删除成功！");
              // 刷新数据
              _this.queryData();
            } else {
              _this._showTip("warning", "删除失败！");
            }
          });
        }
      });
    },
    /**
     * description: 修改病情观察
     * param {*} row 当前行数据
     * return {*}
     */
    modify(row) {
      if (this.isDialog) {
        this.showMeasures("病情观察", "modify", "bedsideObservation", row);
      } else {
        this.$router.push({
          path: "/bedsideObservation",
          query: {
            data: row,
            flag: "G",
          },
        });
      }
    },
    /**
     * description: 病情观察评价
     * param {*} row 待评价数据
     * return {*}
     */
    evaluation(row) {
      if (this.isDialog) {
        this.showMeasures("病情观察评价", "", "observeEvalutaion", row);
      } else {
        this.$router.push({
          path: "/observeEvalutaion",
          query: {
            data: row,
            flag: "G",
          },
        });
      }
    },
    /**
     * description: 组装弹窗标题
     * param {*} content 原标题
     * param {*} type 当前点击类别
     * return {*}
     */
    getTitle(content, type) {
      let title = "";
      if (!this.patientInfo) {
        return title;
      }
      title =
        this.patientInfo.patientName +
        "【" +
        this.patientInfo.gender +
        "-" +
        this.patientInfo.ageDetail +
        "】" +
        " -- " +
        content;
      if (!type) {
        return title;
      }
      if (type == "add") {
        title += " -- 新增";
      } else {
        title += " -- 修改";
      }
      return title;
    },
    /**
     * description: 打开弹窗
     * param {*} title 原标题
     * param {*} type 当前点击类别
     * param {*} component 组件名
     * param {*} data 携带数据
     * return {*}
     */
    showMeasures(title, type, component, data) {
      this.dialogTitle = this.getTitle(title, type);
      this.dialogComponent = component;
      this.dialogComponentParams = {
        isDialog: true,
      };
      if (data) {
        this.dialogComponentParams.flag = "G";
        this.dialogComponentParams.data = data;
      }
      this.showMeasuresFlag = true;
    },
    /**
     * description: 设置查询时间范围
     * return {*}
     */
    setTimeRange() {
      if (this.queryAll) {
        this.startTime = this._datetimeUtil.formatDate(
          this.patientInfo.admissionDate,
          "yyyy-MM-dd"
        );
        this.endTime = this._datetimeUtil.getNow();
      } else {
        this.endTime = this._datetimeUtil.getNow();
        this.startTime = this.startTimeRange
          ? this._datetimeUtil.addDate(this.endTime, this.startTimeRange)
          : this.endTime;
      }
    },
    /**
     * description: 获取查询默认天数
     * return {*}
     */
    async getDefaultTimeRange() {
      let params = {
        settingType: "PageDefaultDate",
        settingCode: "patientObserve",
      };
      await GetClinicalSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.startTimeRange = !!res.data?.settingValue
            ? Number(res.data?.settingValue)
            : -2;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.measures-list {
  .header-left {
    float: left;
  }
  .header-right {
    float: right;
  }
}
</style>
