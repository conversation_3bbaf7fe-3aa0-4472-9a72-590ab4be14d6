<!--
 * FilePath     : \src\pages\patientAllergy\components\allergy.vue
 * Author       : 孟昭永
 * Date         : 2022-03-03 18:46
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-22 11:43
 * Description  : 过敏页面
 * CodeIterationRecord: 2022-07-27 过敏史调整自定义过敏页面-可以选择药品然后联动到对应的过敏类别 -zxz
                        2023-06-27 3587-护理评估按钮跳转专项录入的内容评估时间跟护理评估保持一致 -杨欣欣
-->
<template>
  <base-layout class="new-drug-list" :showHeader="false" v-loading="fullscreenLoading" element-loading-text="加载中……">
    <div class="drug-food">
      <div class="drug">
        <div class="label-name">
          药物过敏史：
          <el-radio-group v-model="drugAllergy" @change="selectDrug()">
            <el-radio :label="false">无药物过敏史</el-radio>
            <el-radio :label="true">有药物过敏史</el-radio>
          </el-radio-group>
          <el-button class="add-button" icon="el-icon-plus" @click="customDialog('D')">自定义</el-button>
        </div>
        <el-table
          ref="drugTable"
          tooltip-effect
          :data="drugDictionaries"
          class="drug-table"
          border
          stripe
          height="100%"
          @selection-change="allergySelectionChange($event, 0)"
          :header-cell-class-name="cellClass"
        >
          <el-table-column
            type="selection"
            :width="convertPX(40)"
            align="center"
            :selectable="
              () => {
                return drugAllergy;
              }
            "
          ></el-table-column>
          <el-table-column prop="allergyName" label="药物" min-width="60" show-overflow-tooltip></el-table-column>
          <el-table-column label="记录日期" header-align="center" width="105" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.addDate"
                :readonly="!drugAllergy || !scope.row.checkBox"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                type="date"
                class="new-drug-list-table-date"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="记录时间" header-align="center" align="center" width="80">
            <template slot-scope="scope">
              <el-time-picker
                v-model="scope.row.addTime"
                :readonly="!drugAllergy || !scope.row.checkBox"
                value-format="HH:mm"
                format="HH:mm"
                class="new-drug-list-table-time"
              ></el-time-picker>
            </template>
          </el-table-column>
          <el-table-column label="过敏情况" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input
                :readonly="!drugAllergy || !scope.row.checkBox"
                v-model="scope.row.explain"
                placeholder="请输入说明"
              ></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="food">
        <div class="label-name">
          食物过敏史：
          <el-radio-group v-model="foodAllergy" class="food-top" @change="selectFood()">
            <el-radio :label="false">无食物过敏史</el-radio>
            <el-radio :label="true">有食物过敏史</el-radio>
          </el-radio-group>
          <el-button class="add-button" icon="el-icon-plus" @click="customDialog('F')">自定义</el-button>
        </div>
        <el-table
          ref="foodTable"
          tooltip-effect
          :data="foodDictionaries"
          class="top"
          border
          stripe
          height="100%"
          @selection-change="allergySelectionChange($event, 1)"
          :row-style="rowClass"
          :header-cell-class-name="cellClass"
        >
          <el-table-column
            type="selection"
            :width="convertPX(40)"
            align="center"
            :selectable="
              () => {
                return foodAllergy;
              }
            "
          ></el-table-column>
          <el-table-column prop="allergyName" label="食物" min-width="60" show-overflow-tooltip></el-table-column>
          <el-table-column label="记录日期" header-align="center" width="105" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.addDate"
                :readonly="!foodAllergy || !scope.row.checkBox"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                type="date"
                class="new-drug-list-table-date"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="记录时间" header-align="center" align="center" width="80">
            <template slot-scope="scope">
              <el-time-picker
                v-model="scope.row.addTime"
                :readonly="!foodAllergy || !scope.row.checkBox"
                value-format="HH:mm"
                format="HH:mm"
                class="new-drug-list-table-time"
              ></el-time-picker>
            </template>
          </el-table-column>
          <el-table-column label="过敏情况" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input
                :readonly="!foodAllergy || !scope.row.checkBox"
                v-model="scope.row.explain"
                placeholder="请输入说明"
              ></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="other-save">
      <div class="other">
        <div class="label-name">
          其他过敏史：
          <el-radio-group v-model="otherAllergy" @change="selectOther()">
            <el-radio :label="false">无其他过敏史</el-radio>
            <el-radio :label="true">有其他过敏史</el-radio>
          </el-radio-group>
          <el-button class="add-button" icon="el-icon-plus" @click="customDialog('O')">自定义</el-button>
        </div>
        <el-table
          ref="otherTable"
          tooltip-effect
          :data="otherDictionaries"
          class="top"
          border
          stripe
          height="100%"
          @selection-change="allergySelectionChange($event, 2)"
          :header-cell-class-name="cellClass"
        >
          <el-table-column
            type="selection"
            :width="convertPX(40)"
            align="center"
            :selectable="
              () => {
                return otherAllergy;
              }
            "
          ></el-table-column>
          <el-table-column prop="allergyName" label="其他" min-width="60" show-overflow-tooltip></el-table-column>
          <el-table-column label="记录日期" header-align="center" width="105" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.addDate"
                :readonly="!otherAllergy || !scope.row.checkBox"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                type="date"
                class="new-drug-list-table-date"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="记录时间" header-align="center" align="center" width="80">
            <template slot-scope="scope">
              <el-time-picker
                v-model="scope.row.addTime"
                :readonly="!otherAllergy || !scope.row.checkBox"
                value-format="HH:mm"
                format="HH:mm"
                class="new-drug-list-table-time"
              ></el-time-picker>
            </template>
          </el-table-column>
          <el-table-column label="过敏情况" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-input
                :readonly="!otherAllergy || !scope.row.checkBox"
                v-model="scope.row.explain"
                placeholder="请输入说明"
              ></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="save">
        <div class="label-name">过敏史：</div>
        <el-input
          type="textarea"
          :rows="7"
          :readonly="true"
          placeholder="请输入内容"
          v-model="allergyHistory"
        ></el-input>
        <div class="allergy-save">
          <el-button type="primary" class="save-button" icon="iconfont icon-save-button" @click="saveDrug">
            保存
          </el-button>
        </div>
      </div>
    </div>

    <el-dialog title="自定义过敏内容" :visible.sync="addAllergyVisible" :close-on-click-modal="false" width="500px">
      <el-row class="top" v-if="displayName == '过敏药物'">
        <el-col :span="4"><span>药品：</span></el-col>
        <el-col :span="20">
          <el-select
            :loading="loadRemoteDrugList"
            filterable
            :filter-method="getDrugListByAbbr"
            clearable
            @keyup.enter.native="enterSelect"
            v-model="selectedDrugCode"
            placeholder="请输入简拼,按下回车检索"
            @change="changeSelectValue"
            class="container"
          >
            <el-option v-for="item in drugOptions" :key="item.druglistID" :label="item.drugName" :value="item.drugCode">
              <span class="el-option-left">{{ item.drugName }}</span>
              <span class="el-option-right">{{ item.drugCode }}</span>
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row class="top">
        <el-col :span="4">
          <span>{{ displayName }}：</span>
        </el-col>
        <el-col :span="20">
          <el-input v-model="allergyName" class="container" placeholder="请输入名称"></el-input>
        </el-col>
      </el-row>
      <el-row class="top">
        <el-col :span="4">
          <span>过敏情况：</span>
        </el-col>
        <el-col :span="20">
          <el-input v-model="explain" class="container" placeholder="请输入说明"></el-input>
        </el-col>
      </el-row>

      <span slot="footer" class="dialog-footer">
        <el-button @click="addAllergyVisible = false">取消</el-button>
        <el-button type="primary" @click="customSave()">确 定</el-button>
      </span>
    </el-dialog>
  </base-layout>
</template>
<script>
import { GetDrugListsByAbbr, GetPatientAllergy, SavePatientAllergy } from "@/api/Allergy";
import baseLayout from "@/components/BaseLayout";
export default {
  components: {
    baseLayout,
  },
  props: {
    patient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      //药物字典
      drugDictionaries: [],
      foodDictionaries: [],
      //选择药物
      drugList: [],
      foodList: [],
      selectDrugList: [],
      selectFoodList: [],
      cloneList: [],
      //是否有食物过敏
      foodAllergy: false,
      saveData: {
        addAllergy: [],
        delAllergy: [],
        updateAllergy: [],
      },
      //是否有药物过敏
      drugAllergy: false,
      allergyHistory: "",
      chartNo: "",
      inpatientID: undefined,
      fullscreenLoading: false,
      addAllergyVisible: false,
      allergyType: "",
      allergyName: "",
      explain: "",
      displayName: "",
      assessMainID: "",
      handoverID: "",
      //药品字典下拉框列表
      drugOptions: [],
      //下拉框中选中的值
      selectedDrugCode: "",
      //下拉框远程搜索的时候显示加载中
      loadRemoteDrugList: false,
      //实时记录下拉框简拼的值
      inputValue: undefined,
      //旧的记录下拉框简拼的值，防止重复点击enter键造成多次相同请求
      oldInputValue: undefined,
      //记录过敏药物类别对应的药理码
      chemicCode: undefined,
      //用来表示监听的下拉框中enter键是否被成功按下
      enterUpFlag: false,
      //是否有其他过敏
      otherAllergy: false,
      //其他过敏选项列表
      otherDictionaries: [],
      //选中的其他过敏
      otherList: [],
      //
      selectOtherList: [],
    };
  },
  watch: {
    patient: {
      handler(newValue) {
        if (!newValue) {
          return;
        }
        this.inpatientID = newValue.inpatientID;
        this.chartNo = newValue.chartNo;
        this.init();
      },
    },
    immediate: true,
  },
  mounted() {
    //菜单进入
    if (this.patient) {
      this.chartNo = this.patient.chartNo;
      this.inpatientID = this.patient.inpatientID;
    }
    //评估模板跳转
    this.$route.query.chartNo && (this.chartNo = this.$route.query.chartNo);
    this.$route.query.inpatientID && (this.inpatientID = this.$route.query.inpatientID);
    this.$route.query.num && (this.assessMainID = this.$route.query.num);
    this.$route.query.handoverID && (this.handoverID = this.$route.query.handoverID);
    if (!this.chartNo) {
      this._showTip("warning", "请选择病人");
    }
    // 设置切换病人
    if (this.$route.query.inpatientID && this.$route.query.chartNo) {
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
    this.init();
  },
  methods: {
    /**
     * description: 初始加载
     * param {*}
     * return {*}
     */
    init() {
      this.drugSelect();
      this.drugList = [];
      this.foodList = [];
      this.otherList = [];
      this.drugDictionaries = [];
      this.foodDictionaries = [];
      this.otherDictionaries = [];
      this.drugAllergy = false;
      this.foodAllergy = false;
      this.otherAllergy = false;
      //获取过敏药物数据
      this.getAllergy();
    },
    /**
     * description: 获取过敏药物数据
     * param {*}
     * return {*}
     */
    getAllergy() {
      let params = {
        inpatientID: this.inpatientID,
        index: Math.random(),
      };
      GetPatientAllergy(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let keyArr = ["drug", "food", "other"];
          result.data.forEach((item) => {
            let key = undefined;
            item.allergyType == "D" && (key = keyArr[0]);
            item.allergyType == "F" && (key = keyArr[1]);
            item.allergyType == "O" && (key = keyArr[2]);
            if (key) {
              this[key + "Dictionaries"].push(item);
              item.checkBox = false;
              if (item.patientAllergyID) {
                item.checkBox = true;
                this[key + "List"].push(item);
              }
            }
          });
          this.cloneList = this._common.clone(this.drugList.concat(this.foodList, this.otherList));
          this.drugList.length && (this.drugAllergy = true);
          this.foodList.length && (this.foodAllergy = true);
          this.otherList.length && (this.otherAllergy = true);
          //初始化加载选项
          this.toggleSelection(this.foodList, "F");
          this.toggleSelection(this.drugList, "D");
          this.toggleSelection(this.otherList, "O");
          //修复表格错列问题
          this.$nextTick(() => {
            this.$refs.drugTable.doLayout();
            this.$refs.foodTable.doLayout();
            this.$refs.otherTable.doLayout();
          });
        }
      });
    },

    /**
     * description: 保存修改删除过敏药物
     * param {*}
     * return {*}
     */
    async saveDrug() {
      if (this.drugAllergy && this.selectDrugList.length <= 0) {
        this._showTip("warning", "请选择要添加的药物过敏");
        return;
      }
      if (this.foodAllergy && this.selectFoodList.length <= 0) {
        this._showTip("warning", "请选择要添加的食物过敏");
        return;
      }
      if (this.otherAllergy && this.selectOtherList.length <= 0) {
        this._showTip("warning", "请选择要添加的其他过敏");
        return;
      }
      this.saveData = {
        addAllergy: [],
        delAllergy: [],
        updateAllergy: [],
      };
      this.saveDataAssemble();
      this.fullscreenLoading = true;
      this.saveData.inpatientID = this.inpatientID;
      this.saveData.sourceID = this.assessMainID ? this.assessMainID : this.handoverID;
      SavePatientAllergy(this.saveData).then((res) => {
        this.fullscreenLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.init();
          this._sendBroadcast("refreshInpatient");
        }
      });
    },
    /**
     * description: 组装保存数据
     * return {*}
     */
    saveDataAssemble() {
      let selectAllergyList = this.selectDrugList.concat(this.selectFoodList, this.selectOtherList);
      selectAllergyList.forEach((item) => {
        if (!item.patientAllergyID) {
          if (!item.allergyName) {
            item.allergyName = "";
          }
          this.saveData.addAllergy.push({
            allergyBasicID: item.allergyBasicID,
            caseNumber: "",
            customName: item.allergyBasicID == "9999" ? item.allergyName : "",
            chartNo: this.chartNo,
            explain: item.explain,
            allergyType: item.allergyType,
            addDate: item.addDate,
            addTime: item.addTime,
            sourceID: this.assessMainID ? this.assessMainID : this.handoverID,
            drugCode: item.drugCode,
            RefillFlag: this.patient ? this.patient.supplemnentFlag : undefined,
          });
        }
      });
      this.cloneList.forEach((item) => {
        let tem = selectAllergyList.filter((element) => {
          return element.patientAllergyID == item.patientAllergyID;
        });
        if (tem.length <= 0) {
          this.saveData.delAllergy.push(item.patientAllergyID);
        } else {
          this.saveData.updateAllergy.push({
            patientAllergyID: tem[0].patientAllergyID,
            explain: tem[0].explain,
            addDate: tem[0].addDate,
            addTime: tem[0].addTime,
            customName: item.allergyBasicID == "9999" ? item.allergyName : "",
            RefillFlag: this.patient ? this.patient.supplemnentFlag : undefined,
          });
        }
      });
    },
    /**
     * description: 表格颜色根据条件变化
     * param {*} row
     * param {*} index
     * return {*}
     */
    rowClass(row, index) {
      if (row.row.allergyLevel == 1) {
        return { color: "#7b7de3", "font-weight": "bold" };
      }
    },
    /**
     * description: 去除全选
     * param {*} row
     * return {*}
     */
    cellClass(row) {
      if (row.columnIndex === 0) {
        return "disabledCheck";
      }
    },
    /**
     * description: 表格勾选内容变化触发
     * param {*} rows
     * param {*} typeIndex
     * return {*}
     */
    allergySelectionChange(rows, typeIndex) {
      let keyArr1 = ["selectDrugList", "selectFoodList", "selectOtherList"];
      let keyArr2 = ["drugDictionaries", "foodDictionaries", "otherDictionaries"];
      this[keyArr1[typeIndex]] = rows;
      if (rows.some((m) => m.isInit)) {
        this[keyArr1[typeIndex]].forEach((m) => (m.isInit = false));
      } else {
        this[keyArr2[typeIndex]].forEach((item) => {
          let list = this[keyArr1[typeIndex]].filter((tem) => {
            return tem.patientAllergyID == item.patientAllergyID && tem.allergyBasicID == item.allergyBasicID;
          });
          if (list.length <= 0) {
            item.checkBox = false;
            item.addDate = undefined;
            item.addTime = undefined;
            item.explain = undefined;
          } else {
            item.checkBox = true;
            if (!item.addDate && !item.addTime) {
              item.addDate = this._datetimeUtil.getNowDate();
              item.addTime = this._datetimeUtil.getNowTime();
            }
          }
        });
      }
      this.drugSelect();
    },
    /**
     * description: 初始化加载选择项
     * param {*} rows
     * param {*} flag
     * return {*}
     */
    toggleSelection(rows, flag) {
      if (flag == "D") {
        if (rows) {
          rows.forEach((row) => {
            row.isInit = true;
            if (this.$refs.drugTable) {
              this.$refs.drugTable.toggleRowSelection(row);
            }
          });
        } else {
          this.$refs.drugTable.clearSelection();
        }
      }
      if (flag == "F") {
        if (rows) {
          rows.forEach((row) => {
            row.isInit = true;
            if (this.$refs.foodTable) {
              this.$refs.foodTable.toggleRowSelection(row);
            }
          });
        } else {
          this.$refs.foodTable.clearSelection();
        }
      }
      if (flag == "O") {
        if (rows) {
          rows.forEach((row) => {
            row.isInit = true;
            if (this.$refs.otherTable) {
              this.$refs.otherTable.toggleRowSelection(row);
            }
          });
        } else {
          this.$refs.otherTable.clearSelection();
        }
      }
    },
    /**
     * description: 自定义过敏弹框
     * param {*} flag
     * return {*}
     */
    customDialog(flag) {
      switch (flag) {
        case "D":
          this.displayName = "过敏药物";
          break;
        case "F":
          this.displayName = "过敏食物";
          break;
        case "O":
          this.displayName = "其他过敏";
          break;
      }
      this.addAllergyVisible = true;
      this.selectedDrugCode = undefined;
      this.drugOptions = [];
      this.oldInputValue = undefined;
      this.inputValue = undefined;
      this.allergyType = flag;
      this.allergyName = "";
      this.explain = "";
      this.chemicCode = undefined;
    },
    /**
     * description: 自定义过敏
     * param {*}
     * return {*}
     */
    customSave() {
      if (!this.allergyName.trim()) {
        this.allergyName = "";
        this._showTip("warning", "请输入过敏药物名称");
        return;
      }
      let item = {
        patientAllergyID: undefined,
        allergyBasicID: "9999",
        allergyName: this.allergyName,
        customName: this.allergyName,
        explain: this.explain,
        allergyType: this.allergyType,
        addDate: this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate(),
        addTime: this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime(),
        checkBox: false,
        drugCode: this.selectedDrugCode,
      };
      if (this.allergyType == "D") {
        let existedItem =
          this.$refs.drugTable.data &&
          this.$refs.drugTable.data.find((m) => m.chemicCode == this.chemicCode && m.allergyName == this.allergyName);
        //1.修改过敏情况 2.将存在的一行置为选中状态
        if (existedItem) {
          existedItem.explain = existedItem.explain ? existedItem.explain + "," + this.explain : this.explain;
          this.$refs.drugTable.toggleRowSelection(existedItem, true);
        } else {
          this.drugDictionaries.push(item);
          this.$refs.drugTable.toggleRowSelection(item);
        }
      }
      if (this.allergyType == "F") {
        this.foodDictionaries.push(item);
        this.$refs.foodTable.toggleRowSelection(item);
      }
      if (this.allergyType == "O") {
        this.otherDictionaries.push(item);
        this.$refs.otherTable.toggleRowSelection(item);
      }
      this.addAllergyVisible = false;
    },
    /**
     * description: 是否有过敏药物
     * param {*}
     * return {*}
     */
    selectDrug() {
      if (!this.drugAllergy) {
        this.$refs.drugTable.clearSelection();
        this.selectDrugList = [];
      }
      this.drugSelect();
    },
    /**
     * description: 是否有过敏食物
     * param {*}
     * return {*}
     */
    selectFood() {
      if (!this.foodAllergy) {
        this.$refs.foodTable.clearSelection();
        this.selectFoodList = [];
      }
      this.drugSelect();
    },
    selectOther() {
      if (!this.otherAllergy) {
        this.$refs.otherTable.clearSelection();
        this.selectOtherList = [];
      }
      this.drugSelect();
    },
    /**
     * description: 选择时拼接过敏史
     * param {*}
     * return {*}
     */
    drugSelect() {
      let text = "";
      if (!this.drugAllergy && !this.foodAllergy && !this.otherAllergy) {
        this.allergyHistory = "否认药物过敏史，否认食物及其他过敏史";
        return;
      }
      if (!this.drugAllergy) {
        text = "否认药物过敏史。";
      } else {
        if (this.selectDrugList.length <= 0) {
          text = "有药物过敏史。";
        } else {
          text += "有";
          text += this.drugSplicing(this.selectDrugList);
          text += "药物过敏。";
        }
      }
      if (!this.foodAllergy) {
        text += "否认食物过敏史。";
      } else {
        if (this.selectFoodList.length <= 0) {
          text += "有食物过敏史。";
        } else {
          text += "有";
          text += this.drugSplicing(this.selectFoodList);
          text += "食物过敏。";
        }
      }
      if (!this.otherAllergy) {
        text += "否认其他过敏史。";
      } else {
        if (this.selectOtherList.length <= 0) {
          text += "有其他过敏史。";
        } else {
          text += "有";
          text += this.drugSplicing(this.selectOtherList);
          text += "其他过敏。";
        }
      }
      this.allergyHistory = text;
    },

    /**
     * description: 过敏拼接
     * param {*} selectList
     * return {*}
     */
    drugSplicing(selectList) {
      let text = "";
      selectList.forEach((item) => {
        if (item.allergyName == selectList[selectList.length - 1].allergyName) {
          text += item.allergyName;
        } else {
          text += item.allergyName + "、";
        }
      });
      return text;
    },

    /**
     * description:根据简拼获取药品集合
     * return {*}
     */
    getDrugListByAbbr(value) {
      //没有按enter键时，只记录值，不进行远端查询
      if (!this.enterUpFlag) {
        this.inputValue = value;
        return;
      }
      //this.selectedDrugCode = this.inputValue;
      if (this.oldInputValue == this.inputValue) {
        this.enterUpFlag = false;
        return;
      }
      this.oldInputValue = this.inputValue;
      let params = {
        abbr: this.inputValue,
      };
      this.loadRemoteDrugList = true;
      GetDrugListsByAbbr(params).then((resp) => {
        this.loadRemoteDrugList = false;
        //将状态置回初始状态（enter键没有被按下）
        this.enterUpFlag = false;
        //更新下拉框列表内容
        if (this._common.isSuccess(resp)) {
          this.drugOptions = resp.data;
        }
        //重新进行简拼查询，如果没有查到内容，将原本已经选择的数据清空
        if (this.drugOptions && this.drugOptions.length == 0) {
          this.selectedDrugCode = undefined;
          this.chemicCode = undefined;
          this.allergyName = "";
        }
      });
    },
    /**
     * description: 输入文本后点击enter键进行简拼查询
     *              监听键盘的方法无法获取当前文本框中输入的值，只起到监听enter键的作用
     * return {*}
     */
    enterSelect() {
      //标记enter键已经点击完成，需要开始向后台查询
      this.enterUpFlag = true;
      this.getDrugListByAbbr();
    },
    /**
     * description: 简拼下拉框-选中内容改变后-触发
     *              1.赋值药理码chemicCode 2.动态联动自定义的过敏药品名称
     * return {null}
     */
    changeSelectValue() {
      //根据药品码找出对应的药品对应的chemicCode
      let drugInfo = this.selectedDrugCode
        ? this.drugOptions.find((m) => m.drugCode == this.selectedDrugCode)
        : undefined;
      if (!drugInfo) {
        this.selectedDrugCode = undefined;
        return;
      }
      //更新data{}中的药理码,药理码为空时，药品名称作为过敏类型名称
      this.chemicCode = drugInfo.chemicCode;
      if (!this.chemicCode) {
        this.allergyName = drugInfo.drugName;
      }
      //根据所选药品，匹配，自动填充自定义页面中过敏药物名称输入框
      let allergyInfo = this.drugDictionaries.find((m) => m.chemicCode == this.chemicCode);
      this.allergyName = allergyInfo ? allergyInfo.allergyName : drugInfo.drugName;
    },
  },
};
</script>
<style lang="scss">
.new-drug-list {
  height: 100%;
  background-color: #fff;
  padding: 10px;
  .drug-food {
    height: 67%;
    .drug {
      float: left;
      width: 49%;
      height: 100%;
      .drug-table {
        height: calc(100%-50px);
      }
    }
    .food {
      float: left;
      width: 49%;
      height: 100%;
      margin-left: 2%;
    }
  }
  .other-save {
    margin-top: 3%;
    height: 22%;
    .other {
      float: left;
      width: 49%;
      height: 100%;
    }
    .save {
      float: left;
      width: 49%;
      height: 100%;
      margin-left: 2%;
      .allergy-history {
        height: 100%;
      }
      .allergy-save {
        position: absolute;
        bottom: 10px;
        right: 10px;
      }
    }
  }
  .label-name {
    margin-top: 5px;
  }
  .add-button {
    float: right;
  }
  .el-dialog {
    height: 400px;
    .el-option-left {
      float: left;
    }
    .el-option-right {
      float: right;
      color: #8492a6;
      font-size: 12px;
    }
  }
  .el-table .disabledCheck .cell .el-checkbox__inner {
    display: none !important;
  }

  .new-drug-list-table-date {
    width: 90px;
  }
  .new-drug-list-table-time {
    width: 50px;
  }
}
</style>
