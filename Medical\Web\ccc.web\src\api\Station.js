/*
 * FilePath     : \src\api\Station.js
 * Author       : 郭鹏超
 * Date         : 2019-10-22 16:36
 * LastEditors  : 苏军志
 * LastEditTime : 2021-05-03 10:12
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/StationList";

export const urls = {
  GetStationList: baseUrl + "/GetStationList",
  GetDepartmentDataByStationID: baseUrl + "/GetDepartmentDataByStationID",
  GetStationByID: baseUrl + "/GetStationByID",
  GetStationListByCode: baseUrl + "/GetStationListByCode",
  GetHeadNurseList: baseUrl + "/GetHeadNurseList",
};

// 获取护理单元
export const GetStationList = params => {
  return http.get(urls.GetStationList, params);
};
// 获取科室
export const GetDepartmentDataByStationID = params => {
  return http.get(urls.GetDepartmentDataByStationID, params);
};
//获取单位名称
export const GetStationByID = params => {
  return http.get(urls.GetStationByID, params);
};
// 根据病区代码获取病区名称
export const GetStationListByCode = params => {
  return http.get(urls.GetStationListByCode, params);
};
// 获取科护士长
export const GetHeadNurseList = params => {
  return http.get(urls.GetHeadNurseList, params);
};
