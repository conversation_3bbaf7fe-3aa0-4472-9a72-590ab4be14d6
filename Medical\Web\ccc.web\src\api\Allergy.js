/*
 * FilePath     : \src\api\Allergy.js
 * Author       : 陈超然
 * Date         : 2020-05-18 15:21
 * LastEditors  : 苏军志
 * LastEditTime : 2023-07-22 18:17
 * Description  :添加过敏药物地址
 */

import qs from "qs";
import http from "../utils/ajax";
var baseUrl = "/Allergy";
var supplementBaseUrl = "AllergySupplement";

export const urls = {
  GetPatientAllergy: baseUrl + "/GetPatientAllergy",
  SavePatientAllergy: baseUrl + "/SavePatientAllergy",
  GetSkinTestByChartNo: baseUrl + "/GetSkinTestByChartNo",
  SaveSkinTest: baseUrl + "/SaveSkinTest",
  GetAllergyDrugList: baseUrl + "/GetAllergyDrugList",
  DeleteSkinTestByID: baseUrl + "/DeleteSkinTestByID",
  GetDrugListsByAbbr: baseUrl + "/GetDrugListsByAbbr"
};

// 获取过敏药物字典和病人过敏药物数据
export const GetPatientAllergy = params => {
  return http.get(urls.GetPatientAllergy, params);
};
// 添加一条病人过敏药物数据
export const SavePatientAllergy = params => {
  return http.post(urls.SavePatientAllergy, params);
};
// 根据ChartNO查询皮试记录
export const GetSkinTestByChartNo = params => {
  return http.get(urls.GetSkinTestByChartNo, params);
};
// 保存皮试记录
export const SaveSkinTest = params => {
  return http.post(urls.SaveSkinTest, params);
};
// 获取过敏药物类型集合
export const GetAllergyDrugList = params => {
  return http.get(urls.GetAllergyDrugList, params);
};
// 根据主键删除皮试记录
export const DeleteSkinTestByID = params => {
  return http.post(urls.DeleteSkinTestByID, qs.stringify(params));
};
//根据简拼获取对应的药品内容
export const GetDrugListsByAbbr = params => {
  return http.get(urls.GetDrugListsByAbbr, params);
};
