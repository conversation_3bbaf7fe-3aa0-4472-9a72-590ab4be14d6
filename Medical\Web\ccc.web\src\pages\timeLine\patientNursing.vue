<!--
 * FilePath     : \src\pages\timeLine\patientNursing.vue
 * Author       : 苏军志
 * Date         : 2020-06-09 10:37
 * LastEditors  : 苏军志
 * LastEditTime : 2020-11-17 14:09
 * Description  : 
-->
<template>
  <div class="patient-nursing" v-loading="loading" element-loading-text="加载中……">
    <div class="btn">
      <el-button class="print-button" @click="$router.go(-1)">返回</el-button>
    </div>
    <time-line templateName="nursing.xml" @callback="callback" @linkClick="linkClick"></time-line>
  </div>
</template>

<script>
import TimeLine from "./components/TimeLine.vue";
import { GetPatientNursingData } from "@/api/TimeLine";
import { mapGetters } from "vuex";
export default {
  components: {
    TimeLine,
  },
  data() {
    return {
      timeLine: undefined,
      loading: false,
      patientNursingData: undefined,
    };
  },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
    }),
  },
  watch: {
    patient(newPatient) {
      if (newPatient) {
        this.getPatientNursingData();
      } else {
        this.patientNursingData = [];
      }
    },
  },
  created() {
    // 设置不可切换病人
    this._sendBroadcast("setPatientSwitch", false);
    if (this.patient) {
      this.getPatientNursingData();
    }
  },
  methods: {
    callback(timeLine) {
      this.timeLine = timeLine;
      this.timeLine.init();
    },
    linkClick(linkStr) {
      // 绑定link单击事件
      if (!linkStr || linkStr === "#" || linkStr.indexOf(";") == -1) {
        return;
      }
      let str = linkStr.split(";");
      if (str[0] == "schedule") {
        this._showTip("info", str[1]);
      }
    },
    getPatientNursingData() {
      this.loading = true;
      let params = {
        inpatientID: this.patient.inpatientID,
        // inpatientID: "93d2e51aafaa4498974769683658d4c6"
      };
      this.patientNursingData = [];
      GetPatientNursingData(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.patientNursingData = result.data;
          if (this.patientNursingData) {
            this.initTemplate();
          }
        } else {
          this.loading = false;
        }
      });
    },
    checkData(data) {
      let flag = data && data != "null";
      if (flag) {
        // 做首字母大写转换，原因：时间轴给的接口首字母必须大写
      }
      return flag;
    },
    initTemplate() {
      let dataList = [];
      // 动态添加Y轴
      if (this.checkData(this.patientNursingData.yAxislist)) {
        // this.patientNursingData.yAxislist.forEach((yAxis) => {
        //   yAxis.scales = this.scales;
        // });
        this.timeLine.addYAxis(this.patientNursingData.yAxislist);
      }

      // 添加科室
      if (this.checkData(this.patientNursingData.stationList)) {
        dataList.push({
          Name: "Station",
          Datas: this.patientNursingData.stationList,
        });
        // this.patientNursingData.stationList.forEach(station => {
        //   this.timeLine.setTextValue(
        //     "Station",
        //     station.startTime,
        //     station.text,
        //     station.endTime,
        //     station.link,
        //     station.linkTarget
        //   );
        // });
      }
      // 添加班次
      if (this.checkData(this.patientNursingData.shiftList)) {
        dataList.push({
          Name: "Shift",
          Datas: this.patientNursingData.shiftList,
        });
        // this.patientNursingData.shiftList.forEach(shift => {
        //   this.timeLine.setTextValue("Shift", shift.startTime, shift.text, shift.endTime, shift.link, shift.linkTarget);
        // });
      }
      // 添加责任护士
      if (this.checkData(this.patientNursingData.nurseList)) {
        dataList.push({
          Name: "Nurse",
          Datas: this.patientNursingData.nurseList,
        });
        // this.patientNursingData.nurseList.forEach(nurse => {
        //   this.timeLine.setTextValue("Nurse", nurse.startTime, nurse.text, nurse.endTime, nurse.link, nurse.linkTarget);
        // });
      }
      // 添加护理级别
      if (this.checkData(this.patientNursingData.nursingLevelList)) {
        dataList.push({
          Name: "NursingLevel",
          Datas: this.patientNursingData.nursingLevelList,
        });
        // this.patientNursingData.nursingLevelList.forEach(nursingLevel => {
        //   this.timeLine.setTextValue(
        //     "NursingLevel",
        //     nursingLevel.startTime,
        //     nursingLevel.text,
        //     nursingLevel.endTime,
        //     nursingLevel.backGroundColor,
        //     nursingLevel.link,
        //     nursingLevel.linkTarget
        //   );
        // });
      }
      // 添加文本时间轴，目前只有入科时间
      if (this.checkData(this.patientNursingData.textList)) {
        dataList.push({
          Name: "TextY",
          Datas: this.patientNursingData.textList,
        });
        // this.patientNursingData.textList.forEach(text => {
        //   this.timeLine.setTextValue("TextY", text.startTime, text.text, text.color, text.link, text.linkTarget);
        // });
      }
      // 添加风险
      if (this.checkData(this.patientNursingData.riskData)) {
        this.patientNursingData.riskData.forEach((type) => {
          dataList.push({
            Name: type.name,
            Datas: type.scoreList,
          });
          // type.scoreList.forEach(risk => {
          //   this.timeLine.setPointValue(type.name, risk.startTime, risk.value, risk.link, risk.linkTarget);
          // });
        });
      }
      // 添加措施
      if (this.checkData(this.patientNursingData.scheduleList)) {
        dataList.push({
          Name: "Schedule",
          Datas: this.patientNursingData.scheduleList,
        });
        // this.patientNursingData.scheduleList.forEach(schedule => {
        //   this.timeLine.setPointValue(
        //     "Schedule",
        //     schedule.startTime,
        //     schedule.value,
        //     schedule.link,
        //     schedule.linkTarget
        //   );
        // });
      }
      // 批量赋值
      this.timeLine.addValuePoints(dataList);
      // 护理问题
      if (this.checkData(this.patientNursingData.nursingProblemList)) {
        for (var i = 0; i < this.patientNursingData.nursingProblemList.length - 1; i++) {
          var nursingProblem = this.patientNursingData.nursingProblemList[i];
          this.timeLine.setTextValue(
            "NursingProblem" + (i + 1),
            nursingProblem.time,
            nursingProblem.text,
            nursingProblem.title,
            nursingProblem.endTime,
            nursingProblem.color,
            "#"
          );
        }
      }
      this.timeLine.refreshView();
      this.loading = false;
    },
  },
};
</script>

<style lang="scss">
.patient-nursing {
  height: 100%;
  .btn {
    width: 100%;
    text-align: right;
    margin-bottom: 8px;
    padding-right: 15px;
    box-sizing: border-box;
  }
}
</style>
