<!--
@selectPatientData： 回调事件，回传选中的病人信息
@change：回调事件，病人信息改变触发
 -->
<template>
  <div class="search-patient">
    <div class="top">
      <query-field-input
        ref="queryFieldInput"
        :queryField="queryField"
        @data="handleData"
        :direction="true"
        :defaultValue="defaultValue"
      />
    </div>
    <el-table
      :data="patientData"
      border
      stripe
      highlight-current-row
      @row-click="selectPatientData"
      max-height="120"
      :row-style="showColumn"
    >
      <el-table-column prop="localCaseNumber" label="住院号" min-width="110" align="center"></el-table-column>
      <el-table-column prop="patientName" label="姓名" align="center"></el-table-column>
      <el-table-column prop="gender" label="性别" min-width="50" align="center"></el-table-column>
      <el-table-column prop="ageDetail" label="年龄" min-width="50" align="center"></el-table-column>
      <el-table-column
        prop="stationName"
        label="病区"
        min-width="150"
        header-align="center"
        align="left"
      ></el-table-column>
      <el-table-column
        prop="departmentListName"
        label="科别"
        min-width="150"
        header-align="center"
        align="left"
      ></el-table-column>
      <el-table-column prop="bedNumber" label="床位号" align="center"></el-table-column>
      <el-table-column label="入院日期时间" min-width="160" align="center">
        <template slot-scope="scope">
          <span
            v-formatTime="{
              value: scope.row.admissionDateTimeView,
              type: 'date',
            }"
          ></span>
          <span
            v-formatTime="{
              value: scope.row.admissionDateTimeView,
              type: 'time',
            }"
          ></span>
        </template>
      </el-table-column>
      <el-table-column label="出院日期时间" min-width="160" align="center">
        <template slot-scope="scope">
          <span
            v-formatTime="{
              value: scope.row.dischargeDateTimeView,
              type: 'date',
            }"
          ></span>
          <span
            v-formatTime="{
              value: scope.row.dischargeDateTimeView,
              type: 'time',
            }"
          ></span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { GetLastStationIDByInpatientID, GetInpatientDataViewByCaseNumber } from "@/api/Inpatient";
import { GetBySettingTypeCodeByArray, GetClinicSettingByTypeCode } from "@/api/Setting";
import { mapGetters } from "vuex";
import queryFieldInput from "@/components/queryFieldInput";
export default {
  components: {
    queryFieldInput,
  },
  props: {
    checkFlag: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      patientData: [],
      stationList: [],
      optionsOfDepartmentList: [],
      caseNumber: "",
      bedNumber: "",
      dueDay: undefined,
      queryField: [],
      defaultValue: undefined,
      patientInfo: undefined,
    };
  },
  computed: {
    ...mapGetters({
      currentPatient: "getCurrentPatient",
    }),
  },
  watch: {
    bedNumber(newValue) {
      if (newValue) {
        this.caseNumber = "";
      }
    },
    caseNumber(newValue) {
      if (newValue) {
        this.bedNumber = "";
      }
    },
  },
  async created() {
    await this.getPatientQueryField();
    if (this.$route.query.caseNumber) {
      this.caseNumber = this.$route.query.caseNumber;
      await this.getInpatientDataViewByCaseNumber();
    }
    let attribute = this.queryField[0].typeValue;
    let attributeValue = this.$route.query.caseNumber
      ? this.patientInfo[attribute.charAt(0).toLowerCase() + attribute.slice(1)]
      : this.currentPatient[attribute.charAt(0).toLowerCase() + attribute.slice(1)];
    this.defaultValue = {
      attribute: this.queryField[0].typeValue,
      attributeValue: attributeValue,
    };
    this.getDudy();
  },
  methods: {
    showColumn() {
      return "cursor:pointer;height:30px;";
    },
    /**
     * 获取病人信息
     * @param patient 患者基本数据
     */
    async selectPatientData(patient) {
      if (this.checkFlag) {
        //因病案归档添加 2021-05-09 En
        if (patient.emrArchivingFlag == "*") {
          this._showTip("warning", "该病人病历已经归档");
          return;
        }
        if (patient.stationID == 0) {
          patient.stationID = await this.getLastStationIDByInpatientID(patient.inpatientID);
        }
        //检核是否符合补录条件
        if (
          patient.refillFlag &&
          this.dueDay &&
          this._datetimeUtil.getTimeDifference(patient.dischargeDateTimeView, undefined, "date", "D", 3) >
            Number(this.dueDay)
        ) {
          this._showTip("warning", "出院" + this.dueDay + "天后数据无法进行补录！");
          return;
        }
      }
      this.$emit("selectPatientData", patient);
    },
    /**
     * 根据住院号获取病人信息
     * @param inpatientID 住院ID
     */
    async getLastStationIDByInpatientID(inpatientID) {
      let param = {
        inpatientID: inpatientID,
      };
      let stationID = 0;
      await GetLastStationIDByInpatientID(param).then((res) => {
        if (this._common.isSuccess(res)) {
          stationID = res.data;
        }
      });
      return stationID;
    },
    /**
     * 获取限制时间
     */
    getDudy() {
      GetBySettingTypeCodeByArray({
        SettingTypeCode: "ModifyDeadline_HN",
      }).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.length > 0) {
            this.dueDay = result.data[0].typeValue;
          } else {
            this.dueDay = undefined;
          }
        }
      });
    },
    /**
     * 获取查询配置
     */
    async getPatientQueryField() {
      let params = {
        settingTypeCode: "PatientQueryField",
      };
      await GetClinicSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.queryField = res.data;
        }
      });
    },
    /**
     * 根据相关参数获取动态获取患者信息
     * @param data 返回数据
     */
    async handleData(data) {
      if (data.length > 0) {
        this.patientData = data;
        this.$emit("change");
        this.$emit("selectPatientData", data[0]);
      } else {
        this._showTip("warning", "没有获取到该病人近几天数据，请确定输入病案号的准确性");
      }
    },
    async getInpatientDataViewByCaseNumber() {
      let params = {
        caseNumber: this.caseNumber,
      };
      await GetInpatientDataViewByCaseNumber(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.patientInfo = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.search-patient {
  max-height: 200px;
  height: 100%;
  .top {
    padding-left: 10px;
    height: 60px;
    line-height: 60px;
    .search-input {
      width: 160px;
      .el-input-group__append {
        padding: 0 5px;
      }
      i {
        color: #8cc63e;
      }
    }
  }
}
</style>
