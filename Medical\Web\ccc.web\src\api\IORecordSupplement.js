/*
 * FilePath     : d:\ccc\web\ccc.web\src\api\IORecordSupplement.js
 * Author       : 曹恩
 * Date         : 2020-09-28 11:34
 * LastEditors  : 曹恩
 * LastEditTime : 2020-10-15 18:02
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/IORecord";
import qs from "qs";

export const urls = {
  GetIORecord: baseUrl + "/GetIORecord",
  SaveIORecord: baseUrl + "/SaveIORecord",
  Delete: baseUrl + "/Delete"
};

// 获取病人的IO记录
export const GetIORecord = params => {
  return http.get(urls.GetIORecord, params);
};
// 保存病人的IO记录
export const SaveIORecord = params => {
  return http.post(urls.SaveIORecord, params);
};
// 删除病人的IO记录
export const Delete = params => {
  return http.post(urls.Delete, qs.stringify(params));
};
