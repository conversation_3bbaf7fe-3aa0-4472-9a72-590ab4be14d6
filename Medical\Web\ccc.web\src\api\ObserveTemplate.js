/*
 * FilePath     : \projectManagement.webe:\CCC3.1\Medical\Web\ccc.web\src\api\ObserveTemplate.js
 * Author       : 郭鹏超
 * Date         : 2021-02-22 14:59
 * LastEditors  : 马超
 * LastEditTime : 2023-04-11 11:43
 * Description  :观察措施模板API
 */
import http from "../utils/ajax";
const baseUrl = "/ObserveTemplate";
import qs from "qs";

export const urls = {
  GetObserveTemplateType: baseUrl + "/GetObserveTemplateType",
  GetObserveTemplateTableData: baseUrl + "/GetObserveTemplateTableData",
  GetObserveTemplateText: baseUrl + "/GetObserveTemplateText",
  GetObserveTemplateTextByMonitor: baseUrl + "/GetObserveTemplateTextByMonitor",
  GetObserveTemplateData: baseUrl + "/GetObserveTemplateData",
  InsertObserveTemplate: baseUrl + "/InsertObserveTemplate",
  RemoveObserveTemplate: baseUrl + "/RemoveObserveTemplate",
  UptateObserveTemplate: baseUrl + "/UptateObserveTemplate",
  GetObserveTemplateButton: baseUrl + "/GetObserveTemplateButton",
  GetObserveTemplateTypeList: baseUrl + "/GetObserveTemplateTypeList",
  GetObserveTemplateSetting: baseUrl + "/GetObserveTemplateSetting",
  GetStationObserveTemplatesBystationID:
    baseUrl + "/GetStationObserveTemplatesBystationID",
  SaveObserveTemplatesSort: baseUrl + "/SaveObserveTemplatesSort",
  GetAllObservation: baseUrl + "/GetAllObservation"
};
// 获取观察措施模板类型
export const GetObserveTemplateType = params => {
  return http.get(urls.GetObserveTemplateType, params);
};
//获取观察措施模板表格数据
export const GetObserveTemplateTableData = params => {
  return http.get(urls.GetObserveTemplateTableData, params);
};
//获取观察措施模板
export const GetObserveTemplateText = params => {
  return http.get(urls.GetObserveTemplateText, params);
};
//根据监测数据获取观察措施模板
export const GetObserveTemplateTextByMonitor = params => {
  return http.post(urls.GetObserveTemplateTextByMonitor, params);
};

export const GetObserveTemplateData = params => {
  return http.get(urls.GetObserveTemplateData, params);
};

export const InsertObserveTemplate = params => {
  return http.post(urls.InsertObserveTemplate, params);
};

export const RemoveObserveTemplate = params => {
  return http.get(urls.RemoveObserveTemplate, params);
};

export const UptateObserveTemplate = params => {
  return http.post(urls.UptateObserveTemplate, params);
};

export const GetObserveTemplateButton = params => {
  return http.get(urls.GetObserveTemplateButton, params);
};
export const GetObserveTemplateTypeList = params => {
  return http.get(urls.GetObserveTemplateTypeList, params);
};
export const GetObserveTemplateSetting = params => {
  return http.get(urls.GetObserveTemplateSetting, params);
};
//根据病区获取模板
export const GetStationObserveTemplatesBystationID = params => {
  return http.get(urls.GetStationObserveTemplatesBystationID, params);
};
//排序保存
export const SaveObserveTemplatesSort = params => {
  return http.post(urls.SaveObserveTemplatesSort, qs.stringify(params));
};
//获取所有病情观察模板
export const GetAllObservation = params => {
  return http.get(urls.GetAllObservation, params);
};
