﻿/*
************************************************************
作者：YTS
版本：1.0.0
日期：2018-6-8
************************************************************
*/
$Common = {
     //ServiceUrl: "http://localhost:56194/api",
    // ServiceUrl: "http://*************/api",
    // ServiceUrl: "http://************:85/api",
    // ServiceUrl: "http://*************:82/api",
    // ServiceUrl: "http://**************:82/api",
     ServiceUrl: localStorage.getItem('apiUrl'),
    SetToken: function (token) {
        if (!token) token = '';
        $Common.cookie('medical-token', token);
    },
    Token: function () {
        return $Common.cookie('medical-token');
    }
}
$Common.ajax = {
    setParams: function (params) {
        var url = '';
        if (params) {
            url += '?';
            for (var i = 0; i < params.length; i++) {
                if (i > 0) {
                    url += '&';
                }
                url += params[i].key;
                url += '=';
                url += encodeURIComponent(params[i].value);
            }
        }
        return url;
    },
    beforeSend: function (XMLHttpRequest) {
        if ($Common.Token()) {
            XMLHttpRequest.setRequestHeader('medical-token', $Common.Token('medical-token'));
        }
    },
    success: function (opt, result, status, xhr) {
        if (result.code == 1) {
            if (opt.callback) {
                opt.callback({
                    success: true,
                    data: result.data,
                    message: result.message
                });
            }
        } else {
            if (opt.callback) {
                opt.callback({
                    success: false,
                    message: result.message
                });
            }
        }
    },
    error: function (opt, xhr) {
        if (xhr.status == 401) {
            //没有权限
            this.invalid(xhr);
            return;
        }
        if (opt.callback) {
            opt.callback({
                success: false,
                message: XMLHttpRequest.statusText
            });
        }
    },
    get: function (opt) {
        if (opt.params) {
            opt.url += this.setParams(opt.params);
        }
        $.ajax({
            url: opt.url,
            type: "GET",
            cache: false,
            beforeSend: this.beforeSend,
            crossDomain: true == !(document.all),
            success: function (result, status, xhr) {
                $Common.ajax.success(opt, result, status, xhr);
            },
            error: function (XMLHttpRequest) {
                $Common.ajax.error(opt, XMLHttpRequest);
            }
        });
    },
    post: function (opt) {
        if (opt.json) {
            opt.json = JSON.stringify(opt.json);
        } else if (opt.params) {
            opt.url += this.setParams(opt.params);
        }
        $.ajax({
            url: opt.url,
            type: "POST",
            contentType: "application/json;",
            data: opt.json,
            dataType: 'json',
            beforeSend: this.beforeSend,
            crossDomain: true == !(document.all),
            success: function (result, status, xhr) {
                $Common.ajax.success(opt, result, status, xhr);
            },
            error: function (XMLHttpRequest) {
                $Common.ajax.error(opt, XMLHttpRequest);
            }
        });
    }
};
$Common.cookie = function (name, value, escape) {
    if (value != undefined) {
        if (escape) {
            document.cookie = (name + "=" + escape(value) + ";path=/");
        } else {
            document.cookie = (name + "=" + value + ";path=/");
        }
    } else {
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
        if (arr = document.cookie.match(reg)) {
            if (escape) {
                return unescape(arr[2]);
            } else {
                return arr[2];
            }
        }
    }
}
$Common.storage = function (name, value) {
    if (!localStorage) return;
    if (value) {
        localStorage.setItem(name, JSON.stringify(value))
    } else {
        return JSON.parse(localStorage.getItem(name))
    }
}
//获取请求参数
$Common.getQueryString = function (name) {
    if (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        }
        return null;
    } else {
        var params = window.location.search.substr(1).split('&');
        var query = {};
        for (var i = 0; i < params.length; i++) {
            var param = params[i].split('=');
            query[param[0]] = param[1];
        }
        return query;
    }
};
$Common.getScriptSrc = function () {
    var e = document.scripts;
    var url = '';
    if (e.length > 0) {
        url += e[e.length - 1].src.substring(0, e[e.length - 1].src.lastIndexOf("/") + 1);
    }
    return url;
};
$Common.combind = function (url) {
    var startIndex = url.indexOf('..');
    if (startIndex > 0) {
        var endIndex = url.lastIndexOf('..') + 3;
        var count = (endIndex - startIndex) / 3;
        var lastUrl = url.substring(endIndex - 1, url.length);
        url = url.substring(0, startIndex - 1);
        for (var i = 0; i < count; i++) {
            url = url.substring(0, url.lastIndexOf('/'));
        }
        return url + lastUrl;
    }
    return url;
};
$Common.containCSS = function (url) {
    for (var i = 0; i < document.links.length; i++) {
        if (document.links[i].href == url) {
            return true;
        }
    }
    return false;
};
//加载CSS文件
$Common.usingCSS = function (url, scriptUrl) {
    if (scriptUrl == undefined) {
        scriptUrl = this.getScriptSrc();
    }
    url = this.combind(scriptUrl + url);
    if (this.containCSS(url)) {
        //防止重复CSS
        return;
    }
    var _head = document.getElementsByTagName('HEAD').item(0);
    var _css = document.createElement("link");
    _css.type = "text/css";
    _css.rel = "stylesheet";
    _css.href = url;
    try {
        _head.appendChild(_css);
    } catch (ex) {
        if (console) console.log(ex);
    }
};