<!--
 * FilePath     : \ccc.web\src\pages\dictionaryMaintain\tube\tubeToBodyPart.vue
 * Author       : 李艳奇
 * Date         : 2020-10-15 15:29
 * LastEditors  : 李艳奇
 * LastEditTime : 2020-11-07 16:11
 * Description  : 
-->
<template>
  <div class="tube-and-body-main">
    <div class="tube-main">
      <el-table
        :data="tubeList"
        highlight-current-row
        @row-click="getBodyPartList"
        border
        stripe
        height="95%"
        v-loading="loading"
        :element-loading-text="loadingText"
      >
        <el-table-column prop="tubeName" label="导管" min-width="60" align="left"></el-table-column>
      </el-table>
    </div>

    <div class="body-main">
      <div class="gender-select">
        <el-button
          v-if="saveShow"
          class="add-button"
          icon="iconfont icon-save-button"
          type="primary"
          @click="addBodyPart()"
        >
          保存
        </el-button>
        <el-radio-group v-model="gender">
          <el-radio-button :label="1">男</el-radio-button>
          <el-radio-button :label="2">女</el-radio-button>
        </el-radio-group>
      </div>
      <iframe :src="url" frameborder="0" class="mobile-body"></iframe>
    </div>
    <div class="body-list">
      <el-table :data="bodyParts" border stripe height="95%">
        <el-table-column prop="bodyPartName" label="部位"></el-table-column>
        <el-table-column label="操作" width="48" align="left">
          <template slot-scope="scope">
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="deleteTubeToBodyPart(scope.row.id)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { GetTubeList, GetBodyPartList, DeleteTubeToBodyPart, SaveTubeToBodyParts } from "@/api/TubeToBodyPart";

export default {
  data() {
    return {
      tubeList: [], //所有的导管
      bodyParts: [], //指定导管对应的身体部位对象集合
      checkedBodyPartCode: [],
      tubeID: "", //当前选中的导管对象ID
      tube: "", //当前选中的导管对象
      url: "", //调用人体图的url
      gender: "", //性别字段
      loading: false, //加载数据
      loadingText: "", //加载数据的时候页面提示内容
      saveShow: false, //控制保存按钮
    };
  },
  watch: {
    gender(newVal, oldVal) {
      this.showSelectPart();
    },
  },
  mounted() {
    this.getAllTubeList();
  },
  methods: {
    //获取所有的导管
    getAllTubeList() {
      this.loading = true;
      this.loadingText = "加载中……";
      GetTubeList().then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.tubeList = result.data;
        }
      });
    },
    //删除指定的病区对导管
    deleteTubeToBodyPart(bodyID) {
      this._deleteConfirm("确认要删除此记录吗？", (flag) => {
        if (flag) {
          let params = {
            TubeListID: this.tubeID,
            BodyPartID: bodyID,
          };
          DeleteTubeToBodyPart(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              this.getBodyPartList(this.tube);
            }
          });
        }
      });
    },
    //获取指定导管对应的身体部位
    getBodyPartList(tube) {
      this.loading = true;
      this.loadingText = "加载中……";
      this.tube = tube;
      this.saveShow = true;
      //设置导管的id
      this.tubeID = tube.id;
      GetBodyPartList(tube.id).then((result) => {
        this.loading = false;
        this.checkedBodyPartCode = [];
        if (this._common.isSuccess(result)) {
          this.bodyParts = result.data;
          for (let i = 0; i < this.bodyParts.length; i++) {
            this.checkedBodyPartCode.push(this.bodyParts[i].id);
          }
          this.showSelectPart();
        }
      });
    },
    showSelectPart() {
      this.url = "../../../../static/body/mobileBody.html?type=Common&gender=" + this.gender + "&t=" + Math.random();
      //获取部位
      localStorage.setItem(
        "selectPart",
        JSON.stringify({
          bodyPartCode: this.checkedBodyPartCode,
        })
      );
    },

    //添加导管对人体部位
    addBodyPart() {
      let checkedPart = JSON.parse(localStorage.getItem("bodyPart"));
      let BodyPartID = [];
      for (let i = 0; i < checkedPart.length; i++) {
        BodyPartID.push(parseInt(checkedPart[i].bodyPartCode));
      }
      this.loadingText = "保存中……";

      //封装参数
      let params = {
        TubeListID: this.tubeID,
        BodyPartID: BodyPartID,
      };
      SaveTubeToBodyParts(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.getBodyPartList(this.tube);
          this._showTip("success", result.message);
        } else {
          this.getBodyPartList(this.tube);
        }
      });
    },
  },
};
</script>
<style lang="scss">
.tube-and-body-main {
  width: 100%;
  height: 100%;
  .tube-main {
    float: left;
    width: 23%;
    height: 100%;
    margin-left: 7px;
  }
  .body-main {
    float: left;
    width: 59%;
    height: 100%;
    .gender-select {
      margin-left: 10px;
    }
    .add-button {
      float: right;
      margin-right: 10px;
    }
  }
  .body-list {
    float: left;
    width: 17%;
    height: 100%;
  }
  .mobile-body {
    margin-top: 10px;
    height: 660px;
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    margin-left: 30px;
  }
}
</style>

