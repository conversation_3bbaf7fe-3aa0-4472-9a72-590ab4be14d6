<!--
 * FilePath     : \ccc.web\src\pages\transferPages\qcContentMaintain.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 郭鹏超
 * LastEditTime : 2021-01-18 16:44
 * Description  : 串三级指控维护
--> 
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
// 代码需要迁移，暂时串到nursing
import { getOldNursingUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
    }),
  },
  created() {
    this.url = getOldNursingUrl() + "export/qcContentMaintain?token=" + this.token;
  },
};
</script>
