/*
 * FilePath     : \src\api\Delivery.js
 * Author       : 郭鹏超
 * Date         : 2021-04-25 09:34
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:44
 * Description  :产时记录API
 */
import http from "../utils/ajax";
const baseUrl = "/Delivery";

const urls = {
  GetRecordTableView: baseUrl + "/GetRecordTableView",
  GetCareMainTableView: baseUrl + "/GetCareMainTableView",
  DeleteRecord: baseUrl + "/DeleteRecord",
  SaveCareMain: baseUrl + "/SaveCareMain",
  DeleteCareRecord: baseUrl + "/DeleteCareRecord",
  GetDeliveryAssessViewAsync: baseUrl + "/GetDeliveryAssessViewAsync",
  SaveRecord: baseUrl + "/SaveRecord",
  SaveCareMain: baseUrl + "/SaveCareMain"
};
//获取主记录表格数据
export const GetRecordTableView = params => {
  return http.get(urls.GetRecordTableView, params);
};
//获取维护主记录表格数据
export const GetCareMainTableView = params => {
  return http.get(urls.GetCareMainTableView, params);
};
//删除主记录
export const DeleteRecord = params => {
  return http.get(urls.DeleteRecord, params);
};
//删除维护记录
export const DeleteCareRecord = params => {
  return http.get(urls.DeleteCareRecord, params);
};
//获取产时记录评估模板
export const GetDeliveryAssessViewAsync = params => {
  return http.get(urls.GetDeliveryAssessViewAsync, params);
};
//保存产时记录
export const SaveRecord = params => {
  return http.post(urls.SaveRecord, params);
};
//保存维护记录
export const SaveCareMain = params => {
  return http.post(urls.SaveCareMain, params);
};
