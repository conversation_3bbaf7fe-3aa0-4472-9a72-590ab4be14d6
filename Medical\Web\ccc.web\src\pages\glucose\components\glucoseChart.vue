<!--
 * FilePath     : \src\pages\glucose\components\glucoseChart.vue
 * Author       : 李正元
 * Date         : 2020-11-03 14:47
 * LastEditors  : 来江禹
 * LastEditTime : 2024-05-12 15:32
 * Description  :
-->
<template>
  <base-layout class="glucose-chart">
    <div slot="header">
      查询范围：
      <el-date-picker
        v-model="startDate"
        type="date"
        placeholder="开始日期"
        :picker-options="pickerOptions"
        @change="getLineChart"
      />
      -
      <el-date-picker v-model="endDate" type="date" placeholder="结束日期" @change="getLineChart" />
      <div class="header-right">
        <el-button class="print-button" icon="iconfont icon-back" @click="$router.go(-1)" v-if="this.routeFlag">
          返回
        </el-button>
      </div>
    </div>
    <div class="chart">
      <ve-line
        :data="chartData"
        :extend="chartExtend"
        :settings="chartSettings"
        :grid="grid"
        :tooltip="tooltip"
        :after-set-option="afterSetOption"
      ></ve-line>
    </div>
  </base-layout>
</template>
<script>
import { GetLineChart } from "@/api/Glucose";
import baseLayout from "@/components/BaseLayout";
import { setGlucoseChartOption, grid, tooltip, chartSettings, chartExtend } from "./chartOption";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  components: {
    baseLayout,
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    readonly: {
      type: Boolean,
      return: false,
    },
  },
  data() {
    return {
      startDate: undefined,
      endDate: undefined,
      //显示统计数据
      chartData: {
        columns: [],
        rows: [],
      },
      routeFlag: false,
      inpatientID: undefined,
      grid: grid,
      tooltip: tooltip,
      chartSettings: chartSettings,
      chartExtend: chartExtend,
      chartShowInsulinFlag: false,
    };
  },
  watch: {
    "patientInfo.inpatientID": {
      handler(newValue) {
        if (newValue) {
          this.inpatientID = newValue;
          this.getLineChart();
        } else {
          this.chartData.columns = [];
          this.chartData.rows = [];
        }
      },
      immediate: true,
    },
  },
  computed: {
    pickerOptions() {
      let that = this;
      return {
        disabledDate(time) {
          return that._datetimeUtil.getTimeDifference(time, that.endDate, "date", "D") < 0;
        },
      };
    },
  },
  async created() {
    await this.getChartShowInsulinSwitch();
    this.endDate = this._datetimeUtil.getNowDate();
    this.startDate = this._datetimeUtil.addDate(this.endDate, -7, "yyyy-MM-dd");
    //组件显示
    if (this.patientInfo) {
      this.inpatientID = this.patientInfo.inpatientID;
      this.routeFlag = false;
    }
    //跳转显示
    if (this.$route?.query?.patientInfo || this.readonly) {
      this.inpatientID = this.$route.query.patientInfo?.inpatientID ?? this.patientInfo.inpatientID;
      this.routeFlag = true;
    }
    this.getLineChart();
    // 设置不可切换病人
    this._sendBroadcast("setPatientSwitch", !this.routeFlag);
  },

  methods: {
    getLineChart() {
      //查无病人数据
      if (!this.inpatientID) {
        return;
      }
      let params = {
        inpatientID: this.inpatientID,
        startDate: this.startDate,
        endDate: this.endDate,
      };
      GetLineChart(params).then((result) => {
        this.chartData.rows = [];
        this.chartData.columns = [];
        if (this._common.isSuccess(result) && result.data) {
          this.chartData = result.data;
        }
      });
    },
    /**
     * description: 统计图撇配置参数
     * param {*} chart
     * return {*}
     */
    afterSetOption(chart) {
      setGlucoseChartOption(chart, this);
    },
    /**
     * description: 获取折线图是否显示胰岛素Y轴
     * return {*}
     */
    async getChartShowInsulinSwitch() {
      let param = {
        SettingTypeCode: "GlucoseChartShowInsulin",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.chartShowInsulinFlag = response.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.glucose-chart {
  height: 100%;
  .base-header {
    .header-right {
      float: right;
    }
  }
  .chart {
    height: 100%;
    padding: 30px 0 10px 0;
    box-sizing: border-box;
    background-color: #ffffff;
    overflow: hidden;
  }
}
</style>
