<!--
 * FilePath     : \ccc.web\src\pages\patientOrder\components\ordersFrequency.vue
 * Author       : xml
 * Date         : 2019-10-13 10:00
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-06-02 17:14
 * Description  : 医嘱审核频次调整业务控件
 -->
<template>
  <el-form class="orders-frequency">
    <div>
      频次选择：
      <el-cascader
        popper-class="frequency-select"
        :props="{ expandTrigger: 'hover', value: 'id' }"
        :options="frequencyList"
        v-model="selectedFrequency"
        @change="changeFrequencyList"
        :show-all-levels="false"
        filterable
        size="mini"
      ></el-cascader>
    </div>
    <div class="week">周频次:</div>
    <div class="times-check">
      <ul v-for="(item, index) in this.weektimes" :key="index">
        <li v-for="weekTime in item" :key="weekTime.value">
          <el-checkbox
            :label="weekTime.value"
            v-model="weekTime.selected"
            :disabled="isdisabled"
            @change="checkedTimeChange"
          >
            {{ weekTime.display }}
          </el-checkbox>
        </li>
      </ul>
    </div>
    <div class="day">日频次</div>
    <div class="time-select">
      首日开始时间：
      <el-time-select
        v-model="startTimeOfFirstDay"
        format="HH:mm"
        :picker-options="{
          start: '00:00',
          step: '01:00',
          end: '23:00',
        }"
      ></el-time-select>
    </div>
    <div class="times-check">
      <ul v-for="(item, index) in times" :key="index">
        <li v-for="time in item" :key="time.value">
          <el-checkbox :label="time.value" v-model="time.selected" :disabled="isdisabled" @change="checkedTimeChange">
            {{ time.display }}
          </el-checkbox>
        </li>
      </ul>
    </div>
    <div class="schedule-select">
      <el-input type="textarea" v-model="selectedSchedule" :rows="2" readonly></el-input>
    </div>
    <div class="button-group">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-form>
</template>

<script>
import { GetTypeFrequency } from "@/api/Frequency";

export default {
  props: {
    frequency: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      currentdata: "1",
      times: [],
      weektimes: [],
      isdisabled: false,
      frequencys: [],
      selectedFrequency: "",
      selectedSchedule: "",
      startTimeOfFirstDay: "",
      newFrequencyList: {
        id: 0,
        value: "0",
        label: "自定义选项",
        children: [
          {
            id: 0,
            value: "0",
            label: "自定义频次",
          },
        ],
      },
      frequencyList: [],
    };
  },
  watch: {
    "frequency.schedule": {
      handler(newName, oldName) {
        if (newName) {
          let list = this.frequencys.filter((item) => {
            return item.schedule == newName;
          });
          this.isdisabled = true;
          if (list.length > 0) {
            this.selectedFrequency = list[0].value;
          } else {
            this.selectedFrequency = "0";
            this.isdisabled = false;
          }
        } else {
          this.isdisabled = false;
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.times = [];
    this.weektimes = [];
    this.initTimesLayout(); //初始化时间选择布局
    this.intiWeekTimes(); //初始化周频次
    this.GetFrequencyList();
    this.startTimeOfFirstDay = "";
    //GPC修改
    let list = this.frequencys.filter((item) => {
      return item.schedule == this.frequency.schedule;
    });
    if (list.length > 0 && list[0].value > 7) {
      this.selectedSchedule = this.frequency.schedule;
      return;
    }
    this.showView();
  },
  methods: {
    showView() {
      this.selectedFrequency = "0";
      for (let i = 0; i < this.times.length; i++) {
        for (let j = 0; j < this.times[i].length; j++) {
          this.times[i][j].selected = false;
        }
      }

      if (this.frequency) {
        this.selectedSchedule = this.frequency.schedule;
        this.startTimeOfFirstDay = this.frequency.startTimeOfFirstDay;
        this.initValue(this.frequency.schedule);
      }
    },
    initTimesLayout() {
      for (let i = 0; i < 24; i++) {
        let time = [];
        for (let j = 0; j < 8; j++) {
          time.push({
            value: i,
            display: (i < 10 ? "0" + i : i) + ":00",
            selected: false,
          });
          i++;
        }
        i--;
        this.times.push(time);
      }
    },
    intiWeekTimes() {
      let time = [];
      time.push({
        value: 0,
        display: "周一",
        selected: false,
      });
      time.push({
        value: 1,
        display: "周二",
        selected: false,
      });
      time.push({
        value: 2,
        display: "周三",
        selected: false,
      });
      time.push({
        value: 3,
        display: "周四",
        selected: false,
      });
      time.push({
        value: 4,
        display: "周五",
        selected: false,
      });
      time.push({
        value: 5,
        display: "周六",
        selected: false,
      });
      time.push({
        value: 6,
        display: "周日",
        selected: false,
      });
      this.weektimes.push(time);
    },
    initValue(schedule) {
      if (!schedule.includes("/")) {
        schedule = "/" + schedule;
      }
      let _weekTime = [];
      let _weeks = [];
      let _times = [];
      if (schedule) {
        _weekTime = schedule.split("/");
      }
      _weeks = _weekTime[0].split("-");
      _times = _weekTime[1].split("-");
      //显示选择的时间
      for (let i = 0; i < this.times.length; i++) {
        for (let j = 0; j < this.times[i].length; j++) {
          this.times[i][j].selected = _times.includes(this.times[i][j].value.toString());
        }
      }
      //显示选择的周
      for (let i = 0; i < this.weektimes[0].length; i++) {
        this.weektimes[0][i].selected = _weeks.includes(this.weektimes[0][i].display.toString());
      }
      //组合时间频次显示方式
      let _showTime = "";
      let _showWeek = "";

      _showWeek = _weekTime[0];
      _showTime = _weekTime[1];
      //只有一个时间
      if (_times.length == 1 && _showTime != "" && !schedule.includes(":")) {
        _showTime = _showTime + ":00";
      }
      schedule = _showWeek + "/" + _showTime;
      //没有选择周频次
      if (_showWeek == "") {
        schedule = _showTime;
      }
      //没有选择时间频次
      if (_showTime == "") {
        schedule = "";
      }
      this.selectedSchedule = schedule; //文本框显示文字
    },
    checkedTimeChange() {
      let scheduleTime = "";
      let scheduleWeek = "";
      let runSchedule = "";
      for (let i = 0; i < this.times.length; i++) {
        let selectTimes = this.times[i].filter((t) => t.selected == true);
        if (selectTimes.length > 0) {
          selectTimes.forEach(function (time, index) {
            if (scheduleTime != "") {
              scheduleTime += "-";
            }
            scheduleTime += time.value;
          });
        }
      }
      for (let i = 0; i < this.weektimes.length; i++) {
        let selectWeeks = this.weektimes[i].filter((t) => t.selected == true);
        if (selectWeeks.length > 0) {
          selectWeeks.forEach(function (week, index) {
            if (scheduleWeek != "") {
              scheduleWeek += "-";
            }
            scheduleWeek += week.display;
          });
        }
      }
      runSchedule = scheduleWeek + "/" + scheduleTime;
      this.initValue(runSchedule);
    },
    changeFrequencyList(selectedFrequency) {
      if (selectedFrequency[0] != "0") {
        this.isdisabled = true;
      } else {
        this.isdisabled = false;
      }
      //GPC修改
      if (selectedFrequency[0] == "0") {
        this.initValue("");
      } else {
        //下面是不选自定义的逻辑
        let frequency = this.frequencyList.find((item) => item.id == selectedFrequency[0]);
        if (frequency.children && frequency.children.length) {
          let childrenFrequency = frequency.children.find((item) => item.id == selectedFrequency[1]);
          if (childrenFrequency && frequency.children.length) {
            this.initValue(childrenFrequency.hoursDetail);
          }
          if (!childrenFrequency.hoursDetail) {
            this.selectedSchedule = childrenFrequency.search;
          }
        }
      }
    },
    confirm() {
      if (this.selectedSchedule == "" || this.selectedSchedule == null) {
        this._showTip("warning", "没有选择频次！");
        return;
      }
      this.$emit(
        "frequencySelect",
        this.selectedSchedule,
        this.startTimeOfFirstDay,
        this.frequency.row,
        this.selectedFrequency
      );
      this.$emit("close");
    },
    close() {
      this.$emit("close");
    },
    //获取频次列表
    GetFrequencyList() {
      this.frequencyList = [];
      GetTypeFrequency().then((result) => {
        if (this._common.isSuccess(result)) {
          this.frequencyList = this.deelFrequency(result.data);
          this.frequencyList.unshift(this.newFrequencyList);
        }
      });
    },
    //组装频次二级菜单数组
    deelFrequency(frequencyList) {
      if (!frequencyList) {
        return [];
      }
      //首次打开修改框，如果不选择自定义，下面默认不能选择
      this.isdisabled = true;
      return frequencyList;
    },
  },
};
</script>

<style lang="scss">
.orders-frequency {
  padding: 5px;
  .frequency-select {
    .el-input__inner {
      height: 30px;
      line-height: 30px;
      color: $base-color;
      cursor: pointer;
      width: 150px;
    }
  }
  .week {
    margin-top: 10px;
  }
  .day {
    margin-bottom: 10px;
  }
  .times-check {
    ul {
      height: 30px;
      line-height: 30px;
      padding: 0;
      li {
        display: table-cell;
        width: 80px;
        .el-checkbox.is-disabled {
          .el-checkbox__inner::after {
            border-color: $base-color;
          }
          .el-checkbox__label {
            color: #606266;
          }
        }
      }
    }
  }
  .time-select {
    .el-input__inner {
      width: 150px;
      height: 30px;
      color: $base-color;
      cursor: pointer;
      line-height: 30px;
    }
  }
  .schedule-select {
    .el-textarea__inner {
      font-size: 15px;
      font-weight: bold;
    }
  }
  .button-group {
    position: absolute;
    bottom: 0;
    height: 40px;
    width: calc(100% - 30px);
    text-align: right;
  }
}
</style>
