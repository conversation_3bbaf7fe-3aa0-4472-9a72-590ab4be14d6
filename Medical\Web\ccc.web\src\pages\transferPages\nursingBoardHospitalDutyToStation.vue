<!--
 * FilePath     : \src\pages\transferPages\nursingBoardHospitalDutyToStation.vue
 * Author       : 来江禹
 * Date         : 2022-12-13 17:27
 * LastEditors  : 来江禹
 * LastEditTime : 2022-12-27 09:22
 * Description  : 串到护理看板病区维护院内总值班
 * CodeIterationRecord: 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getNursingBoard } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      language: "getLanguage",
      hospitalInfo: "getHospitalInfo",
      token: "getToken",
      user: "getUser",
    }),
  },
  created() {
    this.url =
      getNursingBoard() +
      "hospitalDutyToStation?hospitalID=" +
      this.hospitalInfo.hospitalID +
      "&language=" +
      this.language +
      "&token=" +
      this.token +
      "&stationID=" +
      this.user.stationID +
      "&userID=" +
      this.user.userID;
  },
};
</script>