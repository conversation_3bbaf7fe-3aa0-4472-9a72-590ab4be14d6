<!--
 * FilePath     : \src\pages\patientEvent\index.vue
 * Author       : 胡洋
 * Date         : 2020-09-22 10:46
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-18 16:58
 * Description  : 
 * CodeIterationRecord:
    2022-11-23 3133-作为护理人员，我需要患者事件可以呈现患者所有病区的事件，以利于方便查看患者事件（10） 事件补录也使用这个页面 -zxz
-->
<template>
  <base-layout class="event-main">
    <div slot="header" class="event-header">
      <el-button icon="iconfont icon-add" class="add-button" @click="addOrModifyEvent('')">新增</el-button>
    </div>
    <el-table :data="patientEventList" border stripe height="100%" v-loading="loading" element-loading-text="加载中……">
      <el-table-column prop="eventName" label="事件" min-width="85" align="center"></el-table-column>
      <el-table-column prop="stationName" label="病区" min-width="130" align="center"></el-table-column>
      <el-table-column label="发生日期" min-width="130" align="center">
        <template slot-scope="event">
          <span v-formatTime="{ value: event.row.occurDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="发生时间" min-width="90" align="center">
        <template slot-scope="event">
          <span v-formatTime="{ value: event.row.occurTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" align="center">
        <template slot-scope="event">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="addOrModifyEvent(event.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteEvent(event.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="维护病人事件"
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="showDialog"
      custom-class="event-maintain"
      v-loading="dialogLoading"
      :element-loading-text="dialogLoadingText"
    >
      <!-- 使用supplementFlag && showDialog 条件判断是否需要渲染，showDialog保证了其同dialog一同渲染,保证参数改变之后，显示正确内容 -->
      <station-department-bed
        v-if="supplementFlag && showDialog"
        :stDeptBed="stationDepartmentBed"
        @selectStationDepartmentBed="selectStationDepartmentBed"
      ></station-department-bed>
      <div class="content" v-loading="assessModelloading">
        <span class="content-span">发生日期：</span>
        <el-date-picker
          :clearable="false"
          v-model="occurDate"
          type="date"
          value-format="yyyy-MM-dd"
          style="width: 130px"
        ></el-date-picker>
        <span class="content-span">发生时间：</span>
        <el-time-picker
          :clearable="false"
          v-model="occurTime"
          format="HH:mm"
          value-format="HH:mm"
          style="width: 90px"
        ></el-time-picker>
        <span>&nbsp;</span>
        <user-selector
          v-model="performNurse"
          :stationID="stationDepartmentBed.stationID"
          v-if="supplementFlag"
          label="护士："
          clearable
          filterable
          remoteSearch
          :disabled="selectUserDisableFlag"
          width="145px"
        ></user-selector>
        <div class="events-list">
          <div class="events" v-for="(eventView, index1) in eventList" :key="index1">
            <el-radio
              v-model="eventID"
              v-for="(item, index2) in eventView.event"
              :key="index2"
              :label="item.assessListID"
              @change="getEventAssessView(item)"
            >
              {{ item.eventName }}
            </el-radio>
          </div>
        </div>

        <div class="assess-content" v-if="eventDetailData">
          <tabs-layout
            ref="tabsLayout"
            :template-list="eventDetailData"
            checkFlag
            @change-values="changeValues"
            @button-record-click="buttonRecordClick"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取消</el-button>
        <el-button v-if="checkResult" type="primary" @click="saveEvent">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonRecordTitle"
      :visible.sync="showButtonRecordDialog"
      custom-class="no-footer"
    >
      <risk-component :params="conponentParams" @result="result"></risk-component>
    </el-dialog>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import tabsLayout from "@/components/tabsLayout/index";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import stationDepartmentBed from "@/pages/recordSupplement/components/stationDepartmentBed.vue";
import userSelector from "@/components/selector/userSelector";
import { GetEventList, GetPatientEventList, SaveEvent, DeleteEvent, GetEventAssessView } from "@/api/PatientEvent";
import { GetButtonData } from "@/api/Assess";
import { GetOneSettingByTypeAndCode } from "@/api/Setting";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    tabsLayout,
    riskComponent,
    stationDepartmentBed,
    userSelector,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
    }),
  },
  watch: {
    /**
     * description: 监听患者事件页面跳转中传递的病人头信息
     * 20221229 由原本监听patientInfo-->"patientInfo.inpatientID",原因：使用patientIfo的话，按F5刷新页面会出现问题；
     * 在mounted的时候，赋值触发对this.patient的监听后获取事件列表，在页面挂载之后,病人头刷新会重新获取病人信息调用setPatientInfo,
     * 此时触发patientInfo的回调，对this.patient赋值->最后重复获取事件列表被拦截,结果就是页面不显示患者事件。
     * return {*}
     */
    "patientInfo.inpatientID": {
      handler(newValue) {
        if (!newValue) return;
        this.patient = this.patientInfo;
      },
    },
    supplemnentPatient: {
      handler(newValue) {
        if (!newValue) return;
        this.supplementFlag = true;
        this.patient = this.supplemnentPatient;
      },
    },
    patient: {
      handler(newValue, oldValue) {
        this.patientEventList = [];
        if (!newValue) return;
        this.patient = newValue;
        this.init();
      },
    },
  },
  data() {
    return {
      //事件列表
      eventList: [],
      //患者已有事件列表
      patientEventList: [],
      //事件评估模板
      eventDetailData: [],
      loading: false,
      //对话框
      showDialog: false,
      //弹框加载
      dialogLoading: false,
      //弹框加载文本
      dialogLoadingText: "",
      //评估AssessListID
      eventID: "",
      //发生日期
      occurDate: "",
      //发生时间
      occurTime: "",
      //当前事件
      currentEvent: undefined,
      //评估数据
      assessDatas: [],
      //保存跌倒事件使用
      currentRecordsCode: undefined,
      //跌倒事件弹框加载
      assessModelloading: false,
      // 显示BR类弹窗
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR类弹窗所需参数
      conponentParams: {},
      // BR项ID
      brAssessListID: undefined,
      patientEventGuid: undefined,
      checkResult: true,
      //暂存病区科室及床位
      stationDepartmentBed: {
        stationID: "",
        departmentListID: "",
        bedNumber: "",
        bedId: "",
      },
      //该页面使用的病人信息变量(根据是补录调用还是正常调用来赋值)
      patient: undefined,
      //表示是否是进行患者事件补录
      supplementFlag: false,
      //执行护士
      performNurse: undefined,
      //标记：表示执行人是否可以修改 true：表示下拉框不可选，只读
      selectUserDisableFlag: false,
      // 跳转过来的来源信息
      sourceID: "",
    };
  },
  mounted() {
    this.getSetting();
    if (!this.patient) {
      this.patient = this.supplemnentPatient || this.patientInfo;
      this.supplementFlag = !!this.supplemnentPatient;
    }
  },
  methods: {
    changeValues(datas) {
      this.assessDatas = datas;
    },
    /**
     * @description:初始化页面数据
     * @return {*}
     */
    async init() {
      // 页面弹窗跳转
      this.sourceID = this.$route.query.sourceID;
      let showAddOrModifyDialog = this.$route.query.showAddOrModifyDialog;
      if (this.$route.query.sourceID) {
        this._sendBroadcast("setPatientSwitch", false);
      }
      if (this.patient) {
        await this.getPatientEventList();
        // 页面跳转后打开新增和编辑弹窗
        let sourceEvent = this.patientEventList.find((item) => item.sourceID && item.sourceID == this.sourceID);
        showAddOrModifyDialog && (await this.addOrModifyEvent(sourceEvent));
      }
    },
    /**
     * @description: 获取配置判断 补录页面患者事件修改时，是否可以修改执行人
     * @return {*}
     */
    getSetting() {
      let param = {
        settingType: 169,
        settingCode: "SwitchForMaintainer",
      };
      GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.typeValue == "False") {
            this.selectUserDisableFlag = false;
          } else {
            this.selectUserDisableFlag = true;
          }
        }
      });
    },
    /**
     * @description: 新增和修改患者事件弹框前的数据准备
     * @param {*} event
     * @return {*}
     */
    async addOrModifyEvent(event) {
      if (this.loading) return;
      if (this.eventList.length <= 0) {
        this.getEventList();
      }
      if (event) {
        if (
          !this.supplementFlag &&
          event.onlyUpdateCurrentStationEventFlag &&
          this.user &&
          this.user.stationID != event.stationID
        ) {
          this._showTip("warning", "非本病区添加，不能修改");
          return;
        }
        // 修改
        this.eventID = event.assessListID;
        this.occurDate = event.occurDate;
        this.occurTime = event.occurTime;
        this.currentEvent = event;
        this.patientEventGuid = event.patientEventID;
        this.performNurse = this.supplementFlag ? event.modifyPersonID : this.user.userID;
        //在修改患者事件时默认选择上次保存时保存的病区,科室,床位
        this.selectStationDepartmentBed(event);
        if (this.supplementFlag) {
          ({ disabledFlag: this.selectUserDisableFlag, saveButtonFlag: this.checkResult } =
            await this._common.userSelectorDisabled(this.user.userID, false, this.supplementFlag, event.addEmployeeID));
        }
      } else {
        //新增
        this.currentEvent = undefined;
        this.eventID = "";
        this.performNurse = this.user.userID;
        this.occurDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.occurTime = this._datetimeUtil.getNowDate("hh:mm");
        this.eventDetailData = undefined;
        //在新增患者事件时默认选择该患者所在的病区,科室,床位
        this.selectStationDepartmentBed(this.patient);
        this.patientEventGuid = this._common.guid();
        if (this.supplementFlag) {
          ({ disabledFlag: this.selectUserDisableFlag, saveButtonFlag: this.checkResult } =
            await this._common.userSelectorDisabled(this.user.userID, true, this.supplementFlag, ""));
        }
      }
      this.showDialog = true;
      this.getEventAssessView(event);
      if (!this.supplementFlag) {
        this.checkResult = await this._common.checkActionAuthorization(this.user, event && event.addEmployeeID);
      }
    },
    /**
     * @description: 获得事件详情信息
     * @param {*} event
     * @return {*}
     */
    getEventAssessView(event) {
      if (event) {
        this.currentRecordsCode = event.recordsCode;
      }
      if (!event || !event.recordsCode) {
        this.eventDetailData = undefined;
        this.currentRecordsCode = undefined;
        return;
      }
      this.assessModelloading = true;
      //获取事件详情表数据
      let params = {
        patientEventID: this.currentEvent ? this.currentEvent.patientEventID : "",
        departmentListID: this.patient.departmentListID,
        stationID: this.patient.stationID,
        recordsCode: event.recordsCode,
        inpatientID: this.patient.inpatientID,
        age: this.patient.age,
        sourceID: this.patientEventGuid,
        sourceType: "EventBR",
      };
      if (this.patientEventID) {
        params.patientEventID = this.patientEventID;
      }
      GetEventAssessView(params).then((result) => {
        this.assessModelloading = false;
        if (this._common.isSuccess(result)) {
          this.eventDetailData = result.data;
        }
      });
    },
    /**
     * @description: 删除患者事件
     * @param {*} row 删除行
     * @return {*}
     */
    async deleteEvent(row) {
      if (this.supplementFlag) {
        ({ disabledFlag: this.selectUserDisableFlag, saveButtonFlag: this.checkResult } =
          await this._common.userSelectorDisabled(this.user.userID, false, this.supplementFlag, row.addEmployeeID));
      } else {
        this.checkResult = await this._common.checkActionAuthorization(this.user, row.addEmployeeID);
      }
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (
        !this.supplementFlag &&
        row.onlyUpdateCurrentStationEventFlag &&
        this.user &&
        this.user.stationID != row.stationID
      ) {
        this._showTip("warning", "非本病区添加，不能删除");
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            patientEventID: row.patientEventID,
            supplementFlag: this.supplementFlag,
          };
          DeleteEvent(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              // 刷新数据
              this.getPatientEventList();
              // 出现入院和转入患者事件的时候，清除浏览器中的病人Station缓存
              if ([2872, 2874].indexOf(row.assessListID) != -1) {
                this.$store.commit("session/setStationList", `station_${row.inpatientID}`);
              }
            }
          });
        }
      });
    },
    /**
     * @description: 保存患者事件
     * @return {*}
     */
    saveEvent() {
      if (!this.eventID) {
        this._showTip("warning", "请选择事件");
        return;
      }
      if (this.supplementFlag && !this.performNurse) {
        this._showTip("warning", "请选择护士！");
        return;
      }
      this.dialogLoading = true;
      this.dialogLoadingText = "保存中……";
      let params = {
        PatientEvent: null,
        EventDetails: [],
      };
      //修改
      if (this.currentEvent) {
        this.currentEvent.assessListID = this.eventID;
        this.currentEvent.occurDate = this.occurDate;
        this.currentEvent.occurTime = this.occurTime;
        this.currentEvent.modifyPersonID = this.performNurse;
        delete this.currentEvent.eventName;
        if (!this.currentRecordsCode) {
          this.currentEvent.recordsCode = undefined;
        } else {
          this.currentEvent.recordsCode = this.currentRecordsCode;
        }
        params.PatientEvent = this.currentEvent;
      } else {
        //新增
        let event = {
          InpatientID: this.patient.inpatientID,
          PatientID: this.patient.patientID,
          CaseNumber: this.patient.caseNumber,
          ChartNo: this.patient.chartNo,
          DepartmentListID: this.patient.departmentListID,
          StationID: this.patient.stationID,
          BedID: this.patient.bedID,
          BedNumber: this.patient.bedNumber,
          OccurDate: this.occurDate,
          OccurTime: this.occurTime,
          AssessListID: this.eventID,
          PatientEventID: this.patientEventGuid,
          ModifyPersonID: this.performNurse,
          AddEmployeeID: this.performNurse,
          sourceID: this.sourceID,
        };
        if (this.currentRecordsCode) {
          event.recordsCode = this.currentRecordsCode;
        }
        params.PatientEvent = event;
      }
      params.PatientEvent.supplementFlag = this.supplementFlag;
      //补录中包含病区科室床位选择组件,所以以其为主
      this.supplementFlag && Object.assign(params.PatientEvent, this.stationDepartmentBed);
      if (this.currentRecordsCode) {
        //获取评估数据
        this.assessDatas.forEach((content) => {
          let detail = {
            assessListID: content.assessListID,
            assessListGroupID: content.assessListGroupID,
          };
          if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
            detail.assessValue = "";
          } else {
            detail.assessValue = content.assessValue;
          }
          params.EventDetails.push(detail);
        });
      }
      SaveEvent(params).then((result) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.showDialog = false;
          // 刷新数据
          this.getPatientEventList();
          // 出现入院和转入患者事件的时候，清除浏览器中的病人Station缓存
          if ([2872, 2874].indexOf(params.PatientEvent.AssessListID) != -1) {
            this.$store.commit("session/setStationList", `station_${params.PatientEvent.InpatientID}`);
          }
        }
      });
    },
    /**
     * @description: 获取病人已有事件列表
     * @return {*}
     */
    async getPatientEventList() {
      this.loading = true;
      let params = {
        inpatientID: this.patient.inpatientID,
        stationID: this.patient.stationID,
        supplementFLag: this.supplementFlag,
      };
      await GetPatientEventList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.patientEventList = result.data;
        }
      });
    },
    /**
     * @description: 获取病人事件配置集合
     * @return {*}
     */
    async getEventList() {
      let params = {
        asyncFlag: null,
        supplementFLag: this.supplementFlag,
        inpatientID: this.patient.inpatientID,
      };
      this.dialogLoading = true;
      this.dialogLoadingText = "加载中……";
      await GetEventList(params).then((result) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(result)) {
          this.eventList = result.data;
        }
      });
    },
    /**
     * @description:Br类弹窗点击事件
     * @param {*} content 点击的选项的详细信息
     * @return {*}
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      this.conponentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: "EventBR",
        sourceID: this.patientEventGuid,
        assessTime:
          this._datetimeUtil.formatDate(this.occurDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.occurTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * @description: 风险组件回调
     * @param {*} resultFlag
     * @return {*}
     */
    result(resultFlag) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brAssessListID);
      }
    },

    /**
     * @description: 患者事件补录选择对应的病区科室以及床位信息赋值
     * @param {*} val -组件中的返回的值
     * @return {*}void
     */
    selectStationDepartmentBed(val) {
      this.stationDepartmentBed.departmentListID = val.departmentListID;
      this.stationDepartmentBed.stationID = val.stationID;
      this.stationDepartmentBed.bedNumber = val.bedNumber;
      this.stationDepartmentBed.bedId = val?.bedId ?? val?.bedID;
    },
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    /**
     * @description: 获取按钮值
     * @param {*} assessListID 点击的按钮配置对应的AssessListID
     * @return {*}
     */
    async getButtonValue(assessListID) {
      let item = "";
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.currentRecordsCode,
        assessListID: assessListID,
        sourceID: this.patientEventGuid,
        sourceType: "EventBR",
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
  },
};
</script>
<style lang="scss">
.event-main {
  height: 100%;
  .event-header {
    text-align: right;
  }
  .event-maintain {
    .content {
      height: 100%;
      .content-span {
        margin-left: 10px;
      }
      .events-list {
        margin: 10px 10px 0 10px;
        .events {
          margin-top: 12px;
          border: 1px solid #dcdfe6;
          .el-radio {
            width: 140px;
            margin: 5px;
          }
        }
      }
      .assess-content {
        margin: 10px 10px 0 10px;
      }
    }
  }
}
</style>
