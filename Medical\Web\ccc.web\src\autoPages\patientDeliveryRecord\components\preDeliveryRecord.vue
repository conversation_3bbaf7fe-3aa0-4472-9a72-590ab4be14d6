<!--
 * FilePath     : \src\autoPages\patientDeliveryRecord\components\preDeliveryRecord.vue
 * Author       : 杨欣欣
 * Date         : 2023-03-10 11:44
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-03-19 08:47
 * Description  : 待产产程观察记录
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="pre-delivery-record" :showHeader="false" v-loading="loading">
    <packaging-table ref="tableRef" v-model="careMains" :headerList="tableHeaders" @rowClick="updateCareMain">
      <!-- 操作 插槽-->
      <div slot="operate" slot-scope="scope">
        <el-tooltip content="修改">
          <div @click.stop="updateCareMain(scope.row)" class="iconfont icon-edit"></div>
        </el-tooltip>
        <el-tooltip content="删除">
          <div @click.stop="delCareMain(scope.row)" class="iconfont icon-del"></div>
        </el-tooltip>
      </div>
    </packaging-table>
    <!-- 新增抽屉 -->
    <el-drawer
      :title="drawerSetting.title"
      :visible.sync="drawerSetting.showFlag"
      :direction="drawerSetting.direction"
      :size="drawerSize"
      custom-class="care-main-drawer"
      :wrapperClosable="false"
      :modal="false"
      :append-to-body="false"
      @closed="getCareMains"
    >
      <base-layout :showFooter="true">
        <div slot="header">
          <slot name="main-header"></slot>
        </div>
        <slot name="main-tabs-layout"></slot>
        <div slot="footer">
          <slot name="main-footer"></slot>
        </div>
      </base-layout>
    </el-drawer>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import {
  GetPatientDeliveryRecordCareMainViews,
  DeletePatientDeliveryRecordCareMain,
} from "@/api/PatientDeliveryRecord";
import { GetSettingValuesByTypeCodeAndValue } from "@/api/Setting.js";
export default {
  components: {
    baseLayout,
    packagingTable,
  },
  props: {
    mainSaveParams: {
      type: Object,
      required: true,
      default: () => {
        return undefined;
      },
    },
    drawerSize: {
      type: String,
      required: true,
    },
  },
  inject: ["parentDrawerState"],
  data() {
    return {
      loading: false,
      tableHeaders: [],
      careMains: [],
      drawerSetting: {
        title: "",
        showFlag: false,
        direction: this.mainSaveParams.drawerDirection,
      },
      defalutTimeInterval: undefined,
    };
  },
  computed: {
    parentInstance() {
      return this.$parent?.$parent;
    },
    // 令主记录抽屉状态响应式化
    recordDrawerShow() {
      return this.parentDrawerState();
    },
  },
  watch: {
    "mainSaveParams.recordID": {
      handler(newVal) {
        if (newVal?.includes("temp")) {
          this.careMains = [];
        }
      },
    },
    "mainSaveParams.inpatientID": {
      handler() {
        this.getCareMains();
      },
    },
    // 监听主记录抽屉状态
    recordDrawerShow(newVal) {
      if (newVal) {
        // TODO：直接调用会导致滚动至底部不生效，暂时使用setTimeout解决
        setTimeout(() => {
          this.$refs.tableRef.scrollIntoView();
        }, 500);
      }
    },
  },
  async mounted() {
    await this.getTableHeaderList();
  },
  activated() {
    this.$emit("setInpatientInfo");
    const returnValues = {
      showAddBtn: true,
    };
    this.$emit("pageActivated", returnValues);
    this.getCareMains();
  },
  methods: {
    /**
     * description: 获取待产产程记录动态表头
     * return {*}
     * param {*}
     */
    async getTableHeaderList() {
      let params = {
        fileClassID: 38,
        fileClassSub: "DeliveryStart",
        useDescription: "1||Table",
        newSourceFlag: true,
      };
      this.tableHeaders = [];
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tableHeaders = res.data;
        }
      });
    },
    /**
     * description: 获取维护表格数据
     * return {*}
     * param {*}
     */
    async getCareMains() {
      if (this.mainSaveParams.recordID.includes("temp")) {
        return;
      }
      let params = {
        recordID: this.mainSaveParams.recordID,
        recordsCode: this.mainSaveParams.recordsCode,
      };
      this.loading = true;
      await GetPatientDeliveryRecordCareMainViews(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.careMains = res.data;
          this.$refs.tableRef.doLayout();
        }
      });
      this.$nextTick(() => {
        this.$refs.tableRef.scrollIntoView();
      });
    },
    /**
     * description: 更新维护记录
     * param {*} row 当前行
     * return {*}
     */
    async updateCareMain(row) {
      if (this.parentInstance && this.parentInstance.addOrModifyCareMain) {
        await this.parentInstance?.addOrModifyCareMain(row);
      }
    },
    /**
     * description: 删除维护
     * param {*} row 当前行
     * return {*}
     */
    async delCareMain(row) {
      await this.mainSaveParams.checkAuthor(row.patientDeliveryCareMainID, "PatientDeliveryCareMain", row.userID);
      if (!this.mainSaveParams.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          this.loading = true;
          let params = {
            careMainID: row.patientDeliveryCareMainID,
          };
          DeletePatientDeliveryRecordCareMain(params).then((res) => {
            this.getCareMains();
            this.loading = false;
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              // 刷新主记录
              this.$emit("refreshCurrentRecord", this.mainSaveParams.recordID);
            }
          });
        }
      });
    },
    /**
     * description: 专项护理弹窗开关函数
     * return {*}
     * param {*} flag 弹框开关，type 新增/修改
     */
    openOrCloseDrawer(flag, type) {
      this.drawerSetting.showFlag = flag;
      if (flag) {
        this.drawerSetting.title = `待产产程观察记录${type}`;
      }
    },
    /**
     * description: 获取工口全开后 新增时间距离最后一次记录的时间间隔
     * return {*}
     */
    getAddCareMainDefalutTimeInterval() {
      let params = {
        settingTypeCode: "DeliveryRecordDefaultTimeInterval",
        typeValue: this.mainSaveParams.recordsCode,
      };
      GetSettingValuesByTypeCodeAndValue(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.defalutTimeInterval = res?.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.pre-delivery-record {
  .care-main-drawer {
    .base-content {
      overflow: hidden;
    }
    .base-footer {
      display: flex;
      justify-content: flex-end;
      bottom: 5px;
      background-color: #fff;
      .bring-checkbox {
        margin-right: 15px;
      }
    }
  }
}
</style>