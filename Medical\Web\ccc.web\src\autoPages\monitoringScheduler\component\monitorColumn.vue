<!--
 * FilePath     : \src\autoPages\monitoringScheduler\component\monitorColumn.vue
 * Author       : 杨欣欣
 * Date         : 2024-06-18 16:55
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-09-11 09:29
 * Description  : 监测列包装组件
 * CodeIterationRecord: 
 -->
<template functional>
  <u-table-column
    v-if="!props.column.isHidden"
    :label="props.column.title"
    :min-width="data.attrs['min-width']"
    class-name="monitor-column"
    align="center"
  >
    <template slot="header" slot-scope="{ column }">
      <span v-html="column.label" />
    </template>
    <!-- 无子列 -->
    <div
      v-if="(!props.column.childColumns || props.column.style.endsWith('DL')) && row[props.column.index]"
      slot-scope="{ row, $index }"
      class="input-nowrap"
    >
      <component
        :is="$options.getComponent(props.column)"
        v-bind="props"
        :row="row"
        :rowIndex="$index"
        v-on="listeners"
      />
      <!-- 当列存在隐藏项，额外渲染隐藏项组件 -->
      <hidden-item v-if="props.column.relationHiddenItemIndex" v-bind="props" :row="row" />
    </div>
    <template
      v-if="props.column.childColumns && props.column.childColumns.length && !props.column.style.endsWith('DL')"
    >
      <monitor-column
        v-for="childColumn in props.column.childColumns"
        v-bind="{ ...data.attrs }"
        :min-width="childColumn.width == 'auto' ? 'auto' : props.convertPX(childColumn.width)"
        :key="childColumn.assessListID"
        :column="childColumn"
        v-on="listeners"
      />
    </template>
  </u-table-column>
</template>
<script>
export default {
  name: "monitorColumn",
  /**
   * @description: 根据类型使用对应组件
   * @param column 列
   * @return
   */
  getComponent(column) {
    switch (column.style) {
      case "TN":
        return "tnCell";
      case "T":
        return "textCell";
      case "DL":
        return "dropdownCell";
      case "MDL":
        return "dropdownCell";
      case "C":
        return "checkboxCell";
      case "B":
        return "buttonCell";
      case undefined:
        return "";
      default:
        this.defaultStyleCb(column);
    }
  },
  /**
   * @description: 默认样式回调
   * @return
   */
  defaultStyleCb({ title, style }) {
    console.log(`%c 找不到列「${title}」对应的组件类型「${style}」`, "color:red");
    return "";
  },
};
</script>
<style lang="scss">
.monitor-column {
  .cell {
    .el-checkbox {
      margin: 0 auto;
    }
    .input-nowrap {
      white-space: nowrap;
    }
  }
}
</style>