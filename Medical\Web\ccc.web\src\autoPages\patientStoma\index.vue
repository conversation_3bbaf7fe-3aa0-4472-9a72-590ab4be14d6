<!--
 * FilePath     : \src\autoPages\patientStoma\index.vue
 * Author       : 郭鹏超
 * Date         : 2020-12-30 16:59
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-23 16:38
 * Description  : 造口记录页面
 * CodeIterationRecord: 2022-06-09 2672-作为护理人员，我需要造口能够选择院外带入选项，且可不选择造口日期，以保证数据正确 -杨欣欣
                        2022-08-16 2022-08-16 2876-专项增加带入护理记录选框 -杨欣欣
                        2023-06-27 3587-护理评估按钮跳转专项录入的内容评估时间跟护理评估保持一致 -杨欣欣
-->
<template>
  <specific-care
    class="stoma-record"
    v-model="showTemplateFlag"
    :recordTitleSlotFalg="true"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    :editFlag="showEditButton"
    :careMainAddFlag="careMainAddFlag"
    :recordAddFlag="!showHistoryRecords"
    :drawerSize="supplementFlag ? '80%' : ''"
    :mainTableHeight="tableOneRowHeight"
    :previewFlag="previewFlag"
    @mainAdd="recordAdd"
    @maintainAdd="careMainAdd"
    @save="saveByRecordsCode"
    @cancel="drawerClose"
    @getHandOverFlag="getChecked($event, 'handOverArr')"
    @getInformPhysicianFlag="getChecked($event, 'informPhysicianArr')"
    @getNursingRecordFlag="getChecked($event, 'bringToNursingRecordArr')"
    v-loading="recordLoading"
    element-loading-text="加载中……"
  >
    <div slot="record-title">
      <el-radio-group v-model="showHistoryRecords">
        <el-radio-button :label="false">现有造口</el-radio-button>
        <el-radio-button :label="true">历史造口</el-radio-button>
      </el-radio-group>
    </div>
    <!-- 主记录 -->
    <div slot="main-record">
      <packaging-table
        ref="recordTable"
        v-model="stomaTableData"
        :headerList="showHistoryRecords ? historyRecordTableHeaders : currentRecordTableHeaders"
        @rowClick="recordClick"
      >
        <template slot="sourceContent" slot-scope="scope">
          <el-tooltip v-if="scope.row.sourceFlag" :content="scope.row.sourceContent" placement="top">
            <div :class="['flag', scope.row.sourceFlag]">
              <span v-if="scope.row.sourceFlag == 'A'">护</span>
              <span v-if="scope.row.sourceFlag == 'H'">班</span>
            </div>
          </el-tooltip>
        </template>
        <template slot="operate" slot-scope="scope">
          <el-tooltip content="修改">
            <div @click.stop="recordAdd(scope.row)" class="iconfont icon-edit"></div>
          </el-tooltip>
          <el-tooltip content="回纳">
            <div v-if="!show(scope.row)" @click.stop="stopStomaRecord(scope.row)" class="iconfont icon-stop"></div>
          </el-tooltip>
          <el-tooltip content="删除">
            <div v-if="!show(scope.row)" @click.stop="deleteStoma(scope.row)" class="iconfont icon-del"></div>
          </el-tooltip>
        </template>
      </packaging-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <div slot="header" class="top"></div>
      <packaging-table ref="stomaCareMainTable" v-model="careMainData" :headerList="mainTableHeader">
        <!-- 评估类型 插槽 -->
        <div slot="sourceContent" slot-scope="scope">
          <span v-if="scope.row.recordsCode.includes('Start')">开始评估</span>
          <span v-else-if="scope.row.recordsCode.includes('End')">结束评估</span>
          <span v-else>例行评估</span>
        </div>
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope">
          <el-tooltip
            v-if="!scope.row.recordsCode.includes('Start') && !scope.row.recordsCode.includes('End')"
            content="修改"
            placement="top"
          >
            <div class="iconfont icon-edit" @click="careMainAdd(scope.row)" />
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <div v-if="!show(scope.row)" class="iconfont icon-del" @click="deleteCareMainData(scope.row)" />
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 抽屉 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
    >
      <div slot="header">
        <span class="label">日期:</span>
        <el-date-picker
          class="date-picker"
          v-model="startDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        />
        <el-time-picker
          class="time-picker"
          v-model="startTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        />
        <station-selector v-model="currentStation" label="病区:" :width="convertPX(420) + 'px'"></station-selector>
        <dept-selector
          label=""
          :width="convertPX(350) + 'px'"
          v-model="currentDepartment"
          :stationID="currentStation"
        ></dept-selector>
        <span v-if="!stopStomaFlag" class="label">
          造口种类:
          <el-select
            id="stoma-type-selector"
            placeholder="请选择种类"
            @change="getStomaAssessView('Start')"
            v-model="stomaKind"
            :disabled="stomaKindCode ? true : false"
          >
            <el-option
              v-for="item in stomaKindList"
              :key="item.value"
              :label="item.description"
              :value="item.settingValue"
            ></el-option>
          </el-select>
        </span>
      </div>
      <body-and-assess-layout :link="link" :bodyShowFlag="!stopStomaFlag">
        <tabs-layout
          ref="tabsLayout"
          :template-list="templateDatas"
          @change-values="changeValues"
          @button-click="buttonClick"
          @checkTN="checkTN"
          v-loading="templateLoading"
          :element-loading-text="templateLoadingText"
        />
      </body-and-assess-layout>
    </base-layout>
    <!--弹出按钮链接框-->
    <el-dialog
      slot="drawer-dialog"
      v-dialogDrag
      :close-on-click-modal="false"
      :title="dialogName"
      :visible.sync="showButtonDialog"
      fullscreen
      custom-class="no-footer specific-care-view"
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
    </el-dialog>
  </specific-care>
</template>
<script>
import specificCare from "@/components/specificCare";
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import packagingTable from "@/components/table/index";
import bodyAndAssessLayout from "@/components/bodyAndAssessLayout";
import { GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import {
  GetStomaRecordList,
  SaveStomaRecord,
  DeleteStomaRecord,
  StomaStorage,
  GetStomaAssessView,
  GetStomaCareMainsByID,
  SavePatientStomaCareMain,
  DeleteStomaCareData,
} from "@/api/Stoma";
import { GetStomaTypes, GetBringToShiftSetting } from "@/api/Setting";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    tabsLayout,
    stationSelector,
    deptSelector,
    specificCare,
    packagingTable,
    bodyAndAssessLayout,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      // 是否呈现历史造口
      showHistoryRecords: false,
      // 造口主记录数据
      stomaTableData: [],
      // 抽屉开关
      showTemplateFlag: false,
      // 抽屉标题
      drawerTitle: undefined,
      // 主记录/维护记录是否显示
      showRecordArr: [true, false],
      // 带入交班是否显示&是否带入
      handOverArr: [true, false],
      // 通知医师是否显示&是否带入
      informPhysicianArr: [true, false],
      // 带入护理记录是否显示&是否带入
      bringToNursingRecordArr: [true, false],
      // 修改权限
      checkResult: true,
      // 维护记录新增按钮开关
      careMainAddFlag: true,
      drawerLoading: false,
      drawerLoadingText: "",
      recordLoading: false,
      templateLoading: false,
      templateLoadingText: "加载中……",
      currentRecord: {},
      stopStomaFlag: false,
      //历史造口不可修改开关
      historyStomaRecordNoUpdateSwitch: false,
      // 现有造口主记录表头配置
      recordTableHeader: [
        {
          label: "来源",
          prop: "",
          tableColumnWidth: 70,
          align: "center",
          slotName: "sourceContent",
          columnStyle: "slot",
          children: [],
          showTable: "all",
        },
        {
          label: "发生科室",
          prop: "occuredDepartmentName",
          tableColumnWidth: 180,
          headerAlign: "center",
          columnStyle: "text",
          children: [],
          showTable: "all",
        },
        {
          label: "定位时间",
          prop: "locatedDate",
          tableColumnWidth: 170,
          align: "center",
          columnStyle: "date",
          children: [],
          showTable: "current",
        },
        {
          label: "造口日期",
          prop: "opDate",
          tableColumnWidth: 170,
          align: "center",
          columnStyle: "date",
          children: [],
          showTable: "current",
        },
        {
          label: "部位",
          prop: "bodyPartName",
          tableColumnWidth: "100",
          minWidthFlag: true,
          headerAlign: "center",
          columnStyle: "text",
          children: [],
          showTable: "all",
        },
        {
          label: "造口种类",
          prop: "stomaKindName",
          tableColumnWidth: 130,
          headerAlign: "center",
          columnStyle: "text",
          children: [],
          showTable: "all",
        },
        {
          label: "记录日期",
          prop: "startDateTime",
          tableColumnWidth: 250,
          align: "center",
          columnStyle: "datetime",
          children: [],
          showTable: "all",
        },
        {
          label: "回纳日期",
          prop: "endDateTime",
          tableColumnWidth: 200,
          align: "center",
          columnStyle: "date",
          children: [],
          showTable: "history",
        },
        {
          label: "执行人",
          prop: "nurseName",
          tableColumnWidth: 100,
          headerAlign: "center",
          columnStyle: "text",
          children: [],
          showTable: "all",
        },
        {
          label: "操作",
          prop: "",
          tableColumnWidth: 150,
          align: "center",
          slotName: "operate",
          columnStyle: "slot",
          children: [],
          showTable: "current",
        },
      ],
      //维护表头数据
      mainTableHeader: [],
      //人体图路径
      link: "",
      //弹窗右侧顶部数据
      patientStomaRecordID: "",
      startDate: "",
      startTime: "",
      currentStation: undefined,
      currentDepartment: undefined,
      stomaKind: undefined,
      //造口类别
      stomaKindList: [],
      //造口新增或修改评估模板
      templateDatas: [],
      checkTNFlag: true,
      //评估模板返回数据
      assessDatas: [],
      //获取造口评估模板种类数据
      recordsCodeInfo: undefined,
      bodyList: [],
      //路由数据
      assessMainID: undefined,
      handoverID: undefined,
      hisOperationNo: undefined,
      patientScheduleMainID: undefined,
      //是否能编辑删除该数据
      showEditButton: false,
      //2021-09-03点击维护时，进入维护页面要带的是否带交班
      settingHandOver: false,
      settingBringToNursingRecord: false,
      patientStomaCareMainID: undefined,
      sourceID: undefined,
      sourceType: undefined,
      stomaKindCode: undefined,
      patientInfo: undefined,
      careMainData: [],
      patientStomaCareMainID: "",
      autoAddFlag: false,
      showButtonDialog: false,
      dialogName: "",
      //造口停止ID
      stopCareMainID: "",
      //补录标记
      supplementFlag: undefined,
      tableOneRowHeight: 0,
      previewFlag: false,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patient: "getPatientInfo",
      token: "getToken",
    }),
    historyRecordTableHeaders() {
      return this.recordTableHeader.filter((m) => m.showTable === "all" || m.showTable === "history");
    },
    currentRecordTableHeaders() {
      return this.recordTableHeader.filter((m) => m.showTable === "all" || m.showTable === "current");
    },
  },
  watch: {
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patientInfo = this.patient;
          this.supplementFlag = undefined;
        }
      },
      immediate: true,
    },
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patientInfo = this.supplemnentPatient;
          this.supplementFlag = "*";
          this.bringToNursingRecordArr = [false, false];
        }
      },
      immediate: true,
    },
    "patientInfo.inpatientID": {
      async handler(newVal) {
        if (newVal) {
          //获取数据
          await this.getStomaKind();
          this.getStomaTableData();
        } else {
          this.stomaTableData = [];
        }
      },
      immediate: true,
    },
    showHistoryRecords(newVal) {
      this.getStomaTableData();
      this.fixTable();
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    /**
     * description: 页面数据初始化
     * param {*}
     * return {*}
     */
    async init() {
      this.assessMainID = this.$route.query.num;
      this.handoverID = this.$route.query.handoverID;
      this.hisOperationNo = this.$route.query.hisOperationNo;
      this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
      this.sourceID = this.$route.query.sourceID;
      this.stomaKindCode = this.$route.query.stomaKind;
      this.autoAddFlag = this.$route.query.autoAddFlag;
      // 设置切换病人
      if (this.$route.query.keys && this.$route.query.keys.length > 0) {
        this._sendBroadcast("setPatientSwitch", false);
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
      this.getBringHandOverSetting();
      this.getBringToNursingRecordSetting();
      this.getHistoryUpdateSwitchSetting();
    },
    /**
     * description: 获取造口主记录表格数据
     * return {*}
     */
    getStomaTableData(recordID = undefined) {
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        hostoryFlag: this.showHistoryRecords,
        recordID,
      };
      this.recordLoading = true;
      GetStomaRecordList(params).then((res) => {
        this.recordLoading = false;
        if (this._common.isSuccess(res) && res.data) {
          if (this.stomaKindCode) {
            this.stomaTableData = res.data.filter((data) => data.stomaKindCode == this.stomaKindCode);
          } else {
            this.stomaTableData = res.data;
          }
          //调整造口开始结束时间格式
          if (this.stomaTableData.length > 0) {
            this.fixTableData();
          }
          this.currentRecord = recordID && this.stomaTableData?.length ? this.stomaTableData[0] : undefined;
          this.$nextTick(() => {
            if (this.autoAddFlag) {
              if (!this.stomaTableData || this.stomaTableData.length <= 0) {
                this.recordAdd();
              } else if (this.stomaTableData.length == 1) {
                this.recordClick(this.stomaTableData[0]);
              }
              this.autoAddFlag = false;
            }
          });
        }
        if (this._common.isSuccess(res) && !res.data) {
          this.stomaTableData = [];
        }
      });
    },
    /**
     * description: 新增或修改造口
     * param {*} tableRow
     * return {*}
     */
    async recordAdd(tableRow) {
      this.stopStomaFlag = false;
      this.patientStomaCareMainID = undefined;
      //修改
      if (tableRow) {
        this.openOrCloseDrawer(true, "造口主记录修改");
        //判断是否可修改或删除该数据
        this.checkResult = await this._common.checkActionAuthorization(this.user, tableRow.nurseName);
        if (this.checkResult) {
          let ret = await this._common.getEditAuthority(
            tableRow.patientStomaRecordID,
            "PatientStomaRecord",
            !!this.supplementFlag
          );
          if (ret) {
            this.showEditButton = false;
            this._showTip("warning", ret);
          } else {
            this.showEditButton = true;
          }
        }
        if (this.supplementFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            tableRow.nurseID
          );
          this.previewFlag = !saveButtonFlag;
        }
        this.patientStomaCareMainID = tableRow.patientStomaCareMainID;
        this.patientStomaRecordID = tableRow.patientStomaRecordID;
        this.startDate = this._datetimeUtil.formatDate(tableRow.startDate, "yyyy-MM-dd");
        this.startTime = this._datetimeUtil.formatDate(tableRow.startTime, "hh:mm");
        this.currentStation = tableRow.occuredStationID;
        this.currentDepartment = tableRow.occuredDepartmentID;
        this.stomaKind = tableRow.stomaKindCode;
        let bodyPart = this.bodyStringToBodyArr(tableRow.bodyPartCode);
        localStorage.setItem("bodyPart", JSON.stringify(bodyPart));
        localStorage.setItem("selectPart", JSON.stringify(bodyPart));
        this.$set(this.informPhysicianArr, 1, tableRow.informPhysician);
        this.$set(this.handOverArr, 1, tableRow.bringToShift);
        this.$set(this.bringToNursingRecordArr, 1, tableRow.bringToNursingRecord);
      } else {
        this.previewFlag = false;
        this.openOrCloseDrawer(true, "造口主记录新增");
        //新增
        this.link =
          "../../static/body/mobileBody.html?type=Common&recordsCode=''&gender=" + this.patientInfo.genderCode;
        this.showEditButton = true;
        this.patientStomaRecordID = "";
        this.templateDatas = [];
        this.startDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.startTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
        this.currentStation = this.patientInfo.stationID;
        this.currentDepartment = this.patientInfo.departmentListID;
        this.stomaKind = this.stomaKindCode ? this.stomaKindCode : "";
        localStorage.setItem("selectPart", JSON.stringify(this.bodyList));
        localStorage.setItem("bodyPart", JSON.stringify(this.bodyList));
        this.$set(this.informPhysicianArr, 1, false);
        this.$set(this.handOverArr, 1, this.settingHandOver);
        this.$set(this.bringToNursingRecordArr, 1, this.settingNursingRecord);
      }
      if (this.stomaKind) {
        this.getStomaAssessView("Start");
      }
    },
    /**
     * description: 获取主记录新增或修改评估模板
     * param {*} type 表单类型(start\maintain\end)
     * return {*}
     */
    async getStomaAssessView(type) {
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        departmentListID: this.patientInfo.departmentListID,
        mappingType: type,
        age: this.patientInfo.age,
        dateOfBirth: this.patientInfo.dateOfBirth,
      };
      let stomaType = this.stomaKindList.find((item) => {
        return item.settingValue == this.stomaKind;
      });
      if (stomaType) {
        params.mappingType = stomaType.typeValue + type;
        this.link =
          "../../static/body/mobileBody.html?type=CommonMulti&recordsCode=" +
          stomaType.typeValue +
          "&gender=" +
          this.patientInfo.genderCode;
      }
      this.drawerLoading = true;
      this.drawerLoadingText = "加载中……";
      await GetAssessRecordsCodeByDeptID(params).then((res) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(res)) {
          this.recordsCodeInfo = res.data;
        }
      });
      if (!this.recordsCodeInfo) {
        return;
      }
      this.templateLoading = true;
      this.templateLoadingText = "加载中……";
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        patientStomaRecordID: this.patientStomaRecordID,
        age: this.patientInfo.age,
        gender: this.patientInfo.genderCode,
        departmentListID: this.patientInfo.departmentListID,
        dateOfBirth: this.patientInfo.dateOfBirth,
      };
      await GetStomaAssessView(params).then((result) => {
        this.templateLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 新增或修改保存
     * param {*}
     * return {*}
     */
    async stomaSave() {
      var selectPart = JSON.parse(localStorage.getItem("bodyPart"));
      selectPart = this.bodyArrToBodyString(selectPart);
      if (!selectPart) {
        this._showTip("warning", "请选择部位！");
        return;
      }
      if (!this.currentStation) {
        this._showTip("warning", "请选择发生病区");
        return;
      }
      if (!this.currentDepartment) {
        this._showTip("warning", "请选择发生科室");
        return;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        return;
      }
      let saveModel = {
        main: {},
        details: [],
        nursingLevel: "",
        recordsCode: "",
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
      };
      //如果来源于措施执行则填入
      if (this.patientScheduleMainID) {
        saveModel.patientScheduleMainID = this.patientScheduleMainID;
      }
      //组装病人主要数据
      let main = {
        bodyPartID: selectPart,
        inpatientID: this.patientInfo.inpatientID,
        patientStomaRecordID: this.patientStomaRecordID ? this.patientStomaRecordID : "",
        occuredDepartmentID: this.currentDepartment,
        occuredStationID: this.currentStation,
        startDate: this.startDate,
        startTime: this.startTime,
        stomaKind: this.stomaKind,
        details: this.getDetails(),
      };
      saveModel.main = main;
      //填入评估ID
      if (this.assessMainID) {
        saveModel.main.assessMainID = this.assessMainID;
      }
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      saveModel.details = main.details;
      saveModel.nursingLevel = this.patientInfo.nursingLevelCode;
      saveModel.recordsCode = this.recordsCodeInfo.recordsCode;
      saveModel.bringToShift = this.handOverArr[1];
      saveModel.informPhysician = this.informPhysicianArr[1];
      saveModel.bringToNursingRecord = this.bringToNursingRecordArr[1];
      saveModel.sourceID = this.sourceID;
      saveModel.sourceType = this.sourceType;
      saveModel.paitentStomaCareMainID = this.patientStomaCareMainID ?? "temp_" + this._common.guid();
      await SaveStomaRecord(saveModel).then((res) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(res)) {
          this.link = "";
          this.openOrCloseDrawer(false);
          this._showTip("success", "保存成功");
          this.getStomaTableData();
          this.fixTable();
        }
      });
    },
    /**
     * description: 维护记录保存
     * return {*}
     */
    careMainSave() {
      if (!this.currentStation) {
        this._showTip("warning", "请选择病区");
        return;
      }
      if (!this.currentDepartment) {
        this._showTip("warning", "请选择科室");
        return;
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        return;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      let params = {
        assessDate: this.startDate,
        assessTime: this.startTime,
        inpatientID: this.patientInfo.inpatientID,
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        nursingLevel: this.patientInfo.nursingLevelCode,
        patientStomaCareMainID: this.patientStomaCareMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        stomaRecordID: this.currentRecord.patientStomaRecordID,
        departmentListID: this.currentDepartment,
        stationID: this.currentStation,
        bringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        details: this.getDetails(),
      };
      //如果来源于措施执行则填入
      if (this.$route.query.patientScheduleMainID) {
        params.patientScheduleMainID = this.$route.query.patientScheduleMainID;
      }
      SavePatientStomaCareMain(params).then((res) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.openOrCloseDrawer(false);
          this.getStomaTableData(this.currentRecord.patientStomaRecordID);
          this.getStomaCareMainData();
        }
      });
    },
    /**
     * description: 造口收纳方法
     * param {*} stopData
     * return {*}
     */
    async stopStomaRecord(stopData) {
      if (!stopData.opDate && stopData.checkOpDateFlag) {
        this._showTip("warning", "病人没有造口日期,无法进行造口回纳");
        return;
      }
      this.currentRecord = stopData;
      this.stopStomaFlag = true;
      this.openOrCloseDrawer(true, "造口回纳");
      this.patientStomaCareMainID = stopData.paitentStomaCareMainID;
      this.startDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.startTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
      this.currentStation = this.patientInfo.stationID;
      this.currentDepartment = this.patientInfo.departmentListID;
      this.patientStomaRecordID = stopData.patientStomaRecordID;
      this.stomaKind = stopData.stomaKindCode;
      this.getStomaAssessView("End");
      this.$set(this.informPhysicianArr, 1, false);
      this.$set(this.handOverArr, 1, this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, this.settingBringToNursingRecord);
      this.fixTable();
    },
    /**
     * description: 造口收纳确认
     * param {*}
     * return {*}
     */
    stopStomaSave() {
      let stomaType = this.stomaKindList.find((item) => {
        return item.settingValue == this.stomaKind ? this.stomaKind : this.currentRecord.stomaKindCode;
      });
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        stomaRecordID: this.patientStomaRecordID ? this.patientStomaRecordID : this.currentRecord.patientStomaRecordID,
        assessDate: this.startDate,
        assessTime: this.startTime,
        stationID: this.currentStation,
        departmentListID: this.currentDepartment,
        nursingLevel: this.patientInfo.nursingLevel,
        recordsCode: stomaType.typeValue + "End",
        bringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
        patientStomaCareMainID: this.stopCareMainID ? this.stopCareMainID : "temp_" + this._common.guid(),
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        details: this.getDetails(),
      };
      StomaStorage(params).then((res) => {
        this.drawerLoading = false;
        this.openOrCloseDrawer(false);
        if (this._common.isSuccess(res)) {
          this._showTip("success", "收纳成功");
          this.getStomaTableData();
          this.getStomaCareMainData();
        }
      });
    },
    show(item) {
      if (item.recordsCode && item.recordsCode.includes("Start")) {
        return true;
      }
      // 判断是否是历史记录, 历史记录按钮不显示
      if (this.historyStomaRecordNoUpdateSwitch && item.inpatientID != this.patientInfo.inpatientID) {
        return true;
      }
      return false;
    },
    /**
     * description: 删除造口
     * param {*} deleteData
     * return {*}
     */
    async deleteStoma(deleteData) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, deleteData.nurseName);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (this.supplementFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          deleteData.nurseID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(
        deleteData.patientStomaRecordID,
        "PatientStomaRecord",
        !!this.supplementFlag
      );
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm(undefined, (flag) => {
        if (flag) {
          let params = {
            stomaRecordID: deleteData.patientStomaRecordID,
          };
          DeleteStomaRecord(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getStomaTableData();
              this.fixTable();
            }
          });
        }
      });
    },
    /**
     * description: 表格时间数据格式化
     * param {*}
     * return {*}
     */
    fixTableData() {
      this.stomaTableData.forEach((item) => {
        if (item.startDate && item.startTime) {
          item.startDateTime =
            this._datetimeUtil.formatDate(item.startDate, "yyyy-MM-dd") +
            "  " +
            this._datetimeUtil.formatDate(item.startTime, "hh:mm");
        }
        if (item.endDate && item.endTime) {
          item.endDateTime =
            this._datetimeUtil.formatDate(item.endDate, "yyyy-MM-dd") +
            "  " +
            this._datetimeUtil.formatDate(item.endTime, "hh:mm");
        }
        if (item.locatedDate) {
          item.locatedDate = this._datetimeUtil.formatDate(item.locatedDate, "yyyy-MM-dd");
        }
        if (item.opDate) {
          item.opDate = this._datetimeUtil.formatDate(item.opDate, "yyyy-MM-dd");
        }
      });
    },
    /**
     * description: 获取造口类别
     * param {*}
     * return {*}
     */
    async getStomaKind() {
      await GetStomaTypes().then((res) => {
        if (this._common.isSuccess(res)) {
          this.stomaKindList = res.data;
        }
      });
    },
    /**
     * description: 人体图数组切换为字符
     * param {*} bodyArr
     * return {*}
     */
    bodyArrToBodyString(bodyArr) {
      let bodyString = "";
      if (bodyArr.length) {
        bodyArr.forEach((item, index) => {
          if (item.bodyPartCode && !index) {
            bodyString = item.bodyPartCode;
          }
          if (item.bodyPartCode && index) {
            bodyString = bodyString + "||" + item.bodyPartCode;
          }
        });
      }
      return bodyString;
    },
    /**
     * description: bodyCode字符转换为数组
     * param {*} idString
     * return {*}
     */
    bodyStringToBodyArr(idString) {
      let bodyPartArr = [];
      if (!idString) {
        return bodyPartArr;
      }
      let arr = idString.split("||");
      if (arr.length) {
        arr.forEach((item) => {
          let bodyPart = {
            bodyPartCode: item,
            bodyPartName: "",
          };
          bodyPartArr.push(bodyPart);
        });
      }
      return bodyPartArr;
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "Stoma",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.handOverArr[1] = res.data;
          this.settingHandOver = res.data;
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "StomaAutoInterventionToRecord",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.bringToNursingRecordArr[1] = res.data;
          this.settingBringToNursingRecord = res.data;
        }
      });
    },
    /**
     * description: 弹窗开关
     * param {*} flag 开关动作
     * param {*} title 弹窗标题
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 点击选中主记录，展示维护记录
     * param {*} row 当前行数据
     * return {*}
     */
    async recordClick(row) {
      if (row.endDate && row.endDate < this.patientInfo.admissionDate) {
        this._showTip("warning", "该造口记录为历史造口，不可进行维护");
        return;
      }
      //没有手术时间且是本次手术
      if (!row.opDate && row.checkOpDateFlag) {
        this._showTip("warning", "病人没有造口日期,无法进行造口维护");
        return;
      }
      this.currentRecord = row;
      const isLastTimeData =
        this.historyStomaRecordNoUpdateSwitch && this.currentRecord.inpatientID != this.patientInfo.inpatientID;
      this.previewFlag = isLastTimeData;
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.stomaTableData = [row];
        // 维护记录新增按钮呈现条件：非上次住院数据 且 未结束
        this.careMainAddFlag = !isLastTimeData && !row.endDate;
        this.$nextTick(() => {
          this.tableOneRowHeight = this._common.getTableOneRowHeight(
            this.$refs.recordTable?.$el,
            ".main-record-row",
            ".main-record-header-row"
          );
        });
        this.getStomaCareMainData();
        await this.getTableHeaderList();
      } else {
        this.getStomaTableData();
      }
    },
    /**
     * description: 抽屉保存按钮回调，根据RecordsCode判断保存数据
     * return {*}
     */
    async saveByRecordsCode() {
      if (this.recordsCodeInfo.recordsCode.includes("Start") && this.link) {
        this.stomaSave();
      } else if (this.recordsCodeInfo.recordsCode.includes("End")) {
        this.stopStomaSave();
      } else {
        this.careMainSave();
      }
    },
    /**
     * description: 弹窗关闭
     * param {*}
     * return {*}
     */
    drawerClose() {
      this.recordID = undefined;
      this.careMainID = undefined;
      this.templateDatas = [];
      this.assessDatas = [];
      this.showTemplateFlag = false;
    },
    /**
     * description: 抽屉下方勾选项数据回调
     * param {*} flag 回传的勾选状态
     * param {*} checkAttr 勾选项字段名
     * return {*}
     */
    getChecked(flag, checkAttr) {
      this[checkAttr][1] = flag;
    },
    /**
     * description: 获取造口维护数据
     * return {*}
     */
    getStomaCareMainData() {
      let params = {
        stomaID: this.currentRecord.patientStomaRecordID,
      };
      GetStomaCareMainsByID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.length > 0) {
            res.data.forEach((item) => {
              item.addDate =
                this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd") +
                "  " +
                this._datetimeUtil.formatDate(item.assessTime, "hh:mm");
            });
          }
          this.careMainData = res.data;
          this.$nextTick(() => {
            this.$refs?.stomaCareMainTable?.doLayout();
          });
        }
      });
    },
    /**
     * description: 造口维护新增或修改
     * param {*} careMainData
     * return {*}
     */
    async careMainAdd(careMainData) {
      this.stopStomaFlag = true;
      if (careMainData) {
        this.openOrCloseDrawer(true, "造口维护记录修改");
        //判断是否可修改或删除该数据
        this.checkResult = await this._common.checkActionAuthorization(this.user, careMainData.addEmployeeID);
        if (this.checkResult) {
          let ret = await this._common.getEditAuthority(
            careMainData.patientStomaCareMainID,
            "PatientStomaCareMain",
            !!this.supplementFlag
          );
          if (ret) {
            this.showEditButton = false;
            this._showTip("warning", ret);
          } else {
            this.showEditButton = true;
          }
        }
        if (this.supplementFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            careMainData.modifyPersonID
          );
          this.previewFlag = !saveButtonFlag;
        }
        this.startDate = this._datetimeUtil.formatDate(careMainData.assessDate, "yyyy-MM-dd");
        this.startTime = this._datetimeUtil.formatDate(careMainData.assessTime, "hh:mm");
        this.$set(this.informPhysicianArr, 1, careMainData.informPhysician);
        this.$set(this.handOverArr, 1, careMainData.bringToShift);
        this.$set(this.bringToNursingRecordArr, 1, careMainData.bringToNursingRecord);
      } else {
        this.showEditButton = true;
        //弹窗时间处理
        this.startDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.startTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
        this.$set(this.handOverArr, 1, this.settingHandOver);
        this.$set(this.informPhysicianArr, 1, false);
        this.$set(this.bringToNursingRecordArr, 1, this.settingBringToNursingRecord);
        this.openOrCloseDrawer(true, "造口维护记录新增");
      }
      this.currentStation = careMainData ? careMainData.stationID : this.patientInfo.stationID;
      this.currentDepartment = careMainData ? careMainData.departmentListID : this.patientInfo.departmentListID;
      this.patientStomaCareMainID = careMainData ? careMainData.patientStomaCareMainID : "temp_" + this._common.guid();
      this.getStomaMainAssessView(careMainData);
      //造口停止记录修改ID赋值
      if (careMainData && careMainData.recordsCode.includes("End")) {
        this.stopCareMainID = careMainData.patientStomaCareMainID;
      }
    },
    /**
     * description: 获取维护记录新增或修改评估模板
     * param {*} viewDate
     * return {*}
     */
    async getStomaMainAssessView(viewDate) {
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        departmentListID: this.patientInfo.departmentListID,
        age: this.patientInfo.age,
        dateOfBirth: this.patientInfo.dateOfBirth,
      };
      if (viewDate) {
        params.mappingType = viewDate.recordsCode;
      } else {
        //209 - 肠造口 3231-泌尿造口
        this.currentRecord.stomaKindCode == "209"
          ? (params.mappingType = "EnterostomaMaintain")
          : (params.mappingType = "UrostomaMaintain");
      }
      this.templateLoading = true;
      this.drawerLoading = true;
      this.drawerLoadingText = "加载中……";
      await GetAssessRecordsCodeByDeptID(params).then((res) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(res)) {
          this.recordsCodeInfo = res.data;
        }
      });
      if (!this.recordsCodeInfo) {
        this.templateLoading = false;
        return;
      }
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patientInfo.age,
        gender: this.patientInfo.genderCode,
        departmentListID: this.patientInfo.departmentListID,
        stomaCareMainID:
          this.patientStomaCareMainID.indexOf("temp") != -1
            ? this.patientStomaCareMainID.split("_")[1]
            : this.patientStomaCareMainID,
        patientStomaRecordID: this.currentRecord.patientStomaRecordID,
        dateOfBirth: this.patientInfo.dateOfBirth,
        inpatientID: this.patientInfo.inpatientID,
      };
      await GetStomaAssessView(params).then((result) => {
        this.templateLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 删除造口维护记录
     * param {*} deleteData
     * return {*}
     */
    async deleteCareMainData(deleteData) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, deleteData.addEmployeeID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (this.supplementFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          deleteData.modifyPersonID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(
        deleteData.patientStomaCareMainID,
        "PatientStomaCareMain",
        !!this.supplementFlag
      );
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm(undefined, (flag) => {
        if (flag) {
          let params = {
            mainID: deleteData.patientStomaCareMainID,
          };
          DeleteStomaCareData(params).then(async (res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getStomaCareMainData();
              //造口停止，删除结束维护记录要收起维护记录，刷新整个主记录
              if (deleteData.recordsCode.includes("End")) {
                this.getStomaTableData();
                this.fixTable();
              } else {
                //其他维护记录删除逻辑
                this.getStomaTableData(deleteData.patientStomaRecordID);
              }
            }
          });
        }
      });
    },
    /**
     * description: 新增或修改评估模板返回数据
     * param {*} value 返回数据
     * return {*}
     */
    changeValues(value) {
      this.assessDatas = value;
    },
    /**
     * description: 检核TN类型
     * param {*} flag 检核结果
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 获取维护记录动态列
     * param {*}
     * return {*}
     */
    async getTableHeaderList() {
      let params = {
        fileClassID: 34,
        useDescription: "1||Table",
        newSourceFlag: false,
      };
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.mainTableHeader = res.data;
        }
      });
    },
    /**
     * description: 获取评估保存明细
     * return {*}
     */
    getDetails() {
      let details = [];
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          specialListType: content.specialListType,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        if (content.disableGroup != -1) {
          details.push(detail);
        }
      });
      return details;
    },
    /**
     * description: 重置选中状态
     * param {*}
     * return {*}
     */
    fixTable() {
      this.showRecordArr = [true, false];
      this.careMainData = [];
    },
    /**
     * description: 按钮处理
     * param {*} content assessContent配置
     * return {*}
     */
    async buttonClick(content) {
      this.dialogName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += (url.includes("?") ? "&" : "?") + `token=${this.token}`;
      url +=
        `&inpatientID=${this.patientInfo.inpatientID}` +
        `&sourceID=${this.patientStomaCareMainID && this.patientStomaCareMainID.replace(/temp_/g, "")}` +
        `&sourceType=Stoma` +
        "&isDialog=true";
      this.showButtonDialog = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 获取显示措施说明开关
     * return {*}
     */
    getHistoryUpdateSwitchSetting() {
      let params = {
        settingTypeCode: "HistoryStomaRecordNoUpdateSwitch",
      };
      GetSettingSwitchByTypeCode(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.historyStomaRecordNoUpdateSwitch = response.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.stoma-record {
  height: 100%;
  // 设置单元格内的行间距，以免内容折行时文字挤在一起
  .main-record .main-record-content .cell {
    line-height: normal;
  }
  .date-picker {
    width: 160px;
  }
  .time-picker {
    width: 140px;
  }
  #stoma-type-selector {
    width: 304px;
  }
  .station-selector .label {
    margin-left: 0px;
  }
  .flag {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    width: 20px;
    border-radius: 10px;
    text-align: center;
    font-size: 12px;
    color: #fff;
    font-weight: bold;
    margin-bottom: 2px;
    &.A {
      background-color: #f5c181;
    }
    &.H {
      background-color: #21e6c1;
    }
    .stoma-tabs-layout {
      width: 100%;
    }
  }
}
</style>