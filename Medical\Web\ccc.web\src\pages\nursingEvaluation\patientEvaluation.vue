<!--
 * FilePath     : \ccc.web\src\pages\nursingEvaluation\patientEvaluation.vue
 * Author       : 李青原
 * Date         : 2020-03-26 16:02
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-12-22 10:43
 * Description  : 护理问题评价
                  页面支持组件传值和URL传值，当url中存在inpatientID，判断页面以url传值，组件传值将无效，
                  提交按钮：当判断以url传值是，提交按钮将显示，组件则可通过传值选择是否显示；
 -->

<template>
  <div class="patient-evaluation" v-loading="loading" :element-loading-text="loadingText">
    <div class="header" v-if="showCommit">
      <el-radio-group class="isEvaluate" v-model="isEvaluate" @change="getHistoryPatientProblem()">
        <el-radio-button :label="false">未评价</el-radio-button>
        <el-radio-button :label="true">已评价</el-radio-button>
      </el-radio-group>
      <span class="label" v-if="!isEvaluate">评价日期：</span>
      <el-date-picker v-model="evaluationDate" :default-value="evaluationDate" value-format="yyyy-MM-dd"
        format="yyyy-MM-dd" type="date" style="width: 110px" v-if="!isEvaluate"></el-date-picker>
      <span class="label" v-if="!isEvaluate">评价时间：</span>
      <el-time-picker v-model="evaluationTime" :default-value="evaluationTime" value-format="HH:mm" format="HH:mm"
        style="width: 80px" v-if="!isEvaluate"></el-time-picker>
      <el-button class="print-button" icon="iconfont icon-back" v-if="isGoBack" @click="goBack">返回</el-button>
    </div>
    <el-table :height="showCommit ? 'calc(100% - 106px)' : '100%'" v-show="!isEvaluate" :data="problemData" border stripe>
      <el-table-column prop="nursingProblem" label="护理问题" header-align="center" min-width="145"></el-table-column>
      <el-table-column label="相关因素" min-width="100" align="left">
        <template slot-scope="scope">
          <div v-html="scope.row.relatedFactor"></div>
        </template>
      </el-table-column>
      <el-table-column label="定义特征" min-width="100" align="left">
        <template slot-scope="scope">
          <div v-html="scope.row.signAndSymptom"></div>
        </template>
      </el-table-column>
      <el-table-column prop="nursingGoal" label="预期护理目标" min-width="100" align="center"></el-table-column>
      <el-table-column label="护理结局" width="140" align="center">
        <template slot-scope="scope">
          <el-select v-model="scope.row.actualGoalID" placeholder="请选择">
            <el-option v-for="item in scope.row.actualGoals" :key="item.id" :label="item.descript"
              :value="item.id"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="情况说明" width="200" align="center">
        <template slot-scope="scope">
          <input class="mark-in" v-model="scope.row.mark" type="text" />
        </template>
      </el-table-column>
      <el-table-column label="未执行措施" header-align="center" min-width="340">
        <template slot-scope="scope">
          <div v-html="scope.row.noPatientScheduleMainName"></div>
        </template>
      </el-table-column>
    </el-table>
    <el-table v-show="isEvaluate" height="calc(100% - 106px)" :data="historyQues" class="histroy-problem" stripe border>
      <el-table-column label="护理问题" width-min="400px" prop="problem"></el-table-column>
      <el-table-column label="相关因素" min-width="100" align="left">
        <template slot-scope="scope">
          <div v-html="scope.row.relatedFactor"></div>
        </template>
      </el-table-column>
      <el-table-column label="定义特征" min-width="100" align="left">
        <template slot-scope="scope">
          <div v-html="scope.row.signAndSymptom"></div>
        </template>
      </el-table-column>
      <el-table-column label="实际结果" width="150px" align="center" prop="outCome"></el-table-column>
      <el-table-column label="起始时间" width="160px" header-align="center" prop="startDate" sortable>
        <template slot-scope="scope">
          {{ scope.row.startDate.substring(0, 10) + " " + scope.row.startTime.substring(0, 5) }}
        </template>
      </el-table-column>
      <el-table-column label="结束时间" width="160px" header-align="center" prop="endDate">
        <template slot-scope="scope">
          {{ scope.row.endDate.substring(0, 10) + " " + scope.row.endTime.substring(0, 5) }}
        </template>
      </el-table-column>
      <el-table-column label="评价时间" width="160px" header-align="center" prop="evaluationDateTime"></el-table-column>
    </el-table>
    <div class="button-line">
      <el-button type="primary" v-if="showCommit && problemData.length > 0 && !isEvaluate" icon="el-icon-check"
        @click="commit()">
        提 交
      </el-button>
    </div>
  </div>
</template>
<script>
import { GetPreEvaluations } from "@/api/Problem";
import { EvaluationProblem } from "@/api/PatientProblem";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetHistoryPatientProblems } from "@/api/PatientProblem";
import { mapGetters } from "vuex";
export default {
  props: {
    AllFlag: { type: Boolean, default: false },
    showcommit: { type: Boolean, default: true },
    inpatientid: { type: String, default: "" },
    stationid: { type: Number },
    problemids: { type: Array, default: () => [] },
    assessDate: { type: String },
    assessTime: { type: String },
    warnFlag: { type: Boolean, default: false },
  },
  data() {
    return {
      problemData: [],
      inpatientID: this.inpatientid,
      stationID: this.stationid,
      flag: this.AllFlag,
      showCommit: this.showcommit,
      problemID: "",
      isGoBack: false,
      loading: false,
      loadingText: "",
      //评价日期时间
      evaluationDate: this.assessDate ? this.assessDate : this._datetimeUtil.getNowDate(),
      evaluationTime: this.assessTime ? this.assessTime : this._datetimeUtil.getNowTime(),
      // 历史护理问题
      historyQues: [],
      isEvaluate: false,
      warnTipFlag: this.warnFlag,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patient: "getPatientInfo",
    }),
  },
  watch: {
    inpatientid: {
      // immediate: true,
      async handler(newV) {
        this.inpatientID = newV;
        this.stationID = this.stationid;
        //检查病人信息更新，病人信息更新，则stationID也同时会更新
        if (newV) {
          await this.getTableData();
          this.getHistoryPatientProblem();
        }
      },
    },
    assessTime: {
      async handler(newV) {
        this.evaluationTime = newV;
      },
    },
    assessDate: {
      async handler(newV) {
        this.evaluationDate = newV;
      },
    },
  },
  mounted() {
    this.init();
    this.getTableData();
    this.getHistoryPatientProblem();
  },
  methods: {
    // 初始化数据
    init() {
      //判断引用方式；
      let inpatientID = this.$route.query.inpatientID;
      //url传值
      if (inpatientID) {
        this.inpatientID = inpatientID;
        //修复护理问题不显示问题  --GPC
        if (!this.stationID) {
          this.stationID = this.$route.query.stationID;
        }
        this.problemID = this.$route.query.problemID;
        if (this.$route.query.isGoBack) {
          this.isGoBack = this.$route.query.isGoBack;
        }
      }
      if (this.warnTipFlag) {
        this._showTip("warning", "评价护理问题会删除评价时间点后未执行排程！请谨慎操作！");
      }
    },
    //获取护理问题
    async getTableData() {
      if (!this.stationID) {
        return;
      }
      this.loading = true;
      this.loadingText = "加载中……";
      let params = {
        inPatientID: this.inpatientID,
        stationID: this.stationID,
        flag: this.flag,
      };
      //判断护理问题ID是否存在 如果存在使用problemid进行查询
      if (this.problemID) params.patientProblemIDs = this.problemID;
      if (this.problemids.length > 0) params.patientProblemIDs = this.problemids.toString();
      await GetPreEvaluations(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.length > 0) {
            let data = response.data;
            for (let i = 0; i < data.length; i++) {
              let goalsObj = data[i].actualGoals;
              let goalArray = [];

              for (let attr in goalsObj) {
                let item = { id: attr, descript: goalsObj[attr] };
                goalArray.push(item);
              }
              data[i].actualGoals = goalArray;
              let item = data[i];
            }
            this.problemData = response.data;
            this.$emit("have-problem", true);
          } else {
            this.problemData = [];
            this.$emit("have-problem", false);
            // this._showTip('warning', '无护理问题待评价！');
          }
        }
        this.loading = false;
      });
    },
    //提交方法
    async commit() {
      let params = [];
      return this.getNowStationShift().then((data) => {
        for (let i = 0; i < this.problemData.length; i++) {
          let item = this.problemData[i];
          let tempTime =
            this._datetimeUtil.formatDate(this.evaluationDate, "yyyy-MM-dd") +
            " " +
            this._datetimeUtil.formatDate(this.evaluationTime, "hh:mm:ss");
          if (tempTime > this._datetimeUtil.getNow()) {
            this._showTip("warning", "评价日期不得大于当前日期");
            return;
          }
          if (!item.actualGoalID) {
            this._showTip("warning", "护理问题：" + item.nursingProblem + "  未评价！");
            return;
          }
          let obj = {
            PatientProblemID: item.patientProblemID,
            PatientGoalID: item.patientGoalID,
            NursingOutcomeID: item.actualGoalID,
            EvaluateShift: data.nowShift.shift,
            //EvaluationDate: data.shiftDate,
            //EvaluationTime: this._datetimeUtil.formatDate(new Date(), "hh:mm:ss"),
            //2021-05-25 En 原先使用当前班别日期和当前时间，调整为前端选择日期、时间
            EvaluationDate: this.evaluationDate,
            EvaluationTime: this.evaluationTime,
            EvaluationNote: item.mark,
            ModifyPersonID: this.user.userID,
          };
          params.push(obj);
        }
        this.loading = true;
        this.loadingText = "保存中……";
        EvaluationProblem(params).then((response) => {
          this.loading = false;
          if (this._common.isSuccess(response)) {
            this._showTip("success", "保存成功！");
            this.getTableData();
          }
        });
      });
    },
    getHistoryPatientProblem() {
      this.historyQues = [];
      if (!this.isEvaluate) {
        return;
      }
      this.loading = true;
      this.loadingText = "加载中……";
      let params = {
        inPatientID: this.inpatientID,
        stationID: this.stationID,
        flag: this.isEvaluate,
      };
      GetHistoryPatientProblems(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data && res.data.length > 0) {
            res.data.forEach((item) => {
              if (item.evaluationDate && item.evaluationTime) {
                this.$set(
                  item,
                  "evaluationDateTime",
                  this._datetimeUtil.formatDate(item.evaluationDate, "yyyy-MM-dd") +
                  " " +
                  this._datetimeUtil.formatDate(item.evaluationTime, "hh:mm")
                );
              }
            });
          }
          this.historyQues = res.data;
        }
      });
    },
    goBack() {
      this.$router.go(-1); //返回上一层
    },
    async getNowStationShift() {
      let result;
      await GetNowStationShiftData().then((data) => {
        if (this._common.isSuccess(data)) {
          result = data.data;
        }
      });
      return result;
    },
  },
};
</script>
<style lang="scss">
.patient-evaluation {
  background-color: #fff;
  height: 100%;

  .header {
    line-height: 45px;
    margin-bottom: 10px;
    padding: 0 10px;

    .isEvaluate {
      margin-right: 10px;
    }

    .print-button {
      margin-top: 10px;
      float: right;
    }
  }

  .mark-in {
    width: 100%;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    box-sizing: border-box;
    border: 1px solid #dcdfe6;
  }

  .button-line {
    width: 100%;
    margin: 10 0px;
    text-align: center;
  }
}
</style>
