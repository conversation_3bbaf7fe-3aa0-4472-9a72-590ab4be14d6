/*
 * FilePath     : \src\api\PatientPain.js
 * @Author: 李雪建
 * @Date: 2021-06-14 09:20:19
 * LastEditors  : 苏军志
 * LastEditTime : 2021-08-18 15:05
 * @Description: 疼痛评估接口
 */

import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientPain";

export const urls = {
  GetPainRecordsByInpatientID: baseUrl + "/GetPainRecordsByInpatientID",
  GetPainAssessView: baseUrl + "/GetPainAssessView",
  SavePatientPain: baseUrl + "/SavePatientPain",
  DeleteByPainRecodeID: baseUrl + "/DeleteByPainRecodeID",
  GetPainCareByRecordID: baseUrl + "/GetPainCareByRecordID",
  DeletePatientPainMain: baseUrl + "/DeletePatientPainMain",
  SavePainCare: baseUrl + "/SavePainCare",
};

// 获取疼痛记录列表
export const GetPainRecordsByInpatientID = params => {
  return http.get(urls.GetPainRecordsByInpatientID, params);
};
// 查询评估模板疼痛
export const GetPainAssessView = params => {
  return http.get(urls.GetPainAssessView, params);
};
// 保存疼痛
export const SavePatientPain = params => {
  return http.post(urls.SavePatientPain, params);
};
// 根据painRecordID删除疼痛记录数据
export const DeleteByPainRecodeID = params => {
  return http.post(urls.DeleteByPainRecodeID, qs.stringify(params));
};
// 获取疼痛维护记录
export const GetPainCareByRecordID = params => {
  return http.get(urls.GetPainCareByRecordID, params);
};
// 根据careMainID删除疼痛评估
export const DeletePatientPainMain = params => {
  return http.post(urls.DeletePatientPainMain, qs.stringify(params));
};
//新增疼痛明细
export const SavePainCare = params => {
  return http.post(urls.SavePainCare, params);
};

