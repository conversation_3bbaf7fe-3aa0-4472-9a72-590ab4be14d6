/*
 * @Author: your name
 * @Date: 2021-07-27 20:31:35
 * LastEditTime : 2023-07-24 16:58
 * LastEditors  : 马超
 * @Description: In User Settings Edit
 * FilePath     : \projectManagement.webe:\CCC3.1\Medical\Web\ccc.web\src\api\SettingDescription.js
 */

import http from "../utils/ajax";
const baseUrl = "/SettingDescription";
import qs from "qs";

export const urls = {
  //获取相应的配置信息
  GetSettingDescriptionBySettingType:
    baseUrl + "/GetSettingDescriptionBySettingType",
  //添加的Route
  SaveSettingDescription: baseUrl + "/SaveSettingDescription",
  //修改的Router
  UpdateSettingDescription: baseUrl + "/UpdateSettingDescription",
  //删除的Router
  DeleteSettingDescription: baseUrl + "/DeleteSettingDescription",
  //查询一个的Router
  GetSettingDescriptionById: baseUrl + "/GetSettingDescriptionById",
  //模糊查询说名列的router
  GetSettingDescriptionlike: baseUrl + "/GetSettingDescriptionlike",
  //获取带入相关配置
  GetBringToNursingRecordFlagSetting:
    baseUrl + "/GetBringToNursingRecordFlagSetting",
  //获取是否显示的开关
  GetSettingSwitchByTypeCode: baseUrl + "/GetSettingSwitchByTypeCode",
  //获取病案查询页面是否依据异动记录自动刷新
  GetDocumentAutoRefreshFlag: baseUrl + "/GetDocumentAutoRefreshFlag",
  //登陆时获取开关配置
  GetSettingSwitch: baseUrl + "/GetSettingSwitch",
  //根据SettingTypeCode获取SettingValue
  GetSettingValueByTypeCodeAsync: baseUrl + "/GetSettingValueByTypeCodeAsync",
  //获取是否显示的开关
  GetSettingSwitchByTypeCodeAndTypeValue:
    baseUrl + "/GetSettingSwitchByTypeCodeAndTypeValue"
};
//登陆时获取开关配置
export const GetSettingSwitch = params => {
  return http.get(urls.GetSettingSwitch, params);
};
// 获取相应的配置信息
export const GetSettingDescriptionBySettingType = params => {
  return http.get(urls.GetSettingDescriptionBySettingType, params);
};

// 添加数据
export const SaveSettingDescription = params => {
  return http.post(urls.SaveSettingDescription, params);
};

// 修改数据
export const UpdateSettingDescription = params => {
  return http.post(urls.UpdateSettingDescription, params);
};

// 逻辑删除数据
export const DeleteSettingDescription = params => {
  return http.get(urls.DeleteSettingDescription, params);
};

// 获取带入相关配置
export const GetBringToNursingRecordFlagSetting = params => {
  return http.get(urls.GetBringToNursingRecordFlagSetting, params);
};
//获取是否显示的开关
export const GetSettingSwitchByTypeCode = params => {
  return http.get(urls.GetSettingSwitchByTypeCode, params);
};
//获取病案查询页面是否依据异动记录自动刷新
export const GetDocumentAutoRefreshFlag = params => {
  return http.post(urls.GetDocumentAutoRefreshFlag, qs.stringify(params));
};
//根据SettingTypeCode获取SettingValue
export const GetSettingValueByTypeCodeAsync = params => {
  return http.get(urls.GetSettingValueByTypeCodeAsync, params);
};
//获取是否显示的开关
export const GetSettingSwitchByTypeCodeAndTypeValue = params => {
  return http.get(urls.GetSettingSwitchByTypeCodeAndTypeValue, params);
};
