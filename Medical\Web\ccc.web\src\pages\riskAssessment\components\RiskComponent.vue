<!--
 * FilePath     : \src\pages\riskAssessment\components\RiskComponent.vue
 * Author       : 苏军志
 * Date         : 2023-02-25 18:42
 * LastEditors  : 曹恩
 * LastEditTime : 2025-06-16 17:34
 * Description  : 风险业务组件
 * CodeIterationRecord: 
-->
<template>
  <base-layout
    :showHeader="false"
    :showFooter="!componentParams.readOnly"
    class="risk-component"
    v-loading="loading"
    :element-loading-text="loadingText"
  >
    <risk-rating-scale
      v-if="componentParams.showStyle == 'Risk'"
      :conponentData="conponentData"
      :dateTimeArr="[componentParams.showTime, riskAssessDateTime]"
      :showPointFlag="componentParams.showPoint"
      :showBar="componentParams.showBar"
      :recordListID="recordListID"
      :recordList="componentParams.recordList"
      :comEptBedDataList="stationDepartmentBedDate"
      :supplementFalg="componentParams.supplement"
      :dateTimeReadonly="componentParams.assessTimeReadonly"
      :editFlag="!!componentParams.patientScoreMainID"
      @checked-collect="getChecked"
      @deptBedClick="getEptBedData"
      @custCkick="
        (recordListID) => {
          this.recordListID = recordListID;
        }
      "
    />
    <!-- 新风险组件，等完全切换后，把上面v-if删掉 -->
    <base-layout v-if="componentParams.showStyle == 'Assess'" headerHeight="auto">
      <div slot="header" class="header-wrap">
        <div class="supplement-wrap">
          <span v-if="comSupplementFlag.formSwitch">
            表单：
            <el-select
              v-model.number="recordListID"
              clearable
              placeholder="请选择"
              class="record-name"
              :disabled="!!componentParams.patientScoreMainID"
            >
              <el-option
                v-for="item in componentParams.recordList"
                :key="item.recordListID"
                :label="item.recordName"
                :value="item.recordListID"
              ></el-option>
            </el-select>
          </span>
          <station-Department-Bed-Date
            :stDeptBed="stationDepartmentBedDate"
            :switch="comSupplementFlag"
            :componentsReadonly="{ date: componentParams.assessTimeReadonly }"
            @custCkick="getEptBedData"
          ></station-Department-Bed-Date>
        </div>
        <div class="total-point-wrap" v-if="componentParams.showPoint">
          <el-tooltip :content="riskRangeStr">
            <span class="total-range" v-if="componentParams.showRange">{{ riskRangeStr }}</span>
          </el-tooltip>
          <span class="total-label">总分&nbsp;:</span>
          <span class="total">&nbsp;{{ riskPoint }}</span>
        </div>
      </div>
      <tabs-layout
        ref="tabsLayout"
        :class="['assess-risk-layout', { 'show-bar': componentParams.showBar }]"
        :templateList="assessTemplate"
        showStyle="Table"
        model="add"
        hiddenSingleTab
        @change-values="changeValues"
      />
      <risk-bar
        v-if="componentParams.showBar"
        :riskRange="riskRange ? riskRange : []"
        v-model="riskPoint"
        riskBalWidth="800"
        :showPointFlag="componentParams.showPoint"
        :reverseOrderFlag="useReverseOrderRiskBar && showReverseOrderBarStyle"
      />
    </base-layout>
    <div slot="footer" class="component-footer">
      <el-button @click="result(false)">取消</el-button>
      <el-button v-if="saveButtonShowFlag" type="primary" @click="result(true)">确定</el-button>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import riskRatingScale from "@/pages/riskAssessment/components/RiskRatingScale";
import stationDepartmentBedDate from "@/pages/recordSupplement/components/stationDepartmentBedDate";
import riskBar from "./RiskBar";
import tabsLayout from "@/components/tabsLayout/index";
import { GetRiskSaveCheckTime } from "@/api/Assess";
import { GetSourceRiskRecord, GetRiskAssessView } from "@/api/PatientRiskRecord";
import { SavePatientScoreMainAndDetail } from "@/api/PatientScore";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  components: {
    baseLayout,
    riskRatingScale,
    stationDepartmentBedDate,
    riskBar,
    tabsLayout,
  },
  props: {
    // 组件接收参数集合
    params: {
      type: Object,
      required: true,
    },
    //保存按钮是否显示
    saveButtonShowFlag: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    params: {
      immediate: true,
      handler(newValue) {
        this.initParams();
        newValue && newValue.recordListID && this.init();
      },
    },
    recordListID: {
      immediate: true,
      handler(newValue) {
        if (!newValue) {
          return;
        }
        if (this.componentParams && this.componentParams.recordList && this.componentParams.recordList.length) {
          let record = this.componentParams.recordList.find((record) => record.recordListID == newValue);
          if (record) {
            this.componentParams.recordListID = record.recordListID;
            this.componentParams.showStyle = record.showStyle;
            this.componentParams.recordsCode = record.recordsCode;
          }
        }
        this.init();
      },
    },
  },
  data() {
    return {
      defaultParams: {
        // 病人信息
        patientInfo: {},
        // 记录ID
        patientScoreMainID: undefined,
        // 来源ID
        sourceID: undefined,
        // 来源类型
        sourceType: undefined,
        // 最后护理评估时间
        lastAssessDateTime: undefined,
        // 显示样式：取值 Risk、Assess，默认Risk
        showStyle: "Risk",
        // 显示分数条
        showBar: true,
        // 是否显示分数
        showPoint: true,
        // 格式化
        // listFormat: false,
        // 显示分数区间
        showRange: true,
        // 显示评估时间
        showTime: false,
        // 评估时间
        assessTime: undefined,
        // 评估时间是否为只读
        assessTimeReadonly: false,
        // 风险表序号
        recordListID: undefined,
        // 风险表选项
        recordList: [],
        recordsCode: undefined,
        // 是否显示新增按钮
        showAaddButton: false,
        //首次是否默认护理评估时间
        useAdmissionAssessDateFlag: false,
        // 是否为补录
        supplement: false,
        // 补录相关参数
        supplementParams: {},
        // 只读标记
        readOnly: false,
        // 提交标记
        submitFlag: true,
        //忽略早于护理评估时间检核
        timeCheckFlag: true,
      },
      componentParams: {},
      loading: false,
      loadingText: "加载中……",
      // 来源类型
      sourceType: undefined,
      // 来源ID
      sourceID: undefined,
      // 检核数据
      checkData: undefined,
      // 风险评估时间
      riskAssessDateTime: undefined,
      // 风险评估模板
      assessTemplate: [],
      // 风险级距
      riskRangeStr: undefined,
      // 风险级距详细
      riskRange: undefined,
      // 风险分数
      riskPoint: 0,
      // 评估时间
      assessTime: undefined,
      // 表单
      recordListID: undefined,
      // 顶部组件开关
      comSupplementFlag: {
        formSwitch: false,
        stationSwitch: false,
        departmentListSwitch: false,
        bedNumberSwitch: false,
        dateTimeSwitch: false,
      },
      stationDepartmentBedDate: {},
      // 选择的项目ID集合
      selectItemIDs: [],
      specialListTypeData: {},
      // 头部组件信息
      deptBedDataList: {},
      // 在风险某一组中没有任何勾选的时候提示方式
      processUnSelectedGroupFlag: undefined,
      // 根据 不同的风险表 判断是否使用倒序风险条样式
      showReverseOrderBarStyle: false,
      //判断当前医院是否使用倒序风险进度条呈现
      useReverseOrderRiskBar: false,
      /***********后面的以后要删除************/
      conponentData: {},
    };
  },
  mounted() {
    let param = {
      SettingTypeCode: "UseReverseOrderRiskBar",
    };
    GetSettingSwitchByTypeCode(param).then((response) => {
      if (this._common.isSuccess(response)) {
        this.useReverseOrderRiskBar = response.data;
      }
    });
  },
  methods: {
    /**
     * description: 初始化参数
     * param {*}
     * return {*}
     */
    initParams() {
      if (!this.params) {
        this.componentParams = this.defaultParams;
        return;
      }
      let componentParams = {};
      let keys = Object.keys(this.defaultParams);
      keys.forEach((key) => {
        componentParams[key] =
          this.params[key] == undefined || this.params[key] == null ? this.defaultParams[key] : this.params[key];
      });
      this.componentParams = componentParams;
      this.comSupplementFlag = {
        formSwitch: this.componentParams.supplement,
        stationSwitch: this.componentParams.supplement,
        departmentListSwitch: this.componentParams.supplement,
        bedNumberSwitch: this.componentParams.supplement,
        dateTimeSwitch: this.componentParams.showTime,
      };
      this.recordListID = this.componentParams.recordListID;
      this.stationDepartmentBedDate = this.componentParams.supplementParams;
      this.stationDepartmentBedDate.stationID =
        this.params?.assessStationID ?? this.componentParams.patientInfo.stationID;
      this.stationDepartmentBedDate.departmentListID =
        this.params?.assessDepartmentListID ?? this.componentParams.patientInfo.departmentListID;
      this.stationDepartmentBedDate.bedNumber =
        this.params?.assessBedNumber ?? this.componentParams.patientInfo.bedNumber;
      this.stationDepartmentBedDate.bedId = this.params?.assessBedID ?? this.componentParams.patientInfo.bedID;
      this.stationDepartmentBedDate.inpatientID = this.componentParams.patientInfo.inpatientID;
    },
    /**
     * description: 初始化组件
     * param {*}
     * return {*}
     */
    async init() {
      if (!this.componentParams.patientInfo) {
        return;
      }
      if (this.componentParams.sourceID || this.componentParams.sourceType == "RiskSupplement") {
        this.sourceType = this.componentParams.sourceType;
        this.sourceID = this.componentParams.sourceID;
      }
      let result = undefined;
      if (this.componentParams.showStyle == "Assess" && this.componentParams.recordsCode) {
        result = await this.getRiskAssessView();
      } else {
        result = await this.getRiskView();
      }
      if (!result) {
        return;
      }

      await this.GetRiskSaveCheckData(this.componentParams.sourceID);
      if (this.sourceType != "RiskSupplement" && !this.componentParams.sourceID && this.checkData) {
        this.sourceID = this.checkData.sourceID;
        if (this.checkData.assessNumber) {
          this.sourceType = this.checkData.assessNumber < 2 ? "AdmissionAssess" : "PhysicalAssessment";
        }
      }
      this.patientScoreMainID = result.patientScoreMainID;
      //护理评估后的首次风险评估,使用护理评估时间（1.根据配置 2.上一次的风险评估时间为null 3.最近一次护理评估时间>最近一次风险评估）
      if (
        this.componentParams.useAdmissionAssessDateFlag &&
        this.componentParams.lastAssessDateTime &&
        (!result.lastRiskAssessDateTime || this.componentParams.lastAssessDateTime > result.lastRiskAssessDateTime)
      ) {
        this.riskAssessDateTime = this._datetimeUtil.formatDate(
          this.componentParams.lastAssessDateTime,
          "yyyy-MM-dd hh:mm"
        );
      } else {
        this.riskAssessDateTime = this.componentParams.assessTime
          ? this._datetimeUtil.formatDate(this.componentParams.assessTime, "yyyy-MM-dd hh:mm")
          : this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      }
      if (this.stationDepartmentBedDate) {
        this.$set(
          this.stationDepartmentBedDate,
          "assessDate",
          this._datetimeUtil.formatDate(this.riskAssessDateTime, "yyyy-MM-dd")
        );
        this.$set(
          this.stationDepartmentBedDate,
          "assessTime",
          this._datetimeUtil.formatDate(this.riskAssessDateTime, "hh:mm")
        );
      }
    },
    /**
     * description: 获取风险保存需要检核判断的参数
     * param {*} sourceID
     * return {*}
     */
    async GetRiskSaveCheckData(sourceID = undefined) {
      let params = {
        inpatientID: this.componentParams.patientInfo.inpatientID,
        sourceID: sourceID,
      };
      await GetRiskSaveCheckTime(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.checkData = res.data;
        }
      });
    },
    /**
     * description: 获取风险评估模板
     * param {*} data
     * return {*}
     */
    async getRiskAssessView() {
      let params = {
        recordListID: this.componentParams.recordListID,
        recordsCode: this.componentParams.recordsCode,
        patientScoreMainID: this.componentParams.patientScoreMainID,
        inpatientID: this.componentParams.patientInfo.inpatientID,
        age: this.componentParams.patientInfo.age,
        gender: this.componentParams.patientInfo.genderCode,
        departmentListID: this.componentParams.patientInfo.departmentListID,
        stationID: this.componentParams.patientInfo.stationID,
        dateOfBirth: this.componentParams.patientInfo.dateOfBirth,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        sourceTable: "RecordsList",
      };
      this.loading = true;
      this.loadingText = "加载中……";
      let result = undefined;
      await GetRiskAssessView(params).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          result = response.data;
          this.assessTemplate = result.assessTemplate;
          this.riskRangeStr = result.scoreLimit;
          this.riskRange = this.getRiskRange(result.scoreLimitViews);
          this.setUseReverseOrderBarStyleOrNot(result.scoreLimitViews);

          this.deptBedDataList = this.stationDepartmentBedDate;
        }
      });
      return result;
    },
    /**
     * @description: 根据风险级距风险程度和风险级距排序，判断是否使用倒序风险条样式
     * @param scoreLimitViews 风险表中的风险级距数据
     * @return
     */
    setUseReverseOrderBarStyleOrNot(scoreLimitViews) {
      if (!scoreLimitViews || scoreLimitViews.length == 0) {
        this.showReverseOrderBarStyle = false;
        return;
      }
      for (let index = 0; index < scoreLimitViews.length; index++) {
        if (
          index + 1 < scoreLimitViews.length &&
          scoreLimitViews[index].riskLevel > scoreLimitViews[index + 1].riskLevel
        ) {
          this.showReverseOrderBarStyle = true;
          return;
        }
      }
    },
    //#region ******************临时代码，等风险全部切完即可删除************************
    /**
     * description: 获取风险模板
     * param {*} data
     * return {*}
     */
    async getRiskView() {
      const params = {
        recordListID: this.componentParams.recordListID,
        inpatientID: this.componentParams.patientInfo.inpatientID,
        stationID: this.componentParams.patientInfo.stationID,
        patientScoreMainID: this.componentParams.patientScoreMainID,
        sourceID: this.sourceID,
        sourceType: this.componentParams.sourceType,
      };
      this.loading = true;
      this.loadingText = "加载中……";
      let result = undefined;
      await GetSourceRiskRecord(params).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          result = response.data;
          this.conponentData = this.getUpdateData(result);
        }
      });
      return result;
    },
    /**
     * @description: 检查并获取风险表中没有被点选的组（风险模板：当全部替换掉后删除）
     * @return
     */
    checkUnSelectedGroupByRisk() {
      let tableDatas = this.conponentData.tableData;
      if (!tableDatas) {
        return;
      }
      for (let index = 0; index < tableDatas.length; index++) {
        const ele = tableDatas[index];
      }
      let unSelectedGroups = tableDatas.filter((item) => {
        return !item.haveCheck;
      });
      return unSelectedGroups.map((i) => i.content);
    },

    getUpdateData(data) {
      this.processUnSelectedGroupFlag = data.processUnSelectedGroup;
      //创建tableData
      let tableData = [];
      // 创建一个数组
      var childrenArray = [];
      //创建首层对象
      var obj = {
        content: "",
        recordsFormatID: -1,
        radioCheck: "",
        checkedList: [],
        haveCheck: false,
        description: "",
      };
      // 数据处理部分
      for (let i = 0; i < data.headerExtention.length; i++) {
        let item = data.headerExtention[i];
        const recordsFormatID = item[1].recordsFormatID + "";
        // 判断是否同属于一栏
        if (item[0].content == obj.content) {
          // 同一组
          // 创建对象:
          let lineItem = this.creatItem(item[1], data.rows[0][recordsFormatID]);
          if (lineItem.checkFlag) {
            if (lineItem.child.controlerType == "R") {
              obj.radioCheck = lineItem.child.recordsFormatID;
            }
            if (lineItem.child.controlerType == "C") {
              obj.checkedList.push(lineItem.child.recordsFormatID);
            }
            obj.haveCheck = true;
          }

          childrenArray.push(this._common.clone(lineItem.child));
          if (i == data.headerExtention.length - 1) {
            let children = this._common.clone(childrenArray);
            obj.childrenArray = children;
            let objItem = this._common.clone(obj);
            tableData.push(objItem);
          }
          continue;
        } else {
          // 不相等
          let children = this._common.clone(childrenArray);
          obj.childrenArray = children;
          let objItem = this._common.clone(obj);
          tableData.push(objItem);
          childrenArray = [];
          obj.radioCheck = "";
          obj.checkedList = [];
          let lineItem = this.creatItem(item[1], data.rows[0][recordsFormatID]);
          if (lineItem.checkFlag) {
            if (lineItem.child.controlerType == "R") {
              obj.radioCheck = lineItem.child.recordsFormatID;
            }
            if (lineItem.child.controlerType == "C") {
              obj.checkedList.push(lineItem.child.recordsFormatID);
            }
            obj.haveCheck = true;
          }
          childrenArray.push(this._common.clone(lineItem.child));
          obj.content = item[0].content;
          obj.description = item[0].description;
          obj.showMessage = item[0].showMessage;
          obj.recordsFormatID = item[0].recordsFormatID;
          //最后一位存入数组
          if (i == data.headerExtention.length - 1) {
            let children = this._common.clone(childrenArray);
            obj.childrenArray = children;
            let objItem = this._common.clone(obj);
            tableData.push(objItem);
          }
        }
      }
      //清除首个无用元素
      tableData.shift();
      let riskRangeStr = data.scoreLimit;
      let riskRange = this.getRiskRange(data.scoreLimitViews);
      this.setUseReverseOrderBarStyleOrNot(data.scoreLimitViews);
      return {
        tableData: tableData,
        riskRangeStr: riskRangeStr,
        riskRange: riskRange,
        reverseOrderFlag: this.useReverseOrderRiskBar && this.showReverseOrderBarStyle,
      };
    },
    // 创建项目
    creatItem(item, row) {
      let recordsFormatID = item.recordsFormatID;
      let flag = row[recordsFormatID] == "V" ? true : false;
      let childItem = {
        assessListID: item.assessListID,
        point: item.point,
        recordsFormatID: recordsFormatID,
        content: item.content,
        controlerType: row.controlerType,
        source: row.source,
        sourceContent: row.content,
        showMessage: item.showMessage,
        description: item.description,
      };
      return { child: childItem, checkFlag: flag };
    },
    //#endregion ****************************需要删除结束**************************************/

    /**
     * description: 获取风险级距
     * param {*} data
     * return {*}
     */
    getRiskRange(data) {
      let riskRange = data;
      //长度检核
      if (!riskRange || !riskRange.length) {
        return [];
      }
      let unit = 1;
      let length = riskRange.length - 1;
      if (riskRange[length].scoreUpperLimit > 99) {
        unit = 10;
      } else if (riskRange[length].scoreUpperLimit > 30) {
        unit = 5;
      } else if (riskRange[length].scoreUpperLimit > 20) {
        unit = 2;
      }
      let barArray = [];
      let hasSetHeadScore = false;
      for (let i = 0; i < riskRange.length; i++) {
        let rangeArray = [];
        for (let j = riskRange[i].scoreLowerLimit; j <= riskRange[i].scoreUpperLimit; j++) {
          if (j == riskRange[i].scoreLowerLimit) {
            // 调整风险bar的开头放最小风险值，其他每个级距开头新增两个值比如 0-3，4-6，7-21会产生三个barArray [0，2，3],[3,4,6],[6,7,8,10,12,14,16,18,20,21]
            if (!hasSetHeadScore) {
              rangeArray.push(j);
              hasSetHeadScore = true;
            } else {
              rangeArray.push(j - 1);
              rangeArray.push(j);
            }
            continue;
          }
          if (j % unit == 0) {
            rangeArray.push(j);
            continue;
          }
          if (j == riskRange[i].scoreUpperLimit) {
            rangeArray.push(j);
          }
        }

        let item = {
          name: riskRange[i].rangeContent,
          color: riskRange[i].showColor,
          values: rangeArray,
        };
        barArray.push(item);
      }
      return barArray;
    },
    // 评估模板返回值集合
    changeValues(values) {
      this.selectItemIDs = [];
      this.specialListTypeData = {};
      this.riskPoint = 0;
      let pointStr = "0";
      if (values && values.length) {
        let hasNonNumeric = !!values.find(
          (value) => value.specialListType == "RecordsFormat" && value.linkForm.length && isNaN(value.linkForm)
        );
        if (!hasNonNumeric) {
          values.forEach((value) => {
            this.selectItemIDs.push(value.assessListID);
            if (value.specialListType == "RecordsFormat") {
              value.linkForm.length && (pointStr += `+${value.linkForm}`);
            } else {
              this.specialListTypeData[value.assessListID] = value.specialListType;
            }
          });
          if (pointStr !== "0") {
            this.riskPoint = eval(pointStr);
          }
        } else {
          //TODO: 有非数字选项（格拉斯哥），需要每组分开计算，等所有风险切换时再写算法 --苏军志  2023-03-04
        }
      }
    },
    getChecked(values, assessTime) {
      this.assessTime = assessTime;
      this.selectItemIDs = values;
    },
    getEptBedData(datas) {
      this.deptBedDataList = datas;
    },
    /**
     * description: 风险保存检核
     * param {*}
     * return {*}
     */
    saveCheck() {
      if (!this.assessTime) {
        this._showTip("warning", "请选择评估时间!");
        return false;
      }
      let checkTimeFlag = true;
      if (this.componentParams.supplement) {
        checkTimeFlag = false;
      }
      let isNextAssessFlag = true;
      if (
        this.checkData &&
        (this.sourceType == "PhysicalAssessment" || this.sourceType == "AdmissionAssess") &&
        checkTimeFlag
      ) {
        if (!this.checkData.endDateTime) {
          this.checkData.endDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
          isNextAssessFlag = false;
        }
        this.checkData.satrtDateTime = this._datetimeUtil.formatDate(this.checkData.satrtDateTime, "yyyy-MM-dd hh:mm");
        this.checkData.endDateTime = this._datetimeUtil.formatDate(this.checkData.endDateTime, "yyyy-MM-dd hh:mm");
        if (this.assessTime < this.checkData.satrtDateTime && this.componentParams.timeCheckFlag) {
          this._showTip("warning", "时间不能早于护理评估时间！");
          return false;
        }
        if (this.assessTime > this.checkData.endDateTime) {
          this._showTip(
            "warning",
            isNextAssessFlag ? "时间不得晚于该病人护理评估时间:" + this.checkData.endDateTime : "时间不得晚于当前时间！"
          );
          return false;
        }
      } else {
        if (this.componentParams.patientInfo.admissionDateTimeView) {
          let admissionDateTime = this._datetimeUtil.formatDate(
            this.componentParams.patientInfo.admissionDateTimeView,
            "yyyy-MM-dd hh:mm"
          );
          if (this.assessTime < admissionDateTime || this.assessTime > this._datetimeUtil.getNow("yyyy-MM-dd hh:mm")) {
            this._showTip("warning", "保存时间不得小于病人入院时间且不能大于当前时间!");
            return false;
          }
        }
      }
      return true;
    },
    /**
     * description: 组件最终返回方法
     * param {*} returnFlag 返回参数，false保存失败，true保存成功
     * return {*}
     */
    async result(returnFlag) {
      if (!returnFlag) {
        this.$emit("result", false);
        return;
      }
      if (this.processUnSelectedGroupFlag && !(await this.checkUnSelectedGroup())) {
        return;
      }
      // 判断是否走提交数据库标记
      if (!this.componentParams.submitFlag) {
        this.$emit("result", true, this.selectItemIDs);
        return;
      }
      if (this.loading) {
        return;
      }
      if (!this.componentParams.patientInfo || !this.componentParams.patientInfo.inpatientID) {
        this._showTip("warning", "获取病人信息失败!");
        return;
      }
      if (
        this.componentParams.showStyle == "Assess" &&
        this.$refs.tabsLayout &&
        !this.$refs.tabsLayout.checkRequire()
      ) {
        return;
      }
      this.assessTime = this.assessTime || this.riskAssessDateTime;
      // 处理保存逻辑
      if (!this.saveCheck()) {
        return;
      }
      let params = {
        PatientScoreMainID: this.patientScoreMainID,
        RecordListID: this.componentParams.recordListID,
        RecordsFormatID: this.selectItemIDs,
        specialListTypeData: this.specialListTypeData,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        inpatientID: this.componentParams.patientInfo.inpatientID,
        stationID: this.deptBedDataList.stationID
          ? this.deptBedDataList.stationID
          : this.componentParams.patientInfo.stationID,
        departmentListID: this.deptBedDataList.departmentListID
          ? this.deptBedDataList.departmentListID
          : this.componentParams.patientInfo.departmentListID,
        bedNumber: this.deptBedDataList.bedNumber
          ? this.deptBedDataList.bedNumber
          : this.componentParams.patientInfo.bedNumber,
        bedID: this.deptBedDataList.bedId ? this.deptBedDataList.bedId : this.componentParams.patientInfo.bedID,
        ModifyRiskTime: this.assessTime,
        ScheduleDate: this.deptBedDataList.assessDate || this._datetimeUtil.getNowDate("yyyy-MM-dd"),
        ScheduleTime: this.deptBedDataList.assessTime || this._datetimeUtil.getNowTime("hh:mm"),
        SameGroupRiskDueFlag: true,
      };
      this.loadingText = "保存中……";
      this.loading = true;
      SavePatientScoreMainAndDetail(params).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          let resultData = response.data;
          resultData.assessTime = this.assessTime;
          this.$emit("result", true, resultData);
          this._showTip("success", "保存成功");
        }
      });
    },
    /**
     * @description: 检查是否存在未勾选的组（评估模板组件）
     * @return
     */
    checkUnSelectedGroupByAssess() {
      if (!this.assessTemplate && this.assessTemplate.length <= 0) {
        return;
      }
      let unSelectedGroups = [];
      let groups = this.assessTemplate[0].groups;
      for (let index = 0; index < groups.length; index++) {
        const ele = groups[index];
        let selected = ele.contents?.some((item) => this.selectItemIDs.some((t) => t == item.assessListID));
        selected || unSelectedGroups.push(ele.itemName);
      }
      return unSelectedGroups;
    },
    /**
     * @description: 检查风险评估中没有任何点选的组的相关信息
     * @return 没有任何点选的组
     */
    async checkUnSelectedGroup() {
      let unCheckGroups =
        this.componentParams.showStyle == "Assess"
          ? this.checkUnSelectedGroupByAssess()
          : this.checkUnSelectedGroupByRisk();
      if (!unCheckGroups || unCheckGroups.length <= 0) {
        return true;
      }
      // 拼接提示信息
      let message = `<strong>${unCheckGroups.reduce(
        (preValue, currentValue) => preValue + (preValue ? "、" : "") + currentValue
      )}</strong> 组未勾选任何项目`;
      switch (this.processUnSelectedGroupFlag) {
        case "ShowWarning":
          // 使用Promise包裹实现同步访问
          return await new Promise((resolve) => {
            this._showTip("warning", message);
          });
        case "Confirm":
          return await new Promise((resolve) => {
            this._confirm(message, "确认继续", (flag) => resolve(flag));
          });
      }
    },
  },
};
</script>

<style lang="scss">
.risk-component {
  height: 100%;
  .header-wrap {
    .supplement-wrap {
      .record-name {
        width: 160px;
      }
    }
    .total-point-wrap {
      border: 1px solid #ebeef5;
      border-radius: 3px;
      padding-left: 10px;
      line-height: 18px;
      .total-range {
        margin-top: 3px;
        display: inline-block;
        max-width: 60%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .total-label {
        margin-left: 10px;
        font-size: 16px;
        font-weight: bold;
      }
      .total {
        font-size: 20px;
        color: red;
      }
    }
  }
  .assess-risk-layout {
    height: calc(100% - 5px);
    &.show-bar {
      height: calc(100% - 95px);
    }
  }
  .component-footer {
    text-align: right;
  }
}
// 该样式放在了最外边，应为嵌套el-dialog，样式写在里边不生效，footer超出弹窗，审核后有好的方案调整
.tip-dialog {
  .el-dialog {
    height: auto;
    .el-dialog__footer {
      height: 40px;
    }
  }
}
</style>