<!--
  床位组件说明:引用该组件的组件是父组件，该组件为子组件
字段说明：

isDel：是否是删除用
  true：表示该组件是删除用的组件
  false:表示该组件不是删除用组件

bedNumber：该床位的床位号

bedID：该床位的id

isSel:是否选中
  true：表示被选中
  false：表示未被选中

  showDelete: 是否显示删除
  true:显示
  false:不显示

方法：operate：
  点击该方法会将值传递给父组件，
  getBedIDForDel：表示给父组件删除用的数据（床位的id）
  getBedNumberAndBedIDForSel：表示给父组件数据（床位号，床位id，床位是否被选中）
-->

<template>
  <li ref="bedli" :class="['bed-item', type]" @click="operate('U')">
    {{ tempBed.bedNumber }}
    <div v-if="(tempBed.isDel || tempBed.isSel) && this.showDelete" class="icon-back"></div>
    <!-- :class="['iconfont', { 'icon-del-select-mark': tempBed.isDel }, { 'bed-icon-select-mark': tempBed.isSel }]" -->
    <i
      :class="{
        iconfont: true,
        'icon-del-select-mark': tempBed.isDel && this.showDelete,
        'icon-select-mark': tempBed.isSel,
        'bed-icon-select-mark': tempBed.isSel && type == 'jobBed' && this.showDelete,
        'people-icon-select-mark': tempBed.isSel && type == 'people',
      }"
      @click="operate('D')"
    ></i>
  </li>
</template>

<script>
import { getBedNumberWidth } from "@/utils/setting";
import common from "@/utils/common";
export default {
  data() {
    return {
      tempBed: {},
      showDelete: true,
    };
  },
  props: {
    bed: {
      type: Object,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
  },
  watch: {
    bed: {
      immediate: true,
      handler(newVal, oldVal) {
        this.tempBed = common.clone(newVal);
      },
    },
  },
  mounted() {
    this.$refs.bedli.style.width = getBedNumberWidth() + "px";
    this.showDelete = this.tempBed.showDelete == false ? false : true;
  },
  methods: {
    operate(status) {
      if (status == "D") {
        this.$emit("getBedIDForDel", this.tempBed);
      } else if (status == "U" && this.showDelete) {
        this.$emit("getBedNumberAndBedIDForSel");
      }
    },
  },
};
</script>
<style lang="scss" >
.bed-item {
  position: relative;
  list-style: none;
  float: left;
  height: 37px;
  line-height: 37px;
  margin: 3px 3px;
  position: relative;
  color: #333;
  border-radius: 5px;
  text-align: center;
  .iconfont {
    position: absolute;
    right: -3px;
    bottom: 8px;
    font-size: 24px;
    height: 20px;
  }
  .icon-back {
    position: absolute;
    right: 2px;
    bottom: 2px;
    width: 8px;
    height: 8px;
    background-color: #fff;
  }
  .icon-back:before {
    content: none;
  }
  .icon-del-select-mark {
    color: #ff0000;
  }
  .bed-icon-select-mark {
    color: #ff7400 !important; //#3d85da #f2b352
  }
  .people-icon-select-mark {
    color: $base-color !important;
  }
}
.jobBed {
  background-color: #cff4cb;
}
.people {
  color: #fff;
  background-color: #f2b352;
}
</style>

