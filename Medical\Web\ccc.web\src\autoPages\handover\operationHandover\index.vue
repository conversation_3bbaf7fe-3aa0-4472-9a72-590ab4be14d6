<!--
 * FilePath     : \src\autoPages\handover\operationHandover\index.vue
 * Author       : 郭鹏超
 * Date         : 2023-03-24 09:26
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-07-16 20:07
 * Description  : 手术交接
 * CodeIterationRecord:
-->
<template>
  <specific-care
    class="operation-handover"
    v-model="showTemplateFlag"
    drawerSize="80%"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :recordTitleSlotFalg="true"
    :previewFlag="saveButtonShowFlag || handoverData.handonFlag"
    :recordAddFlag="false"
    @mainAdd="handoverAdd"
    @save="operationHandoverSave"
    @cancel="drawerClose"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <div slot="main-record">
      <el-table
        ref="operationHandoverTable"
        :span-method="arraySpanMethod"
        :data="handoverRecordList"
        height="100%"
        border
        stripe
      >
        <el-table-column prop="scheduleDateTime" label="预约日期" :width="convertPX(170)" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.scheduleDateTime, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="operateName" label="手术名称" :min-width="convertPX(10)" align="left"></el-table-column>
        <el-table-column
          prop="anesthesiaMethod"
          label="麻醉方式"
          :width="convertPX(140)"
          align="center"
        ></el-table-column>
        <el-table-column prop="recordsName" label="交班类型" :width="convertPX(240)" align="left"></el-table-column>
        <el-table-column prop="handoverClassName" label="交接班" :width="convertPX(200)" align="left"></el-table-column>
        <el-table-column prop="handoverDate" label="日期" :width="convertPX(260)" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.handoverDate, type: 'date' }"></span>
            <span v-formatTime="{ value: scope.row.handoverTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="handoverNurseName"
          label="交接护士"
          :width="convertPX(120)"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" :width="convertPX(100)" align="center">
          <template slot-scope="scope" v-if="GetModifyFlag(scope.row)">
            <el-tooltip content="修改">
              <div class="iconfont icon-edit" @click="handoverAdd(scope.row)"></div>
            </el-tooltip>
            <el-tooltip content="删除" :class="{ visibility: !scope.row.handoverDate }">
              <div class="iconfont icon-del" @click="handoverOperationDelete(scope.row)"></div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="新增交接" align="center" :width="convertPX(80)">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.handoverType == 'PreOPHandover'" content="新增交接">
              <i class="iconfont icon-handoff" @click="addNewHandover(scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading.fullscreen.lock="layoutLoading"
      :element-loading-text="layoutLoadingText"
    >
      <div slot="header">
        <span>日期:</span>
        <el-date-picker
          v-model="handoverDate"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          class="date-picker"
          :clearable="false"
        ></el-date-picker>
        <span>时间:</span>
        <el-time-picker
          v-model="handoverTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          class="time-picker"
        ></el-time-picker>
        <template v-if="!!refillFlag">
          <station-selector v-model="handoverStationID" :inpatientID="patient.inpatientID" />
          <dept-selector v-model="handoverDepartmentID" :stationID="handoverStationID" />
          <user-selector
            v-model="handoverNurseID"
            :stationID="handoverStationID"
            label="交接护士"
            clearable
            filterable
            remoteSearch
            width="120px"
          />
        </template>
        <span>类型:</span>
        <el-select
          :disabled="!handoverIDArr[0]"
          v-model="recordsCode"
          class="handover-type-select"
          placeholder="请选择"
          @change="recordsCodeChange"
        >
          <el-option
            v-for="item in handoverTypeArr"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="openClinic" v-if="this.showVitalflag == true">生命体征</el-button>
      </div>
      <div class="drawer-content">
        <el-tabs class="handover-tabs" v-model="paneKey" @tab-click="paneChange($event.index)">
          <el-tab-pane
            v-for="(item, index) in handoverPaneList"
            :key="index"
            :label="item.label"
            :name="item.value"
            class="tab-pane"
          ></el-tab-pane>
        </el-tabs>
        <div class="pane-content">
          <tabs-layout
            v-if="paneKey == 'Assessment'"
            ref="tabsLayout"
            :template-list="templateDatas"
            :custom-methods="handleTransfusionData"
            :disabled="false"
            @button-click="buttonClick"
            @change-values="changeValues"
            @button-record-click="buttonRecordClick"
            @checkTN="checkTN"
            :checkFlag="true"
            :gender="patientInfo ? patientInfo.genderCode : ''"
          />
          <handover-rich-text ref="handover" v-if="paneKey == 'Handover'" v-model="sbarData" />
          <patient-evaluation
            v-if="paneKey == 'Evaluate'"
            ref="evaluate"
            :inpatientid="patient ? patient.inpatientID : ''"
            :stationid="getEvaluationStationID()"
            :showcommit="false"
            :all-Flag="false"
            :assessDate="handoverDate"
            :assessTime="handoverTime"
          ></patient-evaluation>
        </div>
      </div>
      <el-drawer
        title="生命体征"
        :modal-append-to-body="false"
        :visible.sync="showClinicFlag"
        :destroy-on-close="true"
        direction="btt"
        size="60%"
        custom-class="clinic-drawer"
        :wrapperClosable="false"
      >
        <el-table
          class="clinic-table"
          border
          stripe
          :data="clinicTable"
          highlight-current-row
          @current-change="handleCurrentChange"
        >
          <el-table-column prop="perfromTime" label="时间" align="center">
            <template slot-scope="scope">{{ scope.row.PerfromTime ? scope.row.PerfromTime.value : "" }}</template>
          </el-table-column>
          <el-table-column prop="temperature" label="体温" align="center">
            <template slot-scope="scope">{{ scope.row.Temperature ? scope.row.Temperature.value : "" }}</template>
          </el-table-column>
          <el-table-column prop="pulseRate" label="脉率" align="center">
            <template slot-scope="scope">{{ scope.row.PulseRate ? scope.row.PulseRate.value : "" }}</template>
          </el-table-column>
          <el-table-column prop="heartRate" label="心率" align="center">
            <template slot-scope="scope">{{ scope.row.HeartRate ? scope.row.HeartRate.value : "" }}</template>
          </el-table-column>
          <el-table-column prop="breathe" label="呼吸" align="center">
            <template slot-scope="scope">{{ scope.row.Breathe ? scope.row.Breathe.value : "" }}</template>
          </el-table-column>
          <el-table-column prop="systolic" label="收缩压" align="center">
            <template slot-scope="scope">{{ scope.row.Systolic ? scope.row.Systolic.value : "" }}</template>
          </el-table-column>
          <el-table-column prop="diastolic" label="舒张压" align="center">
            <template slot-scope="scope">{{ scope.row.Diastolic ? scope.row.Diastolic.value : "" }}</template>
          </el-table-column>
          <el-table-column prop="oxyhemoglobin" label="血氧饱和度" align="center">
            <template slot-scope="scope">{{ scope.row.Oxyhemoglobin ? scope.row.Oxyhemoglobin.value : "" }}</template>
          </el-table-column>
        </el-table>
      </el-drawer>
    </base-layout>
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      fullscreen
      custom-class="no-footer"
      slot="drawer-dialog"
      @close="updateButton"
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonRecordTitle"
      :visible.sync="showButtonRecordDialog"
      slot="drawer-dialog"
      custom-class="no-footer"
    >
      <risk-component
        :saveButtonShowFlag="handoverClass == 'HandOff'"
        :params="componentParams"
        @result="getBrResult"
      ></risk-component>
    </el-dialog>
  </specific-care>
</template>

<script>
import { GetButtonData } from "@/api/Assess";
import { GetHandOverType, GetHandoverPaneList } from "@/api/Handover/HandoverCommonUse";
import {
  DeleteHandoverOperation,
  GetHandOverOperationAssessTemplate,
  GetHandoverOperationList,
  GetHandoverOperationSBAR,
  SaveOperationHandoverAssess,
  SaveOperationHandoverSBAR,
} from "@/api/Handover/HandoverOperation";
import { GetSettingSwitchByTypeCode, GetSettingValueByTypeCodeAsync } from "@/api/SettingDescription";
import { GetVitalSignByInpatientID } from "@/api/operation";
import handoverRichText from "@/autoPages/handover/components/HandoverRichText";
import baseLayout from "@/components/BaseLayout";
import deptSelector from "@/components/selector/deptSelector";
import stationSelector from "@/components/selector/stationSelector";
import userSelector from "@/components/selector/userSelector";
import specificCare from "@/components/specificCare";
import tabsLayout from "@/components/tabsLayout/index";
import patientEvaluation from "@/pages/nursingEvaluation/patientEvaluation";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import { mapGetters } from "vuex";

export default {
  components: {
    specificCare,
    baseLayout,
    tabsLayout,
    handoverRichText,
    patientEvaluation,
    riskComponent,
    stationSelector,
    deptSelector,
    userSelector,
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      user: "getUser",
    }),
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    handoverData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      patient: undefined,
      showTemplateFlag: false,
      saveFlag: false,
      drawerTitle: "手术转运",
      showRecordArr: [true, false],
      //弹窗底部保存按钮是否显示
      saveButtonShowFlag: false,
      loading: false,
      searchHandoverType: undefined,
      handoverDate: undefined,
      handoverTime: undefined,
      handoverStationID: undefined,
      handoverDepartmentID: undefined,
      handoverType: "WardHandover",
      handoverClass: "HandOff",
      recordsCode: undefined,
      handoverTypeArr: [],
      //术前
      handoverTypeArrPreOPHandover: [],
      //术后
      handoverTypeArrPostOPHandover: [],
      handoverRecordList: [],
      layoutLoading: false,
      layoutLoadingText: undefined,
      handoverPaneList: [],
      paneKey: undefined,
      paneIndex: undefined,
      templateDatas: [],
      checkTNFlag: true,
      showButtonDialog: false,
      buttonName: undefined,
      sbarData: {},
      //[0]为true 表示新增 false表示修改
      handoverIDArr: [true, this._common.guid()],
      currentHandover: undefined,
      assessValues: [],
      //BR
      componentParams: undefined,
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR项
      brItem: undefined,
      //表格合并变量
      spanArr: [],
      spanArrOne: [],
      refillFlag: undefined,
      copyHandoverPaneList: [],
      hisFlag: false,
      hisNurseID: undefined,
      //是否为新增交接
      addFlag: false,
      clinicTable: [],
      showClinicFlag: false,
      showVitalflag: false,
      handoverNurseID: undefined,
    };
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = undefined;
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.refillFlag = "*";
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        newVal && this.init();
      },
      immediate: true,
    },
  },
  methods: {
    async init() {
      //获取页面配置
      await this.getPageSetting();
      await this.getHandoverRecordList();
      this.pageAuto();
    },
    /**
     * description: 自动弹窗处理
     * return {*}
     */
    pageAuto() {
      //补录不自动化
      if (this.refillFlag == "*") {
        return;
      }
      //无记录自动弹窗
      if (!this.handoverRecordList?.length) {
        return;
      }
      this.hisFlag = this.$route.query.hisFlag;
      this.hisNurseID = this.$route.query.nurseID;
      //有跳转ID 直接找到弹窗
      let routeHandoverID = this.$route.query?.handoverID ?? this.handoverData?.handoverID ?? undefined;
      if (!routeHandoverID) {
        return;
      }
      let handoverClass = this.$route.query?.handoverClass ?? this.handoverData?.handoverClass ?? undefined;
      let sucHandover = this.handoverRecordList.find(
        (handover) =>
          handover.handoverID == routeHandoverID && (!handoverClass || handover.handoverClass == handoverClass)
      );
      sucHandover && this.handoverAdd(sucHandover);
    },
    /**
     * description: 获取手术列表
     * return {*}
     */
    async getHandoverRecordList() {
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      this.loading = true;
      await GetHandoverOperationList(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverRecordList = res.data;
          this.getSpanArr(res.data);
          this.$nextTick(() => {
            this.$refs.operationHandoverTable.doLayout();
          });
        }
      });
    },
    /**
     * description: 记录新增修改
     * return {*}
     */
    async handoverAdd(item) {
      this.saveFlag = false;
      this.currentHandover = item ?? undefined;
      this.handoverDate = item?.handoverDate ?? this._datetimeUtil.getNowDate();
      this.addFlag = !item?.handoverDate;
      this.handoverTime = item?.handoverTime ?? this._datetimeUtil.getNowTime("hh:mm");
      this.handoverStationID = item?.stationID ?? this.user.stationID;
      this.handoverNurseID = item?.handoverNurse;
      this.handoverDepartmentID = item?.departmentListID ?? this.patient.departmentListID;
      this.recordsCode = item?.recordsCode ?? this.searchHandoverType;
      this.handoverIDArr = [!item?.handoverID, item?.handoverID ?? this._common.guid()];
      this.handoverClass = item?.handoverClass ?? "HandOff";
      this.drawerTitle = "手术转运-" + (this.handoverClass == "HandOff" ? "交班" : "接班");
      //获取保存按钮是否显示
      this.saveButtonShowFlag = this.refillFlag ? false : await this.getSaveButtonShowFlag(item);
      if (item && this.refillFlag && this.refillFlag === "*") {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,item.handoverNurse)
        this.saveButtonShowFlag = saveButtonFlag;
      }
      this.handoverTypeArr = this["handoverTypeArr" + item?.handoverType];
      this.handoverType = this.handoverTypeArr?.length ? this.handoverTypeArr[0].value : "";
      if (this.handoverType == "PreOPHandover") {
        this.showVitalflag = true;
      }
      //页签初始化
      this.recordsCodeChange();
      this.openOrCloseDrawer(true, this.drawerTitle);
    },
    /**
     * description: 弹窗关闭 没有保存记录无需重新拉去数据
     * return {*}
     */
    drawerClose() {
      this.openOrCloseDrawer(false);
      this.saveFlag && this.getHandoverRecordList();
    },
    async recordsCodeChange() {
      await this.getHandoverPaneList();
      this.paneChange(0);
    },
    /**
     * description: 页签切换初始化
     * param {*} index
     * return {*}
     */
    async paneChange(index) {
      if (index >= this.handoverPaneList.length) {
        this.openOrCloseDrawer(false);
        return;
      }
      this.paneIndex = index;
      this.paneKey = this.handoverPaneList[index]?.value;
      //评价无需初始化 或者 初始化方法没有直接返回
      if (this.paneKey == "Evaluate" || !this.handoverPaneList[index]?.initMethod) {
        return;
      }
      this.layoutLoading = true;
      this.layoutLoadingText = "加载中……";
      await this.handoverPaneList[index]?.initMethod();
      this.layoutLoading = false;
    },
    /**
     * description: 评估内容初始化
     * return {*}
     */
    async initAssessment() {
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCode,
        sourceType: this.handoverType,
        operationHandoverID: this.handoverIDArr[1],
        handoverClass: this.handoverClass,
        patientOperationID: this.currentHandover.patientOperationID,
      };
      //复制出来交班并且还未交班保存 传递copyhandoverID 初始为复制的交班记录
      this.handoverIDArr[0] &&
        this.currentHandover?.copyHandoverID &&
        (params.copyOperationHandoverID = this.currentHandover.copyHandoverID);
      this.templateDatas = [];
      await GetHandOverOperationAssessTemplate(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.templateDatas = res.data;
          if (this.templateDatas.length == 0) {
            this.handoverPaneList.splice(
              this.handoverPaneList.findIndex((pane) => pane.value == "Assessment"),
              1
            );
            this.paneChange(0);
          }
        }
      });
    },
    /**
     * description: SBAR初始化
     * return {*}
     */
    async initHandover() {
      let params = {
        handoverID: this.handoverIDArr[1],
        handoverClass: this.handoverClass,
        InpatientID: this.patient.inpatientID,
        StationID: this.handoverStationID,
        RecordsCode: this.recordsCode,
        HandoverDate: this.handoverDate,
        HandoverTime: this.handoverTime,
        HandoverNurse: this.user.userID,
        patientOperationID: this.currentHandover.patientOperationID,
      };
      //复制出来交班并且还未交班保存 传递copyhandoverID 初始为复制的交班记录
      this.handoverIDArr[0] &&
        this.currentHandover?.copyHandoverID &&
        (params.copyOperationHandoverID = this.currentHandover.copyHandoverID);
      await GetHandoverOperationSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.sbarData = res.data?.sbarData ?? {};
        }
      });
    },
    /**
     * description: 手术保存
     * return {*}
     */
    async operationHandoverSave() {
      this.layoutLoading = true;
      this.layoutLoadingText = "保存中……";
      await this.handoverPaneList[this.paneIndex].saveMethod();
      this.layoutLoading = false;
    },
    /**
     * description: 评估内容保存
     * return {*}
     */
    async saveAssessment() {
      if (!this.checkTNFlag || this.checkHandoverDateTime()) {
        this.checkTNFlag = true;
        return;
      }
      let params = this.getSBARSaveView();
      if (!params.handoverCommonSaveView?.assessContentList?.length) {
        return;
      }
      if (!params.handoverCommonSaveView.handoverNurse) {
        this._showTip("warning", "请选择交接护士");
        return;
      }
      await SaveOperationHandoverAssess(params).then((res) => {
        if (this._common.isSuccess(res)) {
          res?.data && (this.handoverIDArr = [false, res?.data]);
          this._showTip("success", "保存成功！");
          this.paneChange(Number(this.paneIndex) + 1);
          this.addFlag = false;
          //保存后重新刷新交班表格数据 没有保存 不需要刷新
          this.saveFlag = true;
        }
      });
    },
    /**
     * description: SBAR内容保存
     * return {*}
     */
    async saveHandover() {
      if (this.checkHandoverDateTime()) {
        return;
      }
      let params = this.getSBARSaveView();
      await SaveOperationHandoverSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          res?.data && (this.handoverIDArr = [false, res?.data]);
          this._showTip("success", "保存成功！");
          this.paneChange(Number(this.paneIndex) + 1);
          this.addFlag = false;
          //保存后重新刷新交班表格数据 没有保存 不需要刷新
          this.saveFlag = true;
        }
      });
    },
    /**
     * description: 评价保存
     * return {*}
     */
    saveEvaluate() {
      this.$refs?.evaluate?.commit();
      if (!this.$refs?.evaluate) {
        return;
      }
      this.paneChange(Number(this.paneIndex) + 1);
      this.getHandoverRecordList();
    },
    /**
     * description: 组装保存参数
     * return {*}
     */
    getSBARSaveView() {
      let view = {
        handoverID: this.handoverIDArr[1],
        handoverClass: this.handoverClass,
        handoverType: this.handoverType,
        recordsCode: this.recordsCode,
        handoverDate: this.handoverDate,
        handoverTime: this.handoverTime,
        handoverNurse: !this.handoverNurseID ? this.user.userID : this.handoverNurseID,
      };

      // 宏力手麻跳转参数中护士工号
      if (this.hisFlag && this.hisNurseID) {
        view.handoverNurse = this.hisNurseID;
      }

      Object.assign(view, this.patient);
      view.stationID = this.handoverStationID;
      view.departmentListID = this.handoverDepartmentID;
      //评估获取明细
      if (this.paneKey == "Assessment") {
        view.assessContentList = this.getAssessContentValue();
      }
      //SBAR获取组件内容
      if (this.paneKey == "Handover") {
        view.situation = this.sbarData?.situation?.value;
        view.background = this.sbarData?.background?.value;
        view.assement = this.sbarData?.assement?.value;
        view.recommendation = this.sbarData?.recommendation?.value;
        view.bodyPartImage = this.sbarData?.bodyPartImage?.value;
      }
      view.refillFlag = this.refillFlag;
      let allView = {
        handoverCommonSaveView: view,
        patientOperationID: this.currentHandover.patientOperationID,
        hISOperationNo: this.currentHandover.hisOperationNo,
        addFlag: this.addFlag,
      };
      return allView;
    },
    /**
     * description: 组装保存模板明细
     * return {*}
     */
    getAssessContentValue() {
      if (!this.assessValues?.length) {
        return [];
      }
      //检核必选项
      if (this.$refs?.tabsLayout && !this.$refs?.tabsLayout.checkRequire()) {
        return undefined;
      }
      let details = [];
      let flag = true;
      for (let i = 0; i < this.assessValues.length; i++) {
        let content = this.assessValues[i];
        // 按钮不处理
        if (content.controlerType.trim() == "B") {
          continue;
        }
        //检核TN内容
        let result = this._common.checkAssessTN(content);
        if (!result.flag) {
          flag = false;
          break;
        }
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          controlerType: content.controlerType,
        };
        if (["C", "R"].includes(content.controlerType.trim())) {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        details.push(detail);
      }
      if (!flag) {
        return [];
      }
      return details;
    },
    /**
     * description: 交接班删除
     * param {*} item
     * return {*}
     */
    async handoverOperationDelete(item) {
      if (item && this.refillFlag && this.refillFlag === "*") {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,item.handoverNurse)
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this.currentHandover = item;
      this.handoverIDArr = [!item, item?.handoverID];
      this.handoverClass = item?.handoverClass;
      let params = {
        handoverID: this.handoverIDArr[1],
        handoverClass: this.handoverClass,
      };
      let message = "";
      if (item?.handoverClass == "HandOff") {
        let handOnItem = this.handoverRecordList.find(
          (m) => m.handoverID == item.handoverID && m.handoverClass == "HandOn" && !!m.handoverDate
        );
        message = handOnItem ? "该转手术运记录已接班,删除交班记录接班记录也会一并删除, 确定要删除么?" : "";
      }
      this._deleteConfirm(message, (flag) => {
        flag &&
          DeleteHandoverOperation(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getHandoverRecordList();
            }
          });
      });
    },
    /**
     * description: 新增术前交接
     * param {*} item
     * return {*}
     */
    addNewHandover(item) {
      if (this.addNewHandoverCheck(item)) {
        return;
      }
      //取符合条件的最后一条交班数据
      let handOffItemIndex = this.handoverRecordList.reduce((accumulator, currentValue, index) => {
        if (
          currentValue.patientOperationID == item.patientOperationID &&
          currentValue.handoverDate &&
          currentValue.handoverClass == "HandOff" &&
          currentValue.handoverType == item.handoverType
        ) {
          return index;
        }
        return accumulator;
      }, -1);
      //取符合条件的最后一条接班数据
      let handOnItemIndex = this.handoverRecordList.reduce((accumulator, currentValue, index) => {
        if (
          currentValue.patientOperationID == item.patientOperationID &&
          currentValue.handoverDate &&
          currentValue.handoverClass == "HandOn" &&
          currentValue.handoverType == item.handoverType
        ) {
          return index;
        }
        return accumulator;
      }, -1);
      this.handoverRecordListAddItem(
        this.handoverRecordList[handOffItemIndex],
        this.handoverRecordList[handOnItemIndex],
        handOnItemIndex + 1
      );
    },
    /**
     * description: 新增术前交接检核
     * return {*}
     */
    addNewHandoverCheck(item) {
      //有未交接的术前交班无法新增
      let notSaveHandover = this.handoverRecordList.find(
        (handover) =>
          handover.patientOperationID == item.patientOperationID &&
          !handover.handoverDate &&
          handover.handoverType == "PreOPHandover"
      );
      if (notSaveHandover) {
        this._showTip("warning", "有未交接的术前交班,无法再新增术前交接!");
        return true;
      }
      //有术后交接无法新增
      let postOpertion = this.handoverRecordList.find(
        (handover) =>
          handover.patientOperationID == item.patientOperationID &&
          handover.handoverDate &&
          handover.handoverType == "PostOPHandover"
      );
      if (postOpertion) {
        this._showTip("warning", "术后交接已完成,无法再新增术前交接!");
        return true;
      }
      return false;
    },
    /**
     * description: 新增数据初始化
     * param {*} handoffItem
     * param {*} handonItem
     * param {*} index
     * return {*}
     */
    handoverRecordListAddItem(handoffItem, handonItem, index) {
      let newHandoverArr = [this._common.clone(handoffItem), this._common.clone(handonItem)];
      newHandoverArr.forEach((handover) => {
        //新增交接初始化默认为原始交班评估内容
        handover.copyHandoverID = handover.handoverID;
        handover.handoverID = undefined;
        handover.handoverDate = undefined;
        handover.handoverTime = undefined;
        handover.handoverNurse = undefined;
        handover.checkDate = undefined;
        handover.checkTime = undefined;
      });
      this.handoverRecordList.splice(index, 0, ...newHandoverArr);
      this.getSpanArr(this.handoverRecordList);
    },
    /**
     * description: 删除按钮权限控制  只有已保存数据和本病区数据才可删除
     * param {*} handover
     * return {*}
     */
    getDeleteShowFlag(handover) {
      if (!handover.handoverID) {
        return false;
      }
      //已接班无法删除
      let handOnItem = this.handoverRecordList.find(
        (m) => m.handoverID == handover.handoverID && m.handoverClass == "HandOn" && m.handoverNurse
      );
      if (handOnItem) {
        return false;
      }
      return handover.stationID == this.user?.stationID;
    },
    /**
     * description: 保存权限控制 交班只有交班病区与登录病区一致才可保存 接班只有交班病区与的登录病区不一致才可保存
     * param {*} item
     * return {*}
     */
    async getSaveButtonShowFlag(item) {
      let hand;
      if (item.handoverClass === "HandOn") {
        const params = {
          settingTypeCode: item.handoverType,
        };
        try {
          // 使用await直接等待异步操作结果
          const res = await GetSettingValueByTypeCodeAsync(params);
          // 检查操作是否成功
          if (this._common.isSuccess(res)) {
            //确保使用正确的变量
            hand = !!res?.data;
          }
        } catch (error) {
          hand = false;
        }
      }
      return hand;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    getBrResult(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brItem.assessListID);
      }
    },
    /**
     * description: 专项跳转
     * param {*} content
     * return {*}
     */
    buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url =
        url +
        (url.includes("?") ? "&" : "?") +
        "handoverID=" +
        this.handoverID +
        "sourceID=" +
        this.handoverID +
        "&userID=" +
        this.user.userID +
        "&isDialog=true" +
        "&bedNumber=" +
        this.patient.bedNumber.replace(/\+/g, "%2B");
      //宏力手麻系统串专项使用
      if (this.hisFlag) {
        url += "&hisOperationNo=" + this.currentHandover.hisOperationNo;
      }
      this.showButtonDialog = true;
      this.$nextTick(() => {
        this.$refs?.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 获取BR及专项按钮按钮数据
     * param {*} assessListID
     * return {*}
     */
    async getButtonValue(assessListID) {
      let item = "";
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCode,
        assessListID: assessListID,
        sourceType: this.recordsCode + "_HandOff",
        sourceID: this.handoverIDArr[1],
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: BR 风险保存回显
     * return {*}
     */
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
      //添加过敏药物后返回刷新病人头 显示新增过敏药物
      if (this.buttonAssessListID == 185 || this.buttonAssessListID == 4570) {
        this._sendBroadcast("refreshInpatient");
      }
    },
    /**
     * description: BR按钮
     * param {*} content
     * return {*}
     */
    async buttonRecordClick(content) {
      let record = content.brParams || {};
      this.brItem = content;
      this.buttonRecordTitle = content.itemName;
      this.brAssessListID = content.assessListID;
      this.componentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        //交接都显示交班BR内容 不允许保存
        sourceType: this.recordsCode + "_HandOff",
        sourceID: this.handoverIDArr[1],
        assessTime:
          this._datetimeUtil.formatDate(this.handoverDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.handoverTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },

    /**
     * description: 评估木模板勾选内容
     * param {*} values
     * return {*}
     */
    changeValues(values) {
      this.assessValues = values;
    },
    /**
     * description: TN检核
     * param {*} flag
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    //弹窗开关函数
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 获取页面配置
     * return {*}
     */
    async getPageSetting() {
      this.getHandoverType("PreOPHandover");
      this.getHandoverType("PostOPHandover");
      await this.getOperationHandoverCheckFlag();
    },
    /**
     * description: 获取交班类型
     * return {*}
     */
    getHandoverType(handoverType) {
      let params = {
        typeValue: handoverType,
      };
      GetHandOverType(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data?.childrenItem?.length) {
            this["handoverTypeArr" + handoverType] = res.data.childrenItem;
          }
        }
      });
    },
    /**
     * description: 获取页签配置
     * return {*}
     */
    async getHandoverPaneList() {
      let params = {
        settingCode: "HandoverFunctionShift",
        handoverCode: this.recordsCode,
      };
      await GetHandoverPaneList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.handoverPaneList = this.getPaneMethods(res.data);
        }
      });
    },
    /**
     * description: pane对象对应函数填充
     * return {*}
     */
    getPaneMethods(paneList) {
      if (!paneList?.length) {
        return;
      }
      paneList.forEach((pane) => {
        // 添加各Pane初始化事件 评价无需初始化方法
        pane.initMethod = pane?.value == "Evaluate" || this["init" + pane.value];
        // 添加各Pane保存事件
        pane.saveMethod = this["save" + pane.value];
      });
      return paneList;
    },
    /**
     * description: 检核交班日期时间 交班时间不得大于接班时间 接班时间不得小于交班时间
     * return {*}
     */
    checkHandoverDateTime() {
      if (!this.currentHandover?.handoverClass || !this.currentHandover?.checkDate) {
        return false;
      }
      let handoverDate =
        this._datetimeUtil.formatDate(this.handoverDate, "yyyy-MM-dd") +
        " " +
        this._datetimeUtil.formatDate(this.handoverTime, "hh:mm");
      let checkDate =
        this._datetimeUtil.formatDate(this.currentHandover.checkDate, "yyyy-MM-dd") +
        " " +
        this._datetimeUtil.formatDate(this.currentHandover.checkTime, "hh:mm");
      let flag = handoverDate > checkDate;
      if (this?.currentHandover?.handoverClass == "HandOff") {
        flag && this._showTip("error", "交班时间不得大于接班时间");
        return flag;
      }
      if (this?.currentHandover?.handoverClass == "HandOn") {
        !flag && this._showTip("error", "接班时间不得小于交班时间");
        return !flag;
      }
      return false;
    },
    /**
     * description: 接班获取交班病区为评价护理问题
     * return {*}
     */
    getEvaluationStationID() {
      if (!this.currentHandover) {
        return this.patient?.stationID ?? 0;
      }
      if (this.handoverClass == "HandOff") {
        return this.currentHandover?.stationID ?? 0;
      }
      // 接班不显示未评价护理问题
      return 0;
    },
    /**
     * description: 表格合并
     * param {*} data
     * return {*}
     */
    getSpanArr(data) {
      if (!data?.length) {
        return;
      }
      this.spanArr = [];
      this.spanArrOne = [];
      let pos = 0;
      let pos1 = 0;
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.spanArrOne.push(1);
          pos = 0;
          pos1 = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].hisOperationNo == data[i - 1].hisOperationNo) {
            this.spanArr[pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            pos = i;
          }
          // 判断当前元素与上一个元素是否相同
          if (data[i].handoverType == data[i - 1].handoverType) {
            this.spanArrOne[pos1] += 1;
            this.spanArrOne.push(0);
          } else {
            this.spanArrOne.push(1);
            pos1 = i;
          }
        }
      }
    },
    /**
     * description: 表格合并
     * param {*} row
     * param {*} column
     * param {*} rowIndex
     * param {*} columnIndex
     * return {*}
     */
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0 || columnIndex == 1 || columnIndex == 2) {
        //合并第一列
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
      if (columnIndex == 3 || columnIndex == 8) {
        //合并第一列
        let _row = this.spanArrOne[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    /**
     * description: 获取是否检核交接班权限开关
     * return {*}
     */
    async getOperationHandoverCheckFlag() {
      let params = {
        settingTypeCode: "OperationHandoverCheckFlag",
      };
      await GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.operationHandoverCheckFlag = res.data;
        }
      });
    },
    /**
     * description: 手术接班修改删除按钮权限检核
     * param {*} item
     * return {*}
     */
    GetModifyFlag(item) {
      if (!!item.handoverDate || !this.operationHandoverCheckFlag) {
        return true;
      }
      // 术前交班
      if (item.handoverClass == "HandOff") {
        return true;
      }
      // 术前接班
      if (item.handoverClass == "HandOn") {
        return !!item.checkDate;
      }
    },
    /**
     * description: 加载最新生命体征数据
     * param {*}
     * return {*}
     */
    openClinic() {
      this.showClinicFlag = true;
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      GetVitalSignByInpatientID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.clinicTable = res.data;
        }
      });
    },
    /**
     * description:单击触发事件
     * param {*}当前行数据
     * return {*}
     */
    handleCurrentChange(value) {
      if (!value) {
        return;
      }
      let valueArr = Object.values(value);
      for (let i = 0; i < this.templateDatas.length; i++) {
        for (let j = 0; j < this.templateDatas[i].groups.length; j++) {
          if (this.templateDatas[i].groups[j].contents.length == 0) {
            continue;
          }
          for (let k = 0; k < this.templateDatas[i].groups[j].contents.length; k++) {
            let keyValue = valueArr.find((item) => item.id == this.templateDatas[i].groups[j].contents[k].assessListID);
            if (keyValue) {
              this.$set(this.templateDatas[i].groups[j].contents[k], "assessValue", keyValue.value);
            }
          }
        }
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout) {
          this.$refs.tabsLayout.init();
        }
      });
      this.showClinicFlag = false;
    },
    /**
     * description: 血液制品回显数据处理
     * param {*} contentFormat
     * param {*} selectTableData
     * return {*}
     */
    handleTransfusionData(contentFormat, selectTableData) {
      if (contentFormat !== "Transfusion") {
        return [];
      }
      if (!Array.isArray(selectTableData)) {
        return [];
      }
      if (selectTableData.length === 0) {
        return [];
      }
      const grouped = selectTableData.reduce((acc, data) => {
        const splicedContent = data.splicedContent || "";
        if (splicedContent.length >= 4) {
          const status = splicedContent.slice(0, 4);
          const content = splicedContent.slice(4);
          if (!acc[status]) {
            acc[status] = [];
          }
          acc[status].push(content);
        }
        return acc;
      }, {});
      const content = Object.entries(grouped).map(([key, values]) => `${key}${values.join(",")}`).join(";");
      const styleContent = Object.entries(grouped).map(([key, values]) => `${key}${values.join(",")}`).join(";<br/>");
      return [content, styleContent];
    }
},
};
</script>

<style lang="scss">
.operation-handover {
  height: 100%;

  .drawer-content {
    .handover-tabs {
      height: 35px;
      margin-bottom: 5px;
    }

    .pane-content {
      height: calc(100% - 45px);
    }
  }

  .date-picker {
    width: 160px;
  }

  .time-picker {
    width: 100px;
  }

  .handover-type-select {
    width: 240px;
  }

  .visibility {
    visibility: hidden;
  }
}
</style>