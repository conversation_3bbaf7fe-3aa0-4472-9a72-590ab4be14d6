<!--
 * FilePath     : \ccc.web\src\pages\PatientNeurovascular\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-07-29 14:42
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-07-05 14:50
 * Description  : 神经血管评估
-->
<template>
  <specific-care
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :recordTitleSlotFalg="true"
    :handOverFlag="handOverArr"
    :previewFlag="!checkResult"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    :drawerSize="supplementFlag ? '80%' : ''"
    @mainAdd="recordAdd"
    @save="saveNeurovascular"
    @getHandOverFlag="getHandOverFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    @cancel="showTemplateFlag = false"
    class="patient-neurovascular"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <div slot="record-title">
      <label>日期：</label>
      <el-date-picker
        v-model="recordDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        class="record-title-data"
        placeholder="选择日期"
        @change="getTableView()"
      ></el-date-picker>
    </div>
    <div slot="main-record">
      <el-table :data="recordList" height="100%" border stripe>
        <el-table-column fixed="left" label="日期" width="100" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column fixed="left" prop="" label="时间" width="60" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="departmentName" label="科室" min-width="85"></el-table-column>
        <el-table-column prop="nursingLevel" label="护理级别" width="80" align="center"></el-table-column>
        <el-table-column prop="assessPart" label="观察区域" min-width="110" header-align="center"></el-table-column>
        <el-table-column prop="painScore" label="疼痛评分" width="100" header-align="center"></el-table-column>
        <el-table-column prop="skinTemperature" label="肤温" width="50" align="center"></el-table-column>
        <el-table-column prop="skinColor" label="肤色" width="60"></el-table-column>
        <el-table-column label="动脉名称" width="50" align="center">
          <el-table-column prop="arteryUpper" label="上肢" width="50"></el-table-column>
          <el-table-column prop="arteryLower" label="下肢" width="50"></el-table-column>
        </el-table-column>
        <el-table-column prop="arteriopalmus" label="动脉搏动" width="50" align="center"></el-table-column>
        <el-table-column prop="capillariesAreFull" label="毛细血管充盈" width="90" align="center"></el-table-column>
        <el-table-column prop="edemaScore" label="水肿" width="70" header-align="center"></el-table-column>
        <el-table-column label="感觉" width="50" align="center">
          <el-table-column prop="feelHandBack" label="手背面拇指和食指之间的网状空间" width="100"></el-table-column>
          <el-table-column prop="feelHandPalm" label="手掌拇指和食指之间的网状空间" width="100"></el-table-column>
          <el-table-column prop="feelPalmBack" label="手掌和手背面，小指和远端无名指" width="100"></el-table-column>
          <el-table-column prop="feelFootBack" label="足部的背侧表面" width="100"></el-table-column>
          <el-table-column prop="feelPelma" label="足底表面" width="80"></el-table-column>
        </el-table-column>
        <el-table-column label="活动度" align="center" width="50">
          <el-table-column
            prop="activityThumbAbduction"
            label="拇指诱导"
            width="100"
            header-align="center"
          ></el-table-column>
          <el-table-column
            prop="activityAbilityAndAnular"
            label="拇指和小拇指可以合在一起 (触摸)"
            width="100"
          ></el-table-column>
          <el-table-column prop="activityFingerAbduction" label="手指诱导" width="100"></el-table-column>
          <el-table-column prop="activityAnkleToesDorsiflex" label="踝关节和脚趾的背屈" width="100"></el-table-column>
        </el-table-column>
        <el-table-column prop="nurseName" label="记录人" width="70" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" width="70" align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="recordAdd(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip v-if="scope.row.recordsCode != 'ThrombolysisStart'" content="删除">
              <div @click.stop="recordDelete(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="layoutLoading"
      :element-loading-text="layoutText"
    >
      <div slot="header">
        <span class="label">执行日期:</span>
        <el-date-picker
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          style="width: 120px"
        ></el-date-picker>
        <el-time-picker
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          style="width: 80px"
        ></el-time-picker>
        <span class="label">执行病区:</span>
        <station-selector v-model="stationID" label="" width="160"></station-selector>
        <dept-selector label="" width="140" v-model="departmentListID" :stationID="stationID"></dept-selector>
      </div>
      <tabs-layout
        ref="tabsLayout"
        :template-list="templateDatas"
        @button-click="buttonClick"
        @change-values="changeValues"
        @checkTN="checkTN"
      />
    </base-layout>
    <div slot="drawer-dialog">
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonName"
        :visible.sync="showButtonDialog"
        fullscreen
        custom-class="no-footer"
      >
        <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
      </el-dialog>
    </div>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import { mapGetters } from "vuex";
import { GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import { GetBringToShiftSetting } from "@/api/Setting.js";
import {
  GetNeurovascularAssesssView,
  NeurovascularSave,
  GetNeurovascularTableView,
  DeleteNeurovascular,
} from "@/api/Neurovascular";
import { GetBringToNursingRecordFlagSetting } from "@/api/SettingDescription";
import { GetButtonData } from "@/api/Assess";
import baseLayout from "@/components/BaseLayout";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  components: {
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    baseLayout,
  },
  data() {
    return {
      //页面加载
      loading: false,
      layoutLoading: false,
      layoutText: undefined,
      //组件变量
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      handOverArr: [true, false],
      informPhysicianArr: [true, false],
      bringToNursingRecordArr: [true, false],
      settingHandOver: false,
      settingNursingRecord: false,
      //顶部时间变量
      recordDate: undefined,
      //评估模板变量
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      departmentListID: undefined,
      recordsCodeInfo: {},
      templateDatas: [],
      assessDatas: [],
      checkTNFlag: undefined,
      patientScheduleMainID: undefined,
      showButtonDialog: false,
      buttonName: "",
      sourceType: "Neurovascular",
      sourceID: undefined,
      //表格变量
      recordList: [],
      careMainID: undefined,
      //表格合并变量
      spanArr: [],
      pos: 0,
      checkResult: true,
      patient: undefined,
      //补录标记
      supplementFlag: undefined,
    };
  },
  watch: {
    "patientInfo.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.supplementFlag = undefined;
        }
      },
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.supplementFlag = "*";
          this.bringToNursingRecordArr = [false, false];
        }
      },
    },
    "patient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getTableView();
        }
      },
    },
    showButtonDialog(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
  },
  mounted() {
    //获取交班配置
    this.getBringHandOverSetting();
    this.getBringToNursingRecordSetting();
    this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
  },

  methods: {
    /**
     * description: 获取表格数据
     * param {*}
     * return {*}
     */
    getTableView() {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        date: this.recordDate,
      };
      this.loading = true;
      GetNeurovascularTableView(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.recordList = res.data;
          if (this.recordList.length > 0) {
            this.getSpanArr(this.recordList);
          }
        }
      });
    },

    /**
     * description: 记录新增或修改
     * param {*} record
     * return {*}
     */
    async recordAdd(record) {
      this.checkResult = true;
      if (record) {
        //是否仅本人操作
        this.checkResult = await this._common.checkActionAuthorization(this.user, record.nurseID);
      }
      this.openOrCloseDrawer(true, "新增");
      this.stationID = record ? record.stationID : this.patient.stationID;
      this.departmentListID = record ? record.departmentListID : this.patient.departmentListID;
      this.assessDate = record ? record.assessDate : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = record ? record.assessTime : this._datetimeUtil.getNowTime("hh:mm");
      this.sourceID = record ? record.patientNeurovascularCareMainID : this._common.guid();
      this.careMainID = this.sourceID;
      this.$set(this.handOverArr, 1, record ? record.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, record && record.informPhysician ? true : false);
      this.$set(this.bringToNursingRecordArr, 1, record ? record.bringToNursingRecord : this.settingNursingRecord);
      this.getNeurovascularAssessTemplate();
    },

    /**
     * description: 记录保存
     * param {*}
     * return {*}
     */
    saveNeurovascular() {
      if (!this.patient || Object.keys(this.recordsCodeInfo).length == 0) {
        return;
      }
      let params = {
        InpatientID: this.patient.inpatientID,
        StationID: this.stationID,
        DepartmentListID: this.departmentListID,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        InterventionID: this.recordsCodeInfo.interventionMainID,
        BringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        PatientNeurovascularCareMainID: this.careMainID,
        RefillFlag: this.supplementFlag,
        Details: this.getDetails(),
      };
      if (this.patientScheduleMainID) {
        params.PatientScheduleMainID = this.patientScheduleMainID;
      }
      if (params.Details.length == 0) {
        this._showTip("warning", "请勾选内容后保存!");
        return;
      }
      this.layoutLoading = true;
      this.layoutText = "保存中……";
      NeurovascularSave(params).then((res) => {
        this.layoutLoading = false;
        this.layoutText = "";
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.openOrCloseDrawer(false);
          this.getTableView();
        }
      });
    },

    /**
     * description: 记录删除
     * param {*} record
     * return {*}
     */
    async recordDelete(record) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, record.nurseID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (!record || !record.patientNeurovascularCareMainID) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            careMainID: record.patientNeurovascularCareMainID,
          };
          DeleteNeurovascular(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getTableView();
            }
          });
        }
      });
    },

    /**
     * description: 获取评估模板
     * param {*}
     * return {*}
     */
    async getNeurovascularAssessTemplate() {
      let params = {
        inpatientID: this.patient.inpatientID,
        departmentListID: this.patient.departmentListID,
        mappingType: "NeurovascularAssessMaintain",
        age: this.patient.age,
      };
      this.layoutLoading = true;
      this.layoutText = "加载中……";
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        this.layoutLoading = false;
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo) {
        return;
      }
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        stationID: this.patient.stationID,
        dateOfBirth: this.patient.dateOfBirth,
        inpatientID: this.patient.inpatientID,
        patientNeurovascularCareMainID: this.careMainID,
      };
      this.layoutLoading = true;
      await GetNeurovascularAssesssView(params).then((res) => {
        this.layoutLoading = false;
        if (this._common.isSuccess(res)) {
          this.templateDatas = res.data;
        }
      });
      this.layoutLoading = false;
      this.layoutText = "";
    },
    /**
     * description: 获取组件选中值
     * param {*} details
     * return {*}
     */
    changeValues(details) {
      this.assessDatas = details;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 弹窗开关函数
     * param {*} flag
     * param {*} title
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
    },
    /**
     * description: 组装保存detail数据
     * param {*}
     * return {*}
     */
    getDetails() {
      let details = [];
      if (!this.assessDatas.length) {
        return details;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return details;
      }
      this.assessDatas.forEach((item) => {
        let detail = {
          assessListID: item.assessListID,
          assessListGroupID: item.assessListGroupID,
          specialListType: item.specialListType,
        };
        if (item.controlerType.trim() == "C" || item.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = item.assessValue;
        }
        details.push(detail);
      });
      return details;
    },

    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "Neurovascular",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.handOverArr[0]) {
            this.settingHandOver = res.data;
          }
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "NeurovascularAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },

    /**
     * description: 组件回传交班flag
     * param {*} flag
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 通知医师标记
     * param {*} flag
     * return {*}
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记
     * param {*} flag
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.bringToNursingRecordArr[1] = flag;
    },
    /**
     * description: 评估模板打开专项
     * param {*} content
     * return {*}
     */
    buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      if (url.indexOf("?") == -1) {
        url += "?sourceID=" + this.sourceID;
      } else {
        url += "&sourceID=" + this.sourceID;
      }
      url +=
        "&sourceType=" +
        this.sourceType +
        "&bedNumber=" +
        this.patient.bedNumber.replace(/\+/g, "%2B") +
        "&userID=" +
        this.user.userID +
        "&token=" +
        this.token +
        "&isDialog=true";
      this.showButtonDialog = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },

    /**
     * description: 添加完更新按钮数据
     * param {*} assessListID
     * return {*}
     */
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      for (let i = 0; i < this.templateDatas.length; i++) {
        for (let j = 0; j < this.templateDatas[i].groups.length; j++) {
          var group = this.templateDatas[i].groups[j];
          if (group.controlerType.trim() == "B" || group.controlerType.trim() == "BR") {
            if (group.assessListID == assessListID) {
              this.$set(group, "assessValue", item.assessValue);
              this.$set(group, "linkForm", item.linkForm);
              return;
            }
          }
          for (let k = 0; k < group.contents.length; k++) {
            var content = group.contents[k];
            if (content.controlerType.trim() == "B" || content.controlerType.trim() == "BR") {
              if (content.assessListID == assessListID) {
                this.$set(content, "assessValue", item.assessValue);
                this.$set(content, "linkForm", item.linkForm);
                return;
              }
            }
            // 判断三阶里是否有按钮
            if (content.childList) {
              for (let m = 0; m < content.childList.length; m++) {
                var child = content.childList[m];
                if (child.controlerType.trim() == "B" || child.controlerType.trim() == "BR") {
                  if (child.assessListID == assessListID) {
                    this.$set(child, "assessValue", item.assessValue);
                    this.$set(child, "linkForm", item.linkForm);
                    return;
                  }
                }
              }
            }
          }
        }
      }
    },
    /**
     * description: 更新按钮回显数据
     * params {*}
     * return {*}
     * param {*} assessListID
     */
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessListID: assessListID,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },

    /**
     * description: 表格合并
     * param {*} data
     * return {*}
     */
    getSpanArr(data) {
      this.spanArr = [];
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].assessDate == data[i - 1].assessDate) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
  },
};
</script>
<style lang="scss">
.patient-neurovascular {
  .record-title-data {
    width: 120px;
  }
}
</style>