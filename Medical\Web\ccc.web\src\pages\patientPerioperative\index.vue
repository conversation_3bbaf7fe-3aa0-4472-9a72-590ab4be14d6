<!--
 * FilePath     : \src\pages\patientPerioperative\index.vue
 * Author       : 胡洋
 * Date         : 2020-11-23 09:41
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-01-06 17:13
 * Description  :
-->
<template>
  <base-layout class="interventional-operation">
    <div slot="header">
      <span class="name-label" v-if="mode == 'editMode' && this.operationName">{{ "手术名称：" + operationName }}</span>
      <div class="header-button">
        <el-button type="primary" icon="iconfont icon-save-button" v-if="mode == 'editMode'" @click="saveEidt">
          保存
        </el-button>
      </div>
    </div>
    <base-layout>
      <div slot="header" class="edit-time">
        <span class="start-time-label">
          手术开始时间：
          <el-date-picker
            v-model="startDateTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            style="width: 170px"
            placeholder="请选择手术开始时间"
          ></el-date-picker>
        </span>
        <span class="end-time-label">
          手术结束时间：
          <el-date-picker
            v-model="endDateTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            style="width: 170px"
            placeholder="请选择手术结束时间"
          ></el-date-picker>
        </span>
      </div>
      <body-and-assess-layout :link="link" v-loading="pageLoading">
        <tabs-layout
          :template-list="templateDatas"
          @change-values="changeValues"
          @checkTN="checkTN"
          @button-click="buttonClick"
          :gender="patient ? patient.genderCode : ''"
        />
      </body-and-assess-layout>
    </base-layout>
    <!-- 记录模式 -->
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      fullscreen
      custom-class="no-footer"
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
    </el-dialog>
  </base-layout>
</template>
<script>
import {
  GetInterventionOPRecordList,
  GetInterventionOPAssessView,
  GetInterventionOPRecordByMainID,
  SaveInterventionOPRecord,
  GetRecordByhisOperationNo,
} from "@/api/PatientPerioperative";
import baseLayout from "@/components/BaseLayout";
import tabsLayout from "@/components/tabsLayout/index";
import bodyAndAssessLayout from "@/components/bodyAndAssessLayout";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
      user: "getUser",
      token: "getToken",
    }),
  },
  components: {
    baseLayout,
    tabsLayout,
    bodyAndAssessLayout,
  },
  data() {
    return {
      // 是否加载页面
      pageLoading: false,
      //选择模式
      mode: "editMode",
      //人体图链接
      link: undefined,
      //手术开始日期时间
      startDateTime: undefined,
      //手术结束日期时间
      endDateTime: undefined,
      //评估模板数据
      templateDatas: [],
      //评估模版是否需要检核互斥
      checkTNFlag: true,
      //评估模版选择的数据
      assessDatas: [],
      OperationRecord: undefined,
      showButtonDialog: false,
      buttonName: undefined,
      operationName: undefined,
    };
  },

  created() {
    if (this.$route.query.hisOperationNo) {
      this.init();
    } else {
      this._showTip("error", "缺少手术ID信息获取失败");
    }
  },
  methods: {
    async init() {
      this.pageLoading = true;
      //获取手术基本数据
      let queryRecord = {
        hisOperationNo: this.$route.query.hisOperationNo,
      };
      await GetRecordByhisOperationNo(queryRecord).then((result) => {
        if (this._common.isSuccess(result)) {
          this.OperationRecord = result.data;
        }
      });
      //如果有数据回显
      if (this.OperationRecord) {
        this.operationName = this.OperationRecord.operationName;
        if (this.OperationRecord.operationStartDateTime) {
          this.startDateTime = this.OperationRecord.operationStartDateTime;
        }
        if (this.OperationRecord.operationEndDateTime) {
          this.endDateTime = this.OperationRecord.operationEndDateTime;
        }
        if (this.OperationRecord.bodypartValue) {
          this._common.storage("selectPart", JSON.parse(this.OperationRecord.bodypartValue));
        }
      } else {
        this._showTip("error", "暂无数据，稍后再试");
        return;
      }

      // 获取评估模版
      let params = {
        recordsCode: "InterventionOPRecord",
        patientOperationRecordMainID: this.OperationRecord.patientOperationRecordMainID,
        inpatientID: this.OperationRecord.inpatientID,
      };
      await GetInterventionOPAssessView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
      // 获取人体图
      this.link =
        "../../static/body/mobileBody.html?type=CommonMulti&recordsCode=InterventionOPRecord&gender=" +
        this.patient.genderCode;
      this.pageLoading = false;
    },
    saveEidt() {
      this.pageLoading = true;
      let params = {
        main: {},
        detailList: [],
        bodyPart: [],
      };
      //人体图
      params.bodyPart = localStorage.getItem("bodyPart");
      //手术基本信息
      params.main.patientOperationRecordMainID = this.OperationRecord.patientOperationRecordMainID;
      if (this.startDateTime) {
        params.main.operationStartDateTime = this.startDateTime;
      }
      if (this.endDateTime) {
        params.main.operationEndDateTime = this.endDateTime;
      }
      //获取评估数据
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        params.detailList.push(detail);
      });
      //保存数据
      SaveInterventionOPRecord(params).then((result) => {
        this.pageLoading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.init();
        }
      });
    },

    //评估模版选中的项目
    changeValues(datas) {
      this.assessDatas = datas;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    buttonClick(content) {
      // this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      // if (url.indexOf("?") == -1) {
      //   url += "?handoverID=" + this.handoverID.replace("handover-", "");
      // } else {
      //   url += "&handoverID=" + this.handoverID.replace("handover-", "");
      // }
      url +=
        "&bedNumber=" +
        this.patient.bedNumber.replace(/\+/g, "%2B") +
        "&userID=" +
        this.user.userID +
        "&token=" +
        this.token +
        "&isDialog=true";
      if (this.$route.query.hisOperationNo) {
        url += "&hisOperationNo=" + this.$route.query.hisOperationNo;
      }
      this.showButtonDialog = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    // 以下暂停使用
    //查询介入手术记录列表
    getOperationList() {
      this.listLoading = true;
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      GetInterventionOPRecordList(params).then((result) => {
        this.listLoading = false;
        if (this._common.isSuccess(result)) {
          this.operationList = result.data;
        }
      });
    },
    //编辑介入手术记录
    async editRecord(row) {
      this.dialogLoading = true;
      this.dialogFormVisible = true;
      this.patientOperationRecordMainID = row.patientOperationRecordMainID;
      let query = {
        mainID: row.patientOperationRecordMainID,
      };
      await GetInterventionOPRecordByMainID(query).then((result) => {
        if (this._common.isSuccess(result)) {
          this.mainRecord = result.data;
        }
      });
      if (this.mainRecord) {
        if (this.mainRecord.operationStartDateTime) {
          this.startDate = this.mainRecord.operationStartDateTime.substring(0, 10);
          this.startTime = this.mainRecord.operationStartDateTime.substring(11, 16);
        }
        if (this.mainRecord.operationEndDateTime) {
          this.endDate = this.mainRecord.operationEndDateTime.substring(0, 10);
          this.endTime = this.mainRecord.operationEndDateTime.substring(11, 16);
        }
      }
      //组装对话框标题
      this.dialogTitle =
        "介入手术记录维护(" +
        this.patient.patientName +
        "-" +
        this.patient.gender +
        "-" +
        this.patient.ageDetail +
        "岁)";
      // 获取人体图
      this.link =
        "../../static/body/mobileBody.html?type=Common&recordsCode=InterventionOPRecord&gender=" +
        this.patient.genderCode;
      // 获取评估模版
      let params = {
        recordsCode: "InterventionOPRecord",
        patientOperationRecordMainID: row.patientOperationRecordMainID,
      };
      await GetInterventionOPAssessView(params).then((result) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.interventional-operation {
  .name-label {
    margin-left: 20px;
  }
  .header-button {
    float: right;
  }
  .layout-body-warp {
    height: 600px !important;
  }
}
</style>