<!--
 * FilePath     : \ccc.web\src\autoPages\recordSupplement\components\consultSwitch.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-17 17:24
 * LastEditors  : LX
 * LastEditTime : 2025-06-20 16:18
 * Description  : 
 * CodeIterationRecord: 
 -->
<template>
  <div class="consult-supplement">
    <el-tabs class="tabs" v-model="activeComponentIndex">
      <el-tab-pane
        v-for="({ description }, index) in childComponents"
        :key="index"
        :label="description"
        :name="index.toString()"
      />
    </el-tabs>
    <div class="tabs-content">
      <component
        :is="childComponents[activeComponentIndex].settingValue"
        :supplementPatient="patient"
        refillFlag
      ></component>
    </div>
  </div>
</template>

<script>
import patientConsult from "@/pages/patientConsult/index";
import consultAssign from "@/pages/patientConsult/consultAssign";
import replyConsult from "@/pages/patientConsult/replyConsult";
export default {
  components: {
    patientConsult,
    consultAssign,
    replyConsult,
  },
  props: {
    patient: {
      type: Object,
      default: () => {},
    },
    childComponents: {
      type: Array,
      require: true,
    },
  },
  data() {
    return {
      activeComponentIndex: 0,
    };
  },
};
</script>

<style lang="scss">
.consult-supplement {
  height: 100%;
  .tabs {
    height: 40px;
  }
  .tabs-content {
    height: calc(100% - 40px);
  }
}
</style>