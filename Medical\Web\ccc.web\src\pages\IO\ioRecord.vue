<!--
 * FilePath     : \src\pages\IO\ioRecord.vue
 * Author       : 郭自飞
 * Date         : 2020-04-30 16:25
 * LastEditors  : 张现忠
 * LastEditTime : 2024-04-17 09:40
 * Description  : io记录单
 * CodeIterationRecord: 
 * 1、2690-作为护理人员，我需要出入水量查询，每个页签都可以根据自然时间查询
 * 2、3013-作为IT人员，我需要出入量的班别汇总、每天汇总单独页签呈现
 -->
<!--io记录单-->
<template>
  <base-layout class="io-record">
    <div slot="header" class="top">
      <span>{{ $t("ioStatistics.switchLabel") }}</span>
      <el-switch @change="changeDateType()" v-model="searchTypeSwitchFlag" />
      <span class="label">{{ $t("label.date") }}</span>
      <span v-show="searchTypeSwitchFlag" >
        <el-date-picker
          v-model="queryCriteria.startShiftTime"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="date"
          class="date-picker"
          :placeholder="placeholder.date"
        ></el-date-picker>
        <span>-</span>
        <el-date-picker
          v-model="queryCriteria.endShiftTime"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="date"
          class="date-picker"
          :placeholder="placeholder.date"
        ></el-date-picker>
        <span class="label">{{ ioRecord.conditionsLabel }}</span>
        <el-select v-model="queryCriteria.choice" class="total">
          <el-option
            v-for="item in options"
            :key="item.typeValue"
            :label="item.settingValue"
            :value="item.typeValue"
          ></el-option>
        </el-select>
      </span>
      <span v-show="!searchTypeSwitchFlag" >
        <el-date-picker
          v-model="queryCriteria.startTime"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          class="datetime-picker"
          :placeholder="placeholder.date"
        ></el-date-picker>
        <span>-</span>
        <el-date-picker
          v-model="queryCriteria.endTime"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          class="datetime-picker"
          :placeholder="placeholder.date"
        ></el-date-picker>
      </span>
      <station-selector
        v-model="stationID"
        :label="$t('label.station')"
        :inpatientID="inpatient ? inpatient.inpatientID : ''"
        width="160"
      ></station-selector>
      <el-button class="query-button" icon="iconfont icon-search" @click="searchData()">{{ button.query }}</el-button>
    </div>
    <el-table height="70%" border :data="rows" :row-style="subTotalRow">
      <el-table-column prop="date" :label="ioRecord.date" resizable width="110" align="center"></el-table-column>
      <el-table-column prop="time" :label="ioRecord.time" resizable width="110" align="center"></el-table-column>
      <el-table-column :label="ioRecord.input" resizable align="center">
        <template>
          <el-table-column
            v-for="(column, index) in inputColumns"
            :key="index"
            :label="column.title"
            :prop="column.name"
            min-width="60"
            resizable
            align="center"
          ></el-table-column>
        </template>
      </el-table-column>
      <el-table-column :label="ioRecord.output" resizable align="center">
        <template>
          <el-table-column
            v-for="(column, index) in outputColumns"
            :key="index"
            :label="column.title"
            :prop="column.name"
            min-width="55"
            resizable
            align="center"
          >
            {{ column.name }}
          </el-table-column>
        </template>
      </el-table-column>
    </el-table>
    <div class="statistics-wrap">
      <el-table v-if="searchTypeSwitchFlag" height="100%" border :data="ioStatisticsShift">
        <el-table-column :label="ioRecord.date" resizable width="110" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.ioDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="shift" :label="ioRecord.shift" resizable width="80" align="center"></el-table-column>
        <el-table-column prop="startTime" :label="ioRecord.startTime" resizable min-width="70" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="endTime" :label="ioRecord.endTime" resizable min-width="70" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.endTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="input"
          :label="ioRecord.input"
          resizable
          min-width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="output"
          :label="ioRecord.output"
          resizable
          min-width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="total"
          :label="ioRecord.inflow"
          resizable
          min-width="100"
          align="center"
        ></el-table-column>
      </el-table>
      <el-table
        v-else
        height="100%"
        border
        :data="ioStatisticsDay"
        :span-method="arraySpanMethod"
        :cell-style="daysStyle"
      >
        <el-table-column :label="ioRecord.date" resizable width="110" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.ioDate == ioRecord.total">{{ scope.row.ioDate }}</span>
            <span v-else v-formatTime="{ value: scope.row.ioDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="startTime"
          :label="ioRecord.startTime"
          resizable
          min-width="70"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="endTime"
          :label="ioRecord.endTime"
          resizable
          min-width="70"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="input"
          :label="ioRecord.input"
          resizable
          min-width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="output"
          :label="ioRecord.output"
          resizable
          min-width="100"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="total"
          :label="ioRecord.inflow"
          resizable
          min-width="100"
          align="center"
        ></el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import { GetClinicalSettingInfo } from "@/api/Assess";
import { GetDocument, GetIOTotalStatistics } from "@/api/IO";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    stationSelector,
  },
  data() {
    return {
      queryCriteria: {
        startTime: "",
        endTime: "",
        startShiftTime: "",
        startShiftTime: "",
        choice: "0",
      },
      ioData: {},
      rows: [],
      columns: [],
      inputColumns: [],
      outputColumns: [],
      nowDate: "",
      ioStatisticsShift: [],
      ioStatisticsDay: [],
      options: [],
      searchTypeSwitchFlag: true,
      stationID: undefined,
      excludeIOTotal: false,
    };
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
    }),
    placeholder() {
      return this.$t("placeholder");
    },
    button() {
      return this.$t("button");
    },
    loadingText() {
      return this.$t("loadingText");
    },
    ioRecord() {
      return this.$t("ioRecord");
    },
  },
  watch: {
    inpatient(newVal) {
      if (!newVal) return;
      this.columns = [];
      this.outputColumns = [];
      this.inputColumns = [];
      this.rows = [];
      this.stationID = newVal.stationID;
      this.searchData();
    },
    stationID: {
      async handler(newV) {
        if (!newV) return;
        this.outputColumns = [];
        this.inputColumns = [];
        this.columns = [];
        this.rows = [];
        this.searchData();
      },
    },
  },
  mounted() {
    this.changeDateType();
    this.getIOSummaryInterval();
    this.getExcludeIOTotalSetting();
  },
  methods: {
    /**
     * @description: 获取IO汇总间隔条件
     * @return
     */
    async getIOSummaryInterval() {
      let params = {
        SettingTypeCode: "IOSummaryInterval",
      };
      await GetClinicalSettingInfo(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.options = res.data;
        }
      });
    },
    /**
     * @description: 小计行样式
     * @param row 表格渲染的当前行
     * @return
     */
    subTotalRow(row) {
      if (row.row.date == this.ioRecord.subtotal) {
        return { backgroundColor: "#FFFFDF" };
      }
    },
    /**
     * @description: 获取IO统计数据
     * @return
     */
    getStatistics() {
      let parms = {
        inpatientID: this.inpatient.inpatientID,
        startTime: this.searchTypeSwitchFlag ? this.queryCriteria.startShiftTime : this.queryCriteria.startTime,
        endTime: this.searchTypeSwitchFlag ? this.queryCriteria.endShiftTime : this.queryCriteria.endTime,
        stationID: this.stationID,
        showDataByShift: this.searchTypeSwitchFlag,
      };
      this.ioStatisticsDay = undefined;
      this.ioStatisticsShift = undefined;
      GetIOTotalStatistics(parms).then((res) => {
        if (this._common.isSuccess(res)) {
          this.searchTypeSwitchFlag ? (this.ioStatisticsShift = res.data) : (this.ioStatisticsDay = res.data);
          this.calcTotalToForDayIoStatistic();
        }
      });
    },
    /**
     * @description: 计算加总量并添加到ioStatisticsDay集合中
     * @return
     */
    calcTotalToForDayIoStatistic() {
      if (this.searchTypeSwitchFlag || this.excludeIOTotal) {
        return;
      }
      // 合计
      let label = this.ioRecord.total;
      let template = {
        ioDate: label,
        startTime: label,
        endTime: label,
        input: 0,
        output: 0,
        total: 0,
      };
      this.ioStatisticsDay.forEach((item) => {
        template.input += item.input;
        template.output += item.output;
        template.total += item.total;
      });
      this.ioStatisticsDay.push(template);
    },
    /**
     * @description: 单元格列合并
     * @param row 当前行
     * @param column 当前列
     * @param rowIndex 行序号
     * @param columnIndex 列序号
     * @return
     */
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (rowIndex == this.ioStatisticsDay.length - 1 && !this.excludeIOTotal) {
        if (columnIndex === 0) {
          return [1, 3];
        }
        if (columnIndex === 1 || columnIndex === 2) {
          return [0, 0];
        }
      }
    },
    daysStyle({ row, column, rowIndex, columnIndex }) {
      if (rowIndex == this.ioStatisticsDay.length - 1) {
        if (columnIndex >= 3) {
          return "color:#f00";
        }
      }
    },
    //查询io数据
    searchData() {
      if (!this.stationID) {
        this.inpatient && (this.stationID = this.inpatient.stationID);
      }
      this.getStatistics();
      let parms = {
        inpatientID: this.inpatient.inpatientID,
        startTime: this.searchTypeSwitchFlag ? this.queryCriteria.startShiftTime : this.queryCriteria.startTime,
        endTime: this.searchTypeSwitchFlag ? this.queryCriteria.endShiftTime : this.queryCriteria.endTime,
        interval: this.queryCriteria.choice,
        stationID: this.stationID,
        typeFlag: this.searchTypeSwitchFlag,
      };
      GetDocument(parms).then((res) => {
        if (this._common.isSuccess(res)) {
          this.ioData = res.data;
          this.columns = this.ioData.columns;
          this.outputColumns = this.ioData.columns.filter((item) => item.other == "output");
          this.inputColumns = this.ioData.columns.filter((item) => item.other == "input");
          this.rows = this.ioData.rows;
        }
      });
    },
    //切换加总类型时触发
    changeDateType() {
      this.rows = [];
      this.ioStatisticsDay = [];
      let nowDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      if (this.searchTypeSwitchFlag) {
        this.$set(this.queryCriteria, "startShiftTime", nowDate);
        this.$set(this.queryCriteria, "endShiftTime", nowDate);
      } else {
        this.$set(this.queryCriteria, "startTime", nowDate + " 00:00");
        this.$set(this.queryCriteria, "endTime", nowDate + " 23:59");
      }
      this.searchData();
    },
    /**
     * description: 获取配置是否排除入出量合计
     * return {*}
     */
    getExcludeIOTotalSetting() {
      let params = {
        settingTypeCode: "ExcludeIOTotal",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.excludeIOTotal = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.io-record {
  .top {
    .label {
      margin-left: 10px;
    }
    .date-picker {
      width: 110px;
    }
    .datetime-picker {
      width: 150px;
    }
  }
  .statistics-wrap {
    margin-top: 10px;
    height: calc(30% - 10px);
  }
  .total {
    width: 100px;
  }
  .el-dialog__wrapper {
    position: absolute;
  }
  .el-dialog {
    height: 250px;
  }
}
</style>
