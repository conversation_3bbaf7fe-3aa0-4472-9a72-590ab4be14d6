<template>
  <div class="my-image" :style="{ width: imgWidth }">
    <div class="image-item" v-for="(imgstr, index) in imgStrArr" :key="index">
      <img class="upload-img" :src="imgstr" @click="showBigImg(imgstr)" />
      <div class="del">
        <i class="iconfont icon-del-img" @click="deleImg(index)"></i>
      </div>
    </div>
    <div class="image-item" v-if="imgStrArr.length < maxNum">
      <input class="input-file" type="file" ref="imgUploadFile" @change="deelImg" accept="image/*" capture="camera" />
      <i class="add iconfont icon-add-img"></i>
    </div>
    <div class="wound-img">
      <div v-if="showBig">
        <div class="img-mask"></div>
        <img v-if="bigImg" :src="bigImg" class="big-img" />
        <div class="close">
          <i class="iconfont icon-del-img" @click="hidBigImg"></i>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    // 最大图片数 默认4
    maxNum: {
      default: 4,
    },
    width: {
      type: Number,
      default: 100,
    },
    defaultImgs: {
      default: function () {
        return [];
      },
    },
  },
  data() {
    return {
      imgStrArr: [],
      imgQuality: 0.7,
      showBig: false,
      bigImg: undefined,
    };
  },
  watch: {
    defaultImgs: {
      immediate: true,
      handler(newImgs) {
        if (newImgs) {
          this.imgStrArr = newImgs;
          this.$emit("change", newImgs);
        }
      },
    },
  },
  computed: {
    imgWidth() {
      return this.width + "px";
    },
  },
  methods: {
    deleImg(index) {
      this.imgStrArr.splice(index, 1);
      this.$emit("change", this.imgStrArr);
    },
    showBigImg(img) {
      this.bigImg = img;
      this.showBig = true;
    },
    hidBigImg() {
      this.showBig = false;
      this.bigImg = undefined;
    },
    // 添加图片处理
    deelImg() {
      var _this = this;
      var file = _this.$refs.imgUploadFile;
      var fileObj = file.files[0]; // 获取文件对象
      file.value = "";
      if (fileObj.size / 1024 > 1025) {
        //大于1M，进行压缩
        _this.photoCompress(fileObj, { quality: _this.imgQuality }, function (base64Codes) {
          _this.imgStrArr.push(base64Codes);
        });
      } else {
        //小于等于1M
        var reader = new FileReader();
        reader.readAsDataURL(fileObj);
        reader.onload = function () {
          _this.imgStrArr.push(this.result); //this.result是base64编码
        };
      }
      _this.$emit("change", _this.imgStrArr);
    },
    /*
      三个参数
      file：文件(类型是图片格式)，
      obj：文件压缩后的宽度，宽度越小，字节越小
      callback：回调函数
      photoCompress()
      */
    photoCompress(file, obj, callback) {
      var _this = this;
      var ready = new FileReader();
      /*
        开始读取指定的Blob对象或File对象中的内容. 
        当读取操作完成时,readyState属性的值会成为DONE,
        如果设置了onloadend事件处理程序,则调用之.
        同时,result属性中将包含一个data: URL格式的字符串以表示所读取文件的内容.
      */
      ready.readAsDataURL(file);
      ready.onload = function () {
        var re = this.result;
        _this.canvasDataURL(re, obj, callback);
      };
    },
    //重新绘制图片
    canvasDataURL(path, obj, callback) {
      var img = new Image();
      img.src = path;
      img.onload = function () {
        var that = this;
        // 默认按比例压缩
        var w = that.width;
        var h = that.height;
        var scale = w / h;
        w = obj.width || w;
        h = obj.height || w / scale;
        var quality = 0.7; // 默认图片质量为0.7
        //生成canvas
        var canvas = document.createElement("canvas");
        var ctx = canvas.getContext("2d");
        // 创建属性节点
        var anw = document.createAttribute("width");
        anw.nodeValue = w;
        var anh = document.createAttribute("height");
        anh.nodeValue = h;
        canvas.setAttributeNode(anw);
        canvas.setAttributeNode(anh);
        ctx.drawImage(that, 0, 0, w, h);
        // 图像质量
        if (obj.quality && obj.quality <= 1 && obj.quality > 0) {
          quality = obj.quality;
        }
        // quality值越小，所绘制出的图像越模糊
        var base64 = canvas.toDataURL("image/jpeg", quality);
        // 回调函数返回base64的值
        callback(base64);
      };
    },
  },
};
</script>

<style>
.my-image {
  padding-top: 5px;
}
.my-image .image-item {
  display: inline-block;
  position: relative;
  width: 21%;
  height: 0;
  padding-bottom: 21%;
  margin: 1% 1% 0 2%;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  /* border-radius: 5px; */
  line-height: 1px;
}
.my-image .image-item .upload-img,
.my-image .image-item .input-file {
  position: absolute;
  width: 100%;
  height: auto;
  max-height: 100%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  /* border-radius: 4px; */
}
.my-image .image-item .input-file {
  height: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  z-index: 9997;
}

.my-image .image-item {
  position: relative;
}
.my-image .image-item .del {
  position: absolute;
  top: 6px;
  right: -5px;
}
.my-image .image-item .del .iconfont {
  color: #333;
  font-size: 1.6em;
  border-radius: 30px;
}

.my-image .image-item .add {
  position: absolute;
  left: 50%;
  top: 48%;
  transform: translate(-48%, -50%);
  color: #d9d9d9;
  z-index: 9996;
  font-size: 2.8em;
}
.wound-img .img-mask {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: #000;
  opacity: 0.75;
  z-index: 9997;
}
.wound-img .close {
  position: absolute;
  left: 45%;
  bottom: 8px;
  z-index: 9999;
  color: #fff;
}
.wound-img .close .iconfont {
  background-color: #ff0000;
  font-size: 3em;
  border-radius: 30px;
}
.wound-img .big-img {
  position: absolute;
  width: 85%;
  height: auto;
  max-height: 80%;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -45%);
  z-index: 9998;
}
</style>