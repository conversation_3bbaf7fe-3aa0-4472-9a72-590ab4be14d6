<!--
 * FilePath     : \src\components\table\dynamicTableSetting.vue
 * Author       : 来江禹
 * Date         : 2024-02-18 17:03
 * LastEditors  : 来江禹
 * LastEditTime : 2024-04-18 09:20
 * Description  : 用户自定义表格列操作组件
 * CodeIterationRecord:
 -->
<template>
  <div class="dynamic-table-setting">
    <el-popover placement="bottom" trigger="click">
      <el-button
        slot="reference"
        class="edit-button"
        icon="iconfont icon-system-setting colum-button"
        @click="showEditTableFlag = true"
      >
        编辑
      </el-button>
      <!-- <i  class="" @click=""></i> -->
      <div slot="default" class="content">
        <div class="header">
          自定义列
          <i class="iconfont icon-info" @click="showMessage()"></i>
          <el-button class="header-button" type="primary" icon="iconfont icon-save-button" @click="saveData()">
            保存
          </el-button>
          <el-button class="edit-button header-button" icon="iconfont icon-refresh" @click="recoverSetting()">
            恢复默认
          </el-button>
        </div>
        <el-table border stripe :data="value" ref="dynamicTable" class="dynamic-table" row-key="columnID" height="100%">
          <el-table-column label="勾选显示列" align="center" :width="convertPX(120)">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.chooseFlag" :disabled="scope.row.defaultShowFlag"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="列名" prop="tableColumnName" :min-width="convertPX(200)"></el-table-column>
          <el-table-column label="宽度" prop="width" :width="convertPX(400)">
            <template slot-scope="scope">
              <span>
                <el-input
                  v-model="scope.row.width"
                  style="width: 80px"
                  @blur="checkWidthValue(scope.row.width)"
                ></el-input>
                px
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-popover>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      saveFlag: true,
    };
  },
  async created() {
    await this.rowDrop();
  },
  methods: {
    /**
     * @description: 提示方法
     * @return
     */
    showMessage() {
      this._showMessage({
        message: "勾选需要显示的列，拖动列名进行排序。",
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },

    /**
     * @description: 恢复默认配置
     * @return
     */
    recoverSetting() {
      this.$emit("recoverSetting");
    },
    /**
     * @description: 保存数据
     * @return
     */
    saveData() {
      if (!this.saveFlag) {
        this._showTip("warning", "有宽度为空的数据，请填写宽度！");
        return;
      }
      this.$emit("saveData", this.value);
    },
    /**
     * description: 拖拽调用
     * param {*}
     * return {*}
     */
    rowDrop() {
      this.$nextTick(() => {
        let bodyDom = this.$refs.dynamicTable.$el.querySelector(".el-table__body-wrapper tbody");
        this._tableRowDrop(bodyDom, this, "value");
      });
    },
    /**
     * @description: 失焦检核宽度是否填写
     * @param width
     * @return
     */
    checkWidthValue(width) {
      if (!width) {
        this.saveFlag = false;
        this._showTip("warning", "请填写宽度！");
      } else {
        this.saveFlag = true;
      }
    },
  },
};
</script>
<style lang="scss">
.dynamic-table-setting {
  display: inline-block;
  .colum-button {
    margin-left: 10px;
    color: #14d8d8;
  }
  .content {
    margin: 30px;
    .header {
      margin: 5px 10px 20px 10px;
      .header-button {
        float: right;
        margin: 0px 10px;
      }
    }
  }
}
</style>

