<!--
 * FilePath     : \src\pages\transferHisCommon\TestTransfer.vue
 * Author       : 曹恩
 * Date         : 2022-04-05 09:58
 * LastEditors  : 曹恩
 * LastEditTime : 2022-04-05 16:18
 * Description  : 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { hisHisCommonUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
    }),
  },
  created() {
    this.url = hisHisCommonUrl() + "specimenTransfer?token=" + this.token;
  },
};
</script>