<!--
 * FilePath     : \ccc.web\src\pages\patientConsult\consultExhibition.vue
 * Author       : 郭自飞
 * Date         : 2020-05-25 17:14
 * LastEditors  : 郭自飞
 * LastEditTime : 2020-05-25 20:22
 * Description  : 会诊审批展示
--> 
<template>
  <div class="consult-exhibition">
    <div class="top">
      <div class="dag-div" v-for="(template, index) in columnData.children" :key="index">
        <span>目的:</span>
        <el-input :readonly="true" class="objective" v-model="template.mainContent"></el-input>
        <el-input :readonly="true" class="objective" v-model="template.detailContent"></el-input>
      </div>
      <div class="tip">
        <span class="tip-span">备注:</span>
        <el-input
          type="textarea"
          :readonly="true"
          class="consult-input"
          v-model="columnData.consultContent"
          resize="none"
        ></el-input>
      </div>
    </div>
  </div>
</template>
<script>
import { GetByPatientConsultID } from "@/api/PatientConsult";
export default {
  data() {
    return {
      columnData: {}
    };
  },
  mounted() {
    let patientConsultID = this.$route.query.patientConsultID;
    let params = {
      patientConsultID: patientConsultID
    };
    GetByPatientConsultID(params).then(res => {
      if (this._common.isSuccess(res)) {
        if (res.data.length > 0) {
          this.columnData = res.data[0];
        }
      }
    });
  }
};
</script>
<style lang='scss'>
.consult-exhibition {
  padding: 20px;
  background-color: #fff;
  height: 100%;
  .objective {
    width: 34.5%;
  }
  .top {
    margin-top: 5%;
  }
  .dag-div {
    margin-bottom: 15px;
    margin-left: 20%;
  }
  .tip {
    margin-left: 20%;
    height: 260px;
    margin-bottom: 10px;
    .tip-span {
      float: left;
    }
    .consult-input {
      width: 70%;
      height: 100%;
      .el-textarea__inner {
        margin-left: 5px;
        height: 100%;
      }
    }
  }
}
</style>