.plTableBox {

  .el-table {
    color: #000000;

    .el-table--border,
    .el-table--group {
      border: 1px solid #cccccc;
      box-sizing: border-box;
    }

    th {
      font-size: 14px;
    }

    .el-table__row td {
      padding: 1px;
      box-sizing: border-box;
      & > .umy-table-beyond {
        // position: sticky !important;
        // top: 0;
      }
    }

    &.el-table--striped tr.el-table__row--striped:hover td,
    .el-table__body tr:hover>td {
      background-color: #ffe0ac !important;
    }

    &.el-table--striped tr.el-table__row--striped.current-row td,
    .el-table__body tr.current-row td {
      background-color: #ffe0ac !important;

      // * {
      //   font-weight: 600;
      // }
    }

    /* 双层表头样式 */
    thead.is-group th {
      background: #ebf7df !important;
    }

    /* 嵌套表格样式 */
    .el-table__expanded-cell {
      .el-table__header-wrapper {
        th {
          background-color: #f1f1f6 !important;
        }
      }
    }

    .umy-table-beyond {
      position: static;
      white-space: normal !important;
    }
  }

  .elx-table.border--default .elx-table--header-wrapper,
  .elx-table.border--full .elx-table--header-wrapper,
  .elx-table.border--outer .elx-table--header-wrapper {
    background-color: #ebf7df !important;
    color: #000;
  }

  .elx-header--row {
    th {
      height: 30px !important;
      line-height: 30px;
      padding: 1px 4px !important;

      .elx-cell {
        padding: 3px;
      }
    }
  }

  .elx-table {
    color: #000000;

    .elx-body--row {
      &:hover {
        background-color: #ffe0ac !important;
      }

      &.row--checked,
      &.row--current,
      &.row--radio {
        background-color: #ffe0ac !important;

        * {
          font-weight: 600;
        }
      }
    }

    .elx-body--column.col--ellipsis,
    .elx-footer--column.col--ellipsis,
    .elx-header--column.col--ellipsis,
    &.elx-editable .elx-body--column {
      height: 30px !important;
      line-height: 30px;
      padding: 3px;
    }
  }
}