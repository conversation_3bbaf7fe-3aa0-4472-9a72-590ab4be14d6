<!--
 * FilePath     : \ccc.web\src\pages\transferPages\nursingBoardRelatedBed.vue
 * Author       : 郭鹏超
 * Date         : 2021-12-09 08:51
 * LastEditors  : 郭鹏超
 * LastEditTime : 2021-12-09 09:24
 * Description  : 医师床位关系维护
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
// 代码需要迁移，暂时串到nursing
import { getNursingBoard } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      user: "getUser",
    }),
  },
  created() {
    this.url =
      getNursingBoard() +
      "physicianRelatedBed?token=" +
      this.token +
      "&stationID=" +
      this.user.stationID +
      "&userID=" +
      this.user.userID;
  },
};
</script>