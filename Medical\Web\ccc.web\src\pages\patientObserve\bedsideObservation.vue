<!--
 * FilePath     : \src\pages\patientObserve\bedsideObservation.vue
 * Author       : 李正元
 * Date         : 2022-01-09 08:09
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-16 16:50
 * Description  : 2022-01-09 2225 病情观察改版新增页面 -正元
 * CodeIterationRecord: 2487-嘉会妇产科交班，需要将DT类型的标题拼接到值 2022-07-24 -杨欣欣
-->
<template>
  <base-layout
    class="patient-observe"
    v-loading="loading"
    :element-loading-text="loadingText"
    @keydown.native.ctrl.shift.83="toggleHidden(true)"
    @keyup.native.ctrl.shift.72="toggleHidden(false)"
  >
    <div slot="header" class="observe-header">
      <span class="add-title">观察时间:</span>
      <el-date-picker
        v-model="oberserDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        style="width: 120px"
        :clearable="false"
        placeholder="日期"
        @change="postClinicPrams"
      ></el-date-picker>
      <el-time-picker
        v-model="oberserTime"
        value-format="HH:mm"
        format="HH:mm"
        :clearable="false"
        style="width: 100px"
        @change="postClinicPrams"
      ></el-time-picker>
      <span v-if="templateData.evalutaionRecordsCode">预计评价时间:</span>
      <el-select
        v-if="templateData.evalutaionRecordsCode"
        style="width: 120px"
        @change="expectChange"
        v-model="expectID"
      >
        <el-option
          class="expect-select"
          v-for="item in expectDateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-date-picker
        v-if="templateData.evalutaionRecordsCode"
        v-model="expectEvalutionDate"
        :clearable="false"
        value-format="yyyy-MM-dd HH:mm"
        format="yyyy-MM-dd HH:mm"
        type="datetime"
        class="date-picker"
        style="width: 160px"
        @change="dayCheck"
      ></el-date-picker>
      <div class="btn">
        <el-button type="primary" v-if="showHidden" @click="updatePinyin">更新所有病情观察字母简拼</el-button>
        <el-checkbox v-model="informPhysician">通知医师</el-checkbox>
        <el-checkbox v-model="bringToShift">带入交班</el-checkbox>
        <lab-result-button :case-number="patientInfo.caseNumber"></lab-result-button>
        <el-button
          type="primary"
          v-if="observationShowOrderButtonFlag"
          icon="iconfont icon-view"
          @click="showPatientOrder = true"
        >
          医嘱查询
        </el-button>
        <el-button
          type="primary"
          icon="iconfont icon-nursing-assement"
          v-if="criticalSummaryFalg"
          @click="criticalSummaryMethod('openDialog')"
        >
          小结数据
        </el-button>
        <el-button type="primary" icon="iconfont icon-clinic1" @click="openClinic">仪器数据</el-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
        <el-button v-if="!scheduleFlag && !dialogFlag" class="print-button" icon="iconfont icon-back" @click="goBack">
          返回
        </el-button>
      </div>
    </div>
    <div class="observe-body">
      <div class="add-measures-left" v-if="!modify">
        <el-cascader
          style="width: 190px"
          v-model="templateType"
          :options="templateTypeList"
          @change="getTemplateList"
          :props="{
            expandTrigger: 'hover',
            value: 'id',
            label: 'name',
            children: 'children',
          }"
        ></el-cascader>
        <!-- 简拼搜索输入框 -->
        <pinyin
          @postData="getPinyinData"
          inputWidth="190"
          tableName="ObserveTemplate"
          v-model="inputData"
          class="pinyin-input"
        ></pinyin>
        <el-table
          @row-click="getTemplateText"
          class="template-table"
          height="calc(100% - 70px)"
          :data="templateList"
          border
          stripe
          highlight-current-row
          ref="templateTable"
        >
          <el-table-column prop="templateName" label="模板名称"></el-table-column>
        </el-table>
      </div>
      <div class="add-measures-right" :style="{ width: modify ? '100%' : 'calc(100% - 200px)' }">
        <div class="test-area" :style="{ height: inputHeight }">
          <div>
            <span class="add-title">病情观察:</span>
            <el-checkbox @change="autoText" class="auto-joint" v-model="autoJointFlag">自动拼接</el-checkbox>
          </div>
          <el-input
            v-if="inputRow"
            v-model="measuresText"
            class="measures-text"
            type="textarea"
            :autosize="{ minRows: inputRow, maxRows: inputRow }"
            resize="none"
            placeholder="请输入内容"
            maxlength="3900"
            show-word-limit
            @paste.native.capture.prevent="onPaste"
          ></el-input>
        </div>
        <tabs-layout
          ref="tabsLayout"
          class="assess-layout"
          :style="{ height: 'calc(100% - ' + inputHeight + ')' }"
          :template-list="templateDatas"
          check-flag
          :gender="patient ? patient.genderCode : ''"
          :splicedSelected="true"
          :excludeSplicedIDArr="excludeSplicedIDArr"
          :excludeSplicedGroupArr="excludeSplicedGroupArr"
          @checkTN="checkTN"
          @change-values="changeValues"
          @button-click="buttonClick"
          @button-record-click="buttonRecordClick"
          @splicedContent="getContent"
        />
      </div>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonRecordTitle"
      :visible.sync="showButtonRecordDialog"
      append-to-body
      custom-class="link no-footer"
    >
      <risk-component :params="conponentParams" @result="result"></risk-component>
    </el-dialog>
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      fullscreen
      append-to-body
      custom-class="link no-footer"
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
    </el-dialog>
    <!-- 小结弹出框 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      :visible.sync="criticalSummaryDialog"
      custom-class="bedside-observation-summary"
    >
      <div class="dialog-header">
        <span class="label">采用班别日期</span>
        <el-switch v-model="queryByShift" @change="initQueryParams" />
        <div v-show="queryByShift" class="shift-section">
          日期：
          <el-date-picker
            v-model="shiftDate"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择班别日期"
            class="date-picker"
          ></el-date-picker>
          <shift-selector
            :stationID="stationID"
            @select-item="changeShift($event, 'startShift')"
            v-model="startShift"
            label="班别："
            class="shift-selector"
          ></shift-selector>
        </div>
        <div v-show="!queryByShift" class="datetime-section">
          日期：
          <el-date-picker
            v-model="summaryStartTime"
            value-format="yyyy-MM-dd HH:mm"
            format="yyyy-MM-dd HH:mm"
            type="datetime"
            class="date-picker start-date-picker"
            placeholder="开始日期时间"
          ></el-date-picker>
          <span class="separator">至</span>
          <el-date-picker
            v-model="summaryEndTime"
            value-format="yyyy-MM-dd HH:mm"
            format="yyyy-MM-dd HH:mm"
            type="datetime"
            class="date-picker end-date-picker"
            placeholder="结束日期时间"
          ></el-date-picker>
        </div>

        <el-button
          type="primary"
          icon="iconfont icon-search"
          class="summary-button"
          @click="criticalSummaryMethod('getData')"
        >
          查询
        </el-button>
      </div>

      <div class="summary-div">
        <span>汇总内容</span>
        <el-input
          type="textarea"
          :autosize="{ minRows: 3 }"
          placeholder="请选择时间区间，点击查询获取小结数据！"
          v-model="criticalSummaryText"
        ></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="criticalSummaryDialog = false">取消</el-button>
        <el-button type="primary" @click="criticalSummaryMethod('splicingData')">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 措施触发措施 -->
    <el-dialog
      :modal-append-to-body="false"
      v-dialogDrag
      @closed="closeOrTransfer()"
      :close-on-click-modal="false"
      title="措施触发措施"
      :visible.sync="showTrigger"
    >
      <trigger-schedule v-if="showTrigger" :triggerParams="triggerParams" @close="closeOrTransfer()"></trigger-schedule>
    </el-dialog>
    <el-drawer
      title="仪器数据"
      :modal-append-to-body="false"
      :visible.sync="showClinicFlag"
      :destroy-on-close="true"
      direction="btt"
      size="60%"
      custom-class="clinic-drawer"
      :wrapperClosable="false"
    >
      <clinic-view
        height="190px"
        v-model="timeRange"
        :clinicPrams="clinicPrams"
        @getSelectClinicData="postClinicData"
      ></clinic-view>
    </el-drawer>
    <el-dialog
      :append-to-body="true"
      v-dialogDrag
      :close-on-click-modal="false"
      :title="`${patientInfo.bedNumber}床 - ${patientInfo.patientName} - 医嘱查询`"
      fullscreen
      :visible.sync="showPatientOrder"
    >
      <patient-orders v-if="showPatientOrder"></patient-orders>
    </el-dialog>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import tabsLayout from "@/components/tabsLayout/index";
import pinyin from "@/components/Pinyin";
import clinicView from "@/pages/schedule/components/scheduleTypes/clinicView";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import triggerSchedule from "@/pages/schedule/components/scheduleTypes/triggerSchedule.vue";
import shiftSelector from "@/components/selector/shiftSelector";
import { GetNowStationShiftData } from "@/api/StationShift";
import { mapGetters } from "vuex";
import {
  CreatePatientObservation,
  UpdatePatientObservation,
  ReadPatientObservation,
  DeleteDataByObserveSourceID,
  GetPatientObservationByScheduleMainID,
  GetCriticalSummaryData,
} from "@/api/PatientObservation";
import {
  GetObserveTemplateType,
  GetObserveTemplateTableData,
  GetObserveTemplateText,
  GetAllObservation,
} from "@/api/ObserveTemplate";
import { GetButtonData } from "@/api/Assess";
import { GetObserveJiontSetting, GetClinicSettingByTypeCode } from "@/api/Setting";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { SetAllObservatePinyin } from "@/api/PinyinListMaintain";
import labResultButton from "@/components/button/labResultButton";
import patientOrders from "@/pages/patientOrder/patientOrders";
export default {
  components: {
    baseLayout,
    tabsLayout,
    pinyin,
    clinicView,
    riskComponent,
    triggerSchedule,
    shiftSelector,
    labResultButton,
    patientOrders,
  },
  props: {
    params: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  watch: {
    "patientInfo.inpatientID": {
      handler(newValue) {
        if (!newValue) return;
        this.patient = this.patientInfo;
        this.init();
      },
      immediate: true,
    },
    assessContentText: {
      handler(newValue) {
        if (!this.autoJointFlag) {
          return;
        }
        this.getObserveText(newValue);
      },
    },
    showButtonDialog(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
    showTrigger(newValue) {
      if (!newValue) {
        this.$emit("refresh");
      }
    },
  },

  data() {
    return {
      showHidden: false,
      patient: undefined,
      //加载中
      loading: false,
      //加载呈现内容
      loadingText: "",
      //是否带交班
      bringToShift: false,
      //通知医师
      informPhysician: false,
      //观察时间
      oberserDate: undefined,
      oberserTime: undefined,
      //预计评价时间
      expectEvalutionDate: undefined,
      //简拼查询条件
      inputData: "",
      //观察措施内容
      measuresText: "",
      ///观察措施模板
      templateType: [],
      //定义好的样式内容
      templateList: [],
      //观察措施模板类型
      templateTypeList: [],
      //评估模版
      templateDatas: [],
      //默许取得仪器时间范围
      timeRange: 30,
      //获取仪器数据所需params
      clinicPrams: {},
      //观察表编号
      recordsCode: "",
      //观察评估内容
      assessDatas: [],
      checkTNFlag: true,
      buttonAssessListID: "",
      buttonName: "",
      showButtonDialog: false,
      // 显示BR类弹窗
      // 显示BR类弹窗
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR项目ID
      brAssessListID: undefined,
      //BR组件参数
      conponentParams: undefined,
      // 病情观察串BR类型
      sourceType: "Observation",
      buttonRecordLoading: false,
      buttonRecordLoadingText: "加载中……",
      //是否维修改状态
      modify: false,
      sourceID: undefined,
      templateData: {},
      expectID: -1,
      expectDateOptions: [
        {
          label: " ",
          value: -1,
        },
        {
          label: "自定义",
          value: 0,
        },
        {
          label: "十五分钟后",
          value: 15,
        },
        {
          label: "半小时后",
          value: 30,
        },
        {
          label: "一小时后",
          value: 60,
        },
        {
          label: "二小时后",
          value: 120,
        },
      ],
      showClinicFlag: false,
      assessContentText: "",
      scheduleFlag: false,
      autoJointFlag: false,
      oldDateTime: undefined,
      oldTemplateContent: undefined,
      dialogFlag: false,
      inputRow: undefined,
      inputHeight: "136px",
      //不许拼接内容数组
      excludeSplicedIDArr: [],
      excludeSplicedGroupArr: [],
      //危重小结弹窗开关
      criticalSummaryDialog: false,
      summaryStartTime: "",
      summaryEndTime: "",
      criticalSummaryText: "",
      criticalSummaryFalg: false,
      onlyStationFlag: false,
      showTrigger: false,
      triggerParams: {},
      queryByShift: true,
      startTime: "",
      endTime: "",
      startShift: "",
      shiftDate: "",
      shiftInfo: undefined,
      stationID: undefined,
      observeTemplateID: undefined,
      showPatientOrder: false,
      observationShowOrderButtonFlag: false,
    };
  },
  beforeMount() {
    this.getInputHeight();
    this.getnotSplicedContentIDSetting();
  },
  created() {
    // 设置不可切换病人
    this._sendBroadcast("setPatientSwitch", false);
  },
  methods: {
    //初始加载
    init() {
      this.keyReNew();
      this.getAutoJointFlag();
      //获取观察措施模板类型
      this.getTemplateTypeList();
      //默许当前科室模版
      this.getTemplateList();
      //加载仪器数据
      this.postClinicPrams();
      //小结按钮开关
      this.getCriticalSummarySwitch();
      //获取是否显示医嘱查询按钮配置
      this.getObservationShowOrderButtonFlag();
      //获取班别日期
      this.initQueryParams();
      //初始化赋值小结时间区间
      this.summaryStartTime = this._datetimeUtil.getNow("yyyy-MM-dd") + " 08:00";
      this.summaryEndTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
    },
    keyReNew() {
      //新增初始化
      this.oberserDate = this._datetimeUtil.getNow("yyyy-MM-dd");
      this.oberserTime = this._datetimeUtil.getNow("hh:mm");
      this.expectEvalutionDate = undefined;
      this.sourceID = this._common.guid();
      this.obervationID = undefined;
      this.bringToShift = false;
      this.informPhysician = false;
      this.modify = false;
      //是否为排程跳转标记
      this.scheduleFlag = false;
      let params = this.$route.query;
      this.observeTemplateID = this.$route.query.data?.observeTemplateID;
      // 组件形式，用父组件传的参数
      if (this.params) {
        params = this.params;
      }
      //修改初始化
      if (params) {
        this.dialogFlag = params.isDialog;
        //观察措施点击修改
        if (params.flag == "G") {
          this.getTemplateData(params.data);
        } else {
          //排程跳转修改
          if (params.patientScheduleMainID) {
            this.scheduleFlag = true;
            this.modify = true;
            this.getObserveBySchedule(params.patientScheduleMainID);
          }
          //排程执行处新增
          if (params.scheduleFlag) {
            this.scheduleFlag = params.scheduleFlag;
          }
        }
      }
    },
    async initQueryParams() {
      this.stationID = this.patient.stationID;
      if (this.queryByShift) {
        await this.getShiftDate();
      } else {
        let nowDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.startTime = nowDate + " 00:00";
        this.endTime = nowDate + " 23:59";
      }
    },
    async getShiftDate() {
      if (this.shiftInfo) {
        let date = this.shiftInfo.shiftDate;
        this.shiftDate = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
        return;
      }
      let params = {
        stationID: this.stationID,
      };
      await GetNowStationShiftData(params).then((res) => {
        if (this._common.isSuccess(res) && res.data) {
          this.shiftInfo = res.data;
          let date = this.shiftInfo.shiftDate;
          this.shiftDate = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
        }
      });
      // }
    },
    async changeShift(shift, typeName) {
      this[typeName] = shift?.id;
    },
    //排程跳转对接
    getObserveBySchedule(scheduleMainID) {
      let params = {
        scheduleMainID,
      };
      GetPatientObservationByScheduleMainID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.getTemplateData(res.data);
        }
      });
    },
    //修改时变量初始化
    getTemplateData(row) {
      this.templateData = {
        templateContent: row.text,
        recordsCode: row.recordsCode,
        observeTemplateID: row.observeTemplateID,
        evalutaionRecordsCode: row.evalutaionRecordsCode,
      };
      this.obervationID = row.id;
      this.oberserDate = this._datetimeUtil.formatDate(row.performDate, "yyyy-MM-dd");
      this.oberserTime = this._datetimeUtil.formatDate(row.performDate, "hh:mm");
      this.expectEvalutionDate = row.expectEvalutionDate;
      this.sourceID = row.sourceID;
      this.bringToShift = row.bringToShift;
      this.informPhysician = row.informPhysician;
      this.modify = true;
      this.measuresText = this.templateData.templateContent;
      this.getRecordTemplate();
    },
    //保存
    async save() {
      let params = {
        performDate: this.oberserDate + " " + this.oberserTime,
        expectEvalutionDate: this.expectEvalutionDate,
        inpatientID: this.patient.inpatientID,
        performText: this.measuresText,
        informPhysician: this.informPhysician,
        bringToShift: this.bringToShift ? "1" : "0",
        recordsCode: this.templateData.recordsCode,
        EvalutionRecordsCode: this.templateData.evalutaionRecordsCode,
        sourceID: this.sourceID,
        observeTemplateID: this.templateData.observeTemplateID,
        id: this.obervationID,
      };
      if (!this.saveCheck(params)) {
        return;
      }
      params.details = this.getDetails();
      if (!params.details?.length && (!params?.performText || !params?.performText.trim())) {
        this._showTip("warning", "请填写病情观察内容后保存！");
        return;
      }
      this.loading = true;
      this.loadingText = "保存中……";
      let ret = this.obervationID ? UpdatePatientObservation(params) : CreatePatientObservation(params);
      await ret.then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data && typeof res.data != "boolean") {
            this.triggerParams = {
              triggerDate: this._datetimeUtil.formatDate(res.data.triggerDate, "yyyy-MM-dd"),
              triggerTime: res.data.triggerTime,
              triggerDatas: res.data.attachedIntervention,
              index: Math.random(),
            };
            this.showTrigger = true;
          } else {
            this.closeOrTransfer();
          }
        }
      });
    },
    //保存检核
    saveCheck(params) {
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      //后端出现observeTemplateID为空数据,并且病情观察有模版ID,不是来源于事件添加的 添加前端检核
      if (!params.observeTemplateID && this.observeTemplateID) {
        this._showTip("warning", "病情观察模板有误！，请联系工程师处理");
        return false;
      }
      if (!this.expectEvalutionDate && this.templateData.evalutaionRecordsCode) {
        this._showTip("warning", "请填写预计评价时间!");
        return false;
      }
      return true;
    },
    getDetails() {
      let details = [];
      this.assessDatas.forEach((item) => {
        if (item.disableGroup.indexOf(-1) == -1) {
          if (item.controlerType == "R" || item.controlerType == "C") {
            item.assessValue = item.itemName;
          }
          if (item.controlerType == "DT") {
            item.assessValue = item.itemName + ": " + item.assessValue;
          }
          let detail = {
            AssessListID: item.assessListID,
            AssessValue: item.assessValue,
            AssessListGroupID: item.assessListGroupID,
            AssessValueJson: item.type === "BD" ? item.assessValue : undefined,
            ControlerType: item.controlerType ? item.controlerType : undefined,
          };
          details.push(detail);
        }
      });
      return details;
    },
    //返回上一层
    async goBack() {
      if (!this.modify) {
        // 新增未保存，点击返回时需要把模板中的BR、B串出的数据删除
        await this.deleteDataByObserveSourceID();
      }
      this.$router.push({
        name: "patientObserve",
      });
    },
    // 删除模板中的BR、B串出的数据
    async deleteDataByObserveSourceID() {
      let params = {
        sourceID: this.sourceID,
        sourceType: this.sourceType,
      };
      this.loading = true;
      this.loadingText = "处理中……";
      // 有则删除，没有返回true
      await DeleteDataByObserveSourceID(params).then((ret) => {
        this.loading = false;
      });
    },
    //日期检核
    dayCheck() {
      if (this.expectEvalutionDate < this.oberserDate + " " + this.oberserTime) {
        this._showTip("warning", "评价时间不得早于观察时间");
        // this.expectEvalutionDate = undefined;
        this.expectID = -1;
      }
    },
    //简拼查询
    getPinyinData(value) {
      if (value.length == 0) {
        this.templateList = [];
        return;
      }
      //无模板类型简拼查询无效
      if (!this.templateTypeList.length) {
        this._showTip("warning", "无模板数据");
        return;
      }
      //属性名转换
      value.forEach((item) => {
        item.templateName = item.name;
        if (item.indexID == 0) {
          item.observeTemplateID = item.indexFieldID;
        } else {
          item.observeTemplateID = item.indexID;
        }
        delete item.name;
        delete item.indexID;
      });
      this.templateList = value;
    },
    //获取观察措施模板表格数据
    async getTemplateList(value) {
      await this.getObserveTemplateStationFlag();
      let params = {
        templateCategory: "",
        onlyStationFlag: this.onlyStationFlag,
      };
      if (value) {
        params.templateCategory = value[1];
        let type = this.templateTypeList.find((type) => type.id == value[0]);
        if (type && type.stationFlag) {
          params.stationListID = value[0];
        } else {
          params.departmentListID = value[0];
        }
      } else {
        params.stationListID = this.patient.stationID;
        params.departmentListID = this.patient.departmentListID;
      }
      GetObserveTemplateTableData(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.templateList = res.data;
          if (this.templateList && this.templateList.length > 0 && this.$refs.templateTable) {
            this.$refs.templateTable.setCurrentRow(this.templateList[0]);
            this.getTemplateText(this.templateList[0]);
          }
        }
      });
    },
    //获取观察措施模板
    getTemplateText(row) {
      this.clinicTemplateID = row.observeTemplateID;
      let params = {
        templateID: row.observeTemplateID,
        inpatientID: this.patient.inpatientID,
      };
      this.monitorFlag = false;
      let oldRecordsCode = this._common.clone(this.templateData.recordsCode);
      GetObserveTemplateText(params).then(async (res) => {
        if (this._common.isSuccess(res) && res.data) {
          let isFirst = false;
          if (!this.templateData.recordsCode) {
            isFirst = true;
          }
          this.templateData = res.data;
          //未配置模板置空评估内容
          if (!this.templateData.recordsCode) {
            this.templateDatas = [];
          }
          this.oldDateTime = undefined;
          this.getTemplateInputText();
          this.monitorFlag = res.data.monitorFlag == "1" ? true : false;
          // 只有第一次或新模板和旧模板不一致才重新获取模板
          //let oldRecordsCode = res.data.recordsCode;
          if (isFirst || this.templateData.recordsCode != oldRecordsCode) {
            await this.getRecordTemplate();
            if (this.templateData.recordsCode != oldRecordsCode) {
              // 如果切换模板，需要把上个模板中的BR、B串出的数据删除
              await this.deleteDataByObserveSourceID();
            }
          }
        }
      });
    },
    //系统时间更新
    getTemplateInputText() {
      this.measuresText = this.templateData.templateContent;
      this.templateTimeReplace();
    },
    templateTimeReplace() {
      if (!this.measuresText) {
        return;
      }
      if (this.modify && !this.oldDateTime) {
        this.oldDateTime = this._datetimeUtil.formatDate(this.oberserDate + " " + this.oberserTime, "yyyy-MM-dd hh:mm");
      }
      if (this.oldDateTime) {
        this.measuresText = this.measuresText.replace(this.oldDateTime, this.oberserDate + " " + this.oberserTime);
      } else {
        this.measuresText = this.measuresText.replace("@系统时间@", this.oberserDate + " " + this.oberserTime);
      }
      this.oldDateTime = this.oberserDate + " " + this.oberserTime;
    },
    //病情观察输入框评估内容组装
    getObserveText() {
      let content = this.fixAssessTemplateContent();
      if (content) {
        this.measuresText = this.measuresText + (this.measuresText.endsWith(" ") && content ? "" : " ") + content;
      } else {
        this.measuresText = this.measuresText + "";
      }
    },

    //评估组件数据加工
    fixAssessTemplateContent() {
      let clonAssessContentText = this._common.clone(this.assessContentText);
      if (this.modify && !this.oldTemplateContent) {
        this.oldTemplateContent = clonAssessContentText;
      }
      if (this.oldTemplateContent) {
        this.measuresText = this.measuresText.replace(this.oldTemplateContent, "");
      }
      this.oldTemplateContent = clonAssessContentText;
      return clonAssessContentText;
    },
    //评估组件返回组装数据数据
    getContent(content) {
      this.assessContentText = content;
    },
    //获取观察措施模板类型
    getTemplateTypeList() {
      let params = {
        stationID: this.patient.stationID,
      };
      GetObserveTemplateType(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.templateTypeList = res.data;
        }
      });
    },
    //取得仪器数据
    postClinicPrams() {
      //重置输入框时间
      this.templateTimeReplace();
      this.clinicPrams = {
        inpatientID: this.patient.inpatientID,
        scheduleDate: this._datetimeUtil.formatDate(this.oberserDate, "yyyy-MM-dd"),
        scheduleTime: this._datetimeUtil.formatDate(this.oberserTime, "hh:mm:ss"),
      };
    },
    //勾选仪器数据事件,回传仪器数据到监测表格数据
    postClinicData(value) {
      //Object.entries 将对象变为数组形式为[[key,value],[key,value]]
      let cinicDataArr = Object.entries(value);
      //评估模版赋值
      cinicDataArr.forEach((item) => {
        if (item[1]) {
          this.$refs.tabsLayout.setItemValue(item[0], item[1]);
        }
      });
      this.showClinicFlag = false;
    },
    //取得观察措施模版
    async getRecordTemplate() {
      this.assessDatas = [];
      let params = {
        inpatientID: this.patient.inpatientID,
        chartNo: this.patient.chartNo,
        recordsCode: this.templateData.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentID: this.patient.departmentListID,
        dateOfBirth: this.patient.dateOfBirth,
        stationID: this.patient.stationID,
        sourceType: this.sourceType,
        sourceID: this.sourceID,
      };
      this.loading = true;
      this.loadingText = "加载中……";
      this.templateDatas = [];
      //取得历史内容
      this.templateDatas = [];
      await ReadPatientObservation(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },

    //评估模版内容变更事件
    changeValues(datas) {
      this.assessDatas = datas;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    keydownSave() {
      this.$refs.record.save();
    },
    buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += (url.includes("?") ? "&" : "?") + "isDialog=true";
      url +=
        `&sourceID=${this.sourceID}` +
        `&sourceType=${this.sourceType}_${content.assessListID}` +
        `&bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}` +
        `&userID=${this.user.userID}` +
        `&token=${this.token}`;
      this.showButtonDialog = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 初始化BR组件
     * return {*}
     * param {*} content BR跳转风险项
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      this.conponentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: this.sourceType,
        sourceID: this.sourceID,
        assessTime:
          this._datetimeUtil.formatDate(this.oberserDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.oberserTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    result(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brAssessListID);
      }
    },

    // 添加完更新按钮数据
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    //更新按钮回显数据
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.templateData.recordsCode,
        assessListID: assessListID,
        sourceID: this.sourceID,
        sourceType: this.sourceType + "_" + assessListID,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    expectChange() {
      if (this.expectID == -1) {
        this.expectEvalutionDate = undefined;
      } else if (this.expectID == 0) {
        this.expectEvalutionDate = this.oberserDate + " " + this.oberserTime;
      } else {
        this.expectEvalutionDate = this._datetimeUtil.addMinutes(
          this.oberserDate + " " + this.oberserTime,
          this.expectID,
          "yyyy-MM-dd hh:mm"
        );
      }
    },
    openClinic() {
      this.showClinicFlag = true;
    },
    //获取是否默认拼接评估内容
    getAutoJointFlag() {
      let params = {
        special: "Observe",
      };
      GetObserveJiontSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.autoJointFlag = res.data;
        }
      });
    },
    /**
     * description: 获取输入框配置高度
     * param {*}
     * return {*}
     */
    getInputHeight() {
      let params = {
        settingTypeCode: "ObserveInputHeight",
      };
      GetClinicSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data || res.data.length) {
            this.$nextTick(() => {
              this.$set(this, "inputRow", res.data[0].typeValue);
              // this.inputRow = Number(res.data[0].typeValue);
              this.inputHeight = res.data[0].settingValue;
            });
          }
        }
      });
    },
    /**
     * description: 自动拼接事件
     * return {*}
     */
    autoText() {
      if (!this.autoJointFlag) {
        return;
      }
      this.getObserveText(this.assessContentText);
    },
    /**
     * description: 获取不需要拼接评估内容配置
     * param {*}
     * return {*}
     */
    getnotSplicedContentIDSetting() {
      let params = {
        settingTypeCode: "AssessContentNotSplicedArr_Observe",
      };
      GetClinicSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data && res.data.length) {
            let excludeSplicedIDArr = res.data.find((m) => m.typeValue == "AssessListIDArr");
            this.excludeSplicedIDArr =
              excludeSplicedIDArr && excludeSplicedIDArr.settingValue
                ? excludeSplicedIDArr.settingValue.split("||").map(Number)
                : [];
            let excludeSplicedGroupArr = res.data.find((m) => m.typeValue == "AssessGroupIDArr");
            this.excludeSplicedGroupArr =
              excludeSplicedGroupArr && excludeSplicedGroupArr.settingValue
                ? excludeSplicedGroupArr.settingValue.split("||")
                : [];
          }
        }
      });
    },
    /**
     * description:获取小结数据按钮开关配置
     * return {*}
     */
    async getCriticalSummarySwitch() {
      let param = {
        SettingTypeCode: "CriticalSummarySwitch",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.criticalSummaryFalg = response.data;
        }
      });
    },
    /**
     * description: type=openDialog:小结按钮点击事件 || type=getData 搜索事件从后端API获取小结数据
     * param {*} type
     * return {*}
     */
    criticalSummaryMethod(type) {
      if (type == "openDialog") {
        this.criticalSummaryText = "";
        this.criticalSummaryDialog = true;
        return;
      }
      if (type == "splicingData") {
        this.measuresText += this.criticalSummaryText;
        this.criticalSummaryDialog = false;
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      if (this.queryByShift) {
        if (!this.shiftDate || !this.startShift) {
          this._showTip("warning", "请选择日期班别！");
          return;
        }
        params.startTime = this.shiftDate;
        params.startShift = this.startShift;
      } else {
        if (!this.summaryStartTime || !this.summaryEndTime) {
          this._showTip("warning", "请选择时间！");
          return;
        }
        params.startTime = this.summaryStartTime;
        params.endTime = this.summaryEndTime;
      }
      GetCriticalSummaryData(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.criticalSummaryText = result.data;
        }
      });
    },
    /**
     * description: 隐藏按钮函数
     * param {*} flag
     * return {*}
     */
    toggleHidden(flag) {
      if (flag) {
        this.showHidden = true;
      } else {
        this.showHidden = false;
      }
    },
    /**
     * description: 更新病情观察类模板拼音简写
     * param {*}
     * return {*}
     */
    async updatePinyin() {
      let paramList = [];
      //先获取所有的模板数据
      await GetAllObservation().then((res) => {
        if (this._common.isSuccess(res)) {
          res.data.forEach((element) => {
            let signle = {
              Key: element.observeTemplateID,
              Value: this._common.initial(element.templateName),
            };
            paramList.push(signle);
          });
        }
      });
      let params = {
        keyValueStrings: JSON.stringify(paramList),
      };
      await SetAllObservatePinyin(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data) {
            this._showTip("success", "修改成功");
            this.getPinyinIndexData();
          } else {
            this._showTip("warning", "修改失败");
          }
        }
      });
    },
    /**
     * description: 获取是否只查询本病区配置开关
     * return {*}
     */
    async getObserveTemplateStationFlag() {
      let param = {
        SettingTypeCode: "GetObserveTemplateOnlyStation",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.onlyStationFlag = response.data;
        }
      });
    },
    closeOrTransfer() {
      if (this.dialogFlag) {
        // 如果是组件，通知父组件关闭
        this.$emit("close");
      } else {
        this.$router.push({
          name: "patientObserve",
        });
      }
    },
    /**
     * @description: 粘贴事件
     * @param {ClipboardEvent} e 剪贴板事件
     */
    onPaste(e) {
      e.preventDefault();
      let text;
      let clp = (e.originalEvent || e).clipboardData;
      // 获取剪贴板数据
      if (clp === undefined || clp === null) {
        text = window.clipboardData.getData("text") || "";
      } else {
        text = clp.getData("text/plain") || ""; // 获取纯文本内容
      }
      // 清除html标签
      if (text) {
        text = this.clearTextStyle(text);
      }
      if (!text) {
        return;
      }
      // 粘贴到输入框
      this.pasteSimpleText(clp, text);
    },
    /**
     * @description: 清除html标签
     * @param {string} text 输入内容
     * @return {string} 清除后的内容
     */
    clearTextStyle(text) {
      const htmlTagRegex = /<[^>]+>/g;
      if (htmlTagRegex.test(text)) {
        text.replace(/<[^>]+>/g, "");
      }
      return text;
    },
    /**
     * @description: 粘贴到输入框
     * @param {ClipboardEvent} clp 剪贴板事件
     * @param {string} text 粘贴板内容
     */
    pasteSimpleText(clp, text) {
      if (clp === undefined || clp === null) {
        if (window.getSelection) {
          var newNode = document.createElement("span");
          newNode.innerHTML = text;
          window.getSelection().getRangeAt(0).insertNode(newNode);
        } else {
          document.selection.createRange().pasteHTML(text);
        }
        return;
      }
      document.execCommand("insertText", false, text);
    },
    /**
     * @description: 获取是否显示医嘱查询按钮配置
     * @return
     */
    getObservationShowOrderButtonFlag() {
      let param = {
        SettingTypeCode: "ObservationShowOrderButtonFlag",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.observationShowOrderButtonFlag = response.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.patient-observe {
  height: 100%;
  .observe-header {
    .btn {
      float: right;
    }
  }
  .observe-body {
    height: 100%;
    .add-measures-left {
      float: left;
      width: 190px;
      height: 100%;
    }
    .add-measures-right {
      float: right;
      height: 100%;
      width: calc(100% - 190px);
      .add-parameter {
        background: white;
      }
      .test-area {
        height: 96px;
        background: white;
        margin: 0 5px;
        .auto-joint {
          float: right;
          margin-right: 10px;
        }
      }
      .add-title {
        margin-left: 5px;
      }
      .assess-layout {
        height: calc(100% - 96px);
        width: calc(100% - 5px);
      }
    }
  }
}
.el-dialog.no-footer.link .el-dialog__body {
  background-color: #f3f3f3;
  iframe {
    height: 99%;
    border: none;
  }
}
.el-dialog.bedside-observation-summary {
  height: 40%;
  width: 55%;
  .dialog-header {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    .summary-button {
      height: 25px;
      margin-left: auto;
    }
  }
  .summary-div {
    padding: 30px 0 0;
  }
  .el-dialog__body {
    height: calc(100% - 65px);
  }
  .date-picker {
    width: 150px;
    margin-right: 10px;
  }
  .shift-section,
  .datetime-section {
    display: flex;
    align-items: center;
  }
  .shift-selector {
    margin-right: 10px;
  }
  .separator {
    margin: 0 10px;
    white-space: nowrap;
  }
}
</style>
