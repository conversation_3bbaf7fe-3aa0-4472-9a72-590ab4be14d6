/*
 * FilePath     : \src\utils\huiMei.js
 * Author       : 张现忠
 * Date         : 2021-05-31 15:35
 * LastEditors  : 张现忠
 * LastEditTime : 2021-06-20 11:40
 * Description  :
 */
import http from "@/utils/ajax";
import common from "@/utils/common";
import { getHuiMei } from "@/utils/setting";
import datetimeUtil from "@/utils/datetimeUtil";
const urls = {
  GetPatientDataByCaseNumber: "/InPatient" + "/GetPatientDataByCaseNumber"
};

var huiMei = undefined;

// 根据病人caseNumber获取病人信息
const GetPatientDataByCaseNumber = params => {
  return http.get(urls.GetPatientDataByCaseNumber, params);
};

export const SendRequestParams = () => {
  if (!window.Mayson || window.Mayson == null) {
    return;
  }
  //创建智能推荐请求参数
  window.Mayson.setMaysonBean(window.commonParam);
  //发送推荐请求
  window.Mayson.ai();
};

//切换智能推荐的病人
export const PatientAuthority = (inpatient, employee) => {
  if (!huiMei || !huiMei.isOpen) {
    return;
  }
  if (typeof HM == "undefined") {
    return;
  }
  //获得病人信息
  GetPatientDataByCaseNumber({
    caseNumber: inpatient.caseNumber
  }).then(res => {
    if (!res.data || res.data == null) {
      return;
    }
    let patientInfo = res.data;
    //复制一个认证参数对象
    let autherEntity = {};
    //更新认证信息
    autherEntity.userGuid = inpatient.ChartNO;
    autherEntity.serialNumber = inpatient.caseNumber;
    autherEntity.department = patientInfo.departmentCode;
    autherEntity.departmentName = patientInfo.departmentName;
    autherEntity.doctorGuid = employee.userID;
    autherEntity.doctorName = employee.userName;
    //组织机构编码
    autherEntity.hospitalGuid = huiMei.hospitalGuid;
    //医院名称
    autherEntity.hospitalName = huiMei.hospitalName;
    //认证密钥
    autherEntity.autherKey = huiMei.autherKey;
    //应用场景：1住院
    autherEntity.customEnv = "1";
    //接入类型：m住院版
    autherEntity.flag = "m";
    // 浮窗距离右侧距离
    autherEntity.locationRight = 400;
    //组装公共病人参数 --每次的病人切换，更新一次公共病人参数
    window.commonParam = SetCommonParam(patientInfo, employee);
    //初始化智能推荐
    _init(autherEntity, window.commonParam);
  });
};

/**
 * description: 加载惠每jssdk脚本
 * params {*}
 * return {*}
 */
export const LoadHuiMeiJssdk = () => {
  huiMei = getHuiMei();
  //如果没有配置，或者配置为false，直接返回
  if (!huiMei || !huiMei.isOpen) {
    return;
  }
  //动态加载js
  LoadJs(huiMei.url);
  //30秒后重新查看jssdk是否已经成功加载
  setTimeout(() => {
    //二次加载jssdk
    if (typeof HM === `undefined`) {
      LoadJs(huiMei.url);
    }
  }, 30000);
};

/**
 * description: 惠每智能推荐-初始化方法
 * params {*}
 * return {*}
 */
const _init = (autherEntity, resultParams) => {
  //客户信息认证-初始化方法，每次加载只执行一次
  //返回一个全局变量
  HM.maysonLoader(autherEntity, function (mayson) {
    window.Mayson = mayson;
    //设置接入住院版浮窗对接方案
    mayson.setDrMaysonConfig("m", 1);
    //创建智能推荐请求参数
    mayson.setMaysonBean(resultParams);
    //发送推荐请求
    mayson.ai();

    /**
     * description: 回写数据的方法,监听mayson传递数据
     * params {*}
     * return {*}
     * param {*} data：当前需要传递的数据
     */
    mayson.listenViewData = function (data) {
      if (data) {
        for (var i = 0; i < data.length; i++) {
          var perData = data[i];
          if (perData.type == 11) {
            //type=11:惠每文献，返回url路径
            if (perData.items) {
              for (var j = 0; j < perData.items.length; j++) {
                window.open(perData.items[j].text);
              }
            }
          } else {
            //文献外的其他类型，直接做回显处理
            if (perData.items) {
              for (var j = 0; j < perData.items.length; j++) {
                window.alert(perData.items[j].text);
              }
            }
          }
        }
      }
    };
  });
};

//组装公共参数的函数
const SetCommonParam = (patient, employee) => {
  //定义公共入参(基本信息)
  let commonParam = {
    //患者编号(患者唯一标识) -对应中山的ChartNo
    userGuid: patient.chartNo,
    //住院就诊编号（一次住院产生的唯一标识） -对应中山的CaseNumber
    serialNumber: patient.caseNumber,
    //病人姓名
    patientName: patient.patientName,
    //医生编码（必填，否则无质控提示）
    doctorGuid: employee.userID,
    //医生名称
    doctorName: employee.userName,
    //病案号
    caseNo: patient.caseNumber,
    //入院时间(格式：yyyy-MM-dd  HH:mm:ss 或  yyyy-MM-dd)
    admissionTime: patient.admissionDate,
    //当前科室名称
    inpatientDepartment: patient.departmentName,
    //当前科室编码
    inpatientDepartmentId: patient.departmentCode,
    //当前病区名称
    inpatientArea: patient.stationName,
    //当前病区编码
    inpatientAreaId: patient.stationCode,
    //当前床号
    currentBedCode: patient.bedNumber,
    //开启提醒功能，默认为1 -非必要
    openInterdict: 1,
    //页面来源 6代表护理页面
    pageSource: 6,
    // 触发来源：1、医生端 2、护士端
    triggerSource: 2,
    //患者信息
    patientInfo: {
      //性别 0女，1男, 2 其他
      gender: patient.gender == 2 ? 0 : patient.gender == 1 ? 1 : 2,
      birthDate: datetimeUtil.formatDate(patient.dateOfBirth, 'yyyy-MM-dd'),
      //年龄 -非必要
      age: parseInt(patient.ageDetail),
      //取值类型：岁、月、天 -非必要
      ageType: "岁"
    },
    nursingRecordList: [
      {
        recordGuid: patient.caseNumber,
        //入院护理记录:2001  日常护理记录:2002  出院护理记录:2003  体温单:2004  护理评估记录:2005
        //参数是对方是对方要求默认的
        recordType: 2004,
        // 暂时屏蔽,跟中山保持一致（调试模式，没有这几个参数，参数检核不通过，如果正式不正常弹窗，将屏蔽打开）
        // recordTemplateName:"体温单",
        // msgType:2,
        // recordTime:datetimeUtil.getNow(),
        // doctorGuid:employee.userID,
      }
    ]
  };
  return commonParam;
};

//动态加载Js
const LoadJs = url => {
  common.LoadJs(url, "huiMei");
};
