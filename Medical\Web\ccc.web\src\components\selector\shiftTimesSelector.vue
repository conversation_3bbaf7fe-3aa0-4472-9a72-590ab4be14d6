<template>
  <div class="shift-time-selector">
    <span :class="{ label: label }">{{ label }}</span>
    <el-select :disabled="disabled" v-model="selected" placeholder="请选择时间段" @change="changeValue" :style="style">
      <el-option
        v-for="(shiftTime, index) in shiftTimes"
        :key="index"
        :label="shiftTime"
        :value="shiftTime"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
import { GetShiftTimeLine } from "@/api/Setting";
export default {
  props: {
    label: {
      type: String,
      default: "时间：",
    },
    value: {},
    disabled: {
      type: Boolean,
      default: false,
    },
    shiftID: {
      required: true,
    },
    width: {
      type: String,
      default: "120px",
    },
  },
  watch: {
    shiftID: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal) {
          if (oldVal && !this.disabled && this.selected) {
            this.$emit("input", undefined);
          }
          this.init();
        }
      },
    },
    value: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal) {
          this.selected = newVal;
          this.changeValue(this.selected);
        }
      },
    },
  },
  data() {
    return {
      selected: "",
      shiftTimes: [],
    };
  },
  computed: {
    style() {
      return {
        width: this._convertUtil.getHeigt(this.width, true),
      };
    },
  },
  methods: {
    init() {
      if (this.shiftID) {
        this.$emit("input", undefined);
        let params = {
          shiftID: this.shiftID,
          index: Math.random(),
        };
        GetShiftTimeLine(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.shiftTimes = result.data;
            if (this.shiftTimes && this.shiftTimes.length > 0 && !this.value) {
              let temp = this.shiftTimes.find((timeRange) => {
                let times = timeRange.split("-");
                let beginTime = this._datetimeUtil.formatDate(times[0], "hh:mm");
                let endTime = this._datetimeUtil.formatDate(times[1], "hh:mm");
                let nowTime = this._datetimeUtil.getNowTime("hh:mm");
                return nowTime >= beginTime && nowTime <= endTime;
              });
              if (temp) {
                this.selected = temp;
              } else {
                this.selected = this.shiftTimes[0];
              }
              this.changeValue(this.selected);
            }
          }
        });
      }
    },
    changeValue(shiftTimes) {
      this.$emit("input", shiftTimes);
      this.$emit("select", shiftTimes);
      //添加时间初始化结束事件
      // this.$emit('initComplete');
    },
  },
};
</script>
<style lang="scss">
.shift-time-selector {
  display: inline-block;
  .label {
    margin-left: 5px;
  }
  .el-select {
    .el-input.is-disabled {
      .el-input__inner {
        color: #606266;
      }
    }
    .el-input__inner {
      padding-left: 5px;
    }
  }
}
</style>
