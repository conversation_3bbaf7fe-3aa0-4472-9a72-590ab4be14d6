<!--
 * FilePath     : \src\autoPages\patientDeliveryRecord\index.vue
 * Author       : 杨欣欣
 * Date         : 2023-03-10 11:07
 * LastEditors  : 马超
 * LastEditTime : 2025-06-15 14:44
 * Description  : 生产全流程记录专项
 * CodeIterationRecord:
-->
<template>
  <div class="patient-delivery-record">
    <!-- 主记录 -->
    <div ref="mainRecord" class="main-record" v-loading="loading">
      <div ref="mainRecordHeader" class="main-record-header" v-if="!deliveryRecords.length">
        <el-button class="add-button" @click="openCareMains()" icon="iconfont icon-add">新增</el-button>
      </div>
      <packaging-table
        ref="mainTable"
        v-model="deliveryRecords"
        class="main-record-content"
        :headerList="mainTableHeader"
        @rowClick="openCareMains"
      >
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope">
          <el-tooltip content="删除">
            <div @click.stop="deleteRecord(scope.row)" class="iconfont icon-del"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 弹窗显示维护记录 -->
    <el-drawer
      :title="drawerSetting.title"
      :visible.sync="drawerSetting.showFlag"
      :direction="drawerSetting.direction"
      :size="drawerHeight"
      custom-class="main-drawer"
      :wrapperClosable="false"
      :modal-append-to-body="true"
      :append-to-body="false"
      :before-close="resetPage"
    >
      <!-- 维护记录切换组件 -->
      <main-component-switch
        ref="careMainSwitch"
        @refreshCurrentRecord="getRecords(true)"
        :mainSaveParams="mainSaveParams"
        @setInpatientInfo="setInpatientInfo"
        :parentDrawerHeight="drawerHeight"
      />
    </el-drawer>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { GetPatientDeliveryRecordRecordViews, DeletePatientDeliveryRecordRecord } from "@/api/PatientDeliveryRecord";
import mainComponentSwitch from "@/autoPages/patientDeliveryRecord/mainComponentSwitch";
export default {
  components: {
    baseLayout,
    packagingTable,
    mainComponentSwitch,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  provide() {
    return {
      // 提供主记录抽屉状态
      parentDrawerState: () => this.drawerSetting.showFlag,
    };
  },
  data() {
    return {
      componentName: "preDelivery",
      // 排程跳转，自动选中对应页签
      linkRecordsCode: undefined,
      patient: undefined,
      loading: false,
      drawerSetting: {
        showFlag: false,
        direction: "btt",
        title: "",
      },
      scheduleMainID: undefined,
      //主记录表格数据
      deliveryRecords: [],
      //主记录动态表头
      mainTableHeader: [],
      //是否能编辑删除该数据
      showEditButton: true,
      //补录标记
      refillFlag: undefined,
      mainSaveParams: {},
      drawerHeight: "",
    };
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      user: "getUser",
      specialDrawerHeightSwitch: "getSpecialDrawerHeightSwitch",
    }),
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = undefined;
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.refillFlag = "*";
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          // 设置能否切换病人
          const isLink = Object.keys(this.$route.query).length;
          const isShortCut = this.$route.query.shortCutFlag;
          this._sendBroadcast("setPatientSwitch", !isLink || isShortCut);

          if (this.$route.query.scheduleMainID != "null") {
            this.scheduleMainID = this.$route.query.scheduleMainID;
          }
          this.init();
        }
      },
      immediate: true,
    },
    "drawerSetting.showFlag": {
      handler(newVal) {
        this.calcDrawerSize();
      },
    },
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.setDrawerDirection();
      })();
    };
    this.setDrawerDirection();
  },
  methods: {
    /**
     * description: 初始化页面
     * return {*}
     */
    init() {
      this.getRecordTableHeader();
      this.getRecords();
      this.linkRecordsCode = this.$route.query.recordsCode;
      if (this.linkRecordsCode) {
        const row = this.deliveryRecords ? this.deliveryRecords[0] : undefined;
        this.openCareMains(row);
      }
    },
    /**
     * description: 计算抽屉高度
     * return {*}
     */
    calcDrawerSize() {
      //抽屉打开方式不是从下往上
      if (this.drawerSetting.direction != "btt") {
        this.drawerHeight = "80%";
      }
      this.$nextTick(() => {
        // 配置弹窗高度是否遮挡主记录，存在配置==遮挡
        if (this.specialDrawerHeightSwitch) {
          this.drawerHeight = this.$refs.mainRecord.offsetHeight + "px";
        }
        // 获取表格Dom，当表格中不存在数据时，露出表头；存在数据时，露出表头+选择的数据
        const tableDom = this.$refs.mainTable.$el;
        //获取不到表格DOM元素，赋值高度80%返回
        if (!tableDom) {
          this.drawerHeight = "80%";
        }
        const recordHeaderHeight = this.$refs.mainRecordHeader?.offsetHeight ?? 0;
        const tableHeaderHeight = tableDom.getElementsByTagName("thead")?.[0]?.offsetHeight ?? 0;
        const tableRowsHeight =
          tableDom.getElementsByTagName("tbody")[0].getElementsByTagName("tr")?.[0]?.offsetHeight ?? 0;
        this.drawerHeight =
          this.$refs.mainRecord.offsetHeight - recordHeaderHeight - tableHeaderHeight - tableRowsHeight + "px";
      });
    },
    /**
     * description: 获取主记录动态表头
     * return {*}
     * param {*}
     */
    getRecordTableHeader() {
      let params = {
        fileClassID: 38,
        fileClassSub: "PatientDeliveryRecord",
        useDescription: "1||Table",
        newSourceFlag: true,
      };
      this.mainTableHeader = [];
      GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.mainTableHeader = res.data;
        }
      });
    },
    /**
     * description: 获取产程主记录
     * return {*}
     * param {*}
     */
    getRecords(refreshCurrentRecordFag = false) {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      this.loading = true;
      GetPatientDeliveryRecordRecordViews(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.deliveryRecords = res.data;
          if (refreshCurrentRecordFag && this.mainSaveParams.currentPatientDeliveryRecord) {
            this.$set(
              this.mainSaveParams,
              "currentPatientDeliveryRecord",
              this.deliveryRecords.find(
                (item) =>
                  item.patientDeliveryRecordID ==
                  this.mainSaveParams.currentPatientDeliveryRecord.patientDeliveryRecordID
              )
            );
          }
          this.$refs.mainTable.doLayout();
        }
      });
    },
    /**
     * description: 打开抽屉，编辑维护信息
     * param {*} row 当前主记录行
     * return {*}
     */
    async openCareMains(row) {
      if (row) {
        const checkResult = await this._common.checkActionAuthorization(this.user, row.userID);
        let authorityResult = await this._common.getEditAuthority(row.patientDeliveryRecordID, "PatientDeliveryRecord");
        if (authorityResult) {
          this.showEditButton = false;
          this._showTip("warning", authorityResult);
        } else {
          this.showEditButton = true;
        }
        if (this.refillFlag && this.refillFlag === "*") {
          let { disabledFlag,saveButtonFlag } = await this._common.userSelectorDisabled(this.user.userID, false, true, row.addEmployeeID)
          this.showEditButton = !saveButtonFlag;
        }
      }
      // 1. 向子组件传递保存所需变量
      this.mainSaveParams = {
        linkRecordsCode: this.linkRecordsCode,
        recordID: row ? row.patientDeliveryRecordID : "temp" + this._common.guid(),
        inpatientID: this.patient.inpatientID,
        currentPatient: this.patient,
        motherInfo: this.patient,
        scheduleMainID: this.scheduleMainID,
        refillFlag: this.refillFlag,
        drawerDirection: this.drawerSetting.direction,
        currentPatientDeliveryRecord: row,
      };
      //3. 打开抽屉
      this.openOrCloseDrawer(true, `产程维护`);
    },
    /**
     * description: 主记录删除
     * return {*}
     * param {*} row 主记录
     */
    async deleteRecord(row) {
      //判断是否可修改或删除该数据
      const checkResult = await this._common.checkActionAuthorization(this.user, row.userID);
      if (!checkResult) {
        this.showEditButton = false;
        this._showTip("warning", "非本人不可操作");
        return;
      }
      let authorityResult = await this._common.getEditAuthority(row.patientDeliveryRecordID, "PatientDeliveryRecord");
      if (authorityResult) {
        this.showEditButton = false;
        this._showTip("warning", authorityResult);
      } else {
        this.showEditButton = true;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            recordID: row.patientDeliveryRecordID,
          };
          DeletePatientDeliveryRecordRecord(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
            }
            this.getRecords();
            this.mainSaveParams.recordID = undefined;
          });
        }
      });
    },
    /**
     * description: 专项护理弹窗开关函数
     * return {*}
     * param {*} flag 弹框开关，title 弹框标题
     */
    openOrCloseDrawer(flag, title = "") {
      this.drawerSetting.showFlag = flag;
      if (flag) {
        this.drawerSetting.title = `${this.patient.bedNumber}床-${this.patient.patientName}【${this.patient.gender}-${
          this.patient.ageDetail ?? ""
        }】-- ${title}`;
      }
    },
    /**
     * description: 设置抽屉弹出方向
     * return {*}
     */
    setDrawerDirection() {
      if (this._common.isPC()) {
        // PC端 默认下向上弹
        this.drawerSetting.direction = "btt";
        return;
      }
      this.drawerSetting.direction = document.body.clientWidth > document.body.clientHeight ? "rtl" : "btt";
    },
    /**
     * description: 重置选中页签到第一个
     * return {*}
     * param {*} done 放行抽屉关闭
     */
    resetPage(done) {
      this.$refs.careMainSwitch?.resetPage();
      done();
    },
    /**
     * @description: 设置当前病人信息
     * @param {*} newInpatientInfo 新的病人信息
     * @return {*}
     */
    setInpatientInfo(newInpatientInfo) {
      if (!newInpatientInfo || newInpatientInfo == {}) {
        this.mainSaveParams.currentPatient = this.mainSaveParams.motherInfo;
        return;
      }

      this.mainSaveParams.currentPatient = {
        inpatientID: newInpatientInfo.inpatientID,
        patientID: newInpatientInfo.patientID,
        chartNo: newInpatientInfo.chartNo,
        caseNumber: newInpatientInfo.caseNumber,
        age: newInpatientInfo.age,
        dateOfBirth: newInpatientInfo.dateOfBirth,
        genderCode: newInpatientInfo.gender,
        nursingLevel: newInpatientInfo.nursingLevel,
        stationID: newInpatientInfo.stationID,
        departmentListID: newInpatientInfo.departmentListID,
        bedID: newInpatientInfo.bedID,
        bedNumber: newInpatientInfo.bedNumber,
      };
    },
  },
};
</script>

<style lang="scss">
.patient-delivery-record {
  height: 100%;
  .main-record {
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    padding: 5px 5px 10px 5px;
    box-sizing: border-box;
    .main-record-header {
      height: 50px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  .main-drawer {
    background-color: #f3f3f3;
    // 兼容49版本Chrome使用
    .el-drawer__body {
      height: calc(100% - 35px);
    }
    .main-component-switch {
      .base-header > div {
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .care-main-drawer {
        .base-header > div {
          justify-content: flex-start;
        }
        background-color: #f3f3f3;
        .date-picker {
          width: 160px;
        }
        .time-picker {
          width: 100px;
        }
      }
    }
  }
}
</style>
