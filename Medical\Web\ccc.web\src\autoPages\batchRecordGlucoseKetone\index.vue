<!--
 * FilePath     : \ccc.web\src\autoPages\batchRecordGlucoseKetone\index.vue
 * Author       : 孟昭永
 * Date         : 2023-05-20 08:30
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-12-02 10:24
 * Description  : 批量录入血糖
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="batch-glucose" v-loading="saveLoading">
    <div class="batch-glucose-top" slot="header">
      <label>显示全科：</label>
      <el-switch :active-value="1" :inactive-value="0" v-model="allFlag" />
      <label>日期：</label>
      <el-date-picker
        type="date"
        placeholder="选择班别日期"
        v-model="scheduleDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        class="top-date-pick"
      ></el-date-picker>
      <!-- 班别选择器组件 -->
      <shift-selector width="90" v-model="shiftID" :stationID="stationID"></shift-selector>
      <!-- 班别时间段选择器组件 -->
      <shift-times-selector width="160" v-model="shiftTimes" :shiftID="shiftID"></shift-times-selector>
      <el-button class="query-button" icon="iconfont icon-search" @click="getTabelData()">查询</el-button>
      <div class="top-right">
        <label v-if="glucoseData.length">监测时机：</label>
        <el-select
          v-if="glucoseData.length"
          v-model="monitoringTiming"
          placeholder="请选择时机"
          class="monitoring-selecter"
          @change="batchChangeMonitoringTiming"
        >
          <el-option
            v-for="item in monitoringTimingOption"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          ></el-option>
        </el-select>
        <label v-if="glucoseData.length">执行时间：</label>
        <el-time-picker
          v-if="glucoseData.length"
          v-model="performTime"
          value-format="HH:mm"
          format="HH:mm"
          class="time-picker"
          @change="fixPerFormTime()"
        ></el-time-picker>
        <el-button type="primary" class="save-button" icon="iconfont icon-save-button" @click="batchSave">
          保存
        </el-button>
      </div>
    </div>
    <div class="batch-glucose-content">
      <el-table
        ref="batchGlucoseDataTable"
        :data="glucoseData"
        border
        stripe
        highlight-current-row
        height="100%"
        element-loading-text="加载中……"
        v-loading="recordLoading"
      >
        <el-table-column type="selection" :width="convertPX(40) + 'px'" align="center" :selectable="selectable" />
        <el-table-column :width="convertPX(400)" label="姓名">
          <template slot-scope="scope">
            {{ scope.row.patientInfo }}
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(150)" label="记录项目" align="center">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.drug == '2' ? bloodKetoneColor : '' }">
              {{ getTestItem(scope.row.drug) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(150)" label="监测时机">
          <template slot-scope="scope">
            <el-select v-model="scope.row.aCorPCGlucose" placeholder="" @change="selectRow(scope.row)">
              <el-option
                v-for="item in scope.row.timingOption"
                :key="item.key"
                :label="item.label"
                :value="item.key"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(120)" label="注射部位" v-if="showInsulinColumn">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.bodyPartID"
              :disabled="scope.row.drug != '4'"
              clearable
              placeholder=""
              @change="selectRow(scope.row)"
            >
              <el-option
                v-for="item in bodyPartList"
                :key="item.typeValue"
                :label="item.description"
                :value="item.typeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(120)" label="记录结果">
          <template slot-scope="scope">
            <el-input v-model="scope.row.recordValue" @blur="checkTestItemValue(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column :min-width="convertPX(80)" label="说明">
          <template slot-scope="scope">
            <el-input v-model="scope.row.insulinNote" @change="selectRow(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(150)" label="执行日期" align="center">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.insulinDate"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              :class="scope.row.id ? 'saved' : 'no-save'"
              @change="selectRow(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(120)" label="执行时间" align="center">
          <template slot-scope="scope">
            <el-time-picker
              v-model="scope.row.insulinTime"
              value-format="HH:mm"
              format="HH:mm"
              :class="scope.row.id ? 'saved' : 'no-save'"
              @change="selectRow(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(120)" label="通知医师" align="center">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.informPhysician" @change="selectRow(scope.row)" />
          </template>
        </el-table-column>
        <!-- 暂时占位用后续合并后处理 -->
        <el-table-column :width="convertPX(160)" label="带入护理记录" align="center">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.bringToNursingRecord" @change="selectRow(scope.row)" />
          </template>
        </el-table-column>
      </el-table>
      <progress-view v-show="progressFlag" @closeProgress="restoreProgress" :tableData="messageData"></progress-view>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import progressView from "@/components/progressView";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetClinicalSettingInfo } from "@/api/Assess";
import { GetOneSettingByTypeAndCode, GetSettingGroupByTypeCodes } from "@/api/Setting";
import { GetSettingValueByTypeCodeAsync } from "@/api/SettingDescription";
import { GetBatchGlucoseByTime } from "@/api/PatientSchedule";
import { Save } from "@/api/Glucose";
import shiftSelector from "@/components/selector/shiftSelector";
import shiftTimesSelector from "@/components/selector/shiftTimesSelector";
import { GetShiftTimeLine } from "@/api/Setting";

export default {
  components: {
    baseLayout,
    progressView,
    shiftSelector,
    shiftTimesSelector,
  },
  data() {
    return {
      allFlag: 0,
      scheduleDate: undefined,
      shiftID: undefined,
      shiftTimes: undefined,
      glucoseData: [],
      stationID: undefined,
      testItemOption: [], //记录项目的选项
      showInsulinColumn: false, // 是否显示胰岛素列
      recordLoading: false, //记录加载
      performTime: this._datetimeUtil.getNowTime(), //一键保存的执行时间
      // 监测项目映射关系
      testItemMapSetting: [
        {
          drug: 1, //血糖
          columnName: "glucose",
          settingTypeCode: "InsulinFrequency",
          option: [],
        },
        {
          drug: 2, //血酮
          columnName: "glucoseInUrine",
          settingTypeCode: "InsulinFrequency",
          option: [],
        },
        {
          drug: 3, //新生儿血糖
          columnName: "glucose",
          settingTypeCode: "InsulinFrequencyNewborn",
          option: [],
        },
        {
          drug: 4, //胰岛素治疗
          columnName: "dose",
          settingTypeCode: "InsulinTreatmentFrequency",
          option: [],
        },
      ],
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      progressFlag: false, //进度条标志
      bloodKetoneColor: "#000", //血酮颜色
      saveLoading: false,
      hasAuthority: false, // 是否能编辑删除该数据
      bodyPartList: [], //胰岛素注射的身体部位配置数据
      startTime: "",
      endTime: "",
      //监测时机
      monitoringTiming: "",
      //监测时机列表
      monitoringTimingOption: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.getNowShiftData();
      this.getTestItemOption();
      this.getSettingValueByTypeCode();
      await this.getTimingSettingGroup();
      await this.getDefaultTiming();
      this.$nextTick(await this.getTabelData());
    },
    /**
     * description: 获取顶部form数据
     * return {*}
     */
    async getNowShiftData() {
      await GetNowStationShiftData().then((res) => {
        if (this._common.isSuccess(res)) {
          let nowShiftData = res.data;
          this.scheduleDate = this._datetimeUtil.formatDate(nowShiftData.shiftDate, "yyyy-MM-dd");
          this.stationID = nowShiftData.nowShift.stationID;
          this.$nextTick(() => {
            this.shiftID = nowShiftData.nowShift.id;
          });
        }
      });
    },
    /**
     * description: 一键批量保存
     * return {*}
     */
    async batchSave() {
      let table = this.$refs.batchGlucoseDataTable;
      let selectRows = table && this._common.clone(table.selection);
      if (!selectRows || !selectRows.length) {
        this._showTip("warning", "请勾选要保存的数据!");
        return;
      }

      this.saveLoading = true;
      this.progressFlag = true;
      let successCount = 0;
      let failCount = 0;

      // 获取进度百分比
      let getPercent = (index) => Number((((index + 1) / selectRows.length) * 100).toFixed());
      // 保存检查公共方法
      let saveCheck = async (index, predicate, errMsg) => {
        let selectRow = selectRows[index];
        if (await predicate(selectRow)) return true;
        failCount++;
        this.messageData[0].value = getPercent(index);
        this.messageData[3].value += `${selectRow.patientInfo}${errMsg}<br/>`;
        return false;
      };
      for (let index = 0; index < selectRows.length; index++) {
        // 必填项检查
        if (!(await saveCheck(index, this.requiredCheck, "【记录结果】、【备注】至少填一项"))) {
          continue;
        }
        // 必填项检查
        if (!(await saveCheck(index, (selectRow) => selectRow.aCorPCGlucose, "请选择【监测时机】"))) {
          continue;
        }
        // 权限检查
        if (!(await saveCheck(index, this.authorityCheck, "非本人不可操作！"))) {
          continue;
        }
        // 创建保存的View
        let view = this.createView(selectRows[index]);
        // 保存请求
        await Save(view).then((response) => {
          if (response.code != 1) {
            failCount++;
            this.messageData[0].value = getPercent(index);
            this.messageData[3].value += `${selectRows[index].patientInfo} ${response.message}<br/>`;
            return;
          }
          successCount++;
          this.messageData[0].value = getPercent(index);
        });
      }
      this.messageData[1].value = `${successCount}条保存成功`;
      this.messageData[2].value = `${failCount}条保存失败`;
      this.saveLoading = false;
      await this.getTabelData();
    },
    /**
     * description: 获取表格数据
     * return {*}
     */
    async getTabelData() {
      if (!this.shiftTimes) {
        //时间组件获取时间段可能为空，导致获取数据接口访问不到，这里时间为空时重新去后端重新获取
        await this.getShiftTime();
      }
      var timeSpan = this.shiftTimes.split("-");
      if (timeSpan && timeSpan.length == 2) {
        this.startTime = timeSpan[0];
        this.endTime = timeSpan[1];
      }
      let params = {
        allFlag: this.allFlag,
        scheduleDate: this.scheduleDate,
        shiftID: this.shiftID,
        startTime: this.startTime,
        endTime: this.endTime,
      };

      this.recordLoading = true;
      await GetBatchGlucoseByTime(params).then((res) => {
        if (this._common.isSuccess(res)) {
          let insulinRecords = res.data;
          insulinRecords.forEach((record) => {
            let currItemTimingSetting = this.testItemMapSetting.find((m) => m.drug == record.drug);
            record.timingOption = currItemTimingSetting ? currItemTimingSetting.option : undefined;
          });
          this.glucoseData = insulinRecords;
        }
      });
      this.recordLoading = false;
      this.performTime = this._datetimeUtil.getNowTime();
    },
    /**
     * description: 已录入数据，显示监测项目
     * return {*}
     */
    getTestItem(drug) {
      return this.testItemOption.find((m) => m.typeValue === drug)?.description;
    },
    /**
     * description: 行数据异动时自动选择当前行
     * param {*} row 当前异动行
     * return {*}
     */
    selectRow(row) {
      if (row.disabled) {
        return;
      }
      this.$nextTick(() => {
        let table = this.$refs.batchGlucoseDataTable;
        if (table) {
          table.toggleRowSelection(row, true);
        }
      });
    },
    /**
     * description: 获取记录项目的选项
     * param {*}
     * return {*}
     */
    getTestItemOption() {
      let params = {
        settingTypeCode: "InsulinTest",
      };
      GetClinicalSettingInfo(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.testItemOption = response.data;
          //记录项目中包含胰岛素,就显示胰岛素和身体部位两列
          this.showInsulinColumn = response.data.some((item) => item.settingValue == "InsulinTreatmentFrequency");
        }
      });
      if (this.showInsulinColumn) {
        this.getInsulinBodyPartList();
      }
    },
    /**
     * description: 获取监测时机的选项
     * return {*}
     */
    async getTimingSettingGroup() {
      let params = {
        settingTypeCodes: ["InsulinFrequency", "InsulinTreatmentFrequency", "InsulinFrequencyNewborn"],
      };
      await GetSettingGroupByTypeCodes(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.initTimingOptionsByItem(response.data);
        }
      });
    },
    /**
     * description: 根据项目配置码初始化监测时机下拉项字典
     * param {*} testItemMapSettings 各项目对应监测时机, {settingTypeCode: "", settings: [{},{}]}
     * return {*}
     */
    initTimingOptionsByItem(itemTimingSettings) {
      let emptyOption = {
        key: "",
        value: 0,
        label: "",
      };
      this.testItemMapSetting.forEach((itemTestTiming) => {
        let currTestItemMapping = itemTimingSettings.find((m) => m.settingTypeCode === itemTestTiming.settingTypeCode);
        if (currTestItemMapping) {
          let option = this._common.clone(currTestItemMapping.settings);
          option.unshift(emptyOption);
          itemTestTiming.option = option;
        }
      });
      this.monitoringTimingOption = this.testItemMapSetting.find((m) => m.drug === 1)?.option;
    },
    /**
     * description: 获取默认监测时机
     * param {*}
     * return {*}
     */
    async getDefaultTiming() {
      let params = {
        settingType: 160,
        settingCode: "DefaultInsulinFrequency",
      };
      await GetOneSettingByTypeAndCode(params).then((response) => {
        if (this._common.isSuccess(response) && response.data) {
          this.defaultInsulinTiming = response.data.typeValue;
        }
      });
    },
    /**
     * description: 获取血酮颜色
     * return {*}
     */
    async getSettingValueByTypeCode() {
      let param = {
        SettingTypeCode: "BloodKetoneColor",
      };
      await GetSettingValueByTypeCodeAsync(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.bloodKetoneColor = response?.data ?? "#000";
        }
      });
    },
    /**
     * description: 获取胰岛素注射部位
     * param {*}
     * return {*}
     */
    getInsulinBodyPartList() {
      let params = {
        settingTypeCode: "InsulinBodyPart",
      };
      GetClinicalSettingInfo(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.bodyPartList = response.data;
        }
      });
    },
    /**
     * description: 检核el-input输入框中输入的是否为数字
     * param {*} row 当前行数据
     * return {*}
     */
    checkTestItemValue(row) {
      this.selectRow(row);
      if (!row.recordValue) {
        return;
      }
      //匹配小数和整数的正则
      let reg = /^[\+\-]?\d*?\.?\d*?$/;
      if (!reg.test(row.recordValue)) {
        row.recordValue = "";
        return;
      }
      //过滤012这样的数值
      if (typeof row.recordValue != "number" && !row.recordValue.includes(".") && !row.recordValue.includes("-")) {
        row.recordValue = Number(row.recordValue);
      }
    },
    /**
     * description: 进度条关闭函数，重置进度条内的数据内容
     * param {*}
     * return {*}
     */
    async restoreProgress() {
      this.progressFlag = false;
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
      this.saveLoading = false;
      await this.getTabelData();
    },
    /**
     * description: 必填项检查回调
     * param {*} selectRow 选中行数据
     * return {*} 检查是否通过
     */
    async requiredCheck(selectRow) {
      return selectRow.insulinNote || selectRow.recordValue;
    },
    /**
     * description: 权限检查回调
     * param {*} selectRow 选中行数据
     * return {*} 检查是否通过
     */
    async authorityCheck(selectRow) {
      if (!selectRow.id) {
        return true;
      }
      await this.checkAuthor(selectRow.id, "InsulinRecord", this.userID);
      return this.hasAuthority;
    },
    /**
     * description: 权限检核
     * param {*} id 行数据主键
     * param {*} tableName 行数据对应的数据表
     * param {*} userID 当前登录用户ID
     * return {*}
     */
    async checkAuthor(id, tableName, userID) {
      let checkResult = await this._common.checkActionAuthorization(this.user, userID);
      if (!checkResult) {
        this.hasAuthority = false;
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, tableName, !!this.refillFlag);
      if (ret) {
        this._showTip("warning", ret);
        this.hasAuthority = false;
      } else {
        this.hasAuthority = true;
      }
    },
    /**
     * description: 组装一行数据的View
     * param {*} row 行数据
     * return {*}
     */
    createView(row) {
      let mapSetting = this.testItemMapSetting.find((item) => item.drug == row.drug);
      if (!mapSetting) {
        return;
      }
      return {
        id: row.id,
        stationID: row.stationID,
        inpatientID: row.inpatientID,
        patientScheduleMainID: row.patientScheduleMainID,
        insulinDate: row.insulinDate,
        insulinTime: row.insulinTime,
        // 借字段以区分不同的监测项目
        drug: row.drug,
        [mapSetting.columnName]: row.recordValue,
        bodyPartID: row.bodyPartID,
        aCorPCGlucose: row.aCorPCGlucose,
        insulinNote: row.insulinNote,
        informPhysician: row.informPhysician,
        bringToNursingRecord: row.bringToNursingRecord,
        sourceID: row.sourceID,
        sourceType: "",
        settingTypeCode: mapSetting.settingTypeCode,
        refillFlag: "",
        index: Math.random(),
        patientScheduleMainIDs: row.patientScheduleMainIDs,
      };
    },
    /**
     * description: 批量修改未执行排程的执行时间
     * return {*}
     */
    fixPerFormTime() {
      for (let i = 0; i < this.glucoseData.length; i++) {
        if (this.glucoseData[i].id) {
          continue;
        }
        this.glucoseData[i].insulinTime = this.performTime;
      }
    },
    /**
     * description: 复选框是否禁用
     * param {*} row
     * return {*}
     */
    selectable(row) {
      if (!row.disabled) {
        return true;
      }
      return false;
    },

    /**
     * @description: 获取班别时间段
     * @return
     */
    async getShiftTime() {
      if (!this.shiftID) {
        return;
      }
      let params = {
        shiftID: this.shiftID,
        index: Math.random(),
      };
      await GetShiftTimeLine(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let shiftTimeLine = result.data;
          if (shiftTimeLine && shiftTimeLine.length > 0) {
            let temp = shiftTimeLine.find((timeRange) => {
              let times = timeRange.split("-");
              let beginTime = this._datetimeUtil.formatDate(times[0], "hh:mm");
              let endTime = this._datetimeUtil.formatDate(times[1], "hh:mm");
              let nowTime = this._datetimeUtil.getNowTime("hh:mm");
              return nowTime >= beginTime && nowTime <= endTime;
            });
            if (temp) {
              this.shiftTimes = temp;
            } else {
              this.shiftTimes = shiftTimeLine[0];
            }
          }
        }
      });
    },
    /**
     * @description: 批量修改监测时机
     * @return
     */
    batchChangeMonitoringTiming() {
      this.glucoseData.forEach((data) => {
        data.aCorPCGlucose = this.monitoringTiming;
      });
    },
  },
};
</script>
<style lang="scss">
.batch-glucose {
  height: 100%;
  .batch-glucose-top {
    padding-left: 0;
    label {
      margin-left: 20px;
    }
    .top-date-pick {
      width: 160px;
    }
    .top-right {
      float: right;
      .time-picker {
        width: 100px;
      }
      .monitoring-selecter {
        width: 120px;
      }
    }
  }
  .batch-glucose-content {
    height: 100%;
    .no-save {
      .el-input__inner {
        color: #ff0000;
      }
    }
  }
}
</style>

