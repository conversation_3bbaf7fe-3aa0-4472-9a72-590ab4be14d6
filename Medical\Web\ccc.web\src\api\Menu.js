/*
 * FilePath     : \src\api\Menu.js
 * Author       : 苏军志
 * Date         : 2020-07-12 16:44
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:45
 * Description  :菜单
 */

import http from "../utils/ajax";
const baseUrl = "/Menu";

export const urls = {
  GetMenuList: baseUrl + "/GetMenuList",
  GetMenuByID: baseUrl + "/GetMenuByID",
  GetMenuByParentID: baseUrl + "/GetMenuByParentID"
};

//获取菜单列表
export const GetMenuList = params => {
  return http.get(urls.GetMenuList, params);
};
//根据菜单ID获取菜单列表
export const GetMenuByID = params => {
  return http.get(urls.GetMenuByID, params);
};
//根据父菜单ID获取数据
export const GetMenuByParentID = params => {
  return http.get(urls.GetMenuByParentID, params);
};
