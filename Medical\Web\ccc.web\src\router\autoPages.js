/*
 * FilePath     : \ccc.web\src\router\autoPages.js
 * Author       : 苏军志
 * Date         : 2022-05-23 11:00
 * LastEditors  : LX
 * LastEditTime : 2025-05-23 17:54
 * Description  : 路由配置
 * CodeIterationRecord:
 */

// 登录
let login = (resolve) => require(["@/autoPages/login/index.vue"], resolve);
// 系统主画面
let main = (resolve) => require(["@/autoPages/mainLayout.vue"], resolve);
// 患者清单
let patientList = (resolve) =>
  require(["@/autoPages/patientList/index.vue"], resolve);
// 患者布局路由
let patientLayout = (resolve) =>
  require(["@/autoPages/patientLayout.vue"], resolve);
/* 单病人批量执行主画面 */
let singlePatientBatchExecution = (resolve) =>
  require(["@/autoPages/singlePatientBatchExecution/index.vue"], resolve);
/* 单病人批量执行排程 */
let batchExecutionSchedule = (resolve) =>
  require([
    "@/autoPages/singlePatientBatchExecution/batchExecutionSchedule.vue",
  ], resolve);
/* 伤口评估 */
let wound = (resolve) => require(["@/autoPages/wound/index.vue"], resolve);
/* 护理评估 */
let assess = (resolve) =>
  require(["@/autoPages/nursingAssessment/index.vue"], resolve);
/* 护理评估明细 */
let assessDetail = (resolve) =>
  require(["@/autoPages/nursingAssessment/detail.vue"], resolve);
// 约束记录
let restraint = (resolve) =>
  require(["@/autoPages/restraint/index.vue"], resolve);
// 出院病人约束记录查看
let restraintLook = (resolve) =>
  require(["@/autoPages/restraint/index.vue"], resolve);
/* 转运交接 */
let transferHandover = (resolve) =>
  require(["@/autoPages/handover/transferHandover.vue"], resolve);
/* 出院小结 */
let dischargeHandover = (resolve) =>
  require(["@/autoPages/handover/dischargeHandover.vue"], resolve);
// 手术转运
let operationHandover = (resolve) =>
  require(["@/autoPages/handover/operationHandover/index.vue"], resolve);
// 批量交班
let multipleShiftHandoff = (resolve) =>
  require(["@/autoPages/handover/multipleShiftHandover/handoff.vue"], resolve);
// 批量接班
let multipleShiftHandon = (resolve) =>
  require(["@/autoPages/handover/multipleShiftHandover/handon.vue"], resolve);
//病人接班
let patientHandon = (resolve) =>
  require(["@/autoPages/handover/patientHandon/index.vue"], resolve);
/* 产时评估 */
let patientDelivery = (resolve) =>
  require(["@/autoPages/patientDelivery/index.vue"], resolve);
/* 末梢血运 */
let peripheralCirculation = (resolve) =>
  require(["@/autoPages/peripheralCirculation/index.vue"], resolve);
//造口record页面
let patientStomaRecord = (resolve) =>
  require(["@/autoPages/patientStoma/index.vue"], resolve);
//造口record页面
let patientStomaRecordLook = (resolve) =>
  require(["@/autoPages/patientStoma/index.vue"], resolve);
// 生产全流程评估
let patientDeliveryRecord = (resolve) =>
  require(["@/autoPages/patientDeliveryRecord/index.vue"], resolve);
// CRRT记录单
let cRRTRecord = (resolve) =>
  require(["@/autoPages/cRRTRecord/index.vue"], resolve);
// 排程执行处带入护理记录配置维护
let interventionToRecordSettingMaintain = (resolve) =>
  require([
    "@/autoPages/dictionaryMaintain/interventionToRecordSettingMaintain.vue",
  ], resolve);
//给药医嘱执行率统计
let medicineScheduleRate = (resolve) =>
  require(["@/autoPages/statistics/medicineStatistics.vue"], resolve);
let patientPain = (resolve) =>
  require(["@/autoPages/patientPain/index.vue"], resolve);
let patientPainLook = (resolve) =>
  require(["@/autoPages/patientPain/index.vue"], resolve);
//批量血糖
let batchRecordGlucoseKetone = (resolve) =>
  require(["@/autoPages/batchRecordGlucoseKetone/index.vue"], resolve);
// 生命体征统计
let tprChart = (resolve) =>
  require(["@/autoPages/patientHomePage/tprChart/index.vue"], resolve);
// 病人主页
let patientHomePage = (resolve) =>
  require(["@/autoPages/patientHomePage/index.vue"], resolve);
// 每日交班
let dailyHandover = (resolve) =>
  require(["@/autoPages/handover/handoverReport/dailyHandover.vue"], resolve);
// 交班报告
let stationHandoverReport = (resolve) =>
  require([
    "@/autoPages/handover/handoverReport/stationHandoverReport.vue",
  ], resolve);
// 晨交班报告
let morningShiftReport = (resolve) =>
  require([
    "@/autoPages/handover/handoverReport/morningShiftReport.vue",
  ], resolve);
// 晨交班
let morningShiftHandoverReport = (resolve) =>
  require([
    "@/autoPages/handover/handoverReport/morningShiftHandoverReport.vue",
  ], resolve);
// 皮瓣护理
let flap = (resolve) => require(["@/autoPages/flap/index.vue"], resolve);
//班别交接班内交接编辑
let shiftHandover = (resolve) =>
  require(["@/autoPages/handover/shiftHandover/index.vue"], resolve);
//镇静评估
let patientSedation = (resolve) =>
  require(["@/autoPages/sedation/index.vue"], resolve);
//无呕专项
let patientCINV = (resolve) =>
  require(["@/autoPages/patientCINV/index.vue"], resolve);
let recordReview = (resolve) =>
  require(["@/autoPages/recordReview/index.vue"], resolve);
//操作日志分析
let operationLogAnalytics = (resolve) =>
  require(["@/autoPages/operationLogAnalytics/index.vue"], resolve);
//导管统计
let statisticDischargeTube = (resolve) =>
  require(["@/autoPages/tube/statisticDischargeTube.vue"], resolve);
let stationToJobTip = (resolve) =>
  require(["@/autoPages/stationToJobTip/index.vue"], resolve);
let patientIconMaintain = (resolve) =>
  require(["@/autoPages/dictionaryMaintain/patientIconMaintain.vue"], resolve);
// 会诊统计
let nurseConsultQuery = (resolve) =>
  require(["@/autoPages/statistics/nurseConsultQuery.vue"], resolve);
//护理级别巡视情况
let nursingLevelCarePerformQuery = (resolve) =>
  require(["@/autoPages/nursingLevelCarePerformQuery/index.vue"], resolve);
//措施统计查询页面
let queryNursingIntervention = (resolve) =>
  require([
    "@/autoPages/dictionaryMaintain/queryNursingIntervention.vue",
  ], resolve);
// 单多人监测排程表
let monitoringScheduler = (resolve) =>
  require(["@/autoPages/monitoringScheduler/index.vue"], resolve);
// 危重患者访视页面
let visitCriticalPatient = (resolve) =>
  require(["@/autoPages/patientCriticallyVisitsRecord/index.vue"], resolve);
//措施触发措施维护画面
let interventionTriggerIntervention = (resolve) =>
  require([
    "@/autoPages/dictionaryMaintain/interventionTriggerIntervention/index.vue",
  ], resolve);
// 抢救画面
let rescueRecord = (resolve) =>
  require(["@/autoPages/patientRescue/index.vue"], resolve);
let recordSupplement = (resolve) =>
  require(["@/autoPages/recordSupplement/index.vue"], resolve);
let giveMedicineRecordTest = (resolve) =>
  require([
    "@/autoPages/recordSupplement/components/giveMedicineRecordTest.vue",
  ], resolve);
// 血袋接收回收画面
let bloodVerificationRecycle = (resolve) =>
  require(["@/autoPages/patientBlood/bloodVerificationRecycle.vue"], resolve);
//给药闭环
let medicationClosedLoop = (resolve) =>
  require(["@/autoPages/medicationClosedLoop/index.vue"], resolve);
/* 导出变量 */
export default {
  login,
  main,
  patientList,
  patientLayout,
  singlePatientBatchExecution,
  batchExecutionSchedule,
  wound,
  assess,
  assessDetail,
  restraintLook,
  restraint,
  transferHandover,
  dischargeHandover,
  operationHandover,
  multipleShiftHandoff,
  multipleShiftHandon,
  patientHandon,
  patientDelivery,
  peripheralCirculation,
  patientStomaRecord,
  patientStomaRecordLook,
  patientDeliveryRecord,
  cRRTRecord,
  interventionToRecordSettingMaintain,
  medicineScheduleRate,
  patientPain,
  patientPainLook,
  batchRecordGlucoseKetone,
  tprChart,
  patientHomePage,
  dailyHandover,
  stationHandoverReport,
  flap,
  shiftHandover,
  patientSedation,
  patientCINV,
  morningShiftReport,
  morningShiftHandoverReport,
  recordReview,
  operationLogAnalytics,
  statisticDischargeTube,
  stationToJobTip,
  patientIconMaintain,
  nurseConsultQuery,
  nursingLevelCarePerformQuery,
  queryNursingIntervention,
  monitoringScheduler,
  visitCriticalPatient,
  interventionTriggerIntervention,
  rescueRecord,
  recordSupplement,
  giveMedicineRecordTest,
  bloodVerificationRecycle,
  medicationClosedLoop,
};
