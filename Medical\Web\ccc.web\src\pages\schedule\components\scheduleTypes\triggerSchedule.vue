<template>
  <div
    class="trigger-schedule"
    v-enter="{ function: 'saveTriggerIntervention' }"
    v-loading.fullscreen.lock="triggerLoading"
    element-loading-text="保存中……"
  >
    <div class="header">
      执行日期：
      <span>{{ triggerDate }}</span>
      <span class="label">执行时间：</span>
      <el-time-picker
        v-model="triggerTime"
        value-format="HH:mm"
        format="HH:mm"
        :clearable="false"
        style="width: 120px"
      ></el-time-picker>
    </div>
    <div class="trigger-table">
      <el-table ref="triggerTable" :data="triggerDatas" border height="100%" @selection-change="selectTrigger">
        <el-table-column type="selection" :width="convertPX(40)" align="center"></el-table-column>
        <el-table-column prop="intervention" label="措施"></el-table-column>
        <el-table-column label="建议原因" width="300">
          <template slot-scope="scope">
            <div v-html="scope.row.triggerBasis"></div>
          </template>
        </el-table-column>
        <el-table-column label="频次" width="180">
          <template slot-scope="scope">
            <frequency-selector
              v-model="scope.row.frequencyID"
              :frequencyList="scope.row.frequencyList"
              @select-item="selectItem($event, scope.row)"
            ></frequency-selector>
          </template>
        </el-table-column>
        <el-table-column label="执行次数" width="80">
          <template slot-scope="scope">
            <el-input v-model="scope.row.numberOfTimes"></el-input>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <span class="footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="saveTriggerIntervention">确 定</el-button>
    </span>
  </div>
</template>

<script>
import { SaveTriggerIntervention } from "@/api/PatientSchedule";
import frequencySelector from "@/components/selector/frequencySelector";
export default {
  components: {
    frequencySelector,
  },
  data() {
    return {
      triggerDatas: [],
      triggerDate: "",
      triggerTime: "",
      triggerLoading: false,
      selectTriggerDatas: [],
    };
  },
  props: {
    triggerParams: {
      type: Object,
      required: true,
    },
  },
  watch: {
    triggerParams: {
      immediate: true,
      handler(newValue) {
        this.init(newValue);
      },
    },
  },
  methods: {
    init(params) {
      this.triggerDatas = params.triggerDatas;
      this.triggerDate = params.triggerDate;
      this.triggerTime = params.triggerTime;
      // 默认全选
      this.$nextTick(() => {
        this.$refs.triggerTable.toggleAllSelection();
      });
    },
    close() {
      // 措施触发措施无论保存与否，都要刷新排程列表
      this.$emit("close", true);
    },
    selectTrigger(selectDatas) {
      this.selectTriggerDatas = selectDatas;
    },
    saveTriggerIntervention() {
      if (this.triggerLoading) {
        return;
      }
      let saveData = [];
      this.selectTriggerDatas.forEach((item) => {
        //组装保存的数据
        let saveItem = {
          frequency: item.frequency,
          frequencyID: item.frequencyID,
          inpatientID: item.inpatientID,
          interventionID: item.interventionID,
          numberOfTimes: item.numberOfTimes,
          patientAttachedInterventionID: "",
          patientInterventionID: item.patientInterventionID,
          patientScheduleMainID: item.patientScheduleMainID,
          patientProblemID: item.patientScheduleMainID,
          select: true,
          startDate: this.triggerDate,
          startTime: this.triggerTime,
          fixTimeFlag: item.fixTimeFlag,
          tprSheetFlag: item.tprSheetFlag,
        };
        saveData.push(saveItem);
      });
      if (saveData.length > 0) {
        this.triggerLoading = true;
        SaveTriggerIntervention(saveData).then((result) => {
          this.triggerLoading = false;
          if (this._common.isSuccess(result) && result.data) {
            this._showTip("success", "保存成功！");
            this.close();
          }
        });
      } else {
        this._showTip("warning", "您没有选择要触发的措施！");
      }
    },
    selectItem(frequencyItem, row) {
      this.selectTriggerDatas.forEach((item) => {
        if (item.frequencyID == frequencyItem.id) {
          item.frequency = frequencyItem.search;
          return;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.trigger-schedule {
  height: 100%;
  .header {
    height: 30px;
    margin-bottom: 10px;
    .label {
      margin-left: 10px;
    }
  }
  .trigger-table {
    height: calc(100% - 80px);
  }
  .footer {
    position: absolute;
    text-align: right;
    width: 100%;
    bottom: 10px;
    right: 20px;
  }
}
</style>