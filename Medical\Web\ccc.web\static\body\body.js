﻿$Common.usingCSS("body.css");

/*
************************************************************
作者：YTS
版本：1.0.0
日期：2018-5-28
************************************************************
 */
//人体部位地图
var BodyMap = function (opt) {
  this.title = "\u4eba\u4f53\u56fe";
  this.showTitile = true;
  //地图
  this.map = undefined;
  //是否支持翻转
  this._reverse = false;
  // 载入时是否需要反转
  this._needReverse = false;
  //当前图形显示状态
  this.positive = 0;
  //背景图物理路径
  this._backgroundImagePath = undefined;
  //原始背景图
  this._backgroudImage = new Image();
  //部位列表
  this.bodyList = new BodyList();
  //部位图
  this._bodyPartMap = undefined;
  //参照容器
  this._container = undefined;
  //选择模式
  this.selectOneMode = true;
  //选中事件
  this.selectedEvent = undefined;
  //取消选中事件
  this.selectedCancelEvent = undefined;
  //选中部位
  this.selectedBodyPart = undefined;
  //选中部位为多个对象
  this.selectBodyPartList = [];
  //视图
  this.viewMap = undefined;
  //是否显示视图
  this.showView = false;
  //是否显示线条
  this.showLine = true;
  //相对路径
  this.url = "";
};
//人体部地图具体实现类
BodyMap.prototype = {
  setCanvas: function (canvas) {
    this.map = $(canvas);
    if (!this.map) return;
    if (this._backgroundImagePath) {
      this.map.css(
        "background-image",
        "url(" + this._backgroundImagePath + ")"
      );
    }
  },
  //是否可翻转
  canReverse: function () {
    return this._reverse && this.positive > 0;
  },
  setReverse: function (flag) {
    this._reverse = flag;
  },
  setSize: function (width, height) {
    if (!this.map) return;
    this.map.css("width", width);
    this.map.css("height", height);
  },
  setLocation: function (_left, _top) {
    if (this.map) {
      this.map.css("margin-left", _left + "px");
      this.map.css("margin-top", _top + "px");
    }
  },
  setImage: function (imagePath, reverse) {
    this._backgroudImage.src = imagePath;
    this._backgroundImagePath = imagePath;
    this._reverse = reverse;
    if (this.map && this._backgroundImagePath) {
      this.map.css(
        "background-image",
        "url(" + this._backgroundImagePath + ")"
      );
    }
  },
  setRalativeImage: function (imagePath, reverse) {
    imagePath = $BodyMap.url + imagePath;
    this.setImage(imagePath, reverse);
  },
  getImage: function () {
    return this._backgroudImage;
  },
  getImageWidth: function () {
    if (this._backgroudImage) {
      return this._backgroudImage.width;
    }
  },
  getImageHeight: function () {
    if (this._backgroudImage) {
      return this._backgroudImage.height;
    }
  },
  setImageSize: function (width, height) {
    if (!this.map) return;
    if (this.positive > 0) {
      width = width * 2;
    }
    this.map.css("background-size", width + "px " + height + "px");
    if (this.positive == 2) {
      this.setImagePosition(-this.map.width(), 0);
    } else {
      this.setImagePosition(0, 0);
    }
  },
  setImagePosition: function (x, y) {
    if (this.map) {
      this.map.css("background-position", x + "px " + y + "px");
    }
  },
  setList: function (bodyList) {
    this.bodyList = bodyList;
  },
  getCurrent: function () {
    if (this._bodyPartMap) {
      return this._bodyPartMap;
    }
    return this;
  },
  getContainer: function () {
    if (this.viewMap) {
      return this.viewMap.getContainer();
    }
    return this._container;
  },
  getList: function () {
    return this.bodyList;
  },
  reset: function () {
    if (this.map) {
      this.map.parent().parent().find("input:checkbox").prop("checked", false);
    }
    this.bodyList.reset();
  },
  init: function () {
    $("#bodyPart_Containter").remove();
    $BodyMap._bodyPartMap = undefined;
    $BodyMap.map.parent().children().show();
    if (!$BodyMap.showTitile) {
      $BodyMap._container.find(".tab").hide();
    }
    if ($BodyMap.getList()._visible) {
      $BodyMap.getList().list.show();
      $BodyMap.map.parent().hide();
    } else {
      //$BodyMap.getList().list.hide();
      $BodyMap.map.parent().show();
      resetMap($BodyMap);
    }
  },
  getZoom: function () {
    if (!this.map)
      return {
        zoomX: 1,
        zoomY: 1,
      };
    if (this._backgroudImage) {
      if (this.positive == 0) {
        return {
          zoomX: this.map.width() / this._backgroudImage.width,
          zoomY: this.map.height() / this._backgroudImage.height,
        };
      } else {
        return {
          zoomX: (this.map.width() * 2) / this._backgroudImage.width,
          zoomY: this.map.height() / this._backgroudImage.height,
        };
      }
    }
  },
};
//参照容器改变大小
BodyMap.prototype.onresize = function () {
  if (!this._container) {
    return;
  }
  if ($BodyMap._bodyPartMap) {
    resetMap($BodyMap._bodyPartMap);
  } else if (!$BodyMap.getList() || !$BodyMap.getList()._visible) {
    resetMap($BodyMap);
  }
};
//设置选中模式
BodyMap.prototype.setOneMode = function (flag) {
  this.selectOneMode = flag;
};
//加载人体图
BodyMap.prototype.onload = function (opt) {
  if (!opt.container) {
    return;
  }
  this.showView = false;
  if (opt.showLine != undefined) {
    this.showLine = opt.showLine;
  }
  if (this._container) {
    this._container.empty();
  }
  if (opt.selectOneMode != undefined) {
    this.selectOneMode = opt.selectOneMode; //选择模式
  } else {
    this.selectOneMode = true; // 默认单选
  }
  this._container = opt.container;
  this.selectedEvent = opt.selected; //将selected方法赋值给变量
  this.selectedCancelEvent = opt.cancelSelected; //取消勾选
  if (opt.title) {
    this.title = opt.title.bodymap;
    this.bodyList.title = opt.title.list;
  }

  var html = '<div class="tab">';
  html += '<div id="mapTab" class="tab__item">' + this.title + "</div>";
  html +=
    '<div id="listTab" class="tab__item">' + this.bodyList.title + "</div>";
  html += "</div>";
  html += '<div class="map_tab">';
  html += '<div class="bodyMap"></div>';
  html += '<div class="reverse">';
  if (opt.title.reverse) {
    html += opt.title.reverse;
  }
  html += "</div>";
  html += "</div>";
  html += '<div class="list_tab" style="display: none"></div>';

  this._container.append(html);
  //添加监听事件
  addBodyMapEventListener();

  if (!opt.showTitile) {
    this.showTitile = false;
    this._container.find(".tab").css("display", "none");
  } else {
    $(".map_tab").show();
    $("#mapTab").addClass("tab__item_selected");
    $(".list_tab").hide();
    //初始化BodyList
    $BodyMap.getList().setContainer(".list_tab");
  }
  //初始化BodyMap
  $BodyMap.setCanvas(".bodyMap");

  this.loadBodyPart(opt.data);
};
//等比调整
BodyMap.prototype.scalingSize = function (
  containerWidth,
  containerHeight,
  maxScale
) {
  var imgWidth = this.getImageWidth();

  var imgHeight = this.getImageHeight();

  if (this._reverse && imgWidth > containerWidth) {
    imgWidth = imgWidth / 2;
    if (this.positive == 0) {
      this.positive = 1;
    }
  } else {
    this.positive = 0;
  }

  var containerRatio = containerWidth / containerHeight;
  var imgRatio = imgWidth / imgHeight;

  if (imgRatio > containerRatio) {
    imgWidth = containerWidth;
    imgHeight = containerWidth / imgRatio;
  } else if (imgRatio < containerRatio) {
    imgHeight = containerHeight;
    imgWidth = containerHeight * imgRatio;
  } else {
    imgWidth = containerWidth;
    imgHeight = containerHeight;
  }
  //最大缩放比限定
  if (maxScale) {
    containerWidth = maxScale * this.getImageWidth();
    containerHeight = maxScale * this.getImageHeight();
    if (imgWidth > containerWidth) {
      imgHeight = (imgHeight * containerWidth) / imgWidth;
      imgWidth = containerWidth;
    } else if (imgHeight > containerHeight) {
      imgWidth = (imgWidth * containerHeight) / imgHeight;
      imgHeight = containerHeight;
    }
  }
  return {
    width: imgWidth,
    height: imgHeight,
  };
};
//加载数据
BodyMap.prototype.loadBodyPart = function (bodyParts) {
  this.init();
  this.clearBodyPart();
  this.bodyList.clear();
  try {
    if (bodyParts) {
      for (var i = 0; i < bodyParts.length; i++) {
        this.bodyList.add(setBodyPart(bodyParts[i]));
      }
    }
  } catch (ex) {}
  //重置Map
  resetMap(this);
};
BodyMap.prototype.setBodyPart = function (codes) {
  var bodyParts = $BodyMap.getList().getBodyParts();
  if (codes) {
    for (var j = 0; j < codes.length; j++) {
      var code = codes[j];
      for (var i = 0; i < bodyParts.length; i++) {
        if (this.setBodyPart2(bodyParts[i], code)) {
          $BodyMap._container
            .find("#" + code + " input:checkbox")
            .prop("checked", true);
          continue;
        }
      }
    }
    $BodyMap.selectedEvent($BodyMap.selectBodyPartList);
    // 设置父层高亮显示/取消高亮显示
    changeFatherState();
    // 如果是背面则反转
    if ($BodyMap._needReverse) {
      $BodyMap.reverse();
    }
  }
};

//定位某个部位,勾选身体部位
BodyMap.prototype.setBodyPart2 = function (_bodyPart, code) {
  if (_bodyPart.code == code) {
    $BodyMap.selectBodyPartList.push(_bodyPart);
    _bodyPart.setSelected(true);

    if (_bodyPart.positive == 2) {
      if ($BodyMap._needReverse && !$BodyMap.selectOneMode) {
        $BodyMap._needReverse = false;
      } else {
        $BodyMap._needReverse = true;
      }
    }
    return true;
  } else {
    if (_bodyPart.hasChild()) {
      for (var i = 0; i < _bodyPart.getChild().length; i++) {
        if (this.setBodyPart2(_bodyPart.getChild()[i], code)) {
          return true;
        }
      }
    }
  }
  return false;
};
//转身动作
BodyMap.prototype.reverse = function () {
  if (!this._reverse || this.positive == 0) {
    return;
  }
  if (this.positive == 1) {
    //显示背面
    this.setImagePosition(-this.map.width(), 0);
    this.positive = 2;
  } else if (this.positive == 2) {
    this.setImagePosition(0, 0);
    this.positive = 1;
  }
  this.showBodyPart(true);
};
//显示身体部位,notInit=true表示当前在子层，刷新时不初始化当前图
BodyMap.prototype.showBodyPart = function (addEvent, notInit) {
  if (!this.bodyList || !this.bodyList.bodyparts) return;
  var html = "";
  var point, location;
  var zoom = this.getZoom();
  for (var i = 0; i < this.bodyList.bodyparts.length; i++) {
    if (
      this.positive > 0 &&
      this.bodyList.bodyparts[i].positive > 0 &&
      this.bodyList.bodyparts[i].positive != this.positive
    ) {
      continue;
    }
    if (!this.showView) {
      point = this.convertPoint(
        this.bodyList.bodyparts[i].pointX,
        this.bodyList.bodyparts[i].pointY,
        this.bodyList.bodyparts[i].positive
      );
    } else {
      this.showLine = false;
    }
    if (
      this.bodyList.bodyparts[i]._locationX &&
      this.bodyList.bodyparts[i]._locationY
    ) {
      location = this.convertPoint(
        this.bodyList.bodyparts[i]._locationX,
        this.bodyList.bodyparts[i]._locationY,
        this.bodyList.bodyparts[i].positive
      );
      if (
        this.showLine &&
        !this.showView &&
        !(
          this.bodyList.bodyparts[i]._locationX ==
            this.bodyList.bodyparts[i].pointX &&
          this.bodyList.bodyparts[i]._locationY ==
            this.bodyList.bodyparts[i].pointY
        )
      ) {
        //非视图模式下显示线条
        this.bodyList.bodyparts[i]._line = new BodyCanvas();
      }
    }
    if (this.showView) {
      html +=
        '<div class="labelType" style="left:' +
        location.X +
        "px;top:" +
        location.Y +
        "px;";
      if (this.bodyList.bodyparts[i].labelType) {
        html +=
          "background-image:url(" +
          $BodyMap.url +
          "images/label/" +
          this.bodyList.bodyparts[i].labelType +
          ".png);";
      }
      html += '"></div>';
    } else {
      point.X = point.X - this.bodyList.bodyparts[i].name.length * 7;
      if (!this.bodyList.bodyparts[i].hasChild()) {
        point.X = point.X - 7;
      }
      html +=
        '<div id="' +
        this.bodyList.bodyparts[i].code +
        '" style="left:' +
        point.X +
        "px;top:" +
        point.Y +
        "px;";
      if (!this.bodyList.bodyparts[i].hasChild()) {
        html +=
          " min-width:" +
          (this.bodyList.bodyparts[i].name.length * 14 + 15) +
          'px"';
        //叶子节点
        if (this.bodyList.bodyparts[i].isSelected) {
          html +=
            ' class="point is-checked"><input type="checkbox" checked="checked">';
        } else {
          html += ' class="point"><input type="checkbox">';
        }
      } else {
        if (this.bodyList.bodyparts[i].hasSelectedChild) {
          html += '" class="point point2 hasSelectedChild">';
        } else {
          html += '" class="point point2">';
        }
      }
      html += this.bodyList.bodyparts[i].name;
      html += "</div>";
    }
    //
    if (this.bodyList.bodyparts[i]._line) {
      //增加线条
      var fontW = this.bodyList.bodyparts[i].name.length * 14;
      point.X = point.X + (point.X < location.X ? fontW : 0);
      point.Y = point.Y + 10;
      html += this.bodyList.bodyparts[i]._line.drawLine(point, location);
    }
  }
  this.clearBodyPart();
  this.map.parent().append(html);
  //添加对应事件
  if (addEvent && !this.showView) {
    this.bodyList.appendEvent(this.title);
  }

  this.getList()._visible = false;

  if (!notInit) {
    this._bodyPartMap = undefined;
  }
};
BodyMap.prototype.convertPoint = function (x, y, positive) {
  var zoom = this.getZoom();
  var pos = getPosition(this.map);
  if (this.positive == 2 && positive == 2) {
    x = (x - this.getImageWidth() / 2) * zoom.zoomX;
  } else {
    x = x * zoom.zoomX;
  }
  y = y * zoom.zoomY;
  return {
    X: x + pos.left,
    Y: y + pos.top,
  };
};
//清理身体部位
BodyMap.prototype.clearBodyPart = function () {
  if (!this.map) return;
  if (!this.bodyList || !this.bodyList.bodyparts) return;
  for (var i = 0; i < this.bodyList.bodyparts.length; i++) {
    this.map
      .parent()
      .find("#" + this.bodyList.bodyparts[i].code)
      .remove();
  }
  this.map.parent().find(".dot").remove();
};
BodyMap.prototype.getBodyPart = function (code, topCode) {
  if (!this.bodyList) return null;
  for (var i = 0; i < this.bodyList.bodyparts.length; i++) {
    if (!this.bodyList.bodyparts[i].hasChild()) continue;
    if (this.bodyList.bodyparts[i].code == topCode) {
      return this.getBodyPart2(this.bodyList.bodyparts[i], code);
    }
  }
  return null;
};
BodyMap.prototype.getBodyPart2 = function (bodyPart, code) {
  if (bodyPart.code == code) {
    return bodyPart;
  } else if (bodyPart.childrens) {
    for (var i = 0; i < bodyPart.childrens.length; i++) {
      return getBodyPart2(bodyPart.childrens[i]);
    }
  }
  return null;
};
//获取选中部位
BodyMap.prototype.getSelectBodyPart = function () {
  return this.selectedBodyPart;
};
//显示预览视图
BodyMap.prototype.view = function (bodyParts, container) {
  loadCanvas();
  if (!container && !$BodyMap.viewMap) {
    $BodyMap.showView = true;
    $BodyMap.loadBodyPart(bodyParts);
    return;
  }
  if (!$BodyMap.viewMap) {
    $BodyMap.viewMap = new BodyMap();
    $BodyMap.viewMap._container = container;
    $BodyMap.viewMap._container.append('<div class="bodyMap"></div>');
    $BodyMap.viewMap.setImage($BodyMap._backgroundImagePath, $BodyMap._reverse);
    //初始化BodyMap
    $BodyMap.viewMap.setCanvas(".bodyMap");
  }
  $BodyMap.viewMap.showView = true;
  $BodyMap.viewMap.loadBodyPart(bodyParts);
};
BodyMap.prototype.toImage = function (complate) {
  var container;
  var scale = 1;
  if ($BodyMap.viewMap) {
    if (!$BodyMap.viewMap.showView) return;
    container = $BodyMap.viewMap._container;
    scale = $BodyMap.viewMap.getImageWidth() / container.width();
  } else {
    if (!$BodyMap.showView) return;
    container = $BodyMap._container;
    scale = $BodyMap.getImageWidth() / container.width();
  }
  var width = container.width();
  var height = container.height();
  var canvas = document.createElement("canvas");
  if (scale > 2) scale = 2;
  canvas.width = width * scale;
  canvas.height = height * scale;
  canvas.getContext("2d").scale(scale, scale);
  html2canvas(container, {
    scale: scale,
    canvas: canvas,
    width: width,
    height: height,
    useCORS: true,
    logging: false,
  }).then(function (canvas) {
    var context = canvas.getContext("2d");
    // 【重要】关闭抗锯齿
    context.mozImageSmoothingEnabled = false;
    context.webkitImageSmoothingEnabled = false;
    context.msImageSmoothingEnabled = false;
    context.imageSmoothingEnabled = false;

    imgBlob = canvas.toDataURL("image/png"); //将图片转为base64
    //imgBlob = imgBlob.toString().substring(imgBlob.indexOf(",") + 1);
    if (complate) complate(imgBlob);
  });
};
//***********************************************BodyMap*****************

//人体部位列表
var BodyList = function () {
  this.title = "\u5217\u8868";
  this._visible = false;
  //地图
  this.list = undefined;
  //人体部位
  this.bodyparts = [];
};
//人体部列表具体实现类
BodyList.prototype = {
  //添加子部位
  add: function (bodyPart) {
    if (!this.contain(bodyPart)) {
      if (bodyPart.imagePath) {
        //加载图元
        bodyPart.loadImage();
      }
      this.bodyparts.push(bodyPart);
    }
  },
  //是否含有bodyPart
  contain: function (bodyPart) {
    for (var i = 0; i < this.bodyparts.length; i++) {
      if (this.bodyparts[i].code == bodyPart.code) {
        return true;
      }
    }
    return false;
  },
  setContainer: function (listContainer) {
    this.list = $(listContainer);
  },
  getContainer: function () {
    return this.list;
  },
  reset: function () {
    for (var i = 0; i < this.bodyparts.length; i++) {
      this.reset2(this.bodyparts[i]);
    }
  },
  reset2: function (bodyPart) {
    if (!bodyPart.hasChild()) {
      bodyPart.setSelected(false);
    } else {
      bodyPart.hasSelectedChild = false;
      for (var i = 0; i < bodyPart.childrens.length; i++) {
        this.reset2.call(this, bodyPart.childrens[i]);
      }
    }
  },
  getBodyParts: function () {
    return this.bodyparts;
  },
  clear: function () {
    this.bodyparts = [];
  },
  clearList: function () {
    if (!this.list) return;
    this.list.empty();
  },
};
//显示列表
BodyList.prototype.showList = function (addEvent) {
  if (!this.list) return;
  this.clearList();
  var html = "<ul>";
  this._visible = true;
  for (var i = 0; i < this.bodyparts.length; i++) {
    if (!this.bodyparts[i].hasChild()) {
      html += '<li id="' + this.bodyparts[i].code + '">';
      if (this.bodyparts[i].isSelected) {
        html += '<input type="checkbox" checked="checked">';
      } else {
        html += '<input type="checkbox">&nbsp';
      }
      html += "<a>" + this.bodyparts[i].name + "</a>";
    } else {
      html += '<li id="' + this.bodyparts[i].code + '" class="level0_select">';
      html += "<a>" + this.bodyparts[i].name + "</a>";
      html += "<span>&gt;</span>";
    }
    html += "</li>";
  }
  html += "</ul>";

  this.list.append(html);
  if (addEvent) {
    this.appendEvent(this.title);
  }
};
BodyList.prototype.appendEvent = function (map) {
  for (var i = 0; i < this.bodyparts.length; i++) {
    if (!this.bodyparts[i].hasChild()) {
      $("#" + this.bodyparts[i].code)
        .unbind("click")
        .click(
          {
            index: i,
          },
          function (e) {
            selectedBodyPart($(this).find("input:checkbox"), e.data.index);
          }
        );
      // $("#" + this.bodyparts[i].code)
      //   .find("input:checkbox")
      //   .unbind("click")
      //   .click(
      //     {
      //       index: i
      //     },
      //     function(e) {
      //       selectedBodyPart($(this), e.data.index);
      //       e.stopPropagation(); //阻止事件传递
      //     }
      //   );
    } else {
      $("#" + this.bodyparts[i].code)
        .unbind("click")
        .click(
          {
            index: i,
            maptitle: map,
          },
          function (e) {
            if ($BodyMap.getCurrent().getList()) {
              //显示局部地图
              $BodyMap
                .getCurrent()
                .getList()
                .bodyparts[e.data.index].showView(
                  $BodyMap.getContainer(),
                  e.data.maptitle
                );
            }
          }
        );
    }
  }
};

//***********************************************BodyList*****************
//人体部位
var BodyPart = function () {
  //部位代码
  this.code = undefined;
  //所属层级
  this.level = 0;
  //是否选择
  this.isSelected = false;
  //是否不可治疗
  this.isNoTreatment = false;
  //部位类别
  //0代表全部，1代表正面，2代表背面
  this.positive = 0;
  //性别
  this.gender = 0;
  //显示名称
  this.name = undefined;
  //部位具体名称
  this.bodyPartName = undefined;
  //对应图元资源路径
  this.imagePath = undefined;
  //对应的图元
  this.image = undefined;
  //相对X坐标
  this.pointX = undefined;
  //相对Y坐标
  this.pointY = undefined;
  //相对X位置
  this._locationX = undefined;
  //相对Y位置
  this._locationY = undefined;
  //标签类别
  this.labelType = undefined;
  //子部位
  this.childrens = [];
  // 是否有子元素被选中
  this.hasSelectedChild = false;
  //父级部位
  this._parent = undefined;
  //线条
  this._line = undefined;
};
//人体部具体实现类
BodyPart.prototype = {
  addChild: function (_bodyPart) {
    _bodyPart._parent = this;
    _bodyPart.positive = this.positive;
    _bodyPart.isSelected = false;
    _bodyPart.level = this.level + 1;
    _bodyPart.loadImage();
    this.childrens.push(_bodyPart);
  },
  getImage: function () {
    return this.image;
  },
  hasChild: function () {
    return this.childrens.length > 0;
  },
  setSelected: function (selected) {
    this.isSelected = selected;
  },
  loadImage: function () {
    if (this.imagePath) {
      this.image = new Image();

      this.image.src = this.imagePath;
    }
  },
  //获取子部位
  getChild: function () {
    return this.childrens;
  },
  getFullName: function () {
    var name = this.name;
    var parent = this._parent;
    while (parent) {
      name = parent.name + ">>" + name;
      parent = parent._parent;
    }
    return name;
  },
  showView: function (container, title) {
    if (!container) return;
    if (!this.hasChild()) return;
    var _reverse = false;
    var _imagePath = this.imagePath;
    if (!_imagePath) {
      _imagePath = $BodyMap.getCurrent()._backgroundImagePath;
      _reverse = $BodyMap.getCurrent()._reverse;
    }
    if (_imagePath) {
      var bodyPartId = "bodyPart_Containter";
      //显示局部地图
      if (container.children("#" + bodyPartId).length > 0) {
        container.children("#" + bodyPartId).remove();
      }
      container.children().hide();
      var html = '<div id="' + bodyPartId + '" class="map_tab">';

      html += '<div class="menuTitle">';

      var titles = [];
      titles.push({
        id: "bodyPartTitle0",
        title: this.name,
        code: this.code,
      });

      var parent = this._parent;

      while (parent) {
        titles.push({
          id: "bodyPartTitle" + titles.length,
          title: parent.name,
          code: parent.code,
        });
        parent = parent._parent;
      }

      titles.push({
        id: "bodyPartTitle",
        title: title,
        code: 0,
      });

      for (var i = titles.length - 1; i >= 0; i--) {
        if (i < titles.length - 1) {
          html += "<span>&nbsp;&gt;&gt;</span>";
        }
        html +=
          '&nbsp;<span id="' +
          titles[i].id +
          '" class="titleName">' +
          titles[i].title +
          "</span>";
      }
      html += "</div>";

      html += '<div class="bodyPartMap"></div>';
      html += "</div>";

      container.append(html);

      //添加事件
      container
        .find("#bodyPartTitle")
        .unbind("click")
        .click(function () {
          $BodyMap.init();
        });

      for (var i = 1; i < titles.length - 1; i++) {
        container
          .find("#" + titles[i].id)
          .unbind("click")
          .click(
            {
              index: i,
              title: title,
            },
            function (event) {
              $("#" + bodyPartId).remove();
              var bodyPart = $BodyMap.getBodyPart(
                titles[event.data.index].code,
                titles[event.data.index].code
              );
              if (bodyPart != null) {
                bodyPart.showView($BodyMap.getContainer(), event.data.title);
              }
            }
          );
      }

      $BodyMap._bodyPartMap = new BodyMap();
      $BodyMap._bodyPartMap.title = title;
      //控制二阶人体图是否显示人体图
      // $BodyMap._bodyPartMap.showLine = false;
      //设置Llist
      $BodyMap._bodyPartMap.getList().list = $BodyMap.getList().getContainer();

      //设置人体图，内存中缓存
      $BodyMap._bodyPartMap.setImage(_imagePath, _reverse);

      $BodyMap._bodyPartMap.setCanvas(".bodyPartMap");

      for (var i = 0; i < this.childrens.length; i++) {
        $BodyMap._bodyPartMap.getList().add(this.childrens[i]);
      }

      resetMap($BodyMap._bodyPartMap);
    }
  },
};

//***********************************************BodyPart*****************
var BodyCanvas = function () {
  this._dot = 1;
  this._lines = [];
};
BodyCanvas.prototype = {
  drawPoint: function (id, opts) {
    return (
      '<span id="' +
      id +
      '" class="dot" style="left:' +
      opts.left +
      "px; top:" +
      opts.top +
      'px"></span>'
    );
  },
  drawLine: function (start, end) {
    var html = "";
    //var slope = 0; //斜率
    var hdist = end.X - start.X;
    var vdist = end.Y - start.Y;

    //斜边长度
    var diagonal = Math.sqrt(Math.pow(hdist, 2) + Math.pow(vdist, 2));
    //计算两点之间的点的数量
    var pn = parseInt(diagonal / this._dot);
    if (pn < 3) {
      return html;
    }
    //相邻两点间的垂直距离
    var vgap = Math.abs(vdist) / pn;
    //相邻两点间的水平距离
    var hgap = Math.abs(hdist) / pn;
    this._lines = [];
    for (var i = 0; i < pn; i++) {
      this._lines.push("BodyCanvas" + i);

      //描点
      html += this.drawPoint(this._lines[i], {
        left:
          hgap * i * (end.X < start.X ? -1 : 1) * (hdist == 0 ? 0 : 1) +
          start.X,
        top: vgap * i * (end.Y < start.Y ? -1 : 1) + start.Y,
      });
    }
    return html;
  },
};

//***********************************************BodyCanvas*****************

//设置人体部位
function setBodyPart(bodyPart) {
  var _bodyPart = new BodyPart();
  _bodyPart.code = bodyPart.code;
  _bodyPart.name = bodyPart.name;
  _bodyPart.bodyPartName = bodyPart.bodyPartName;
  _bodyPart.contentName = bodyPart.contentName;
  _bodyPart.pointX = bodyPart.pointX;
  _bodyPart.pointY = bodyPart.pointY;

  _bodyPart.labelType = bodyPart.labelType;
  if (bodyPart.locationX) {
    //具体位置点
    _bodyPart._locationX = bodyPart.locationX;
  }
  if (bodyPart.locationY) {
    //具体位置点
    _bodyPart._locationY = bodyPart.locationY;
  }
  if (bodyPart.positive) {
    _bodyPart.positive = bodyPart.positive;
  }
  _bodyPart.level = bodyPart.level;
  if (bodyPart.imagePath) {
    _bodyPart.imagePath = $BodyMap.url + "images/" + bodyPart.imagePath;
  }

  if (bodyPart.childrens && bodyPart.childrens.length > 0) {
    for (var i = 0; i < bodyPart.childrens.length; i++) {
      _bodyPart.addChild(setBodyPart.call(this, bodyPart.childrens[i]));
    }
  }
  return _bodyPart;
}

//添加监听事件
function addBodyMapEventListener() {
  if (!$BodyMap) return;
  $BodyMap._container.unbind("contextmenu").bind("contextmenu", function () {
    return false;
  });
  $BodyMap._container.unbind("selectstart").bind("selectstart", function () {
    return false;
  });
  $BodyMap._container
    .find("#mapTab")
    .unbind("click")
    .click(function () {
      $BodyMap._container.find("#listTab").removeClass("tab__item_selected");
      $BodyMap._container.find(".list_tab").hide();
      //清理List防止ID一致
      $BodyMap.getList().clearList();

      $BodyMap._container.find("#mapTab").addClass("tab__item_selected");
      $BodyMap._container.find(".map_tab").show();
      //$(selector).show(speed,callback);
      resetMap($BodyMap);
    });

  $BodyMap._container
    .find("#listTab")
    .unbind("click")
    .click(function () {
      $BodyMap._container.find("#mapTab").removeClass("tab__item_selected");
      $BodyMap._container.find(".map_tab").hide();
      //清理BodyPart防止ID一致
      $BodyMap.clearBodyPart();

      $BodyMap._container.find("#listTab").addClass("tab__item_selected");
      $BodyMap._container.find(".list_tab").show();
      //显示列表
      $BodyMap.getList().showList(true);
    });

  //转身
  $BodyMap._container
    .find(".reverse")
    .unbind("click")
    .click(function () {
      $BodyMap.reverse();
    });
}

//设置地图
function resetMap(bodyMap) {
  var container = $BodyMap.getContainer();

  if (!container) return;
  var _height = container.height() - getTabHeight();

  var _width = container.width();

  //调整大小
  var _size = bodyMap.scalingSize(_width, _height, 2);
  _size.width = (_size.width * 14) / 15;
  _size.height = (_size.height * 14) / 15;
  if (bodyMap.canReverse()) {
    container.find(".reverse").show();
  } else {
    container.find(".reverse").hide();
  }
  //设置容器大小
  bodyMap.setSize(_size.width, _size.height);
  bodyMap.setLocation((_width - _size.width) / 2, (_height - _size.height) / 2);

  //设置背景图片大小
  bodyMap.setImageSize(_size.width, _size.height);

  //显示位置,true表示开启响应事件
  bodyMap.showBodyPart(true);
}

//获取选择卡
function getTabHeight() {
  if ($BodyMap._container && $BodyMap._container.find(".tab").is(":visible")) {
    return $BodyMap._container.find(".tab").height();
  }
  if ($("#bodyPart_Containter .menuTitle").is(":visible")) {
    return $("#bodyPart_Containter .menuTitle").height();
  }
  return 0;
}

function getPosition(target) {
  if (!target.offset())
    return {
      top: 0,
      left: 0,
    };
  var left = target.offset().left;
  var top = target.offset().top;
  target = target.parent();
  if (target.length > 0 && target.offset()) {
    left = left - target.offset().left;
    top = top - target.offset().top;
  }
  return {
    top: top,
    left: left,
  };
}
// 设置是否有子项被选中
function setSelectedChild(bodyPart) {
  bodyPart.hasSelectedChild = false;
  var num = 0;
  for (var i = 0; i < bodyPart.childrens.length; i++) {
    if (bodyPart.childrens[i].hasChild()) {
      // 有子项，递归调用
      bodyPart.hasSelectedChild = setSelectedChild(bodyPart.childrens[i]);
      if (bodyPart.hasSelectedChild) {
        num++;
      } else {
        if (num > 0) {
          bodyPart.hasSelectedChild = true;
        }
      }
    } else {
      for (var j = 0; j < $BodyMap.selectBodyPartList.length; j++) {
        if ($BodyMap.selectBodyPartList[j].code == bodyPart.childrens[i].code) {
          bodyPart.hasSelectedChild = true;
          num++;
          break;
        } else {
          if (num == 0) {
            bodyPart.hasSelectedChild = false;
          }
        }
      }
    }
  }
  return bodyPart.hasSelectedChild;
}
function changeFatherState() {
  for (var i = 0; i < $BodyMap.getList().getBodyParts().length; i++) {
    var bodyPart = $BodyMap.getList().getBodyParts()[i];
    setSelectedChild(bodyPart);
  }
  $BodyMap.showBodyPart(true, true);
}

function selectedBodyPart(sender, index) {
  var _bodyPart = $BodyMap.getCurrent().getList().bodyparts[index];
  if ($BodyMap.selectOneMode) {
    sender.parent().parent().find("input:checkbox").prop("checked", false);
  }

  if (_bodyPart.isSelected) {
    sender.prop("checked", false);
    if ($BodyMap.selectedCancelEvent) {
      //如果取消勾选,就移除该该元素
      for (var i = 0; i < $BodyMap.selectBodyPartList.length; i++) {
        if ($BodyMap.selectBodyPartList[i].code == _bodyPart.code) {
          $BodyMap.selectBodyPartList.splice(i, 1);
          break;
        }
      }
      $BodyMap.selectedCancelEvent($BodyMap.selectBodyPartList);
    }
  } else {
    sender.prop("checked", true);
    if ($BodyMap.selectedEvent) {
      if ($BodyMap.selectOneMode) {
        //清理其他选中项目
        $BodyMap.getList().reset();
        $BodyMap.selectBodyPartList = [];
      }
      $BodyMap.selectBodyPartList.push(_bodyPart);
      $BodyMap.selectedEvent($BodyMap.selectBodyPartList);
    }
  }
  _bodyPart.isSelected = !_bodyPart.isSelected;
  // 设置父层高亮显示/取消高亮显示
  changeFatherState();
}

function loadCanvas() {
  $Common.usingJS("js/html2canvas.min.js", $BodyMap.url);
}
//人体部位地图
var $BodyMap = new BodyMap();
$BodyMap.url = $Common.getScriptSrc();
//设置人体图，内存中缓存
$BodyMap.setRalativeImage("images/body.png", true);
