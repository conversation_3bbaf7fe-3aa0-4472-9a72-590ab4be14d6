<!--
 * FilePath     : \ccc.web\src\components\selector\bedSelector.vue
 * Author       : 杨欣欣
 * Date         : 2022-09-30 15:35
 * LastEditors  : 郭鹏超
 * LastEditTime : 2022-11-08 11:10
 * Description  : 
 * CodeIterationRecord: 
-->

<template>
  <div class="bed-selector">
    <span :class="{ label: label }">{{ label }}</span>
    <el-select v-model="bedID" placeholder="请选择床号" @change="changeValue" :style="style">
      <el-option v-for="bed in bedList" :key="bed.bedID" :label="bed.bedNumber" :value="bed.bedID"></el-option>
    </el-select>
  </div>
</template>

<script>
import { GetBedList } from "@/api/BedList";
import { mapGetters } from "vuex";
export default {
  props: {
    label: {
      type: String,
      default: "床号：",
    },
    value: {
      type: Number,
    },
    stationID: {
      type: Number,
      default: undefined,
    },
    width: {
      type: String,
      default: "90px",
    },
  },
  watch: {
    stationID: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal) {
          if (this.bedID) {
            this.$emit("input", undefined);
          }
          this.init();
        }
      },
    },
    value: {
      immediate: true,
      handler(newVal, oldVal) {
        this.bedID = newVal;
      },
    },
    localBedList() {
      // 页面有多个此组件时，后面组件请求会被拦截，需要监听缓存手动放入List
      if (!this.bedList.length) {
        this.bedList = this.localBedList[this.stationID];
      }
    },
  },
  computed: {
    ...mapGetters({
      localBedList: "getBedList",
    }),
    style() {
      return {
        width: this._convertUtil.getHeigt(this.width, true),
      };
    },
  },
  data() {
    return {
      bedID: undefined,
      bedList: [],
    };
  },
  methods: {
    init() {
      // 未选择病区，不展示床位数据
      if (!this.stationID) {
        return;
      }
      // 走缓存
      if (this.localBedList && Object.keys(this.localBedList).length > 0) {
        this.bedList = this.localBedList[this.stationID];
        return;
      }
      // 后端请求
      GetBedList().then((result) => {
        if (this._common.isSuccess(result)) {
          this.bedList = result.data[this.stationID];
          this.$store.commit("session/setBedList", result.data);
        }
      });
    },
    changeValue(bedID) {
      if (!bedID) {
        this.$emit("input", undefined);
        return;
      }
      // 实现双向绑定
      this.$emit("input", bedID);
      this.$emit("change", undefined);
      var bed = this.bedList.find((bed) => bed.bedID == bedID);
      if (bed) {
        this.$emit("select-item", bed.bedNumber);
      }
    },
  },
};
</script>
<style lang="scss">
.bed-selector {
  display: inline-block;
  .label {
    margin-left: 5px;
  }
  .el-select {
    .el-input.is-disabled {
      .el-input__inner {
        color: #606266;
      }
    }
    .el-input__inner {
      padding-left: 5px;
    }
  }
}
</style>
