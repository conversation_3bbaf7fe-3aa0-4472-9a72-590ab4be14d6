<!--queryData
 * FilePath     : \nursingBoard.webe:\CCC3.1\Medical\Web\ccc.web\src\pages\stationSwitch\index.vue
 * Author       : 杨欣欣
 * Date         : 2021-10-08 14:04
 * LastEditors  : 马超
 * LastEditTime : 2023-02-07 14:19
 * Description  : 病区权限维护主页面
-->
<template>
  <base-layout class="emp-dept-switch-page">
    <div slot="header">
      <!-- 筛选与搜索功能区域 -->
      <!-- 绑定子组件的hospitalFlag值为true，插入全院检索 -->
      <station-selector width="150" v-model="stationID" :userID="user.userID" :label="stationLabel"></station-selector>
      <span class="label">查询：</span>
      <el-input
        v-model="keyWords"
        style="width: 200px"
        @keyup.enter.native="queryData"
        placeholder="输入工号或姓名"
        class="search-input"
        clearable
        @clear="getData"
      >
        <i slot="append" class="iconfont icon-search" @click="queryData"></i>
      </el-input>
    </div>
    <div slot-scope="data">
      <!-- 数据表格区域 -->
      <el-table
        :height="data.height"
        v-loading="loading"
        element-loading-text="加载中……"
        border
        stripe
        :data="tableData"
        ref="employeeDepartmentSwitchTable"
        :highlightCurrentRow="false"
      >
        <!-- 列对应属性 -->
        <el-table-column prop="employeeName" label="姓名" width="80" align="center"></el-table-column>
        <!-- 列对应属性 -->
        <el-table-column prop="employeeID" label="工号" width="80" align="center"></el-table-column>
        <!-- 列对应属性 -->
        <el-table-column prop="departmentName" label="所属病区" width="180" align="center"></el-table-column>
        <!-- 列对应属性 -->
        <el-table-column prop="title" label="职称" width="80" align="center"></el-table-column>
        <!-- 列对应属性 -->
        <el-table-column label="权限病区" prop="departmentList">
          <template slot-scope="scope">
            <el-tag
              effect="plain"
              v-for="(item, index) in scope.row.departmentList"
              :key="index"
              v-if="item.stationName !== ''"
            >
              {{ item.stationName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="70">
          <template slot-scope="scope">
            <el-tooltip content="维护">
              <i class="iconfont icon-edit" @click="toEdit(scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        v-loading="dialogLoading"
        element-loading-text="保存中……"
        v-dialogDrag
        :close-on-click-modal="false"
        title="维护权限病区"
        :visible.sync="editDialog"
        custom-class="switch-dialog"
      >
        <el-checkbox :indeterminate="!checkAll" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
        <el-checkbox-group
          v-if="editDialog"
          class="checkbox-group"
          v-model="checkedList"
          @change="handleCheckedCitiesChange"
        >
          <el-checkbox class="checkbox" v-for="(data, index) in stationList" :label="data.stationCode" :key="index">
            {{ data.stationName }}
          </el-checkbox>
        </el-checkbox-group>
        <span slot="footer">
          <el-button @click="closeBtn()">取消</el-button>
          <el-button type="primary" @click="saveBtn()">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout.vue";
import stationSelector from "@/components/selector/stationSelector";
import { UpdateSwitch, GetSwitchData } from "@/api/StationSwitch";
import { GetStationList } from "@/api/Station";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    stationSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      checkAll: false,
      loading: false,
      dialogLoading: false,
      stationID: 999999,
      //显示出来的数据集，会因模糊查询而改变
      tableData: [],
      //克隆的完整数据集
      cloneTableData: [],
      editDialog: false,
      keyWords: "",
      employeeID: "",
      // 选中的病区Codes
      checkedList: [],
      // 所属病区Codes
      departmentCode: "",
      //dialog中的病区列表
      stationList: [],
      stationOptions: [],
      stationLabel: "病区：",
      defaultStationCode: undefined,
    };
  },
  watch: {
    // 切换病区时，重新获取数据
    stationID: {
      handler() {
        this.getDataAndClearSerch();
      },
    },
    //监听对话框关闭时，清空checkedList
    editDialog: {
      handler(newVal) {
        if (!newVal) {
          this.checkedList = [];
        }
      },
    },
  },
  created() {
    this.stationID = this.user.stationID;
    this.getData();
    this.GetDeptList();
  },
  methods: {
    handleCheckAllChange(val) {
      this.checkedList = val ? this.stationOptions : [];
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.stationList.length;
    },
    // 获取表格数据
    async getData(stationID = this.stationID) {
      let params = {
        stationID: stationID,
      };
      this.loading = true;
      //获取前清空历史数据
      this.tableData = [];
      await GetSwitchData(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.tableData = res.data;
          //仅在查全院时才进行克隆，保证克隆表数据完整
          if (stationID === 999999) {
            this.cloneTableData = this._common.clone(this.tableData);
          }
        }
      });
    },
    getDataAndClearSerch() {
      //仅当切换病区时清空
      this.keyWords = "";
      this.getData();
    },
    //获取弹窗中的病区单元
    GetDeptList() {
      GetStationList({ index: Math.random() }).then((result) => {
        if (this._common.isSuccess(result)) {
          this.stationList = result.data;
          this.stationList.forEach((m) => {
            this.stationOptions.push(m.stationCode);
          });
        }
      });
    },
    //模糊查询
    async queryData() {
      await this.getData(999999);
      if (this.keyWords) {
        // 按给定的关键字进行过滤，并赋值给tableData
        this.tableData = this.cloneTableData.filter((element) => {
          let nameStr = element.employeeName;
          if (nameStr.indexOf(this.keyWords) != -1) {
            return element.employeeName;
          }
          let idStr = element.employeeID;
          if (idStr.indexOf(this.keyWords) != -1) {
            return element.employeeID;
          }
        });
      }

      //修复el-table表头错位
      this.$nextTick(() => {
        this.$refs.employeeDepartmentSwitchTable.doLayout();
      });
    },
    //打开修改权限窗口
    toEdit(row) {
      this.editDialog = true;
      //将工号、所属病区Code带入dialog
      this.employeeID = row.employeeID;
      this.departmentCode = row.departmentCode;
      //将当前行的权限病区code放入checkedList，实现回显
      row.departmentList.forEach((element) => {
        if (element.stationCode) {
          this.checkedList.push(element.stationCode);
        }
      });
      this.defaultStationCode = this.stationList.find(
        (station) => station.stationCode === row.departmentCode
      )?.stationCode;
    },
    //保存修改权限
    saveBtn() {
      if (this.checkedList.length == 0 && this.defaultStationCode) {
        this.checkedList.push(this.defaultStationCode);
      }
      let params = {
        EmployeeID: this.employeeID,
        StationCodes: this.checkedList,
      };
      this.dialogLoading = true;
      UpdateSwitch(params).then((res) => {
        this.dialogLoading = false;
        this.editDialog = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "修改权限成功！");
          this.getData();
        }
      });
    },
    closeBtn() {
      this.editDialog = false;
    },
  },
};
</script>

<style lang="scss">
.emp-dept-switch-page {
  height: 100%;

  .search-input {
    width: 70%;

    .el-input-group__append {
      padding: 0 5px;
    }

    i {
      color: #8cc63e;
    }
  }

  .el-tag {
    margin: 4px;
  }

  .label {
    margin-left: 10px;
  }

  .switch-dialog {
    width: 450px;

    .checkbox-group {
      .checkbox {
        margin: 5px 0;
        width: 200px;
      }

      //禁止去勾复选框
      .el-checkbox.is-disabled {
        .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
          background-color: $base-color;
          border-color: $base-color;

          &:after {
            border-color: #ffffff;
          }
        }

        .el-checkbox__label {
          color: $base-color;
        }
      }
    }
  }
}
</style>
