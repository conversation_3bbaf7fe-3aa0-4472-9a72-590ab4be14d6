<!--
 * FilePath     : \src\pages\patientCosts\index.vue
 * Author       : 苏军志
 * Date         : 2021-08-25 14:50
 * LastEditors  : 苏军志
 * LastEditTime : 2021-08-26 17:25
 * Description  : 
-->
<template>
  <base-layout class="patient-costs">
    <div slot="header">
      <span class="label">开始日期:</span>
      <el-date-picker
        v-model="startDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="pickerOptionsStart"
      ></el-date-picker>
      <span class="label">结束日期:</span>
      <el-date-picker
        v-model="endDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="pickerOptionsEnd"
      ></el-date-picker>
      <el-button class="query-button" icon="iconfont icon-search" @click="getPatientCosts">查询</el-button>
      <div v-if="totalCost" class="top-right">在院期间总费用：{{ totalCost }}元</div>
    </div>
    <u-table
      slot-scope="layout"
      ref="costsTable"
      border
      stripe
      use-virtual
      :height="layout.height"
      :row-height="30"
      show-summary
      :total-option="totalOption"
      v-loading="loading"
      element-loading-text="加载中……"
      :default-sort="{ prop: 'chargeTime', order: 'ascending' }"
    >
      <u-table-column label="项目名称" prop="itemName" min-width="150" header-align="center"></u-table-column>
      <u-table-column label="规格" prop="specs" min-width="100" header-align="center"></u-table-column>
      <u-table-column label="单位" prop="unit" min-width="60" align="center"></u-table-column>
      <u-table-column label="数量" prop="amount" width="60" align="center"></u-table-column>
      <u-table-column label="单价(元)" prop="price" width="80" header-align="center" align="right"></u-table-column>
      <u-table-column
        label="金额(元)"
        prop="amountOfMoney"
        width="80"
        header-align="center"
        align="right"
      ></u-table-column>
      <u-table-column
        label="费用名称"
        prop="costName"
        width="100"
        align="center"
        :filters="costNameList"
        :filter-method="filterData"
      ></u-table-column>
      <u-table-column label="计费时间" prop="chargeTime" sortable width="160" align="center">
        <template slot-scope="cost">
          <span v-formatTime="{ value: cost.row.chargeTime, type: 'datetime' }"></span>
        </template>
      </u-table-column>
    </u-table>
  </base-layout>
</template>

<script>
import { GetPatientCosts } from "@/api/PatientCosts";
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
export default {
  components: { baseLayout },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
    }),
  },
  watch: {
    patient: {
      handler(newValue) {
        this.totalCost = undefined;
        this.costsList = [];
        this.getPatientCosts();
      },
    },
  },
  data() {
    let that = this;
    return {
      loading: false,
      totalCost: undefined,
      costsList: [],
      startDate: undefined,
      endDate: undefined,
      //时间的禁用
      pickerOptionsStart: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.endDate).getTime();
        },
      },
      pickerOptionsEnd: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.startDate).getTime() - 8.64e7;
        },
      },
      totalOption: [{ label: "金额(元)", unit: "元" }],
      costNameList: [],
    };
  },
  created() {
    this.startDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
    this.endDate = this.startDate;
  },
  methods: {
    // 获取病人已有事件列表
    getPatientCosts() {
      if (this.loading) {
        return;
      }
      let startTime = this._datetimeUtil.formatDate(this.startDate, "yyyy-MM-dd") + " 00:00:00";
      let endTime = this._datetimeUtil.formatDate(this.endDate, "yyyy-MM-dd") + " 23:59:59";
      this.loading = true;
      let params = {
        chartNo: this.patient.chartNo,
        numberOfAdmissions: this.patient.numberOfAdmissions,
        startTime: startTime,
        endTime: endTime,
      };
      this.costsList = [];
      GetPatientCosts(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result) && result.data) {
          this.costsList = result.data.costsItems;
          this.totalCost = result.data.totalCost;
          this.setFilterParams();
          this.$refs.costsTable.reloadData(this.costsList);
        }
      });
    },
    // 设置筛选列表
    setFilterParams() {
      this.costsList.forEach((cost) => {
        //动态获取settingType列种类
        var costName = this.costNameList.find((costName) => {
          return costName.value == cost.costName.trim();
        });
        if (!costName) {
          this.costNameList.push({ text: cost.costName.trim(), value: cost.costName.trim() });
        }
      });
    },
    // 筛选数据
    filterData(value, row, column) {
      const property = column["property"];
      return row[property] === value;
    },
  },
};
</script>

<style lang="scss">
.patient-costs {
  .label,
  .query-button {
    margin-left: 10px;
  }
  .top-right {
    float: right;
    color: #ff0000;
    font-weight: bold;
  }
}
</style>