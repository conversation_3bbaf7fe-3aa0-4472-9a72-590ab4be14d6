<!--
 * FilePath     : \src\pages\attendance\index.vue
 * Author       : 孟昭永
 * Date         : 2020-06-14 09:34
 * LastEditors  : 马超
 * LastEditTime : 2025-07-03 11:53
 * Description  :
-->
<template>
  <base-layout
    headerHeight="auto"
    class="attendance"
    v-loading.fullscreen.lock="dialogLoading"
    element-loading-text="加载中……"
  >
    <div class="attendance-header" slot="header">
      <span class="attendance-num">
        <span class="unfinished">未派班：{{ attendanceCount[1] }}</span>
        <br />
        <span class="finished">已派班：{{ attendanceCount[2] }}</span>
      </span>

      <label class="top-label">岗位派班：</label>
      <el-switch v-model="postAttendanceSwitch"></el-switch>

      <label class="top-label">日期：</label>
      <el-date-picker
        class="attendace-date"
        v-model="attendaceDate"
        type="date"
        placeholder="选择日期时间"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        @change="changeDate"
      ></el-date-picker>

      <!-- 班别选择器组件 -->
      <shift-selector v-model="attendaceShiftID" :stationID="stationID" @select-item="selectShiftItem"></shift-selector>

      <label class="top-label">用户：</label>
      <el-input class="query-input" v-model="queryInput" placeholder="请输入姓名、工号" @keyup.enter.native="queryUser">
        <i slot="append" class="iconfont icon-search" @click="queryUser"></i>
      </el-input>

      <span class="attendance-explain" v-if="explainFlag">
        <span class="working-status">当班</span>
        <br />
        <span class="standby-status">其他班</span>
        <br />
        <span class="vacation-status">休假</span>
      </span>

      <el-button
        class="import-yesterday"
        type="primary"
        icon="iconfont icon-bring-into"
        @click="importYesterdayAttendancec"
      >
        带入前日
      </el-button>
    </div>

    <!-- 表格内容 -->
    <el-table
      v-loading="tableLoading"
      element-loading-text="加载中……"
      ref="attendanceTable"
      :data="tableDataNurse"
      height="100%"
      border
    >
      <el-table-column label="责任护士" align="center" width="100px">
        <template slot-scope="scope">
          <div
            v-if="explainFlag"
            :class="{
              'standby-status': scope.row.shiftID !== attendaceShiftID,
              'vacation-status': scope.row.shiftID == 0,
            }"
          >
            {{ scope.row.name }}{{ "(" + getAttendaceInpatientCount(scope.row.userID) + ")" }}
            <br />
            {{ scope.row.userID }}
          </div>
          <div v-if="!explainFlag">
            {{ scope.row.name }}{{ "(" + getAttendaceInpatientCount(scope.row.userID) + ")" }}
            <br />
            {{ scope.row.userID }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="层级" align="center" width="100px" v-if="capabilityLevelFlag">
        <template slot-scope="scope">
          <span v-if="scope.row.capabilityLevel">{{ scope.row.capabilityLevel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作量" align="center" width="80px">
        <template slot-scope="scope">
          <el-progress
            class="workload"
            :text-inside="true"
            :show-text="false"
            :stroke-width="20"
            :percentage="getWorkloadPercentage(scope.row)"
          ></el-progress>
          {{ scope.row.successSchedule }}/{{ scope.row.allSchedule }}
        </template>
      </el-table-column>

      <el-table-column label="主责病人">
        <template slot-scope="scope">
          <attendance-card
            v-for="(item, index) in getAttendanceCardInfos(scope.row, 'Principal')"
            :key="index"
            :item="item"
            @getTabelData="getPageData"
          ></attendance-card>
        </template>
      </el-table-column>

      <el-table-column label="次责病人">
        <template slot-scope="scope">
          <attendance-card
            v-for="(item, index) in getAttendanceCardInfos(scope.row, 'secondary')"
            :key="index"
            :item="item"
            @getTabelData="getPageData"
          ></attendance-card>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="130px">
        <template slot-scope="scope" v-if="!onlyView">
          <el-tooltip effect="dark" content="主责派班" placement="bottom">
            <i class="iconfont icon-first-attendance" @click="getPrincipalAttendance(scope.row)"></i>
          </el-tooltip>
          <el-tooltip effect="dark" content="次责派班" placement="bottom">
            <i class="iconfont icon-second-attendance" @click="getSecondaryAttendance(scope.row)"></i>
          </el-tooltip>
          <el-tooltip effect="dark" content="带入前日派班" placement="bottom">
            <i class="iconfont icon-bring-into" @click="importYesterdayAttendancecByNurseID(scope.row)"></i>
          </el-tooltip>
          <el-tooltip effect="dark" content="删除派班" placement="bottom">
            <i class="iconfont icon-del" @click="deleteAttendancecInfos(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 责护派班 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="nurseAttendanceDialogTitle"
      :visible.sync="nurseAttendanceDialogVisible"
    >
      <div class="nurse-attendance">
        <div class="nurse-attendance-head">
          <span>分类选择:</span>
          <span>
            <el-radio v-model="attendanceRadio" label="undoneAttendance" @change="switchAttendanceRadio">
              未派班病人
            </el-radio>
          </span>
          <span>
            <el-radio v-model="attendanceRadio" label="doneAttendance" @change="switchAttendanceRadio">
              已派一名责护病人
            </el-radio>
          </span>
          <span>
            <el-checkbox v-model="allChecked" @change="setAllChecked">全选</el-checkbox>
          </span>

          <span v-if="crossStationswitch">
            <span class="nurse-cross-station">
              <el-select v-model="crossStation" placeholder="请选择" @change="switchStation">
                <el-option
                  v-for="item in crossStationList"
                  :key="item.id"
                  :label="item.stationName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </span>
          </span>
        </div>
        <div class="nurse-attendance-body">
          <attendance-card
            v-for="(item, index) in nurseAttendanceInfos"
            :key="index"
            :item="item"
            :checked="item.checked"
            @getTabelData="getPageData"
            @postItemData="getItemData"
          ></attendance-card>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="nurseAttendanceDialogVisible = false">取消</el-button>

        <el-button type="primary" @click="saveNurseAttendance">确定</el-button>
      </span>
    </el-dialog>

    <!-- 岗位派班 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="postAttendanceDialogTitle"
      :visible.sync="postAttendanceDialogVisible"
      width="500px"
    >
      <div class="post-cross-station" v-if="crossStationswitch">
        <span>
          <el-select v-model="crossStation" placeholder="请选择" @change="switchStation">
            <el-option
              v-for="item in crossStationList"
              :key="item.id"
              :label="item.stationName"
              :value="item.id"
            ></el-option>
          </el-select>
        </span>
        <span class="post-cross-station-label">可跨病区</span>
      </div>
      <div class="fixed-post-bed">
        <label>是否固定当前班次岗床派班：</label>
        <el-switch v-model="fixedPostBed"></el-switch>
      </div>
      <div class="fixed-post-bed">
        <label>岗位分组：</label>
        <el-select v-model="jobGroupID" placeholder="岗位分组" @change="changeJobGroupValue">
          <el-option v-for="(item, index) in jobGroupList" :key="index" :label="item" :value="index"></el-option>
        </el-select>
      </div>
      <el-radio-group class="post-radio-group" v-model="postAttendanceRadio">
        <el-radio
          class="post-radio-item"
          :label="index"
          border
          v-for="(item, index) in this.postAttendanceInfos"
          :key="index"
        >
          {{ item }}
        </el-radio>
      </el-radio-group>

      <span slot="footer" class="dialog-footer">
        <el-button @click="postAttendanceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="postAttendance">确定</el-button>
      </span>
    </el-dialog>
  </base-layout>
</template>
<script>
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
import shiftSelector from "@/components/selector/shiftSelector";
import attendanceCard from "./components/attendanceCard";
import {
  GetAttendanceCount,
  GetWeb,
  GetAttendanceBed,
  GetOneNurseCarePatient,
  MultiSave,
  AttendanceFast,
  GetNurseJob,
  GetDeptmentJobGroupList,
  LoadingYesterdayShiftByNurseID,
  LoadingYesterdayShift,
  ClearCare,
  GetAttendanceExplainConfig,
  GetUserAttenceAuthority,
} from "@/api/Attendance";
import { GetAttendanceCrossAsync } from "@/api/AttendanceCross";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  components: {
    baseLayout,
    shiftSelector,
    attendanceCard,
  },
  watch: {
    attendanceRadio: {
      handler(newValue, oldValue) {
        if (newValue) {
          if (newValue == "undoneAttendance") {
            this.getUndoneAttendance();
          } else {
            this.getDoneAttendance();
          }
        }
      },
    },
  },
  created() {
    this.init();
  },

  data() {
    return {
      dialogLoading: false,
      attendanceCount: [],
      //岗位派班开关
      postAttendanceSwitch: false,
      attendaceDate: undefined,
      attendanceShift: undefined,
      attendaceShiftID: undefined,
      attendanceShiftOptions: undefined,
      queryInput: undefined,
      //当前病区id
      stationID: undefined,
      //完整护士信息
      alltableDataNurse: undefined,
      //搜索查询表格信息
      tableDataNurse: undefined,
      tableDataInpatient: undefined,
      //责护派班Dialog标题
      nurseAttendanceDialogTitle: undefined,
      //责护派班Dialog开关
      nurseAttendanceDialogVisible: false,
      //岗位派班Dialog标题
      postAttendanceDialogTitle: undefined,
      //岗位派班Dialog开关
      postAttendanceDialogVisible: false,
      //责护派班的派班信息
      nurseAttendanceInfos: undefined,
      //岗位派班的派班信息
      postAttendanceInfos: undefined,
      //选中的岗位派班
      postAttendanceRadio: undefined,
      //派班分类选项
      attendanceRadio: undefined,
      //被派班的护士
      attendanceNurse: undefined,
      //派班病人ID数组
      attendanceInpatientIDs: [],
      //全选
      allChecked: false,
      //组件全选
      elementAllChecked: false,
      //判断是否主责
      primaryNurse: undefined,
      //加载数据列表
      tableLoading: true,
      //可跨病区
      crossStation: undefined,
      //可跨病区列表
      crossStationList: undefined,
      //可跨病区开关
      crossStationswitch: false,
      //仅查看权限
      onlyView: true,
      //派班说明
      explainFlag: true,
      shiftList: [],
      //岗位分组
      jobGroupList: undefined,
      //是否固定当前班次岗床派班
      fixedPostBed: true,
      //岗位分组编码
      jobGroupID: undefined,
      authorityFlag: false,
      //是否显示能力等级
      capabilityLevelFlag: true,
    };
  },
  methods: {
    //
    async getCurrentUserAttenceAuthority() {
      let params = {
        userID: this.user.userID,
      };
      GetUserAttenceAuthority(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.authorityFlag = result.data;
        }
      });
    },
    //通过配置取派班标识说明
    getAttendanceExplainFlag() {
      GetAttendanceExplainConfig().then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data) {
            //打开配置
            this.explainFlag = true;
          } else {
            //关闭配置
            this.explainFlag = false;
          }
        }
      });
    },

    //初始化加载
    async init() {
      this.stationID = this.user.stationID;
      await this.getNowStationShift();
      await this.getCrossStation();
      await this.getAttendanceCountNum();
      await this.getNurseList();
      await this.getCurrentUserAttenceAuthority();
      await this.getCapabilityLevelFlag();
    },
    //获取当前病区班别
    async getNowStationShift() {
      await GetNowStationShiftData().then((result) => {
        if (this._common.isSuccess(result)) {
          let infos = result.data;
          this.shiftList = infos.stationShifts;
          this.attendaceDate = infos.shiftDate;
          this.stationID = infos.nowShift.stationID;
          this.attendanceShift = infos.nowShift.shift;
          this.crossStation = infos.nowShift.stationID;
          this.attendaceShiftID = infos.nowShift.id;
        }
      });
    },

    //获取可跨病区
    async getCrossStation() {
      let params = {
        stationID: this.stationID,
      };
      await GetAttendanceCrossAsync(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.crossStationList = result.data;
          if (this.crossStationList.length > 0) {
            this.crossStationswitch = true;
          }
        }
      });
    },

    //获取已派班未派班统计数
    async getAttendanceCountNum() {
      let params = {
        attendaceDate: this.attendaceDate,
        shiftID: this.attendaceShiftID,
      };

      await GetAttendanceCount(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.attendanceCount = result.data;
        }
      });
    },

    //获取派班护士列表
    async getNurseList() {
      let params = {
        stationID: this.stationID,
        shiftDate: this.attendaceDate,
        shiftID: this.attendaceShiftID,
        shift: this.attendanceShift,
      };
      if (!params.stationID || !params.shiftDate || !params.shiftID || !params.shift) {
        this._showTip("warning", "缺少请求参数");
        this.tableLoading = false;
        return;
      }
      await GetWeb(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.alltableDataNurse = result.data.nurseList;
          this.onlyView = result.data.onlyView;
          this.tableDataNurse = this.alltableDataNurse;
          this.$nextTick(() => {
            this.$refs?.attendanceTable?.doLayout();
          });
          this.tableDataInpatient = result.data.simpleAttendances;
          this.tableLoading = false;
        }
      });
    },

    //切换日期时获取新数据
    changeDate() {
      this.getPageData();
    },

    switchAttendanceRadio(value) {
      this.dialogLoading = true;
      if (value == "undoneAttendance") {
        this.getUndoneAttendance();
      } else {
        this.getDoneAttendance();
      }
    },

    switchStation(value) {
      this.dialogLoading = true;
      this.crossStation = value;
      this.nurseAttendanceInfos = undefined;
      this.attendanceInpatientIDs = [];
      this.allChecked = false;
      this.elementAllChecked = false;
      this.postAttendanceRadio = undefined;
      if (this.postAttendanceSwitch) {
        this.getPostAttendance();
      } else {
        if (this.attendanceRadio == "undoneAttendance") {
          this.getUndoneAttendance();
        } else {
          this.getDoneAttendance();
        }
      }
    },

    //获取刷新后页面数据
    getPageData() {
      this.tableLoading = true;
      this.getAttendanceCountNum();
      this.getNurseList();
    },

    // 取得未派班床位信息
    getUndoneAttendance() {
      let params = {
        shift: this.attendanceShift,
        shiftDate: this.attendaceDate,
        stationShiftID: this.attendaceShiftID,
        check: false,
      };
      if (this.crossStationswitch) {
        params.stationID = this.crossStation;
      } else {
        params.stationID = this.stationID;
      }

      GetAttendanceBed(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.nurseAttendanceInfos = result.data;
          this.tableLoading = false;
          this.nurseAttendanceDialogVisible = true;
          this.dialogLoading = false;
        } else {
          this.tableLoading = false;
          this.dialogLoading = false;
        }
      });
    },

    //取得已派一名责护病人信息
    getDoneAttendance() {
      let params = {
        shift: this.attendanceShift,
        shiftDate: this.attendaceDate,
        employeeID: this.attendanceNurse,
      };
      if (this.crossStationswitch) {
        params.stationID = this.crossStation;
      } else {
        params.stationID = this.stationID;
      }
      GetOneNurseCarePatient(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.nurseAttendanceInfos = result.data;
          this.dialogLoading = false;
        } else {
          this.dialogLoading = false;
        }
      });
    },

    //获取岗位派班数据
    getPostAttendance() {
      let params = {};
      if (this.crossStationswitch) {
        params.stationID = this.crossStation;
      } else {
        params.stationID = this.stationID;
      }
      //params.jobGroupID = 1;
      //获取科室岗位分组
      this.getJobGroupList();
      //获取默认岗位
      this.getNurseJob(params);
    },

    //获取科室岗位分组
    getJobGroupList() {
      let params = {};
      GetDeptmentJobGroupList(params).then((result) => {
        this.jobGroupList = result.data;
      });
    },
    //根据分组获取岗位
    changeJobGroupValue(jobGroupID) {
      let params = {};
      if (this.crossStationswitch) {
        params.stationID = this.crossStation;
      } else {
        params.stationID = this.stationID;
      }
      params.jobGroupID = jobGroupID;
      this.getNurseJob(params);
    },
    //获取岗位
    getNurseJob(params) {
      GetNurseJob(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.postAttendanceInfos = result.data;
          this.tableLoading = false;
          this.dialogLoading = false;
          this.postAttendanceDialogVisible = true;
        } else {
          this.tableLoading = false;
          this.dialogLoading = false;
        }
      });
    },
    //根据护士ID带入前日派班
    importYesterdayAttendancecByNurseID(row) {
      let params = {
        shiftDate: this.attendaceDate,
        stationID: this.stationID,
        stationShiftID: this.attendaceShiftID,
        modifyPersonID: this.user.userID,
        nurseID: row.userID,
      };
      LoadingYesterdayShiftByNurseID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.getPageData();
        }
      });
    },

    //根据病区班别带入前日派班
    importYesterdayAttendancec() {
      let params = {
        shiftDate: this.attendaceDate,
        stationID: this.stationID,
        stationShiftID: this.attendaceShiftID,
        modifyPersonID: this.user.userID,
      };
      LoadingYesterdayShift(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.getPageData();
        }
      });
    },

    //保存岗位派班
    postAttendance() {
      let params = {
        Date: this.attendaceDate,
        DeptmentJobID: this.postAttendanceRadio,
        NurseID: this.attendanceNurse,
        ShiftID: this.attendaceShiftID,
        Shift: this.attendanceShift,
        FixedPostBed: this.fixedPostBed,
      };
      if (this.crossStationswitch) {
        params.StationID = this.crossStation;
      } else {
        params.StationID = this.stationID;
      }
      if (this.primaryNurse == "Principal") {
        params.CarePriority = "1";
      } else {
        params.CarePriority = "2";
      }
      return AttendanceFast(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data) {
            this._showTip("success", "派班成功");
          } else {
            this._showTip("warning", "派班失败");
          }
          this.getPageData();
          this.postAttendanceDialogVisible = false;
        }
      });
    },

    //保存责护派班
    saveNurseAttendance() {
      if (this.attendanceInpatientIDs.length > 0) {
        let params = [];
        this.attendanceInpatientIDs.forEach((item) => {
          let param = {
            AttendanceDate: this.attendaceDate,
            AttendanceID: "",
            DeleteFlag: "",
            InpatientID: item,
            ModifyPersonID: this.user.userID,
            NurseEmployeeID: this.attendanceNurse,
            Shift: this.attendanceShift,
            StationShiftID: this.attendaceShiftID,
          };
          if (this.primaryNurse == "Principal") {
            param.CarePriority = "1";
          } else {
            param.CarePriority = "2";
          }
          if (this.crossStationswitch) {
            param.StationID = this.crossStation;
          } else {
            param.StationID = this.stationID;
          }
          params.push(param);
        });
        return MultiSave(params).then((result) => {
          if (this._common.isSuccess(result)) {
            if (result.data) {
              this._showTip("success", "派班成功");
            } else {
              this._showTip("warning", "派班失败");
            }
            this.getPageData();
            this.nurseAttendanceDialogVisible = false;
          }
        });
      } else {
        this._showTip("warning", "请选择患者后进行派班");
        return;
      }
    },

    //根据护士ID删除派班
    deleteAttendancecInfos(value) {
      this._deleteConfirm("确认删除当前派班吗", (flag) => {
        if (flag) {
          let ids = [];
          this.tableDataInpatient.forEach((item) => {
            if (item.careNurse1ID == value.userID || item.careNurse2ID == value.userID) {
              ids.push(item.attendanceID);
            }
          });
          let params = {
            attendanceIDS: ids,
            operatorID: this.user.userID,
          };
          if (params.attendanceIDS.length == 0 || !params.operatorID) {
            this._showTip("warning", "无需删除");
            return;
          }
          ClearCare(params).then((result) => {
            if (this._common.isSuccess(result)) {
              if (result.data) {
                this._showTip("success", "删除成功");
              } else {
                this._showTip("warning", "删除失败");
              }
            }
            this.getPageData();
          });
        }
      });
    },

    //获取某个护士被派班的患者数
    getAttendaceInpatientCount(id) {
      let i = 0;
      this.tableDataInpatient.forEach((item) => {
        if (item.careNurse1ID == id) {
          i++;
        }
      });
      return i;
    },

    //获取某个护士工作量的半分比
    getWorkloadPercentage(row) {
      let result = 0;
      if (row.allSchedule == 0) {
        result = 0;
      } else {
        result = (row.successSchedule / row.allSchedule) * 100;
      }
      return result;
    },

    //获取某个护士被派班患者卡片的信息
    getAttendanceCardInfos(row, value) {
      let result = [];
      this.tableDataInpatient.forEach((item) => {
        if (value == "Principal") {
          if (item.careNurse1ID == row.userID && item.carePriority == "1") {
            result.push(item);
          }
        } else {
          if (item.careNurse2ID == row.userID && item.carePriority == "2") {
            result.push(item);
          }
        }
      });
      return result;
    },

    //获取主责派班
    async getPrincipalAttendance(value) {
      let authorityflag = false;
      let params = {
        userID: value.userID,
      };
      await GetUserAttenceAuthority(params).then((result) => {
        if (this._common.isSuccess(result)) {
          authorityflag = result.data;
        }
      });
      if (!authorityflag) {
        this._showTip("warning", "该用户无派主班权限！");
        return;
      }
      this.jobGroupID = undefined;
      this.primaryNurse = "Principal";
      this.attendanceNurse = value.userID;
      this.crossStation = this.stationID;
      if (this.postAttendanceSwitch) {
        this.tableLoading = true;
        this.postAttendanceDialogTitle = "主责-快速派班-" + value.name;
        this.postAttendanceRadio = undefined;
        this.getPostAttendance();
      } else {
        this.tableLoading = true;
        this.nurseAttendanceDialogTitle = "主责-添加病人";
        this.attendanceRadio = "undoneAttendance";
        this.nurseAttendanceInfos = undefined;
        this.attendanceInpatientIDs = [];
        this.allChecked = false;
        this.elementAllChecked = false;
        this.getUndoneAttendance();
      }
    },

    //获取次责派班
    getSecondaryAttendance(value) {
      this.jobGroupID = undefined;
      this.primaryNurse = "secondary";
      this.attendanceNurse = value.userID;
      this.crossStation = this.stationID;
      if (this.postAttendanceSwitch) {
        this.tableLoading = true;
        this.postAttendanceDialogTitle = "次责-快速派班-" + value.name;
        this.postAttendanceRadio = undefined;
        this.getPostAttendance();
      } else {
        this.tableLoading = true;
        this.nurseAttendanceDialogTitle = "次责-添加病人";
        this.attendanceRadio = "undoneAttendance";
        this.nurseAttendanceInfos = undefined;
        this.attendanceInpatientIDs = [];
        this.allChecked = false;
        this.elementAllChecked = false;
        this.getUndoneAttendance();
      }
    },

    //切换班别
    selectShiftItem(shiftInfo) {
      this.attendanceShift = shiftInfo.shift;
      this.attendaceShiftID = shiftInfo.id;
      this.getPageData();
    },

    //设置组件全选
    setAllChecked(value) {
      this.nurseAttendanceInfos.forEach((item) => {
        this.$set(item, "checked", value);
        if (value) {
          if (this.attendanceInpatientIDs.indexOf(item.inpatientID) == -1) {
            this.attendanceInpatientIDs.push(item.inpatientID);
          }
        } else {
          if (this.attendanceInpatientIDs.indexOf(item.inpatientID) > -1) {
            this.attendanceInpatientIDs.splice(this.attendanceInpatientIDs.indexOf(item.inpatientID), 1);
          }
        }
      });
      if (!value) {
        this.attendanceInpatientIDs = [];
      }
    },

    //组件抛出数据后自动设置全选
    getItemData(item, boole) {
      item.checked = boole;
      if (boole) {
        if (this.attendanceInpatientIDs.indexOf(item.inpatientID) == -1) {
          this.attendanceInpatientIDs.push(item.inpatientID);
        }
      } else {
        if (this.attendanceInpatientIDs.indexOf(item.inpatientID) > -1) {
          this.attendanceInpatientIDs.splice(this.attendanceInpatientIDs.indexOf(item.inpatientID), 1);
        }
      }
      if (this.attendanceInpatientIDs.length == this.nurseAttendanceInfos.length) {
        this.allChecked = true;
      } else {
        this.allChecked = false;
      }
    },

    queryUser() {
      //监控查询内容
      let queryContent = this.queryInput;
      let queryResult = this.alltableDataNurse.filter(function (value) {
        let searchField = { userID: value.userID, name: value.name };
        return Object.keys(searchField).some(function (key) {
          return String(value[key]).toLowerCase().indexOf(queryContent) > -1;
        });
      });
      this.tableDataNurse = queryResult;
    },
    /**
     * 获取能力等级配置
     */
    async getCapabilityLevelFlag() {
      let param = {
        SettingTypeCode: "CapabilityLevelFlag",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.capabilityLevelFlag = response.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.attendance {
  .base-header {
    .top-select {
      width: 120px;
    }
    .top-label {
      margin-left: 20px;
    }
    .attendance-header {
      min-width: 1100px;
    }
  }
  .import-yesterday {
    margin-left: 20px;
  }
  .attendance-num {
    line-height: 20px;
    display: inline-block;
    vertical-align: middle;
    .unfinished {
      color: #ff0000;
    }
  }
  .attendance-explain {
    line-height: 20px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 25px;
    margin-right: 20px;
  }
  .standby-status {
    color: #ffcccc;
  }
  .vacation-status {
    color: #aaaaaa;
  }
  .attendace-date {
    width: 120px;
    margin-right: 20px;
  }
  .query-input {
    width: 180px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .workload {
    width: 60px;
    padding-left: 5px;
  }
  .post-radio-group {
    margin-left: 30px;
  }
  .post-radio-item {
    margin: 10px;
  }
  .post-cross-station {
    margin: 5px 40px;
  }
  .post-cross-station-label {
    margin-left: 10px;
  }
  .nurse-attendance {
    margin-left: 30px;
    .nurse-attendance-head {
      padding: 5px;
      .nurse-cross-station {
        padding-left: 10px;
      }
    }
  }
  .fixed-post-bed {
    margin-left: 40px;
  }
}
</style>
