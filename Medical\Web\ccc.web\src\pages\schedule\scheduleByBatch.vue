<template>
  <base-layout class="schedule-batch" v-loading="loading" element-loading-text="加载中……">
    <schedule-top
      slot="header"
      :stationID="user.stationID"
      :shiftTime="shiftTime"
      :shift="shiftID"
      isBatch
      @switch-all="switchAll"
      @switch-schedule="switchSchedule"
      @switch-hide="switchHide"
      @switch-perform="switchPerform"
      @change-shift-time="changeShiftTime"
      @change-shift="changeShift"
      @change-shiftTimes="changeShiftTimes"
      @change-bedNumber="changeBedNumber"
      ref="scheduleTest"
    ></schedule-top>
    <div class="schedule-data-wrap" ref="scheduleDataWrap" @scroll="scrollDiv">
      <el-table
        :data="selectRow ? [selectRow] : []"
        border
        stripe
        v-show="!hideTable"
        :class="['schedule-data', 'select-schedule-data', { 'no-select': !showSelectTable }, { pad: !isPC }]"
      >
        <el-table-column class-name="column-first" label="措施内容" min-width="120">
          <template slot-scope="schedule">
            {{ schedule.row[3] }}
          </template>
        </el-table-column>
        <el-table-column class-name="column-first" label="快速执行" align="center" width="100">
          <template slot-scope="scope">
            <el-tooltip content="快速执行" v-if="scope.row[2].trim() !== '1'">
              <i class="iconfont icon-execute" @click="goScheduleExecution(scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <template v-for="(column, index) in scheduleHeader">
          <el-table-column
            :label="getPatientName(column)"
            label-class-name="batch-header-cell"
            min-width="90"
            align="center"
            :key="index"
            :index="index - 4"
            v-if="index > 3"
          >
            <template slot-scope="schedule">
              <div
                :class="[
                  'column',
                  {
                    'is-select': selectRowIndex == schedule.$index && selectColumnIndex == schedule.column.index,
                  },
                ]"
                @click="showSchedules(schedule.row, schedule.column, schedule.$index, column)"
              >
                {{ schedule.row[index] }}
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <el-table :data="scheduleDatas" border stripe v-show="!hideTable" class="schedule-data" ref="scheduleData">
        <el-table-column class-name="column-first" label="措施内容" min-width="120" :fixed="true">
          <template slot-scope="schedule">
            {{ schedule.row[3] }}
          </template>
        </el-table-column>
        <el-table-column class-name="column-first" label="快速执行" align="center" width="100">
          <template slot-scope="scope">
            <el-tooltip content="快速执行" v-if="scope.row[2].trim() !== '1'">
              <i class="iconfont icon-execute" @click="goScheduleExecution(scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <template v-for="(column, index) in scheduleHeader">
          <el-table-column
            :label="getPatientName(column)"
            label-class-name="batch-header-cell"
            min-width="90"
            align="center"
            :key="index"
            :index="index - 4"
            v-if="index > 3"
          >
            <template slot-scope="schedule">
              <div
                :class="[
                  'column',
                  {
                    'is-select': selectRowIndex == schedule.$index && selectColumnIndex == schedule.column.index,
                  },
                ]"
                @click="showSchedules(schedule.row, schedule.column, schedule.$index, column)"
              >
                {{ schedule.row[index] }}
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <schedule-list
        v-if="selectRowIndex >= 0 && selectColumnIndex >= 0"
        :scheduleParams="scheduleParams"
        @refresh="getPatientSchedule(false)"
      />
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import scheduleTop from "./components/scheduleTop";
import scheduleList from "./components/scheduleList";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetPatientDataByBedNumber } from "@/api/Inpatient";
import { GetTimeSchedule } from "@/api/PatientSchedule";
import { mapGetters } from "vuex";
export default {
  components: { baseLayout, scheduleTop, scheduleList },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  data() {
    return {
      showAll: false,
      hideTable: false,
      shiftTime: "",
      shiftID: undefined,
      shiftInfo: undefined,
      startTime: undefined,
      endTime: undefined,
      bedNumber: "",
      loading: false,
      scheduleHeader: [],
      scheduleDatas: [],
      selectRowIndex: undefined,
      selectColumnIndex: undefined,
      selectRow: undefined,
      selectColumn: undefined,
      label: undefined,
      scheduleParams: {},
      showSelectTable: false,
      isPC: true,
      selectTable: undefined,
      scheduleTable: undefined,
      notPerformFlag: false,
    };
  },
  async activated() {
    //班别、时间不进行刷新
    if (this.$route.meta.refreshFlag) {
      await this.GetNowStationShiftData();
      this.$refs?.scheduleTest?.changeShift(this.shiftID, true);
      return;
    }
    this.$refs?.scheduleTest?.changeShift(this.shiftID, false);
    this.init();
  },
  methods: {
    scrollDiv() {
      if (this.$refs.scheduleDataWrap.scrollTop - 2 >= this.selectRowIndex * 39) {
        this.showSelectTable = true;
        this.scheduleTable?.addEventListener("scroll", () => {
          this.selectTable.scrollLeft = this.scheduleTable.scrollLeft;
        });
      } else {
        this.showSelectTable = false;
      }
    },
    /**
     * description: 初始化
     * return {*}
     */
    async init() {
      // 联动两个表格的横向滚动条
      let tables = document.getElementsByClassName("el-table__body-wrapper");
      this.selectTable = tables[0];
      this.scheduleTable = tables[1];
      this.scheduleTable?.addEventListener("scroll", () => {
        this.selectTable.scrollLeft = this.scheduleTable.scrollLeft;
      });
      this.isPC = this._common.isPC();
      await this.getPatientSchedule(true);
    },
    switchAll(flag) {
      this.showAll = flag;
      this.getPatientSchedule(true);
    },
    switchSchedule(flag) {
      this.showNoSchedule = flag;
      this.getPatientSchedule(true);
    },
    switchHide(flag) {
      this.hideTable = flag;
    },
    switchPerform(flag) {
      this.notPerformFlag = flag;
      this.getPatientSchedule(true);
    },
    changeShiftTime(shiftTime) {
      this.shiftTime = shiftTime;
      this.getPatientSchedule(true);
    },
    changeShift(shift) {
      this.shiftID = shift.id;
      this.shiftInfo = shift;
      this.getPatientSchedule(true);
    },
    changeShiftTimes(shiftTimes) {
      var timeSpan = shiftTimes.split("-");
      if (timeSpan && timeSpan.length == 2) {
        this.startTime = timeSpan[0];
        this.endTime = timeSpan[1];
      }
      this.getPatientSchedule(true);
    },
    changeBedNumber(bedNumber) {
      this.bedNumber = bedNumber;
      this.getPatientSchedule(true);
    },
    /**
     * description: 获取当前班次
     * return {*}
     */
    async GetNowStationShiftData() {
      await GetNowStationShiftData().then((result) => {
        if (this._common.isSuccess(result)) {
          this.shiftID = result.data.nowShift.id;
          this.shiftInfo = result.data.nowShift;
          this.shiftTime = result.data.shiftDate;
        }
      });
    },
    /**
     * description: 组装表格头部患者信息
     * return {*}
     * param {*} value
     */
    getPatientName(value) {
      var temp = value.split("||");
      return temp[0] + "-" + temp[1];
    },

    /**
     * description: 获取病人排程
     * return {*}
     * param {*} initFlag
     */
    async getPatientSchedule(initFlag) {
      if (initFlag) {
        this.selectRowIndex = undefined;
        this.selectColumnIndex = undefined;
        this.selectRow = undefined;
        this.selectColumn = undefined;
        this.scheduleParams = {};
        this.showSelectTable = false;
      }
      if (!this.shiftTime || !this.shiftID || !this.startTime || !this.endTime) {
        return;
      }
      this.showSelectTable = false;
      this.loading = true;
      this.scheduleDatas = [];
      let params = {
        scheduleDate: this.shiftTime,
        stationID: this.user.stationID,
        shiftID: this.shiftID,
        startDate: this.startTime,
        endDate: this.endTime,
        index: Math.random(),
      };
      if (this.bedNumber) {
        params.bedNum = this.bedNumber;
      }
      if (this.showAll) {
        params.allStation = true;
      } else {
        params.allStation = false;
      }
      params.notPerformFlag = this.notPerformFlag;
      await GetTimeSchedule(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.length > 0) {
            this.scheduleHeader = result.data[0];
            result.data.splice(0, 1);
            this.scheduleDatas = result.data;
          }
        }
      });
      // 如果已选择了。刷新已选人的排程
      if (this.selectRowIndex >= 0 && this.selectColumnIndex >= 0) {
        this.selectColumnIndex = undefined;
        this.selectRow = this.scheduleDatas[this.selectRowIndex];
        //解决触发措施弹窗保存完后报错问题  --GPC
        if (!this.selectRow) {
          this.selectRow = [];
        }
        await this.showSchedules(this.selectRow, this.selectColumn, this.selectRowIndex, this.label);
      }
    },
    /**
     * description: 展示排程明细
     * return {*}
     * param {*} row
     * param {*} column
     * param {*} rowIndex
     * param {*} label
     */
    async showSchedules(row, column, rowIndex, label) {
      if (this.selectRowIndex == rowIndex && this.selectColumnIndex == column.index) {
        return;
      }
      this.selectRow = row;
      if (rowIndex > 0) {
        this.$nextTick(() => {
          this.scrollDiv();
        });
      }
      this.selectColumn = column;
      this.selectRowIndex = rowIndex;
      this.selectColumnIndex = column.index;
      this.label = label;
      let tempPatient = label.split("||");
      // 获取病人信息并存入缓存，同步病人头组件
      let params = {
        bedNumber: tempPatient[0],
      };
      await GetPatientDataByBedNumber(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.$store.commit("session/setPatientInfo", result.data);
          let currentPatient = {
            bedNumber: result.data.bedNumber,
            inpatientID: result.data.inpatientID,
            stationID: result.data.stationID,
            caseNumber: result.data.caseNumber,
            chartNo: result.data.chartNo,
            admissionDate: result.data.admissionDate,
            localCaseNumber: result.data.localCaseNumber,
            departmentCode: result.data.departmentCode,
          };
          this.$store.commit("session/setCurrentPatient", currentPatient);
        }
      });
      this.scheduleParams = {
        inpatientID: this.patientInfo.inpatientID,
        caseNumber: this.patientInfo.caseNumber,
        bedNumber: tempPatient[0],
        stationID: this.user.stationID,
        shiftID: this.shiftID,
        shiftName: this.shiftInfo.shiftName,
        interventionID: row[0],
        startTime: this.startTime,
        endTime: this.endTime,
        shiftDate: this._datetimeUtil.formatDate(this.shiftTime, "yyyy-MM-dd"),
      };
      this.scheduleParams.patientInfo =
        tempPatient[0] +
        "-" +
        this.patientInfo.patientName +
        "【" +
        this.patientInfo.gender +
        "-" +
        this.patientInfo.ageDetail +
        "】";
      //跨天 并且开始时间大于班次结束时间，执行日期加1
      if (this.shiftInfo.crossDayFlag == "*" && this.scheduleParams.startTime <= this.shiftInfo.shiftEndTime) {
        this.scheduleParams.scheduleDate = this._datetimeUtil.addDate(this.shiftTime, 1, "yyyy-MM-dd");
      } else {
        this.scheduleParams.scheduleDate = this._datetimeUtil.formatDate(this.shiftTime, "yyyy-MM-dd");
      }
    },

    /**
     * description: 相同排程批量执行
     * return {*}
     * param {*} value
     */
    goScheduleExecution(value) {
      let flag = false;
      value.forEach((item, index) => {
        if (index > 3 && item && item != 0) {
          flag = true;
        }
      });
      if (!flag) {
        this._showTip("warning", "无排程需要执行");
        return;
      }
      let params = {
        shiftDate: this.shiftTime,
        shiftInfo: this.shiftInfo,
        interventionID: value[0],
        startTime: this.startTime,
        endTime: this.endTime,
        showAll: this.showAll,
      };
      this.$router.push({
        name: "schedulesExecution",
        params: params,
      });
    },
  },
};
</script>

<style lang="scss">
.schedule-batch {
  height: 100%;
  width: 100%;
  padding: 5px;
  box-sizing: border-box;
  position: relative;
  .schedule-data-wrap {
    overflow: auto;
    height: 100%;
    width: 100%;
    padding-right: calc(100% - 100vw);
    .schedule-data {
      td {
        height: 38px;
      }
      // 冻结表
      &.select-schedule-data {
        position: absolute;
        left: 0;
        top: 0;
        width: calc(100% - 17px);
        z-index: 100;
        border-bottom: none;
        &.pad {
          width: 100%;
        }
        &.no-select {
          display: none;
        }
        .el-table__body-wrapper {
          box-shadow: 0 3px 3px rgba(0, 0, 0, 0.12);
          margin-bottom: 2px;
          &::-webkit-scrollbar {
            display: none;
          }
        }
      }
      .header-first {
        text-align: center;
      }
      .header-cell {
        background-image: none !important;
        cursor: default !important;
        &.cell {
          width: 60px;
          text-align: left;
        }
      }
      td {
        padding: 0;
        .cell > .column {
          cursor: pointer;
          height: 36px;
          line-height: 36px;
          &.is-select {
            background-image: url("../../../static/images/text-select.png");
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 40px;
          }
        }
      }
    }
    .schedule-list {
      margin-top: 10px;
      height: calc(100% - 85px);
      width: 100%;
    }
  }
}
</style>
