/*
 * FilePath     : \src\api\PatientScore.js
 * Author       : 苏军志
 * Date         : 2020-08-28 15:54
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-23 17:46
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientScore";

export const urls = {
  SavePatientScoreMainAndDetail: baseUrl + "/SavePatientScoreMainAndDetail",
  GetPatientScoreMainListByInpatient:
    baseUrl + "/GetPatientScoreMainListByInpatient",
  DeletePatientScoreMainAndDetailByID:
    baseUrl + "/DeletePatientScoreMainAndDetailByID",
  GetLastPatientRisk: baseUrl + "/GetLastPatientRisk",
  GetPatientScheduleScore: baseUrl + "/GetPatientScheduleScore",
  SavePatientScore: baseUrl + "/SavePatientScore",
  GetFormatDataByMainID: baseUrl + "/GetFormatDataByMainID",
  GetDateTimeByAssessListID: baseUrl + "/GetDateTimeByAssessListID",
  GetInterventionsByScoreMainID: baseUrl + "/GetInterventionsByScoreMainID",
  SaveScoreInterventions: baseUrl + "/SaveScoreInterventions",
};
// 获取病人医嘱
export const SavePatientScoreMainAndDetail = (params) => {
  return http.post(urls.SavePatientScoreMainAndDetail, params);
};
export const GetPatientScoreMainListByInpatient = (params) => {
  return http.get(urls.GetPatientScoreMainListByInpatient, params);
};
export const DeletePatientScoreMainAndDetailByID = (params) => {
  return http.post(
    urls.DeletePatientScoreMainAndDetailByID,
    qs.stringify(params)
  );
};

export const GetLastPatientRisk = (params) => {
  return http.get(urls.GetLastPatientRisk, params);
};
//获取评量表
export const GetPatientScheduleScore = (params) => {
  return http.get(urls.GetPatientScheduleScore, params);
};
//保存评量表
export const SavePatientScore = (params) => {
  return http.post(urls.SavePatientScore, params);
};
// 获取模板
export const GetFormatDataByMainID = (params) => {
  return http.get(urls.GetFormatDataByMainID, params);
};

// 根据AssessListID计算病人评量表评估时间
export const GetDateTimeByAssessListID = (params) => {
  return http.get(urls.GetDateTimeByAssessListID, params);
};
//获取风险措施
export const GetInterventionsByScoreMainID = (params) => {
  return http.get(urls.GetInterventionsByScoreMainID, params);
};
//保存风险措施
export const SaveScoreInterventions = (params) => {
  return http.post(urls.SaveScoreInterventions, params);
};
