<!--
 * FilePath     : \src\autoPages\recordSupplement\components\giveMedicineRecordTest.vue
 * Author       : AI Assistant
 * Date         : 2025-07-21
 * LastEditors  : AI Assistant
 * LastEditTime : 2025-07-21
 * Description  : 给药记录补录测试页面
 * CodeIterationRecord: 
 -->
<template>
  <div class="give-medicine-record-test">
    <h2>给药记录补录组件测试</h2>
    <div class="test-controls">
      <el-button type="primary" @click="loadTestData">加载测试数据</el-button>
      <el-button @click="clearTestData">清空数据</el-button>
    </div>
    <div class="component-wrapper">
      <give-medicine-record :supplementPatient="testPatient" />
    </div>
  </div>
</template>
<script>
import giveMedicineRecord from "./giveMedicineRecord";
export default {
  components: {
    giveMedicineRecord,
  },
  data() {
    return {
      testPatient: null,
    };
  },
  methods: {
    /**
     * description: 加载测试数据
     * return {*}
     */
    loadTestData() {
      this.testPatient = {
        inpatientID: "TEST001",
        patientID: "P001",
        patientName: "测试患者",
        bedNumber: "001",
        gender: "男",
        age: "45",
        admissionDate: "2025-04-25",
        stationID: "ST001",
        departmentListID: "DEPT001",
      };
      this._showTip("success", "测试数据已加载");
    },
    /**
     * description: 清空测试数据
     * return {*}
     */
    clearTestData() {
      this.testPatient = null;
      this._showTip("info", "测试数据已清空");
    },
  },
};
</script>
<style lang="scss">
.give-medicine-record-test {
  padding: 20px;
  height: 100vh;
  box-sizing: border-box;
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
  .test-controls {
    margin-bottom: 20px;
    .el-button {
      margin-right: 10px;
    }
  }
  .component-wrapper {
    height: calc(100% - 120px);
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
  }
}
</style>
