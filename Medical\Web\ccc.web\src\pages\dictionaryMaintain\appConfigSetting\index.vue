<!--
 * FilePath     : \src\pages\dictionaryMaintain\appConfigSetting\index.vue
 * Author       : 吴馥辰
 * Date         : 2022-11-07 08:57
 * LastEditors  : 吴馥辰
 * LastEditTime : 2022-12-13 20:59
 * Description  : 
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="app-config-setting">
    <div slot="header">
      <el-input
        class="setting-input"
        v-model="inputContext"
        placeholder="查询配置值、配置码、说明"
        @keyup.enter.native="search"
        cleartable
      >
        <i slot="append" class="iconfont icon-search" @click="search"></i>
      </el-input>
      <div class="right-btn">
        <el-button type="success" @click="addButtonEvent" icon="iconfont icon-add">新增</el-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="btachSave">保存</el-button>
      </div>
    </div>
    <div slot-scope="innerLayout" :style="{ height: innerLayout.height + 2 + 'px' }">
      <el-table
        v-loading="loadingFlag"
        border
        :data="fixAppData"
        stripe
        ref="appConfigSettingDataTable"
        highlight-current-row
        :height="innerLayout.height"
      >
        <el-table-column type="selection" align="center" width="35"></el-table-column>
        <el-table-column label="配置类型" width="90">
          <template slot-scope="scope">
            <el-input v-model="scope.row.settingType" @change="selectRow(scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="配置码">
          <template slot-scope="scope">
            <el-input v-model="scope.row.settingCode" @change="selectRow(scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="配置值" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.settingValue" @change="selectRow(scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="说明" width="180">
          <template slot-scope="scope">
            <el-input v-model="scope.row.description" @change="selectRow(scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="所属系统" width="120">
          <template slot-scope="scope">
            <el-input v-model="scope.row.systemType" @change="selectRow(scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="修改人" width="90">
          <template slot-scope="scope">
            <el-input v-model="scope.row.personInCharge" @change="selectRow(scope.row)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="80">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.switchCode" @change="selectRow(scope.row)"></el-switch>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>

<script>
import {
  //引入查询所有信息
  GetAppConfigSettings,
  //引入新增
  SaveAppConfigSettingInfo,
} from "@/api/AppConfigSetting.js";
import BaseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
export default {
  components: {
    BaseLayout,
  },
  data() {
    return {
      //加载框
      loadingFlag: false,
      //表格数据
      fixAppData: [],
      //搜索框数据
      inputContext: "",
      // 新增时，默认初始化的数据模板
      newRecordTemplate: {},
    };
  },
  computed: {
    ...mapGetters({
      hospitalID: "getHospitalInfo",
    }),
  },
  async mounted() {
    await this.getAppConfigSettingData();
  },
  methods: {
    /**
     * description:获取数据
     * return {*}
     */
    async getAppConfigSettingData() {
      this.loadingFlag = true;
      await GetAppConfigSettings().then((res) => {
        this.loadingFlag = false;
        if (this._common.isSuccess(res)) {
          this.fixAppData = res.data;
          this.cloneAppData = this._common.clone(this.fixAppData);
        }
      });
      //修复el-table表头错位
      this.$nextTick(() => {
        let appConfigSettingDataTable = this.$refs.appConfigSettingDataTable;
        if (!appConfigSettingDataTable) {
          this._showTip("warning", "不能为空");
          return;
        }
        this.$refs.appConfigSettingDataTable.doLayout();
      });
    },
    /**
     * description: 模糊查询
     * return {*}
     */
    search() {
      let fuzzy = this.inputContext;
      if (fuzzy) {
        this.fixAppData = this.cloneAppData.filter((item) => {
          return (
            item.settingCode.includes(fuzzy) || item.settingValue.includes(fuzzy) || item.description.includes(fuzzy)
          );
        });
      } else {
        this.fixAppData = this.cloneAppData;
      }
    },
    /**
     * description: 行数据异动时自动选择当前行
     * param {*} row 当前异动行
     * return {*}
     */
    selectRow(row) {
      this.$nextTick(() => {
        let table = this.$refs.appConfigSettingDataTable;
        if (table) {
          table.toggleRowSelection(row, true);
        }
      });
    },
    /**
     * description: 保存异动数据 和修改数据
     * param {*}
     * return {*}
     */
    async btachSave() {
      let table = this.$refs.appConfigSettingDataTable;
      let saveViews = table && table.selection;
      let successFlag = true;
      let failIndex = 0;
      /**
       * description: 第一次遍历判断数据是否为空，不走批量
       * param {*}
       * return {*}
       */
      for (let index = 0; index < saveViews.length; index++) {
        let appConfigSettingInfo = saveViews[index];
        // 每一行数据单独做非空判断
        if (
          !appConfigSettingInfo.settingCode ||
          !appConfigSettingInfo.settingType ||
          !appConfigSettingInfo.description ||
          !appConfigSettingInfo.systemType ||
          !appConfigSettingInfo.settingValue ||
          !appConfigSettingInfo.personInCharge
        ) {
          this._showTip("warning", "请填写完整记录信息");
          successFlag = false;
          failIndex = saveViews.length - index;
          break;
        }
      }
      if (!successFlag) {
        this._showTip("warning", `请将第${failIndex}条记录填写完整`);
        return;
      }
      /**
       * description: 再次遍历走批量保存逻辑
       * param {*}
       * return {*}
       */
      for (let index = 0; index < saveViews.length; index++) {
        let appConfigSettingInfo = saveViews[index];
        appConfigSettingInfo.deleteFlag = appConfigSettingInfo.switchCode ? "" : "*";
        await SaveAppConfigSettingInfo(appConfigSettingInfo).then((res) => {
          if (!this._common.isSuccess(res)) {
            this._showTip("success", "保存成功！");
          }
        });
      }
      await this.getAppConfigSettingData();
    },
    /**
     * description: 新增数据
     * param {*}
     * return {*}
     */
    addButtonEvent() {
      let newRow = this._common.clone(this.newRecordTemplate);
      newRow.hospitalID = this.hospitalID.hospitalID;
      this.fixAppData.unshift(newRow);
      this.selectRow(newRow);
    },
  },
};
</script>
<style lang="scss">
.app-config-setting {
  .setting-input {
    width: 255px;
    i {
      color: #8cc63e;
    }
  }
  .right-btn {
    float: right;
  }
}
</style>