/*
 * FilePath     : \ccc.web\src\router\pages.js
 * Author       : 杨欣欣
 * Date         : 2024-08-03 09:43
 * LastEditors  : LX
 * LastEditTime : 2025-05-23 17:54
 * Description  : 路由配置
 * CodeIterationRecord:
 */
/* 组件demo */
let componentDemo = (resolve) =>
  require(["@/components/componentDemo.vue"], resolve);
/* 登录页面 */
// let login = resolve => require(["@/pages/login.vue"], resolve);
/* 电子签名授权页面 */
let caAuthorize = (resolve) => require(["@/pages/caAuthorize.vue"], resolve);
// 供第三方对接页面
let externalTransfer = (resolve) =>
  require(["@/pages/externalTransfer.vue"], resolve);
//系统帮助画面
let help = (resolve) => require(["@/pages/help/index.vue"], resolve);
// 主页面
// let main = resolve => require(["@/pages/mainLayout.vue"], resolve);
// 跳转到护理管理页面
let transferManagement = (resolve) =>
  require(["@/pages/transferManagement/index.vue"], resolve);
//跳转到年度计划页面
let annuaPlanMaintain = (resolve) =>
  require(["@/pages/transferManagement/annuaPlanMaintain.vue"], resolve);
/* 病人头部父路由 */
// let patientLayout = resolve => require(["@/pages/patientLayout.vue"], resolve);
/* 护理评估 */
// let assess = resolve =>
//   require(["@/pages/nursingAssessment/index.vue"], resolve);
// /* 护理评估明细 */
// let assessDetail = resolve =>
//   require(["@/pages/nursingAssessment/detail.vue"], resolve);
// /* 伤口评估 */
// let wound = resolve => require(["@/pages/wound/index.vue"], resolve);
/* 导管评估 */
let tube = (resolve) => require(["@/pages/tube/index.vue"], resolve);
//护理会诊
let patientConsult = (resolve) =>
  require(["@/pages/patientConsult/index.vue"], resolve);
//护士多次交班
let multipleHandoffByNurse = (resolve) =>
  require(["@/pages/handover/multipleHandoffByNurse.vue"], resolve);
//护士多次交班
let multipleHandonByNurse = (resolve) =>
  require(["@/pages/handover/multipleHandonByNurse.vue"], resolve);
//SBAR交班
let handoverSBAR = (resolve) =>
  require(["@/pages/handover/handoverSBAR.vue"], resolve);
//护士交班
let nurseHandover = (resolve) =>
  require(["@/pages/handover/nurseHandover.vue"], resolve);
//每日交班
let dailyLog = (resolve) => require(["@/pages/handover/dailyLog"], resolve);
//交班单根据病人查询
let handoverQueryByInpatient = (resolve) =>
  require(["@/pages/handover/handoverQueryByInpatient.vue"], resolve);
//交班单根据病区查询
let handoverQueryByStation = (resolve) =>
  require(["@/pages/handover/handoverQueryByStation.vue"], resolve);
//handover
let handover = (resolve) => require(["@/pages/handover/index.vue"], resolve);
//会诊回复
let replyConsult = (resolve) =>
  require(["@/pages/patientConsult/replyConsult.vue"], resolve);
//会诊指派
let consultAssign = (resolve) =>
  require(["@/pages/patientConsult/consultAssign.vue"], resolve);
//会诊人员名单维护
let consultGoalToEmployee = (resolve) =>
  require(["@/pages/patientConsult/consultGoalToEmployee.vue"], resolve);
// 排程执行
let schedule = (resolve) => require(["@/pages/schedule/index.vue"], resolve);
// 排程批量执行
let scheduleByBatch = (resolve) =>
  require(["@/pages/schedule/scheduleByBatch.vue"], resolve);
let patientHomePage = (resolve) =>
  require(["@/pages/patientHomePage/index.vue"], resolve);
//io柱状图
let ioStatisticalChart = (resolve) =>
  require(["@/pages/IO/ioStatisticalChart.vue"], resolve);
//测试
let patientTestDemo = (resolve) =>
  require(["@/pages/PatientTestDemo.vue"], resolve);
let glucose = (resolve) => require(["@/pages/glucose/index.vue"], resolve);
//血糖统计
let glucoseChart = (resolve) =>
  require(["@/pages/glucose/components/glucoseChart.vue"], resolve);
//io
let io = (resolve) => require(["@/pages/IO/index.vue"], resolve);
//io记录单
let ioRecordMaintenance = (resolve) =>
  require(["@/pages/IO/ioRecordMaintenance.vue"], resolve);

//引流液统计
let drainageStatistics = (resolve) =>
  require(["@/pages/IO/drainageStatistics.vue"], resolve);
// io记录
let ioRecord = (resolve) => require(["@/pages/IO/ioRecord.vue"], resolve);
// 入出量平衡
let ioBalanceStatistics = (resolve) =>
  require(["@/pages/IO/ioBalanceStatistics.vue"], resolve);
//引流液统计图
let ioDrainageChart = (resolve) =>
  require(["@/pages/IO/ioDrainageChart/index.vue"], resolve);
// 给药带入记录单
let medicineToRecord = (resolve) =>
  require(["@/pages/medicineToRecord/index.vue"], resolve);
//护理评价
let nursingEvaluation = (resolve) =>
  require(["@/pages/nursingEvaluation/patientEvaluation.vue"], resolve);
//版本更新记录
let systemVersionMaintain = (resolve) =>
  require(["@/pages/dictionaryMaintain/systemVersion/index.vue"], resolve);
//常见问题
let questionAndAnaswer = (resolve) =>
  require(["@/pages/dictionaryMaintain/questionAndAnaswer/index.vue"], resolve);
//拼音字典数据表维护
let pinyinListMaintain = (resolve) =>
  require(["@/pages/dictionaryMaintain/pinyinListMaintain/index.vue"], resolve);
//appConfigSetting数据表维护
let appConfigSetting = (resolve) =>
  require(["@/pages/dictionaryMaintain/appConfigSetting/index.vue"], resolve);
let recordRiskRating = (resolve) =>
  require(["@/pages/riskAssessment/recordRiskRating.vue"], resolve);
//措施汇总
let measuresTheSummary = (resolve) =>
  require(["@/pages/measuresTheSummary/index.vue"], resolve);
//在院评价
let inHospital = (resolve) =>
  require(["@/pages/nursingEvaluation/inHospital.vue"], resolve);
//转科评价
let transfer = (resolve) =>
  require(["@/pages/nursingEvaluation/index.vue"], resolve);
//护理诊断中心页面
let nursingPlan = (resolve) =>
  require(["@/pages/nursingPlan/index.vue"], resolve);
//出院作业
let discharge = (resolve) => require(["@/pages/discharge/index.vue"], resolve);
//出院小结
let dischargeSummary = (resolve) =>
  require(["@/pages/discharge/dischargeSummary.vue"], resolve);
//出院小结
let consultExhibition = (resolve) =>
  require(["@/pages/patientConsult/consultExhibition.vue"], resolve);
// 评估对问题权重维护功能
let assessToProblem = (resolve) =>
  require(["@/pages/maintain/assessToProblem.vue"], resolve);
// 病人清单路由
// let patientList = resolve =>
//   require(["@/pages/patientList/index.vue"], resolve);
let schedulesExecution = (resolve) =>
  require(["@/pages/schedule/scheduleExecution.vue"], resolve);
let patientNursingTimeLine = (resolve) =>
  require(["@/pages/timeLine/patientNursing.vue"], resolve);
//病案查询
let document = (resolve) => require(["@/pages/document/index.vue"], resolve);
//生命体征
let vitalSign = (resolve) =>
  require(["@/pages/document/vitalSign.vue"], resolve);
//医嘱审核
let orderCheck = (resolve) =>
  require(["@/pages/patientOrder/orderCheck.vue"], resolve);
//医嘱执行
let patientMedicineSchedule = (resolve) =>
  require(["@/pages/patientOrder/patientMedicineSchedule.vue"], resolve);
//医嘱查看
let patientOrders = (resolve) =>
  require(["@/pages/patientOrder/patientOrders.vue"], resolve);
//患者事件
let patientEvent = (resolve) =>
  require(["@/pages/patientEvent/index.vue"], resolve);
//介入手术
let patientPerioperative = (resolve) =>
  require(["@/pages/patientPerioperative/index.vue"], resolve);
//批量输入引流液
let outputMultiPerform = (resolve) =>
  require([
    "@/pages/schedule/components/scheduleTypes/outputMultiPerform.vue",
  ], resolve);
//批量监测
let batchMonitoring = (resolve) =>
  require(["@/pages/schedule/components/batchMonitoring.vue"], resolve);
//2020-06-20因应更新缓存不方便加上缓存维护
let cacheList = (resolve) =>
  require(["@/pages/maintain/cacheList.vue"], resolve);
let assessContent = (resolve) =>
  require(["@/pages/recordSupplement/assessRecord/assessDetail.vue"], resolve);
//岗床配置
let bedToJob = (resolve) =>
  require(["@/pages/nursingJob/bedToJob.vue"], resolve);
//岗位配置
let departmentJob = (resolve) =>
  require(["@/pages/nursingJob/departmentJob.vue"], resolve);
//人员岗位配置
let employeeToJob = (resolve) =>
  require(["@/pages/nursingJob/employeeToJob.vue"], resolve);
//岗位分组配置模版
let careGroupList = (resolve) =>
  require(["@/pages/nursingJob/careGroupList.vue"], resolve);
//批量录入大小便
let batchRelieveOneself = (resolve) =>
  require(["@/pages/batchRelieveOneself/index.vue"], resolve);
//责护派班-派班
let attendance = (resolve) =>
  require(["@/pages/attendance/index.vue"], resolve);
//泵入
let pumping = (resolve) => require(["@/pages/pumping/index.vue"], resolve);
//评估记录补录(出院病人)
let assessRecordSupplement = (resolve) =>
  require([
    "@/pages/recordSupplement/assessRecordOutHospital/index.vue",
  ], resolve);
// 护理评估状态维护
let nursingCodeMaintain = (resolve) =>
  require(["@/pages/maintain/nursingCodeMaintain.vue"], resolve);
// 跳转到医嘱瓶签
let orderLabel = (resolve) =>
  require(["@/pages/patientOrder/orderLabel.vue"], resolve);
// 串检查检验页面
let testExamine = (resolve) =>
  require(["@/pages/transferHisCommon/transferTestExamine.vue"], resolve);
// 药物过敏页面
let drugAllergy = (resolve) =>
  require(["@/pages/patientAllergy/components/allergy.vue"], resolve);
// 串检验标签打印页面
let testLabelPrint = (resolve) =>
  require(["@/pages/transferHisCommon/transferTestLabelPrint.vue"], resolve);
// 串检查单打印页面
let examinationRequisitionLabelPrint = (resolve) =>
  require(["@/pages/transferHisCommon/transferExamLabelPrint.vue"], resolve);
//检验闭环日志查询
let queryClosingControlLog = (resolve) =>
  require(["@/pages/transferHisCommon/queryClosingControlLog.vue"], resolve);
//patientProfileLog补录
let profileLogSupplement = (resolve) =>
  require(["@/pages/recordSupplement/profileLogSupplement/index.vue"], resolve);
let patientNursingRecordDetailSupplement = (resolve) =>
  require([
    "@/pages/recordSupplement/patientNursingRecordDetailSupplement/index.vue",
  ], resolve);
//延续护理 查询出院病人页面
let continuousCare = (resolve) =>
  require(["@/pages/continuousCare/index.vue"], resolve);
// 诊断审核
let suggestProblem = (resolve) =>
  require(["@/pages/suggestProblem/index.vue"], resolve);

// 病区使用导管维护
let stationUseTube = (resolve) =>
  require(["@/pages/dictionaryMaintain/tube/stationUseTube.vue"], resolve);

// 导管对身体部位维护
let tubeToBodyPart = (resolve) =>
  require(["@/pages/dictionaryMaintain/tube/tubeToBodyPart.vue"], resolve);

// 跳转到值班医师维护
let dutyDoctors = (resolve) =>
  require(["@/pages/ElectBoard/DutyDoctors.vue"], resolve);

// 跳转到虚拟病区
let virtualStation = (resolve) =>
  require(["@/pages/dictionaryMaintain/virtualStation/index.vue"], resolve);

//同意书
let agreement = (resolve) => require(["@/pages/agreement/index.vue"], resolve);

// 生命体征统计
let tprChart = (resolve) =>
  require(["@/pages/patientHomePage/tprChart.vue"], resolve);

// 跳转到统计分析
let statistics = (resolve) =>
  require(["@/pages/transferPages/transferStatistics.vue"], resolve);

let statisticsV3 = (resolve) =>
  require(["@/pages/transferPages/transferStatisticsV3.vue"], resolve);

// 跳转到柏拉图统计
let platonicStatistics = (resolve) =>
  require(["@/pages/transferPages/transferPlatonicStatistics.vue"], resolve);
//跳转到风险统计
let riskScreenRecordList = (resolve) =>
  require(["@/pages/riskAssessment/riskScreenRecordList.vue"], resolve);
// 跳转到鱼骨图统计
let fishboneStatistics = (resolve) =>
  require(["@/pages/transferPages/transferFishboneStatistics.vue"], resolve);
let datasetUserEntry = (resolve) =>
  require(["@/pages/transferPages/transferDatasetUserEntry.vue"], resolve);
let indicatorFactorNurse = (resolve) =>
  require(["@/pages/transferPages/transferIndicatorFactorNurse.vue"], resolve);
let eventTimeData = (resolve) =>
  require(["@/pages/transferPages/transfereventTimeData.vue"], resolve);
// 串nursing或html的中转页面=======================================================
// 跳转到三级质控维护
let qcContentMaintain = (resolve) =>
  require(["@/pages/transferPages/qcContentMaintain.vue"], resolve);
//跳转到三级质控主页面
let qcPerformCheck = (resolve) =>
  require(["@/pages/transferPages/qcPerformCheck.vue"], resolve);
let qcPerformStatistics = (resolve) =>
  require(["@/pages/transferPages/qcPerformStatistics.vue"], resolve);
// 跳转到出院病人归档
let patientEMR = (resolve) =>
  require(["@/pages/transferPages/transferPatientEMR.vue"], resolve);
// 跳转到权限维护
let roleAuthority = (resolve) =>
  require(["@/pages/AuthorityManagement/roleAuthority.vue"], resolve);
// 跳转到用户角色维护
let userRole = (resolve) =>
  require(["@/pages/transferPages/transferUserRole.vue"], resolve);
// ================================================================================
// 围手术期交接
let handoverPerioperative = (resolve) =>
  require(["@/pages/handover/handoverPerioperative.vue"], resolve);

// 跳转到敏感指标上报
let sensitiveIndicator = (resolve) =>
  require(["@/pages/transferPages/transferSensitiveIndicator.vue"], resolve);

//病情观察页面
let patientObserve = (resolve) =>
  require(["@/pages/patientObserve/index.vue"], resolve);

let batchRecordDrainage = (resolve) =>
  require(["@/pages/batchTubeDrainage/index.vue"], resolve);
let multyPatientRecordDrainage = (resolve) =>
  require([
    "@/pages/batchTubeDrainage/multyPatientRecordDrainage.vue",
  ], resolve);
// 跳转工作量统计
let workLoad = (resolve) =>
  require(["@/pages/transferPages/transferWorkLoad.vue"], resolve);

//病历审核
let verifyRecords = (resolve) => require(["@/pages/verify/index.vue"], resolve);

let bloodTransfusionRecord = (resolve) =>
  require(["@/pages/transfusion/index.vue"], resolve);
//跳转到一级质控统计饼状图扣分细项
let qcFaultStatistics = (resolve) =>
  require(["@/pages/transferPages/qcFaultStatistics.vue"], resolve);
//点药
let stockInventory = (resolve) =>
  require(["@/pages/stockInventory/index.vue"], resolve);
//病案归档
let documentPigeonhole = (resolve) =>
  require(["@/pages/documentPigeonhole/index.vue"], resolve);
let deliveryRecord = (resolve) =>
  require(["@/pages/patientDelivery_bak/index.vue"], resolve);
//专项护理溶栓
let patientThrombolysis = (resolve) =>
  require(["@/pages/patientThrombolysis/index.vue"], resolve);
let assessTemplate = (resolve) =>
  require(["@/pages/dictionaryMaintain/assessTemplate/index.vue"], resolve);

//病情观察可自行维护页面
let observeTemplate = (resolve) =>
  require(["@/pages/patientObserve/observeTemplates.vue"], resolve);
//病历转换页面
let documentChange = (resolve) =>
  require(["@/pages/dictionaryMaintain/documentChange/index.vue"], resolve);

//出院病人评估记录和专项护理记录查看
let assessRecordLook = (resolve) =>
  require(["@/pages/recordSupplement/assessRecordLook/index.vue"], resolve);
/* 护理评估 */
// let assessLook = resolve =>
//   require(["@/pages/nursingAssessment/index.vue"], resolve);
// /* 伤口评估 */
// let woundLook = resolve => require(["@/pages/wound/index.vue"], resolve);
//io记录单
let ioLook = (resolve) =>
  require(["@/pages/IO/ioRecordMaintenance.vue"], resolve);
/* 导管评估 */
let tubeLook = (resolve) => require(["@/pages/tube/index.vue"], resolve);
/* 血糖 */
let glucoseLook = (resolve) => require(["@/pages/glucose/index.vue"], resolve);
// 抢救记录
let rescueRecordLook = (resolve) =>
  require(["@/autoPages/patientRescue/index.vue"], resolve);
//疼痛评估
// let patientPainLook = resolve =>
//   require(["@/pages/patientPain/index.vue"], resolve);
/* 末梢血运 */
let peripheralCirculationLook = (resolve) =>
  require(["@/autoPages/peripheralCirculation/index.vue"], resolve);
//专项护理母乳喂养
let patientBabyFeeding = (resolve) =>
  require(["@/pages/patientBabyFeeding/index.vue"], resolve);

//中山手术记录单
let operationRecord = (resolve) =>
  require(["@/pages/operationRecord/index.vue"], resolve);
//输血专项护理
let patientTransfusion = (resolve) =>
  require(["@/pages/transfusion/index.vue"], resolve);
//饮食记录
let patientDietIntake = (resolve) =>
  require(["@/pages/patientDietIntake/index.vue"], resolve);
//谵妄评估
let patientDelirium = (resolve) =>
  require(["@/pages/patientDelirium/index.vue"], resolve);
// 费用查询
let patientCosts = (resolve) =>
  require(["@/pages/patientCosts/index.vue"], resolve);
//
let settingDescription = (resolve) =>
  require(["@/pages/dictionaryMaintain/settingDescription/index.vue"], resolve);
// 不良事件查询
let adverseEvent = (resolve) =>
  require(["@/pages/adverseEvent/index.vue"], resolve);
// 病区权限维护
let stationSwitch = (resolve) =>
  require(["@/pages/stationSwitch/index.vue"], resolve);

// 输血查对页面
let transfusionRecordsCheckForm = (resolve) =>
  require(["@/pages/transfusion/transfusionCheckIn.vue"], resolve);

//宣教资料维护
let interventionFileUpload = (resolve) =>
  require([
    "@/pages/dictionaryMaintain/interventionFileUpload/index.vue",
  ], resolve);
let AdmissionStatistics = (resolve) =>
  require(["@/pages/AdmissionStatistics/index.vue"], resolve);
let addBed = (resolve) =>
  require(["@/pages/dictionaryMaintain/addBed/index.vue"], resolve);
let physicianRelatedBed = (resolve) =>
  require(["@/pages/transferPages/nursingBoardRelatedBed.vue"], resolve);
let physicianGroup = (resolve) =>
  require(["@/pages/transferPages/nursingBoardPhysicianGroup.vue"], resolve);
let hospitalDutyMaintain = (resolve) =>
  require(["@/pages/transferPages/nursingBoardHospitalDuty.vue"], resolve);
let customBulletinMaintain = (resolve) =>
  require(["@/pages/transferPages/nursingBoardBulletinMaintain.vue"], resolve);
let treatmentMaintain = (resolve) =>
  require(["@/pages/transferPages/nursingBoardTreatmentMaintain.vue"], resolve);
let specimenCollection = (resolve) =>
  require(["@/pages/transferHisCommon/transferTestCollection.vue"], resolve);
let specimenTransfer = (resolve) =>
  require(["@/pages/transferHisCommon/TestTransfer.vue"], resolve);
let hospitalDutyToStationMaintain = (resolve) =>
  require(["@/pages/transferPages/nursingBoardHospitalDutyToStation"], resolve);
let doctorDutyBatchMaintain = (resolve) =>
  require([
    "@/pages/transferPages/nursingBoardDoctorDutyBatchMaintain",
  ], resolve);
//2022-01-09 2225 病情观察改版新增页面 -正元
let bedsideObservation = (resolve) =>
  require(["@/pages/patientObserve/bedsideObservation.vue"], resolve);
//2022-01-19 2224 病情观察评价新增页面 -正元
let observeEvalutaion = (resolve) =>
  require(["@/pages/patientObserve/observeEvalutaion.vue"], resolve);
let skinTest = (resolve) =>
  require(["@/pages/patientAllergy/components/skinTest.vue"], resolve);
let allergyAndSkinTest = (resolve) =>
  require(["@/pages/patientAllergy/index.vue"], resolve);
//跳转其它系统公共页面
let jumpOtherSystemCommon = (resolve) =>
  require(["@/pages/transferPages/jumpOtherSystemCommon.vue"], resolve);
//跳转个案管理系统相关画面
let jumpCaseSystem = (resolve) =>
  require(["@/pages/transferPages/jumpCaseSystem.vue"], resolve);
//神经血管评估
let patientNeurovascular = (resolve) =>
  require(["@/pages/PatientNeurovascular/index.vue"], resolve);
//评估记录
let formRecord = (resolve) =>
  require(["@/pages/formRecord/index.vue"], resolve);
// 文件预览画面
let filePreview = (resolve) =>
  require(["@/pages/transferPages/filePreview.vue"], resolve);
//楼层维护主页面
let hospitalBuilding = (resolve) =>
  require(["@/pages/dictionaryMaintain/hospitalBuilding/index.vue"], resolve);
//楼栋维护页面
let building = (resolve) =>
  require([
    "@/pages/dictionaryMaintain/hospitalBuilding/building.vue",
  ], resolve);
//楼层维护页面
let floor = (resolve) =>
  require(["@/pages/dictionaryMaintain/hospitalBuilding/floor.vue"], resolve);
//楼层位置维护页面
let location = (resolve) =>
  require([
    "@/pages/dictionaryMaintain/hospitalBuilding/location.vue",
  ], resolve);
//房间维护页面
let room = (resolve) =>
  require(["@/pages/dictionaryMaintain/hospitalBuilding/room.vue"], resolve);
//动静脉内瘘
let arteriovenousFistula = (resolve) =>
  require(["@/pages/arteriovenousFistula/index.vue"], resolve);
//异动表维护
let dataTableEditList = (resolve) =>
  require(["@/pages/recordSupplement/dataTableEditList/index.vue"], resolve);
//重症设备绑定
let equipmentBinding = (resolve) =>
  require(["@/pages/equipmentBinding/index.vue"], resolve);
//操作日志
let operationLog = (resolve) =>
  require(["@/pages/operationLog/index.vue"], resolve);
//io记录单
let summaryIntakeOutput = (resolve) =>
  require(["@/pages/IO/summaryIntakeOutput.vue"], resolve);
//医疗机构给药数据
let stationMedicineSchedule = (resolve) =>
  require(["@/pages/patientOrder/stationMedicineSchedule.vue"], resolve);
//看板医嘱总览自定义项目维护画面
let orderOverviewMaintain = (resolve) =>
  require([
    "@/pages/transferPages/nursingBoardOrderOverviewMaintain.vue",
  ], resolve);
let transferNursingManagement = (resolve) =>
  require(["@/pages/transferPages/transferNursingManagement.vue"], resolve);
//常见问题
let drugListConvertedVolumeMaintain = (resolve) =>
  require([
    "@/pages/dictionaryMaintain/drugListConvertedVolumeMaintain/index.vue",
  ], resolve);
/* 导出变量 */
export default {
  room,
  location,
  floor,
  building,
  hospitalBuilding,
  componentDemo,
  caAuthorize,
  externalTransfer,
  help,
  transferManagement,
  annuaPlanMaintain,
  tube,
  multipleHandoffByNurse,
  multipleHandonByNurse,
  careGroupList,
  patientConsult,
  replyConsult,
  patientHomePage,
  handoverSBAR,
  patientTestDemo,
  handoverQueryByInpatient,
  handoverQueryByStation,
  handover,
  dailyLog,
  schedule,
  scheduleByBatch,
  glucose,
  glucoseChart,
  io,
  ioRecord,
  drainageStatistics,
  ioRecordMaintenance,
  ioBalanceStatistics,
  ioDrainageChart,
  ioStatisticalChart,
  measuresTheSummary,
  patientPerioperative,
  nursingEvaluation,
  systemVersionMaintain,
  questionAndAnaswer,
  pinyinListMaintain,
  appConfigSetting,
  recordRiskRating,
  consultAssign,
  nurseHandover,
  inHospital,
  nursingPlan,
  discharge,
  transfer,
  dischargeSummary,
  assessToProblem,
  schedulesExecution,
  patientNursingTimeLine,
  consultExhibition,
  document,
  vitalSign,
  orderCheck,
  patientMedicineSchedule,
  patientOrders,
  patientEvent,
  outputMultiPerform,
  assessToProblem,
  cacheList,
  assessContent,
  bedToJob,
  departmentJob,
  employeeToJob,
  batchRelieveOneself,
  attendance,
  pumping,
  assessRecordSupplement,
  nursingCodeMaintain,
  orderLabel,
  testExamine,
  drugAllergy,
  testLabelPrint,
  examinationRequisitionLabelPrint,
  profileLogSupplement,
  patientNursingRecordDetailSupplement,
  continuousCare,
  suggestProblem,
  stationUseTube,
  tubeToBodyPart,
  consultGoalToEmployee,
  dutyDoctors,
  virtualStation,
  agreement,
  batchMonitoring,
  medicineToRecord,
  tprChart,
  queryClosingControlLog,
  statistics,
  statisticsV3,
  platonicStatistics,
  fishboneStatistics,
  datasetUserEntry,
  indicatorFactorNurse,
  qcContentMaintain,
  qcPerformCheck,
  qcPerformStatistics,
  patientEMR,
  roleAuthority,
  userRole,
  handoverPerioperative,
  sensitiveIndicator,
  patientObserve,
  batchRecordDrainage,
  workLoad,
  verifyRecords,
  bloodTransfusionRecord,
  qcFaultStatistics,
  stockInventory,
  documentPigeonhole,
  deliveryRecord,
  patientThrombolysis,
  assessTemplate,
  observeTemplate,
  documentChange,
  //出院病人只允许查看
  assessRecordLook,
  ioLook,
  tubeLook,
  glucoseLook,
  rescueRecordLook,
  peripheralCirculationLook,
  //=========
  patientBabyFeeding,
  operationRecord,
  patientTransfusion,
  patientDietIntake,
  patientDelirium,
  riskScreenRecordList,
  patientCosts,
  settingDescription,
  adverseEvent,
  stationSwitch,
  transfusionRecordsCheckForm,
  interventionFileUpload,
  addBed,
  AdmissionStatistics,
  physicianRelatedBed,
  physicianGroup,
  hospitalDutyMaintain,
  hospitalDutyToStationMaintain,
  doctorDutyBatchMaintain,
  customBulletinMaintain,
  treatmentMaintain,
  specimenCollection,
  specimenTransfer,
  bedsideObservation,
  observeEvalutaion,
  skinTest,
  allergyAndSkinTest,
  jumpOtherSystemCommon,
  jumpCaseSystem,
  patientNeurovascular,
  formRecord,
  filePreview,
  arteriovenousFistula,
  dataTableEditList,
  equipmentBinding,
  operationLog,
  summaryIntakeOutput,
  eventTimeData,
  stationMedicineSchedule,
  multyPatientRecordDrainage,
  orderOverviewMaintain,
  transferNursingManagement,
  drugListConvertedVolumeMaintain,
};
