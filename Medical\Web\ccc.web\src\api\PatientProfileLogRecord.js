/*
 * FilePath     : \src\api\PatientProfileLogRecord.js
 * Author       : 石高阳
 * Date         : 2020-09-28 09:15
 * LastEditors  : 石高阳
 * LastEditTime : 2020-10-08 10:45
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientProfileLog";

//生成API
export const urls = {
  //取得病人数据
  GetPatientProfileLog: baseUrl + "/GetPatientProfileLog",
  //删除护理记录
  DeletePatientprofileLog: baseUrl + "/DeletePatientprofileLog",
  //更新护理记录
  UpdatePatientprofileLog: baseUrl + "/UpdatePatientprofileLog",
  //新增护理记录
  SavePatientprofileLog: baseUrl + "/SavePatientprofileLog"
};

//取得病人数据
export const GetPatientProfileLog = params => {
  return http.get(urls.GetPatientProfileLog, params);
};
// 删除补录记录
export const DeletePatientprofileLog = params => {
  return http.post(urls.DeletePatientprofileLog, qs.stringify(params));
};
// 修改补录记录
export const UpdatePatientprofileLog = params => {
  return http.post(urls.UpdatePatientprofileLog, params);
};
// 新增补录记录
export const SavePatientprofileLog = params => {
  return http.post(urls.SavePatientprofileLog, params);
};
