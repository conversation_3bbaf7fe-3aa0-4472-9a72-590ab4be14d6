<!--
 * FilePath     : \src\pages\nursingPlan\components\nursingPlan.vue
 * Author       : 苏军志
 * Date         : 2020-05-08 11:56
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-16 08:30
 * Description  : 护理计划
                  2021-12-30 因应线上要求护理计划时间调整为可修改 -正元
 -->
<template>
  <base-layout class="nursing-plan" v-loading="loading" :element-loading-text="loadingText">
    <div slot="header" class="top">
      <el-button type="primary" icon="iconfont icon-save-button" @click="save(1)">保存</el-button>
      <el-button class="print-button" icon="iconfont icon-temp-save" @click="save(0)">暂存</el-button>
    </div>
    <div class="left-wrap">
      <el-table
        class="nursing-problem-table"
        ref="problem"
        :data="problemList"
        highlight-current-row
        border
        stripe
        height="100%"
        @row-click="showIntervention"
      >
        <el-table-column width="20" header-align="center">
          <template slot-scope="problem">
            <div class="mark" v-if="problem.row.selected">*</div>
          </template>
        </el-table-column>
        <el-table-column label="护理问题" header-align="center">
          <template slot-scope="problem">
            <span :class="{ 'no-save': problem.row.status == 0 }">
              {{ problem.row.nursingProblem }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="预期护理目标" width="130" align="center">
          <template slot-scope="problem">
            <el-select
              v-model="problem.row.nursingGoalId"
              @change="changeGoal($event, problem.row, problem.row.problemGoals)"
              style="width: 115px"
            >
              <el-option
                v-for="goal in problem.row.problemGoals"
                :key="goal.id"
                :label="goal.shortName"
                :value="goal.id"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="right-wrap">
      <div class="related-list">
        <el-tag v-for="(related, index) in relatedFactorList" :key="index" color="#FAFAFA">{{ related.name }}</el-tag>
      </div>
      <el-table
        class="nursing-intervention-table"
        ref="intervention"
        :data="interventionList"
        highlight-current-row
        border
        stripe
        height="100%"
        @select="selectionIntervention"
        @select-all="selectionAllIntervention"
      >
        <el-table-column type="selection" :width="convertPX(40)" align="center" class-name="select"></el-table-column>
        <el-table-column label="频次" width="180" header-align="center">
          <template slot-scope="intervention">
            <frequency-selector
              v-model="intervention.row.frequencyID"
              :frequencyList="frequencyList"
              @select="selectFrequency(intervention.row)"
            ></frequency-selector>
          </template>
        </el-table-column>
        <el-table-column label="护理措施" header-align="center">
          <template slot-scope="intervention">
            <i class="iconfont icon-info" @click="showMessage(intervention.row.inforbuttonContent)"></i>
            <span v-html="intervention.row.interventionName"></span>
          </template>
        </el-table-column>
        <el-table-column label="开始日期" width="140" header-align="center">
          <template slot-scope="intervention">
            <el-date-picker
              v-model="intervention.row.startDate"
              :clearable="false"
              type="datetime"
              placeholder="选择日期时间"
              style="width: 125px"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              @change="startTimeCheck(intervention.row)"
            ></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="结束日期" width="140" header-align="center">
          <template slot-scope="intervention">
            <el-date-picker
              v-model="intervention.row.endDate"
              :clearable="false"
              type="datetime"
              placeholder="选择日期时间"
              style="width: 125px"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              @change="endTimeCheck(intervention.row)"
            ></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="首次" width="50" align="center">
          <template slot-scope="intervention">
            <el-checkbox
              :disabled="getDisabledFlag(intervention.row)"
              v-model="intervention.row.firstDayFlag"
              true-label="*"
              false-label=""
              @change="updateList(intervention.row)"
            ></el-checkbox>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import {
  GetNursingPlanProblem,
  GetRelatedFactor,
  GetIntervention,
  GetNeedFirstFrequency,
  SavePlan,
} from "@/api/NursingPlan";
import frequencySelector from "@/components/selector/frequencySelector";
import { GetTypeFrequency } from "@/api/Frequency";
import { GetShifDayStartAndEndDateTime } from "@/api/StationShift";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    frequencySelector,
  },
  props: {
    inpatientinfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      loading: false,
      loadingText: "加载中……",
      relatedFactorList: [],
      problemList: [],
      interventionList: [],
      frequencyList: undefined,
      currentPatientProblemID: undefined,
      selectInterventions: [],
      needFirstFrequencyList: [],
      shiftStartDate: undefined,
      shiftEndDate: undefined,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  watch: {
    "inpatientinfo.inpatientID": {
      async handler(newvalue) {
        this.relatedFactorList = [];
        this.problemList = [];
        this.interventionList = [];
        if (!newvalue) return;
        this.init();
      },
      immediate: true,
    },
  },
  beforeMount() {
    //获取当天班别开始和结束日期
    this.getShifDayStartAndEndDateTime();
  },
  methods: {
    // 初始化
    async init() {
      this.loading = true;
      this.loadingText = "加载中……";
      // 获取频次列表
      await this.GetTypeFrequency();
      await this.getNursingPlanProblem();
      // 获取是否首次评估
      this.needFirstFrequencyList = [];
      let params = {
        inpatientID: this.inpatientinfo.inpatientID,
      };
      await GetNeedFirstFrequency(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.needFirstFrequencyList = result.data;
        }
      });
    },
    // 获取频次列表
    async GetTypeFrequency() {
      this.frequencyList = [];
      let params = {
        visibleFlag: true,
      };
      await GetTypeFrequency(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.frequencyList = result.data;
        }
      });
    },
    // 获取问题列表
    async getNursingPlanProblem() {
      this.problemList = [];
      this.interventionList = [];
      let params = {
        inpatientID: this.inpatientinfo.inpatientID,
        stationID: this.inpatientinfo.stationID,
        nursingOrder: false,
      };
      // 获取问题列表
      await GetNursingPlanProblem(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.problemList = result.data;
          if (this.problemList && this.problemList.length > 0) {
            this.$nextTick(() => {
              // 默认选中
              if (this.$refs.problem) {
                this.$refs.problem.setCurrentRow(this.problemList[0]);
              }
              this.showIntervention(this.problemList[0]);
            });
          }
        } else {
          this.loading = false;
        }
      });
    },
    async showIntervention(row) {
      if (row.clickTimes == undefined) {
        row.clickTimes = 1;
      } else {
        row.clickTimes = row.clickTimes + 1;
      }
      // 获取护理问题相关因素
      this.relatedFactorList = [];
      let params = {
        patientProblemId: row.patientProblemId,
      };
      this.loading = true;
      await GetRelatedFactor(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.relatedFactorList = result.data;
        }
      });
      // 获取护理问题对应的护理措施
      this.interventionList = [];
      params = {
        problemID: row.problemId,
        patientProblemID: row.patientProblemId,
      };
      this.loading = true;
      this.loadingText = "加载中……";
      await GetIntervention(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.interventionList = result.data.patientInterventions;
          for (let i = 0; i < this.interventionList.length; i++) {
            // 防止有重复措施，添加序号区分
            this.interventionList[i].originalStartTime = this.interventionList[i].startDate;
            this.interventionList[i].originalEndTime = this.interventionList[i].endDate;
          }
          //解决patientProblemID串的问题  --GPC
          this.currentPatientProblemID = row.patientProblemId;
          this.$nextTick(() => {
            // 默认选中
            if (this.$refs.intervention) {
              this.$refs.intervention.setCurrentRow(this.interventionList[0]);
            }
          });
        }
      });
      this.loading = false;
      // 设置已经选择的值 row.clickTimes > 1 第二次点击，取已选的数据回显
      if (row.clickTimes > 1) {
        // 先清除所有选择标记
        for (let i = 0; i < this.interventionList.length; i++) {
          this.interventionList[i].selected = false;
        }
        let selectList = undefined;
        if (this.selectInterventions.length > 0) {
          for (let i = 0; i < this.selectInterventions.length; i++) {
            if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
              selectList = this.selectInterventions[i].interventions;
              break;
            }
          }
        }
        if (selectList && selectList.length > 0) {
          // 回显本次选择的项目
          this.setSelectFlag(this.currentPatientProblemID, true);
          selectList.forEach((intervention) => {
            for (let i = 0; i < this.interventionList.length; i++) {
              if (this.interventionList[i].interventionID == intervention.interventionID && intervention.selected) {
                this.interventionList[i].selected = true;
                this.interventionList[i].frequencyID = intervention.frequencyID;
                this.interventionList[i].frequency = intervention.frequency;
                this.interventionList[i].frequencyDescription = intervention.frequencyDescription;
                this.interventionList[i].firstDayFlag = intervention.firstDayFlag;
                this.interventionList[i].startDate = intervention.startDate;
                this.interventionList[i].endDate = intervention.endDate;
                if (this.$refs.intervention) {
                  this.$nextTick(() => {
                    this.$refs.intervention.toggleRowSelection(this.interventionList[i], true);
                  });
                }
              }
            }
          });
        }
      } else {
        let oldList = [];
        for (let i = 0; i < this.interventionList.length; i++) {
          if (this.interventionList[i].patientInterventionID) {
            if (this.$refs.intervention) {
              this.interventionList[i].selected = true;
              this.$nextTick(() => {
                this.$refs.intervention.toggleRowSelection(this.interventionList[i]);
              });
            }
            oldList.push(this.interventionList[i]);
          } else {
            if (this.interventionList[i].selected) {
              if (this.$refs.intervention) {
                this.$nextTick(() => {
                  this.$refs.intervention.toggleRowSelection(this.interventionList[i]);
                });
              }
              oldList.push(this.interventionList[i]);
            }
          }
        }
        if (oldList.length > 0) {
          let temp = {
            patientProblemID: this.currentPatientProblemID,
            interventions: oldList,
          };
          this.selectInterventions.push(temp);
          this.setSelectFlag(this.currentPatientProblemID, true);
        } else {
          this.setSelectFlag(this.currentPatientProblemID, false);
        }
      }
    },
    // 全选全不选
    selectionAllIntervention(interventions) {
      //全选
      if (interventions.length) {
        interventions.forEach((item) => {
          item.selected = false;
          this.selectionIntervention(interventions, item);
        });
      } else {
        //全部取消
        this.interventionList.forEach((item) => {
          item.selected = false;
          this.setSelectFlag(this.currentPatientProblemID, false);
          this.updateList(item);
        });
      }
    },
    // 显示护理问题对应的措施列表
    selectionIntervention(interventions, row) {
      row.selected = !row.selected;
      // 设置是否首次
      this.setFirst(row);
      let isExist = false;
      for (let i = 0; i < this.selectInterventions.length; i++) {
        if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
          let flag = false;
          for (let j = this.selectInterventions[i].interventions.length - 1; j >= 0; j--) {
            if (this.selectInterventions[i].interventions[j].interventionID == row.interventionID) {
              flag = true;
              if (!row.selected) {
                this.selectInterventions[i].interventions[j].selected = false;
                if (!row.patientInterventionID) {
                  // 移除
                  this.selectInterventions[i].interventions.splice(j, 1);
                }
              } else {
                this.selectInterventions[i].interventions[j].selected = true;
              }
              break;
            }
          }
          if (!flag && row.selected) {
            this.selectInterventions[i].interventions.push(row);
          }
          if (interventions.length == 0) {
            this.setSelectFlag(this.currentPatientProblemID, false);
          } else {
            this.setSelectFlag(this.currentPatientProblemID, true);
          }
          isExist = true;
          break;
        }
      }
      if (!isExist && interventions.length > 0) {
        let temp = {
          patientProblemID: this.currentPatientProblemID,
          interventions: interventions,
        };
        this.selectInterventions.push(temp);
        this.setSelectFlag(this.currentPatientProblemID, true);
      }
    },
    // 切换护理目标
    changeGoal(goalID, row, nursingGoals) {
      row.nursingGoalId = goalID;
      let nursingGoal = nursingGoals[goalID];
      if (nursingGoal) {
        row.outComeStatus = nursingGoal.status;
      }
      if (this.currentPatientProblemID != row.patientProblemId) {
        this.$refs.problem.setCurrentRow(row);
        this.showIntervention(row);
      }
    },
    // 将已选中项目更改的内容同步到selectInterventions中
    updateList(row) {
      for (let i = 0; i < this.selectInterventions.length; i++) {
        if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
          let interventions = this.selectInterventions[i].interventions;
          for (let j = 0; j < interventions.length; j++) {
            if (interventions[j].interventionID == row.interventionID) {
              interventions[j] = row;
              break;
            }
          }
          break;
        }
      }
    },
    setFirst(row) {
      row.firstDayFlag = "";
      let length = this.needFirstFrequencyList.length;
      if (
        row.selected &&
        this.needFirstFrequencyList &&
        length > 0 &&
        this.needFirstFrequencyList.indexOf(Number(row.frequencyID)) != -1
      ) {
        row.firstDayFlag = "*";
      }
      this.updateList(row);
    },
    // 设置护理问题是否有选择的对应的措施
    setSelectFlag(patientProblemID, flag) {
      for (let i = 0; i < this.problemList.length; i++) {
        if (this.problemList[i].patientProblemId == patientProblemID) {
          this.$set(this.problemList[i], "selected", flag);
          break;
        }
      }
    },
    save(status) {
      if (this.loading || this.problemList.length <= 0) {
        return;
      }
      this.loading = true;
      this.loadingText = "保存中……";
      let patientInfo = {
        bedID: this.inpatientinfo.bedID,
        bedNumber: this.inpatientinfo.bedNumber,
        caseNumber: this.inpatientinfo.caseNumber,
        chartNo: this.inpatientinfo.chartNo,
        departmentListID: this.inpatientinfo.departmentListID,
        gender: this.inpatientinfo.gender,
        genderCode: this.inpatientinfo.genderCode,
        inpatientID: this.inpatientinfo.inpatientID,
        patientID: this.inpatientinfo.patientID,
        stationID: this.inpatientinfo.stationID,
        age: this.inpatientinfo.age,
      };
      let saveParams = {};
      let submitList = [];
      for (let i = 0; i < this.problemList.length; i++) {
        let problem = this.problemList[i];
        if (!problem.selected) {
          if (status == 1) {
            // 保存不允许不选
            this.loading = false;
            this._showTip("warning", "<font color='red'><b>" + problem.nursingProblem + "</b></font>没有选择措施！");
            return;
          }
        }
        let temp = this.selectInterventions.find((intervention) => {
          return intervention.patientProblemID == problem.patientProblemId;
        });
        let patientGoal = {
          goalID: problem.nursingGoalId,
          id: problem.outComeStatus,
        };
        let interventions = [];
        if (temp) {
          let newTemp = JSON.parse(JSON.stringify(temp));
          newTemp.interventions.forEach((intervention) => {
            if (!intervention.selected) {
              intervention.interventionID = null;
            }
            if (typeof intervention.frequencyID == "object") {
              intervention.frequencyID = intervention.frequencyID[1];
            }
            //更新选中的值，复制给interventions
            this.frequencyList.find((frequency) => {
              if (frequency.children) {
                frequency.children.forEach((children) => {
                  if (children.value == intervention.frequencyID) {
                    intervention.frequency = children.search;
                    intervention.frequencyDescription = children.label;
                    intervention.frequencyID = Number(children.value);
                  }
                });
              }
            });
          });
          interventions = newTemp.interventions;
        }
        submitList.push({
          patientGoal: patientGoal,
          patientInterventions: interventions,
          patientProblemID: problem.patientProblemId,
          problemID: problem.problemId,
          status: status,
        });
      }
      if (submitList.length == 0) {
        this.loading = false;
        this._showTip("warning", "无计划需要保存！");
        return;
      }
      saveParams = {
        patient: patientInfo,
        submitList: submitList,
      };
      return SavePlan(saveParams).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          if (status == 1) {
            // 跳转页面
            this.$emit("jump-page");
          } else {
            // 刷新当前页面
            this.refresh();
          }
        }
      });
    },
    selectFrequency(row) {
      this.setFirst(row);
    },
    //显示措施提示信息
    showMessage(messageContent) {
      this._showMessage({
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
    // 刷新页面
    refresh() {
      this.currentPatientProblemID = undefined;
      this.selectInterventions = [];
      this.getNursingPlanProblem();
    },
    startTimeCheck(row) {
      let check = false;
      if (row.originalStartDateTime > row.startDate) {
        this._showTip("warning", "措施开始时间不得早于问题开始时间！");
        check = true;
      }
      if (row.startDate > row.endDate) {
        this._showTip("warning", "措施开始时间不得晚于措施结束时间！");
        check = true;
      }
      if (check) {
        row.startDate = row.originalStartTime;
      }
    },
    endTimeCheck(row) {
      let check = false;
      if (row.originalEndDateTime < row.endDate) {
        this._showTip("warning", "措施结束时间不得晚于问题预计结束时间！");
        check = true;
      }
      if (row.startDate > row.endDate) {
        this._showTip("warning", "措施开始时间不得晚于措施结束时间！");
        check = true;
      }
      if (row.endDate < this._datetimeUtil.getNow("yyyy-MM-dd hh:mm")) {
        this._showTip("warning", "措施结束时间不得早于当前时间！");
        check = true;
      }
      if (check) {
        row.endDate = row.originalEndTime;
      }
    },
    getDisabledFlag(row) {
      if (!row || !row.originalStartDateTime || !this.shiftStartDate || !this.shiftEndDate) {
        return false;
      }
      //未勾选 不可选择
      if (!row.selected) {
        return true;
      }
      //未在班别日期之内 禁止勾选
      if (this._datetimeUtil.getTimeDifference(this.shiftStartDate, row.originalStartDateTime, undefined, "M") < 0) {
        return true;
      }
      if (this._datetimeUtil.getTimeDifference(row.originalStartDateTime, this.shiftEndDate, undefined, "M") < 0) {
        return true;
      }
      return false;
    },
    /**
     * description: 获取当天班别开始时间和结束时间
     * param {*}
     * return {*}
     */
    getShifDayStartAndEndDateTime() {
      let params = {
        type: "S",
      };
      GetShifDayStartAndEndDateTime(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.shiftStartDate = result.data.startDateTime;
          this.shiftEndDate = result.data.endDateTime;
        } else {
          this.shiftStartDate = undefined;
          this.shiftEndDate = undefined;
        }
      });
    },
  },
};
</script>

<style <style lang="scss">
.nursing-plan {
  .top {
    text-align: right;
  }
  .el-select .el-input__inner {
    color: $base-color;
  }
  .left-wrap {
    float: left;
    height: 100%;
    width: 360px;
    .nursing-problem-table {
      .mark {
        font-weight: bold;
        font-size: 24px;
        margin-left: -3px;
        margin-top: 8px;
      }
      .mark,
      .no-save {
        color: #ff7400;
      }
      .cell {
        text-overflow: unset;
      }
    }
  }
  .right-wrap {
    float: right;
    height: 100%;
    width: calc(100% - 380px);
    margin-left: 15px;
    display: flex;
    flex-direction: column;
    .related-list {
      .el-tag {
        margin: 0 3px 5px 3px;
        font-size: 13px;
        border-color: #ddd;
        letter-spacing: 0.6px;
        color: #000;
      }
    }
    .nursing-intervention-table {
      flex: auto;
      .select {
        padding: 3px;
      }
      .cell {
        text-overflow: unset;
      }
    }
  }
}
</style>
