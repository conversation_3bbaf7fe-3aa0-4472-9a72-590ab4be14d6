/*
 * FilePath     : \src\utils\convertUtil.js
 * Author       : 郭鹏超
 * Date         : 2022-11-08 10:54
 * LastEditors  : 来江禹
 * LastEditTime : 2022-11-17 19:46
 * Description  :CSS 长度 宽度样式转换
 * CodeIterationRecord:
 */
import flexible from "@/utils/flexible.js";
/**
 * description: 样式转换为规范样式
 * param {*} val
 * param {*} remFlag  是否转化为自适应样式
 * return {*}
 */
let getHeigt = (val, remFlag = false) => {
  // 如果是auto则直接返回
  if (val == "auto") {
    return "auto";
  }
  if (val.indexOf("%") != -1) {
    return val;
  }
  val = val.replace("px", "");
  if (!val && val != 0) {
    return "auto";
  }
  return remFlag ? flexible.convertPX(val) + "px" : val + "px";
};
export default {
  getHeigt
};
