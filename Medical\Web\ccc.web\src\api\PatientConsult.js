/*
 * FilePath     : \nursingBoard.webe:\CCC3.1\Medical\Web\ccc.web\src\api\PatientConsult.js
 * Author       : 郭自飞
 * Date         : 2020-01-14 14:16
 * LastEditors  : 马超
 * LastEditTime : 2022-11-10 14:05
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/Consult";

export const urls = {
  GetPatientConsult: baseUrl + "/GetPatientConsult",
  GetByPatientConsultID: baseUrl + "/GetByPatientConsultID",
  GetConsultGoal: baseUrl + "/GetConsultGoal",
  GetConsultGoalById: baseUrl + "/GetConsultGoalById",
  GetConsultGoalEmployee: baseUrl + "/GetConsultGoalEmployee",
  SaveConsult: baseUrl + "/SaveConsult",
  UpdateConsult: baseUrl + "/UpdateConsult",
  DelectConsultByID: baseUrl + "/DelectConsultByID",
  ReplyConsult: baseUrl + "/ReplyConsult",
  GetGroupPersonnelList: baseUrl + "/GetGroupPersonnelList",
  GetByCondition: baseUrl + "/GetByCondition",
  GetConsultSetting: baseUrl + "/GetConsultSetting",
  GetConsultAssign: baseUrl + "/GetConsultAssign",
  GetPatientConsultBySationID: baseUrl + "/GetPatientConsultBySationID",
  GetConsultByConsultGroupID: baseUrl + "/GetConsultByConsultGroupID",
  SaveMultidisciplinaryCousult: baseUrl + "/SaveMultidisciplinaryCousult",
  GetConsultAssessView: baseUrl + "/GetConsultAssessView",
  SaveConsultDetail: baseUrl + "/SaveConsultDetail",
  GetConsultRecord: baseUrl + "/GetConsultRecord",
};

// 根据inpatientid会诊信息
export const GetPatientConsult = params => {
  return http.get(urls.GetPatientConsult, params);
};
// 根据会诊序号查询会诊信息
export const GetByPatientConsultID = params => {
  return http.get(urls.GetByPatientConsultID, params);
};
// 根据条件查询会诊信息
export const GetByCondition = params => {
  return http.get(urls.GetByCondition, params);
};
// 获取同组会诊人员
export const GetConsultSetting = () => {
  return http.get(urls.GetConsultSetting);
};
// 获取会诊过期时间
export const GetGroupPersonnelList = params => {
  return http.get(urls.GetGroupPersonnelList, params);
};
// 会诊回复
export const ReplyConsult = params => {
  return http.post(urls.ReplyConsult, params);
};
// 获得一阶会诊目的
export const GetConsultGoal = params => {
  return http.get(urls.GetConsultGoal, params);
};
// 获得对应的一阶会诊目的
export const GetConsultGoalById = params => {
  return http.get(urls.GetConsultGoalById, params);
};
// 获取会诊人员
export const GetConsultGoalEmployee = params => {
  return http.get(urls.GetConsultGoalEmployee, params);
};
// 新增
export const SaveConsult = params => {
  return http.post(urls.SaveConsult, params);
};
// 修改
export const UpdateConsult = params => {
  return http.post(urls.UpdateConsult, params);
};
// 删除
export const DelectConsultByID = params => {
  return http.get(urls.DelectConsultByID, params);
};
// 获取会诊是否指派,并获取会诊可指派科室
export const GetConsultAssign = params => {
  return http.get(urls.GetConsultAssign, params);
};
// 根据病区查询未指派病人信息
export const GetPatientConsultBySationID = params => {
  return http.get(urls.GetPatientConsultBySationID, params);
};
// 根据主id查询同组会诊信息
export const GetConsultByConsultGroupID = params => {
  return http.get(urls.GetConsultByConsultGroupID, params);
};
// 多学科会诊发起
export const SaveMultidisciplinaryCousult = params => {
  return http.post(urls.SaveMultidisciplinaryCousult, params);
};
// 获取会诊评价模版
export const GetConsultAssessView = params => {
  return http.get(urls.GetConsultAssessView, params);
};
// 保存会诊评价
export const SaveConsultDetail = params => {
  return http.post(urls.SaveConsultDetail, params);
};
// 获取主记录
export const GetConsultRecord = params => {
  return http.get(urls.GetConsultRecord, params);
};
