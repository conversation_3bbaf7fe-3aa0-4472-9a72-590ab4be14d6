/*
 * FilePath     : \src\api\PatientSedation.js
 * Author       : 曹恩
 * Date         : 2023-08-28 14:25
 * LastEditors  : 曹恩
 * LastEditTime : 2023-08-28 14:29
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientSedation";

const urls = {
  GetSedationAssessView: baseUrl + "/GetSedationAssessView",
  GetSedationTableView: baseUrl + "/GetSedationTableView",
  SaveSedationCare: baseUrl + "/SaveSedationCare",
  DeleteSedationCare: baseUrl + "/DeleteSedationCare"
};

//获取评估模板
export const GetSedationAssessView = params => {
  return http.get(urls.GetSedationAssessView, params);
};
//获取镇静评估记录
export const GetSedationTableView = params => {
  return http.get(urls.GetSedationTableView, params);
};
//保存镇静评估
export const SaveSedationCare = params => {
  return http.post(urls.SaveSedationCare, params);
};
//删除镇静评估
export const DeleteSedationCare = params => {
  return http.post(urls.DeleteSedationCare, qs.stringify(params));
};
