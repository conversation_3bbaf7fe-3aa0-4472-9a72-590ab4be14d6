/*
 * FilePath     : \ccc.web\src\api\Handover\HandoverOperation.js
 * Author       : 郭鹏超
 * Date         : 2023-02-24 16:00
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-04-07 11:38
 * Description  :手术交班API
 * CodeIterationRecord:
 */
import http from "../../utils/ajax";
const baseUrl = "/HandoverOperation";
const urls = {
  GetHandOverOperationAssessTemplate:
    baseUrl + "/GetHandOverOperationAssessTemplate",
  GetHandoverOperationSBAR: baseUrl + "/GetHandoverOperationSBAR",
  SaveOperationHandoverSBAR: baseUrl + "/SaveOperationHandoverSBAR",
  GetHandoverOperationList: baseUrl + "/GetHandoverOperationList",
  DeleteHandoverOperation: baseUrl + "/DeleteHandoverOperation",
  SaveOperationHandoverAssess: baseUrl + "/SaveOperationHandoverAssess",
  GetTransOutStationID: baseUrl + "/GetTransOutStationID"
};
//获取交班类型
export const GetHandOverOperationAssessTemplate = params => {
  return http.get(urls.GetHandOverOperationAssessTemplate, params);
};
//获取交班页签
export const GetHandoverOperationSBAR = params => {
  return http.get(urls.GetHandoverOperationSBAR, params);
};
//sbar保存
export const SaveOperationHandoverSBAR = params => {
  return http.post(urls.SaveOperationHandoverSBAR, params);
};
//获取手术列表
export const GetHandoverOperationList = params => {
  return http.get(urls.GetHandoverOperationList, params);
};
//删除交接记录
export const DeleteHandoverOperation = params => {
  return http.get(urls.DeleteHandoverOperation, params);
};
//手术评估保存
export const SaveOperationHandoverAssess = params => {
  return http.post(urls.SaveOperationHandoverAssess, params);
};
//获取转出病区ID
export const GetTransOutStationID = params => {
  return http.get(urls.GetTransOutStationID, params);
};
