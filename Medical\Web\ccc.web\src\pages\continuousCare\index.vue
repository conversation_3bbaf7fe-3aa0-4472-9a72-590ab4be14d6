<!--
 * FilePath     : \src\pages\continuousCare\index.vue
 * Author       : 曹恩
 * Date         : 2020-10-20 20:08
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-16 16:11
 * Description  : 延续护理 出院病人查询页面
-->
<template>
  <base-layout class="continuous-care" header-height="auto">
    <div class="query-header" slot="header">
      <span>起始日期:</span>
      <el-date-picker
        v-model="query.startDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="开始日期"
      ></el-date-picker>
      <span>结束日期:</span>
      <el-date-picker
        v-model="query.endDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="结束日期"
      ></el-date-picker>
      <span>病区:</span>
      <station-selector v-model="query.stationID" hospitalFlag label="" width="140px"></station-selector>
      <span>病人类别:</span>
      <el-select v-model="query.typeValue" style="width: 120px" placeholder="请选择">
        <el-option
          v-for="item in optionsOfDistinction"
          :key="item.id"
          :label="item.description"
          :value="item.typeValue"
        ></el-option>
      </el-select>
      <el-button class="query-button" icon="iconfont icon-search" @click="selectDischargePatient()">查询</el-button>
      <div class="button-line">
        <el-button class="print-button" @click="exportToExcel" v-if="allTypeFlag">
          <i class="iconfont icon-arrow-download"></i>
          <span>导出EXCEL</span>
        </el-button>
      </div>
    </div>
    <el-table v-loading="loading" element-loading-text="加载中……" :data="dischargePatients" border stripe height="100%">
      <el-table-column prop="patientName" label="姓名" align="center" min-width="100"></el-table-column>
      <el-table-column prop="bedNumber" label="床号" align="center" min-width="80" sortable></el-table-column>
      <el-table-column prop="localCaseNumber" label="住院号" align="center" width="100"></el-table-column>
      <el-table-column prop="gender" label="性别" align="center" width="60"></el-table-column>
      <el-table-column prop="age" label="年龄" align="center" width="80" sortable></el-table-column>
      <el-table-column prop="dateOfBirth" label="出生日期" align="center" width="120" sortable></el-table-column>
      <el-table-column prop="admissionDate" label="入院日期" align="center" width="120" sortable></el-table-column>
      <el-table-column prop="dischargeDate" label="出院日期" align="center" width="120" sortable></el-table-column>
      <el-table-column prop="inHospitalDays" label="住院天数" align="center" width="110" sortable></el-table-column>
      <el-table-column prop="departmentName" label="出院科室" align="left" min-width="150" sortable></el-table-column>
      <el-table-column prop="patientDiagnosis" label="离院诊断" align="left" min-width="150"></el-table-column>
      <el-table-column
        prop="continuousCareTypeName"
        label="延续护理类别"
        align="center"
        width="150"
        v-if="allTypeFlag"
      ></el-table-column>
      <el-table-column
        prop="patientTubeInfo"
        label="部位-导管"
        align="left"
        min-width="150"
        v-if="tubeFlag"
      ></el-table-column>
      <el-table-column prop="edit" align="center" fixed="right" label="操作" width="120">
        <template slot-scope="scope">
          <el-tooltip content="离院小结" v-if="allTypeFlag">
            <i class="iconfont icon-edit" @click="getPDF(scope.row)"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-tooltip content="护理详情">
            <i class="iconfont icon-nursing-record" @click="openDialog('recordEdit', scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="病案查询">
            <i class="iconfont icon-preview" @click="openDialog('emrEdit', scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="出院小结">
            <i class="iconfont icon-nursing-process-records" @click="openDialog('handoverSBARLook', scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="离院小结" :close-on-click-modal="false" :visible.sync="PDFVisiable" fullscreen>
      <iframe :src="ftpPath" width="100%" height="100%" frameborder="1" scrolling="auto" />
    </el-dialog>
    <el-dialog :title="this.openDialogTitle" :visible.sync="dialogVisible" @close="closeDialog" fullscreen>
      <iframe v-if="!showDischargeHandoverSBAR" :src="linkToAddress" width="100%" height="100%" scrolling="no" />
      <handover-sbar v-if="showDischargeHandoverSBAR" usingType="component" defaultHandoverType="DischargeAssess"></handover-sbar>
    </el-dialog>
  </base-layout>
</template>

<script>
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
import handoverSbar from '@/pages/handover/handoverSBAR'
import { GetDischargePatientsByQuery, GetDischargePatientsEmrFile } from "@/api/ContinuousCare";
import { GetPatientDocument } from "@/api/Document";
import { export_json_to_excel } from "@/vendor/Export2Excel.js";
import { GetClinicalSettingInfo } from "@/api/Assess";
import { GetClinicSettingByTypeCode } from "@/api/Setting";
import stationSelector from "@/components/selector/stationSelector";
export default {
  components: {
    baseLayout,
    stationSelector,
    handoverSbar
  },
  data() {
    return {
      //跳转的地址
      linkToAddress: undefined,
      //操作框开关
      dialogVisible: false,
      //出院病人
      dischargePatients: [],
      query: {
        //开始日期
        startDate: this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -7, "yyyy-MM-dd"),
        //结束日期
        endDate: this._datetimeUtil.getNowDate(),
        //病区ID
        stationID: undefined,
        //病种类别
        typeValue: "",
      },
      optionsOfDistinction: [],
      //遮罩
      loading: false,
      //离院小结pdf弹窗
      PDFVisiable: false,
      //离院小结pdf文件
      ftpPath: "",
      fileID: "",
      //查询所有类别类别出院病人时不显示
      allTypeFlag: true,
      //显示导管
      tubeFlag: false,
      openDialogTitle: undefined,
      showDischargeHandoverSBAR: false,
      queryField: [],
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  watch: {
    query: {
      handler(newV) {
        if (newV.typeValue != "Discharge") {
          this.allTypeFlag = true;
        }
        if (newV.typeValue == "Discharge") {
          this.allTypeFlag = false;
        }
        if (newV.typeValue != "Tube") {
          this.tubeFlag = false;
        }
        if (newV.typeValue == "Tube") {
          this.tubeFlag = true;
        }
      },
      deep: true,
    },
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.query.stationID = this.user.stationID;
      await this.getClinicalSettingInfo();
      await this.getPatientQueryField();
      //默认页面重新渲染时显示病人类别-所有病人
      if (this.optionsOfDistinction.length > 0) {
        this.query.typeValue = this.optionsOfDistinction[0].typeValue;
      }
    },

    async getClinicalSettingInfo() {
      //从配置获取延续护理类别
      let params = {
        SettingTypeCode: "ContinuousCare_Admission",
      };
      await GetClinicalSettingInfo(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.optionsOfDistinction = res.data;
        }
      });
    },

    async selectDischargePatient() {
      if (this.query.endDate > this._datetimeUtil.addDate(this.query.startDate, 30)) {
        this._showTip("warning", "查询日期间隔不能大于30天");
        return;
      }
      if (this.query.endDate > this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd")) {
        this._showTip("warning", "结束时间不得大于当日");
        return;
      }
      if (this.query.startDate > this.query.endDate) {
        this._showTip("warning", "开始时间不得大于结束时间");
        return;
      }
      this.loading = true;
      await GetDischargePatientsByQuery(this.query).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          this.dischargePatients = response.data;
          this.fixDateTime(this.dischargePatients);
        }
      });
    },

    //跳转离院小结PDF
    async getPDF(row) {
      this.loading = true;
      let params = {
        inpatientID: row.inpatientID,
      };
      //获取PDF fileId
      await GetDischargePatientsEmrFile(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.fileID = response.data[0].fileId;
        } else {
          this._showTip("warning", "获取该病人文件失败！");
        }
      });
      //获取离院小结PDF
      let param = {
        fileID: this.fileID,
      };
      await GetPatientDocument(param).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.ftpPath = result.data.fileUrl;
          this.PDFVisiable = true;
        } else {
          this._showTip("warning", "获取文件失败！");
        }
      });
    },
    //导出Excel表格通用
    exportToExcel() {
      let mainData = this.dischargePatients;
      const mainHeader = [
        "姓名",
        "延续护理类别",
        "出院科别",
        "院内住院号",
        "床号",
        "性别",
        "年龄",
        "出生日期",
        "有效证件号",
        "入院日期",
        "出院日期",
        "在院天数",
        "总费用",
        "联系电话",
        "乡镇邮编",
        "社区",
        "家庭住址",
        "部位-导管",
      ];
      const mainFilterVal = [
        "patientName",
        "continuousCareTypeName",
        "departmentName",
        "localCaseNumber",
        "bedNumber",
        "gender",
        "age",
        "dateOfBirth",
        "identityID",
        "admissionDate",
        "dischargeDate",
        "inHospitalDays",
        "totalCharges",
        "phoneNumber",
        "postalCode",
        "community",
        "homeAddress",
        "patientTubeInfo",
      ];
      const dataMain = this.formatJson(mainFilterVal, mainData);
      export_json_to_excel(
        mainHeader,
        dataMain,
        this._datetimeUtil.getNowDate("yyyyMMdd") + "延续护理病人清单",
        undefined,
        false
      );
    },

    formatJson(filterVal, jsonData) {
      return jsonData.map((jsonDataItem) =>
        filterVal.map((filterValItem) => {
          if (filterValItem === "admissionDate" || filterValItem === "dischargeDate") {
            var date = new Date(jsonDataItem[filterValItem]);
            return this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
          } else {
            return jsonDataItem[filterValItem];
          }
        })
      );
    },
    fixDateTime(value) {
      if (value.length == 0) {
        return;
      }
      value.forEach((item) => {
        item.admissionDate = this._datetimeUtil.formatDate(item.admissionDate, "yyyy-MM-dd");
        // !item.dateOfBirth || 用于解决数据为null formatDate格式化时间时报错
        item.dateOfBirth = !item.dateOfBirth || this._datetimeUtil.formatDate(item.dateOfBirth, "yyyy-MM-dd");
        item.dischargeDate = this._datetimeUtil.formatDate(item.dischargeDate, "yyyy-MM-dd");
      });
    },
    /**
     * description: 出院患者跳转病案查询和记录编辑方法
     * param {*} flag
     * param {*} row
     * return {*}
     */
    openDialog(flag, row) {
      if (flag == "recordEdit") {
        this.openDialogTitle = "护理详情";
        let attribute = this.queryField[0].typeValue.charAt(0).toLowerCase() + this.queryField[0].typeValue.slice(1)
        this.linkToAddress = `/recordSupplement?isDialog=true&${attribute}=${row[attribute]}`;
      }
      if (flag == "emrEdit") {
        this.openDialogTitle = "病案查询";
        this.linkToAddress = `/document?isDialog=true${row.caseNumber ? `&caseNumber=${row.caseNumber}` : ""}`;
      }
      if (flag == "handoverSBARLook") {
        this.openDialogTitle = "出院小结";
        this.showDischargeHandoverSBAR = true;
      }
      this.dialogVisible = true;
    },
    /**
     * @description: 关闭弹窗
     * @return 
     */
    closeDialog() {
      this.linkToAddress = undefined;
      this.openDialogTitle = "";
      this.showDischargeHandoverSBAR = false;
    },
        /**
     * 获取查询配置
     */
    async getPatientQueryField() {
      let params = {
        settingTypeCode: "PatientQueryField",
      };
      await GetClinicSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.queryField = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.continuous-care {
  .query-header {
    min-width: 940px;
  }
  .button-line {
    float: right;
  }
  .icon-nursing-record {
    color: #409eff;
  }
  iframe {
    height: 99%;
    width: 100%;
    border: 0;
    overflow: hidden;
  }
}
</style>