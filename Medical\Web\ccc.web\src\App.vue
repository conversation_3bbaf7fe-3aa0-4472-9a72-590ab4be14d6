<!--
 * FilePath     : \src\App.vue
 * Author       : 苏军志
 * Date         : 2020-05-05 00:06
 * LastEditors  : 苏军志
 * LastEditTime : 2022-10-22 16:27
 * Description  : 
 -->
<template>
  <div id="app" :class="language">
    <router-view />
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  name: "App",
  computed: {
    ...mapGetters({
      language: "getLanguage",
    }),
  },
  mounted() {
    //禁止ctrl 加 ‘+’ ‘-’ 缩放页面
    document.addEventListener(
      "keydown",
      function (event) {
        if (
          (event.ctrlKey === true || event.metaKey === true) &&
          (event.code === 61 ||
            event.code === 107 ||
            event.code === 173 ||
            event.code === 109 ||
            event.code === 187 ||
            event.code === 189)
        ) {
          event.preventDefault();
        }
      },
      false
    );
    // 禁止ctrl+鼠标滚轮 缩放页面
    window.addEventListener(
      "mousewheel",
      function (event) {
        if (event.ctrlKey === true || event.metaKey) {
          event.preventDefault();
        }
      },
      { passive: false }
    );
  },
};
</script>

<style>
html,
body,
#app {
  height: 100%;
  width: 100%;
  min-width: 750px;
  margin: 0;
  padding: 0;
  user-select: none;
  overflow-y: hidden;
  overflow-x: auto;
  background-color: #f3f3f3;
  /* 设置字体 */
  font-family: "Microsoft Yahei", "Avenir", Helvetica, Arial, sans-serif, "Times New Roman";
  /* 设置文字抗锯齿 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* 所有内容按照单词换行，解决英文版换行问题 */
* {
  word-break: break-word !important;
}
/* 日期时间控件弹窗不换行 */
.el-date-picker * {
  word-break: keep-all !important;
}
</style>
