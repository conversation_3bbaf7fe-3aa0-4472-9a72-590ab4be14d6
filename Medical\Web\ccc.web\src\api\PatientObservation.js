/*
 * FilePath     : \src\api\PatientObservation.js
 * Author       : 李正元
 * Date         : 2022-01-11 11:39
 * LastEditors  : 来江禹
 * LastEditTime : 2023-03-28 09:39
 * Description  : 观察措施API
 */
import http from "../utils/ajax";
const baseUrl = "/PatientObservation";
import qs from "qs";

export const urls = {
  GetAssessView: baseUrl + "/GetAssessView",
  CreatePatientObservation: baseUrl + "/CreatePatientObservation",
  GetPatientObservationList: baseUrl + "/GetPatientObservationList",
  ReadPatientObservation: baseUrl + "/ReadPatientObservation",
  UpdatePatientObservation: baseUrl + "/UpdatePatientObservation",
  DeletePatientObservation: baseUrl + "/DeletePatientObservation",
  CreateObserveEvalution: baseUrl + "/CreateObserveEvalution",
  UpdateObserveEvalution: baseUrl + "/UpdateObserveEvalution",
  DeleteDataByObserveSourceID: baseUrl + "/DeleteDataByObserveSourceID",
  GetPatientObservationByScheduleMainID:
    baseUrl + "/GetPatientObservationByScheduleMainID",
  GetCriticalSummaryData: baseUrl + "/GetCriticalSummaryData",
};
//自动汇总特定时间段内的病情观察模板
export const GetCriticalSummaryData = params => {
  return http.get(urls.GetCriticalSummaryData, params);
};
// 取得病人病情观察记录清单
export const GetPatientObservationList = params => {
  return http.get(urls.GetPatientObservationList, params);
};
// 取得观察措施模版
export const GetAssessView = params => {
  return http.get(urls.GetAssessView, params);
};
// 新增观察措施模版
export const CreatePatientObservation = params => {
  return http.post(urls.CreatePatientObservation, params);
};
// 读取观察措施模版记录
export const ReadPatientObservation = params => {
  return http.get(urls.ReadPatientObservation, params);
};
// 删除观察措施模版记录
export const DeletePatientObservation = params => {
  return http.post(urls.DeletePatientObservation, params);
};
// 更新观察措施模版记录
export const UpdatePatientObservation = params => {
  return http.post(urls.UpdatePatientObservation, params);
};
// 新增观察评价
export const CreateObserveEvalution = params => {
  return http.post(urls.CreateObserveEvalution, params);
};
// 更新观察评价
export const UpdateObserveEvalution = params => {
  return http.post(urls.UpdateObserveEvalution, params);
};
// 根据sourceID删除BR及专项评估数据
export const DeleteDataByObserveSourceID = params => {
  return http.post(urls.DeleteDataByObserveSourceID, qs.stringify(params));
};
GetPatientObservationByScheduleMainID;
// 根据排程ID获取观察措施
export const GetPatientObservationByScheduleMainID = params => {
  return http.get(urls.GetPatientObservationByScheduleMainID, params);
};
