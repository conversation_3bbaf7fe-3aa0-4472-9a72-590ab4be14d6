<!--
 * FilePath     : /src/autoPages/mainLayout.vue
 * Author       : 苏军志
 * Date         : 2020-07-04 17:28
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-05 15:18
 * Description  : 全智能护理管理系统前端主框架页面
 * CodeIterationRecord: 2023-02-28 3277-作为IT人员,我需要优化迭代系统主页刷新按钮功能,以利于后续增添其它页面同步功能 -zxz
-->
<template>
  <el-container class="main-layout" :style="{ '--menu-font-color': isEducation ? '#00ffff' : '#ffffff' }">
    <el-aside class="left-menu" v-if="!isDialog">
      <div class="logo" @click="toggleMenu()">
        <img v-if="hospitalInfo" :src="hospitalInfo.logoImage" />
      </div>
      <!-- 导航菜单 -->
      <el-menu
        :default-active="activeRouter"
        :background-color="isEducation ? '#323544' : '#1cc6a3'"
        text-color="#fff"
        :collapse="isCollapse"
        router
        unique-opened
      >
        <nav-menu
          :navMenus="menus"
          menuID="menuID"
          parentID="parentID"
          menuName="menuName"
          router="router"
          iconName="iconName"
          children="children"
        ></nav-menu>
      </el-menu>
    </el-aside>
    <el-container class="main">
      <!-- isDialog=true为dialog模式，不显示显示菜单和顶部导航；showSystemHeader是否显示顶部导航 -->
      <el-header class="system-header" v-if="!isDialog || showSystemHeader">
        <div class="left-wrap">
          <div
            :class="['hospital-name', `hospital${hospitalInfo.hospitalID}`, { 'is-education': isEducation }]"
            v-html="hospitalName"
            @click="goHome"
          ></div>
          <div class="system-version">
            {{ $t("label.systemName") }}
            <span class="version" @click="showVersion">
              {{ systemVersion }}
            </span>
          </div>
        </div>
        <div :class="['right-wrap', { 'is-pad': isPad }]">
          <el-popover
            class="popover-btn"
            popper-class="popover-btn-content"
            :trigger="trigger"
            v-model="popoverVisible"
          >
            <i slot="reference" class="iconfont icon-list-icon" />
            <div class="popover-items">
              <div class="item" v-if="showAIWidgetIconFlag" @click="openAiWidget()">
                <i class="iconfont icon-ai-widget" />
                <span>{{ mainTexts.aiWidget }}</span>
              </div>
              <div class="item user" @click="popoverVisible = !popoverVisible">
                <i class="iconfont icon-user" />
                <div class="user-name" v-if="user">{{ user.userName }}</div>
              </div>
              <div class="item" @click="goHome">
                <i class="iconfont icon-home" />
                <span>{{ mainTexts.home }}</span>
              </div>
              <div class="item" @click="goStationJobTip" v-if="showNursingAgentIconFlag">
                <div class="station-tip">
                  <el-badge v-if="attendanceStationJobList && attendanceStationJobList.length" is-dot>
                    <i class="iconfont icon-leftfont-15" />
                  </el-badge>
                  <i v-else class="iconfont icon-leftfont-15" />
                  <span>{{ mainTexts.stationJobTip }}</span>
                </div>
              </div>
              <div class="item" @click="refresh()">
                <i class="iconfont icon-refresh" />
                <span>{{ mainTexts.refresh }}</span>
              </div>
              <div class="item" @click="showMsg">
                <div class="message">
                  <el-badge v-if="mqMessageCount > 0" :value="mqMessageCount" :max="99">
                    <i class="iconfont icon-message" />
                  </el-badge>
                  <i v-else class="iconfont icon-message" />
                  <span>{{ mainTexts.message }}</span>
                </div>
              </div>
              <div class="item" @click="loginOut" v-if="!isDialog">
                <i class="iconfont icon-exit" />
                <span>{{ mainTexts.signOut }}</span>
              </div>
            </div>
          </el-popover>
          <div class="btn">
            <el-tooltip v-if="showAIWidgetIconFlag" :content="mainTexts.aiWidget">
              <i class="iconfont icon-ai-widget" @click="openAiWidget()" />
            </el-tooltip>
            <el-tooltip :content="mainTexts.home">
              <i class="iconfont icon-home" @click="goHome" />
            </el-tooltip>
            <el-tooltip v-if="showNursingAgentIconFlag" :content="mainTexts.stationJobTip">
              <div class="station-tip" @click="goStationJobTip">
                <el-badge v-if="attendanceStationJobList && attendanceStationJobList.length" is-dot>
                  <i class="iconfont icon-leftfont-15" />
                </el-badge>
                <i v-else class="iconfont icon-leftfont-15" />
              </div>
            </el-tooltip>
            <el-tooltip :content="mainTexts.refresh">
              <i class="iconfont icon-refresh" @click="refresh()" />
            </el-tooltip>
            <el-tooltip :content="mainTexts.message">
              <div class="message" @click="showMsg">
                <el-badge v-if="mqMessageCount > 0" :value="mqMessageCount" :max="99">
                  <i class="iconfont icon-message" />
                </el-badge>
                <i v-else class="iconfont icon-message" />
              </div>
            </el-tooltip>
            <el-tooltip :content="mainTexts.signOut">
              <i class="iconfont icon-exit" @click="loginOut" />
            </el-tooltip>
          </div>
          <div class="user-info">
            <i class="iconfont icon-user" />
            <span class="text" v-if="user">{{ user.userName }}</span>
          </div>
          <station-selector
            v-model="currentStationID"
            :userID="user.userID"
            label=""
            onlyOneDataStyle="Text"
            width="180"
            class="select-station"
            @select-item="changeStation"
          ></station-selector>
        </div>
        <el-tooltip :content="isFullscreen ? '退出全屏' : '单击系统全屏，右击页面全屏'">
          <i
            :class="`toggle-fullscreen iconfont icon-${isFullscreen ? 'exit-fullscreen' : 'fullscreen'}`"
            @click="toggleScreenfull($event, false)"
            @contextmenu="toggleScreenfull($event, true)"
          ></i>
        </el-tooltip>
      </el-header>
      <el-main :class="['router-view-main', { 'is-dialog': isDialog && !showSystemHeader }]">
        <el-breadcrumb
          separator=">"
          class="bread-router"
          v-if="!isDialog && $route.path != '/patientList' && $route.path != '/stationToJobTip'"
        >
          <el-breadcrumb-item :to="{ path: '/patientList' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item v-for="(item, index) in breads" :key="index">
            <span v-if="index == breads.length - 1">{{ item.name }}</span>
            <router-link v-else :to="{ name: item.path }" replace>{{ item.name }}</router-link>
          </el-breadcrumb-item>
        </el-breadcrumb>
        <div
          :class="[
            'router-view-wrap screenfull-dom',
            { 'read-only': readOnly },
            { 'is-dialog': isDialog || $route.path == '/patientList' },
          ]"
        >
          <!-- 不需要缓存的路由加载位置 -->
          <router-view v-if="!$route.meta.keepAlive && isRouterAlive" ref="childPage"></router-view>
          <keep-alive>
            <!-- 需要缓存的路由加载位置 -->
            <router-view v-if="$route.meta.keepAlive && isRouterAlive" ref="childPage"></router-view>
          </keep-alive>
        </div>
      </el-main>
    </el-container>
    <el-dialog
      :title="mainTexts.msgTitle"
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="showMessage"
      custom-class="message-dialog"
    >
      <base-layout class="tube">
        <div slot="header">
          {{ mainTexts.msgRead }}
          <el-switch v-model="isRead"></el-switch>
          <span class="label">{{ label.date }}</span>
          <el-date-picker
            :disabled="!isRead"
            v-model="startDate"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="date"
            class="select-date"
            :placeholder="placeholder.date"
          ></el-date-picker>
          -
          <el-date-picker
            :disabled="!isRead"
            v-model="endDate"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="date"
            class="select-date"
            :placeholder="placeholder.date"
          ></el-date-picker>
          <el-input
            class="select-input"
            v-model="filterKeywords"
            @keyup.enter.native="searchMessage"
            :placeholder="mainTexts.msgFilterKeywords"
            @clear="searchMessage"
            clearable
          >
            <i slot="append" class="iconfont icon-search" @click="searchMessage"></i>
          </el-input>
        </div>

        <el-table
          :data="messageList"
          height="100%"
          @selection-change="selectionMessage"
          v-loading="messageLoading"
          :element-loading-text="messageLoadingText"
          tooltip-effect="dark message-tool-tips"
          border
          stripe
        >
          <el-table-column type="selection" v-if="!isRead" :width="convertPX(30)"></el-table-column>
          <el-table-column
            :label="mainTexts.index"
            type="index"
            :width="convertPX(65)"
            align="center"
          ></el-table-column>
          <el-table-column prop="messageTitle" :label="mainTexts.theme" :width="convertPX(150)"></el-table-column>
          <el-table-column :label="mainTexts.content" show-overflow-tooltip>
            <template slot-scope="message">
              <span v-html="message.row.messageContent"></span>
            </template>
          </el-table-column>
          <el-table-column prop="addDateTime" :label="mainTexts.time" :width="convertPX(160)">
            <template slot-scope="message">
              <span v-formatTime="{ value: message.row.addDateTime, type: 'date' }"></span>
            </template>
          </el-table-column>
        </el-table>
      </base-layout>
      <div slot="footer">
        <el-button @click="showMessage = false">{{ button.cancel }}</el-button>
        <el-button type="primary" @click="updateMessage">{{ button.confirm }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="版本信息"
      :visible.sync="showVersionFlag"
      custom-class="no-footer version-dialog"
    >
      <div class="version-wrap" v-if="versionInfo">
        <div class="version-info">
          <span class="label">版本号：</span>
          {{ versionInfo.version }}
        </div>
        <div class="version-info">
          <span class="label">发布时间：</span>
          <span v-formatTime="{ value: versionInfo.updateTime, type: 'dateTime' }"></span>
        </div>
        <div class="version-contents">
          <span class="label">发布内容：</span>
          <div class="contents" v-if="versionInfo.addFunction">
            <span class="label add-function">新增功能：</span>
            <div class="content" v-html="versionInfo.addFunction"></div>
          </div>
          <div class="contents" v-if="versionInfo.bugFix">
            <span class="label bug-fix">问题修复</span>
            <div class="content" v-html="versionInfo.bugFix"></div>
          </div>
          <div class="contents" v-if="versionInfo.systemOptimization">
            <span class="label system-optimization">系统优化：</span>
            <div class="content" v-html="versionInfo.systemOptimization"></div>
          </div>
        </div>
        <div class="file-list" v-if="versionInfo.fileList && versionInfo.fileList.length > 0">
          <span class="label">附件：</span>
          <span
            class="file-name"
            v-for="(file, index) in versionInfo.fileList"
            :key="index"
            @click="versionFilePreview(index, versionInfo.fileList, versionInfo.version)"
          >
            {{ index + 1 + "、" + file.name }}
          </span>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="filePreviewDialogTitle"
      :visible.sync="showFilePreview"
      custom-class="no-footer"
    >
      <file-preview
        v-if="showFilePreview"
        :defaultFileIndex="defaultPreviewFileIndex"
        :datas="filePreviewData"
      ></file-preview>
    </el-dialog>
    <el-dialog
      title="密码校验"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="lockFlag"
      custom-class="validation-dialog"
      :close-on-press-escape="false"
    >
      <div class="lock-label">
        请输入
        <spam class="lock-user">{{ this.user.userName }}</spam>
        账户的密码
      </div>
      <el-input
        class="lock-password"
        placeholder="请输入密码"
        v-model="password"
        @focus="$refs.passwordInp.type = 'password'"
        maxlength="20"
        ref="passwordInp"
        @keyup.enter.native="validation"
      >
        <i slot="prepend" class="iconfont icon-login-password"></i>
      </el-input>
      <div slot="footer">
        <el-button type="primary" @click="validation()">{{ "确定" }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="AI"
      :close-on-click-modal="true"
      :show-close="true"
      :visible.sync="aiIframeFlag"
      custom-class="ai-widget-dialog"
      :close-on-press-escape="false"
    >
      <iframe
        :src="aISrc"
        style="width: 100%; height: 98%; min-height: 200px"
        frameborder="0"
        allow="microphone"
      ></iframe>
    </el-dialog>
  </el-container>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import NavMenu from "@/components/NavMenu";
import filePreview from "@/components/FilePreview";
import stationSelector from "@/components/selector/stationSelector";
import { GetMenuList } from "@/api/Menu";
import { getMQSetting, getEducationEdition } from "@/utils/setting";
import { UpdateSessionStationID, GetSession, userLogin } from "@/api/User";
import { GetExcharge, GetNoReadNoticeListAsync, GetMyNotice, UpdateMessages } from "@/api/Message";
import { CheckVersion, GetOneSettingByTypeAndCode } from "@/api/Setting";
import { GetLastVersionBySystemCode } from "@/api/SystemVersion";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { GetButtonAuthorityByRoles } from "@/api/Authority";
import { mapGetters } from "vuex";
import { wp } from "@/utils/web-proxy";
import { RefreshData } from "@/api/Refresh";
import getMqClient from "@/utils/mqClient";
import { GetStationToJobTipList } from "@/api/JobTip";
import screenfull from "screenfull";
import { GetConfigSettingByCode } from "@/api/AppConfigSetting";
export default {
  components: {
    NavMenu,
    baseLayout,
    filePreview,
    stationSelector,
  },
  computed: {
    ...mapGetters({
      hospitalInfo: "getHospitalInfo",
      user: "getUser",
      mqMessageCount: "getMQMessageCount",
      readOnly: "getReadOnly",
      loginParams: "getLoginParams",
      externalParams: "getExternalParams",
    }),
    placeholder() {
      return this.$t("placeholder");
    },
    label() {
      return this.$t("label");
    },
    button() {
      return this.$t("button");
    },
    loadingText() {
      return this.$t("loadingText");
    },
    mainTexts() {
      return this.$t("mainLayout");
    },
    activeRouter() {
      if (this.$route.meta && this.$route.meta.parentPath) {
        return this.$route.meta.parentPath;
      }
      return this.$route.fullPath;
    },
    isEducation() {
      return getEducationEdition();
    },
  },
  watch: {
    $route(to, from) {
      // 处理面包屑导航
      this.getBreadcrumb();
    },
    isRead(newVale) {
      this.searchMessage();
    },
  },
  data() {
    return {
      // 判断是否为dialog模式,dialog模式只显示router-view部分
      isDialog: false,
      showSystemHeader: true,
      // 路由是否存活，用于刷新页面
      isRouterAlive: true,
      // 医院名称
      hospitalName: "OOOO医院",
      // 系统版本号
      systemVersion: "2.0.0",
      // 菜单是否展开
      isCollapse: true,
      // 菜单列表
      menus: [],
      // 当前病区
      currentStationID: undefined,
      // 面包屑导航列表
      breads: [],
      // mq消息配置
      mqSetting: undefined,
      // 是否显示消息对话框
      showMessage: false,
      //消息加载
      messageLoading: false,
      // 消息加载提示内容
      messageLoadingText: "",
      // 消息列表
      messageList: [],
      // 是否已读
      isRead: false,
      // 查询消息开始时间
      startDate: undefined,
      // 查询消息结束始时间
      endDate: undefined,
      // 查询消息关键字
      filterKeywords: "",
      // 要更新的消息列表
      updateMessageList: [],
      // 屏幕窄时的浮动按钮列表是否显示
      popoverVisible: false,
      // 浮动按钮列表 触发方式
      trigger: "click",
      isPad: false,
      systemNotify: undefined,
      // 版本信息
      showVersionFlag: false,
      // 版本信息
      versionInfo: undefined,
      showFilePreview: false,
      filePreviewDialogTitle: "",
      filePreviewData: [],
      defaultPreviewFileIndex: false,
      //点击刷新按钮是否重新同步病人信息
      refreshPatientFlag: false,
      //锁定屏幕弹窗开关
      lockFlag: false,
      //弹窗账户密码
      userName: "",
      password: "",
      //密码加密配置开关
      encryptFlag: false,
      //锁定账户配置开关
      validationFlag: false,
      //前端无操作锁定账户时长
      validationMinutes: 10,
      client: undefined,
      //护理待办列表图标开关
      showNursingAgentIconFlag: false,
      stationJobList: [],
      attendanceStationJobList: [],
      // 全屏状态
      isFullscreen: false,
      showAIWidgetIconFlag: false,
      aISrc: "",
      aiIframeFlag: false,
    };
  },
  async created() {
    // 获取按钮级缓存
    this.getButtonAuthorityByRoles();
    //获取病人头组件床位宽度配置
    this.getBedNumberWidthSetting();
    if (this._common.isPC()) {
      this.isPad = false;
      this.trigger = "hover";
      // 检查打印插件是否需要更新
      this.checkWPVersion();
    } else {
      this.isPad = true;
      this.trigger = "click";
    }
    if (this.hospitalInfo) {
      this.hospitalName = this.hospitalInfo.hospitalName;
      this.systemVersion = this.hospitalInfo.systemVersion;
      if (this.hospitalName.includes(" ")) {
        this.hospitalName = this.hospitalName.replace(" ", "<br>");
      }
    }

    this.currentStationID = this.user.stationID;
    this.mqSetting = getMQSetting();
    if (this.mqSetting.isOpen) {
      this.client = getMqClient();
      // 订阅病人转病区消息
      this.client.subscribe({
        exchangeName: "PatientTransfer",
        callback: (inpatientID) => {
          if (!inpatientID) {
            return;
          }
          const cache = this._common.session("stationList");
          if (cache[`station_${inpatientID}`]) {
            // 清除此患者的病区缓存，注意如果停留在IO页面需要点击一下页内刷新按钮
            this.$store.commit("session/setStationList", `station_${inpatientID}`);
          }
        }
      });
      // 订阅收到的MQ消息，旧版路由键规则
      this.createMessageBox();
    }
    if (this.$route.query.isDialog) {
      this.isDialog = this.$route.query.isDialog;
      if (this.$route.query.showSystemHeader && this.$route.query.showSystemHeader == "true") {
        this.showSystemHeader = this.$route.query.showSystemHeader;
      } else {
        this.showSystemHeader = false;
      }
    } else {
      this.isDialog = false;
      this.showSystemHeader = true;
      // 获取菜单列表
      await this.getMenuList();
      if (this.mqSetting.isOpen) {
        await this.getMessageCount();
      }
      //获取护理待办列表图标显示开关
      await this.getShowNursingAgentIconSwitch();
      if (this.showNursingAgentIconFlag) {
        this.stationJobTipWork();
      }
    }
    await this.getValidationSettingSwitch();
    await this.lockSystemValidation();
    //获取前端无操作定时器
    await this.getValidationMinutes();
    await this.getChatWidgetSetting();
    //获取专项抽屉高度遮挡主记录开关配置
    this.getDrawerHeightSettingSwitch();
  },
  mounted() {
    if (screenfull.enabled) {
      // 组件挂载时，绑定事件
      screenfull.on("change", this.screenfullChange);
    }
  },
  destroyed() {
    if (screenfull.enabled) {
      // 组件卸载时，解绑事件
      screenfull.off("change", this.screenfullChange);
    }
    if (!this.isDialog && this.client) {
      this.client.disconnect();
    }
    if (this.systemNotify) {
      this.systemNotify.close();
      this.systemNotify = undefined;
    }
    this.stopWorker();
  },
  provide() {
    return {
      toggleScreenfull: this.toggleScreenfull,
    };
  },
  methods: {
    /**
     * @description: 全屏变化
     */
    screenfullChange() {
      this.isFullscreen = screenfull.isFullscreen;
    },
    /**
     * @description: 全屏/退出全屏
     * @param event 事件
     * @param screenfullDomFlag 全屏标记
     * @param screenfullDom 全屏dom
     * @return
     */
    toggleScreenfull(event, screenfullDomFlag, screenfullDom = "screenfull-dom") {
      event && event.preventDefault();
      //切换全屏
      if (!screenfull.enabled) {
        return;
      }
      let targetElement = "";
      if (screenfullDomFlag) {
        const elements = document.getElementsByClassName(screenfullDom);
        if (elements?.length) {
          // 取最后一个dom
          targetElement = elements[elements.length - 1];
        }
      }
      screenfull.toggle(targetElement);
    },
    /**
     * description: 获取定时器倒计时分钟数
     * param {*}
     * return {*}
     */
    async getValidationMinutes() {
      let param = {
        settingType: 226,
        settingCode: "LockValidationPageMinutes",
      };
      await GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response) && response.data && !isNaN(response.data.typeValue)) {
          this.validationMinutes = Number(response.data.typeValue);
        }
      });
    },
    /**
     * description: 获取病人头组件床位宽度配置
     * param {*}
     * return {*}
     */
    getBedNumberWidthSetting() {
      let param = {
        settingType: 236,
        settingCode: "BedNumberWidth",
      };
      let bedNumberWidth = 100;
      GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response) && response.data && !isNaN(response.data.typeValue)) {
          bedNumberWidth = Number(response.data.typeValue);
        }
        this.$store.commit("session/setBedNumberWidth", bedNumberWidth);
      });
    },
    /**
     * description: 获取按钮级权限
     * param {*}
     * return {*}
     */
    getButtonAuthorityByRoles() {
      GetButtonAuthorityByRoles().then((response) => {
        let buttonAuthorityList = [];
        let buttonList = [];
        let routerList = [];
        if (this._common.isSuccess(response) && response.data) {
          buttonAuthorityList = response.data.buttonAuthorityViewList;
          buttonList = response.data.buttonList;
          routerList = response.data.routerList;
        }
        // 放入session
        this._common.session("buttonAuthorityList", buttonAuthorityList);
        this._common.session("buttonList", buttonList);
        this._common.session("routerList", routerList);
      });
    },
    /**
     * description: 锁定账户开关
     * return {*}
     */
    async getValidationSettingSwitch() {
      let param = {
        SettingTypeCode: "LockValidationPage",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.validationFlag = response.data;
        }
      });
    },
    /**
     * description: 密码加密开关
     * return {*}
     */
    async getEncryptSettingSwitch() {
      let param = {
        SettingTypeCode: "LoginEncryption",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.encryptFlag = response.data;
        }
      });
    },
    /**
     * description: 验证账户密码
     * return {*}
     */
    async validation() {
      await this.getEncryptSettingSwitch();
      let pas = this.password;
      if (!pas.trim()) {
        this._showTip("warning", "密码不可为空!");
        return;
      }
      if (pas.trim() === this.loginParams.password) {
        this.password = "";
        this.lockFlag = false;
        this.lockSystemValidation();
      } else {
        this.password = "";
        this._showTip("warning", "密码错误!");
        // if (this.encryptFlag) {
        //   pas = encryption(pas.trim());
        // }
        // let params = {
        //   userId: this.user.userID.trim(),
        //   password: pas.trim(),
        //   language: this.loginParams.language,
        //   hospitalID: this.loginParams.hospitalID,
        //   clientType: this.loginParams.clientType
        // };
        // this.password = "";
        // await userLogin(params).then((result) => {
        //   if (this._common.isSuccess(result)) {
        //     this.lockFlag = false;
        //     this.lockSystemValidation();
        //   }
        // });
      }
    },
    /**
     * description:倒计时无操作时间
     * return {*}
     */
    lockSystemValidation() {
      if (!this.validationFlag) {
        this.lockFlag = false;
        return;
      }
      //定义时长十分钟
      let time = 60 * this.validationMinutes;
      const eventFun = (e) => {
        time = 60 * this.validationMinutes;
      };
      document.body.addEventListener("mousedown", eventFun);
      document.body.addEventListener("click", eventFun);
      document.body.addEventListener("keydown", eventFun);
      document.body.addEventListener("mousemove", eventFun);
      document.body.addEventListener("mousewheel", eventFun);

      // 开启多线程 在后台线程中开启定时器
      const workerScript = `
        let timer = undefined;
        // 接受主线程发送的消息
        onmessage = (event)=> {
          if(event.data == 'close'){
            // 关闭定时器
            if(timer){
              clearInterval(timer);
            }
            // 关闭自己所属线程
            self.close();
          }
        }
        // 开启定时器
        timer = setInterval(()=> {
          // 随便返回 ，这里只是为了触发主线程的onmessage
          postMessage("child worker!")
        }, 1000);
      `;
      // 从内联JavaScript创建Worker
      const workerScriptBlob = new Blob([workerScript]);
      const workerScriptBlobUrl = URL.createObjectURL(workerScriptBlob);
      // 创建新的线程前先停止旧的线程
      this.stopWorker();
      this.worker = new Worker(workerScriptBlobUrl);
      this.worker.onmessage = (event) => {
        time--;
        if (time == 0) {
          this.lockFlag = true;
          this.stopWorker();
        }
      };
    },
    /**
     * description: 停止线程
     * param {*}
     * return {*}
     */
    stopWorker() {
      if (this.worker) {
        this.worker.postMessage("close");
        this.worker.terminate();
        this.worker = undefined;
      }
    },
    // 检查打印插件是否需要更新
    checkWPVersion() {
      wp.client.version((oldVersion) => {
        if (!oldVersion) {
          console.log("此电脑没有启动打印插件，将会影响打印作业！");
          return;
        }
        // 旧版本
        oldVersion = oldVersion.replace(/\./g, "");
        // 获取最新版本号
        let params = {
          clientType: "4",
          system: "webProxy",
          versionCode: oldVersion,
        };
        CheckVersion(params).then((result) => {
          // 不需要显示异常数据，所以不使用this._common.isSuccess(result)
          if (result.code == 1 && result.data) {
            console.log("开始自动更新打印（webProxy）插件");
            wp.client.update(result.data, () => {
              console.log("插件更新完成");
            });
          }
        });
      });
    },
    /**
     * description: 显示版本信息
     * param {*}
     * return {*}
     */
    showVersion() {
      let params = {
        systemCode: "CCC",
      };
      GetLastVersionBySystemCode(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.versionInfo = result.data;
          this.showVersionFlag = true;
        }
      });
    },
    /**
     * description: 版本说明文件预览
     * param {*} index 文件序号
     * param {*} fileList 文件列表
     * param {*} version 当前选择行的版本号
     * return {*}
     */
    versionFilePreview(index, fileList, version) {
      this.filePreviewData = [];
      this.filePreviewDialogTitle = `${version}版本说明文档`;
      this.defaultPreviewFileIndex = index;
      fileList.forEach((file) => {
        this.filePreviewData.push(file.filePath);
      });
      this.showFilePreview = true;
    },
    // 获取面包屑导航数据
    getBreadcrumb() {
      this.breads = [];
      this.$route.matched.forEach((item, index) => {
        let path = this.$route.fullPath;
        if (item.meta.parentPath) {
          path = item.meta.parentPath;
        }
        let pos = path.indexOf("shortCutFlag");
        if (pos > 0) {
          path = path.substring(0, pos - 1);
        }
        if (!item.meta.isParent) {
          let name = this.findMenu(path, this.menus);
          if (name) {
            this.breads.push({
              name: name,
              path: item.path,
            });
          }
        }
      });
    },
    /**
     * description: 查找菜单
     * param {*} path
     * param {*} menus
     * return {*}
     */
    findMenu(path, menus) {
      let locatingRouterTitle = "";
      for (let i = 0; i < menus.length; i++) {
        if (menus[i].children) {
          locatingRouterTitle = this.findMenu(path, menus[i].children);
          if (locatingRouterTitle) {
            break;
          }
        }
        if (menus[i].router && menus[i].router.trim() == path.trim()) {
          locatingRouterTitle = menus[i].locatingRouterTitle;
          break;
        }
      }
      return locatingRouterTitle;
    },
    // 回到主页
    goHome() {
      if (this.popoverVisible) {
        this.popoverVisible = false;
      }
      // 如果当前页面就是主页则刷新页面，否则跳转到主页
      if (this.$route.name == "patientList") {
        this.pageRefresh();
      } else {
        this.$router.replace({ name: "patientList" });
      }
    },
    // 刷新页面
    refresh() {
      if (this.popoverVisible) {
        this.popoverVisible = false;
      }
      /**
       * 页面重新加载前 ajax请求进行前置处理
       * 1.判断页面刷新前是否需要做前置处理,判断RouterList中的RefreshAPI配置
       * 2.获取各子页面的方法，获取请求参数,如果没有，使用公共的请求参数
       * 3.组装请求参数
       * 4.请求完成后加载子页面的刷新逻辑
       */
      let routerList = this._common.session("routerList");
      let path = this.$route.path;
      let curRouter = routerList && routerList.find((item) => item.router == path);
      if (curRouter && curRouter.refreshAPI) {
        let params;
        let promiseResult;
        //调用各页面独有刷新数据方式
        this.$nextTick(async () => {
          // 从个页面获取必要的刷新参数
          if (this.$refs["childPage"] && this.$refs["childPage"].getRefreshParams) {
            params = this.$refs["childPage"].getRefreshParams();
          }
          promiseResult = await RefreshData({
            refreshType: curRouter.refreshAPI,
            stationCode: this.user.stationCode,
            stationID: this.user.stationID,
            ...params,
          });
          // 调用子组件的刷新方法进行页面刷新
          if (this.$refs["childPage"] && this.$refs["childPage"].refreshData) {
            this.$refs["childPage"].refreshData(promiseResult);
          }
        });
      } else {
        this.pageRefresh();
      }
    },
    /**
     * description: 页面刷新
     * return {*}
     */
    pageRefresh() {
      // 控制router-view的显示或隐藏，从而控制页面的再次加载,模拟刷新页面操作。
      this.isRouterAlive = false;
      // 刷新前取消未完成的请求
      if (window._apiRequestList.length > 0) {
        window._apiRequestList.forEach((request) => {
          request.cancel("refresh||" + decodeURIComponent(request.key));
        });
        window._apiRequestList = [];
      }
      this.$nextTick(() => {
        this.isRouterAlive = true;
      });
    },
    // 显示MQ消息
    showMsg() {
      if (this.popoverVisible) {
        this.popoverVisible = false;
      }
      this.isRead = false;
      this.startDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.endDate = this.startDate;
      this.showMessage = true;
      this.searchMessage();
    },
    // 退出登录
    loginOut() {
      if (this.popoverVisible) {
        this.popoverVisible = false;
      }
      this.$confirm(this.mainTexts.signOutConfirm, this.$t("tip.systemTip"), {
        cancelButtonText: this.button.cancel,
        confirmButtonText: this.button.confirm,
        type: "warning",
      })
        .then(() => {
          this.exit();
        })
        .catch(() => {});
    },
    exit() {
      this.$store.commit("auth/exit");
      this.$store.commit("session/setUser", undefined);
      this.$store.commit("session/setCurrentPatient", undefined);
      this.$store.commit("session/setPatientInfo", undefined);
      // 这种写法会清空所有session
      location.pathname = "login";
      // 清空路由历史记录
      this.$router = undefined;
    },
    // 切换菜单收缩
    toggleMenu() {
      this.isCollapse = !this.isCollapse;
    },
    // 切换病区
    async changeStation(station) {
      let newUser = this.user;
      newUser.stationID = station.id;
      newUser.stationCode = station.stationCode;
      // 更新本地缓存
      this.$store.commit("session/setUser", newUser);
      this.$store.commit("session/setCurrentPatient", "");
      // 更新服务端缓存
      let params = {
        stationID: station.id,
      };
      await UpdateSessionStationID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.goHome();
        }
      });
      if (this.mqSetting.isOpen) {
        // 订阅收到的MQ消息
        await this.createMessageBox();
      }
    },
    // 获取菜单列表
    async getMenuList() {
      let params = {
        system: "CCC",
        menuType: "MainMenu",
      };
      await GetMenuList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.menus = result.data;
          this.getBreadcrumb();
        }
      });
    },
    // 选择消息
    selectionMessage(selectMessageList) {
      this.updateMessageList = selectMessageList;
    },
    // 查询消息列表
    searchMessage() {
      let params = {
        SendStationID: this.user.stationID,
        SendEmployeeID: this.user.userID,
        ReadFlag: this.isRead,
      };
      if (this.isRead) {
        params.StartDate = this.startDate + " 00:00:00";
        params.EndDate = this.endDate + " 23:59:59";
      }
      if (this.filterKeywords) {
        params.TitleOrContent = this.filterKeywords;
      }
      this.messageList = [];
      this.messageLoading = true;
      this.messageLoadingText = this.loadingText.load;
      GetMyNotice(params)
        .then((result) => {
          this.messageLoading = false;
          if (this._common.isSuccess(result)) {
            this.messageList = result.data;
          }
        })
        .catch(() => {
          this.messageLoading = false;
        });
    },
    // 更新消息状态
    updateMessage() {
      if (this.updateMessageList.length > 0) {
        let messageIDs = [];
        this.updateMessageList.forEach((message) => {
          messageIDs.push(message.id);
        });
        let params = {
          SendStationID: this.user.stationID,
          SendEmployeeID: this.user.userID,
          ReadFlag: this.isRead,
          ListID: messageIDs,
        };
        this.messageLoading = true;
        this.messageLoadingText = this.loadingText.save;
        UpdateMessages(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.getMessageCount();
            this.searchMessage();
            this.showMessage = false;
          }
        });
      }
    },
    // 获取未读消息数量
    async getMessageCount() {
      let params = {
        SendStationID: this.currentStationID,
        SendEmployeeID: this.user.userID,
        ReadFlag: this.isRead,
      };
      await GetNoReadNoticeListAsync(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.$store.commit("session/setMQMessageCount", result.data.length);
        }
      });
    },
    // 创建MQ消息队列
    async createMessageBox() {
      let params = {
        stationID: this.currentStationID,
        userID: this.user.userID,
      };
      await GetExcharge(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.openMessageBox(result.data, this.user.userID);
        }
      });
    },
    // 创建MQ消息链接
    openMessageBox(exchange, queue) {
      this.client.subscribe({
        exchangeName: exchange,
        routingKey: queue,
        callback: (mq) => {
          let messageCount = 0;
          if (!mq?.length) {
            return;
          }
          for (let i = 0; i < mq.length; i++) {
            let messageKind = mq[i].MessageKind;
            if (messageKind == "W") {
              this.$confirm(mq[i].Messsage, "系统提示", {
                cancelButtonText: "取消",
                confirmButtonText: "确定",
                type: "warning",
              });
              break;
            }
            if (messageKind == "Q") {
              this.exitSystem(mq[i].Messsage);
              break;
            }
            // 系统更新相关消息
            if (messageKind == "U") {
              this.systemUpdate(mq[i].SystemID, mq[i].Messsage);
              break;
            }
            if (messageKind == "M" || messageKind == "N") {
              messageCount++;
            }
          }
          let messagePrompting = mq.find((item) => {
            return item.MessageKind == "N";
          });
          if (messagePrompting) {
            this.showMsg();
          }
          messageCount += this.mqMessageCount;
          this.$store.commit("session/setMQMessageCount", messageCount);
        }
      });
    },
    exitSystem(message) {
      this.$confirm(`${message || "您的帐号在另一台电脑登入"}，被迫下线`, "系统提示", {
        type: "warning",
        //不显示取消按钮
        showCancelButton: false,
        //是否显示右上角的x
        showClose: false,
        //是否可以点击空白处关闭弹窗
        closeOnClickModal: false,
        confirmButtonText: "确定",
      }).then(() => {
        this.exit();
      });
    },
    /**
     * description: 处理系统更新相关消息
     * param {*} systemID 通知类型，1系统更新预告，2系统更新开始，3系统更新结束
     * param {*} message 消息内容
     * return {*}
     */
    systemUpdate(systemID, message) {
      //1系统更新预告
      if (systemID == "1") {
        this.createNotify("pre", "系统更新预告", message, 0, true);
        return;
      }
      //2系统更新开始
      if (systemID == "2") {
        this.createNotify("start", "", message, 0, false);
        return;
      }
      //3系统更新结束
      if (systemID == "3") {
        // 重新登录
        this.reLogin();
        this.createNotify("end", "", message, 3000, true);
        return;
      }
    },
    /**
     * description: 创建通知消息
     * param {*} type 类型：pre系统更新预告，start系统更新开始，end系统更新结束
     * param {*} title 通知标题，可空
     * param {*} message 消息内容
     * param {*} duration 消息显示时间后关闭，单位毫秒，0表示不会自动关闭
     * param {*} showClose 是否显示关闭按钮
     * return {*}
     */
    createNotify(type, title, message, duration, showClose) {
      if (this.systemNotify) {
        this.systemNotify.close();
        this.systemNotify = undefined;
      }
      let params = {
        title: title,
        dangerouslyUseHTMLString: true,
        message: message,
        offset: this.convertPX(40),
        duration: duration,
        showClose: showClose,
        customClass: "system-update " + type,
      };
      this.systemNotify = this.$notify(params);
    },
    /**
     * description: 重新登录
     * param {*}
     * return {*}
     */
    reLogin() {
      // 如果是第三方跳转过来的，带着系统更新标记跳转到系统中转画面
      if (this.externalParams) {
        this.$router.replace({
          name: "externalTransfer",
          query: this.externalParams,
          params: { refreshFlag: true },
        });
        return;
      }
      userLogin(this.loginParams).then(async (result) => {
        if (this._common.isSuccess(result)) {
          //登录成功,更新缓存中token
          this.$store.commit("auth/setToken", result.data.token);
          // 获取session
          GetSession().then(() => {
            // 刷新画面为更新后的系统
            window.location.reload();
          });
        }
      });
    },
    /**
     * description: 查询抽屉高度是否遮挡主记录配置
     * return {*}
     */
    async getDrawerHeightSettingSwitch() {
      let param = {
        SettingTypeCode: "SpecialDrawerHeightSwitch",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          let specialDrawerHeightSwitch = response.data;
          this.$store.commit("session/setSpecialDrawerHeightSwitch", specialDrawerHeightSwitch);
        }
      });
    },
    /**
     * @description: 跳转护理待办列表页面
     * @return
     */
    goStationJobTip() {
      this.$router.replace({ name: "stationToJobTip", params: { stationJopTipList: this.stationJobList } });
    },
    /**
     * @description: 获取护理待办列表图标显示开关
     * @return
     */
    async getShowNursingAgentIconSwitch() {
      let param = {
        SettingTypeCode: "ShowNursingAgentIcon",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.showNursingAgentIconFlag = response.data;
        }
      });
    },
    /**
     * @description: 创建工作提醒定时器
     * @return
     */
    stationJobTipWork() {
      //定义时长五分钟
      let time = 60 * 5;
      if (!this.stationJobList || !this.stationJobList.length) {
        this.getStationJobList();
      }
      // 开启多线程 在后台线程中开启定时器
      const workerScript = `
        let timer = undefined;
        // 接受主线程发送的消息
        onmessage = (event)=> {
          if(event.data == 'close'){
            // 关闭定时器
            if(timer){
              clearInterval(timer);
            }
            // 关闭自己所属线程
            self.close();
          }
        }
        // 开启定时器
        timer = setInterval(()=> {
          // 随便返回 ，这里只是为了触发主线程的onmessage
          postMessage("child worker!")
        }, 1000);
      `;
      // 从内联JavaScript创建Worker
      const workerScriptBlob = new Blob([workerScript]);
      const workerScriptBlobUrl = URL.createObjectURL(workerScriptBlob);
      // 创建新的线程前先停止旧的线程
      this.stopWorker();
      this.worker = new Worker(workerScriptBlobUrl);
      this.worker.onmessage = (event) => {
        time--;
        if (time == 0) {
          this.getStationJobList();
          this.stopWorker();
        }
      };
    },
    /**
     * @description: 获取工作提醒数据
     * @return
     */
    getStationJobList() {
      let params = {
        stationID: this.user.stationID,
      };
      GetStationToJobTipList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.stationJobList = res.data;
          this.attendanceStationJobList = this.stationJobList.filter(
            (tip) => tip.attendanceNurseID == this.user.userID
          );
          this.stationJobTipWork();
        }
      });
    },
    /**
     * @description: 获取调用AI组件配置
     * @return
     */
    async getChatWidgetSetting() {
      let params = {
        settingTypeCode: "AIWidgetShowSettingFlag",
      };
      await GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res) && res.data) {
          this.showAIWidgetIconFlag = res.data;
        }
      });
      if (this.showAIWidgetIconFlag) {
        await this.getChatWidgetAPI();
      }
    },
    /**
     * @description: 获取调用AI组件地址
     * @return
     */
    async getChatWidgetAPI() {
      let params = {
        systemType: "Medical",
        settingCode: "GetAIWidgetUrl",
      };
      await GetConfigSettingByCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.aISrc = res.data.find((setting) => setting.settingType == "AISrc")?.settingValue;
        }
      });
    },
    /**
     * @description: 点击AI按钮弹窗开关
     * @return
     */
    openAiWidget() {
      this.aiIframeFlag = true;
    },
  },
};
</script>

<style lang="scss">
$header-hight: 50px;
.el-container.main-layout {
  width: 100%;
  height: 100%;
  .el-aside.left-menu {
    height: 100%;
    width: auto !important;
    background-color: $base-color;
    overflow: hidden;
    .logo {
      height: $header-hight - 2;
      width: auto;
      box-sizing: border-box;
      text-align: center;
      background-color: #ffffff;
      cursor: pointer;
      img {
        margin: 4px;
        height: 40px;
        width: 40px;
      }
    }
  }
  .el-container.main {
    height: 100%;
    .el-header.system-header {
      position: relative;
      background-color: #ffffff;
      padding: 0 10px 0 5px;
      border-bottom: 2px solid $base-color;
      height: $header-hight !important;
      line-height: $header-hight;
      .left-wrap {
        float: left;
        .hospital-name {
          float: left;
          font-size: 26px;
          margin-left: 5px;
          letter-spacing: 2px;
          cursor: pointer;
          &.is-education {
            color: #ff0000;
            &::after {
              content: "(教学版)";
            }
          }
          &.hospital5 {
            font-size: 18px;
            line-height: 22px;
            font-family: Source Han Serif SC;
            text-align-last: justify;
          }
        }
        .system-version {
          float: left;
          height: 30px;
          line-height: 30px;
          margin-top: 14px;
          color: $base-color;
          font-size: 18px;
          margin-left: 10px;
          .version {
            cursor: pointer;
            &:hover {
              color: $base-color;
              border-bottom: 1px solid $base-color;
            }
          }
        }
      }
      .toggle-fullscreen {
        float: right;
        margin: 0 20px 5px 0;
        color: #ff0000;
        font-size: 18px;
      }
      .right-wrap {
        float: right;
        height: 100%;
        &.is-pad {
          .popover-btn {
            display: block !important;
          }

          .btn,
          .user-info {
            display: none !important;
          }
        }
        @media screen and (min-width: 1000px) {
          .popover-btn {
            display: none !important;
          }

          .btn,
          .user-info {
            display: inline-block !important;
          }
        }

        @media screen and (max-width: 1000px) {
          .popover-btn {
            display: block !important;
          }

          .btn,
          .user-info {
            display: none !important;
          }
        }
        .popover-btn {
          margin-right: 180px;
          .iconfont {
            height: 36px;
            margin-right: 10px;
            font-size: 28px;
            color: $base-color;
          }
        }
        .btn {
          float: left;
          .message {
            display: inline-block;
            .el-badge {
              vertical-align: baseline;
              .el-badge__content {
                top: 13px;
                right: 26px;
                font-size: 8px;
                padding: 0 3px;
                min-width: 12px;
              }
            }
          }
          .station-tip {
            display: inline-block;
            .el-badge {
              vertical-align: baseline;
              .el-badge__content {
                top: 16px;
                right: 16px;
                height: 6px;
                width: 6px;
              }
            }
          }
        }
        .iconfont {
          height: 36px;
          margin-top: 0;
          margin-right: 15px;
          font-size: 18px;
          color: $base-color;
        }
        .user-info {
          height: 100%;
          margin-left: 5px;
          float: left;
          font-size: 20px;
          margin-right: 190px;
          .iconfont {
            margin: 0 5px;
            font-size: 36px;
          }
          .text {
            font-size: 14px;
            vertical-align: top;
            display: inline-block;
          }
        }
        .select-station {
          position: absolute;
          top: -1px;
          right: 10px;
          .el-input {
            .el-input__inner {
              height: 32px !important;
              padding: 0 30px 0 10px;
              border: 1px solid $base-color;
              color: #000000 !important;
            }
          }
        }
      }
    }
    .el-main.router-view-main {
      height: calc(100% - 50px);
      &.is-dialog {
        height: 100%;
      }
      padding: 0px;
      box-sizing: border-box;
      .bread-router {
        background-color: #ffffff;
        height: 30px;
        line-height: 30px;
        padding: 0 5px;
        box-sizing: border-box;
        margin: 0 5px 5px 5px;
        .el-breadcrumb__item:last-child .el-breadcrumb__inner {
          color: $base-color;
        }
      }
      .router-view-wrap {
        height: calc(100% - 35px);
        &.read-only {
          .el-button.add-button,
          .el-button.save-button,
          .el-tooltip.iconfont.icon-drawing,
          .el-tooltip.iconfont.icon-change,
          .el-tooltip.iconfont.icon-stop,
          .el-tooltip.iconfont.icon-del,
          .el-tooltip.iconfont.icon-edit,
          .el-tooltip.iconfont.icon-temperature {
            display: none !important;
          }
        }
        &.is-dialog {
          height: 100%;
        }
      }
    }
  }
  .message-dialog {
    width: 760px;
    .el-dialog__body {
      padding: 0;
      .label {
        margin-left: 10px;
      }
    }
    .select-date {
      width: 120px;
    }
    .select-input {
      width: 210px;
      .el-input-group__append {
        padding: 0 5px;
        color: #8cc63e;
      }
    }
  }
  .validation-dialog {
    width: 760px;
    height: 300px;
    margin-top: 30vh !important;
    .lock-label {
      font-size: 20px;
      font-weight: bold;
      margin: 20px;
      .lock-user {
        color: #ff0000;
      }
    }
    .el-input.lock-password {
      height: 50px;
      width: 700px;
      margin: 10px 20px;
      border: 1px solid #cccccc;
      background-color: #ffffff;
      .el-input-group__prepend {
        height: 50px;
        line-height: 50px;
        border-radius: 0;
        border: 0;
        .iconfont {
          cursor: default;
          margin: 0;
          font-size: 30px;
        }
      }
      .el-input__inner {
        font-size: 30px;
        border: 0px;
        height: 100% !important;
        outline: none;
        padding-left: 20px;
        background-color: #ffffff;
        &:-webkit-autofill {
          box-shadow: 0 0 0 1000px #ffffff inset !important;
        }
      }
    }
  }
  .version-dialog {
    max-width: 800px;
    .version-wrap {
      .label {
        margin-left: 10px;
        letter-spacing: 1px;
        &.add-function,
        &.bug-fix,
        &.system-optimization {
          color: #ff0000;
        }
      }
      .version-info {
        margin-bottom: 5px;
      }
      .version-contents {
        margin-top: 5px;
        .contents {
          letter-spacing: 1px;
          margin: 5px 5px 0px 30px;
          .content {
            margin-left: 26px;
          }
        }
      }
      .file-list {
        margin: 15px 5px 0px 5px;
        .file-name {
          display: block;
          width: fit-content;
          margin-left: 40px;
          cursor: pointer;
          &:hover {
            color: $base-color;
            border-bottom: 1px solid $base-color;
          }
        }
      }
    }
  }
  .ai-widget-dialog {
    width: 490px;
    height: 800px;
    margin-top: 10vh !important;
  }
}
.popover-btn-content {
  width: auto;
  min-width: 100px;
  padding: 0;
  .popover-items {
    .iconfont {
      margin-right: 5px;
      font-size: 18px;
      color: $base-color;
    }
    .item {
      padding: 1px 10px;
      cursor: pointer;
      &:hover:not(.user) {
        background-color: $base-color;
        color: #ffffff;
        .iconfont {
          color: #ffffff;
        }
      }
      &.user {
        padding-left: 5px;
        display: flex;
        border-bottom: 1px dashed #cccccc;
        .icon-user {
          font-size: 30px;
        }
        .user-name {
          display: inline-block;
          margin-top: 15px;
        }
      }
      .message {
        display: inline-block;
        .el-badge {
          vertical-align: baseline;
          .el-badge__content {
            top: 6px;
            right: 16px;
            font-size: 9px;
            padding: 0 3px;
            min-width: 12px;
          }
        }
      }
    }
  }
}
/* 系统更新提示消息样式 */
.el-notification.system-update {
  background-color: $base-color;
  max-height: 90%;
  padding: 7px 0;
  .el-notification__group {
    margin: 0 7px;
    width: 100%;
  }
  &.pre {
    .el-notification__title {
      color: #ffffff;
      padding: 0 0 5px 10px;
      letter-spacing: 1px;
    }
  }
  &.start,
  &.end {
    .el-notification__content {
      max-height: 100%;
      color: #ff0000;
      border-radius: 6px;
    }
  }
  .el-notification__content {
    padding: 8px;
    color: $base-color;
    background-color: #ffffff;
    border-radius: 0 0 6px 6px;
    margin: 0;
    max-height: calc(100% - 20px);
    overflow: auto;
  }
  .el-icon-close {
    color: #ffffff;
    top: 12px;
  }
}
.icon-ai-widget {
  color: #0680ec !important;
}
.message-tool-tips {
  width: 80%;
  color: #fff !important;
}
#fastgpt-chatbot-window {
  border-radius: 0px !important;
}
</style>
