<!--
 * FilePath     : CareDirect\Medical\Web\ccc.web\src\autoPages\restraint\index.vue
 * Author       : 杨欣欣
 * Date         : 2021-10-21 10:30
 * LastEditors  : 曹恩
 * LastEditTime : 2025-04-23 10:25
 * Description  :  约束主页面
 * CodeIterationRecord: 2022-08-16 2876-专项护理增加带入护理记录标记 -杨欣欣
                        2023-02-15 3235-作为IT人员，我需要改版约束专项，以利快速查看操作 -杨欣欣
-->
<template>
  <specific-care
    class="restraint"
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    :editFlag="checkResult"
    :careMainAddFlag="careMainAddFlag"
    :recordAddFlag="canAdd"
    :drawerSize="refillFlag ? '80%' : ''"
    :previewFlag="previewFlag"
    @mainAdd="recordAdd"
    @maintainAdd="careMainAdd"
    @save="saveByRecordsCode"
    @cancel="drawerClose"
    @getHandOverFlag="getChecked($event, 'handOverArr')"
    @getInformPhysicianFlag="getChecked($event, 'informPhysicianArr')"
    @getNursingRecordFlag="getChecked($event, 'bringToNursingRecordArr')"
    v-loading="recordLoading"
    element-loading-text="加载中……"
  >
    <!-- 主记录 -->
    <div slot="main-record">
      <el-table
        @row-click="recordClick"
        :data="recordData"
        border
        stripe
        height="100%"
        row-class-name="main-record-row"
        header-row-class-name="main-record-herder-row"
      >
        <el-table-column prop="departmentName" label="科室" :width="convertPX(200)" header-align="center" />
        <el-table-column label="开始时间" :width="convertPX(210)" align="center">
          <template slot-scope="restraint">
            <span v-formatTime="{ value: restraint.row.startDate, type: 'date' }" />
            <span v-formatTime="{ value: restraint.row.startTime, type: 'time' }" />
          </template>
        </el-table-column>
        <el-table-column prop="frequency" label="频次" header-align="center" />
        <el-table-column label="结束时间" :width="convertPX(210)" align="center">
          <template slot-scope="restraint">
            <span v-formatTime="{ value: restraint.row.endDate, type: 'date' }" />
            <span v-formatTime="{ value: restraint.row.endTime, type: 'time' }" />
          </template>
        </el-table-column>
        <el-table-column prop="restraintDays" label="约束天数" :width="convertPX(110)" header-align="center" />
        <el-table-column prop="consent" label="同意书" :width="convertPX(100)" header-align="center" />
        <el-table-column prop="orderDoctorName" label="医嘱医师" :width="convertPX(110)" header-align="center" />
        <el-table-column prop="userName" label="记录人" :width="convertPX(120)" header-align="center" />
        <el-table-column label="最近维护时间" :width="convertPX(160)" align="center">
          <template slot-scope="restraint">
            <span v-formatTime="{ value: restraint.row.lastAssessDateTime, type: 'datetime' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="convertPX(150)" align="center">
          <template slot-scope="restraint">
            <el-tooltip content="修改" v-show="!restraint.row.endDate">
              <div class="iconfont icon-edit" @click.stop="recordAdd(restraint.row)" />
            </el-tooltip>
            <el-tooltip content="停止" v-show="!restraint.row.endDate">
              <div class="iconfont icon-stop" @click.stop="recordEnd(restraint.row.patientRestraintRecordID)" />
            </el-tooltip>
            <el-tooltip v-if="showRecordArr[0]" content="删除" v-show="!restraint.row.endDate">
              <div class="iconfont icon-del" @click.stop="deleteRecordByID(restraint.row)" />
            </el-tooltip>
            <el-tooltip content="告知书" v-if="restraint.row.emrDocumentID != 0">
              <div class="iconfont icon-pdf" @click.stop="getNotice(restraint.row.emrDocumentID)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <packaging-table v-model="careMainData" :headerList="tableHeaderList">
        <!-- 评估类型 插槽 -->
        <div slot="assessType" slot-scope="scope">
          <span v-if="scope.row.recordsCode.includes('Start')">开始评估</span>
          <span v-else-if="scope.row.recordsCode.includes('End')">结束评估</span>
          <span v-else>例行评估</span>
        </div>
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope" v-if="!scope.row.recordsCode.includes('Start')">
          <el-tooltip content="修改" placement="top">
            <div class="iconfont icon-edit" @click="careMainAdd(scope.row)" />
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <div class="iconfont icon-del" @click="deleteCareMain(scope.row)" />
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 抽屉 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
    >
      <!-- 开始记录抽屉头 -->
      <div slot="header">
        <span class="label">时间:</span>
        <el-date-picker
          class="date-picker"
          v-model="assessDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          :picker-options="datePickerOptions"
        />
        <el-time-picker
          class="time-picker"
          v-model="assessTime"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          :picker-options="timePickerOptions"
        />
        <station-selector v-model="stationID" label="病区：" />
        <dept-selector label="" v-model="departmentListID" :stationID="stationID" />
        <span class="consent-flag" v-show="recordsCode.includes('Start')">
          同&nbsp;&nbsp;意&nbsp;&nbsp;书：
          <el-checkbox v-model="consentFlag" true-label="1" false-label="0" />
          评估频次：
          <el-select class="frequency-selector" placeholder="请选择频次" v-model="frequency">
            <el-option
              v-for="item in frequencyList"
              :key="item.typeValue"
              :label="item.description"
              :value="item.typeValue"
            ></el-option>
          </el-select>
          <employee-info v-model="orderDoctorID" label="开嘱医师：" :width="convertPX(120) + 'px'" :height="24" />
        </span>
      </div>
      <div class="base-layout-content">
        <tabs-layout
          ref="tabsLayout"
          :image-max-num="imageMaxNum"
          :template-list="templateDatas"
          @change-values="changeValues"
          @checkTN="checkTN"
          @wound-img="restraintImg"
          v-loading="templateLoading"
          :element-loading-text="templateLoadingText"
          @button-record-click="buttonRecordClick"
          :checkFlag="true"
        />
      </div>
    </base-layout>
    <!-- 弹窗 -->
    <div class="drawer-dialog" slot="drawer-dialog">
      <!-- 风险跳转 -->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonRecordTitle"
        :visible.sync="showButtonRecordDialog"
        custom-class="no-footer"
      >
        <risk-component :params="conponentParams" @result="result"></risk-component>
      </el-dialog>
      <!-- 告知书 -->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        title="告知书"
        custom-class="no-footer"
        fullscreen
        :visible.sync="showNotification"
      >
        <document-sign :iframeType="'application/x-google-chrome-pdf'" :signParams="notificationParams"></document-sign>
      </el-dialog>
    </div>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import employeeInfo from "@/components/employeeInfo";
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import tabsLayout from "@/components/tabsLayout/index";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import { GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import { GetBySettingTypeCode, GetBringToShiftSetting } from "@/api/Setting";
import { GetBringToNursingRecordFlagSetting, GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import {
  GetRestraintRecordList,
  GetCareByRecordID,
  SaveRestraint,
  SaveRestraintCare,
  DeleteRestraintByID,
  DeleteRestraintCare,
  StopRestraint,
  GetRestraintAssessView,
  GetRestraintOrder,
} from "@/api/RestraintRecord";
import { mapGetters } from "vuex";
import { GetButtonData } from "@/api/Assess";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import documentSign from "@/components/DocumentSign";
export default {
  components: {
    specificCare,
    baseLayout,
    tabsLayout,
    stationSelector,
    deptSelector,
    employeeInfo,
    riskComponent,
    packagingTable,
    documentSign,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      patientInfo: undefined,
      recordLoading: false,
      buttonRecordLoading: false,
      buttonRecordLoadingText: "加载中……",
      drawerLoading: false,
      drawerLoadingText: "",
      templateLoading: false,
      templateLoadingText: "加载中……",
      // 抽屉开关
      showTemplateFlag: false,
      // 抽屉标题
      drawerTitle: undefined,
      // 主记录/维护记录是否显示
      showRecordArr: [true, false],
      // 修改权限
      checkResult: true,
      // 维护记录新增按钮开关
      careMainAddFlag: false,
      // 动态表头
      tableHeaderList: [],
      // 当前选中的主记录
      currentRecord: undefined,
      imageMaxNum: 4,
      recordData: [],
      careMainData: [],
      patientScheduleMainID: undefined,
      recordsCodeInfo: undefined,
      stationID: undefined,
      departmentListID: undefined,
      frequency: "",
      orderDoctorID: "",
      //医嘱开立人员ID
      doctorID: undefined,
      consentFlag: 0,
      //约束同意书配置
      defaultConsentFlag: undefined,
      frequencyList: [],
      restraintImages: [],
      templateDatas: [],
      assessDatas: [],
      patientRestraintRecordID: "",
      checkTNFlag: true,
      //显示打印告知书
      showNotification: false,
      //是否能编辑删除该数据
      showEditButton: false,
      // BR类变量
      showButtonRecordDialog: false,
      buttonRecordTitle: "",
      // BR参数
      conponentParams: {},
      // BR项目ID
      brAssessListID: undefined,
      // 带入交班是否显示&是否带入
      handOverArr: [true, false],
      // 通知医师是否显示&是否带入
      informPhysicianArr: [true, false],
      // 带入护理记录是否显示&是否带入
      bringToNursingRecordArr: [true, false],
      settingHandOver: false,
      settingBringToNursingRecord: false,
      informPhysician: false,
      sourceID: undefined,
      sourceType: undefined,
      assessDate: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      assessTime: undefined,
      departmentListID: undefined,
      recordsCode: "",
      refillFlag: "",
      // 图片保存参数列表
      imageSaveArr: [],
      // 明细数据保存参数列表
      detailSaveArr: [],
      //告知书显示和签名的参数
      notificationParams: undefined,
      previewFlag: false,
      endDate: undefined,
      endTime: undefined,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patient: "getPatientInfo",
    }),
    canAdd() {
      // 只要有一条数据的EndDate为空，就返回false
      return !this.recordData.some((restraint) => !restraint.endDate);
    },
    //获取最后一条结束的约束记录
    lastEndRecord() {
      let lastEndRecord = this.recordData
        .filter((item) => item.endDate != null)
        .sort((a, b) => {
          let endDateTime = this._datetimeUtil.formatDate(a.endDate) + " " + this._datetimeUtil.formatDate(a.endTime);
          let endDateTimeB = this._datetimeUtil.formatDate(b.endDate) + " " + this._datetimeUtil.formatDate(b.endTime);
          return new Date(endDateTime) - new Date(endDateTimeB);
        });
      if (lastEndRecord.length == 0) {
        return undefined;
      }
      return lastEndRecord[0];
    },
    //日期检核,不能小于最后一条结束的约束记录的结束日期
    datePickerOptions() {
      return {
        disabledDate: (value) => {
          if (!this.lastEndRecord) {
            return false;
          }
          if (this._datetimeUtil.formatDate(value) < this.lastEndRecord.endDate) {
            return true;
          }
          return false;
        },
      };
    },
    //时间检核,不能小于最后一条结束的约束记录的结束时间
    timePickerOptions() {
      if (
        !this.lastEndRecord ||
        this.assessDate > this._datetimeUtil.formatDate(this.lastEndRecord.endDate, "yyyy-MM-dd")
      ) {
        return {
          selectableRange: "00:00:00 - 23:59:59",
        };
      }
      return {
        selectableRange: this._datetimeUtil.formatDate(this.lastEndRecord.endTime, "hh:mm:ss") + " - 23:59:59",
      };
    },
  },
  watch: {
    //在院病人信息
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patientInfo = this.patient;
          this.refillFlag = "";
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patientInfo = this.supplemnentPatient;
          this.refillFlag = "*";
          this.bringToNursingRecordArr = [false, false];
        }
      },
      immediate: true,
    },
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 进入页面，初始化
     * return {*}
     */
    init() {
      this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
      // 设置切换病人
      if (this.patientScheduleMainID || this.sourceID) {
        this._sendBroadcast("setPatientSwitch", false);
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
      if ((this.patientInfo || {}).inpatientID) {
        this.getRecordData();
      }
      this.getBringHandOverSetting();
      this.getBringToNursingRecordSetting();
      this.getAssessFrequency();
      this.getConsentDefaultSetting();
      this.getRestraintOrder(this.patientScheduleMainID);
    },
    /*****配置获取*****/
    /**
     * description: 获取评估频次
     * return {*}
     */
    getAssessFrequency() {
      const params = {
        settingTypeCode: "RestraintFrequency",
      };
      GetBySettingTypeCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.frequencyList = result.data;
        }
      });
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "Restraint",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingHandOver = res.data;
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "RestraintAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },
    /**
     * description: 获取维护记录动态列
     * param {*}
     * return {*}
     */
    async getTableHeaderList() {
      let params = {
        fileClassID: 44,
        useDescription: "1||Table",
        newSourceFlag: true,
      };
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tableHeaderList = res.data;
        }
      });
    },
    /*****专项数据查询*****/

    /**
     * description: 获取约束主记录
     * return {*}
     */
    getRecordData(recordID = undefined) {
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        recordID,
      };
      this.recordData = [];
      this.recordLoading = true;
      GetRestraintRecordList(params).then((result) => {
        this.recordLoading = false;
        if (this._common.isSuccess(result)) {
          this.recordData = result.data;
          this.currentRecord = recordID && this.recordData?.length ? this.recordData[0] : undefined;
        }
      });
    },
    /**
     * description: 获取维护记录
     * param {*} careMain
     * return {*}
     */
    getCareMainData() {
      const params = {
        recordID: this.currentRecord.patientRestraintRecordID,
      };
      this.restraintDetails = [];
      this.recordLoading = true;
      GetCareByRecordID(params).then((result) => {
        this.recordLoading = false;
        if (this._common.isSuccess(result)) {
          this.careMainData = result.data;
        }
      });
    },

    /*****新增/修改专项数据，打开抽屉*****/

    /**
     * description: 新增/修改主记录
     * param {*} restraint 主记录
     * return {*}
     */
    async recordAdd(restraint) {
      await this.getAssessRecordsCode("RestraintStart");

      if (restraint) {
        //权限检核
        await this.checkAuthor(restraint.patientRestraintRecordID, "PatientRestraintRecord", restraint.addEmployeeID);
        if (!this.showEditButton) {
          return;
        }
        if (this.refillFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            restraint.addEmployeeID
          );
          this.previewFlag = !saveButtonFlag;
        }
        this.openOrCloseDrawer(true, "约束主记录修改");
        this.stationID = restraint.stationID;
        this.departmentListID = restraint.departmentListID;
        this.assessDate = restraint.startDate;
        this.assessTime = restraint.startTime;
        this.frequency = restraint.assessFrequency + "";
        this.consentFlag = restraint.consentFlag;
        this.orderDoctorID = restraint.orderPhysicianID;
        this.patientRestraintRecordID = restraint.patientRestraintRecordID;
        this.patientRestraintCareMainID = restraint.patientRestraintCareMainID;
        this.$set(this.informPhysicianArr, 1, restraint.informPhysician);
        this.$set(this.handOverArr, 1, restraint.bringToShift);
        this.$set(this.bringToNursingRecordArr, 1, restraint.bringToNursingRecord);
      } else {
        this.openOrCloseDrawer(true, "约束主记录新增");
        this.showEditButton = true;
        this.stationID = this.patientInfo.stationID;
        this.departmentListID = this.patientInfo.departmentListID;
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
        this.patientRestraintRecordID = "";
        this.patientRestraintCareMainID = "temp_" + this._common.guid();
        this.$set(this.informPhysicianArr, 1, false);
        this.$set(this.handOverArr, 1, this.settingHandOver);
        this.$set(this.bringToNursingRecordArr, 1, this.settingNursingRecord);
        this.consentFlag = this.defaultConsentFlag ? "1" : "0";
        this.orderDoctorID = this.doctorID;
      }

      const params = {
        recordID: this.patientRestraintRecordID,
        careMainID: this.patientRestraintCareMainID.includes("temp") ? "" : this.patientRestraintCareMainID,
        inpatientID: this.patientInfo.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patientInfo.age,
        gender: this.patientInfo.genderCode,
        departmentListID: this.patientInfo.departmentListID,
        dateOfBirth: this.patientInfo.dateOfBirth,
        sourceID: this.patientRestraintCareMainID,
        sourceType: this.recordsCodeInfo.recordsCode + "BR",
      };
      this.templateDatas = [];
      this.templateLoading = true;
      this.templateLoadingText = "加载中……";
      await GetRestraintAssessView(params).then((result) => {
        this.templateLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 新增/修改维护记录
     * param {*} careMain 维护记录
     * return {*}
     */
    async careMainAdd(careMain) {
      this.patientRestraintRecordID = this.currentRecord.patientRestraintRecordID;
      // 修改
      if (careMain) {
        //权限检核
        await this.checkAuthor(careMain.patientRestraintCareMainID, "PatientRestraintCareMain", careMain.addEmployeeID);
        if (!this.showEditButton) {
          return;
        }
        if (this.refillFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            careMain.addEmployeeID
          );
          this.previewFlag = !saveButtonFlag;
        }
        this.openOrCloseDrawer(true, "约束维护记录修改");
        this.stationID = careMain.stationID;
        this.departmentListID = careMain.departmentListID;
        this.assessDate = this._datetimeUtil.formatDate(careMain.assessDateTime, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(careMain.assessDateTime, "hh:mm");
        this.patientRestraintCareMainID = careMain.patientRestraintCareMainID;
        this.patientScheduleMainID = careMain.patientScheduleMainID;
        this.$set(this.informPhysicianArr, 1, careMain.informPhysician);
        this.$set(this.handOverArr, 1, careMain.bringToShift);
        this.$set(this.bringToNursingRecordArr, 1, careMain.bringToNursingRecord);
        await this.getAssessRecordsCode(careMain.recordsCode);
      } else {
        this.openOrCloseDrawer(true, "约束维护记录新增");
        // 新增
        this.showEditButton = true;
        this.stationID = this.patientInfo.stationID;
        this.departmentListID = this.patientInfo.departmentListID;
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
        this.patientRestraintCareMainID = "temp_" + this._common.guid();
        this.$set(this.informPhysicianArr, 1, false);
        this.$set(this.handOverArr, 1, this.settingHandOver);
        this.$set(this.bringToNursingRecordArr, 1, this.settingBringToNursingRecord);
        await this.getAssessRecordsCode("RestraintMaintain");
      }

      const params = {
        age: this.patientInfo.age,
        gender: this.patientInfo.genderCode,
        departmentListID: this.patientInfo.departmentListID,
        dateOfBirth: this.patientInfo.dateOfBirth,
        careMainID: this.patientRestraintCareMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        recordID: this.patientRestraintRecordID,
        inpatientID: this.patientInfo.inpatientID,
        sourceID: this.patientRestraintCareMainID,
        sourceType: this.recordsCodeInfo.recordsCode + "BR",
      };
      this.templateLoading = true;
      this.templateLoadingText = "加载中……";
      await GetRestraintAssessView(params).then((result) => {
        this.templateLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 新增停止约束
     * param {*} recordID 主记录ID
     * return {*}
     */
    async recordEnd(recordID) {
      await this.getAssessRecordsCode("RestraintEnd");
      this.openOrCloseDrawer(true, "约束停止");
      this.stationID = this.patientInfo.stationID;
      this.departmentListID = this.patientInfo.departmentListID;
      this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
      this.patientRestraintRecordID = recordID;
      this.patientRestraintCareMainID = "temp_" + this._common.guid();
      this.$set(this.informPhysicianArr, 1, false);
      this.$set(this.handOverArr, 1, this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, this.settingBringToNursingRecord);

      let params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patientInfo.age,
        gender: this.patientInfo.gender,
        departmentListID: this.patientInfo.departmentListID,
        dateOfBirth: this.patientInfo.dateOfBirth,
        sourceID: this.patientRestraintCareMainID,
        sourceType: this.recordsCodeInfo.recordsCode + "BR",
        inpatientID: this.patientInfo.inpatientID,
      };
      this.templateDatas = [];
      this.templateLoading = true;
      this.templateLoadingText = "加载中……";
      await GetRestraintAssessView(params).then((result) => {
        this.templateLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },

    /*****删除专项数据*****/

    /**
     * description: 删除主记录
     * param {*} row 主记录行数据
     * return {*}
     */
    async deleteRecordByID(row) {
      //权限检核
      await this.checkAuthor(row.patientRestraintRecordID, "PatientRestraintRecord", row.addEmployeeID);
      if (!this.showEditButton) {
        return;
      }
      if (this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.addEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            recordID: row.patientRestraintRecordID,
          };
          this.recordLoading = true;
          DeleteRestraintByID(params).then((result) => {
            this.recordLoading = false;
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              this.fixTable();
              // 刷新数据
              this.getRecordData();
            }
          });
        }
      });
    },
    /**
     * description: 删除维护记录
     * param {*} row 维护记录行数据
     * return {*}
     */
    async deleteCareMain(row) {
      //权限检核
      await this.checkAuthor(row.patientRestraintCareMainID, "PatientRestraintCareMain", row.addEmployeeID);
      if (!this.showEditButton) {
        return;
      }
      if (this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.addEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            careMainID: row.patientRestraintCareMainID,
          };
          this.recordLoading = true;
          DeleteRestraintCare(params).then((result) => {
            this.recordLoading = false;
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              if (row.recordsCode.includes("End")) {
                this.careMainAddFlag = true;
              }
              // 刷新数据
              this.getRecordData(row.patientRestraintRecordID);
              this.getCareMainData();
            }
          });
        }
      });
    },

    /*****保存专项数据*****/

    /**
     * description: 保存主记录
     * return {*}
     */
    async recordSave() {
      if (!this.saveCheck("Start")) {
        return;
      }
      // 组装约束记录表数据
      let saveData = {
        record: {
          patientRestraintRecordID: this.patientRestraintRecordID,
          inpatientID: this.patientInfo.inpatientID,
          patientID: this.patientInfo.patientID,
          stationID: this.stationID,
          departmentListID: this.departmentListID,
          bedID: this.patientInfo.bedID,
          caseNumber: this.patientInfo.caseNumber,
          chartNo: this.patientInfo.chartNo,
          bedNumber: this.patientInfo.bedNumber,
          startDate: this.assessDate,
          startTime: this.assessTime,
          assessFrequency: this.frequency,
          consentFlag: this.consentFlag,
          orderPhysicianID: this.orderDoctorID,
        },
        patientRestraintCareMainID: this.patientRestraintCareMainID,
        details: this.detailSaveArr,
        restraintImages: this.imageSaveArr,
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        nursingLevel: this.patientInfo.nursingLevelCode,
        recordsCode: this.recordsCodeInfo.recordsCode,
        bringToShift: this.handOverArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
        informPhysician: this.informPhysicianArr[1],
        patientScheduleMainID: this.patientScheduleMainID,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        refillFlag: this.refillFlag,
      };
      await SaveRestraint(saveData).then((result) => {
        if (this._common.isSuccess(result)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", "保存成功！");
          this.fixTable();
          // 刷新数据
          this.getRecordData();
        }
      });
    },
    /**
     * description: 保存
     * return {*}
     */
    async careMainSave() {
      if (!this.saveCheck("Maintain")) {
        return;
      }
      const saveData = this.createCareMainSaveView();
      await SaveRestraintCare(saveData).then((result) => {
        if (this._common.isSuccess(result)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", "保存成功！");
          // 刷新数据
          this.getRecordData(this.currentRecord.patientRestraintRecordID);
          this.getCareMainData();
        }
      });
    },
    /**
     * description: 保存停止
     * return {*}
     */
    async recordEndSave() {
      if (!this.saveCheck("End")) {
        return;
      }
      const saveData = this.createCareMainSaveView();
      await StopRestraint(saveData).then((result) => {
        if (this._common.isSuccess(result)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", "停止成功！");
          this.fixTable();
          this.getRecordData();
        }
      });
    },
    /**
     * description: 创建维护记录保存View
     * return {*}
     */
    createCareMainSaveView() {
      return {
        patientRestraintRecordID: this.patientRestraintRecordID,
        patientRestraintCareMainID: this.patientRestraintCareMainID,
        inpatientID: this.patientInfo.inpatientID,
        patientScheduleMainID: this.patientScheduleMainID,
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        nursingLevel: this.patientInfo.nursingLevelCode,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessDate: this.assessDate,
        assessTime: this.assessTime,
        stationID: this.stationID,
        departmentListID: this.departmentListID,
        details: this.detailSaveArr,
        restraintImages: this.imageSaveArr,
        bringToShift: this.handOverArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
        informPhysician: this.informPhysicianArr[1],
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        refillFlag: this.refillFlag,
      };
    },
    /*****风险相关方法*****/
    /**
     * description: 更新按钮角标
     * param {*} assessListID
     * return {*}
     */
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    /**
     * description: 获取按钮值
     * param {*} assessListID
     * return {*}
     */
    async getButtonValue(assessListID) {
      let item = "";
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessListID: assessListID,
        sourceID: this.getPatientRestraintCareMainID(),
        sourceType: this.recordsCodeInfo.recordsCode + "BR",
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },

    /*****专项组件相关方法*****/

    /**
     * description: 抽屉保存按钮回调，根据RecordsCode判断保存数据
     * return {*}
     */
    async saveByRecordsCode() {
      if (!this.checkRestraintDate()) {
        this._showTip("error", "约束开始时间不能早于上次约束结束时间！");
        return;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return;
      }
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      this.getImages();
      this.getDetails();
      if (this.recordsCodeInfo.recordsCode.includes("Start")) {
        await this.recordSave();
      }
      if (this.recordsCodeInfo.recordsCode.includes("Maintain")) {
        await this.careMainSave();
      }
      if (this.recordsCodeInfo.recordsCode.includes("End")) {
        await this.recordEndSave();
      }
      this.drawerLoading = false;
    },
    /**
     * description: 回调，接收BR类按钮配置数据
     * param {*} content
     * return {*}
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      this.brAssessListID = content.assessListID;
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      this.conponentParams = {
        patientInfo: this.patientInfo,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: this.recordsCodeInfo.recordsCode + "BR",
        sourceID: this.getPatientRestraintCareMainID(),
        assessTime:
          this._datetimeUtil.formatDate(this.assessDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.assessTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    result(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brAssessListID);
      }
    },
    /**
     * description: 回调存储约束图片
     * param {*} images
     * return {*}
     */
    restraintImg(images) {
      this.restraintImages = images;
    },
    /**
     * description: 回调评估数据存储
     * param {*} data
     * return {*}
     */
    changeValues(data) {
      this.assessDatas = data;
    },
    /**
     * description: TN类录入回调，存储数据录入是否通过
     * param {*} flag
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 抽屉下方勾选项数据回调
     * param {*} flag 回传的勾选状态
     * param {*} checkAttr 勾选项字段名
     * return {*}
     */
    getChecked(flag, checkAttr) {
      this[checkAttr][1] = flag;
    },

    /*****页面公共方法*****/

    /**
     * description: 不同科室可能不同模板，获取DepartmentToAssess
     * param {*} recordsCode
     * return {*}
     */
    async getAssessRecordsCode(recordsCode) {
      let params = {
        mappingType: recordsCode,
        inpatientID: this.patientInfo.inpatientID,
        departmentListID: this.patientInfo.departmentListID,
        age: this.patientInfo.age,
        dateOfBirth: this.patientInfo.dateOfBirth,
      };
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
          this.recordsCode = (this.recordsCodeInfo || {}).recordsCode;
        }
      });
    },
    /**
     * description: 弹窗关闭
     * param {*}
     * return {*}
     */
    drawerClose() {
      this.recordID = undefined;
      this.careMainID = undefined;
      this.templateDatas = [];
      this.assessDatas = [];
      this.showTemplateFlag = false;
    },
    /**
     * description: 获取保存明细
     * param {*}
     * return {*}
     */
    getDetails() {
      this.detailSaveArr = [];
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        if (content.disableGroup != -1) {
          this.detailSaveArr.push(detail);
        }
      });
    },
    /**
     * description: 组装图片字段
     * return {*}
     */
    getImages() {
      this.imageSaveArr = [];
      if ((this.restraintImages || []).length) {
        for (let i = 0; i < this.restraintImages.length; i++) {
          let image = this.restraintImages[i].split(",");
          let type = image[0].match(/:(.*?);/)[1].split("/")[1];
          let imageInfo = {
            inpatientID: this.patientInfo.inpatientID,
            patientID: this.patientInfo.patientID,
            specialListImageNumber: i + 1,
            specialListImage: image[1],
            imageType: type,
          };
          this.imageSaveArr.push(imageInfo);
        }
      }
    },
    /**
     * description: 重置选中状态
     * param {*}
     * return {*}
     */
    fixTable() {
      this.showRecordArr = [true, false];
      this.currentRecord = undefined;
      this.careMainData = [];
      this.restraintImages = [];
    },

    /**
     * description: 保存检核
     * return {*}
     */
    saveCheck(recordsCode) {
      let pass = true;

      // TN类检核
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        pass = false;
        return pass;
      }
      // 模板明细检核
      if (!this.assessDatas) {
        this._showTip("warning", "请选择或填写相关项目！");
        pass = false;
        return pass;
      }
      if (this.imageSaveArr.length > 0 && this.detailSaveArr.length === 0) {
        this._showTip("warning", "不能只传图片，请评估其他项目！");
        return;
      }
      if (recordsCode.includes("Start")) {
        if (!this.frequency) {
          this._showTip("warning", "请选择评估频次！");
          pass = false;
          return pass;
        }
        if (!this.orderDoctorID) {
          this._showTip("warning", "请输入开嘱医师！");
          pass = false;
          return pass;
        }
      }
      return pass;
    },
    /**
     * description: 告知书
     * param {*} emrCode
     * return {*}
     */
    getNotice(emrCode) {
      this.notificationParams = {
        inpatientID: this.patientInfo.inpatientID,
        emrDocumentID: emrCode,
      };
      this.showNotification = true;
    },
    /**
     * description: 获取约束维护记录ID
     * return {*}
     */
    getPatientRestraintCareMainID() {
      let patientRestraintCareMainID = "";
      if (this.patientRestraintCareMainID) {
        patientRestraintCareMainID = this.patientRestraintCareMainID.includes("temp")
          ? this.patientRestraintCareMainID.split("_")[1]
          : this.patientRestraintCareMainID;
      }
      return patientRestraintCareMainID;
    },
    /**
     * description: 点击选中主记录，展示维护记录
     * param {*} row 当前行数据
     * return {*}
     */
    async recordClick(row) {
      this.currentRecord = row;
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.recordData = [row];
        this.careMainAddFlag = !row.endDate;
        this.getCareMainData();
        await this.getTableHeaderList();
      } else {
        this.getRecordData();
      }
    },
    /**
     * description: 弹窗开关
     * param {*} flag 开关动作
     * param {*} title 弹窗标题
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 权限检核
     * param {*} id 主记录/主表ID
     * param {*} tableName
     * return {*}
     */
    async checkAuthor(id, tableName, userID) {
      this.checkResult = await this._common.checkActionAuthorization(this.user, userID);
      if (!this.checkResult) {
        this.showEditButton = false;
        this._showTip("warning", "非本人不可操作");
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, tableName, !!this.refillFlag);
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
    },
    /**
     * description: 获取约束同意配置
     * param {*}
     * return {*}
     */
    getConsentDefaultSetting() {
      let param = {
        settingTypeCode: "RestraintConsentFlag",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.defaultConsentFlag = response.data;
        }
      });
    },
    /**
     * description: 检核约束开始时间
     * param {*}
     * return {*}
     */
    checkRestraintDate() {
      if (this.recordData.length === 0) return true;

      const lastEndRestraint = this.recordData.find((element) => element.endDate != null);
      if (lastEndRestraint) {
        const lastEndDateTime = this._datetimeUtil.toDate(
          `${this._datetimeUtil.formatDate(lastEndRestraint.endDate, "yyyy-MM-dd")} ${lastEndRestraint.endTime}`
        );
        const assessDateTime = this._datetimeUtil.toDate(
          `${this._datetimeUtil.formatDate(this.assessDate, "yyyy-MM-dd")} ${this.assessTime}`
        );

        return lastEndDateTime <= assessDateTime;
      }
      return true;
    },
    /**
     * description: 获取约束医嘱开嘱医生
     * param {*} scheduleMainID 跳转排程ID
     * return {*}
     */
    getRestraintOrder(scheduleMainID) {
      let param = {
        scheduleMainID: scheduleMainID,
        inpatientID: this.patientInfo.inpatientID,
      };
      GetRestraintOrder(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.doctorID = response.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.restraint {
  .base-layout {
    .date-picker {
      width: 160px;
    }
    .time-picker {
      width: 100px;
    }
    .frequency-selector {
      width: 110px;
    }
    .consent-flag {
      margin-left: 20px;
    }
    .el-checkbox {
      margin-right: 20px;
    }
    .employee-info {
      margin-left: 20px;
    }
    .base-layout-content {
      overflow: hidden;
      height: 100%;
    }
  }
}
</style>
