<!--
 * FilePath     : \src\pages\schedule\components\scheduleTypes\scheduleAssess.vue
 * Author       : 苏军志
 * Date         : 2022-05-08 11:27
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-15 19:49
 * Description  : 评估类执行页面
 * CodeIterationRecord:
 2811-作为数据配置人员，我需要合并监测生命体征和心电监护措施内容，以利临床排程操作 2022-08-20 En
-->

<template>
  <baseLayout
    class="schedule-assess"
    v-enter="{ function: 'saveScheduleAssess' }"
    v-loading.fullscreen.lock="loading"
    :element-loading-text="loadingText"
    :showFooter="!hideOperate"
    headerHeight="auto"
  >
    <div slot="header">
      <div class="header-item">
        <span class="label">执行日期：</span>
        <el-date-picker
          v-model="scheduleDate"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          :clearable="false"
          placeholder="日期"
          class="assess-date"
        ></el-date-picker>
      </div>
      <div class="header-item">
        <span class="label">执行时间：</span>
        <el-time-picker
          v-model="scheduleTime"
          value-format="HH:mm"
          format="HH:mm"
          :clearable="false"
          class="assess-time"
        ></el-time-picker>
      </div>
      <el-button
        v-if="showClinicBtnFlag"
        class="clinic-btn"
        type="primary"
        icon="iconfont icon-clinic1"
        @click="showClinicDialog()"
      >
        仪器数据
      </el-button>
    </div>
    <div class="assess-wrap">
      <tabs-layout
        ref="tabsLayout"
        class="schedule"
        model="add"
        :template-list="templateDatas"
        check-flag
        @change-values="changeValues"
        @checkTN="checkTN"
        @button-click="buttonClick"
      />
      <div v-if="notDialog" class="not-dialog">
        <el-checkbox class="bring-checkbox" v-model="informPhysician">通知医师</el-checkbox>
        <el-checkbox class="bring-checkbox" v-model="bringToShift">带入交班</el-checkbox>
        <el-checkbox class="bring-checkbox" v-model="bringToNursingRecords">带入护理记录单</el-checkbox>
      </div>
      <div class="perform-comment">
        <span>执行说明：</span>
        <el-input
          v-model="performComment"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 4 }"
          resize="none"
          placeholder="请输入内容"
        ></el-input>
      </div>
    </div>
    <div slot="footer">
      <el-checkbox class="bring-checkbox" v-model="informPhysician">通知医师</el-checkbox>
      <el-checkbox class="bring-checkbox" v-model="bringToShift">带入交班</el-checkbox>
      <el-checkbox class="bring-checkbox" v-model="bringToNursingRecords">带入护理记录单</el-checkbox>
      <el-button @click="close(false)">取消</el-button>
      <el-button type="primary" @click="saveScheduleAssess()">确定</el-button>
    </div>
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      fullscreen
      :modal="false"
      custom-class="link no-footer"
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog"></iframe>
    </el-dialog>
    <!--仪器数据-->
    <el-drawer
      title="仪器数据"
      :modal-append-to-body="false"
      :visible.sync="showClinicFlag"
      :destroy-on-close="true"
      direction="btt"
      size="50%"
      custom-class="clinic-drawer"
      :wrapperClosable="false"
    >
      <clinic-view v-model="timeRange" :clinicPrams="clinicPrams" @getSelectClinicData="postClinicData"></clinic-view>
    </el-drawer>
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      fullscreen
      custom-class="link no-footer"
      append-to-body
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
    </el-dialog>
  </baseLayout>
</template>
<script>
import { GetScheduleAssessView, SaveScheduleAssess } from "@/api/PatientSchedule";
import { GetButtonData } from "@/api/Assess";
import clinicView from "@/pages/schedule/components/scheduleTypes/clinicView";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import { GetSettingValuesByTypeCodeAndValue } from "@/api/Setting";
import { mapGetters } from "vuex";
export default {
  components: {
    tabsLayout,
    baseLayout,
    clinicView,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patient: "getPatientInfo",
      token: "getToken",
    }),
  },
  data() {
    return {
      templateDatas: [],
      scheduleDate: "",
      scheduleTime: "",
      bringToNursingRecords: "1",
      bringToShift: "0",
      performComment: "",
      scheduleValue: [],
      loading: false,
      loadingText: "加载中……",
      checkTNFlag: true,
      informPhysician: undefined,
      showButtonDialog: false,
      buttonName: undefined,
      sourceType: "Schedule",
      //获取仪器数据所需params
      clinicPrams: {},
      showClinicFlag: false,
      //默许取得仪器时间范围
      timeRange: 30,
      //仪器数据按钮显示标记
      showClinicBtnFlag: false,
      painScoreThreshold: undefined,
    };
  },
  props: {
    params: {
      type: Object,
      require: true,
    },
    hideOperate: {
      type: Boolean,
      default: false,
    },
    notDialog: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    params: {
      immediate: true,
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.init();
        }
      },
    },
    /**
     * description: 监听A类弹窗开关变量
     * param {*} newVal
     * param {*} oldVal
     * return {*}
     */
    showButtonDialog(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    async init() {
      this.showClinicBtnFlag = this.params.batchMonitorFlag;
      this.scheduleDate = this.params.performDate;
      this.scheduleTime = this.params.performTime;
      this.bringToNursingRecords = this.params.bringToNursingRecord;
      this.bringToShift = this.params.bringToShift;
      this.performComment = this.params.performComment;
      this.painScoreThreshold = this.params.painScoreThreshold;
      this.loading = true;
      this.loadingText = "加载中……";
      this.informPhysician = this.params.informPhysician;
      await this.getSettingTimeRange();
      let params = {
        interventionID: this.params.interventionID,
        patientScheduleMainID: this.params.patientScheduleMainID,
        inpatientID: this.params.inpatientID,
      };
      GetScheduleAssessView(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.templateDatas = res.data;
        }
      });
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, this.params.performEmployeeID);
    },
    /**
     * description: 排程保存
     * param {*}
     * return {*}
     */
    saveScheduleAssess() {
      if (this.loading) {
        return;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return;
      }
      this.loading = true;
      this.loadingText = "保存中……";
      let params = this.getSaveData();
      SaveScheduleAssess(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          if (result.data) {
            let triggerParams = {
              triggerDate: this._datetimeUtil.formatDate(result.data.triggerDate, "yyyy-MM-dd"),
              triggerTime: result.data.triggerTime,
              triggerDatas: result.data.attachedIntervention,
              index: Math.random(),
            };
            this.close(false, triggerParams);
          } else {
            this._showTip("success", "执行成功！");
            this.close(true);
          }
        }
      });
    },
    /**
     * description: 获取保存参数
     * 单病人批量监测画面调用，不可改名，不可删除
     * param {*}
     * return {*}
     */
    getSaveData() {
      let params = {
        inPatientID: this.params.inpatientID,
        patientAssessMainID: this.params.patientAssessMainID,
        patientInterventionID: this.params.patientInterventionID,
        patientScheduleMainID: this.params.patientScheduleMainID,
        scheduleDate: this.scheduleDate,
        scheduleTime: this.scheduleTime,
        bringToShift: this.bringToShift ? "1" : "0",
        bringToNursingRecords: this.bringToNursingRecords ? "1" : "0",
        performComment: this.performComment,
        saveData: this.getDetails(),
        informPhysician: this.informPhysician,
        // ICU单病人批量执行会用到，勿删，不影响原来保存逻辑  2022-06-01  SJZ
        interventionID: this.params.interventionID,
      };
      return params;
    },
    /**
     * description: 保存明细组装
     * param {*}
     * return {*}
     */
    getDetails() {
      let details = [];
      if (this.scheduleValue) {
        this.scheduleValue.forEach((element) => {
          let saveFlag = true;
          //疼痛明细排除
          if (element.controlerType != "B") {
            let template = {
              assessListID: element.assessListID,
              assessValue: element.assessValue,
              assessListGroupID: element.assessListGroupID,
            };
            if (element.controlerType == "C" || element.controlerType == "R") {
              template.assessValue = element.itemName;
            } else {
              template.assessValue = element.assessValue;
            }
            if ((element.controlerType == "T" || element.controlerType == "TN") && !element.assessValue.trim()) {
              saveFlag = false;
            }
            if (saveFlag) {
              details.push(template);
            }
          }
        });
      }
      return details;
    },

    /**
     * description: 按钮类跳转
     * param {*} content
     * return {*}
     */
    buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      if (url.indexOf("?") == -1) {
        url += "?sourceID=" + this.params.patientScheduleMainID;
      } else {
        url += "&sourceID=" + this.params.patientScheduleMainID;
      }
      url +=
        "&sourceType=" +
        this.sourceType +
        `&patientScheduleMainID=${this.params.patientScheduleMainID}` +
        "&bedNumber=" +
        this.patient.bedNumber.replace(/\+/g, "%2B") +
        "&userID=" +
        this.user.userID +
        "&token=" +
        this.token +
        "&isDialog=true";
      this.showButtonDialog = true;

      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 添加完更新按钮数据
     * param {*} assessListID
     * return {*}
     */
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      for (let i = 0; i < this.templateDatas.length; i++) {
        for (let j = 0; j < this.templateDatas[i].groups.length; j++) {
          var group = this.templateDatas[i].groups[j];
          if (group.controlerType.trim() == "B" || group.controlerType.trim() == "BR") {
            if (group.assessListID == assessListID) {
              this.$set(group, "assessValue", item.assessValue);
              this.$set(group, "linkForm", item.linkForm);
              return;
            }
          }
          for (let k = 0; k < group.contents.length; k++) {
            var content = group.contents[k];
            if (content.controlerType.trim() == "B" || content.controlerType.trim() == "BR") {
              if (content.assessListID == assessListID) {
                this.$set(content, "assessValue", item.assessValue);
                this.$set(content, "linkForm", item.linkForm);
                return;
              }
            }
            // 判断三阶里是否有按钮
            if (content.childList) {
              for (let m = 0; m < content.childList.length; m++) {
                var child = content.childList[m];
                if (child.controlerType.trim() == "B" || child.controlerType.trim() == "BR") {
                  if (child.assessListID == assessListID) {
                    this.$set(child, "assessValue", item.assessValue);
                    this.$set(child, "linkForm", item.linkForm);
                    return;
                  }
                }
              }
            }
          }
        }
      }
    },
    /**
     * description: 更新按钮回显数据
     * param {*} assessListID
     * return {*}
     */
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.params.performUrl,
        assessListID: assessListID,
        sourceID: this.params.patientScheduleMainID,
        sourceType: this.sourceType,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: 窗口关闭
     * param {*} flag
     * param {*} triggerParams
     * return {*}
     */
    close(flag, triggerParams) {
      this.$emit("close", flag, triggerParams);
    },
    /**
     * description: 获取评估组件勾选内容
     * param {*} val
     */
    changeValues(val) {
      this.scheduleValue = val;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 打开仪器数据弹窗
     */
    showClinicDialog() {
      //组装获取仪器数据所需params
      this.clinicPrams = {
        inpatientID: this.patient.inpatientID,
        scheduleDate: this._datetimeUtil.formatDate(this.scheduleDate, "yyyy-MM-dd"),
        scheduleTime: this._datetimeUtil.formatDate(this.scheduleTime, "hh:mm:ss"),
        interventionID: this.params.interventionID,
      };
      this.showClinicFlag = true;
    },
    /**
     * description: 勾选仪器数据事件,回传仪器数据到监测表格数据
     * param {*} value
     */
    postClinicData(value) {
      //Object.entries 将对象变为数组形式为[[key,value],[key,value]]
      let clinicDataArr = Object.entries(value);
      // 只需要value非空的项
      clinicDataArr = clinicDataArr.filter((item) => item[1]);
      //评估模版赋值
      clinicDataArr.forEach((item) => {
        this.templateDatas.forEach(({ groups }) => {
          this.loopAddData(item, groups);
        });
      });
      this.$refs.tabsLayout.init();
      this.showClinicFlag = false;
    },
    /**
     * description: 仪器数据回显评估模板
     * param {*} item
     * param {*} data
     */
    loopAddData(item, data) {
      for (let index = 0; index < data.length; index++) {
        const detail = data[index];
        if (detail.assessListID == item[0]) {
          this.$set(detail, "assessValue", item[1]);
          if (["R", "C"].includes(detail.controlerType)) {
            this.$set(detail, "assessValue", "1");
          }
          continue;
        }
        if (detail.childList?.length) {
          this.loopAddData(item, detail.childList);
        }
        if (detail.contents?.length) {
          this.loopAddData(item, detail.contents);
        }
      }
    },

    /**
     * @description: 获取仪器数据范围配置
     * @return
     */
    getSettingTimeRange() {
      const params = {
        settingTypeCode: "ClinicDataTimeRange",
        typeValue: "TimeRange",
      };
      GetSettingValuesByTypeCodeAndValue(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.timeRange = Number(result?.data ?? 30);
        }
      });
    },
  },
};
</script>
<style lang="scss">
.schedule-assess.base-layout {
  background-color: #ffffff;
  padding: 0 10px 5px 10px;
  box-sizing: border-box;
  .base-header {
    margin-bottom: 0;
    .header-item {
      display: inline-block;
      .label {
        margin-left: 10px;
      }
      .assess-date,
      .assess-time {
        width: 110px;
      }
      .clinic-btn {
        float: right;
        margin-top: 8px;
      }
    }
  }
  .assess-wrap {
    height: 100%;
    min-height: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .schedule {
      width: 100%;
      flex: auto;
      overflow: hidden;
    }
    .not-dialog .bring-checkbox {
      float: left;
      margin-top: 10px;
      margin-left: 5px;
    }
    .perform-comment {
      display: flex;
      width: 100%;
      padding: 5px 5px 0 5px;
      box-sizing: border-box;
      > span {
        white-space: nowrap;
      }
    }
  }
  .bring-checkbox {
    margin-right: 10px;
  }
}
.link.el-dialog .el-dialog__body {
  background-color: #f3f3f3;
  iframe {
    height: 99%;
    width: 100%;
    border: 0;
    overflow: hidden;
  }
}
</style>
