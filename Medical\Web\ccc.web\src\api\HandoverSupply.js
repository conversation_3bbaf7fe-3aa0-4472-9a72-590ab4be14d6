/*
 * FilePath     : \src\api\HandoverSupply.js
 * Author       : 孟昭永
 * Date         : 2021-04-06 10:42
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:45
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/HandoverSupply";

export const urls = {
  GetPatientHandoverList: baseUrl + "/GetPatientHandoverList",
  SaveHandoverSupplement: baseUrl + "/SaveHandoverSupplement",
  DeleteHandoverSupplement: baseUrl + "/DeleteHandoverSupplement",
  GetSBARByHandoverID: baseUrl + "/GetSBARByHandoverID"
};

//取得病人交接班补录清单
export const GetPatientHandoverList = params => {
  return http.get(urls.GetPatientHandoverList, params);
};

//保存病人交接班补录数据
export const SaveHandoverSupplement = params => {
  return http.post(urls.SaveHandoverSupplement, params);
};

//删除病人交接班补录数据
export const DeleteHandoverSupplement = params => {
  return http.post(urls.DeleteHandoverSupplement, qs.stringify(params));
};

//根据交班ID获取患者SBAR数据
export const GetSBARByHandoverID = params => {
  return http.get(urls.GetSBARByHandoverID, params);
};
