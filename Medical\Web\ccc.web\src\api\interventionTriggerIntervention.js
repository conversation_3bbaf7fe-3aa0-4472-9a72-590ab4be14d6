/*
 * FilePath     : \src\api\interventionTriggerIntervention.js
 * Author       : 郭鹏超
 * Date         : 2023-08-08 23:23
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-04-19 11:19
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/InterventionTriggerIntervention";

export const urls = {
  GetTriggerInterventionTableData: baseUrl + "/GetTriggerInterventionTableData",
  GetTriggerInterventionSelectOption:
    baseUrl + "/GetTriggerInterventionSelectOption",
  SaveTriggerIntervention: baseUrl + "/SaveTriggerIntervention",
  DeleteTriggerIntervention: baseUrl + "/DeleteTriggerIntervention"
};
// 获取触发措施表数据
export const GetTriggerInterventionTableData = params => {
  return http.get(urls.GetTriggerInterventionTableData, params);
};
// 获取触发措施页面下拉框配置
export const GetTriggerInterventionSelectOption = params => {
  return http.get(urls.GetTriggerInterventionSelectOption, params);
};
//触发措施保存
export const SaveTriggerIntervention = params => {
  return http.post(urls.SaveTriggerIntervention, params);
};
//触发措施删除
export const DeleteTriggerIntervention = params => {
  return http.post(urls.DeleteTriggerIntervention, qs.stringify(params));
};
