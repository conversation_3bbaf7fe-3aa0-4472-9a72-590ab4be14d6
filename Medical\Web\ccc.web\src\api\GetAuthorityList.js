/*
 * FilePath     : \ccc.web\src\api\GetAuthorityList.js
 * Author       : 王浩杰
 * Date         : 2021-08-30 20:36
 * LastEditors  : 王浩杰
 * LastEditTime : 2021-10-30 09:15
 * Description  :权限列表
 */
import http from "../utils/ajax";
const baseUrl = "/Authority";

export const urls = {
  GetAuthorityList: baseUrl + "/GetAuthorityList",
  DeleteAuthorityListById: baseUrl + "/DeleteAuthorityListById",
  GetMenuTree: baseUrl + "/GetMenuTree",
  GetAuthorityListById: baseUrl + "/GetAuthorityListById",
  UpdataAuthorityListById: baseUrl + "/UpdataAuthorityListById",

  AddAuthorityList: baseUrl + "/AddAuthorityList"
};
//获取权限列表
export const GetAuthorityList = () => {
  return http.get(urls.GetAuthorityList);
};
// 删除权限列表
export const DeleteAuthorityListById = params => {
  return http.post(urls.DeleteAuthorityListById, params);
};
//获取权限功能树
export const GetMenuTree = params => {
  return http.get(urls.GetMenuTree, params);
};
//查询单个权限信息
export const GetAuthorityListById = params => {
  return http.get(urls.GetAuthorityListById, params);
};
//修改权限
export const UpdataAuthorityListById = params => {
  return http.post(urls.UpdataAuthorityListById, params);
};
//添加权限
export const AddAuthorityList = params => {
  return http.post(urls.AddAuthorityList, params);
};
