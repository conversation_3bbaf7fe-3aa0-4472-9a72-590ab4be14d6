<!--
 * FilePath     : \ccc.web\src\autoPages\patientList\components\patientBed2.vue
 * Author       : 苏军志
 * Date         : 2020-04-15 16:41
 * LastEditors  : LX
 * LastEditTime : 2025-05-09 18:38
 * Description  : 病人卡片组件2
 -->
<template>
  <div :class="['patient-bed-two', { 'is-select': focusOn }, { 'filter-mask': patientInfo.filterMaskFlag }]">
    <div :class="['patient-info', { 'is-danger': patientInfo.isDanger }]">
      <tip-marker
        v-if="jobTipList && jobTipList.length > 0"
        class="job-tip"
        :width="100"
        :tipList="jobTipList"
        @jumpPage="tipJumpPage"
      ></tip-marker>
      <div class="info bed-number">
        {{ patientInfo.bedNumber + (patientInfo.bedRemark ? " - " + patientInfo.bedRemark : "") }}
      </div>
      <div v-if="!emptyBed">
        <div class="info">{{ patientInfo.localCaseNumber }}</div>
        <div v-if="isPrintable" class="info is-print" :title="patientInfo.patientName.trim()" @click="bedsideCard">
          {{ patientInfo.patientName.trim() }}
        </div>
        <div v-else class="info patient-name" :title="patientInfo.patientName.trim()">
          {{ patientInfo.patientName.trim() }}
        </div>
        <div class="info">
          {{ patientInfo.gender + " " + (patientInfo.ageDetail ? patientInfo.ageDetail : "") }}
        </div>
        <div class="info diagnose" :title="patientInfo.diagnose">
          {{ patientInfo.diagnose ? patientInfo.diagnose : "&nbsp;" }}
        </div>
        <div class="info-last" v-if="!emptyBed">
          <div :class="['days', { empty: !patientInfo.days || patientInfo.days == 0 }]" title="住院天数">
            {{ patientInfo.days ? patientInfo.days : 0 }}
          </div>
          <div :class="['surgery', { empty: patientInfo.surgerydays == '0' }]" title="手术天数">
            {{ patientInfo.surgerydays == 999999 || !patientInfo.surgerydays == null ? "" : patientInfo.surgerydays }}
          </div>
          <div
            class="nursing"
            v-if="patientInfo.inPatientNursingLevelStyle && patientInfo.inPatientNursingLevelStyle.identifyID != 2742"
            :style="getNursingStyle(patientInfo.inPatientNursingLevelStyle)"
          >
            {{ patientInfo.inPatientNursingLevelStyle.remark }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="!emptyBed" class="mark-list">
      <template v-for="(item, index) in patientInfo.inPatientMarkStyleList">
        <patient-mark v-if="item.display" :key="index" :markStyle="item" type="2" :showGroup="showGroup"></patient-mark>
      </template>
    </div>
  </div>
</template>

<script>
import tipMarker from "./tipMarker";
import patientMark from "./patientMark";
export default {
  components: { tipMarker, patientMark },
  props: {
    patientInfo: {},
    focusOn: { type: Boolean, default: false },
    jobTipList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    isPrintable: {
      type: Boolean,
      default: false,
    },
    showGroup: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    this.emptyBed = this.patientInfo.chartNo == "" && this.patientInfo.caseNumber == "";
  },
  data() {
    return {
      emptyBed: true,
    };
  },

  methods: {
    getNursingStyle(nursingStyle) {
      if (!nursingStyle) return;
      return {
        color: nursingStyle.iconColor,
        backgroundColor: nursingStyle.backGroundColor,
      };
    },

    tipJumpPage(router) {
      if (!router) {
        return;
      }
      this.$emit("tipJumpPage", router);
    },
    //床头卡事件
    bedsideCard() {
      this.$emit("bedside-card", this.patientInfo);
    },
  },
};
</script>
<style lang="scss">
.patient-bed-two {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 250px;
  padding: 3px;
  border: 3px solid #f3f3f3;
  background-color: #ffffff;
  font-size: 20px;
  box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
  &.is-select {
    border-color: $base-color;
    background-color: #ebf7df;
  }
  &.filter-mask,
  &.filter-mask * {
    color: #ffffff !important;
    background-color: #e3e3e3 !important;
  }
  &:hover {
    background-color: #ebf7df;
  }
  .patient-info {
    position: relative;
    flex: auto;
    height: 100%;
    box-sizing: border-box;
    border-left: 5px solid transparent;
    &.is-danger {
      border-left-color: #ff0000;
    }
    .job-tip .tip-marker {
      top: -4px;
      left: 4px;
    }
    .info {
      margin-bottom: 5px;
      margin-left: 5px;
      line-height: 26px;
      &.bed-number {
        margin-left: 40px;
        font-size: 24px;
        font-weight: bold;
      }
      &.is-print:hover {
        text-decoration: underline;
        cursor: pointer;
      }
      &.patient-name,
      &.diagnose {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .info-last {
      margin-left: 0px;
      display: flex;
      font-size: 16px;
      div {
        height: 24px;
        line-height: 24px;
        padding: 3px;
        margin: 2px;
        text-align: center;
        border-radius: 5px;
        background-color: #dddddd;
        &.days,
        &.surgery {
          width: 45px;
          font-weight: 600;
        }
        &.nursing {
          width: 40px;
          line-height: 24px;
          letter-spacing: 2px;
          overflow: hidden;
          text-align: center;
        }
        &.empty {
          color: #ffffff;
          background-color: blue;
        }
      }
    }
  }
  .mark-list {
    width: 100%;
    max-height: 64px;
    line-height: 24px;
    padding: 2px 0;
    box-sizing: border-box;
    cursor: default;
  }
}
</style>
