/*
 * FilePath     : \src\api\NursingPlanSupplement.js
 * Author       : 郭鹏超
 * Date         : 2021-08-14 11:00
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:46
 * Description  :护理计划补录
 */
import http from "../utils/ajax";
const baseUrl = "/NursingPlanSupplement";

export const urls = {
  GetSupplementProblemList: baseUrl + "/GetSupplementProblemList",
  UpdateProblemDate: baseUrl + "/UpdateProblemDate",
  DeleteProblem: baseUrl + "/DeleteProblem"
};
// 获取护理问题记录
export const GetSupplementProblemList = params => {
  return http.get(urls.GetSupplementProblemList, params);
};
//更新问题时间
export const UpdateProblemDate = params => {
  return http.get(urls.UpdateProblemDate, params);
};
//删除护理问题
export const DeleteProblem = params => {
  return http.get(urls.DeleteProblem, params);
};
