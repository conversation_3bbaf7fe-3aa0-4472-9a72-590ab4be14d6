/*
 * FilePath     : \src\api\PatientTransfusion.js
 * Author       : 郭鹏超
 * Date         : 2021-04-12 19:03
 * LastEditors  : 张现忠
 * LastEditTime : 2025-05-24 11:32
 * Description  : 输血专项护理接口
 */
import http from "../utils/ajax";
const baseUrl = "/Transfusion";

export const urls = {
  GetTransfusionRecordByInpatientID:
    baseUrl + "/GetTransfusionRecordByInpatientID",
  GetTransfusionCareMainByTransfusionID:
    baseUrl + "/GetTransfusionCareMainByTransfusionID",
  GetTransfusionRecordsCodeInfo: baseUrl + "/GetTransfusionRecordsCodeInfo",
  GetTransfusionAssessView: baseUrl + "/GetTransfusionAssessView",
  GetBloodDonorBagsCodeListByInpatientID:
    baseUrl + "/GetBloodDonorBagsCodeListByInpatientID",
  SavePatientTransfusionRecord: baseUrl + "/SavePatientTransfusionRecord",
  SavePatientTransfusionCareMain: baseUrl + "/SavePatientTransfusionCareMain",
  //输血结束
  StopTransfusionRecord: baseUrl + "/StopTransfusionRecord",
  //删除输血主记录
  DeleteTransfusionRecord: baseUrl + "/DeleteTransfusionRecord",
  //删除输血巡视
  DeleteTransfusionCareMain: baseUrl + "/DeleteTransfusionCareMain",
  GetCurrentTransfusionRecord: baseUrl + "/GetCurrentTransfusionRecord",
  GetTransfusionRecordsCheckForm: baseUrl + "/GetTransfusionRecordsCheckForm",
  //  获取血袋列表
  GetBloodBagMainList: baseUrl + "/GetBloodBagMainList",
  // 检核血袋是否已经接收
  CheckBloodIsReception: baseUrl + "/CheckBloodIsReception",
  // 血袋接收
  SaveBloodReception: baseUrl + "/SaveBloodReception",
  // 血袋回收
  SaveBloodRecycle: baseUrl + "/SaveBloodRecycle",
  //  模拟获取扫码后的信息并检核
  GetScannedBloodBagMainData: baseUrl + "/GetScannedBloodBagMainData",
};
//获取输血主记录
export const GetTransfusionRecordByInpatientID = (params) => {
  return http.get(urls.GetTransfusionRecordByInpatientID, params);
};
//获取维护主记录
export const GetTransfusionCareMainByTransfusionID = (params) => {
  return http.get(urls.GetTransfusionCareMainByTransfusionID, params);
};
//获取输血对应的DepartmentToAssessInfo记录
export const GetTransfusionRecordsCodeInfo = (params) => {
  return http.get(urls.GetTransfusionRecordsCodeInfo, params);
};
//获取输血评估评估模板
export const GetTransfusionAssessView = (params) => {
  return http.get(urls.GetTransfusionAssessView, params);
};
//获得病人血袋码集合
export const GetBloodDonorBagsCodeListByInpatientID = (params) => {
  return http.get(urls.GetBloodDonorBagsCodeListByInpatientID, params);
};
//保存病人主记录
export const SavePatientTransfusionRecord = (params) => {
  return http.post(urls.SavePatientTransfusionRecord, params);
};
//保存病人输血维护记录
export const SavePatientTransfusionCareMain = (params) => {
  return http.post(urls.SavePatientTransfusionCareMain, params);
};

//输血结束
export const StopTransfusionRecord = (params) => {
  return http.post(urls.StopTransfusionRecord, params);
};

//删除输血主记录
export const DeleteTransfusionRecord = (params) => {
  return http.get(urls.DeleteTransfusionRecord, params);
};

//删除输血巡视
export const DeleteTransfusionCareMain = (params) => {
  return http.get(urls.DeleteTransfusionCareMain, params);
};
//排程跳转后获取输血主记录
export const GetCurrentTransfusionRecord = (params) => {
  return http.get(urls.GetCurrentTransfusionRecord, params);
};
// 查询员工所在病区输血登记单
export const GetTransfusionRecordsCheckForm = (params) => {
  return http.get(urls.GetTransfusionRecordsCheckForm, params);
};
//  获取血袋列表
export const GetBloodBagMainList = (params) => {
  return http.get(urls.GetBloodBagMainList, params);
};
// 检核血袋是否已经接收
export const CheckBloodIsReception = (params) => {
  return http.get(urls.CheckBloodIsReception, params);
};
// 血袋接收
export const SaveBloodReception = (params) => {
  return http.post(urls.SaveBloodReception, params);
};
// 血袋回收
export const SaveBloodRecycle = (params) => {
  return http.post(urls.SaveBloodRecycle, params);
};
//  模拟获取扫码后的信息并检核
export const GetScannedBloodBagMainData = (params) => {
  return http.get(urls.GetScannedBloodBagMainData, params);
};
