<!--
 * FilePath     : \src\autoPages\monitoringScheduler\component\monitorScheduleHeader.vue
 * Author       : 杨欣欣
 * Date         : 2024-06-13 17:31
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-08-23 11:36
 * Description  : 多病人监测排程表头部
 * CodeIterationRecord: 
 -->
<template>
  <div class="monitor-schedule-header">
    <div class="left">
      <template v-if="params">
        <span>班别日期：{{ params.scheduleDate }}</span>
        <span>班别：{{ params.shiftName }}</span>
      </template>
      <template v-else>
        <div>
          <label>显示全科：</label>
          <el-switch v-model="showAll" @change="$emit('change', headerParameters)" />
        </div>
        <div v-if="notPerformToggleEnable">
          <label>未执行：</label>
          <el-switch v-model="showNotPerform" />
        </div>
        <div>
          <label>日期：</label>
          <el-date-picker
            type="date"
            v-model="scheduleDate"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            class="date-picker"
          />
        </div>
        <shift-selector :stationID="stationID" v-model="shiftID" />
        <shift-times-selector @select="changeShiftTimes" width="160" :shiftID="shiftID" />
        <el-button @click="$emit('change', headerParameters)" class="query-button" icon="iconfont icon-search">
          查询
        </el-button>
      </template>
    </div>
    <div class="right">
      <template v-if="!params">
        <template v-if="tableDataLength">
          <label>执行时间：</label>
          <el-date-picker
            v-if="tableDataLength"
            v-model="performDateTime"
            value-format="yyyy-MM-dd HH:mm"
            format="yyyy-MM-dd HH:mm"
            type="datetime"
            class="date-time-picker"
            @change="(performDateTime) => $emit('change-date', performDateTime)"
            :picker-options="dateOptions"
            placeholder="选择日期时间"
          ></el-date-picker>
        </template>
        <el-button v-if="showPDFButton" class="print-button" icon="iconfont icon-print" @click="getBatchMonitoringPDF">
          批量监测排程表
        </el-button>
      </template>
      <el-button type="primary" class="save-button" icon="iconfont icon-save-button" @click="$emit('save')">
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { GetBatchMonitoringPDF } from "@/api/Document";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetOneSettingByTypeAndCode } from "@/api/Setting";
import shiftSelector from "@/components/selector/shiftSelector";
import shiftTimesSelector from "@/components/selector/shiftTimesSelector";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  components: {
    shiftSelector,
    shiftTimesSelector,
  },
  data() {
    return {
      showAll: false,
      notPerformToggleEnable: false,
      showNotPerform: false,
      scheduleDate: "",
      stationID: "",
      shiftID: "",
      shiftTimes: undefined,
      performDateTime: this._datetimeUtil.getNowDate("yyyy-MM-dd hh:mm"),
      showPDFButton: false,
    };
  },
  computed: {
    headerParameters() {
      return {
        stationID: this.stationID,
        showAll: this.showAll,
        showNotPerform: this.showNotPerform,
        scheduleDate: this.scheduleDate,
        shiftID: this.shiftID,
        shiftTimes: this.shiftTimes,
      };
    },
  },
  props: {
    // 表格数据长度
    tableDataLength: {
      type: Number,
      required: true,
    },
    // 单病人监测排程表入参，若有显示单病人的头部
    params: {
      type: Object,
      default: () => undefined,
    },
    // 日期选择控件检核
    dateOptions: {
      type: Object,
      required: true,
    },
  },
  async created() {
    this.getShowPDFButtonSetting();
    this.getNotPerformSwitch();
    await this.getNowShiftData();
  },
  methods: {
    /**
     * @description: 获取当前班别信息
     * @return
     */
    async getNowShiftData() {
      const params = {
        index: Math.random(),
      };
      const res = await GetNowStationShiftData(params);
      if (this._common.isSuccess(res)) {
        const { nowShift, shiftDate } = res.data;
        this.stationID = nowShift.stationID;
        this.scheduleDate = this._datetimeUtil.formatDate(shiftDate, "yyyy-MM-dd");
      }
    },
    /**
     * @description: 切换班别时间
     * @param newShiftTimes 新的班别时间
     * @return
     */
    changeShiftTimes(newShiftTimes) {
      this.shiftTimes = newShiftTimes;
      this.$emit("change", this.headerParameters);
    },
    /**
     * @description: 获取查看批量监测排程表按钮开关配置
     * @return
     */
    getShowPDFButtonSetting() {
      const param = {
        SettingTypeCode: "BatchMonitoringPtintPDFFlag",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.showPDFButton = response.data;
        }
      });
    },
    /**
     * @description: 获取批量监测排程PDF
     * @return
     */
    getBatchMonitoringPDF() {
      const params = {
        scheduleDate: this.scheduleDate,
        shiftID: this.shiftID,
        startTime: this.shiftTimes.split("-")[0],
        endTime: this.shiftTimes.split("-")[1],
      };
      GetBatchMonitoringPDF(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (!res.data.includes("pdf")) {
            this._showTip("warning", "当前时间段没有未执行批量监测排程数据！");
            return;
          }
          this.$emit("open-pdf", res.data);
        }
      });
    },
    /**
     * @description: 获取是否显示未执行开关配置
     * @return
     */
    getNotPerformSwitch() {
      const params = {
        settingType: 277,
        settingCode: "NotPerformSwitch",
      };
      GetOneSettingByTypeAndCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data.typeValue == "True") {
            this.notPerformToggleEnable = true;
          }
        }
      });
    },
  },
};
</script>

<style lang="scss">
.monitor-schedule-header {
  display: flex;
  justify-content: space-between;
  .left,
  .right {
    display: flex;
    align-items: center;
    gap: 8px;
    .date-picker {
      width: 160px;
    }
  }
}
</style>