<!--
 * FilePath     : \src\autoPages\handover\components\handoverCommon.vue
 * Author       : 郭鹏超
 * Date         : 2023-03-27 11:02
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-17 15:22
 * Description  : 交班补录公共页面
 * CodeIterationRecord: 
-->
<template>
  <div class="handover-common-page">
    <el-tabs class="tabs" v-model="activeName">
      <el-tab-pane
        v-for="(component, index) in components"
        :key="index"
        :label="component.label"
        :name="component.name"
      ></el-tab-pane>
    </el-tabs>
    <div class="pane-content">
      <component v-if="patientInfo" :is="activeName" :supplemnentPatient="patientInfo"></component>
    </div>
  </div>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData";
import transferHandover from "@/autoPages/handover/transferHandover.vue";
import dischargeHandover from "@/autoPages/handover/dischargeHandover.vue";
import operationHandover from "@/autoPages/handover/operationHandover/index.vue";
import shiftHandover from "@/autoPages/handover/shiftHandover/index.vue";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    searchPatientData,
    transferHandover,
    dischargeHandover,
    operationHandover,
    shiftHandover,
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      showFlag: true,
      activeName: "transferHandover",
      components: [
        {
          label: "转运交接",
          name: "transferHandover",
        },
        {
          label: "出院小结",
          name: "dischargeHandover",
        },
        {
          label: "手术转运",
          name: "operationHandover",
        },
        {
          label: "班别班内交接",
          name: "shiftHandover",
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      hospitalInfo: "getHospitalInfo",
    }),
  },
  watch: {
    "patientInfo.inpatientID": {
      handler(newVal) {},
      immediate: true,
    },
  },
  created() {
    this.filterComponents();
  },
  methods: {
    /**
     * @description: 中山补录只显示转运交接  处理中山单独上转运交接的问题 后续新版交班全部上线 统一删除
     * @return
     */
    filterComponents() {
      if (this.hospitalInfo.hospitalID !== "2") {
        return;
      }
      this.components = this.components.filter((component) => component.name === "transferHandover");
    },
  },
};
</script>

<style lang="scss">
.handover-common-page {
  height: 100%;
  .tabs {
    height: 35px;
    margin-bottom: 5px;
  }
  .pane-content {
    height: calc(100% - 45px);
  }
}
</style>