/*
 * FilePath     : \ccc.web\src\autoPages\patientHomePage\tprChart\chartOption.js
 * Author       : 郭鹏超
 * Date         : 2023-05-17 10:26
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-06-25 18:34
 * Description  :
 * CodeIterationRecord:
 */
export const setChartOption = (chart, _this) => {
  let symbolSize = _this.convertPX(8);
  let lineWidth = _this.convertPX(2);
  let fontSize = _this.convertPX(15);
  let yTitleFontSize = _this.convertPX(12);
  let yAxisLine = {
    show: true,
    lineStyle: {
      width: _this.convertPX(1)
    }
  };
  let yAxisTick = {
    show: true,
    lineStyle: {
      width: _this.convertPX(1)
    }
  };
  let yRightAxisLabel = {
    show: true,
    fontWeight: "bold",
    fontSize: _this.convertPX(13),
    margin: _this.convertPX(8),
    padding: [0, 13, 0, 0]
  };
  let yLeftAxisLabel = {
    show: true,
    fontWeight: "bold",
    fontSize: _this.convertPX(13),
    margin: _this.convertPX(8),
    padding: [0, 0, 0, 9]
  };
  let seriesLabel = {
    show: false,
    position: "top",
    fontWeight: "bold",
    fontSize: fontSize
  };
  chart.setOption({
    xAxis: {
      axisLabel: {
        formatter: val => {
          let newVal = _this._datetimeUtil.formatDate(val, "MM-dd hh:mm");
          return newVal.split(" ").join("\n");
        },
        textStyle: {
          fontWeight: "bold",
          fontSize: _this.convertPX(14)
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: "#000000",
          width: _this.convertPX(1)
        }
      }
    },
    yAxis: [
      {
        position: "left",
        offset: _this.convertPX(75),
        type: "value",
        name: "呼吸",
        min: 10, //Y轴最小值
        max: 110, //Y轴最大值
        interval: 10, //Y轴间隔
        nameTextStyle: {
          padding: [0, 0, _this.convertPX(5), _this.convertPX(-45)],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        //坐标轴线
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yLeftAxisLabel
      },
      {
        position: "left",
        offset: _this.convertPX(35),
        type: "value",
        name: "脉搏",
        min: 30, //Y轴最小值
        max: 180, //Y轴最大值
        interval: 15, //Y轴间隔
        nameTextStyle: {
          padding: [0, 0, _this.convertPX(5), _this.convertPX(-35)],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yLeftAxisLabel
      },
      {
        position: "left",
        type: "value",
        name: "体温",
        min: 32, //Y轴最小值
        max: 42, //Y轴最大值
        interval: 1, //Y轴间隔
        nameTextStyle: {
          padding: [0, 0, _this.convertPX(5), _this.convertPX(-25)],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        //坐标轴线
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yLeftAxisLabel
      },
      {
        position: "right",
        type: "value",
        name: "血压",
        min: 0, //Y轴最小值
        max: 200, //Y轴最大值
        interval: 20, //Y轴间隔
        nameTextStyle: {
          padding: [0, _this.convertPX(-25), _this.convertPX(5), 0],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        //坐标轴线
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yRightAxisLabel
      },
      {
        position: "right",
        offset: _this.convertPX(40),
        type: "value",
        name: "疼痛",
        min: 0, //Y轴最小值
        max: 10, //Y轴最大值
        interval: 1, //Y轴间隔
        nameTextStyle: {
          padding: [0, _this.convertPX(-25), _this.convertPX(5), 0],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        //坐标轴线
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yRightAxisLabel
      },
      {
        position: "right",
        offset: _this.convertPX(70),
        type: "value",
        name: "SPO2",
        min: 0, //Y轴最小值
        max: 100, //Y轴最大值
        interval: 10, //Y轴间隔
        nameTextStyle: {
          padding: [0, _this.convertPX(-35), _this.convertPX(5), 0],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        //坐标轴线
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yRightAxisLabel,
        //分割线/网格样式
        splitLine: {
          show: true,
          lineStyle: {
            color: "#000000",
            width: _this.convertPX(1),
            type: "solid"
          }
        }
      },
      {
        position: "right",
        offset: _this.convertPX(110),
        type: "value",
        name: "中心\n静脉压",
        min: 0, //Y轴最小值
        max: 50, //Y轴最大值
        interval: 5, //Y轴间隔
        width: 50,
        nameTextStyle: {
          padding: [0, _this.convertPX(-35), _this.convertPX(5), 0],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        //坐标轴线
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yRightAxisLabel,
        //分割线/网格样式
        splitLine: {
          show: true,
          lineStyle: {
            color: "#000000",
            width: _this.convertPX(1),
            type: "solid"
          }
        }
      }
    ],
    series: [
      {
        type: "line",
        yAxisIndex: 2,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        symbol: "circle",
        symbolSize: symbolSize, //图例大小
        itemStyle: {
          normal: {
            color: "#0000FF", //图例颜色
            lineStyle: {
              //折线颜色大小
              type: "solid",
              color: "#0000FF",
              width: lineWidth
            }
          }
        }
      },
      {
        type: "line",
        yAxisIndex: 1,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        symbol: "circle",
        symbolSize: symbolSize, //图例大小
        itemStyle: {
          normal: {
            color: "#FF0000", //图例颜色

            lineStyle: {
              //折线颜色大小
              type: "solid",
              color: "#FF0000",
              width: lineWidth
            }
          }
        }
      },
      {
        type: "line",
        yAxisIndex: 0,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        symbolSize: symbolSize, //图例大小
        itemStyle: {
          normal: {
            color: "#1E90FF", //图例颜色
            lineStyle: {
              //折线颜色大小
              type: "solid",
              color: "#1E90FF",
              width: lineWidth
            }
          }
        }
      },
      {
        type: "line",
        yAxisIndex: 3,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        // 自定义图例  向下箭头
        symbol:
          "path://M128.759549 31.7009177l383.063419 383.064442 388.096039-388.097062 0.033769 0.07060799c13.137205-12.709463 31.024597-20.529569 50.731428-20.52956899 40.376593 0 73.11215799 32.736589 73.112158 73.115228 0 19.705807-7.819083 37.591153-20.528546 50.730405l0.035816 0.034792L564.622498 568.7760127l-0.035816-0.035816c-13.28149101 13.779841-31.91589701 22.3838-52.585659 22.3838-0.07163101 0-0.106424 0-0.178055 0-0.072655 0-0.10847 0-0.144286 0-20.66976201 0-39.341007-8.603959-52.622498-22.3838l-0.034792 0.035816L20.337187 130.0928317l0.179079-0.180102c-12.56619999-13.139252-20.313651-30.950919-20.313651-50.587142 0-40.378639 32.736589-73.115228 73.114205-73.115228C95.485725 6.2113837 115.33479499 16.0995907 128.759549 31.7009177z",
        symbolSize: symbolSize, //图例大小
        symbolOffset: [0, "-50%"],
        itemStyle: {
          normal: {
            color: "#1cc6a3", //图例颜色
            lineStyle: {
              //折线颜色大小
              type: "solid",
              color: "#1cc6a3",
              width: lineWidth
            }
          }
        }
      },
      {
        type: "line",
        yAxisIndex: 3,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        // 自定义图例  向上箭头
        symbol:
          "path://M895.240451 992.2990823l-383.063419-383.064442-388.09603901 388.097062-0.03376899-0.07060799c-13.137205 12.709463-31.024597 20.529569-50.731428 20.52956899-40.376593 0-73.11215799-32.736589-73.112158-73.115228 0-19.705807 7.819083-37.591153 20.528546-50.730405l-0.035816-0.034792L459.37750201 455.2239873l0.03581599 0.035816c13.28149101-13.779841 31.91589701-22.3838 52.585659-22.3838 0.07163101 0 0.106424 0 0.178055 0 0.072655 0 0.10847 0 0.144286 0 20.66976201 0 39.341007 8.603959 52.622498 22.3838l0.034792-0.035816L1003.662813 893.9071683l-0.179079 0.180102c12.56619999 13.139252 20.313651 30.950919 20.313651 50.587142 0 40.378639-32.736589 73.115228-73.114205 73.11522799C928.514275 1017.7886163 908.66520501 1007.9004093 895.240451 992.2990823z",
        symbolSize: symbolSize, //图例大小
        symbolOffset: [0, "50%"],
        itemStyle: {
          normal: {
            color: "#1cc6a3", //图例颜色
            lineStyle: {
              //折线颜色大小
              type: "solid",
              color: "#1cc6a3",
              width: lineWidth
            }
          }
        }
      },
      {
        type: "line",
        yAxisIndex: 1,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        symbol: "triangle",
        symbolSize: symbolSize, //图例大小
        itemStyle: {
          normal: {
            color: "#B22222", //图例颜色
            lineStyle: {
              //折线颜色大小
              type: "solid",
              color: "#B22222",
              width: lineWidth
            }
          }
        }
      },
      {
        type: "line",
        yAxisIndex: 5,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        symbol: "circle",
        symbolSize: symbolSize, //图例大小
        itemStyle: {
          normal: {
            color: "#FFA500", //图例颜色
            lineStyle: {
              //折线颜色大小
              type: "solid",
              color: "#FFA500",
              width: lineWidth
            }
          }
        }
      },
      {
        type: "line",
        yAxisIndex: 4,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        // symbol: "diamond",
        symbol:
          "path://M512 1e-8C264.576 1e-8 64 200.57700001 64 448.00000001s200.576 448 448 448 448-200.576 448-448S759.424 1e-8 512 1e-8zM512 832.00000001c-212.078 0-384-171.923-384-384 0-212.077 171.922-384 384-384 212.077 0 384 171.923 384 384C896 660.07700001 724.077 832.00000001 512 832.00000001z M544 193.00000001l-96 0-32 0c-17.673 0-33 13.327-33 31l0 32 0 192 0 64 0 160c0 17.673 14.327 32 32 32s32-14.327 32-32L447 513.00000001l97 0c88.366 0 160-71.634 160-160S632.366 193.00000001 544 193.00000001zM640 353.00000001c0 52.935-43.065 96-96 96l-97 0 0-1L447 257.00000001l1 0 0-1 96 0c53.02 0 96 42.98 96 96 0 0.167-0.006 0.333-0.006 0.501C639.994 352.66700001 640 352.83300001 640 353.00000001z",
        symbolSize: symbolSize, //图例大小
        itemStyle: {
          normal: {
            color: "#FF1493", //图例颜色
            lineStyle: {
              //折线颜色大小
              type: "solid",
              color: "#FF1493",
              width: lineWidth
            }
          }
        }
      },
      {
        type: "line",
        yAxisIndex: 6,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        symbolSize: symbolSize, //图例大小
        itemStyle: {
          normal: {
            lineStyle: {
              //折线颜色大小
              type: "solid",
              width: lineWidth
            }
          }
        }
      },
      {
        type: "line",
        yAxisIndex: 6,
        connectNulls: true,
        smooth: false,
        showAllSymbol: true,
        label: seriesLabel,
        symbolSize: symbolSize, //图例大小
        itemStyle: {
          normal: {
            color: "#FF0000",
            lineStyle: {
              //折线颜色大小
              type: "solid",
              color: "#FF0000",
              width: lineWidth
            }
          }
        }
      }
    ]
  });
};
export const grid = {
  top: 60,
  height: "auto",
  bottom: 5,
  right: 10,
  left: 10
};

export const tooltip = {
  trigger: "axis",
  // 将 tooltip 框限制在图表的区域内
  confine: true,
  formatter: function(params) {
    var result = "";
    for (var i = 0; i < params.length; i++) {
      // 判断当前 series 中数据是否为空
      if (params[i].data[1] === "") {
        continue;
      }
      // 自定义 tooltip 内容
      result +=
        params[i].marker +
        params[i].seriesName +
        ": " +
        params[i].value[1] +
        "<br>";
    }
    return result;
  },
  axisPointer: {
    animation: false
  }
};
export const extend = {
  "xAxis.0.axisLabel.formatter": val => {
    return val.split(" ").join("\n");
  },
  series: {
    type: "line",
    connectNulls: true,
    smooth: false
  }
};
