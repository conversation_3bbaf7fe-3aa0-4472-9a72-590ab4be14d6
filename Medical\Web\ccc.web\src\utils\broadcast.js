/*
 * FilePath     : \src\utils\broadcast.js
 * Author       : 苏军志
 * Date         : 2020-05-28 02:50
 * LastEditors  : 苏军志
 * LastEditTime : 2020-07-09 16:36
 * Description  :
 */

// 创建一个自定义事件
var broadcastEvent = new CustomEvent("broadcast", {
  detail: {
    key: "",
    value: ""
  }
});
// 注册自定义属性，发布广播
const sendBroadcast = (key, sendValue) => {
  // 设置事件detail的值
  broadcastEvent.detail.key = key;
  broadcastEvent.detail.value = sendValue;
  // 发布自定义事件
  window.dispatchEvent(broadcastEvent);
};

// 注册自定义属性，接收广播 key：事件detail的key，callback：回调函数
const receiveBroadcast = (key, callback) => {
  window.addEventListener(
    "broadcast",
    event => {
      if (event.detail.key == key) {
        callback(event.detail.value);
      }
    },
    true
  );
};

export default {
  sendBroadcast,
  receiveBroadcast
};
