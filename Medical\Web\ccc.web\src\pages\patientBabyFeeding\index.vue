<!--
 * FilePath     : /src/pages/patientBabyFeeding/index.vue
 * Author       : 郭鹏超
 * Date         : 2021-06-08 15:27
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-06 17:07
 * Description  : 母婴喂养
 * CodeIterationRecord: 2415-添加补录来源信息 2022-04-13 杨欣欣
                        2855-作为IT人员，我需要依据提供的资料调整嘉会产科及新生儿科交接内容，以利于护理交班内容完整。 2022-08-24 杨欣欣
                        2022-08-18 2876-专项增加带入护理记录选框 -杨欣欣
                        2022-11-28 2869-IO弹窗改为专项跳转 -杨欣欣
-->
<template>
  <specific-care
    v-model="showDrawerFlag"
    :showRecordArr="showRecordArr"
    :drawerTitle="drawerTitle"
    :handOverFlag="handOverArr"
    :nursingRecordFlag="nursingRecordArr"
    :previewFlag="!checkResult"
    :informPhysicianFlag="informPhysicianArr"
    :drawerSize="refillFlag ? '80%' : ''"
    @getMainFlag="currentRecord"
    @mainAdd="recordAdd"
    @maintainAdd="careMainAdd"
    @getHandOverFlag="getHandOverFlag"
    @cancel="drawerClose"
    @save="saveSelect"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    class="patient-babyFeeding"
    v-loading="loading"
    element-loading-text="加载中……"
    v-if="useObj.useFlag == '1'"
  >
    <!-- 主记录 -->
    <div slot="main-record">
      <el-table @row-click="currentTableList" height="100%" :data="recordTableList" border stripe>
        <el-table-column prop="sourceContent" label="来源" width="50" header-align="center" align="center" />
        <el-table-column prop="occuredDepartmentName" label="发生科室" width="150" header-align="center" align="left" />
        <el-table-column prop="startDate" label="开始时间" width="160" header-align="center" align="center">
          <template slot-scope="scope">
            <span
              v-formatTime="{
                value: scope.row.startDateTime,
                type: 'dateTime',
              }"
            ></span>
          </template>
        </el-table-column>
        <el-table-column prop="endDateTime" label="结束时间" width="160" header-align="center" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.endDateTime, type: 'dateTime' }"></span>
            <span>{{ scope.row.endDate + " " + scope.row.endTime }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="addNurseName" label="新增人员" width="80" header-align="center" align="center" />
        <el-table-column prop="content" label="护理措施" min-width="180" header-align="center" align="center" />
        <el-table-column label="操作" fixed="right" header-align="center" align="center" width="80">
          <template slot-scope="scope">
            <el-tooltip v-if="!scope.row.endDate" content="结束">
              <div @click.stop="recordEnd(scope.row)" class="iconfont icon-stop"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="deleteRecord(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <el-table height="100%" :data="careMainTableList" border stripe>
        <el-table-column label="类型" width="50" header-align="center" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.recordsCode.indexOf('Start') != -1">喂养开始</span>
            <span v-else-if="scope.row.recordsCode.indexOf('End') != -1">喂养结束</span>
            <span v-else>例行评估</span>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="评估时间" width="160" header-align="center" align="center">
          <template slot-scope="scope">
            <span
              v-formatTime="{
                value: scope.row.assessDateTime,
                type: 'dateTime',
              }"
            ></span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="patient.inpatientID != useObj.motherInpatientID"
          label="喂养评估"
          header-align="center"
          align="center"
        >
          <el-table-column prop="feedingType" label="喂养方式" width="120" header-align="center" align="center" />
          <el-table-column
            prop="feedingMinute_L"
            label="左乳喂养时间(min)"
            width="95"
            header-align="center"
            align="center"
          />
          <el-table-column
            prop="feedingMinute_R"
            label="右乳喂养时间(min)"
            width="95"
            header-align="center"
            align="center"
          />
          <el-table-column prop="formulaMilkType" label="配方奶类型" width="70" header-align="center" align="center" />
          <el-table-column prop="amount" label="配方奶量(ml)" width="80" header-align="center" align="center" />
        </el-table-column>
        <el-table-column header-align="center" prop="careIntervention" label="乳房评估" min-width="120" />
        <el-table-column header-align="center" prop="remark" label="备注" min-width="90" />
        <el-table-column prop="addEmployeeName" label="记录人" width="120" header-align="center" align="center" />
        <el-table-column label="操作" fixed="right" header-align="center" align="center" width="80">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="careMainAdd(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="deleteCareMain(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 评估内容 -->
    <base-layout slot="drawer-content" header-height="auto">
      <div slot="header">
        <span class="label">执行日期:</span>
        <el-date-picker
          v-model="performDate"
          class="date-picker"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        />
        <el-time-picker
          class="time-picker"
          v-model="performTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        />
        <station-selector v-model="performStationID" label="执行病区:" width="160" />
        <dept-selector label="" width="140" v-model="performDepartmentID" :stationID="performStationID" />
      </div>
      <tabs-layout
        v-loading="layoutLoading"
        :element-loading-text="layoutText"
        ref="tabsLayout"
        :template-list="templateDatas"
        @change-values="changeValues"
        @checkTN="checkTN"
        @button-click="buttonClick"
      />
    </base-layout>
    <div class="patient-babyFeeding-dialog" slot="drawer-dialog">
      <!--弹出按钮链接框-->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonName"
        :visible.sync="showButtonDialog"
        @close="updateButtonData"
        fullscreen
        custom-class="no-footer specific-care-view"
      >
        <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
      </el-dialog>
    </div>
  </specific-care>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import { mapGetters } from "vuex";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { GetButtonData } from "@/api/Assess";
import {
  GetBabyFeedingRecordList,
  SaveBabyFeedingRecord,
  DeleteBabyFeedingRecordByID,
  GetBabyFeedingCareList,
  SaveBabyFeedingCare,
  DeleteBabyFeedingCareByID,
  GetFeedingAssessView,
  GetBabyFeedingRecordsCodeInfo,
  GetUseFlag,
} from "@/api/BabyFeeding";
import { GetBringToShiftSetting } from "@/api/Setting.js";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  components: {
    baseLayout,
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      //组件使用变量
      showDrawerFlag: false,
      drawerTitle: "",
      showRecordArr: [true, false],
      handOverArr: [true, false],
      nursingRecordArr: [false, false],
      settingBringToNursingRecord: false,
      settingHandover: false,
      informPhysicianArr: [true, false],
      //主记录变量
      recordTableList: [],
      babyFeedingRecordID: undefined,
      currentRecordData: undefined,
      //维护记录变量
      careMainTableList: [],
      babyFeedingCareMainID: undefined,
      //抽屉变量
      performDate: undefined,
      performTime: undefined,
      performStationID: undefined,
      performDepartmentID: undefined,
      //评估模板变量
      recordsCodeInfo: {},
      templateDatas: [],
      assessDatas: [],
      checkTNFlag: undefined,
      buttonItem: undefined,
      //IO组件变量
      showDrain: false,
      ioData: {},
      ioKindList: [],
      //配置数据
      inKindList: [],
      //加载变量
      loading: false,
      layoutLoading: false,
      layoutText: undefined,
      useObj: {},
      routeData: undefined,
      checkResult: true,
      //补录标记
      refillFlag: "",
      //最终患者信息(两种来源1.在院2.补录)
      patient: undefined,
      // 按钮跳转相关变量
      showButtonDialog: false,
      buttonAssessListID: undefined,
      sourceID: undefined,
      sourceType: undefined,
      buttonName: "",
    };
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = "";
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.refillFlag = "*";
          this.nursingRecordArr = [false, false];
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
    },
  },
  methods: {
    async init() {
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
      await this.useCheck();
      if (!this.useObj || this.useObj.useFlag == "0") {
        this._showTip("warning", "该病人无法使用此功能!");
        return;
      }
      this.mainDataList = [];
      this.showRecordArr = [true, false];
      this.getBringHandOverSetting();
      await this.getRecordTableList();
      if (Object.keys(this.$route.query).length && !this.$route.query.shortCutFlag) {
        this._sendBroadcast("setPatientSwitch", false);
        this.routeData = this.$route.query;
        //排程跳转处理
        this.scheduleJumpFix();
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
      this.getBringToNursingRecordSetting();
    },

    /*使用权限检核*/
    async useCheck() {
      let params = {
        caseNumber: this.patient.caseNumber,
      };
      await GetUseFlag(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.useObj = res.data;
        }
      });
    },

    /*获取数据逻辑*/

    //获取主记录表格数据
    async getRecordTableList() {
      this.loading = true;
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      await GetBabyFeedingRecordList(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.recordTableList = res.data;
          if (this.recordTableList.length) {
            this.recordTableList.forEach((item) => {
              if (item.startDate && item.startTime) {
                item.startDateTime = this._datetimeUtil.formatDate(item.startDate, "yyyy-MM-dd") + " " + item.startTime;
              }
              item.endDate = item.endDate ? this._datetimeUtil.formatDate(item.endDate, "yyyy-MM-dd") : "";
              item.endTime = item.endTime ? this._datetimeUtil.formatDate(item.endTime, "hh:mm") : "";
            });
          }
        }
      });
    },
    //获取维护记录表格数据
    async getCareMainTableList() {
      this.loading = true;
      let params = {
        recordID: this.currentRecordData.babyFeedingRecordID,
      };
      await GetBabyFeedingCareList(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.careMainTableList = res.data;
          if (this.careMainTableList.length) {
            this.careMainTableList.forEach((item) => {
              if (item.assessDate && item.assessTime) {
                item.assessDateTime =
                  this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd") + " " + item.assessTime;
              }
            });
          }
        }
      });
    },
    //点击主记录获取对应维护记录
    async currentTableList(item) {
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.currentRecordData = item;
        this.recordTableList = [item];
        await this.getCareMainTableList();
      }
    },

    /*抽屉初始化逻辑逻辑*/
    //主记录抽屉初始化
    recordAdd() {
      this.checkResult = true;
      this.openOrCloseDrawer(true, "主记录");
      this.recordsCodeInfo.recordsCode = "BabyFeedingStart";
      this.performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = this._datetimeUtil.getNowTime("hh:mm");
      this.performStationID = this.patient.stationID;
      this.performDepartmentID = this.patient.departmentListID;
      this.babyFeedingRecordID = undefined;
      this.babyFeedingTampCareMainID = "temp_" + this._common.guid();
      this.$set(this.handOverArr, 1, this.settingHandover);
      this.$set(this.informPhysicianArr, 1, false);
      this.$set(this.nursingRecordArr, 1, this.settingBringToNursingRecord);
      this.getFeedingAssessView();
    },
    //维护记录抽屉初始化
    async careMainAdd(item) {
      this.checkResult = true;
      if (item) {
        //是否仅本人操作
        this.checkResult = await this._common.checkActionAuthorization(this.user, item.addEmployeeName);
      }

      if (!this.currentRecordData) {
        this._showTip("warning", "请先选择主记录!");
        return;
      }
      if (!item && this.currentRecordData.endDate) {
        this._showTip("warning", "该记录已停止,无法新增维护记录");
        return;
      }
      this.openOrCloseDrawer(true, "维护记录");
      this.recordsCodeInfo.recordsCode = item ? item.recordsCode : "BabyFeedingMaintain";
      this.templateDatas = [];
      this.performDate = item
        ? this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = item
        ? this._datetimeUtil.formatDate(item.assessTime, "hh:mm:ss")
        : this._datetimeUtil.getNowTime("hh:mm:ss");
      this.performStationID = item ? item.stationID : this.patient.stationID;
      this.performDepartmentID = item ? item.departmentListID : this.patient.departmentListID;
      this.babyFeedingCareMainID = item ? item.babyFeedingCareMainID : "temp_" + this._common.guid();
      this.babyFeedingRecordID = item ? item.babyFeedingRecordID : this.currentRecordData.babyFeedingRecordID;
      this.babyFeedingTampCareMainID = this.babyFeedingCareMainID;
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandover);
      this.getFeedingAssessView();
    },
    recordEnd(item) {
      this.checkResult = true;
      this.currentRecordData = item;
      this.openOrCloseDrawer(true, "停止");
      this.recordsCodeInfo.recordsCode = "BabyFeedingEnd";
      this.performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = this._datetimeUtil.getNowTime("hh:mm:ss");
      this.performStationID = item.stationID;
      this.performDepartmentID = item.departmentID;
      this.babyFeedingRecordID = item.babyFeedingRecordID;
      this.$set(this.informPhysicianArr, 1, false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      this.$set(this.handOverArr, 1, this.settingHandover);
      this.getFeedingAssessView();
    },
    /*页面保存逻辑*/
    //弹窗保存选择
    async saveSelect() {
      let recordsCode = this.recordsCodeInfo.recordsCode;
      this.layoutLoading = true;
      this.layoutText = "保存中……";
      if (recordsCode == "BabyFeedingStart") {
        await this.recordSave();
      }
      if (recordsCode == "BabyFeedingMaintain") {
        await this.careMainSave();
      }
      if (recordsCode == "BabyFeedingEnd") {
        await this.recordEndSave();
      }
      this.layoutLoading = false;
      this.layoutText = "";
    },
    //主记录保存
    async recordSave() {
      let params = {
        record: {
          babyFeedingRecordID: this.babyFeedingRecordID,
          inpatientID: this.patient.inpatientID,
          startDate: this.performDate,
          startTime: this.performTime,
          stationID: this.performStationID,
          recordDepartmentID: this.performDepartmentID,
          RecordStationID: this.patient.stationID,
          BabyFeedingRecordID: this.babyFeedingRecordID,
          RefillFlag: this.refillFlag,
        },
        Details: this.getDetails(),
        patientScheduleMainID: this.patientScheduleMainID,
        nursingLevel: this.patient.nursingLevelCode,
        recordsCode: this.recordsCodeInfo.recordsCode,
        BabyFeedingTampCareMainID: this.babyFeedingTampCareMainID,
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.nursingRecordArr[1],
        bringToShift: this.handOverArr[1],
        sourceID: this.sourceID,
        sourceType: this.sourceType,
      };
      if (this.useObj.motherInpatientID) {
        params.record.motherInpatientID = this.useObj.motherInpatientID;
      }
      if (this.routeData && this.routeData.patientScheduleMainID) {
        params.PatientScheduleMainID = this.routeData.patientScheduleMainID;
      }
      // params.record.motherInpatientID = "a85197d2cf4d40ad9b05a7d22fb4e987";
      //接入主记录保存API
      await SaveBabyFeedingRecord(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", "保存成功");
          this.showRecordArr = [true, false];
          this.careMainTableList = [];
          this.currentRecordData = undefined;
          this.currentRecord(true);
        }
      });
    },
    //维护记录保存
    async careMainSave() {
      let params = {
        Main: {
          babyFeedingRecordID: this.babyFeedingRecordID,
          babyFeedingCareMainID: this.babyFeedingCareMainID,
          patientScheduleMainID: this.patientScheduleMainID,
          recordsCode: this.recordsCodeInfo.recordsCode,
          numberOfAssessment: 1,
          AssessDate: this.performDate,
          AssessTime: this.performTime,
          StationID: this.performStationID,
          DepartmentListID: this.performDepartmentID,
          InpatientID: this.patient.inpatientID,
          informPhysician: this.informPhysicianArr[1],
          bringToShift: this.handOverArr[1],
          RefillFlag: this.refillFlag,
          bringToNursingRecord: this.nursingRecordArr[1],
          sourceID: this.sourceID,
          sourceType: this.sourceType,
        },
        Details: this.getDetails(),
      };
      if (this.useObj.motherInpatientID) {
        params.Main.motherInpatientID = this.useObj.motherInpatientID;
      }
      if (this.routeData && this.routeData.patientScheduleMainID) {
        params.Main.PatientScheduleMainID = this.routeData.patientScheduleMainID;
      }
      // params.Main.motherInpatientID = "a85197d2cf4d40ad9b05a7d22fb4e987";
      //接入维护记录记录保存API
      await SaveBabyFeedingCare(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", "保存成功");
          this.getCareMainTableList();
        }
      });
    },
    //主记录停止
    async recordEndSave() {
      let params = {
        record: {
          babyFeedingRecordID: this.babyFeedingRecordID,
          inpatientID: this.patient.inpatientID,
          startDate: this.performDate,
          startTime: this.performTime,
          stationID: this.performStationID,
          recordDepartmentID: this.performDepartmentID,
          RecordStationID: this.patient.stationID,
          RefillFlag: this.refillFlag,
        },
        Details: this.getDetails(),
        patientScheduleMainID: this.patientScheduleMainID,
        nursingLevel: this.patient.nursingLevelCode,
        recordsCode: this.recordsCodeInfo.recordsCode,
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.nursingRecordArr[1],
        bringToShift: this.bringToShift,
      };
      if (this.useObj.motherInpatientID) {
        params.record.motherInpatientID = this.useObj.motherInpatientID;
      }
      // params.record.motherInpatientID = "a85197d2cf4d40ad9b05a7d22fb4e987";
      //接入主记录保存API
      await SaveBabyFeedingRecord(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", "保存成功");
          this.showRecordArr = [true, false];
          this.careMainTableList = [];
          this.currentRecordData = undefined;
          this.currentRecord(true);
        }
      });
    },
    /*删除逻辑*/
    //删除主记录
    async deleteRecord(item) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, item.addNurseName);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            recordID: item.babyFeedingRecordID,
            RefillFlag: this.refillFlag,
          };
          DeleteBabyFeedingRecordByID(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.showRecordArr = [true, false];
              this.getRecordTableList();
            }
          });
        }
      });
    },
    //删除维护记录
    async deleteCareMain(item) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, item.addEmployeeName);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            careMainID: item.babyFeedingCareMainID,
            RefillFlag: this.refillFlag,
          };
          DeleteBabyFeedingCareByID(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getCareMainTableList();
              if (item.recordsCode == "BabyFeedingEnd") {
                this.$set(this.recordTableList[0], "endTime", "");
                this.$set(this.recordTableList[0], "endDate", "");
              }
            }
          });
        }
      });
    },
    /*评估模板 */
    async getFeedingAssessView() {
      this.layoutLoading = true;
      this.layoutText = "加载中……";
      this.templateDatas = [];
      let params = {
        departmentListID: this.patient.departmentListID,
        mappingType: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
      };
      await GetBabyFeedingRecordsCodeInfo(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
        } else {
          this.recordsCodeInfo.recordsCode = undefined;
        }
      });
      if (!this.recordsCodeInfo || !this.recordsCodeInfo.recordsCode) {
        this.openOrCloseDrawer(false);
        return;
      }
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        inpatientID: this.patient.inpatientID,
      };
      if (this.babyFeedingRecordID) {
        params.recordID = this.babyFeedingRecordID;
      }
      if (this.babyFeedingCareMainID && this.babyFeedingCareMainID.indexOf("temp") == -1) {
        params.careMainID = this.babyFeedingCareMainID;
      }
      await GetFeedingAssessView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
      this.layoutLoading = false;
      this.layoutText = "";
    },
    changeValues(datas) {
      this.assessDatas = datas;
    },
    /**
     * description: 按钮处理
     * param {*} content assessContent配置
     * return {*}
     */
    async buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += (url.includes("?") ? "&" : "?") + `token=${this.token}`;
      url +=
        `&inpatientID=${this.patient.inpatientID}` +
        `&sourceID=${this.babyFeedingTampCareMainID && this.babyFeedingTampCareMainID.replace(/temp_/g, "")}` +
        `&sourceType=babyFeeding_${content.assessListID}` +
        "&isDialog=true";

      this.showButtonDialog = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * @description: 更新按钮角标
     * @return 
     */
    async updateButtonData() {
      const params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessListID: this.buttonAssessListID,
        sourceID: this.getCareMainID(),
        sourceType: "BabyFeeding" + "_" + this.buttonAssessListID,
      };
      const result = await GetButtonData(params);
      const item = this._common.isSuccess(result) && result.data ? result.data : undefined;
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    /**
     * @description: 获取维护记录CareMainID
     * @return
     */
    getCareMainID() {
      let tempCareMainID = "";
      if (this.babyFeedingTampCareMainID) {
        if (this.babyFeedingTampCareMainID.indexOf("temp") != -1) {
          tempCareMainID = this.babyFeedingTampCareMainID.split("_")[1];
        } else {
          tempCareMainID = this.babyFeedingTampCareMainID;
        }
      }
      return tempCareMainID;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 带交班逻辑
     * param {*} flag
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /*抽屉关闭逻辑*/
    drawerClose() {
      this.openOrCloseDrawer(false);
      this.babyFeedingRecordID = undefined;
      this.babyFeedingCareMainID = undefined;
    },

    /*顶部勾选框逻辑*/
    //主记录勾选
    async currentRecord(flag) {
      if (flag) {
        this.getRecordTableList();
        this.currentRecordData = undefined;
        this.careMainTableList = [];
      } else {
        if (!this.currentRecordData && this.recordTableList.length) {
          this.currentRecordData = this.recordTableList[0];
          this.recordTableList = [this.recordTableList[0]];
          await this.getCareMainTableList();
        }
      }
    },
    /*配合主流程逻辑*/
    //打开或关闭抽屉
    openOrCloseDrawer(flag, title) {
      this.showDrawerFlag = flag;
      this.drawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
    },
    //组装保存detail数据
    getDetails() {
      let details = [];
      if (!this.assessDatas.length) {
        return details;
      }
      this.assessDatas.forEach((item) => {
        let detail = {
          assessListID: item.assessListID,
          assessListGroupID: item.assessListGroupID,
        };
        if (item.controlerType.trim() == "C" || item.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = item.assessValue;
        }
        details.push(detail);
      });
      return details;
    },
    //排程跳转处理
    async scheduleJumpFix() {
      if (!this.routeData) {
        return;
      }
      if (this.recordTableList.length == 0) {
        this.recordAdd();
        this.performDate = this._datetimeUtil.formatDate(this.routeData.scheduleDate, "yyyy-MM-dd");
        this.performTime = this._datetimeUtil.formatDate(this.routeData.scheduleTime, "hh:mm:ss");
      }
      if (this.recordTableList.length == 1) {
        this.currentRecordData = this.recordTableList[0];
        this.recordTableList = [this.recordTableList[0]];
        await this.getCareMainTableList();
        if (this.routeData.completeMark == "0") {
          this.careMainAdd();
          this.performDate = this._datetimeUtil.formatDate(this.routeData.scheduleDate, "yyyy-MM-dd");
          this.performTime = this._datetimeUtil.formatDate(this.routeData.scheduleTime, "hh:mm:ss");
        }
        if (this.routeData.completeMark == "1") {
          var sucCareMain = this.careMainTableList.find(
            (item) => item.patientScheduleMainID == this.routeData.patientScheduleMainID
          );
          if (sucCareMain) {
            this.careMainAdd(sucCareMain);
          }
        }
      }
    },
    //通知医师标记
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.nursingRecordArr[1] = flag;
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "BabyFeedingAutoInterventionToRecord",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingBringToNursingRecord = res.data;
        }
      });
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "BabyFeeding",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.handOverArr[0]) {
            this.settingHandover = res.data;
          }
        }
      });
    },
  },
};
</script>

<style lang="scss">
.patient-babyFeeding {
  .date-picker {
    width: 120px;
  }
  .time-picker {
    width: 80px;
  }
  .station-selector .label {
    margin-left: 0px;
  }
  .patient-babyFeeding-dialog {
    .specific-care-view {
      background-color: #f3f3f3;
      iframe {
        height: 99%;
        border: none;
      }
    }
  }
}
</style>
