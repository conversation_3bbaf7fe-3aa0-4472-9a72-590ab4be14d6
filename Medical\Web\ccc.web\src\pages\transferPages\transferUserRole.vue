<template>
  <div class="user-role-wrap">
    <div class="userRole-header">
      <!-- 页首分两栏 -->
      <el-row :gutter="20">
        <el-col :span="3">
          <div class="select-station">
            <!-- 病区选择器 -->
            <el-select v-model="stationID" placeholder="请选择病区">
              <el-option
                v-for="item in optionsStations"
                :key="item.id"
                :label="item.stationName"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="input-query">
            <el-input
              placeholder="请输入工号查询"
              v-model.trim="input"
              class="search-input"
              @keyup.enter.native="userQuery"
              clearable
              @clear="getUserList"
            >
              <i slot="append" class="iconfont icon-search" @click="userQuery"></i>
            </el-input>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 主体页面 -->
    <div
      class="userRole-main"
      v-loading="loadingData"
      :element-loading-text="loadingText"
      element-loading-background="rgba(0, 0, 0, 0.5)"
    >
      <!-- 表格 -->
      <el-table :data="userList" stripe border height="100%" :row-style="{ height: '30px' }">
        <el-table-column type="index" label="序号" width="60px" align="center"></el-table-column>
        <el-table-column prop="userID" label="工号" min-width="150px" align="center"></el-table-column>
        <el-table-column prop="name" label="姓名" min-width="150px" align="center"></el-table-column>
        <el-table-column prop="title" label="头衔" min-width="150px" align="center"></el-table-column>
        <el-table-column prop="roleName" label="用户角色" min-width="300px" align="center"></el-table-column>
        <el-table-column label="操作" width="100px" align="center">
          <template slot-scope="scope">
            <div class="multi_icon" style="margin-left: 20px">
              <el-tooltip content="修改">
                <i class="iconfont icon-edit" @click="UserUpdate(scope.row)"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      title="修改用户"
      :visible.sync="dialogFormVisibleUserUpdate"
      v-if="dialogFormVisibleUserUpdate"
      v-loading="loadingData"
      v-dialogDrag
      :close-on-click-modal="false"
      :element-loading-text="loadingText"
      element-loading-background="rgba(0, 0, 0, 0.5)"
      width="600px"
    >
      <!-- 用户修改的表单 -->
      <el-form :model="editForm" :rules="rules" ref="editForm" label-width="100px">
        <el-form-item label="角色设置：" prop="checkList">
          <el-checkbox-group class="checkbox-group" v-model="editForm.checkList">
            <el-checkbox
              class="checkbox-roleList"
              name="roleListType"
              v-for="item in roleList"
              :key="item.id"
              :label="item.id"
            >
              {{ item.groupName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :disabled="isSave" type="primary" @click="saveEditForm()">确 定</el-button>
        <el-button @click="dialogFormVisibleUserUpdate = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { GetUserRoleByStationID, UpdataUserById, GetUserRoleByUserID } from "@/api/User";
import { GetStationList } from "@/api/Station";
import { GetRoleList } from "@/api/Authority";

export default {
  data() {
    return {
      // 选择病区
      optionsStations: [],
      //选择器绑定的病区
      stationID: this._common.session("user").stationID,
      // 输入框
      input: "",
      //表格展示数据
      userList: [],
      roleList: [],
      userID: undefined,
      // 嵌套修改表单的对话框
      dialogFormVisibleUserUpdate: false,
      loadingData: false,
      loadingText: "数据加载中",
      // 表单数据
      editForm: {
        checkList: [],
      },
      // 表单的验证规则
      rules: {
        checkList: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个角色",
            trigger: "change",
          },
        ],
      },
      // 确认保存按钮是否禁用
      isSave: false,
    };
  },
  watch: {
    stationID(newValue, oldValue) {
      if (newValue) {
        this.getUserList();
      }
    },
  },
  created() {
    // this.stationID = 1;
    // 钩子进入页面时，获取用户列表
    this.getUserList();
    //钩子进入页面时，获取角色列表
    this.getRoleList();
    //钩子进入页面时，获取病区列表
    this.getStations();
  },
  methods: {
    // 进入页面时根据用户单元加载用户列表
    getUserList() {
      this.loadingData = true;
      let params = {
        stationID: this.stationID,
      };
      GetUserRoleByStationID(params).then((result) => {
        this.loadingData = false;
        if (result.code == 1) {
          this.userList = result.data;
        } else {
          this._showTip("warning", result.message);
        }
      });
    },
    //获取角色列表
    getRoleList() {
      GetRoleList().then((result) => {
        if (result.code == 1) {
          this.roleList = result.data;
        } else {
          this._showTip("warning", result.message);
        }
      });
    },
    // 获取病区列表
    getStations() {
      this.optionsStations = [];
      GetStationList().then((result) => {
        if (result.code == 1) {
          this.optionsStations = result.data;
        } else {
          this._showTip("warning", result.message);
        }
      });
    },
    // 用户信息#查询
    userQuery() {
      let params = {
        // 要与后台一致
        UserID: this.input,
      };
      if (params.UserID.length == 0) {
        this.getUserList();
      } else {
        GetUserRoleByUserID(params).then((result) => {
          if (result.code == 1) {
            // 结果获取到数据result,通过他的属性给，this的属性，就是data中定义的字段赋值
            this.userList = result.data;
          } else {
            this._showTip("warning", result.message);
          }
        });
      }
    },
    // 用户信息#更新
    UserUpdate(row) {
      this.dialogFormVisibleUserUpdate = true;
      this.editForm.checkList = [];
      for (let i = 0; i < row.roleList.length; i++) {
        this.editForm.checkList.push(row.roleList[i].id);
      }
      this.userID = row.userID;
    },
    saveEditForm() {
      if (this.editForm.checkList.length == 0) {
        this.isSave = true;
        this._showTip("warning", "请选择用户角色！");
      } else {
        let params = {};
        params.UserID = this.userID;
        params.RoleIDs = this.editForm.checkList;
        UpdataUserById(params).then((result) => {
          this.dialogFormVisibleUserUpdate = false;
          if (result.code == 1) {
            this.getUserList();
            this._showTip("success", "修改成功！");
          } else {
            this._showTip("warning", result.message);
          }
        });
      }
      this.isSave = false;
    },
  },
};
</script>
<style lang="scss">
.user-role-wrap {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  .search-input {
    width: 70%;
    margin-top: 3px;
    width: 220px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .checkbox-group {
    .el-checkbox {
      width: 100px;
    }
  }
  .userRole-header {
    height: 35px;
    margin: 10px;
  }
  .userRole-main {
    height: calc(100% - 50px);
  }
  .select-station {
    margin-top: 3px;
    width: 180px;
  }
  .input-query {
    position: fixed;
    left: 260px;
  }
  .button-query {
    position: fixed;
    left: 440px;
  }
  /* 样式覆盖之自定义class名+组件class名 */
  .checkbox-roleList.el-checkbox {
    margin-left: 30px;
  }
}
</style>
