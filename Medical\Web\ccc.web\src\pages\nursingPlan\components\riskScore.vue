<!--
 * FilePath     : \ccc.web\src\pages\nursingPlan\components\riskScore.vue
 * Author       : 曹恩
 * Date         : 2022-03-11 15:37
 * LastEditors  : 陈超然
 * LastEditTime : 2024-09-03 10:20
 * Description  : 风险评分页面
 * CodeIterationRecord:  2022-05-31 2640-作为IT人员，我需要调整风险评估呈现方式，使风险表分类呈现 En
                         2022-06-08 2670-作为护理人员，我需要入院评估时间默认病人入院时间和第一次风险评估使时间默认入院评估时间，以提高工作效率 -杨欣欣
                         2022-06-10 2697-作为护理人员，我需要风险评估完成后可以直接操作集束护理，以利风险管理 Meng
                         2022-08-02 2816-作为护理人员，我需要护理评估后的首次风险评估，评估时间默认护理评估时间，以减少数据调整时间 -zxz
                         2022-09-08 增加是否显示分数判断 -杨欣欣
-->
<template>
  <base-layout class="risk-score" :showHeader="false" v-loading="loading">
    <el-table :data="riskTable" stripe border highlight-current-row :span-method="cellMerge" @row-click="getRiskClustr">
      <el-table-column prop="recordCategoryName" label="风险类别" align="center" width="130"></el-table-column>
      <el-table-column prop="recordName" label="风险评估量表" align="left" header-align="center" min-width="100">
        <template slot-scope="message">
          <div class="unconfirmed-risk" v-if="message.row.addDate == null">
            {{ message.row.recordName }}
          </div>
          <div v-if="message.row.addDate != null">
            {{ message.row.recordName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="point" label="分值" align="center" min-width="45">
        <template slot-scope="message">
          <span v-if="message.row.showPointFlag">{{ message.row.point }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="scoreRangeContent" label="风险等级" align="center">
        <template slot-scope="message">
          <div
            :style="{
              'background-color': message.row.highestRiskMark ? message.row.highestRiskMarkColor : '',
              color: message.row.highestRiskMark ? '#ffffff' : '#000000',
            }"
          >
            {{ message.row.scoreRangeContent }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="assessDateTime" label="评估时间" align="center" header-align="center" width="158">
        <template slot-scope="message">
          <span v-formatTime="{ value: message.row.assessDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="dueDay" label="到期时间" align="center" header-align="center" width="110">
        <template slot-scope="message">
          <span v-formatTime="{ value: message.row.dueDay, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="dueShiftName" label="到期班别" align="center" width="50" />
      <el-table-column prop="confirm" label="状态" align="center" width="70">
        <template slot-scope="scope">
          <div v-if="scope.row.confirm == true">已确认</div>
          <div class="unconfirmed-risk" v-if="scope.row.confirm != true">未确认</div>
        </template>
      </el-table-column>
      <el-table-column header-align="center" label="操作" width="65">
        <template slot-scope="scope">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click.stop="riskRowClick(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="打印告知书">
            <i
              class="iconfont icon-pdf"
              v-if="scope.row.emrDocumentID != null"
              @click.stop="getNoticePDF(scope.row)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 风险带出集束护理的措施 -->
    <div class="risk-cluster-intervention" v-if="problemList && problemList.length > 0 && problem">
      <div class="cluster-header">
        <label class="cluster">护嘱：{{ problem.nursingProblem }}</label>
        <label class="cluster-time">开始时间：</label>
        <el-date-picker
          v-model="problem.startTime"
          :clearable="false"
          type="datetime"
          placeholder="选择时间"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          @click.native.stop
          @change="problemTimeCheck(problem)"
        ></el-date-picker>
        <el-button class="cluster-save" type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
      </div>
      <!-- 现有护理问题所对应的护理措施 -->
      <el-table
        class="cluster-intervention"
        ref="intervention"
        :data="interventionList"
        highlight-current-row
        border
        stripe
        @select="selectionIntervention"
        @select-all="selectionAllIntervention"
        v-loading="loadingIntervention"
      >
        <el-table-column type="selection" :width="convertPX(40)" class-name="select" align="center"></el-table-column>
        <el-table-column label="频次" width="140" header-align="center">
          <template v-if="frequencyList && frequencyList.length > 0" slot-scope="intervention">
            <frequency-selector
              v-model="intervention.row.frequencyID"
              :frequencyList="frequencyList"
              @select-item="selectFrequency($event, intervention.row)"
            ></frequency-selector>
          </template>
        </el-table-column>
        <el-table-column
          prop="interventionName"
          min-width="170"
          label="护理措施"
          header-align="center"
        ></el-table-column>
        <el-table-column label="开始时间" width="140" header-align="center">
          <template slot-scope="intervention">
            <el-date-picker
              class="intervention-start"
              v-model="intervention.row.startDate"
              :clearable="false"
              type="datetime"
              placeholder=""
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              @change="startTimeCheck(intervention.row)"
            ></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" width="140" header-align="center">
          <template slot-scope="intervention">
            <el-date-picker
              class="intervention-end"
              v-model="intervention.row.endDate"
              :clearable="false"
              type="datetime"
              placeholder=""
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              @change="endTimeCheck(intervention.row)"
            ></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="首次" width="50" align="center">
          <template slot-scope="intervention">
            <el-checkbox
              :disabled="!intervention.row.selected"
              v-model="intervention.row.firstDayFlag"
              true-label="*"
              false-label=""
              @change="updateList(intervention.row)"
            ></el-checkbox>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 告知书打印 -->
    <el-dialog
      :close-on-click-modal="false"
      custom-class="no-footer"
      :title="noticeName"
      :visible.sync="noticeVisible"
      v-if="noticeVisible"
      fullscreen
    >
      <document-sign :iframeType="'application/x-google-chrome-pdf'" :signParams="notificationParams"></document-sign>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      v-loading="loading"
      :element-loading-text="loadingText"
      :title="dialogTitle"
      :visible.sync="centerDialogVisible"
      custom-class="no-footer"
      v-if="centerDialogVisible"
      fullscreen
    >
      <risk-component :params="conponentParams" @result="result"></risk-component>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="deselectTitle"
      custom-class="reasons"
      :visible.sync="showDeselectFlag"
      :before-close="cancelDeselectReason"
    >
      <span class="comment-label">原因：</span>
      <el-input
        class="comment"
        v-model="deselectReason"
        type="textarea"
        :autosize="{ minRows: 4, maxRows: 4 }"
        placeholder="请输入内容"
        resize="none"
      />
      <div slot="footer">
        <el-button @click="cancelDeselectReason">取消</el-button>
        <el-button type="primary" @click="saveDeselectReason">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import { RiskAssess } from "@/api/Assess";
import { GetTypeFrequency } from "@/api/Frequency";
import {
  GetIntervention,
  GetNeedFirstFrequency,
  GetPatientNursingClusterViewBySourceID,
  SaveCluster,
} from "@/api/NursingPlan";
import { SaveDeselectReason } from "@/api/PatientProblem";
import { GetScheduleTop } from "@/api/Setting";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import baseLayout from "@/components/BaseLayout";
import documentSign from "@/components/DocumentSign";
import frequencySelector from "@/components/selector/frequencySelector";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import { SendRequestParams } from "@/utils/huiMei";
export default {
  components: {
    frequencySelector,
    baseLayout,
    riskComponent,
    documentSign,
  },
  props: {
    inpatientinfo: {
      type: Object,
      default: () => {
        return null;
      },
    },
    lastAssessDateTime: {
      type: String,
      default: () => {
        return null;
      },
    },
  },
  data() {
    return {
      riskTable: [],
      centerDialogVisible: false,
      dialogTitle: "",
      sourceID: "",
      loading: false,
      noticeVisible: false,
      loadingText: "",
      noticeName: "",
      riskAssessDateTime: undefined,
      clickRow: undefined,
      //首次是否默认护理评估时间
      useAdmissionAssessDateFlag: false,
      //选中的集束护理
      problemList: [],
      //显示的措施列表
      interventionList: [],
      //当前问题ID
      currentPatientProblemID: undefined,
      //选中的措施
      selectInterventions: [],
      //频次字典
      frequencyList: undefined,
      //是否显示措施标记
      loadingIntervention: true,
      //设置首次
      needFirstFrequencyList: [],
      problem: undefined,
      conponentParams: {},
      //显示弹窗
      showDeselectFlag: false,
      //取消勾选原因文本
      deselectReason: undefined,
      //取消勾选措施集合
      deselectIntervention: [],
      //取消勾选是否填写原因
      showDeselectReasonFlag: false,
      //获取告知书的参数
      notificationParams: {
        inpatientID: undefined,
        emrDocumentID: undefined,
        signedDocFlag: true,
      },
      //措施取消或频次调整弹框标题
      deselectTitle: undefined,
      //需要填写原因的频次ID
      needEnterReasonFrequencylDs: [],
      //选择前原有频次
      oldFrequency: undefined,
      //原始措施集合
      originalInterventionList: [],
    };
  },
  watch: {
    "inpatientinfo.inpatientID": {
      async handler(newValue) {
        this.riskTable = [];
        if (!newValue) return;
        this.getCalcRisk();
      },
      immediate: true,
    },
  },
  created() {
    this.getAssessDateSetting();
  },
  methods: {
    /**
     * description: 点击修改风险
     * param {*} row 要修改的行数据
     * param {*} column
     * param {*} event
     * return {*}
     */
    async riskRowClick(row, column, event) {
      this.clickRow = row;
      this.dialogTitle = this.getDialogTitle(this.inpatientinfo, row.recordName, row.dateTime);
      this.centerDialogVisible = true;
      this.conponentParams = {
        patientInfo: this.inpatientinfo,
        showPoint: row.showPointFlag,
        showTime: true,
        showStyle: row.showStyle,
        recordListID: row.recordListID,
        recordsCode: row.recordsCode,
        patientScoreMainID: row.patientScoreMainID,
        lastAssessDateTime: this.lastAssessDateTime,
        assessTime: row.assessDateTime,
        useAdmissionAssessDateFlag: this.useAdmissionAssessDateFlag,
      };
    },

    /**
     * description: 组件关闭回调函数
     * param {*} resultFlag 返回状态，true保存成功；false保存失败
     * param {*} resultData 保存成功后的返回数据
     * return {*}
     */
    result(resultFlag, resultData) {
      this.centerDialogVisible = false;
      if (resultFlag) {
        let message = this.getSaveMessage(resultData && resultData.assessTime);
        SendRequestParams();
        //同组风险保存提示
        if (resultData && resultData.recordListID && resultData.recordName) {
          this.deelWithHaveSameTypeRisk(resultData, message);
        } else {
          this._showTip("success", message);
          this.getCalcRisk();
        }
      }
    },
    /**
     * description: 获取保存结果提示信息
     * param {*}
     * return {*}
     */
    getSaveMessage(assessTime) {
      let sucMessage = "保存成功!";
      if (!assessTime) {
        return sucMessage;
      }
      if (
        this.clickRow.assessDateTime &&
        this._datetimeUtil.formatDate(assessTime, "yyyy-MM-dd hh:mm") !=
          this._datetimeUtil.formatDate(this.clickRow.assessDateTime, "yyyy-MM-dd hh:mm")
      ) {
        sucMessage = sucMessage + "  请调整该风险触发集束护理时间范围";
      }
      return sucMessage;
    },
    /**
     * description: 同组风险保存提示处理
     * param {*} data
     * param {*} message
     * return {*}
     */
    deelWithHaveSameTypeRisk(data, message) {
      this._confirm(message + " 是否要继续评估 " + data.recordName, "同组风险评估", (flag) => {
        if (flag) {
          let row = {
            recordListID: data.recordListID,
            patientScoreMainID: "",
            sourceID: "",
          };
          this.riskRowClick(row);
        } else {
          this.centerDialogVisible = false;
          this._showTip("success", message);
          this.getCalcRisk();
        }
      });
    },

    getCalcRisk() {
      this.loading = true;
      this.loadingText = "加载中……";
      this.riskTable = [];
      let params = {
        inpatientID: this.inpatientinfo.inpatientID,
        chartNo: this.inpatientinfo.chartNo,
        stationID: this.inpatientinfo.stationID,
        departmentListID: this.inpatientinfo.departmentListID,
      };
      RiskAssess(params).then((response) => {
        this.loading = false;
        this.loadingIntervention = false;
        if (this._common.isSuccess(response)) {
          this.riskTable = response.data;
          if (this.riskTable && this.riskTable.length > 0) {
            this.getSpanArr(this.riskTable);
          }
        }
      });
      this.GetTypeFrequency();
      this.GetNeedFirst();
    },
    /**
     * description: 获取告知书pdf
     * return {*}
     * param {*} row 风险评估页面中被点击的记录行
     */
    getNoticePDF(row) {
      this.noticeVisible = true;
      this.noticeName = this.getDialogTitle(this.inpatientinfo, "【风险告知书】" + row.recordName, row.dateTime);
      this.notificationParams = {
        scorePoint: row.point,
        emrDocumentID: row.emrDocumentID,
        inpatientID: this.inpatientinfo.inpatientID,
      };
    },
    /**
     * description: 获取弹框标题
     * return {*}
     * param {*} patientInfo
     * param {*} content
     * param {*} dateTime
     */
    getDialogTitle(patientInfo, content, dateTime) {
      let str = `${patientInfo.bedNumber}-${patientInfo.patientName}【${patientInfo.gender}-${patientInfo.age}】`;
      str += content ? `-- ${content}` : "";
      str += dateTime ? `-- 评估时间:${dateTime}` : "";
      return str;
    },

    /**
     * description: 表格合并
     * param {*} data
     * return {*}
     */
    getSpanArr(data) {
      this.spanArr = [];
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].recordCategoryName == data[i - 1].recordCategoryName) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    /**
     * description: 表格合并
     * param {*} row
     * param {*} column
     * param {*} rowIndex
     * param {*} columnIndex
     * return {*}
     */
    cellMerge({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        //合并第一列和第二列
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    /**
     * description: 获取风险评估默认时间配置（2022-08-02调整使用UseAdmissionAssessDateFlag配置，原本使用配置错误  ）
     * param {*}
     * return {*}
     */
    getAssessDateSetting() {
      let param = {
        settingTypeCode: "UseAdmissionAssessDateFlag",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.useAdmissionAssessDateFlag = response.data;
        }
      });
    },
    /**
     * description:1、点击风险获取由风险带出集束护理的措施列表
     * param {*} row
     * return {*}
     */
    async getRiskClustr(row) {
      this.problemList = [];
      this.problem = undefined;
      this.interventionList = [];
      // 1-1、根据风险获取集束护理
      let params = {
        inpatientID: this.inpatientinfo.inpatientID,
        stationID: this.inpatientinfo.stationID,
        sourceID: row.patientScoreMainID,
      };
      await GetPatientNursingClusterViewBySourceID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data) {
            this.problemList = result.data;
          }
        }
      });
      if (this.problemList.length > 0) {
        this.problem = this.problemList[0];
        // 1-2、根据集束护理获取对应措施
        await this.showIntervention(this.problem, false);
      }
    },
    /**
     * description: 获取频次列表
     * return {*}
     */
    async GetTypeFrequency() {
      this.frequencyList = [];
      let params = {
        visibleFlag: true,
      };
      await GetTypeFrequency(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.frequencyList = result.data;
        }
      });
    },
    /**
     * description: 判断是否勾选首次
     * return {*}
     */
    async GetNeedFirst() {
      this.needFirstFrequencyList = [];
      let params = {
        inpatientID: this.inpatientinfo.inpatientID,
      };
      await GetNeedFirstFrequency(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.needFirstFrequencyList = result.data;
        }
      });
      let param = {
        SettingTypeCode: "NeedEnterReasonFrequencylDs",
      };
      await GetScheduleTop(param).then((result) => {
        if (this._common.isSuccess(result)) {
          this.needEnterReasonFrequencylDs = ["31", "131"];
        }
      });
    },
    /**
     * description: 1-2、根据集束护理获取对应措施
     * param {*} row
     * return {*}
     */
    async showIntervention(row, isSave) {
      //保存过来的点击次数置空
      if (isSave) {
        row.clickTimes = undefined;
      }
      if (row.clickTimes == undefined) {
        row.clickTimes = 1;
      } else {
        row.clickTimes = row.clickTimes + 1;
      }
      // 获取护理问题对应的护理措施
      this.interventionList = [];
      this.currentPatientProblemID = row.patientProblemId;
      this.loadingIntervention = true;
      let params = {
        problemID: row.problemId,
        patientProblemID: row.patientProblemId,
      };
      await GetIntervention(params).then((result) => {
        this.loadingIntervention = false;
        if (this._common.isSuccess(result)) {
          this.interventionList = result.data.patientInterventions;
          this.originalInterventionList = this._common.clone(this.interventionList);
          for (let i = 0; i < this.interventionList.length; i++) {
            // 防止有重复措施，添加序号区分
            this.interventionList[i].index = i;
            this.interventionList[i].startTimeStartLimit = row.startTime;
            this.interventionList[i].originalStartTime = this.interventionList[i].startDate;
            this.interventionList[i].originalEndTime = this.interventionList[i].endDate;
          }
          // 默认选中
          this.$nextTick(() => {
            if (this.$refs.intervention) {
              this.$refs.intervention.setCurrentRow(this.interventionList[0]);
            }
          });
          this.showDeselectReasonFlag = result.data.interventionDeselectFlag;
        }
      });
      // 设置已经选择的值 row.clickTimes > 1 第二次点击，取已选的数据回显
      if (row.clickTimes > 1) {
        let selectList = undefined;
        if (this.selectInterventions.length > 0) {
          for (let i = 0; i < this.selectInterventions.length; i++) {
            if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
              selectList = this.selectInterventions[i].interventions;
              break;
            }
          }
        }
        // 回显本次选择的项目
        if (selectList && selectList.length > 0) {
          this.setSelectFlag(this.currentPatientProblemID, true);
          selectList.forEach((intervention) => {
            for (let i = 0; i < this.interventionList.length; i++) {
              if (
                this.interventionList[i].interventionID == intervention.interventionID &&
                this.interventionList[i].index == intervention.index &&
                intervention.selected
              ) {
                this.interventionList[i].selected = true;
                this.interventionList[i].frequencyID = intervention.frequencyID;
                this.interventionList[i].frequency = intervention.frequency;
                this.interventionList[i].frequencyDescription = intervention.frequencyDescription;
                this.interventionList[i].firstDayFlag = intervention.firstDayFlag;
                this.interventionList[i].startDate = intervention.startDate;
                this.interventionList[i].endDate = intervention.endDate;
                if (this.$refs.intervention) {
                  this.$nextTick(() => {
                    this.$refs.intervention.toggleRowSelection(this.interventionList[i]);
                  });
                }
              } else {
                this.interventionList[i].selected = false;
              }
            }
          });
        }
      } else {
        // row.clickTimes == 1 第一次点击，取上次保存的数据回显
        let oldList = [];
        for (let i = 0; i < this.interventionList.length; i++) {
          if (this.interventionList[i].selected || this.interventionList[i].patientInterventionID) {
            this.interventionList[i].selected = true;
            if (this.$refs.intervention) {
              this.$nextTick(() => {
                this.$refs.intervention.toggleRowSelection(this.interventionList[i]);
              });
            }
            oldList.push(this.interventionList[i]);
          }
        }
        if (oldList.length > 0) {
          let temp = {
            patientProblemID: this.currentPatientProblemID,
            interventions: oldList,
          };
          //首次回显要清空，要不会重复加入
          this.selectInterventions = [];
          this.selectInterventions.push(temp);
          this.setSelectFlag(this.currentPatientProblemID, true);
        } else {
          this.setSelectFlag(this.currentPatientProblemID, false);
        }
      }
    },
    /**
     * description: 设置选中标志
     * param {*} patientProblemID
     * param {*} flag
     * return {*}
     */
    setSelectFlag(patientProblemID, flag) {
      for (let i = 0; i < this.problemList.length; i++) {
        if (this.problemList[i].patientProblemId == patientProblemID) {
          this.$set(this.problemList[i], "selected", flag);
          break;
        }
      }
    },
    /**
     * description: 2、点选措施
     * param {*} interventions
     * param {*} row
     * return {*}
     */
    selectionIntervention(interventions, row) {
      this.deselectTitle = "措施取消勾选原因";
      if (row.selected) {
        this.showDeselectReason(row, true, true);
      }
      // 2-1、TPRFlag提示检测
      this.TPRFlagCheck(row.tprFlag);
      row.selected = !row.selected;
      // 2-2、设置首次
      this.setFirst(row);
      let isExist = false;
      for (let i = 0; i < this.selectInterventions.length; i++) {
        if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
          let flag = false;
          for (let j = this.selectInterventions[i].interventions.length - 1; j >= 0; j--) {
            if (
              this.selectInterventions[i].interventions[j].interventionID == row.interventionID &&
              this.selectInterventions[i].interventions[j].index == row.index
            ) {
              flag = true;
              if (!row.selected) {
                if (row.patientInterventionID) {
                  this.selectInterventions[i].interventions[j].selected = false;
                } else {
                  // 移除
                  this.selectInterventions[i].interventions.splice(j, 1);
                }
              } else {
                this.selectInterventions[i].interventions[j].selected = true;
              }
              break;
            }
          }
          if (!flag && row.selected) {
            row.startDate = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
            this.selectInterventions[i].interventions.push(row);
          }
          if (interventions.length == 0) {
            this.setSelectFlag(this.currentPatientProblemID, false);
          } else {
            this.setSelectFlag(this.currentPatientProblemID, true);
          }
          isExist = true;
          break;
        }
      }
      if (!isExist && interventions.length > 0) {
        let temp = {
          patientProblemID: this.currentPatientProblemID,
          interventions: interventions,
        };
        temp.interventions.forEach((item) => {
          item.startDate = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
        });
        this.selectInterventions.push(temp);
        this.setSelectFlag(this.currentPatientProblemID, true);
      }
    },
    /**
     * description: 2-1、TPRFlag提示检测
     * param {*} tprFlag
     * return {*}
     */
    TPRFlagCheck(tprFlag) {
      if (tprFlag) {
        this._showTip("warning", "该操作可能会影响体温单数据！请谨慎操作！");
      }
    },
    /**
     * description: 2-2、设置首次
     * param {*} row
     * return {*}
     */
    setFirst(row) {
      row.firstDayFlag = "";
      let length = this.needFirstFrequencyList.length;
      if (
        row.selected &&
        this.needFirstFrequencyList &&
        length > 0 &&
        this.needFirstFrequencyList.indexOf(Number(row.frequencyID)) != -1
      ) {
        row.firstDayFlag = "*";
      }
      this.updateList(row);
    },
    /**
     * description:将已选中项目更改的内容同步到selectInterventions中
     * param {*} row
     * return {*}
     */
    updateList(row) {
      for (let i = 0; i < this.selectInterventions.length; i++) {
        if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
          let interventions = this.selectInterventions[i].interventions;
          for (let j = 0; j < interventions.length; j++) {
            if (interventions[j].interventionID == row.interventionID && interventions[j].index == row.index) {
              interventions[j] = row;
              break;
            }
          }
          break;
        }
      }
    },
    /**
     * description: 3、措施全选
     * param {*} interventions
     * return {*}
     */
    selectionAllIntervention(interventions) {
      //全选
      if (interventions.length) {
        interventions.forEach((item) => {
          item.selected = false;
          this.selectionIntervention(interventions, item);
        });
      } else {
        //全部取消
        this.interventionList.forEach((item) => {
          item.selected = false;
          this.setSelectFlag(this.currentPatientProblemID, false);
          this.updateList(item);
          this.showDeselectReason(item, true, false);
        });
      }
    },
    /**
     * description: 4、选择频次
     * param {*} row
     * return {*}
     */
    selectFrequency(event, row) {
      this.oldFrequency = row.frequency; //调整前的频次，用于取消填写时还原原来选中的频次
      row.frequency = event.search;
      let originalIntervention = this.originalInterventionList[row.index];
      //风险类集束 默认推荐措施 原有频次不是prn改为prn时 弹窗填写原因
      if (
        this.showDeselectReasonFlag &&
        row.selected &&
        row.patientInterventionID && //默认推荐措施
        this.needEnterReasonFrequencylDs.length &&
        !this.needEnterReasonFrequencylDs.includes(originalIntervention.frequencyID.toString()) && //初始频次
        this.needEnterReasonFrequencylDs.includes(row.frequencyID.toString()) && //当前频次
        !this.deselectIntervention.find((m) => m == row.patientInterventionID) //填写过一次后不重复填写
      ) {
        this.deselectTitle = "措施频次由非" + row.frequency + "调整为" + row.frequency + "原因";
        this.showDeselectReason(row, true, true);
      }
      // 设置是否首次
      this.setFirst(row);
      //TPRFlag提示检测
      this.TPRFlagCheck(row.tprFlag);
    },
    /**
     * description: 5、选择开始时间
     * param {*} row
     * return {*}
     */
    startTimeCheck(row) {
      let check = false;
      if (!row.startDate) {
        this._showTip("warning", "开始时间不得为空！");
        row.startDate = row.originalStartTime;
        return;
      }
      if (row.endDate < row.startDate) {
        this._showTip("warning", "开始时间不得晚于结束时间！");
        check = true;
      }
      if (
        this._datetimeUtil.formatDate(row.startDate, "yyyy-MM-dd hh:mm") <
        this._datetimeUtil.formatDate(row.startTimeStartLimit, "yyyy-MM-dd hh:mm")
      ) {
        this._showTip("warning", "开始时间不得早于集束护理开始时间！");
        check = true;
      }
      if (
        row.endDate &&
        this._datetimeUtil.formatDate(row.startDate, "yyyy-MM-dd hh:mm") >
          this._datetimeUtil.formatDate(row.endDate, "yyyy-MM-dd hh:mm")
      ) {
        this._showTip("warning", "开始时间不得晚于于集束护理结束时间！");
        check = true;
      }
      if (check) {
        row.startDate = row.originalStartTime;
      }
    },
    /**
     * description: 6、选择结束时间
     * param {*} row
     * return {*}
     */
    endTimeCheck(row) {
      let check = false;
      if (row.endDate < row.startDate) {
        this._showTip("warning", "结束时间不得早于开始时间！");
        check = true;
      }
      if (row.endDate < this._datetimeUtil.getNow("yyyy-MM-dd hh:mm")) {
        this._showTip("warning", "结束时间不得早于当前时间！");
        check = true;
      }
      if (check) {
        row.endDate = row.originalEndTime;
      }
    },
    /**
     * description: 7、点击保存
     * return {*}
     */
    async save() {
      if (this.loadingIntervention || this.problemList.length <= 0) {
        return;
      }
      this.loadingIntervention = true;
      let saveParams = {};
      let submitList = [];
      // 循环组装提交的措施
      for (let i = 0; i < this.problemList.length; i++) {
        let problem = this.problemList[i];
        if (!problem.selected) {
          this.loadingIntervention = false;
          this._showTip("warning", "<font color='red'><b>" + problem.nursingProblem + "</b></font>没有选择措施！");
          return;
        }
        let temp = this.selectInterventions.find((intervention) => {
          return intervention.patientProblemID == problem.patientProblemId;
        });
        let interventions = [];
        if (temp) {
          let newTemp = JSON.parse(JSON.stringify(temp));
          newTemp.interventions.forEach((intervention) => {
            if (!intervention.selected) {
              intervention.interventionID = null;
            }
            if (typeof intervention.frequencyID == "object") {
              intervention.frequencyID = intervention.frequencyID[1];
            }
            this.frequencyList.find((frequency) => {
              frequency.children.forEach((children) => {
                if (children.value === intervention.frequencyID) {
                  intervention.frequency = children.search;
                  intervention.frequencyDescription = children.label;
                  intervention.frequencyID = Number(children.value);
                }
              });
            });
          });
          interventions = newTemp.interventions;
        }
        submitList.push({
          patientInterventions: interventions,
          patientProblemID: problem.patientProblemId,
          problemID: problem.problemId,
          inpatientID: this.inpatientinfo.inpatientID,
          startTime: problem.startTime,
        });
      }
      let patientInfo = {
        bedID: this.inpatientinfo.bedID,
        bedNumber: this.inpatientinfo.bedNumber,
        caseNumber: this.inpatientinfo.caseNumber,
        chartNo: this.inpatientinfo.chartNo,
        departmentListID: this.inpatientinfo.departmentListID,
        gender: this.inpatientinfo.gender,
        genderCode: this.inpatientinfo.genderCode,
        inpatientID: this.inpatientinfo.inpatientID,
        patientID: this.inpatientinfo.patientID,
        stationID: this.inpatientinfo.stationID,
      };
      saveParams = {
        patient: patientInfo,
        submitList: submitList,
        singleClusterSubmitFlag: "*",
      };
      await SaveCluster(saveParams).then((result) => {
        this.loadingIntervention = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
        }
      });
      await this.showIntervention(this.problemList[0], true);
    },
    /**
     * description: 集束护理时间检核
     * param {*} row
     * return {*}
     */
    problemTimeCheck(row) {
      let check = false;
      if (row.startTime < row.startTimeStartLimit) {
        this._showTip("warning", "开始时间不得早于" + row.problemSource + "时间！");
        check = true;
      }
      if (row.startTime > this._datetimeUtil.getNow("yyyy-MM-dd hh:mm")) {
        this._showTip("warning", "开始时间不得晚于当前时间！");
        check = true;
      }
      if (check) {
        row.startTime = row.originalStartTime;
      } else {
        this._showTip("warning", "请确认计划开始、结束时间是否需要调整！");
        //更新措施开始时间限制
        for (let i = 0; i < this.interventionList.length; i++) {
          if (!this.interventionList[i].eventTrigger) {
            this.interventionList[i].startDate = row.startTime;
          }
          this.interventionList[i].startTimeStartLimit = row.startTime;
          if (this.interventionList[i].originalStartTime < row.startTime) {
            this.interventionList[i].originalStartTime = row.startTime;
          }
        }
      }
    },
    /**
     * description: 显示取消勾选原因弹窗
     * param {*} row 措施
     * param {*} clearReason 清除原因
     * param {*} clearDeselectIntervention 清除取消勾选集合
     * return {*}
     */
    showDeselectReason(row, clearReason, clearDeselectIntervention) {
      if (!this.showDeselectReasonFlag) {
        return;
      }
      if (clearReason) {
        this.deselectReason = undefined;
      }
      if (clearDeselectIntervention) {
        this.deselectIntervention = [];
      }
      this.showDeselectFlag = true;
      if (row.patientInterventionID && !this.deselectIntervention.find((m) => m == row.patientInterventionID)) {
        this.deselectIntervention.push(row.patientInterventionID);
      }
    },
    /**
     * description: 保存取消勾选原因
     * return {*}
     */
    saveDeselectReason() {
      if (!this.deselectReason || this.deselectReason.length == 0) {
        this._showTip("warning", "未填写取消措施原因");
        return;
      }
      this.showDeselectFlag = false;
      this.loading = true;
      let params = [];
      this.deselectIntervention.forEach((item) => {
        params.push({
          patientProblemID: this.currentPatientProblemID,
          inpatientID: this.inpatientinfo.inpatientID,
          patientInterventionID: item,
          deselectReason: this.deselectReason,
        });
      });
      SaveDeselectReason(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
        }
      });
    },
    /**
     * description: 关闭弹窗
     * return {*}
     */
    cancelDeselectReason() {
      this.showDeselectFlag = false;
      this.interventionList.forEach((item) => {
        if (this.deselectIntervention.find((m) => m == item.patientInterventionID)) {
          item.selected = true;
          //this.updateList(item);
          this.$nextTick(() => {
            this.$refs.intervention.toggleRowSelection(item, true);
          });
        }
      });
      //还原频次
      this.frequencyList.find((frequency) => {
        frequency.children.forEach((children) => {
          if (children.search === this.oldFrequency) {
            item.frequencyID = children.value;
            item.frequency = this.oldFrequency;
          }
        });
      });
    },
  },
};
</script>
<style lang="scss">
.risk-score {
  height: 100%;
  .risk-cluster-intervention {
    .cluster-header {
      margin: 10px;
      .cluster-time {
        margin-left: 30px;
        width: 150px;
      }
      .cluster-save {
        float: right;
      }
      .cluster-intervention {
        .intervention-start {
          width: 125px;
        }
        .intervention-end {
          width: 125px;
        }
      }
    }
  }
  .unconfirmed-risk {
    color: red;
  }
  .iconfont.icon-pdf {
    font-weight: 500 !important;
  }
  .conponent-dialog.el-dialog {
    .el-button.el-button--primary span {
      color: #ffffff;
    }
  }
  .reasons.el-dialog {
    width: 520px;
    height: 300px;
  }
}
</style>
