<!--
 * FilePath     : \src\pages\dictionaryMaintain\hospitalBuilding\location.vue
 * Author       : 来江禹
 * Date         : 2022-07-23 08:11
 * LastEditors  : 来江禹
 * LastEditTime : 2022-09-12 14:32
 * Description  : 楼层位置维护，对楼栋对应楼层的楼层位置进行维护管理
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="hospital-location">
    <div class="hospital-location-header" slot="header">
      <span class="btn-list">
        <el-button type="success" icon="iconfont icon-add" @click="addLocation">新增</el-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
        <el-button class="print-button" icon="iconfont icon-back" @click="goBack">返回</el-button>
      </span>
      <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    </div>
    <div class="location-table">
      <el-table :data="locationTabList" border stripe>
        <el-table-column type="index" width="50" label="序号"></el-table-column>
        <el-table-column label="楼栋" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.buildID">{{ scope.row.buildName }}</span>
            <el-select
              v-else
              class="table-build-select"
              v-model="scope.row.buildListID"
              placeholder="请选择楼栋"
              @change="getBuidId(scope.row)"
            >
              <el-option
                v-for="(item, index) in buildList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="楼层" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.floorID">
              {{ scope.row.floorName }}
            </span>
            <el-select v-else class="table-floor-select" v-model="scope.row.floorListID" placeholder="请选择楼层">
              <el-option
                v-for="(item, index) in scope.row.floorList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="楼层位置名称" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.locationID">{{ scope.row.locationName }}</span>
            <el-input
              v-else
              class="table-location-input"
              v-model="scope.row.locationListName"
              placeholder="请输入楼层位置名称"
              @change="getLocationName()"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" width="80px" align="left">
          <template slot-scope="scope">
            <el-tooltip content="删除">
              <i
                class="iconfont icon-del"
                @click="deletectLocation(scope.row.locationID, scope.row.locationCode, scope.row)"
              ></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import progressView from "@/components/progressView";
import {
  GetFloorDatas,
  GetLoactionDatas,
  SaveBuildDatas,
  DeleteSettingDescriptionLocationDatas,
  GetSettingDescriptionOne,
} from "@/api/WardMaintenance";
export default {
  components: {
    baseLayout,
    stationSelector,
    progressView,
  },
  data() {
    return {
      list: [],
      floorTabList: [],
      buildIndex: "",
      buildList: [],
      floorList: [],
      locationTabList: [],
      settingTypeCode: "WardPartition",
      code: undefined,
      successCode: "",
      //进度条开关
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
    };
  },
  created() {
    this.refresh();
  },
  methods: {
    /**
     * description: 进度条关闭函数
     * return {*}
     */
    progressClose() {
      this.progressFlag = false;
      this.renewMessageData();
    },
    /**
     * description: 重置进度条
     * return {*}
     */
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    /**
     * description: 刷新页面数据
     * return {*}
     */
    async refresh() {
      this.locationTabList = [];
      this.buildList = [];
      this.floorTabList = [];
      this.getSettingfloor("WardBuilding");
      this.getSettingLocation("WardBuilding");
      this.getBuildSelect();
    },
    /**
     * description: 新增按钮实现新增表格行
     * return {*}
     */
    addLocation() {
      let row = {
        buildName: "",
        buildID: "",
        floorName: "",
        floorID: "",
        locationName: "",
        locationID: "",
        locationCode: "",
      };
      this.locationTabList.push(row);
    },
    /**
     * description: 获取当前页面表格显示的数据
     * param {*} SettingTypeCode
     * return {*}
     */
    async getSettingLocation(SettingTypeCode) {
      let params = {
        SettingTypeCode: SettingTypeCode,
      };
      await GetLoactionDatas(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            for (let index = 0; index < list.length; index++) {
              const selectList = list[index];
              let params = {
                buildName: selectList.buildName,
                buildID: selectList.buildID,
                floorName: selectList.floorName,
                floorID: selectList.floorID,
                locationName: selectList.loactionName,
                locationID: selectList.locationID,
                locationCode: selectList.locationCode,
              };
              this.locationTabList.push(params);
            }
          }
        }
      });
    },
    /**
     * description: 获取楼栋列下拉框数据
     * return {*}
     */
    async getBuildSelect() {
      let params = {
        SettingTypeCode: "WardBuilding",
      };
      await GetSettingDescriptionOne(params).then((result) => {
        if (this._common.isSuccess) {
          let list = result.data;
          if (list != null) {
            list.forEach((build) => {
              let params = {
                Description: build.description,
                TypeValue: build.typeValue,
              };
              this.buildList.push(params);
            });
          }
        }
      });
    },
    /**
     * description: 获取后台已经维护好的楼层数据
     * param {*} SettingTypeCode
     * return {*}
     */
    async getSettingfloor(SettingTypeCode) {
      let params = {
        SettingTypeCode: SettingTypeCode,
      };
      await GetFloorDatas(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            list.forEach((floor) => {
              let params = {
                floorName: floor.floorName,
                floorID: floor.floorID,
                floorCode: floor.floorCode,
              };
              this.floorTabList.push(params);
            });
          }
        }
      });
    },
    /**
     * description: 根据楼栋TypeValue动态的更新楼层下拉框数据
     * return {*}
     */
    getFloorSelect(row) {
      let floorList = [];
      this.floorList = [];
      this.floorTabList.forEach((floorIndex) => {
        if (floorIndex.floorCode == "WardFloor" + "_" + this.buildIndex) {
          let params = {
            Description: floorIndex.floorName,
            TypeValue: floorIndex.floorID,
          };
          floorList.push(params);
        }
        this.$set(row, "floorList", floorList);
      });
    },
    /**
     * description: 获取新增输入框数据以便于保存，实现批量保存
     * return {*}
     */
    getLocationName() {
      let modifyDatas = this.locationTabList;
      if (modifyDatas.length == 0) {
        return undefined;
      }
      return modifyDatas;
    },
    /**
     * description: 获取下拉框异动数据，可以根据下拉框TypeValue选取对应楼层
     * param {*} index
     * return {*}
     */
    getBuidId(row) {
      if (!row.buildListID) {
        return;
      }
      this.buildIndex = row.buildListID;
      this.$set(row, "floorListID", "");
      this.getFloorSelect(row);
    },

    /**
     * description: 保存页面新增数据，写入后端
     * return {*}
     */
    async save() {
      let datas = this.getLocationName();
      let successMessage = "";
      let failMessage = "";
      for (let index = 0; index < datas.length; index++) {
        const locationData = datas[index];
        this.progressFlag = true;
        //判断下拉框输入框是否有数据没有输入，全部输入执行保存
        if (locationData.locationListName && locationData.buildListID && locationData.floorListID) {
          let params = {
            SettingType: "216",
            SettingTypeCode: this.settingTypeCode + "_" + locationData.buildListID + "_" + locationData.floorListID,
            Description: locationData.locationListName,
          };
          let messageItem = locationData.locationListName;
          await SaveBuildDatas(params).then((res) => {
            if (res.code == 1) {
              successMessage = index == 0 ? messageItem : successMessage + "," + messageItem;
            } else {
              failMessage = failMessage + " " + messageItem;
            }
          });
        }
        //配置进度条内容
        let progress = (((index + 1) / datas.length) * 100).toFixed(0);
        //配置进度条内容
        this.messageData[0].value = Number(progress);
        this.messageData[1].value = successMessage;
        this.messageData[2].value = failMessage;
        this.messageData[3].value = "";
      }
      await this.refresh();
    },
    /**
     * description: 删除当前行数据
     * param {*} index
     * param {*} settingTypeCode
     * return {*}
     */
    deletectLocation(index, settingTypeCode, row) {
      if (index == "" && settingTypeCode == "") {
        let index = this.locationTabList.findIndex((location) => location == row);
        if (index >= 0) {
          this.locationTabList.splice(index, 1);
        }
      } else {
        this._deleteConfirm("确定删除数据么？", (flag) => {
          if (flag) {
            let params = {
              TypeValue: index,
              SettingTypeCode: settingTypeCode,
            };
            DeleteSettingDescriptionLocationDatas(params).then((res) => {
              if (this._common.isSuccess(res)) {
                this._showTip("success", "删除成功");
                this.refresh();
              }
            });
          }
        });
      }
    },
    /**
     * description:返回按钮跳转页面
     * return {*}
     */
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss">
.hospital-location {
  .hospital-location-header {
    .btn-list {
      display: block;
      float: right;
    }
  }
  .location-table {
    .table-build-select,
    .table-floor-select,
    .table-location-input {
      min-width: 96%;
    }
  }
}
</style>
