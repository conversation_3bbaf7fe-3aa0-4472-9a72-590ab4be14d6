<!--
 * FilePath     : \src\pages\riskAssessment\recordRiskRating.vue
 * Author       : 苏军志
 * Date         : 2021-04-06 09:42
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-19 16:59
 * Description  : 风险评估页面 - 支持正常风险模式和补录模式
-->
<template>
  <base-layout class="record-risk-rating">
    <div slot="header">
      <span>日期:</span>
      <el-date-picker
        v-model="queryCriteria.startTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="startPickerOption"
        @change="getScoreMainList()"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        v-model="queryCriteria.endTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="endPickerOption"
        @change="getScoreMainList()"
      ></el-date-picker>

      <record-selector
        :recordID="recordListID"
        :type="recordType"
        :list="recordList"
        @changeRecordType="changeRecordType"
        @change="selectRecord"
      ></record-selector>

      <div class="btn">
        <el-button v-if="mode === 'normal'" type="primary" @click="selectAll()" icon="iconfont icon-delay">
          历次评估
        </el-button>
        <el-button class="add-button" icon="iconfont icon-add" @click="addOrModifyRiskRecord()">新增</el-button>
      </div>
    </div>

    <!-- 表格部分 -->
    <el-table height="100%" border :data="patientScoreMain" class="table-class" stripe>
      <el-table-column
        v-if="mode === 'normal'"
        prop="source"
        label="来源"
        align="center"
        width="110"
        fixed="left"
      ></el-table-column>
      <el-table-column prop="assessDate" label="评估日期" align="center" sortable min-width="110"></el-table-column>
      <el-table-column prop="assessTime" label="评估时间" align="center" min-width="90"></el-table-column>
      <el-table-column
        prop="recordListName"
        label="名称"
        min-width="280"
        align="left"
        header-align="center"
        :filters="filters"
        :filter-method="filterTag"
        filter-placement="bottom-end"
      ></el-table-column>
      <el-table-column label="分数" align="center" min-width="60">
        <template slot-scope="scope">
          <span v-if="mode === 'normal' && scope.row.showPointFlag">
            {{ scope.row.scoreContent ? "" : scope.row.scorePoint }}
          </span>
          <span v-if="mode === 'supplement' && !scope.row.scoreContent">
            {{ scope.row.scorePoint }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="rangeName" label="级距" min-width="110" align="left"></el-table-column>
      <!-- 正常模式下显示到期日期 -->
      <el-table-column v-if="mode === 'normal'" prop="dueDay" label="到期日期" min-width="110" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.dueDay, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" header-align="center" width="120" fixed="right">
        <template slot-scope="scope">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="addOrModifyRiskRecord(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="修改预防措施" v-if="scope.row.nursingClusterFlag">
            <i class="iconfont icon-preventiveMeasure" @click="showRiskInterventions(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteRiskAssessment(scope.row)"></i>
          </el-tooltip>
          <!-- 正常风险评估页面可以打印告知书 -->
          <el-tooltip content="打印告知书" v-if="mode === 'normal' && scope.row.emrDocumentID">
            <i class="iconfont icon-pdf" @click="getNoticePDF(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 风险告知单弹窗 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      custom-class="no-footer"
      :visible.sync="notificationVisible"
      title="风险告知单"
      fullscreen
    >
      <document-sign :iframeType="'application/x-google-chrome-pdf'" :signParams="notificationParams"></document-sign>
    </el-dialog>
    <!-- 风险编辑弹窗 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      fullscreen
      :title="dialogTitle"
      :visible.sync="updateVisible"
      custom-class="no-footer"
      v-if="updateVisible"
    >
      <risk-component :params="componentParams" @result="result"></risk-component>
    </el-dialog>
    <!-- 风险措施补录弹窗 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="true"
      title="风险预防措施"
      :visible.sync="showInterventions"
      v-if="showInterventions"
      custom-class="no-footer"
    >
      <el-table
        class="nursing-intervention-table"
        ref="intervention"
        :data="interventionList"
        height="calc(100% - 40px);"
        highlight-current-row
        border
        stripe
      >
        <el-table-column type="selection" width="40" align="center" class-name="select"></el-table-column>
        <el-table-column
          prop="interventionName"
          min-width="170"
          label="护理措施"
          header-align="center"
        ></el-table-column>
      </el-table>
      <div class="footer">
        <el-button @click="closeInterventions()">取消</el-button>
        <el-button type="primary" @click="saveInterventions">确 定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
// 导入依赖
import { GetRecordCascaderData } from "@/api/RecordsList";
import riskComponent from "./components/RiskComponent";
import baseLayout from "@/components/BaseLayout";
import {
  GetPatientScoreMainListByInpatient,
  DeletePatientScoreMainAndDetailByID,
  GetInterventionsByScoreMainID,
  SaveScoreInterventions,
} from "@/api/PatientScore";
import { mapGetters } from "vuex";
import documentSign from "@/components/DocumentSign";
import recordSelector from "./components/recordSelector.vue";

export default {
  components: {
    baseLayout,
    riskComponent,
    documentSign,
    recordSelector,
  },
  props: {
    // 模式：normal(正常模式) 或 supplement(补录模式)
    mode: {
      type: String,
      default: "normal",
      validator: (value) => ["normal", "supplement"].includes(value),
    },
    // 患者信息 - 补录模式下通过props传入
    patient: {
      type: Object,
      default: () => undefined,
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
    }),
    // 根据模式获取患者信息
    currentPatient() {
      return this.mode === "supplement" ? this.patient : this.patientInfo;
    },
    // 平铺出来的风险表recordsList
    flattenedRecordsList() {
      return this.recordList.filter((record) => record.children).flatMap((record) => record.children);
    },
  },
  data() {
    let that = this;
    return {
      //初始化为当前日期
      queryCriteria: {
        startTime: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
        endTime: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
      },
      //时间的禁用
      startPickerOption: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.queryCriteria.endTime).getTime();
        },
      },
      endPickerOption: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.queryCriteria.startTime).getTime() - 8.64e7;
        },
      },
      //当前表单ID
      recordListID: undefined,
      //修改/新增窗口控制
      updateVisible: false,
      //病人所有表集合
      patientScoreMain: [],
      //评估表列表
      recordList: [],
      //弹出框表头
      dialogTitle: "",
      notificationVisible: false,
      //过滤集合
      filters: [],
      // 评量表类别：0全部、1风险、2非风险
      recordType: 1,
      riskAssessDateTime: undefined,
      clickRow: {},
      record: undefined,
      componentParams: undefined,
      //获取告知书的参数
      notificationParams: {
        inpatientID: undefined,
        emrDocumentID: undefined,
        signedDocFlag: true,
      },
      // 预防措施相关
      interventionList: [],
      showInterventions: false,
      selectedScoreMain: undefined,
    };
  },
  created() {
    this.getRecordsList();
  },
  watch: {
    "currentPatient.inpatientID": {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        this.recordListID = undefined;
        // 补录模式下的特殊日期初始化逻辑
        if (this.mode === "supplement") {
          this.queryCriteria.startTime = this.currentPatient.admissionDate
            ? this.currentPatient.admissionDate
            : this._datetimeUtil.getNowDate();
          this.queryCriteria.endTime = this.currentPatient.dischargeDate
            ? this.currentPatient.dischargeDate
            : undefined;
        }

        this.getScoreMainList();
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 获取评估表列表数据
     * return {*}
     */
    getRecordsList() {
      GetRecordCascaderData().then((result) => {
        if (this._common.isSuccess(result)) {
          this.recordList = result.data;
        }
      });
    },

    /**
     * description: 切换评估表类型
     * param {*} recordType 评估表类型
     * return {*}
     */
    changeRecordType(recordType) {
      this.recordType = recordType;
      this.recordListID = undefined;
      this.getScoreMainList();
    },

    /**
     * description: 选择评估表记录
     * param {*} record 选中的评估表记录
     * return {*}
     */
    selectRecord(record) {
      this.record = record;
      this.recordListID = record?.recordListID ?? undefined;
      this.getScoreMainList();
    },
    /**
     * description: 获取患者评分主表列表
     * param {*} flag 是否获取全部数据
     * return {*}
     */
    getScoreMainList(flag) {
      let params = {
        index: Math.random(),
        InpatientID: this.currentPatient.inpatientID,
        stationID: this.currentPatient.stationID,
      };
      if (this.recordType == 1) {
        params.onlyRisk = true;
      } else if (this.recordType == 2) {
        params.onlyRisk = false;
      }
      if (!flag) {
        params.RecordListID = this.recordListID;
        params.startDate = this.queryCriteria.startTime;
        params.endDate = this.queryCriteria.endTime;
      }
      GetPatientScoreMainListByInpatient(params).then((response) => {
        if (response == null) {
          return;
        }
        this.filters = [];
        let recordListIDs = [];
        for (var i = 0; i < response.data.length; i++) {
          response.data[i].assessDate = this._datetimeUtil.formatDate(response.data[i].assessDate, "yyyy-MM-dd");
          response.data[i].assessTime = this._datetimeUtil.formatDate(response.data[i].assessTime, "hh:mm");

          if (recordListIDs.indexOf(response.data[i].recordListID) < 0) {
            recordListIDs.push(response.data[i].recordListID);
            this.filters.push({
              text: response.data[i].recordListName,
              value: response.data[i].recordListID,
            });
          }
        }
        this.patientScoreMain = response.data;
      });
    },
    /**
     * description: 表格筛选方法
     * param {*} value 筛选值
     * param {*} row 行数据
     * return {*}
     */
    filterTag(value, row) {
      return row.recordListID == value;
    },

    /**
     * description: 获取告知书PDF
     * param {*} row 行数据
     * return {*}
     */
    getNoticePDF(row) {
      this.notificationParams = {
        scorePoint: row.scorePoint,
        emrDocumentID: row.emrDocumentID,
        inpatientID: this.currentPatient.inpatientID,
        signedDocFlag: true,
      };
      this.notificationVisible = true;
    },

    /**
     * description: 删除风险评估记录
     * param {*} row 行数据
     * return {*}
     */
    async deleteRiskAssessment(row) {
      let checkResult = await this._common.checkActionAuthorization(this.user, row.addEmployeeID);
      if (!checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }

      this._deleteConfirm("确定删除此评量表吗?", (flag) => {
        if (flag) {
          let params = {
            scoreMainID: row.patientScoreMainID,
            supplementFlag: this.mode === "supplement",
          };
          DeletePatientScoreMainAndDetailByID(params).then((response) => {
            if (this._common.isSuccess(response)) {
              this.getScoreMainList();
              this._showTip("success", "删除成功！");
            }
          });
        }
      });
    },

    /**
     * description: 组件关闭回调函数
     * param {*} resultFlag 返回状态，true保存成功；false保存失败
     * param {*} resultData 保存成功后的返回数据
     * return {*}
     */
    result(resultFlag, resultData) {
      this.updateVisible = false;
      if (resultFlag) {
        //同组风险保存提示
        if (resultData && resultData.recordListID && resultData.recordName && this.model === "normal") {
          this.dealWithHaveSameTypeRisk(resultData);
        } else {
          this._showTip("success", "保存成功!");
          this.getScoreMainList();
        }
      }
    },
    /**
     * description: 同组风险保存提示处理
     * param {*} data 保存返回的数据
     * return {*}
     */
    dealWithHaveSameTypeRisk(data) {
      this._confirm("保存成功! 是否要继续评估 " + data.recordName, "同组风险评估", (flag) => {
        if (flag) {
          this.recordListID = data.recordListID;

          this.addOrModifyRiskRecord();
        } else {
          this.getScoreMainList();
        }
      });
    },
    /**
     * description: 新增或修改风险评估记录
     * param {*} row 行数据，为空时新增，有值时修改
     * return {*}
     */
    async addOrModifyRiskRecord(row) {
      let checkResult = true;
      this.clickRow = undefined;

      if (row) {
        checkResult = await this._common.checkActionAuthorization(this.user, row.addEmployeeID);
        if (!checkResult && this.mode === "supplement") {
          this._showTip("warning", "非本人不可操作");
          return;
        }
        this.clickRow = row;

        for (const record of this.recordList) {
          if (record.children) {
            this.record = record.children.find((child) => child.recordListID == row.recordListID);
            if (this.record) {
              break;
            }
          }
        }

        //初始化风险时间
        this.riskAssessDateTime =
          this._datetimeUtil.formatDate(this.clickRow.assessDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.clickRow.assessTime, "hh:mm");
      } else {
        if (!this.recordListID) {
          this._showTip("warning", "请选择评估表！");
          return;
        }
        //初始化评估时间
        this.riskAssessDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      }

      this.dialogTitle = this.getDialogTitle(this.currentPatient, this.record.recordName);
      this.updateVisible = true;
      // 根据模式设置不同的参数
      this.componentParams = {
        patientInfo: this.currentPatient,
        showPoint: this.record.showPointFlag,
        showTime: true,
        showStyle: this.record.showStyle,
        showBar: this.record.recordType == "Risk",
        recordListID: this.record.recordListID,
        recordList: this.flattenedRecordsList,
        recordsCode: this.record.recordsCode,
        patientScoreMainID: this.clickRow?.patientScoreMainID,
        assessTime: this.riskAssessDateTime,
        sourceType: "RiskSupplement",
      };
      if (this.mode === "normal") {
        this.componentParams.readOnly = !checkResult;
        this.componentParams.timeCheckFlag = false;
      }
      // 补录模式特有参数
      if (this.mode === "supplement") {
        this.componentParams.supplement = true;
        this.componentParams.assessStationID = row?.stationID ?? undefined;
        this.componentParams.assessDepartmentListID = row?.departmentListID ?? undefined;
        this.componentParams.assessBedNumber = row?.bedNumber ?? undefined;
        this.componentParams.assessBedID = row?.bedID ?? undefined;
        this.componentParams.sourceID = "";
      }
    },

    /**
     * description: 历次评估功能
     * return {*}
     */
    selectAll() {
      this.recordType = 0;
      this.recordListID = undefined;
      this.queryCriteria.startTime = "";
      this.queryCriteria.endTime = "";
      this.getScoreMainList(true);
    },

    /**
     * description: 显示风险预防措施对话框
     * param {*} row 行数据
     * return {*}
     */
    showRiskInterventions(row) {
      this.selectedScoreMain = row;
      this.showInterventions = true;
      let params = {
        scoreMainID: row.patientScoreMainID,
        recordListID: row.recordListID,
      };
      GetInterventionsByScoreMainID(params).then((response) => {
        if (this._common.isSuccess(response) && response.data) {
          this.interventionList = response.data;
          this.interventionList.forEach((element) => {
            if (element.selected) {
              this.$nextTick(() => {
                this.$refs.intervention.toggleRowSelection(element);
              });
            }
          });
        } else {
          this._showTip("warning", "获取风险预防措施失败");
        }
      });
    },
    /**
     * description: 关闭预防措施对话框
     * return {*}
     */
    closeInterventions() {
      this.showInterventions = false;
    },

    /**
     * description: 保存预防措施
     * return {*}
     */
    saveInterventions() {
      let param = {
        inpatientID: this.currentPatient.inpatientID,
        patientScoreMainID: this.selectedScoreMain.patientScoreMainID,
        recordListID: this.selectedScoreMain.recordListID,
        scoreInterventions: this.$refs.intervention.selection,
      };
      SaveScoreInterventions(param).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this._showTip("success", "保存成功！");
        } else {
          this._showTip("warning", "保存失败");
        }
        this.closeInterventions();
      });
    },
    /**
     * description: 获取对话框标题
     * param {*} patientInfo 患者信息
     * param {*} detail 详细信息
     * return {*}
     */
    getDialogTitle(patientInfo, detail) {
      let str = `${patientInfo.bedNumber}-${patientInfo.patientName}【${patientInfo.gender}-${patientInfo.age}】`;
      str += detail ? `-- ${detail}` : "";
      return str;
    },
  },
};
</script>

<style lang="scss">
.record-risk-rating {
  .btn {
    float: right;
  }
  // 预防措施对话框样式
  .nursing-intervention-table {
    flex: auto;
    height: calc(100% - 40px);
    .select {
      padding: 3px;
    }
  }
  .footer {
    text-align: right;
    width: 100%;
    padding-right: 10px;
    padding-top: 10px;
    box-sizing: border-box;
  }
}
</style>
