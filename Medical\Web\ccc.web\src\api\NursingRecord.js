/*
 * FilePath     : \ccc.web\src\api\NursingRecord.js
 * Author       : 李正元
 * Date         : 2020-05-18 22:05
 * LastEditors  : LX
 * LastEditTime : 2021-11-03 11:28
 * Description  :
 */

import http from "../utils/ajax";
const baseUrl = "/NursingRecord";

//生成API
export const urls = {
  //根据id查询护理记录
  GetNursingRecordInfosById: baseUrl + "/GetNursingRecordInfosById",
  //保存护理记录
  SaveNursingRecord: baseUrl + "/SaveNursingRecord",
  //删除护理记录
  DeleteNursingRecord: baseUrl + "/DeleteNursingRecord",
  //更新护理记录
  UpdateNursingRecord: baseUrl + "/UpdateNursingRecord",
  //复原护理记录
  ResetNursingRecordData: baseUrl + "/ResetNursingRecordData",
  //取得样式
  GetFormat: baseUrl + "/GetFormat",
  //取得病人数据
  GetPatientData: baseUrl + "/GetPatientData",
  //根据复测tpr记录ID获取对应的护理记录
  GetRetestNursingRecord: baseUrl + "/GetRetestNursingRecord",
  // 统计患者时间段内的生命体征
  GetTPRChartData: baseUrl + "/GetTPRChartData",
  ChangeBringToNR: baseUrl + "/ChangeBringToNR",
  //获取新增护理记录默认带入护理记录单开关是否打开
  GetAddNursingRecordFlagSwitch: baseUrl + "/GetAddNursingRecordFlagSwitch"
};

// 保存补录记录
export const SaveNursingRecord = params => {
  return http.post(urls.SaveNursingRecord, params);
};
// 删除补录记录
export const DeleteNursingRecord = params => {
  return http.get(urls.DeleteNursingRecord, params);
};
// 修改补录记录
export const UpdateNursingRecord = params => {
  return http.post(urls.UpdateNursingRecord, params);
};
// 根据id获取相应的数据
export const GetNursingRecordInfosById = params => {
  return http.get(urls.GetNursingRecordInfosById, params);
};
// 重置补录记录
export const ResetNursingRecordData = params => {
  return http.get(urls.ResetNursingRecordData, params);
};
// 取得样式
export const GetFormat = params => {
  return http.get(urls.GetFormat, params);
};
// 取得病人数据
export const GetPatientData = params => {
  return http.get(urls.GetPatientData, params);
};
// 取得复测数据
export const GetRetestNursingRecord = params => {
  return http.get(urls.GetRetestNursingRecord, params);
};
// 统计患者时间段内的生命体征
export const GetTPRChartData = params => {
  return http.get(urls.GetTPRChartData, params);
};
// 修改带入护理记录单标记
export const ChangeBringToNR = params => {
  return http.get(urls.ChangeBringToNR, params);
};
//获取新增护理记录默认带入护理记录单开关是否打开
export const GetAddNursingRecordFlagSwitch = params => {
  return http.get(urls.GetAddNursingRecordFlagSwitch, params);
};
