/*
 * FilePath     : \src\api\diagnosis.js
 * Author       : 石高阳
 * Date         : 2020-10-27 15:38
 * LastEditors  : 石高阳
 * LastEditTime : 2020-10-27 17:15
 * Description  : 诊断刷新（目前只银川使用）
 */
import http from "../utils/ajax";
const baseUrl = "/diagnosis";

export const urls = {
  UpdatePatientDiagonsisByCaseNumber:
    baseUrl + "/UpdatePatientDiagonsisByCaseNumber"
};


//更新病人诊断
export const UpdatePatientDiagonsisByCaseNumber = params => {
  return http.get(urls.UpdatePatientDiagonsisByCaseNumber,params);
};
