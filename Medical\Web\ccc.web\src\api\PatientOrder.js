import http from '../utils/ajax'
const baseUrl = '/order'

export const urls = {
  GetPatientOrderDetail: baseUrl + '/GetPatientOrderDetail',
  CheckOrder: baseUrl + '/CheckOrder',
  GetOrdersByInpatientIDAsync: baseUrl + '/GetOrdersByInpatientIDAsync',
  GetLongTermOrdersByInpatientIDAsync: baseUrl + "/GetLongTermOrdersByInpatientIDAsync"
}
// 获取病人医嘱 
export const GetPatientOrderDetail = (params) => {
  return http.get(urls.GetPatientOrderDetail, params)
}
export const CheckOrder = (params) => {
  return http.post(urls.CheckOrder, params);
}
export const GetOrdersByInpatientIDAsync = (params) => {
  return http.post(urls.GetOrdersByInpatientIDAsync, params);

}
export const GetLongTermOrdersByInpatientIDAsync = (params) => {
  return http.get(urls.GetLongTermOrdersByInpatientIDAsync, params)
}
