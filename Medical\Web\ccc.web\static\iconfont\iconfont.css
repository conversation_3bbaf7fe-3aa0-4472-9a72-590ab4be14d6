@font-face {
  font-family: "iconfont"; /* Project id 1379540 */
  src: url('iconfont.woff2?t=1746691822891') format('woff2'),
       url('iconfont.woff?t=1746691822891') format('woff'),
       url('iconfont.ttf?t=1746691822891') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-blood-recycle:before {
  content: "\e6a7";
}

.icon-blood-verification:before {
  content: "\e691";
}

.icon-increase-font-size:before {
  content: "\e68c";
}

.icon-reduce-font-size:before {
  content: "\e68d";
}

.icon-ai-widget:before {
  content: "\e6a6";
}

.icon-fullscreen:before {
  content: "\e642";
}

.icon-exit-fullscreen:before {
  content: "\e773";
}

.icon-list-v31:before {
  content: "\e69f";
}

.icon-biaoge:before {
  content: "\e6c6";
}

.icon-biaoge1:before {
  content: "\e640";
}

.icon-jurassic_edit-table:before {
  content: "\e6f3";
}

.icon-list-v3:before {
  content: "\ea39";
}

.icon-initiate-a-signature:before {
  content: "\e69e";
}

.icon-get-signature:before {
  content: "\e695";
}

.icon-test-sample-transfer:before {
  content: "\e697";
}

.icon-preventiveMeasure:before {
  content: "\e807";
}

.icon-third-level-visits-guidance-content:before {
  content: "\e931";
}

.icon-head-nurse-guidance-content:before {
  content: "\e67e";
}

.icon-patient-situation:before {
  content: "\e625";
}

.icon-rentitu:before {
  content: "\e68e";
}

.icon-nursing-process-records:before {
  content: "\e629";
}

.icon-daiban2:before {
  content: "\e63f";
}

.icon-leftfont-15:before {
  content: "\e6d3";
}

.icon-daiban4:before {
  content: "\e67f";
}

.icon-qiyong:before {
  content: "\e675";
}

.icon-shaixuan-copy:before {
  content: "\e930";
}

.icon-wenjianjiancetongji:before {
  content: "\e652";
}

.icon-xieqifenxi:before {
  content: "\e622";
}

.icon-a-daoguantubiaozhonggouzhuanhuan:before {
  content: "\e67d";
}

.icon-a-shuidizhuanhuan:before {
  content: "\e67a";
}

.icon-a-weibiaoti-1zhuanhuan:before {
  content: "\e67c";
}

.icon-signature:before {
  content: "\e67b";
}

.icon-img-preview:before {
  content: "\e840";
}

.icon-b:before {
  content: "\e682";
}

.icon-a:before {
  content: "\e765";
}

.icon-retest:before {
  content: "\e8a1";
}

.icon-shujuzidian-02:before {
  content: "\e60f";
}

.icon-shujuliuzhuan-01:before {
  content: "\e617";
}

.icon-huanjingjiance:before {
  content: "\e63e";
}

.icon-chakanwodedingzhijilu:before {
  content: "\e609";
}

.icon-chakan:before {
  content: "\e6d2";
}

.icon-chakan1:before {
  content: "\e63d";
}

.icon-jiance-11:before {
  content: "\e60e";
}

.icon-jump-page:before {
  content: "\e606";
}

.icon-message-notification:before {
  content: "\e63b";
}

.icon-record-evaluate:before {
  content: "\e628";
}

.icon-evaluation:before {
  content: "\e604";
}

.icon-fee-select:before {
  content: "\e679";
}

.icon-archivedFile:before {
  content: "\e673";
}

.icon-solutionFile:before {
  content: "\e674";
}

.icon-observation:before {
  content: "\e678";
}

.icon-dragin:before {
  content: "\e676";
}

.icon-observeMonitor:before {
  content: "\e670";
}

.icon-clinic1:before {
  content: "\e66f";
}

.icon-sort:before {
  content: "\e60d";
}

.icon-clinic:before {
  content: "\e92f";
}

.icon-temperature:before {
  content: "\e61c";
}

.icon-event:before {
  content: "\e66d";
}

.icon-preview:before {
  content: "\e66e";
}

.icon-nursing-problem:before {
  content: "\e66c";
}

.icon-time-line:before {
  content: "\e69a";
}

.icon-advice:before {
  content: "\e66a";
}

.icon-line-chart:before {
  content: "\e66b";
}

.icon-login-user:before {
  content: "\e646";
}

.icon-login-password:before {
  content: "\e672";
}

.icon-male-patients:before {
  content: "\e666";
}

.icon-female-patients:before {
  content: "\e669";
}

.icon-nursing-record:before {
  content: "\e664";
}

.icon-perform:before {
  content: "\e663";
}

.icon-nursing-assement:before {
  content: "\e661";
}

.icon-order-check:before {
  content: "\e662";
}

.icon-create:before {
  content: "\e660";
}

.icon-bring-into:before {
  content: "\e65f";
}

.icon-second-attendance:before {
  content: "\e65d";
}

.icon-list-icon:before {
  content: "\e684";
}

.icon-big-icon:before {
  content: "\e63c";
}

.icon-small-icon:before {
  content: "\e6a4";
}

.icon-first-attendance:before {
  content: "\e65c";
}

.icon-nursing-problem_1:before {
  content: "\e659";
}

.icon-system-setting:before {
  content: "\e65a";
}

.icon-test-examine:before {
  content: "\e65b";
}

.icon-management:before {
  content: "\e657";
}

.icon-nursing-plan:before {
  content: "\e64f";
}

.icon-record-select:before {
  content: "\e650";
}

.icon-evaluate:before {
  content: "\e653";
}

.icon-attendance:before {
  content: "\e64e";
}

.icon-nurse-shift:before {
  content: "\e64d";
}

.icon-statistics:before {
  content: "\e64b";
}

.icon-discharge-work:before {
  content: "\e648";
}

.icon-special-care:before {
  content: "\e647";
}

.icon-lishijilu:before {
  content: "\e64c";
}

.icon-print:before {
  content: "\e60c";
}

.icon-view:before {
  content: "\e671";
}

.icon-time:before {
  content: "\e605";
}

.icon-save-button:before {
  content: "\e615";
}

.icon-paste:before {
  content: "\e668";
}

.icon-body:before {
  content: "\e644";
}

.icon-plan:before {
  content: "\e611";
}

.icon-upload:before {
  content: "\e64a";
}

.icon-left-arrow:before {
  content: "\e656";
}

.icon-temp-save:before {
  content: "\e643";
}

.icon-chart:before {
  content: "\e638";
}

.icon-search:before {
  content: "\e610";
}

.icon-add:before {
  content: "\e627";
}

.icon-back:before {
  content: "\e77d";
}

.icon-check:before {
  content: "\e62e";
}

.icon-minus:before {
  content: "\e724";
}

.icon-plus:before {
  content: "\e71f";
}

.icon-plan1:before {
  content: "\e54b";
}

.icon-checked:before {
  content: "\e632";
}

.icon-no-checked:before {
  content: "\e714";
}

.icon-radio-select:before {
  content: "\e62d";
}

.icon-radio-normal:before {
  content: "\e713";
}

.icon-switch:before {
  content: "\e614";
}

.icon-handon:before {
  content: "\e641";
}

.icon-summation:before {
  content: "\e81b";
}

.icon-info1:before {
  content: "\e602";
}

.icon-clear:before {
  content: "\e637";
}

.icon-save:before {
  content: "\e731";
}

.icon-copy:before {
  content: "\e8b0";
}

.icon-pdf:before {
  content: "\e60a";
}

.icon-revocation:before {
  content: "\e603";
}

.icon-applyFor:before {
  content: "\e665";
}

.icon-assign:before {
  content: "\e667";
}

.icon-handoff:before {
  content: "\e649";
}

.icon-pass:before {
  content: "\e619";
}

.icon-reject:before {
  content: "\e618";
}

.icon-pause:before {
  content: "\e616";
}

.icon-delay:before {
  content: "\e608";
}

.icon-caigoujihua:before {
  content: "\e602";
}

.icon-info:before {
  content: "\e600";
}

.icon-execute:before {
  content: "\e621";
}

.icon-check-mark:before {
  content: "\e60b";
}

.icon-zhankai:before {
  content: "\e636";
}

.icon-drawing:before {
  content: "\e634";
}

.icon-change:before {
  content: "\e635";
}

.icon-add-img:before {
  content: "\e63a";
}

.icon-del-img:before {
  content: "\e626";
}

.icon-stop:before {
  content: "\e62f";
}

.icon-curriculum:before {
  content: "\e61b";
}

.icon-trip:before {
  content: "\e688";
}

.icon-show-more:before {
  content: "\e620";
}

.icon-user:before {
  content: "\e607";
}

.icon-message:before {
  content: "\e639";
}

.icon-refresh:before {
  content: "\e65e";
}

.icon-home:before {
  content: "\e601";
}

.icon-exit:before {
  content: "\e61f";
}

.icon-human-resources:before {
  content: "\e677";
}

.icon-download-fill:before {
  content: "\e61a";
}

.icon-sign:before {
  content: "\e633";
}

.icon-cancel:before {
  content: "\e73a";
}

.icon-reply:before {
  content: "\e61d";
}

.icon-select-mark:before {
  content: "\e631";
}

.icon-del-select-mark:before {
  content: "\e630";
}

.icon-arrow-download:before {
  content: "\e7d2";
}

.icon-arrow-top:before {
  content: "\e61e";
}

.icon-arrow-down:before {
  content: "\e7d0";
}

.icon-arrow-up:before {
  content: "\e7d1";
}

.icon-add1:before {
  content: "\e6a3";
}

.icon-more:before {
  content: "\e62c";
}

.icon-del:before {
  content: "\e612";
}

.icon-edit:before {
  content: "\e613";
}

