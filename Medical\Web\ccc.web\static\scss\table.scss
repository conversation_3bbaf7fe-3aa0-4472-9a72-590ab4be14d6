.el-table.el-table--striped tr.el-table__row--striped.current-row td,r
.el-table .el-table__body tr.current-row td {
  background-color: #ffe0ac !important;

  // * {
  //   font-weight: 600;
  // }

  // .iconfont {
  //   font-weight: normal;
  // }
}

/* 解决冻结列时滚动条无法拖动问题 */
.el-table--scrollable-x {

  .el-table__fixed-right,
  .el-table__fixed {
    height: calc(100% - 17px) !important;
  }
}

.el-table.el-table--striped tr.el-table__row--striped:hover td,
.el-table .el-table__body tr:hover>td {
  background-color: #ffe0ac !important;
}

.el-table {
  border: 1px solid #cccccc;

  /* 防止表格错位 */
  th.gutter {
    display: table-cell !important;
  }

  colgroup.gutter {
    display: table-cell !important;
  }

  th,
  td {
    border-color: #cccccc !important;
  }

  th,
  th.is-leaf,
  .el-table__row td {
    padding: 3px;
    box-sizing: border-box;
  }

  th {
    color: #000000;

    &.is-leaf {
      background-color: #ebf7df !important;
      color: #000000;

      .cell {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        line-height: 24px;
        font-size: 16px;
      }
    }
  }

  td .cell {
    // position: sticky !important;
    // top: 10px;
    font-size: 14px;
  }
  .cell {
    padding: 1px 4px !important;
    box-sizing: border-box;

    .el-table__column-filter-trigger {
      line-height: 32px;
      margin-left: 3px;
    }

    .caret-wrapper {
      height: 32px;

      .sort-caret {
        &.ascending {
          top: 0;
        }

        &.descending {
          bottom: 0;
        }
      }
    }

    .el-date-editor {
      .el-input__prefix {
        display: none;
      }

      .el-input__inner {
        padding-left: 5px;
        padding-right: 2px;
      }
    }
  }

  /* 
  双层表头样式
  2020-1-5 李青原
  */
  thead.is-group th {
    background: #ebf7df !important;
  }

  /*
  嵌套表格样式
  */
  .el-table__expanded-cell {
    .el-table__header-wrapper {
      th {
        background-color: #f1f1f6;
      }
    }
  }
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f4f1f1;
}