<!--
 * FilePath     : \ccc.web\src\pages\nursingPlan\components\clusterCare.vue
 * Author       : 苏军志
 * Date         : 2020-05-08 11:56
 * LastEditors  : 陈超然
 * LastEditTime : 2024-09-03 10:21
 * Description  : 集束护理
 -->
 <template>
  <base-layout class="cluster-care" v-loading="loading" :element-loading-text="loadingText">
    <div slot="header" class="top">
      <el-radio-group @change="getData" v-model="typeIndex">
        <el-radio-button label="1">现有集束护理</el-radio-button>
        <el-radio-button label="2">历史集束护理</el-radio-button>
      </el-radio-group>

      <div class="btn">
        <el-button type="primary" icon="iconfont icon-save-button" v-if="typeIndex == '1'" @click="save">
          保存
        </el-button>
        <el-button
          type="primary"
          class="edit-button"
          icon="iconfont icon-add"
          v-if="typeIndex == '1'"
          @click="addProblem"
        >
          手工加入
        </el-button>
      </div>
    </div>
    <div class="cluster-wrap" v-show="typeIndex == '1'">
      <!-- 现有护理问题 -->
      <div class="left-wrap">
        <el-table
          class="nursing-problem-table"
          ref="problem"
          :data="problemList"
          highlight-current-row
          border
          stripe
          height="100%"
          @row-click="showIntervention"
        >
          <el-table-column label="护嘱" header-align="center">
            <template slot-scope="problem">
              <div :class="problem.row.status == '0' ? 'unconfirmed-cluster' : ''">
                {{ problem.row.nursingProblem }}
                <span v-if="problem.row.problemSourceInfo && problem.row.problemSourceInfo.content">
                  {{ "(" + problem.row.problemSourceInfo.content + ")" }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" width="140" header-align="center">
            <template slot-scope="problem">
              <el-date-picker
                v-model="problem.row.startTime"
                :clearable="false"
                type="datetime"
                placeholder="选择时间"
                style="width: 125px"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                @click.native.stop
                @change="problemTimeCheck(problem.row)"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="70px" align="center" class-name="button-col">
            <template slot-scope="problem">
              <el-tooltip content="停止集束护理">
                <i class="iconfont icon-stop" @click="stopProblem(problem.row.patientProblemId, problem.$index)"></i>
              </el-tooltip>
              <el-tooltip content="删除集束护理">
                <i class="iconfont icon-del" @click="deleteProblem(problem.row.patientProblemId, problem.$index)"></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 现有护理问题所对应的护理措施 -->
      <div class="right-wrap">
        <el-table
          class="nursing-intervention-table"
          ref="intervention"
          :data="interventionList"
          highlight-current-row
          border
          stripe
          height="100%"
          @select="selectionIntervention"
          @select-all="selectionAllIntervention"
        >
          <el-table-column type="selection" :width="convertPX(40)" align="center" class-name="select"></el-table-column>
          <el-table-column label="频次" width="150" header-align="center">
            <template slot-scope="intervention">
              <frequency-selector
                v-model="intervention.row.frequencyID"
                :frequencyList="frequencyList"
                @select-item="selectFrequency($event, intervention.row)"
              ></frequency-selector>
            </template>
          </el-table-column>
          <el-table-column
            prop="interventionName"
            min-width="170"
            label="护理措施"
            header-align="center"
          ></el-table-column>
          <el-table-column label="开始时间" width="140" header-align="center">
            <template slot-scope="intervention">
              <el-date-picker
                v-model="intervention.row.startDate"
                :clearable="false"
                type="datetime"
                placeholder=""
                style="width: 125px"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                @change="startTimeCheck(intervention.row)"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="结束时间" width="140" header-align="center">
            <template slot-scope="intervention">
              <el-date-picker
                v-model="intervention.row.endDate"
                :clearable="false"
                type="datetime"
                placeholder=""
                style="width: 125px"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                @change="endTimeCheck(intervention.row)"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="首次" width="50" align="center">
            <template slot-scope="intervention">
              <el-checkbox
                :disabled="getDisabledFlag(intervention.row)"
                v-model="intervention.row.firstDayFlag"
                true-label="*"
                false-label=""
                @change="updateList(intervention.row)"
              ></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="cluster-wrap history" v-show="typeIndex == '2'">
      <!-- 历史护理问题 -->
      <div class="left-wrap">
        <el-table
          :data="historyProblemList"
          highlight-current-row
          border
          stripe
          height="100%"
          @row-click="showHistoryIntervention"
        >
          <el-table-column label="护嘱" min-width="168px" header-align="center">
            <template slot-scope="problems">
              {{ problems.row.nursingProblem }}
            </template>
          </el-table-column>

          <el-table-column label="状态" min-width="70px" prop="operation" align="center" class-name="button-col">
            <template slot-scope="scope">
              <div v-if="scope.row.deleteFlag == '*' || (scope.row.deleteFlag == '*' && scope.row.endDate != '')">
                删除
              </div>
              <div v-else-if="scope.row.endDate != ''">停止</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 历史护理问题所对应的护理措施 -->
      <div class="right-wrap">
        <el-table ref="interventions" :data="historyInterventionList" highlight-current-row border stripe height="100%">
          <el-table-column
            prop="frequencyDescription"
            label="频次"
            width="160"
            header-align="center"
            align="left"
          ></el-table-column>
          <el-table-column
            prop="interventionName"
            min-width="200"
            label="护理措施"
            header-align="center"
          ></el-table-column>
          <el-table-column label="开始时间" width="170" header-align="center" align="center">
            <template slot-scope="interventions">
              <span
                v-formatTime="{
                  value: interventions.row.startDate,
                  type: 'data',
                }"
              ></span>
            </template>
          </el-table-column>
          <el-table-column label="删除/停止时间" width="170" header-align="center" align="center">
            <template slot-scope="interventions">
              <span
                v-formatTime="{
                  value: interventions.row.modifyDate,
                  type: 'data',
                }"
              ></span>
            </template>
          </el-table-column>

          <el-table-column
            prop="userName"
            label="执行护士"
            width="115"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 手工加入集束护理 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="showAdd"
      :title="patientInfo"
      custom-class="add-nursing-orders"
      v-loading="addLoading"
      :element-loading-text="addLoadingText"
    >
      <div class="pinyin-select">
        <label>简拼:</label>
        <el-input
          style="width: 130px"
          v-model="selectContent"
          @keyup.enter.native="queryProblem"
          class="select-input"
          placeholder="请输入内容"
        >
          <i slot="append" class="iconfont icon-search" @click="queryProblem"></i>
        </el-input>
      </div>
      <u-table
        v-if="showAdd"
        class="nursing-orders-table"
        ref="nursingOrdersTable"
        :height="450"
        :row-height="30"
        :data="nursingOrders"
        border
        stripe
        bigDataCheckbox
        use-virtual
        @select="selectionProblem"
      >
        <u-table-column type="selection" :width="convertPX(40)" align="center" />
        <u-table-column prop="problem" label="集束护理" />
      </u-table>
      <div slot="footer">
        <label>日期:</label>
        <el-date-picker
          class="date-width"
          type="date"
          v-model="clusterNursingDate"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
        <el-time-picker
          class="time-width"
          v-model="clusterNursingTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        ></el-time-picker>
        <el-button align="right" @click="showAdd = false">取消</el-button>
        <el-button align="right" type="primary" @click="saveAdd">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="deselectTitle"
      custom-class="reasons"
      :visible.sync="showDeselectFlag"
      :before-close="cancelDeselectReason"
    >
      <span class="comment-label">原因：</span>
      <el-input
        class="comment"
        v-model="deselectReason"
        type="textarea"
        :autosize="{ minRows: 4, maxRows: 4 }"
        placeholder="请输入内容"
        resize="none"
      />
      <div slot="footer">
        <el-button @click="cancelDeselectReason">取消</el-button>
        <el-button type="primary" @click="saveDeselectReason">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import { GetLastMain } from "@/api/Assess";
import { GetTypeFrequency } from "@/api/Frequency";
import {
  GetCanSelectNursingOrder,
  GetHistoryIntervention,
  GetHistoryPaitentProblem,
  GetIntervention,
  GetNeedFirstFrequency,
  GetPatientNursingClusterView,
  SaveCluster,
} from "@/api/NursingPlan";
import {
  DelePatientProblem,
  SaveClusterProblem,
  SaveDeselectReason,
  StopPatientProblemByID,
} from "@/api/PatientProblem";
import { GetClusterOrderByPinyin } from "@/api/Problem";
import { GetScheduleTop } from "@/api/Setting";
import { GetShifDayStartAndEndDateTime } from "@/api/StationShift";
import baseLayout from "@/components/BaseLayout";
import frequencySelector from "@/components/selector/frequencySelector";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    frequencySelector,
  },
  props: {
    inpatientinfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    admissionDateTime: {
      type: String,
      default: undefined,
    },
    lastAssessDateTime: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      loading: false,
      loadingText: "加载中……",
      problemList: [],
      historyProblemList: [],
      interventionList: [],
      historyInterventionList: [],
      frequencyList: undefined,
      currentPatientProblemID: undefined,
      selectInterventions: [],
      needFirstFrequencyList: [],
      showAdd: false,
      addLoading: false,
      addLoadingText: "加载中……",
      nursingOrders: [],
      patientInfo: "",
      selectProblems: [],
      typeIndex: "1",
      assessMainID: "",
      //简拼查询
      selectContent: "",
      clusterNursingDate: undefined,
      clusterNursingTime: undefined,
      selectedProblem: undefined,
      shiftStartDate: undefined,
      shiftEndDate: undefined,
      //显示弹窗
      showDeselectFlag: false,
      //取消勾选原因文本
      deselectReason: undefined,
      //取消勾选措施集合
      deselectIntervention: [],
      //取消勾选是否填写原因
      showDeselectReasonFlag: false,
      //措施取消或频次调整弹框标题
      deselectTitle: undefined,
      //需要填写原因的频次ID
      needEnterReasonFrequencylDs: [],
      //选择前原有频次
      oldFrequency: undefined,
      //原始措施集合
      originalInterventionList: [],
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  watch: {
    "inpatientinfo.inpatientID": {
      async handler(newvalue) {
        this.problemList = [];
        this.historyProblemList = [];

        this.interventionList = [];
        this.historyInterventionList = [];

        if (!newvalue) return;
        this.patientInfo =
          this.inpatientinfo.patientName +
          "【" +
          this.inpatientinfo.gender +
          "-" +
          this.inpatientinfo.ageDetail +
          "】手工加入集束护理";
        this.init();
      },
      immediate: true,
    },
  },
  beforeMount() {
    //获取当天班别开始和结束日期
    this.getShifDayStartAndEndDateTime();
  },
  methods: {
    // 初始化
    async init() {
      this.loading = true;
      this.loadingText = "加载中……";
      // 获取频次列表
      await this.GetTypeFrequency();
      await this.getPaitentProblem();
      // 获取是否首次评估
      this.needFirstFrequencyList = [];
      let params = {
        inpatientID: this.inpatientinfo.inpatientID,
      };
      await GetNeedFirstFrequency(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.needFirstFrequencyList = result.data;
        }
      });
      let param = {
        SettingTypeCode: "NeedEnterReasonFrequencylDs",
      };
      await GetScheduleTop(param).then((result) => {
        if (this._common.isSuccess(result)) {
          this.needEnterReasonFrequencylDs = ["31", "131"];
        }
      });
    },
    // 获取频次列表
    async GetTypeFrequency() {
      this.frequencyList = [];
      let params = {
        visibleFlag: true,
      };
      await GetTypeFrequency(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.frequencyList = result.data;
        }
      });
    },
    // 获取问题列表
    async getPaitentProblem() {
      this.problemList = [];
      this.interventionList = [];
      let params = {
        inpatientID: this.inpatientinfo.inpatientID,
        stationID: this.inpatientinfo.stationID,
        nursingOrder: true,
      };
      // 获取问题列表
      await GetPatientNursingClusterView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.problemList = result.data;
          if (this.problemList && this.problemList.length > 0) {
            this.showIntervention(this.problemList[0]);
            this.selectedProblem = this.problemList[0];
            this.$nextTick(() => {
              // 默认选中
              if (this.$refs.problem) {
                this.$refs.problem.setCurrentRow(this.problemList[0]);
              }
            });
          }
        } else {
          this.loading = false;
        }
      });
    },
    // 获取历史问题列表
    async getHistoryPaitentProblem() {
      this.historyProblemList = [];
      this.historyInterventionList = [];
      let params = {
        inpatientID: this.inpatientinfo.inpatientID,
        stationID: this.inpatientinfo.stationID,
      };
      // 获取历史问题列表
      await GetHistoryPaitentProblem(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.historyProblemList = result.data;
          this.$nextTick(() => {
            if (this.historyProblemList.length) {
              this.showHistoryIntervention(this.historyProblemList[0]);
            }
          });
        } else {
          this.loading = false;
        }
      });
    },
    async showIntervention(row) {
      this.deselectIntervention = [];
      this.currentPatientProblemID = row.patientProblemId;
      let temp = this.selectInterventions.find(
        (intervention) => intervention.patientProblemID == this.currentPatientProblemID
      );
      // 解决初始化和从历史集束护理页签切换过来时 数据显示异常
      temp && row.clickTimes == undefined && (row.clickTimes = 1);
      if (row.clickTimes == undefined) {
        row.clickTimes = 1;
      } else {
        row.clickTimes = row.clickTimes + 1;
      }
      // 获取护理问题对应的护理措施
      let params = {
        problemID: row.problemId,
        patientProblemID: row.patientProblemId,
        index: Math.random(),
      };
      //解决连续切换问题 --GPC
      this.interventionList = [];
      this.loading = true;
      this.loadingText = "加载中……";
      await GetIntervention(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.interventionList = result.data.patientInterventions;
          this.originalInterventionList = this._common.clone(this.interventionList);
          for (let i = 0; i < this.interventionList.length; i++) {
            // 防止有重复措施，添加序号区分
            this.interventionList[i].index = i;
            this.$set(this.interventionList[i], "startTimeStartLimit", row.startTime);
            this.interventionList[i].originalStartTime = this.interventionList[i].startDate;
            this.interventionList[i].originalEndTime = this.interventionList[i].endDate;
          }
          this.$nextTick(() => {
            // 默认选中
            if (this.$refs.intervention) {
              this.$refs.intervention.setCurrentRow(this.interventionList[0]);
            }
          });
          this.showDeselectReasonFlag = result.data.interventionDeselectFlag;
        }
      });
      // 设置已经选择的值 row.clickTimes > 1 第二次点击，取已选的数据回显
      if (row.clickTimes > 1) {
        let selectList = undefined;
        if (this.selectInterventions.length > 0) {
          let temp = this.selectInterventions.find(
            (intervention) => intervention.patientProblemID == this.currentPatientProblemID
          );
          temp && (selectList = temp.interventions);
        }
        if (selectList && selectList.length > 0) {
          // 回显本次选择的项目
          this.setSelectFlag(this.currentPatientProblemID, true);
          for (let i = 0; i < this.interventionList.length; i++) {
            let selectIntervention = selectList.find((intervention) => {
              return (
                this.interventionList[i].interventionID == intervention.interventionID &&
                this.interventionList[i].index == intervention.index &&
                intervention.selected
              );
            });
            if (selectIntervention) {
              this.interventionList[i].selected = true;
              this.interventionList[i].frequencyID = selectIntervention.frequencyID;
              this.interventionList[i].frequency = selectIntervention.frequency;
              this.interventionList[i].frequencyDescription = selectIntervention.frequencyDescription;
              this.interventionList[i].firstDayFlag = selectIntervention.firstDayFlag;
              this.interventionList[i].startDate = selectIntervention.startDate;
              this.interventionList[i].endDate = selectIntervention.endDate;
              if (this.$refs.intervention) {
                this.$nextTick(() => {
                  this.$refs.intervention.toggleRowSelection(this.interventionList[i]);
                });
              }
            } else {
              this.$set(this.interventionList[i], "selected", false);
            }
          }
        }
      } else {
        // row.clickTimes == 1 第一次点击，取上次保存的数据回显
        let oldList = [];
        for (let i = 0; i < this.interventionList.length; i++) {
          if (this.interventionList[i].selected || this.interventionList[i].patientInterventionID) {
            this.interventionList[i].selected = true;
            if (this.$refs.intervention) {
              this.$nextTick(() => {
                this.$refs.intervention.toggleRowSelection(this.interventionList[i]);
              });
            }
            oldList.push(this.interventionList[i]);
          }
        }
        if (oldList.length > 0) {
          let temp = {
            patientProblemID: this.currentPatientProblemID,
            interventions: oldList,
          };
          this.selectInterventions.push(temp);
          this.setSelectFlag(this.currentPatientProblemID, true);
        } else {
          this.setSelectFlag(this.currentPatientProblemID, false);
        }
      }
    },
    async showHistoryIntervention(row) {
      // if (row.clickTimes == undefined) {
      //   row.clickTimes = 1;
      // } else {
      //   row.clickTimes = row.clickTimes + 1;
      // }

      // 获取护理问题对应的护理措施
      this.historyInterventionList = [];
      let params = {
        problemID: row.problemId,

        patientProblemID: row.patientProblemId,
      };
      //解决连续切换问题 --GPC
      this.loading = true;
      this.loadingText = "加载中……";
      await GetHistoryIntervention(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.historyInterventionList = result.data;
        }
      });
    },
    //全选全不选
    selectionAllIntervention(interventions) {
      //全选
      if (interventions.length) {
        interventions.forEach((item) => {
          item.selected = false;
          this.selectionIntervention(interventions, item);
        });
      } else {
        //全部取消
        this.interventionList.forEach((item) => {
          item.selected = false;
          this.setSelectFlag(this.currentPatientProblemID, false);
          this.updateList(item);
          this.showDeselectReason(item, true, false);
        });
      }
    },
    // 显示护理问题对应的措施列表
    selectionIntervention(interventions, row) {
      this.deselectTitle = "措施取消勾选原因";
      if (row.selected) {
        this.showDeselectReason(row, true, true);
      }
      //TPRFlag提示检测
      this.TPRFlagCheck(row.tprFlag);
      row.selected = !row.selected;
      // 设置是否首次
      this.setFirst(row);
      let isExist = false;
      for (let i = 0; i < this.selectInterventions.length; i++) {
        if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
          let flag = false;
          for (let j = this.selectInterventions[i].interventions.length - 1; j >= 0; j--) {
            if (
              this.selectInterventions[i].interventions[j].interventionID == row.interventionID &&
              this.selectInterventions[i].interventions[j].index == row.index
            ) {
              flag = true;
              if (!row.selected) {
                if (row.patientInterventionID) {
                  this.selectInterventions[i].interventions[j].selected = false;
                } else {
                  // 移除
                  this.selectInterventions[i].interventions.splice(j, 1);
                }
              } else {
                this.selectInterventions[i].interventions[j].selected = true;
              }
              break;
            }
          }
          if (!flag && row.selected) {
            row.startDate = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
            this.selectInterventions[i].interventions.push(row);
          }
          if (interventions.length == 0) {
            this.setSelectFlag(this.currentPatientProblemID, false);
          } else {
            this.setSelectFlag(this.currentPatientProblemID, true);
          }
          isExist = true;
          break;
        }
      }
      if (!isExist && interventions.length > 0) {
        let temp = {
          patientProblemID: this.currentPatientProblemID,
          interventions: interventions,
        };
        temp.interventions.forEach((item) => {
          item.startDate = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
        });
        this.selectInterventions.push(temp);
        this.setSelectFlag(this.currentPatientProblemID, true);
      }
    },
    selectFrequency(event, row) {
      this.oldFrequency = row.frequency; //调整前的频次，用于取消填写时还原原来选中的频次
      row.frequency = event.search;
      let originalIntervention = this.originalInterventionList[row.index];
      //风险类集束 默认推荐措施 原有频次不是prn改为prn时 弹窗填写原因
      if (
        this.showDeselectReasonFlag &&
        row.selected &&
        row.patientInterventionID && //默认推荐措施
        this.needEnterReasonFrequencylDs.length &&
        !this.needEnterReasonFrequencylDs.includes(originalIntervention.frequencyID.toString()) && //初始频次
        this.needEnterReasonFrequencylDs.includes(row.frequencyID.toString()) && //当前频次
        !this.deselectIntervention.find((m) => m == row.patientInterventionID) //填写过一次后不重复填写
      ) {
        this.deselectTitle = "措施频次由非" + row.frequency + "调整为" + row.frequency + "原因";
        this.showDeselectReason(row, true, true);
      }

      // 设置是否首次
      this.setFirst(row);
      //TPRFlag提示检测
      this.TPRFlagCheck(row.tprFlag);
    },
    // 将已选中项目更改的内容同步到selectInterventions中
    updateList(row) {
      for (let i = 0; i < this.selectInterventions.length; i++) {
        if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
          let interventions = this.selectInterventions[i].interventions;
          for (let j = 0; j < interventions.length; j++) {
            if (interventions[j].interventionID == row.interventionID && interventions[j].index == row.index) {
              interventions[j] = row;
              break;
            }
          }
          break;
        }
      }
    },
    setFirst(row) {
      row.firstDayFlag = "";
      let length = this.needFirstFrequencyList.length;
      if (
        row.selected &&
        this.needFirstFrequencyList &&
        length > 0 &&
        this.needFirstFrequencyList.indexOf(Number(row.frequencyID)) != -1
      ) {
        row.firstDayFlag = "*";
      }
      this.updateList(row);
    },
    // 设置护理问题是否有选择的对应的措施
    setSelectFlag(patientProblemID, flag) {
      for (let i = 0; i < this.problemList.length; i++) {
        if (this.problemList[i].patientProblemId == patientProblemID) {
          this.$set(this.problemList[i], "selected", flag);
          break;
        }
      }
    },
    save() {
      if (this.loading || this.problemList.length <= 0) {
        return;
      }

      this.loading = true;
      this.loadingText = "保存中……";
      let patientInfo = {
        bedID: this.inpatientinfo.bedID,
        bedNumber: this.inpatientinfo.bedNumber,
        caseNumber: this.inpatientinfo.caseNumber,
        chartNo: this.inpatientinfo.chartNo,
        departmentListID: this.inpatientinfo.departmentListID,
        gender: this.inpatientinfo.gender,
        genderCode: this.inpatientinfo.genderCode,
        inpatientID: this.inpatientinfo.inpatientID,
        patientID: this.inpatientinfo.patientID,
        stationID: this.inpatientinfo.stationID,
      };
      let saveParams = {};
      let submitList = [];
      for (let i = 0; i < this.problemList.length; i++) {
        let problem = this.problemList[i];
        if (!problem.selected) {
          this.loading = false;
          this._showTip("warning", "<font color='red'><b>" + problem.nursingProblem + "</b></font>没有选择措施！");
          return;
        }
        let temp = this.selectInterventions.find((intervention) => {
          return intervention.patientProblemID == problem.patientProblemId;
        });
        let interventions = [];
        if (temp) {
          let newTemp = JSON.parse(JSON.stringify(temp));
          newTemp.interventions.forEach((intervention) => {
            if (!intervention.selected) {
              intervention.interventionID = null;
            }
            if (typeof intervention.frequencyID == "object") {
              intervention.frequencyID = intervention.frequencyID[1];
            }
            this.frequencyList.find((frequency) => {
              frequency.children.forEach((children) => {
                if (children.value === intervention.frequencyID) {
                  intervention.frequency = children.search;
                  intervention.frequencyDescription = children.label;
                  intervention.frequencyID = Number(children.value);
                }
              });
            });
          });
          interventions = newTemp.interventions;
        }
        submitList.push({
          patientInterventions: interventions,
          patientProblemID: problem.patientProblemId,
          problemID: problem.problemId,
          userID: this.user.userID,
          inpatientID: this.inpatientinfo.inpatientID,
          startTime: problem.startTime,
        });
      }
      saveParams = {
        patient: patientInfo,
        submitList: submitList,
      };
      return SaveCluster(saveParams).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          // 跳转页面
          this.$emit("jump-page");
        }
      });
    },
    // 刷新页面
    refresh() {
      this.currentPatientProblemID = undefined;
      this.selectInterventions = [];
      this.getPaitentProblem();
    },
    stopProblem(patientProblemID, index) {
      let _this = this;
      _this._deleteConfirm("确认要停止此集束护理吗?", (flag) => {
        if (flag) {
          let params = {
            inpatientID: this.inpatientinfo.inpatientID,
            patientProblemID: patientProblemID,
          };
          StopPatientProblemByID(params).then((result) => {
            if (_this._common.isSuccess(result)) {
              _this._showTip("success", "停止成功！");
              this.problemList.splice(index, 1);
              this.interventionList = [];
            }
          });
        }
      });
    },
    deleteProblem(patientProblemID, index) {
      let _this = this;
      _this._deleteConfirm("确认要删除此集束护理吗?", (flag) => {
        if (flag) {
          let params = {
            patientProblemID: patientProblemID,
          };
          DelePatientProblem(params).then((result) => {
            if (_this._common.isSuccess(result)) {
              _this._showTip("success", "删除成功！");
              this.problemList.splice(index, 1);
              this.interventionList = [];
            }
          });
        }
      });
    },
    addProblem() {
      this.clusterNursingDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.clusterNursingTime = this._datetimeUtil.getNowTime("hh:mm");
      this.nursingOrders = [];
      let params = { inpatientID: this.inpatientinfo.inpatientID };
      GetLastMain(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (!result.data) {
            this._showTip("warning", "尚未护理评估,不得手动加入集束护理！");
            return;
          }
          if (result.data && result.data.tempSaveMark === "T") {
            this._showTip("warning", "有暂存护理评估,不得手动加入集束护理！");
          } else {
            this.assessMainID = result.data.id;
            this.showAdd = true;
            this.addLoading = true;
            this.addLoadingText = "加载中……";
            let params = {
              inpatientID: this.inpatientinfo.inpatientID,
            };
            GetCanSelectNursingOrder(params).then((result) => {
              this.addLoading = false;
              if (this._common.isSuccess(result)) {
                this.nursingOrders = result.data;
              }
            });
          }
        }
      });
    },
    queryProblem() {
      if (!this.selectContent.length) {
        this.addProblem();
        return;
      }
      let param = {
        pinyin: this.selectContent,
        inpatientID: this.inpatientinfo.inpatientID,
      };
      GetClusterOrderByPinyin(param).then((response) => {
        this.nursingOrders = [];
        if (this._common.isSuccess(response)) {
          this.nursingOrders = response.data;
        }
      });
    },
    selectionProblem(selectProblems, row) {
      if (this.$refs.nursingOrdersTable) {
        this.$refs.nursingOrdersTable.setCurrentRow(row);
      }
      this.selectProblems = selectProblems;
    },
    saveAdd() {
      if (!this.diaAddTimeCheck()) {
        return;
      }
      if (this.addLoading) {
        return;
      }
      if (this.selectProblems.length <= 0) {
        return;
      }

      this.addLoading = true;
      this.addLoadingText = "保存中……";
      let saveParams = [];
      this.selectProblems.forEach((problem) => {
        saveParams.push({
          assessMainID: this.assessMainID,
          choose: true,
          diagnoseFlag: "O",
          inpatientID: this.inpatientinfo.inpatientID,
          problemID: problem.id,
          startDate: this.clusterNursingDate,
          startTime: this.clusterNursingTime,
          addType: "H",
        });
      });
      return SaveClusterProblem(saveParams).then((result) => {
        this.addLoading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          if (result.data.length > 0) {
            this.problemList = [...this.problemList, ...result.data];
          }
          this.showAdd = false;
        }
      });
    },
    //TPRFlag提示检测
    TPRFlagCheck(tprFlag) {
      if (tprFlag) {
        this._showTip("warning", "该操作可能会影响体温单数据！请谨慎操作！");
      }
    },
    getData(flag) {
      // 现有
      if (flag == "1") {
        this.getPaitentProblem();
      }
      //历史
      if (flag == "2") {
        this.getHistoryPaitentProblem();
      }
    },
    problemTimeCheck(row) {
      let check = false;
      if (row.startTime < row.startTimeStartLimit) {
        this._showTip("warning", "开始时间不得早于" + row.problemSource + "时间！");
        check = true;
      }
      if (row.startTime > this._datetimeUtil.getNow("yyyy-MM-dd hh:mm")) {
        this._showTip("warning", "开始时间不得晚于当前时间！");
        check = true;
      }
      if (check) {
        row.startTime = row.originalStartTime;
      } else {
        this._showTip("warning", "请确认计划开始、结束时间是否需要调整！");
        //更新措施开始时间限制
        for (let i = 0; i < this.interventionList.length; i++) {
          if (!this.interventionList[i].eventTrigger) {
            this.interventionList[i].startDate = row.startTime;
          }
          this.interventionList[i].startTimeStartLimit = row.startTime;
          if (this.interventionList[i].originalStartTime < row.startTime) {
            this.interventionList[i].originalStartTime = row.startTime;
          }
        }
      }
    },
    startTimeCheck(row) {
      let check = false;
      if (!row.startDate) {
        this._showTip("warning", "开始时间不得为空！");
        row.startDate = row.originalStartTime;
        return;
      }
      if (row.endDate < row.startDate) {
        this._showTip("warning", "开始时间不得晚于结束时间！");
        check = true;
      }
      if (
        this._datetimeUtil.formatDate(row.startDate, "yyyy-MM-dd hh:mm") <
        this._datetimeUtil.formatDate(row.startTimeStartLimit, "yyyy-MM-dd hh:mm")
      ) {
        this._showTip("warning", "开始时间不得早于集束护理开始时间！");
        check = true;
      }
      if (
        row.endDate &&
        this._datetimeUtil.formatDate(row.startDate, "yyyy-MM-dd hh:mm") >
          this._datetimeUtil.formatDate(row.endDate, "yyyy-MM-dd hh:mm")
      ) {
        this._showTip("warning", "开始时间不得晚于于集束护理结束时间！");
        check = true;
      }
      if (check) {
        row.startDate = row.originalStartTime;
      }
    },
    endTimeCheck(row) {
      let check = false;
      if (row.endDate < row.startDate) {
        this._showTip("warning", "结束时间不得早于开始时间！");
        check = true;
      }
      if (row.endDate < this._datetimeUtil.getNow("yyyy-MM-dd hh:mm")) {
        this._showTip("warning", "结束时间不得早于当前时间！");
        check = true;
      }
      if (check) {
        row.endDate = row.originalEndTime;
      }
    },
    diaAddTimeCheck() {
      this.nowDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      this.currentTime = this.clusterNursingDate + " " + this.clusterNursingTime;
      if (this.currentTime > this.nowDateTime) {
        this._showTip("warning", "时间不得晚于当前时间！！");
        return false;
      }
      if (this.currentTime < this.lastAssessDateTime) {
        this._showTip("warning", "开始时间不能早于护理评估时间！");
        return false;
      }
      return true;
    },
    /**
     * description:检核首次是否勾选
     * param {*} row
     * return {*}
     */
    getDisabledFlag(row) {
      if (!row || !row.startTimeStartLimit || !this.shiftStartDate || !this.shiftEndDate) {
        return false;
      }
      //未勾选 不可选择
      if (!row.selected) {
        return true;
      }
      //未在班别日期之内 禁止勾选
      if (this._datetimeUtil.getTimeDifference(this.shiftStartDate, row.startTimeStartLimit, undefined, "M") < 0) {
        return true;
      }
      if (this._datetimeUtil.getTimeDifference(row.startTimeStartLimit, this.shiftEndDate, undefined, "M") < 0) {
        return true;
      }
      return false;
    },
    /**
     * description: 获取当天班别开始时间和结束时间
     * param {*}
     * return {*}
     */
    getShifDayStartAndEndDateTime() {
      let params = {
        type: "S",
      };
      GetShifDayStartAndEndDateTime(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.shiftStartDate = result.data.startDateTime;
          this.shiftEndDate = result.data.endDateTime;
        } else {
          this.shiftStartDate = undefined;
          this.shiftEndDate = undefined;
        }
      });
    },
    /**
     * description: 显示取消勾选原因弹窗
     * param {*} row 措施
     * param {*} clearReason 清除原因
     * param {*} clearDeselectIntervention 清除取消勾选集合
     * return {*}
     */
    showDeselectReason(row, clearReason, clearDeselectIntervention) {
      if (!this.showDeselectReasonFlag) {
        return;
      }
      if (clearReason) {
        this.deselectReason = undefined;
      }
      if (clearDeselectIntervention) {
        this.deselectIntervention = [];
      }
      this.showDeselectFlag = true;
      if (row.patientInterventionID && !this.deselectIntervention.find((m) => m == row.patientInterventionID)) {
        this.deselectIntervention.push(row.patientInterventionID);
      }
    },
    /**
     * description: 保存取消勾选原因
     * return {*}
     */
    saveDeselectReason() {
      if (!this.deselectReason || this.deselectReason.length == 0) {
        this._showTip("warning", "未填写取消措施原因");
        return;
      }
      this.showDeselectFlag = false;
      this.loading = true;
      let params = [];
      this.deselectIntervention.forEach((item) => {
        params.push({
          patientProblemID: this.currentPatientProblemID,
          inpatientID: this.inpatientinfo.inpatientID,
          patientInterventionID: item,
          deselectReason: this.deselectReason,
        });
      });
      SaveDeselectReason(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
        }
      });
    },
    /**
     * description: 关闭弹窗
     * return {*}
     */
    cancelDeselectReason() {
      this.showDeselectFlag = false;
      this.interventionList.forEach((item) => {
        if (this.deselectIntervention.find((m) => m == item.patientInterventionID)) {
          item.selected = true;
          this.$nextTick(() => {
            this.$refs.intervention.toggleRowSelection(item, true);
          });
          //还原频次
          this.frequencyList.find((frequency) => {
            frequency.children.forEach((children) => {
              if (children.search === this.oldFrequency) {
                item.frequencyID = children.value;
                item.frequency = this.oldFrequency;
              }
            });
          });
        }
      });
      this.deselectIntervention = [];
    },
  },
};
</script>

<style <style lang="scss">
.cluster-care {
  .top {
    .btn {
      float: right;
    }
  }
  .el-select .el-input__inner {
    color: $base-color;
  }
  .cluster-wrap {
    height: 100%;
    width: 100%;
    .left-wrap {
      float: left;
      height: 100%;
      width: 405px;
      .nursing-problem-table {
        .button-col .iconfont {
          font-weight: normal;
        }
      }
    }
    .right-wrap {
      float: right;
      height: 100%;
      width: calc(100% - 415px);
      margin-left: 10px;
      .nursing-intervention-table {
        flex: auto;
        .select {
          padding: 3px;
        }
        /* 禁止全选 */
        // th .el-checkbox {
        //   display: none;
        // }
        .cell {
          text-overflow: unset;
        }
      }
    }
  }
  .unconfirmed-cluster {
    color: #ff0000;
  }
  .el-dialog.add-nursing-orders {
    width: 500px;
    height: 600px;
    .pinyin-select {
      height: 40px;
      .select-input {
        width: 70%;
        .el-input-group__append {
          padding: 0 5px;
        }
        i {
          color: #8cc63e;
        }
      }
    }
    .nursing-orders-table {
      .select {
        padding: 3px;
      }
      /* 禁止全选 */
      th .el-checkbox {
        display: none;
      }
    }
    .el-dialog__footer {
      padding: 0px 15px;
    }
  }
  .date-width {
    width: 115px !important;
  }
  .time-width {
    width: 80px !important;
  }
  .reasons.el-dialog {
    width: 520px;
    height: 300px;
  }
}
</style>
