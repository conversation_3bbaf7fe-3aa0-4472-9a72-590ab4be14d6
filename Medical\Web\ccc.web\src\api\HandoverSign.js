/*
 * FilePath     : \ccc.web\src\api\HandoverSign.js
 * Author       : 郭鹏超
 * Date         : 2023-02-10 11:25
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-02-14 11:25
 * Description  :
 * CodeIterationRecord:
 */

import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/HandoverSign";

export const urls = {
  SaveKeySign: baseUrl + "/SaveKeySign"
};
// 批量交班保存HandoverSign
export const SaveKeySign = params => {
  return http.post(urls.SaveKeySign, params);
};
