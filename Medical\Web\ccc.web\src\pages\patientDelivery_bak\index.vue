<!--
 * FilePath     : \src\pages\patientDelivery_bak\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-04-22 17:06
 * LastEditors  : 苏军志
 * LastEditTime : 2023-03-09 10:59
 * Description  : 产程专项护理
 * CodeIterationRecord: 2022-08-18 2876-专项增加带入护理记录选框 -杨欣欣
                        2022-08-24 2855-作为IT人员，我需要依据提供的资料调整嘉会产科及新生儿科交接内容，以利于护理交班内容完整。 杨欣欣
                        2022-12-01 2869-跳转IO改版 杨欣欣
                        2023-01-12 3251-作为IT人员，我需要排程串产时专项时，可以反写排程状态，以利业务流程正常 -苏军志
-->
<template>
  <div class="patient-delivery-wrap">
    <specific-care
      v-model="showMaintainFlag"
      :showRecordArr="showRecordArr"
      :showRecordLabelArr="['产程主记录', '产程维护记录']"
      :handOverFlag="handOverArr"
      :nursingRecordFlag="nursingRecordArr"
      :drawerTitle="deliveryDrawerTitle"
      :informPhysicianFlag="informPhysicianArr"
      drawerHeight="700"
      :mainTableHeight="tableOneRowHeight"
      :editFlag="showEditButton"
      @mainAdd="mainAddOrFix"
      @maintainAdd="maintainAddOrFix"
      @save="deliverySave()"
      @cancel="deliveryCancel()"
      @getMainFlag="getMainFlag"
      @getMaintainFlag="getMaintainFlag"
      @getHandOverFlag="getHandOverFlag"
      @getNursingRecordFlag="getBringToNursingRecordFlag"
      @getInformPhysicianFlag="getInformPhysicianFlag"
      class="patient-delivery"
    >
      <!-- 主记录 -->
      <div slot="main-record">
        <el-table
          row-class-name="maintain-record-row"
          header-row-class-name="maintain-record-herderRow"
          @row-click="getMaintainList"
          height="100%"
          :data="mainRecord"
          border
          stripe
        >
          <el-table-column
            v-for="(item, index) in mianTableHeader"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :width="!item.minWidthFlag ? item.width : item.minWidthFlag"
            :min-width="item.minWidthFlag ? item.width : item.minWidthFlag"
            :header-align="item.herderPosition"
            :align="item.position"
          >
            <template slot-scope="scope">
              <div v-if="item.prop == 'sourceContent'">
                <el-tooltip v-if="scope.row.sourceFlag" :content="scope.row.sourceContent" placement="top">
                  <div :class="['flag', scope.row.sourceFlag]">
                    <span v-if="scope.row.sourceContent == 'A'">护</span>
                    <span v-if="scope.row.sourceContent == 'H'">班</span>
                  </div>
                </el-tooltip>
              </div>
              <div v-else-if="item.dateFlag" v-formatTime="{ value: scope.row[item.prop], type: item.format }"></div>

              <div v-else>
                {{ scope.row[item.prop] }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="100" header-align="center">
            <template slot-scope="scope">
              <el-tooltip v-if="!scope.row.thirdStageCompletedTime" content="结束">
                <div @click.stop="endDelivery(scope.row)" class="iconfont icon-stop"></div>
              </el-tooltip>
              <el-tooltip content="删除">
                <div @click.stop="deleteMain(scope.row)" class="iconfont icon-del"></div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 维护记录 -->
      <div slot="maintain-record">
        <el-table height="100%" :data="maintainRecord" border stripe>
          <el-table-column
            v-for="(item, index) in maintainTableHeader"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :width="!item.minWidthFlag ? item.width : item.minWidthFlag"
            :min-width="item.minWidthFlag ? item.width : item.minWidthFlag"
            :header-align="item.herderPosition"
            :align="item.position"
          >
            <template slot-scope="scope">
              <div v-if="item.prop == 'sourceContent'">
                <span v-if="scope.row.recordsCode.indexOf('Start') != -1">开始评估</span>
                <span v-else-if="scope.row.recordsCode.indexOf('End') != -1">结束评估</span>
                <span v-else>例行评估</span>
              </div>
              <div v-else-if="item.dateFlag" v-formatTime="{ value: scope.row[item.prop], type: item.format }"></div>
              <div v-else-if="item.prop == 'pregnantWeeks'">
                {{ scope.row.pregnantWeeks + "周" + scope.row.pregnantDays + "天" }}
              </div>
              <div v-else>
                {{ scope.row[item.prop] }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="70" header-align="center">
            <template slot-scope="scope">
              <el-tooltip content="修改">
                <div @click.stop="maintainAddOrFix(scope.row)" class="iconfont icon-edit"></div>
              </el-tooltip>
              <el-tooltip v-if="scope.row.recordsCode != 'DeliveryStart'" content="删除">
                <div @click="deleteMaintain(scope.row)" class="iconfont icon-del"></div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 弹窗内容 -->
      <base-layout slot="drawer-content" header-height="auto">
        <div slot="header">
          <span class="label">执行日期:</span>
          <el-date-picker
            class="date-picker"
            v-model="performDate"
            type="date"
            :clearable="false"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          />
          <el-time-picker
            class="time-picker"
            v-model="performTime"
            :clearable="false"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
          />
          <station-selector v-model="currentStation" label="执行病区:" width="160" />
          <dept-selector label="" width="140" v-model="currentDepartment" :stationID="currentStation" />
        </div>
        <tabs-layout
          ref="tabsLayout"
          v-loading="tabsLayoutLoading"
          checkFlag
          :element-loading-text="tabsLayoutText"
          @button-click="buttonClick"
          :template-list="templateDatas"
          @change-values="changeValues"
          @checkTN="checkTN"
          @button-record-click="buttonRecordClick"
        />
      </base-layout>
    </specific-care>
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      fullscreen
      custom-class="no-footer specific-care-view"
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" scrolling="no" frameborder="0" width="100%" height="99%" />
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonRecordTitle"
      :visible.sync="showButtonRecordDialog"
      v-loading="buttonRecordLoading"
      :element-loading-text="buttonRecordLoadingText"
    >
      <risk-rating-scale
        v-if="showButtonRecordDialog"
        :conponentData="buttonRecordParams"
        @checked-collect="getScoreReturn"
        :show-bar="false"
      />
      <div slot="footer">
        <el-button @click="showButtonRecordDialog = false">取消</el-button>
        <el-button type="primary" @click="saveButtonRecord">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import RiskRatingScale from "@/pages/riskAssessment/components/RiskRatingScale";
import { GetButtonData, GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { mapGetters } from "vuex";
import {
  GetRecordTableView,
  GetCareMainTableView,
  DeleteRecord,
  DeleteCareRecord,
  SaveRecord,
  SaveCareMain,
  GetDeliveryAssessViewAsync,
} from "@/api/Delivery";
// import { GetFormatDataByMainID, SavePatientScoreMainAndDetail, GetRecordBySourceType } from "@/api/PatientScore";
import { GetBringToShiftSetting } from "@/api/Setting.js";
export default {
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
      user: "getUser",
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  components: {
    baseLayout,
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    RiskRatingScale,
  },
  data() {
    return {
      showMaintainFlag: false,
      showMaintainFlag: false,
      deliveryDrawerTitle: "",
      showRecordArr: [true, false],
      recordsCodeInfo: {},
      //维护弹窗顶部数据
      performDate: undefined,
      performTime: undefined,
      currentStation: undefined,
      currentDepartment: undefined,
      //评估模板loading开关
      tabsLayoutLoading: false,
      tabsLayoutText: "加载中……",
      //维护弹窗模板数据
      templateDatas: [],
      //评估模板返回数据
      assessDatas: [],
      checkTNFlag: true,
      // 显示BR类弹窗
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR类弹窗所需参数
      buttonRecordParams: {},
      // BR类弹窗选择后数据
      scoreReturn: undefined,
      // BR项
      brItem: undefined,
      // BR项修改序号
      patientScoreMainID: "",
      buttonRecordLoading: false,
      buttonRecordLoadingText: "加载中……",
      //主记录表格数据
      mainRecord: [],
      //维护记录表格数据
      maintainRecord: [],
      //选中主记录表格数据
      currentMainRecord: undefined,
      //主记录表头
      mianTableHeader: [
        {
          label: "来源",
          prop: "sourceContent",
          width: 50,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "发生科室",
          prop: "occuredDepartmenName",
          width: 120,
          minWidthFlag: true,
          herderPosition: "left",
          position: "left",
        },
        {
          label: "开始时间",
          prop: "addDate",
          width: 160,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          dateFlag: true,
          format: "dateTime",
        },
        {
          label: "胎次",
          prop: "pregnantTimes",
          width: 50,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "产次",
          prop: "deliveryTimes",
          width: 50,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "孕周",
          prop: "pregnantWeekDays",
          width: 100,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },

        {
          label: "预产期",
          prop: "edc",
          width: 110,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          dateFlag: true,
          format: "date",
        },
        {
          label: "规律宫缩开始",
          prop: "contractionStartedTime",
          width: 160,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          dateFlag: true,
          format: "dateTime",
        },
        {
          label: "宫口全开",
          prop: "fullyDilatedTime",
          width: 160,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          dateFlag: true,
          format: "dateTime",
        },
        {
          label: "胎儿娩出",
          prop: "deliveryStartTime",
          width: 160,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          dateFlag: true,
          format: "dateTime",
        },
        {
          label: "第三产程结束",
          prop: "thirdStageCompletedTime",
          width: 160,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          dateFlag: true,
          format: "dateTime",
        },
        {
          label: "新生儿数量",
          prop: "newBornNum",
          width: 160,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "记录人",
          prop: "addUserName",
          width: 100,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
      ],
      //维护记录表头
      maintainTableHeader: [
        {
          label: "类型",
          prop: "sourceContent",
          width: 50,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "维护科室",
          prop: "departmentName",
          width: 120,
          minWidthFlag: false,
          herderPosition: "left",
          position: "left",
        },
        {
          label: "日期",
          prop: "assessDate",
          width: 110,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          dateFlag: true,
          format: "date",
        },
        {
          label: "时间",
          prop: "assessTime",
          width: 60,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          dateFlag: true,
          format: "time",
        },
        {
          label: "疼痛评分",
          prop: "painScore",
          width: 60,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "宫缩强度",
          prop: "contractionIntensity",
          width: 60,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "宫缩频率",
          prop: "uterineContractions",
          width: 60,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "宫缩间隔时间",
          prop: "contractionInterval",
          width: 70,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "宫缩持续时间",
          prop: "contractionDuration",
          width: 70,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "胎方位",
          prop: "fetalPosition",
          width: 90,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "胎心率",
          prop: "fetalHeartRates",
          width: 140,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "宫口大小",
          prop: "cervixDilatatio",
          width: 80,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "宫颈消失",
          prop: "effacement",
          width: 100,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "先露高低",
          prop: "fetalPresentation",
          width: 80,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "胎膜",
          prop: "membraneStatus",
          width: 80,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "分娩信息",
          prop: "deliveryInformation",
          width: 180,
          minWidthFlag: true,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "催产素",
          prop: "oxytocin",
          width: 120,
          minWidthFlag: true,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "观察记录",
          prop: "remarks",
          width: 100,
          minWidthFlag: true,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "记录人",
          prop: "addUserName",
          width: 80,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
      ],
      recordID: undefined,
      careMainID: "",
      //主记录表格表头加第一行的高度
      tableOneRowHeight: undefined,
      //保存维护记录需要更新主记录assessListID
      recordTime: {
        4792: "contractionStartedTime",
        4793: "fullyDilatedTime",
        4794: "deliveryStartTime",
        4795: "thirdStageCompletedTime",
        4747: "newBornNum",
      },
      buttonAssessListID: "",
      buttonName: "",
      showButtonDialog: false,
      settingHandOver: false,
      handOverArr: [true, false],
      settingBringToNursingRecord: false,
      nursingRecordArr: [false, false],
      informPhysicianArr: [true, false],
      //是否能编辑删除该数据
      showEditButton: true,
      patientScheduleMainID: undefined,
    };
  },
  watch: {
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.showRecordArr = [true, false];
          this.getMainFlag(true);
        }
      },
      immediate: true,
    },
    showButtonDialog(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
  },
  mounted() {
    //跳转不允许切换病人  右键进入除外
    if (Object.keys(this.$route.query).length > 0 && !this.$route.query.shortCutFlag) {
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
    if (this.$route.query.patientScheduleMainID && this.$route.query.patientScheduleMainID != "null") {
      this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
    }
    this.getBringHandOverSetting();
    this.getBringToNursingRecordSetting();
    if (this.hospitalInfo.hospitalID == "2") {
      this.iniHeader();
    }
  },
  methods: {
    /**
     * description: 临时处理中山产时表头
     */
    iniHeader() {
      for (let index = 0; index < this.mianTableHeader.length; index++) {
        const element = this.mianTableHeader[index];
        if (element.label == "规律宫缩开始") {
          this.mianTableHeader[index].label = "临产";
        }
        if (element.label == "胎儿娩出") {
          this.mianTableHeader[index].label = "分娩";
        }
        if (element.label == "第三产程结束") {
          this.mianTableHeader[index].label = "胎盘娩出";
        }
        if (element.label == "新生儿数量") {
          this.mianTableHeader.splice(index, 1);
        }
      }
      for (let index = 0; index < this.maintainTableHeader.length; index++) {
        const element = this.maintainTableHeader[index];
        if (element.label == "疼痛评分") {
          this.maintainTableHeader.splice(index, 1);
        }
        if (element.label == "分娩信息") {
          this.maintainTableHeader.splice(index, 1);
        }
      }
    },
    //获取主记录表格数据
    getMainRecord() {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      GetRecordTableView(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.mainRecord = res.data;
          this.currentMainRecord = undefined;
          this.getMainTableOneRowhight();
        }
      });
    },
    //获取维护表格数据
    getMaintainRecord() {
      let params = {
        recordID: this.currentMainRecord.patientDeliveryRecordID,
      };
      GetCareMainTableView(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.maintainRecord = res.data;
        }
      });
    },
    //主记录新增或修改
    async mainAddOrFix(item) {
      this.showEditButton = true;
      if (item) {
        let userID = undefined;
        if (item.addUserName == this.user.userName) {
          userID = this.user.userID;
        }
        //判断是否可修改该数据
        this.showEditButton = await this._common.checkActionAuthorization(this.user, userID);
        if (this.showEditButton) {
          let ret = await this._common.getEditAuthority(item.patientDeliveryRecordID, "PatientDeliveryRecord");
          if (ret) {
            this.showEditButton = false;
            this._showTip("warning", ret);
          } else {
            this.showEditButton = true;
          }
        }
      }
      //打开弹窗 修改弹窗标题
      this.openOrCloseDrawer(true, "新增产程记录");
      //修改评估表单号
      this.recordsCodeInfo.recordsCode = "DeliveryStart";
      //填充弹窗顶部数据
      this.performDate = item
        ? this._datetimeUtil.formatDate(item.addDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = item
        ? this._datetimeUtil.formatDate(item.addDate, "hh:mm:ss")
        : this._datetimeUtil.getNowTime("hh:mm:ss");
      this.currentStation = item ? item.occuredStationID : this.patient.stationID;
      this.currentDepartment = item ? item.occuredDepartmentID : this.patient.departmentListID;
      this.currentMainRecord = item ? item : undefined;
      this.recordID = item ? item.patientDeliveryRecordID : undefined;
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      this.getDeliveryAssessView(item);
    },
    //维护主表新增或修改
    async maintainAddOrFix(item) {
      this.showEditButton = true;
      if (item) {
        //判断是否可修改该数据
        let userID = undefined;
        if (item.addUserName == this.user.userName) {
          userID = this.user.userID;
        }
        this.showEditButton = await this._common.checkActionAuthorization(this.user, userID);
        if (this.showEditButton) {
          let ret = await this._common.getEditAuthority(item.patientDeliveryRecordID, "PatientDeliveryRecord");
          if (ret) {
            this.showEditButton = false;
            this._showTip("warning", ret);
          } else {
            this.showEditButton = true;
          }
        }
      }
      //新增维护记录检核
      if (!item && !this.maintainAddCheck()) {
        return;
      }
      this.openOrCloseDrawer(true, "产程记录维护");
      this.recordsCodeInfo.recordsCode = item ? item.recordsCode : "DeliveryMaintain";
      //填充弹窗顶部数据
      this.performDate = item
        ? this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = item
        ? this._datetimeUtil.formatDate(item.assessTime, "hh:mm:ss")
        : this._datetimeUtil.getNowTime("hh:mm:ss");
      this.currentStation = item ? item.stationID : this.patient.stationID;
      this.currentDepartment = item ? item.departmentListID : this.patient.departmentListID;
      this.careMainID = item ? item.patientDeliveryCareMainID : "temp_" + this._common.guid();
      // if (item && item.recordsCode == "DeliveryStart") {
      //   this.recordID = undefined;
      // }
      this.recordID = item ? undefined : this.currentMainRecord.patientDeliveryRecordID;
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      this.getDeliveryAssessView();
    },
    //结束产程
    endDelivery(item) {
      this.openOrCloseDrawer(true, "产程结束");
      this.recordsCodeInfo.recordsCode = "DeliveryEnd";
      //填充弹窗顶部数据
      this.performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = this._datetimeUtil.getNowTime("hh:mm:ss");
      this.currentStation = this.patient.stationID;
      this.currentDepartment = this.patient.departmentListID;
      this.currentMainRecord = item;
      this.recordID = item.patientDeliveryRecordID;
      this.careMainID = "temp_" + this._common.guid();

      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      this.getDeliveryAssessView();
    },
    //获取评估模板
    async getDeliveryAssessView(isAdd) {
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "加载中……";
      let params = {
        inpatientID: this.patient.inpatientID,
        departmentListID: this.patient.departmentListID,
        mappingType: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
      };
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.recordsCodeInfo = result.data;
        } else {
          this.recordsCodeInfo.recordsCode = undefined;
        }
      });
      if (!this.recordsCodeInfo || !this.recordsCodeInfo.recordsCode) {
        this.openOrCloseDrawer(false);
        this._showTip("warning", "找不到评估模板！");
        return;
      }
      // if (typeof isAdd !== undefined) {
      //   this.openOrCloseDrawer(true, "产程记录维护");
      // }
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        inpatientID: this.patient.inpatientID,
        dateOfBirth: this.patient.dateOfBirth,
      };
      // if (this.patientPCRecordID) {
      //   params.recordID = this.patientPCRecordID;
      // }
      //&& this.recordsCodeInfo.recordsCode == "DeliveryStart"
      if (this.recordID) {
        params.recordID = this.recordID;
      }
      if (this.careMainID && this.careMainID.indexOf("temp") == -1) {
        params.careMainID = this.careMainID;
      }
      this.templateDatas = [];
      await GetDeliveryAssessViewAsync(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
      this.tabsLayoutLoading = false;
      this.tabsLayoutText = "";
    },
    //组件保存事件
    async deliverySave() {
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "保存中……";
      //主记录保存
      if (this.recordsCodeInfo.recordsCode == "DeliveryStart") {
        await this.deliveryRecordSave();
        return;
      }
      //维护记录和产程结束保存
      if (this.recordsCodeInfo.recordsCode == "DeliveryMaintain" || this.recordsCodeInfo.recordsCode == "DeliveryEnd") {
        await this.deliveryCareMainSave();
        return;
      }
      this.tabsLayoutLoading = false;
      this.tabsLayoutText = "";
    },
    //主记录保存
    async deliveryRecordSave() {
      //保存检核
      if (!this.saveCheck()) {
        return;
      }
      // 新增保存数据
      let saveData = {
        Main: {},
        Details: [],
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        informPhysician: this.informPhysicianArr[1],
      };
      saveData.bringToShift = this.handOverArr[1];
      saveData.bringToNursingRecord = this.nursingRecordArr[1];
      //如果来源于措施执行则填入
      if (this.patientScheduleMainID) {
        saveData.PatientScheduleMainID = this.patientScheduleMainID;
      }
      //组装保存Detail数据
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          bookMarkID: "DeliveryStart",
          assessValueJson: content.controlerType == "BD" && content.assessValue ? content.assessValue : undefined,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        saveData.Details.push(detail);
      });
      //组装保存main数据
      if (this.currentMainRecord) {
        saveData.Main.patientDeliveryRecordID = this.currentMainRecord.patientDeliveryRecordID;
      }
      saveData.Main.inpatientID = this.patient.inpatientID;
      saveData.Main.addDate = this.performDate + " " + this.performTime;
      saveData.Main.occuredStationID = this.currentStation;
      saveData.Main.occuredDepartmentID = this.currentDepartment;
      saveData.Main.PatientScheduleMainID = this.patientScheduleMainID;
      await SaveRecord(saveData).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
          this.openOrCloseDrawer(false);
          this.showRecordArr = [true, false];
          this.getMainFlag(true);
        }
      });
    },
    //维护记录和产程结束保存
    async deliveryCareMainSave() {
      //保存检核
      if (!this.saveCheck()) {
        this.tabsLayoutLoading = false;
        this.tabsLayoutText = "";
        return;
      }
      if (!this.$refs.tabsLayout.checkRequire()) {
        this.tabsLayoutLoading = false;
        return;
      }
      //排除结束评估保存
      if (!this.currentMainRecord) {
        return;
      }
      let saveData = {
        Main: {},
        Details: [],
        HandoverID: "",
      };
      //组装保存Detail数据
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          bookMarkID: content.bookMarkID,
          assessValueJson: content.controlerType == "BD" && content.assessValue ? content.assessValue : undefined,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        saveData.Details.push(detail);
      });
      saveData.Main.patientDeliveryRecordID = this.currentMainRecord.patientDeliveryRecordID;
      saveData.Main.inpatientID = this.currentMainRecord.inpatientID;
      saveData.Main.recordsCode = this.recordsCodeInfo.recordsCode;
      saveData.Main.assessDate = this.performDate;
      saveData.Main.assessTime = this.performTime;
      saveData.Main.bringToShift = this.handOverArr[1];
      saveData.Main.bringToNursingRecord = this.nursingRecordArr[1];
      saveData.Main.informPhysician = this.informPhysicianArr[1];
      saveData.Main.StationID = this.currentStation;
      saveData.Main.DepartmentListID = this.currentDepartment;
      saveData.Main.PatientScheduleMainID = this.patientScheduleMainID;
      if (this.careMainID.indexOf("temp") == -1) {
        saveData.Main.patientDeliveryCareMainID = this.careMainID;
        saveData.isAdd = false;
      } else {
        saveData.Main.patientDeliveryCareMainID = this.careMainID.split("_")[1];
        saveData.Main.numberOfAssessment = this.maintainRecord.length + 1;
        saveData.isAdd = true;
      }

      await SaveCareMain(saveData).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
          this.getMaintainRecord();
          //结束评估
          // if (this.recordsCodeInfo.recordsCode == "DeliveryEnd") {
          //   this.getMainRecord();
          // }
          //前端先更新主表内容
          this.updateRecordData(saveData.Details);
          this.openOrCloseDrawer(false);
        }
      });
    },
    /**
     * description: 底部弹窗取消事件
     * param {*}
     * return {*}
     */
    deliveryCancel() {
      this.openOrCloseDrawer(false, "");
      this.recordID = undefined;
      this.careMainID = undefined;
    },
    //主记录删除
    async deleteMain(item) {
      //判断是否可修改或删除该数据
      this.showEditButton = true;
      if (item) {
        let userID = undefined;
        if (item.addUserName == this.user.userName) {
          userID = this.user.userID;
        }
        //判断是否可修改该数据
        this.showEditButton = await this._common.checkActionAuthorization(this.user, userID);
        if (this.showEditButton) {
          let ret = await this._common.getEditAuthority(item.patientDeliveryRecordID, "PatientDeliveryRecord");
          if (ret) {
            this.showEditButton = false;
            this._showTip("warning", ret);
            return;
          } else {
            this.showEditButton = true;
          }
        }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            recordID: item.patientDeliveryRecordID,
          };
          DeleteRecord(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getMainRecord();
              this.showRecordArr = [true, false];
              this.maintainRecord = [];
            }
          });
        }
      });
    },
    //维护记录删除
    async deleteMaintain(item) {
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(item.patientDeliveryCareMainID, "PatientDeliveryCareMain");
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            careMainID: item.patientDeliveryCareMainID,
          };
          DeleteCareRecord(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getMaintainRecord();
              //删除结束评估维护记录清空第三产程结束时间
              if (item.recordsCode == "DeliveryEnd") {
                this.currentMainRecord.thirdStageCompletedTime = undefined;
              }
            }
          });
        }
      });
    },
    //点击主记录切换页面形态
    getMaintainList(item) {
      //切换页面形态 主记录只显示选中记录
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.mainRecord = [item];
        this.currentMainRecord = item;
        this.getMaintainRecord();
      }
    },
    //主记录勾选框事件
    getMainFlag(flag) {
      if (flag) {
        this.getMainRecord();
        this.maintainRecord = [];
      } else {
        if (!this.currentMainRecord && this.mainRecord.length > 0) {
          this.currentMainRecord = this.mainRecord[0];
          this.getMaintainRecord();
        }
      }
    },
    //维护记录勾选框事件
    getMaintainFlag(flag) {
      if (flag && this.currentMainRecord) {
        this.getMaintainRecord();
      } else {
        this.getMainRecord();
      }
    },
    //评估模板返回数据
    changeValues(datas) {
      this.assessDatas = datas;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    //维护记录新增检核
    maintainAddCheck() {
      if (!this.currentMainRecord) {
        this._showTip("warning", "未选择主记录");
        return false;
      }
      if (this.currentMainRecord.thirdStageCompletedTime) {
        this._showTip("warning", "该记录已结束");
        return false;
      }
      return true;
    },
    // 拔管BR
    // async buttonRecordClick(content) {
    //   if (!content.linkForm) {
    //     this._showTip("warning", "【" + content.itemName + "】为BR类，但未配置linkForm！");
    //     return;
    //   }
    //   let params = {
    //     inpatientID: this.patient.inpatientID,
    //     recordListID: Number(content.linkForm),
    //     sourceType: "Delivery",
    //     sourceID: this.getCareMainID() + "_" + content.assessListID,
    //   };
    //   this.patientScoreMainID = "";
    //   await GetRecordBySourceType(params).then((result) => {
    //     if (this._common.isSuccess(result) && result.data) {
    //       this.patientScoreMainID = result.data.patientScoreMainID;
    //     }
    //   });
    //   this.brItem = content;
    //   this.buttonRecordTitle = content.itemName;
    //   this.showButtonRecordDialog = true;
    //   this.buttonRecordLoading = true;
    //   this.buttonRecordLoadingText = "加载中……";
    //   params = {
    //     recordListID: Number(content.linkForm),
    //     patientScoreMainID: this.patientScoreMainID,
    //     stationID: this.patient.stationID,
    //   };
    //   await GetFormatDataByMainID(params).then((result) => {
    //     this.buttonRecordLoading = false;
    //     if (this._common.isSuccess(result)) {
    //       this.buttonRecordParams = result.data;
    //     }
    //   });
    // },
    // getScoreReturn(value) {
    //   this.scoreReturn = value;
    // },
    // saveButtonRecord() {
    //   this.buttonRecordLoading = true;
    //   this.buttonRecordLoadingText = "保存中……";
    //   let params = {
    //     PatientScoreMainID: this.patientScoreMainID,
    //     RecordListID: Number(this.brItem.linkForm),
    //     RecordsFormatID: this.scoreReturn,
    //     sourceType: "Delivery",
    //     sourceID: this.getCareMainID() + "_" + this.brItem.assessListID,
    //     inpatientID: this.patient.inpatientID,
    //     stationID: this.patient.stationID,
    //     scheduleDate: this.assessDate,
    //     scheduleTime: this.assessTime,
    //   };
    //   SavePatientScoreMainAndDetail(params).then((result) => {
    //     this.buttonRecordLoading = false;
    //     this.showButtonRecordDialog = false;
    //     if (this._common.isSuccess(result)) {
    //       // 保存成功，回显数据
    //       this.updateButton(this.brItem.assessListID);
    //     }
    //   });
    // },
    getCareMainID() {
      let tempCareMainID = "";
      if (this.careMainID) {
        if (this.careMainID.indexOf("temp") != -1) {
          tempCareMainID = this.careMainID.split("_")[1];
        } else {
          tempCareMainID = this.careMainID;
        }
      }
      return tempCareMainID;
    },
    //评估保存检核
    saveCheck() {
      if (this.currentStation == "") {
        this._showTip("warning", "请选择发生病区");
        return false;
      }
      if (this.currentDepartment == "") {
        this._showTip("warning", "请选择发生科室");
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        this.tabsLayoutLoading = false;
        return false;
      }
      return true;
    },
    //产程过程检核 不允许重复保存同一分娩状态
    deliveryProcessCheck(details) {
      let flag = true;
      Object.keys(this.recordTime).forEach((key) => {
        if (
          details.find((item) => item.assessListID == key) &&
          this.currentMainRecord[this.recordTime[key]] &&
          key != 4747
        ) {
          flag = false;
        }
      });
      return flag;
    },
    //前端先更新主表内容
    updateRecordData(details) {
      if (!details.length) {
        return;
      }
      Object.keys(this.recordTime).forEach((key) => {
        let sucDetail = details.find((item) => item.assessListID == key);
        if (sucDetail) {
          if (key == 4747) {
            //回显主记录分娩时间和新生儿数量
            this.currentMainRecord[this.recordTime[key]] = sucDetail.assessValue;
            if (sucDetail.assessValue == 1) {
              this.currentMainRecord[this.recordTime[4794]] = this.performDate + " " + this.performTime;
            }
          } else {
            this.currentMainRecord[this.recordTime[key]] = this.performDate + " " + this.performTime;
          }
        } else {
          if (this.recordsCodeInfo.recordsCode == "DeliveryMaintain" && key == 4747) {
            //回显主记录分娩时间和新生儿数量
            this.currentMainRecord[this.recordTime[key]] = "";
          }
        }
      });
      if (this.recordsCodeInfo.recordsCode == "DeliveryEnd") {
        this.currentMainRecord.thirdStageCompletedTime = this.performDate + " " + this.performTime;
      }
    },
    //专项护理弹窗开关函数
    openOrCloseDrawer(flag, title) {
      // this.deliveryDrawerTitle = title;
      this.showMaintainFlag = flag;
      this.deliveryDrawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
    },
    //获取表格第一行加表头高度
    getMainTableOneRowhight() {
      this.$nextTick(() => {
        let oneDom = document.getElementsByClassName("patient-delivery")[0];
        let twoDom = oneDom.getElementsByClassName("maintain-record-row");
        let headerTwoDom = oneDom.getElementsByClassName("maintain-record-herderRow");
        let twoDomHeight = twoDom.length > 0 ? twoDom[0].offsetHeight : 0;
        let headerTwoDomHeight = headerTwoDom[0].offsetHeight;
        this.tableOneRowHeight = twoDomHeight + headerTwoDomHeight;
      });
    },
    //评估组件按钮事件
    buttonClick(content) {
      this.showButtonDialog = true;
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        `&sourceID=${this.getCareMainID()}` +
        "&sourceType=Delivery" +
        "&isDialog=true";
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    //添加完更新按钮数量
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      for (let i = 0; i < this.templateDatas.length; i++) {
        for (let j = 0; j < this.templateDatas[i].groups.length; j++) {
          if (
            this.templateDatas[i].groups[j].controlerType.trim() == "B" ||
            this.templateDatas[i].groups[j].controlerType.trim() == "BR"
          ) {
            if (this.templateDatas[i].groups[j].assessListID == assessListID) {
              this.$set(this.templateDatas[i].groups[j], "assessValue", item.assessValue);
              this.$set(this.templateDatas[i].groups[j], "linkForm", item.linkForm);
              return;
            }
          }
          for (let k = 0; k < this.templateDatas[i].groups[j].contents.length; k++) {
            if (
              this.templateDatas[i].groups[j].contents[k].controlerType.trim() == "B" ||
              this.templateDatas[i].groups[j].contents[k].controlerType.trim() == "BR"
            ) {
              if (this.templateDatas[i].groups[j].contents[k].assessListID == assessListID) {
                this.$set(this.templateDatas[i].groups[j].contents[k], "assessValue", item.assessValue);
                this.$set(this.templateDatas[i].groups[j].contents[k], "linkForm", item.linkForm);
                return;
              }
            }
            // 判断三阶里是否有按钮
            if (this.templateDatas[i].groups[j].contents[k].childList) {
              for (let m = 0; m < this.templateDatas[i].groups[j].contents[k].childList.length; m++) {
                if (
                  this.templateDatas[i].groups[j].contents[k].childList[m].controlerType.trim() == "B" ||
                  this.templateDatas[i].groups[j].contents[k].childList[m].controlerType.trim() == "BR"
                ) {
                  if (this.templateDatas[i].groups[j].contents[k].childList[m].assessListID == assessListID) {
                    this.$set(
                      this.templateDatas[i].groups[j].contents[k].childList[m],
                      "assessValue",
                      item.assessValue
                    );
                    this.$set(this.templateDatas[i].groups[j].contents[k].childList[m], "linkForm", item.linkForm);
                    return;
                  }
                }
              }
            }
          }
        }
      }
    },
    //更新按钮数量API
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: "DeliveryMaintain",
        assessListID: assessListID,
        sourceID: this.getCareMainID() + "_" + assessListID,
        sourceType: "Delivery",
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    //带入交班勾选框事件
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.nursingRecordArr[1] = flag;
    },
    //通知医师标记
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "Delivery",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.handOverArr[0]) {
            this.settingHandOver = res.data;
          }
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "DeliveryAutoInterventionToRecord",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingBringToNursingRecord = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.patient-delivery-wrap {
  height: 100%;
  .drawer-content .date-picker {
    width: 120px;
  }
  .drawer-content .time-picker {
    width: 80px;
  }
  .station-selector .label {
    margin-left: 0px;
  }
  .specific-care-view {
    background-color: #f3f3f3;
    iframe {
      height: 99%;
      border: none;
    }
  }
}
</style>
