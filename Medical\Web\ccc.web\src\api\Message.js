/*
 * FilePath     : \src\api\Message.js
 * Author       : 苏军志
 * Date         : 2020-07-18 15:25
 * LastEditors  : 苏军志
 * LastEditTime : 2020-07-19 17:54
 * Description  :MQ接口
 */

import http from "@/utils/ajax";
import { getMessageApiUrl } from "@/utils/setting";
const baseUrl = getMessageApiUrl() + "/Message";

export const urls = {
  // 生成消息队列
  GetExcharge: baseUrl + "/GetExcharge",
  // 获取未读消息
  GetNoReadNoticeListAsync: baseUrl + "/GetNoReadNoticeListAsync",
  // 获取个人消息
  GetMyNotice: baseUrl + "/GetMyNotice",
  // 更新消息列表
  UpdateMessages: baseUrl + "/UpdateMessages"
};

export const GetExcharge = params => {
  return http.get(urls.GetExcharge, params);
};
export const GetNoReadNoticeListAsync = params => {
  return http.get(urls.GetNoReadNoticeListAsync, params);
};
export const GetMyNotice = params => {
  return http.get(urls.GetMyNotice, params);
};
export const UpdateMessages = params => {
  return http.post(urls.UpdateMessages, params);
};
