/*
 * FilePath     : \ccc.web\src\api\IO.js
 * Author       : 郭自飞
 * Date         : 2020-03-22 08:19
 * LastEditors  : LX
 * LastEditTime : 2024-11-01 09:34
 * Description  : 2020-06-06:取得可以批量输出的数据
 *                2020-06-08:批量保存引流液体输出
 */
import http from "../utils/ajax";
const baseUrl = "/io";
import qs from "qs";

export const urls = {
  GetRecord: baseUrl + "/GetRecord",
  Delete: baseUrl + "/delete",
  Save: baseUrl + "/save",
  GetByID: baseUrl + "/GetByID",
  GetIoInpatient: baseUrl + "/GetIoInpatient",
  GetDocument: baseUrl + "/GetDocument",
  GetIOTotalStatistics: baseUrl + "/GetIOTotalStatistics",
  GetOutputAttributeByAssessListIDArr:
    baseUrl + "/GetOutputAttributeByAssessListIDArr",
  GetOutputAttribute: baseUrl + "/GetOutputAttribute",
  DeleteByTubeCare: baseUrl + "/DeleteByTubeCare",
  GetMultiOutputList: baseUrl + "/GetMultiOutputList",
  MultiSaveOutput: baseUrl + "/MultiSaveOutput",
  BatchSave: baseUrl + "/BatchSave",
  GetBatchRecordDrainage: baseUrl + "/GetBatchRecordDrainage",
  GetBatchRecordDrainagePDF: baseUrl + "/GetBatchRecordDrainagePDF",
  SaveDrainageOutput: baseUrl + "/SaveDrainageOutput",
  GetIOBalanceData: baseUrl + "/GetIOBalanceData",
  GetMultiHistory: baseUrl + "/GetMultiHistory",
  GetBringToNursingRecord: baseUrl + "/GetBringToNursingRecord",
  GetIntakeOutputRecord: baseUrl + "/GetIntakeOutputRecord",
  GetIntakeOutputRecordView: baseUrl + "/GetIntakeOutputRecordView",
  GetIoAllOption: baseUrl + "/GetIoAllOption",
  GetIoDrainageChart: baseUrl + "/GetIoDrainageChart",
  GetSummaryIntakeOutputViews: baseUrl + "/GetSummaryIntakeOutputViews",
  GetSummaryIntakeOutputColumns: baseUrl + "/GetSummaryIntakeOutputColumns",
};

// 获取病人的IO记录
export const GetRecord = (params) => {
  return http.get(urls.GetRecord, params);
};
// 删除病人的IO记录
export const Delete = (params) => {
  return http.post(urls.Delete, qs.stringify(params));
};
// 保存病人的IO记录
export const Save = (params) => {
  return http.post(urls.Save, params);
};
// 获取IO详细记录
export const GetByID = (params) => {
  return http.get(urls.GetByID, params);
};
// 获取IO记录单数据
export const GetDocument = (params) => {
  return http.get(urls.GetDocument, params);
};
// 获取出入量合计
export const GetIOTotalStatistics = (params) => {
  return http.get(urls.GetIOTotalStatistics, params);
};

//获取io柱状图数据
export const GetIoInpatient = (params) => {
  return http.get(urls.GetIoInpatient, params);
};

//获取气味性状
export const GetOutputAttribute = (params) => {
  return http.get(urls.GetOutputAttribute, params);
};
//获取气味性状根据IDArr
export const GetOutputAttributeByAssessListIDArr = (params) => {
  return http.post(
    urls.GetOutputAttributeByAssessListIDArr,
    qs.stringify(params)
  );
};

// 根据导管维护主表id删除IO记录
export const DeleteByTubeCare = (params) => {
  return http.post(urls.DeleteByTubeCare, qs.stringify(params));
};

//取得可以批量输出的数据
export const GetMultiOutputList = (params) => {
  return http.get(urls.GetMultiOutputList, params);
};

//批量保存引流液体输出
export const MultiSaveOutput = (params) => {
  return http.post(urls.MultiSaveOutput, params);
};
//批量录入大小便保存
// 获取病人的IO记录
export const BatchSave = (params) => {
  return http.post(urls.BatchSave, params);
};

//取得可以批量输出的引流液数据
export const GetBatchRecordDrainage = (params) => {
  return http.get(urls.GetBatchRecordDrainage, params);
};
//获取批量引流液PDF
export const GetBatchRecordDrainagePDF = (params) => {
  return http.get(urls.GetBatchRecordDrainagePDF, params);
};
//每次保存一个引流液内容BatchRecordDrainage使用
export const SaveDrainageOutput = (params) => {
  return http.post(urls.SaveDrainageOutput, params);
};
// 获取入出量平衡统计
export const GetIOBalanceData = (params) => {
  return http.get(urls.GetIOBalanceData, params);
};
//取得批量引流历史数据
export const GetMultiHistory = (params) => {
  return http.get(urls.GetMultiHistory, params);
};
//获取专项护理是否带入护理记录配置
export const GetBringToNursingRecord = (params) => {
  return http.get(urls.GetBringToNursingRecord, params);
};
export const GetIntakeOutputRecord = (params) => {
  return http.get(urls.GetIntakeOutputRecord, params);
};
//获取IO记录新增view
export const GetIntakeOutputRecordView = (params) => {
  return http.get(urls.GetIntakeOutputRecordView, params);
};
//获颜色性状气味
export const GetIoAllOption = (params) => {
  return http.get(urls.GetIoAllOption, params);
};
//获取引流量统计图数据
export const GetIoDrainageChart = (params) => {
  return http.get(urls.GetIoDrainageChart, params);
};
//获取出入量统计结果
export const GetSummaryIntakeOutputViews = (params) => {
  return http.get(urls.GetSummaryIntakeOutputViews, params);
};
//获取出入量统计所需的动态列
export const GetSummaryIntakeOutputColumns = (params) => {
  return http.get(urls.GetSummaryIntakeOutputColumns, params);
};
