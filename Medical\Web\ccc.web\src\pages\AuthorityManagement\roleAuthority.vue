<template>
  <base-layout class="role-authority-wrap" v-loading="mainloading" element-loading-text="加载中……">
    <div slot="header">
      <div class="btn">
        <el-button class="add-button" icon="el-icon-plus" @click="roleAdd()">新增</el-button>
      </div>
    </div>

    <el-table :default-sort="{ prop: 'id', order: 'descending' }" :data="roleList" stripe border height="100%">
      <el-table-column prop="id" label="序号" align="center" width="50px"></el-table-column>
      <el-table-column prop="groupName" label="角色名称" align="center"></el-table-column>
      <el-table-column prop="functionNames" label="权限" min-width="200px">
        <template slot-scope="scope">
          <el-tag effect="plain" v-for="(item, index) in scope.row.functionNames" :key="index">
            {{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="modifyPersonID" label="修改人员" align="center"></el-table-column>
      <el-table-column sortable prop="modifyDate" label="修改日期" align="center">
        <template slot-scope="scope">
          <!-- 时间格式化 -->
          <span
            v-formatTime="{
              value: scope.row.modifyDate,
              type: 'dateTime',
              format: 'yyyy-MM-dd hh:mm',
            }"
          ></span>
        </template>
      </el-table-column>
      <el-table-column label="操 作" width="70px" align="center">
        <template slot-scope="scope">
          <div class="multi_icon">
            <el-tooltip content="修改">
              <i class="iconfont icon-edit" @click="roleUpdate(scope.$index, scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="roleDelete(scope.row.id)"></i>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加角色的表单 -->
    <el-dialog
      title="添加角色"
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleRoleAdd"
      v-loading="loadingData"
      :element-loading-text="loadingText"
      custom-class="role-edit"
      width="880px"
    >
      <el-row>
        <el-col :span="12">
          <span label="" prop="id">角色编号：</span>
          <el-input v-model.number="addForm.id" placeholder="请输入非0开头的数字编号"></el-input>
        </el-col>
        <el-col :span="12">
          <span label="角色名称" prop="MenuName">请输入角色名称：</span>
          <el-input v-model="addForm.groupName" placeholder="请输入角色名称" :disabled="isInput"></el-input>
        </el-col>
      </el-row>
      <div class="fun-div">
        <el-table :data="authorityListData" border stripe @selection-change="handleSelectionChange" height="100%">
          <el-table-column type="selection" :width="convertPX(40)" align="center"></el-table-column>
          <!-- type="expand"展开项表格用 -->
          <el-table-column prop="functionName" label="权限" width="118"></el-table-column>
          <el-table-column prop="function" label="权限包含功能">
            <template slot-scope="scope">
              <el-tag effect="plain" v-for="(item, index) in scope.row.function" :key="index">
                {{ item }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button @click="dialogFormVisibleRoleAdd = false">取消</el-button>
        <el-button type="primary" :disabled="isSave" @click="saveRole()">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 修改角色的表单 -->
    <el-dialog
      title="修改角色"
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisibleRoleUpdate"
      v-loading="loadingData"
      :before-close="handleClose"
      :element-loading-text="loadingText"
      custom-class="role-edit"
      width="880px"
    >
      <el-row class="my-test">
        <el-col :span="12">
          <span prop="id">角色编号：</span>
          <el-input v-model="editForm.id" :disabled="true"></el-input>
        </el-col>
        <el-col :span="12">
          <span prop="groupName">角色名称：</span>
          <el-input v-model="editForm.groupName"></el-input>
        </el-col>
      </el-row>
      <div class="fun-div">
        <el-table
          ref="updateIds"
          :data="authorityListData"
          row-key="id"
          @selection-change="selectAuthId"
          border
          stripe
          height="100%"
        >
          <el-table-column
            type="selection"
            :reserve-selection="true"
            :width="convertPX(40)"
            align="center"
          ></el-table-column>
          <el-table-column prop="functionName" label="权限" width="118"></el-table-column>
          <el-table-column prop="function" label="系统功能菜单">
            <template slot-scope="scope">
              <el-tag effect="plain" v-for="(item, index) in scope.row.function" :key="index">
                {{ item }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :disabled="isSave" @click="saveEditForm()">确 定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import {
  GetRoleList,
  DeleteRoleById,
  UpdataRoleById,
  AddRole,
  GetFunctionIDByRoleId,
  GetAllRoleID,
} from "@/api/Authority";
import baseLayout from "@/components/BaseLayout";
import { GetMenuTree } from "@/api/GetAuthorityList";

export default {
  ioRecord: {},
  components: {
    baseLayout,
  },
  data() {
    return {
      //表格数据
      roleList: [],
      //新增弹出框是否启用
      dialogFormVisibleRoleAdd: false,
      //新增数据弹窗绑定用(Model)
      addForm: {
        id: "",
        groupName: "",
      },
      //修改数据弹窗绑定用(Model)
      editForm: {
        id: "",
        groupName: "",
      },
      mainloading: false,
      //修改弹出框是否启用
      dialogFormVisibleRoleUpdate: false,
      loadingData: false,
      loadingText: "加载中……",
      // 验证规则
      rules: {
        id: [
          { required: true, message: "请输入角色编号", trigger: "blur" },
          {
            type: "number",
            required: true,
            message: "角色编码必须为数值",
            trigger: "blur",
          },
        ],
        groupName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
          {
            min: 1,
            max: 100,
            message: "长度在 1 到 100 个字符",
            trigger: "blur",
          },
        ],
      },
      // 树形结构
      authorityListData: [],
      // 确认保存按钮是否禁用
      isSave: false,
      isInput: false,
      authIDs: [],
      ids: [],
      defaultProps: [],
      allRoleID: [],
    };
  },
  created() {
    //钩子进入页面时，获取角色列表
    this.getRoleList();
    this.getAllRoleID();
  },
  methods: {
    selectAuthId(val) {
      this.authIDs = val.map((v) => {
        return v.id;
      });
    },
    handleSelectionChange(val) {
      this.ids = [];
      //val 数组 包含选中对象
      for (let i = 0; i < val.length; i++) {
        this.ids.push(val[i].id);
      }
    },
    //获取角色列表
    getRoleList() {
      this.mainloading = true;
      GetRoleList().then((result) => {
        this.mainloading = false;
        if (this._common.isSuccess(result)) {
          this.roleList = result.data;
        }
      });
    },

    // 打开新增
    roleAdd() {
      this.dialogFormVisibleRoleAdd = true;
      this.loadingData = true;
      this.loadingText = "加载中……";
      //每次打开时为空
      this.addForm.id = undefined;
      this.addForm.groupName = undefined;
      this.authIDs = [];
      GetMenuTree().then((result) => {
        this.loadingData = false;
        if (this._common.isSuccess(result)) {
          this.authorityListData = result.data;
        }
      });
    },

    // 保存新增
    saveRole() {
      this.authIDs = this.authorityListData.id;
      if (!this.addForm.id) {
        this._showTip("warning", "请输入角色编号");
        return;
      }
      if (this.allRoleID.findIndex((item) => item == this.addForm.id) > -1) {
        this._showTip("warning", "角色编号重复");
        return;
      }
      if (this.roleList.findIndex((item) => item.groupName == this.addForm.groupName) > -1) {
        this._showTip("warning", "角色名称重复");
        return;
      }
      if (!this.addForm.groupName) {
        this._showTip("warning", "请输入角色名称");
        return;
      }
      let params = {};
      params.roleID = this.addForm.id;
      params.roleName = this.addForm.groupName;

      params.authorityIDs = this.ids;
      if (!this.roleNameValidate(params)) {
        return;
      }
      this.loadingData = true;
      this.loadingText = "保存中……";
      AddRole(params).then((result) => {
        this.loadingData = false;
        if (this._common.isSuccess(result)) {
          this.dialogFormVisibleRoleAdd = false;
          this.getRoleList();
          this._showTip("success", "保存成功！");
        }
      });
    },

    // 打开修改
    async roleUpdate(index, row) {
      this.authorityListData = [];
      this.loadingData = true;
      this.loadingText = "保存中……";
      this.defaultProps = [];
      await GetMenuTree().then((result) => {
        this.loadingData = false;
        if (this._common.isSuccess(result)) {
          this.dialogFormVisibleRoleUpdate = true;
          this.authorityListData = result.data;
        }
      });

      // 修改数据绑定
      this.editForm = Object.assign({}, row);
      let params = { RoleID: row.id };
      await GetFunctionIDByRoleId(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.defaultProps = result.data;
          for (let i = 0; i < this.authorityListData.length; i++) {
            for (let j = 0; j < this.defaultProps.length; j++) {
              if (this.defaultProps[j] == this.authorityListData[i].id) {
                this.$refs.updateIds.toggleRowSelection(this.authorityListData[i], true);
              }
            }
          }
        }
      });
    },

    //保存修改
    saveEditForm() {
      let authList = this.authIDs == undefined ? [] : this.authIDs;
      let params = {};
      params.roleID = this.editForm.id;
      params.roleName = this.editForm.groupName;
      params.authorityIDs = authList;
      if (!this.roleNameValidate(params)) {
        return;
      }
      UpdataRoleById(params).then((result) => {
        this.handleClose();
        if (this._common.isSuccess(result)) {
          this.getRoleList();
          this._showTip("success", "修改成功！");
        }
      });
    },
    // 角色信息#删除
    roleDelete(id) {
      this._deleteConfirm("", (flag) => {
        let _this = this;
        if (flag) {
          this.loading = true;
          let params = {
            id: id,
          };
          DeleteRoleById(params).then((result) => {
            if (this._common.isSuccess(result)) {
              _this.getRoleList();
              this._showTip("success", "删除成功！");
            }
          });
        }
      });
    },
    roleNameValidate(params) {
      if (params.roleName.length > 15) {
        this._showTip("warning", "角色名称过长！");
        return false;
      }
      return true;
    },
    handleClose() {
      this.$refs.updateIds.clearSelection();
      this.dialogFormVisibleRoleUpdate = false;
    },
    getAllRoleID() {
      GetAllRoleID().then((result) => {
        if (this._common.isSuccess(result)) {
          this.allRoleID = result.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.role-authority-wrap {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  .btn {
    float: right;
  }
  .el-dialog.role-edit {
    .el-row {
      margin: 8px 0 15px 0;
      .el-col .el-input {
        width: 200px;
      }
    }
    .el-tag {
      margin: 4px;
    }
    .fun-div {
      height: calc(100% - 55px);
    }
  }
  .el-tag {
    margin: 3px;
  }
}
</style>
