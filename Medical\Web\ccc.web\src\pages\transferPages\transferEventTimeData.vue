<!--
 * FilePath     : \src\pages\transferPages\transferEventTimeData.vue
 * Author       : 曹恩
 * Date         : 2023-06-22 10:58
 * LastEditors  : 张现忠
 * LastEditTime : 2023-11-22 09:27
 * Description  : 护士工作量统计跳转
 * CodeIterationRecord: 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getStatisticsUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
      user: "getUser",
    }),
  },
  watch: {
    // 单页面多路由
    $route: function (to, from) {
      if (to.path == from.path) {
        this.initPage(to.query.dataSetType);
      }
    },
  },
  created() {
    this.initPage(this.$route.query.dataSetType);
  },
  methods: {
    /**
     * description: 初始化请求地址
     * return {*}
     * param {*} statisticsType
     */
    initPage(dataSetType) {
      if (!dataSetType) {
        this._showTip("warning", "缺少dataSetType参数，请确认菜单配置！");
        return;
      }
      this.url =
        getStatisticsUrl() +
        "eventTimeData?dataSetType=" +
        this.$route.query.dataSetType +
        "&token=" +
        this.token +
        "&hospitalID=" +
        this.hospitalInfo.hospitalID +
        "&stationID=" +
        this.user.stationID +
        "&empID=" +
        this.user.userID;
      console.log(this.url);
    },
  },
};
</script>
<style lang="scss"></style>