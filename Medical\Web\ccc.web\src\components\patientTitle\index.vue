<!--
 * FilePath     : \src\components\patientTitle\index.vue
 * Author       : 苏军志
 * Date         : 2022-05-23 11:19
 * LastEditors  : 苏军志
 * LastEditTime : 2024-08-22 16:48
 * Description  : 病人头组件
 * CodeIterationRecord: 
-->

<template>
  <div v-loading.fullscreen.lock="loading" element-loading-text="加载中……" class="patient-title">
    <div class="left-property">
      <div class="gender-img-wrap">
        <img class="gender-img" :src="genderImg" @dblclick="openEmr" />
        <span v-if="infectiousDiseaseFlag" :style="{ color: infectiousDiseaseColor }">
          {{ infectiousDiseaseFlag }}
        </span>
      </div>
      <div :class="['bed-number-wrap', { 'is-disabled': disabled }]">
        <input
          class="bed-number"
          v-model="bedNumber"
          type="text"
          :disabled="disabled"
          :style="{ width: convertPX(bedNumberWidth) + 'px' }"
          @keyup.enter="getPatientInfo()"
          @blur="getPatientInfo('blur')"
        />
        <span class="bed-label">床</span>
        <el-popover
          v-if="!disabled && attendancePatientList && attendancePatientList.length > 0"
          v-model="showPatientList"
          width="auto"
          trigger="click"
          popper-class="component-patient-list"
        >
          <el-radio-group v-model="bedNumber" @change="selectPatient">
            <el-radio v-for="(patient, index) in attendancePatientList" :key="index" :label="patient.bedNumber">
              {{ patient.bedNumber }}床|{{ patient.patientName }}
            </el-radio>
          </el-radio-group>
          <i class="iconfont icon-switch attendance-patient-switch" slot="reference" />
        </el-popover>
      </div>
    </div>
    <div class="right-property">
      <el-table class="patient-table" :data="patientTableData" :show-header="false">
        <el-table-column v-for="column in patientTableColumns" :key="column.index" :width="column.width">
          <template slot-scope="scope">
            <div
              :class="scope.row[column.index].isLabel ? 'label-cell' : 'property-cell'"
              :title="scope.row[column.index].isLabel ? '' : scope.row[column.index].content"
            >
              <template v-if="scope.row[column.index].isLabel">
                <el-tooltip v-if="scope.row[column.index].tip" :content="scope.row[column.index].tip">
                  <div
                    v-if="scope.row[column.index].clickEvent"
                    @click="handleEvent(scope.row[column.index].clickEvent)"
                  >
                    {{ scope.row[column.index].content }}：
                  </div>
                  <div v-else class="label">{{ scope.row[column.index].content }}：</div>
                </el-tooltip>
                <div v-else class="label">{{ scope.row[column.index].content }}：</div>
              </template>
              <div v-else class="property">{{ scope.row[column.index].content }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      :visible.sync="openEmrDialog"
      fullscreen
      :close-on-click-modal="false"
      title="患者病历查询"
      custom-class="no-footer"
    >
      <document-page isComponent></document-page>
    </el-dialog>
  </div>
</template>
<script>
import { GetPatientDataByBedNumber, GetPatientBasicDataByInpatientID } from "@/api/Inpatient";
import { UpdatePatientDiagonsisByCaseNumber } from "@/api/Diagnosis";
import { GetPatientList } from "@/api/Attendance";
import { mapGetters } from "vuex";
import documentPage from "@/pages/document";

export default {
  components: {
    documentPage,
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    isRefresh: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      // 住院序号
      inpatientID: "",
      // 出院标记
      dischargeFlag: false,
      // 床号
      bedNumber: "",
      // 病区序号
      stationID: "",
      // 刷新标记
      refreshFlag: false,
      // 性别图标
      genderImg: "",
      // 传染病标记
      infectiousDiseaseFlag: undefined,
      // 传染病标记颜色
      infectiousDiseaseColor: undefined,
      // 派班病人清单
      attendancePatientList: undefined,
      // 是否显示派班病人清单
      showPatientList: false,
      // 病人表格数据
      patientTableData: [],
      // 打开病历对话框
      openEmrDialog: false,
    };
  },
  computed: {
    ...mapGetters({
      currentPatient: "getCurrentPatient",
      patientInfo: "getPatientInfo",
      user: "getUser",
      hospitalInfo: "getHospitalInfo",
      bedNumberWidth: "getBedNumberWidth",
    }),
    patientTittle() {
      return this.$t("patientTittle");
    },
    patientTableColumns() {
      return [
        { index: 1, width: this.convertPX(98) },
        { index: 2, width: "auto" },
        { index: 3, width: this.convertPX(90) },
        { index: 4, width: this.convertPX(90) },
        { index: 5, width: this.convertPX(120) },
        { index: 6, width: this.convertPX(140) },
        { index: 7, width: this.convertPX(120) },
        { index: 8, width: this.convertPX(140) },
        { index: 9, width: this.convertPX(75) },
        { index: 10, width: "auto" },
        { index: 11, width: this.convertPX(120) },
        { index: 12, width: this.convertPX(90) },
      ];
    },
  },
  watch: {
    isRefresh(newVal) {
      this.refreshFlag = true;
      this.init();
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    /**
     * description: 初始化数据
     * param {*}
     * return {*}
     */
    async init() {
      if (!this.$route.query.noToken) {
        this.getPatientList();
      }
      if (this.$route.query.inpatientID && this.$route.query.isDischarge) {
        this.dischargeFlag = this.$route.query.isDischarge;
        this.inpatientID = this.$route.query.inpatientID;
      } else {
        this.inpatientID = undefined;
        this.dischargeFlag = false;
        if (this.$route.query.noToken) {
          this.bedNumber = this.$route.query.bedNumber;
          this.stationID = this.$route.query.stationID;
        } else {
          if (this.currentPatient) {
            this.bedNumber = this.currentPatient.bedNumber;
            this.stationID = this.currentPatient.stationID;
          }
          if (this.$route.query.bedNumber) {
            this.bedNumber = this.$route.query.bedNumber;
          }
        }
      }
      await this.getPatientInfo();
    },
    /**
     * description: 获取派班患者清单
     * param {*}
     * return {*}
     */
    getPatientList() {
      let parms = {
        nurseID: this.user.userID,
      };
      GetPatientList(parms).then((res) => {
        if (this._common.isSuccess(res)) {
          this.attendancePatientList = res.data;
        }
      });
    },
    /**
     * description: 获取患者信息
     * param {*} eventType 解决回车后失焦二次获取病人信息问题
     * return {*}
     */
    async getPatientInfo(eventType) {
      let params = {};
      // 如果inpatientID有值，说明是出院患者
      if (this.inpatientID) {
        if (this.patientInfo && this.patientInfo.inpatientID == this.inpatientID && !this.refreshFlag) {
          this.bedNumber = this.patientInfo.bedNumber;
          this.setPatientTableData(this.patientInfo);
          return;
        }
        // 出院病人 根据inpatientID发出请求；
        params.inpatientID = this.inpatientID;
        this.bedNumber = "";
        this.loading = true;
        await GetPatientBasicDataByInpatientID(params).then((result) => {
          this.loading = false;
          if (this._common.isSuccess(result)) {
            this.refreshFlag = false;
            this.bedNumber = result.data.bedNumber;
            this.setPatientTableData(result.data);
            this.$store.commit("session/setPatientInfo", result.data);
          } else {
            this.$store.commit("session/setPatientInfo", undefined);
          }
        });
      } else {
        //解决回车后失焦二次获取病人信息问题
        if (eventType == "blur" && this.patientInfo?.bedNumber == this.bedNumber) {
          return;
        }
        // 在院患者根据床号发出请求；
        if (this.bedNumber == "") {
          this._showTip("error", this.patientTittle.bedNumberTip);
          this.$store.commit("session/setPatientInfo", undefined);
          return;
        }
        if (
          this.patientInfo &&
          this.stationID &&
          this.patientInfo.bedNumber == this.bedNumber &&
          this.patientInfo.stationID == this.stationID &&
          this.patientInfo.dischargeFlag &&
          !this.refreshFlag
        ) {
          this.setPatientTableData(this.patientInfo);
          return;
        }
        if (this.refreshFlag && this.patientInfo) {
          params.bedNumber = this.patientInfo.bedNumber;
        } else {
          params.bedNumber = this.bedNumber;
          params.stationID = this.stationID;
        }
        if (this.$route.query.noToken) {
          params.hospitalID = this.$route.query.hospitalID;
          params.language = this.$route.query.language;
        }
        if (!params || !params.bedNumber) {
          return;
        }
        this.loading = true;
        await GetPatientDataByBedNumber(params).then((result) => {
          this.loading = false;
          if (this._common.isSuccess(result)) {
            this.refreshFlag = false;
            this.bedNumber = result.data.bedNumber;
            this.setPatientTableData(result.data);
            this.$store.commit("session/setPatientInfo", result.data);
            let currentPatient = {
              bedNumber: result.data.bedNumber,
              inpatientID: result.data.inpatientID,
              stationID: result.data.stationID,
              caseNumber: result.data.caseNumber,
              chartNo: result.data.chartNo,
              admissionDate: result.data.admissionDate,
              localCaseNumber: result.data.localCaseNumber,
              departmentCode: result.data.departmentCode,
            };
            this.$store.commit("session/setCurrentPatient", currentPatient);
          }
        });
      }
    },

    /**
     * description: 设置病人表格数据
     * param {*} patient 患者信息
     * return {*}
     */
    setPatientTableData(patient) {
      this.patientTableData = [];
      //病人头像处理
      if (patient.genderCode == "1") {
        this.genderImg = require("./manImg.png");
      } else {
        this.genderImg = require("./womanImg.png");
      }
      // 模拟传染病标记
      this.infectiousDiseaseFlag = patient.infectiousDiseaseFlag;
      this.infectiousDiseaseColor = patient.infectiousDiseaseColor;
      // 组第一行数据
      let rowOne = {
        // 患者姓名
        1: {
          content: this.patientTittle.patientName,
          isLabel: true,
        },
        2: {
          content: patient.patientName,
          isLabel: false,
        },
        // 性别
        3: {
          content: this.patientTittle.gender,
          isLabel: true,
        },
        4: {
          content: patient.gender,
          isLabel: false,
        },
        // 住院号
        5: {
          content: this.patientTittle.localCaseNumber,
          isLabel: true,
        },
        6: {
          content: patient.localCaseNumber,
          isLabel: false,
        },
        // 护理级别
        7: {
          content: this.patientTittle.nursingLevel,
          isLabel: true,
        },
        8: {
          content: patient.nursingLevel,
          isLabel: false,
        },
        // 诊断
        9: {
          content: this.patientTittle.diagnosis,
          isLabel: true,
          tip: this.hospitalInfo.hospitalID == "3" ? this.patientTittle.refreshDiagnostics : "",
          clickEvent: this.hospitalInfo.hospitalID == "3" ? "refreshDiagnose" : "",
        },
        10: {
          content: patient.diagnose,
          isLabel: false,
        },
        // 主治医师
        11: {
          content: this.patientTittle.attendingDoctor,
          isLabel: true,
        },
        12: {
          content: patient.physicianName,
          isLabel: false,
        },
      };
      this.patientTableData.push(rowOne);
      // 组第二行数据
      let rowTwo = {
        // 过敏史
        1: {
          content: this.patientTittle.allergicHistory,
          isLabel: true,
        },
        2: {
          content: patient.drugAllergy,
          isLabel: false,
        },
        // 年龄
        3: {
          content: this.patientTittle.age,
          isLabel: true,
        },
        4: {
          content: patient.ageDetail,
          isLabel: false,
        },
        // 出生日期
        5: {
          content: this.patientTittle.birthday,
          isLabel: true,
        },
        6: {
          content: this._datetimeUtil.formatDate(patient.dateOfBirth, "yyyy-MM-dd"),
          isLabel: false,
        },
        // 入院时间
        7: {
          content: this.patientTittle.admissionTime,
          isLabel: true,
        },
        8: {
          content: this._datetimeUtil.formatDate(patient.admissionDate, "yyyy-MM-dd"),
          isLabel: false,
        },
        // 主诉
        9: {
          content: this.patientTittle.chiefComplaint,
          isLabel: true,
        },
        10: {
          content: patient.chiefComplaint,
          isLabel: false,
        },
        // 主责护士
        11: {
          content: this.patientTittle.principalNurse,
          isLabel: true,
        },
        12: {
          content: patient.careNurse,
          isLabel: false,
        },
      };
      this.patientTableData.push(rowTwo);
    },
    /**
     * description: 切换派班患者
     * param {*}
     * return {*}
     */
    selectPatient() {
      this.showPatientList = false;
      this.getPatientInfo();
    },
    /**
     * description: 动态单击事件
     * param {*}
     * return {*}
     */
    handleEvent(func, params) {
      this[func](params);
    },
    /**
     * description: 刷新诊断，目前只银川 在使用
     * param {*}
     * return {*}
     */
    //
    refreshDiagnose() {
      if (this.hospitalInfo.hospitalID != "3") {
        return;
      }
      this.loading = true;
      let params = {
        hospitalID: this.hospitalInfo.hospitalID,
        caseNumber: this.patientInfo.caseNumber,
      };
      this.loading = false;
      UpdatePatientDiagonsisByCaseNumber(params).then(async (result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          // 更新患者信息
          this.refreshFlag = true;
          await this.getPatientInfo();
          this._showTip("success", "更新成功！");
        }
      });
    },
    openEmr() {
      this.openEmrDialog = true;
    },
  },
};
</script>
<style lang="scss">
.patient-title {
  width: 100%;
  height: 60px;
  display: flex;
  .left-property {
    display: flex;
    line-height: 16px;
    .gender-img-wrap {
      width: 40px;
      text-align: center;
      margin: 2px 6px 0 -4px;
      box-sizing: border-box;
      .gender-img {
        width: 40px;
        height: 40px;
        cursor: pointer;
      }
    }
    .bed-number-wrap {
      display: flex;
      height: 40px;
      margin-right: 10px;
      background-color: $base-color;
      &.is-disabled {
        background-color: #d3d3d3;
        .bed-number,
        .bed-label {
          color: $base-color;
          font-weight: bold;
        }
      }
      .bed-number {
        width: 100px;
        border: 0;
        font-size: 24px;
        color: #ffffff;
        padding: 3px;
        text-align: center;
        background-color: transparent;
        &:focus {
          outline: 0;
        }
      }
      .bed-label {
        font-size: 24px;
        color: #ffffff;
        padding: 15px 5px 5px 5px;
        box-sizing: border-box;
      }
      .attendance-patient-switch {
        margin: -1px -5px 0 3px;
        padding-top: 14px;
        font-size: 30px;
        background-color: #ffffff;
        height: 40px;
      }
    }
  }
  .right-property {
    flex: auto;
    width: 100%;
    overflow-x: auto;
    .patient-table {
      border: 0;
      border-right: 1px solid #cccccc;
      td {
        border-bottom: 1px solid #cccccc;
        padding: 0;
        .cell {
          font-size: 16px !important;
          padding: 0 !important;
          div {
            padding: 1px 3px;
          }
          .label-cell {
            color: #ffffff;
            background-color: $base-color;
            text-align-last: justify;
            text-align: justify;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .property-cell {
            color: #000000;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
.component-patient-list {
  padding: 5px;
  .el-radio {
    display: block;
    margin: 5px;
  }
}
</style>
