<!--
 * FilePath     : \src\autoPages\handover\shiftHandover\index.vue
 * Author       : 来江禹
 * Date         : 2023-07-25 15:31
 * LastEditors  : 马超
 * LastEditTime : 2025-06-15 15:49
 * Description  : 班别交班，班内交班修改页面（新版补录页面，病人接班页面可进入当前页面）
 * CodeIterationRecord: 
-->

<template>
  <specific-care
    class="handover-shift"
    drawerSize="80%"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :recordTitleSlotFalg="true"
    :previewFlag="saveButtonShowFlag"
    :recordAddFlag="false"
    @save="shiftHandoverSave"
    @cancel="drawerClose"
    v-loading="loading"
    element-loading-text="加载中……"
    v-model="showTemplateFlag"
  >
    <div slot="record-title">
      <span class="title-span">
        交班类别
        <el-select
          class="handover-record-select"
          placeholder="请选择交班类别"
          v-model="chooseRecordCode"
          @change="getShiftHandoverList()"
        >
          <el-option
            v-for="item in handoverCodeSelectList"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          ></el-option>
        </el-select>
      </span>
      <span class="title-span">
        开始日期：
        <el-date-picker
          class="header-date"
          v-model="startDate"
          type="date"
          placeholder="选择开始日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="getShiftHandoverList()"
        ></el-date-picker>
      </span>
      <span class="title-span">
        结束日期:
        <el-date-picker
          class="header-date"
          v-model="endDate"
          type="date"
          placeholder="选择结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          @change="getShiftHandoverList()"
        ></el-date-picker>
      </span>
    </div>
    <div slot="main-record">
      <el-table
        class="main-table"
        ref="handoverShiftTable"
        :data="handoverTableData"
        :span-method="tableSpanMethod"
        height="100%"
        border
        stripe
      >
        <el-table-column prop="recordsName" label="交班类型" :width="convertPX(100)" align="center"></el-table-column>
        <el-table-column prop="handoverType" label="交接班" :width="convertPX(100)" align="center">
          <template slot-scope="scope">{{ scope.row.handoverType == "HandOff" ? "交班" : "接班" }}</template>
        </el-table-column>
        <el-table-column prop="stationName" label="病区" :width="convertPX(100)" align="center"></el-table-column>
        <el-table-column prop="handoverDate" label="日期" :width="convertPX(170)" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.handoverDate, type: 'date' }"></span>
            <span v-formatTime="{ value: scope.row.handoverTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="S-状况" :min-width="convertPX(60)">
          <template slot-scope="scope">
            <span v-html="scope.row.situation"></span>
          </template>
        </el-table-column>
        <el-table-column label="B-背景" :min-width="convertPX(60)">
          <template slot-scope="scope">
            <span v-html="scope.row.background"></span>
          </template>
        </el-table-column>
        <el-table-column label="A-评估" :min-width="convertPX(60)">
          <template slot-scope="scope">
            <span v-html="scope.row.assement"></span>
          </template>
        </el-table-column>
        <el-table-column label="R-建议" :min-width="convertPX(60)">
          <template slot-scope="scope">
            <span v-html="scope.row.recommendation"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="handoverNurseName"
          label="交接护士"
          :width="convertPX(120)"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" :width="convertPX(70)" align="center">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.handoverNurseName" content="修改">
              <div class="iconfont icon-edit" @click="handoverEdit(scope.row)"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading.fullscreen.lock="layoutLoading"
      :element-loading-text="layoutLoadingText"
    >
      <div slot="header">
        <span>日期:</span>
        <el-date-picker
          v-model="shiftHandoverDate"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          :clearable="false"
        ></el-date-picker>
        <span>时间:</span>
        <el-time-picker
          v-model="shiftHandoverTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        ></el-time-picker>
        <template v-if="!!refillFlag">
          <station-selector v-model="handoverStationID" :inpatientID="patientData.inpatientID" />
          <dept-selector v-model="handoverDepartmentID" :stationID="handoverStationID" />
        </template>
      </div>
      <div class="drawer-text">
        <handover-rich-text ref="handover" v-model="handoverText" />
      </div>
    </base-layout>
  </specific-care>
</template>
<script>
import specificCare from "@/components/specificCare";
import baseLayout from "@/components/BaseLayout";
import handoverRichText from "@/autoPages/handover/components/HandoverRichText";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import { mapGetters } from "vuex";
import { GetHandOverType } from "@/api/Handover/HandoverCommonUse";
import { GetShiftHandOver, SaveShiftHandOver } from "@/api/Handover";
import { GetShitHandoverSBAR } from "@/api/Handover/MultipleShifHandover";
export default {
  components: {
    specificCare,
    baseLayout,
    handoverRichText,
    stationSelector,
    deptSelector,
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      user: "getUser",
    }),
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    handoverData: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      drawerTitle: "班别交接",
      //专项组件-主记录维护记录开关
      showRecordArr: [true, false],
      //抽屉保存按钮开关
      saveButtonShowFlag: false,
      //页面加载开关
      loading: false,
      //交班类别下拉框数据
      handoverCodeSelectList: [],
      //病人数据
      patientData: undefined,
      //补录标记
      refillFlag: undefined,
      //选择的交班类别Code
      chooseRecordCode: "ShiftHandover",
      //交接班记录表格数据
      handoverTableData: [],
      //抽屉加载开关
      layoutLoading: false,
      //抽屉加载文本内容
      layoutLoadingText: undefined,
      //抽屉SBAR内容
      handoverText: {},
      //查询数据开始日期
      startDate: "",
      //查询数据结束日期
      endDate: "",
      //获取相同数据表格合并行数与下标数组
      spanArr: [],
      //选择的当前数据
      currentHandover: undefined,
      //抽屉交接班时间
      shiftHandoverDate: undefined,
      //抽屉交接班日期
      shiftHandoverTime: undefined,
      //抽屉交接病区
      handoverStationID: undefined,
      //抽屉交接科室
      handoverDepartmentID: undefined,
      //弹窗开关
      showTemplateFlag: false,
    };
  },
  watch: {
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patientData = this.patientInfo;
        }
      },
      immediate: true,
    },
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patientData = this.supplemnentPatient;
          this.refillFlag = "*";
        }
      },
      immediate: true,
    },
    "patientData.inpatientID": {
      handler(newVal) {
        newVal && this.init();
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 初始化函数
     * return {*}
     */
    async init() {
      await this.getHandoverType();
      //从病人接班页面跳转优先赋值，不是跳转初始化数据(数据时间区间默认两天数据)
      this.endDate = this.handoverData?.endDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.startDate = this.handoverData?.startDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.chooseRecordCode = this.handoverData?.handoverType ?? "ShiftHandover";
      await this.getShiftHandoverList();
      this.goPageToDrawer();
    },
    /**
     * description: 病人接班跳转页面，打开弹窗
     * return {*}
     */
    goPageToDrawer() {
      if (!this.handoverData) {
        return;
      }
      let handoverID = this.handoverData?.handoverID;
      if (!handoverID) {
        this._showTip("warning", "跳转页面失败，请重新跳转！");
        return;
      }
      //获取交班还是接班数据
      let handoverClass = this.handoverData?.handoverClass;
      if (!handoverClass) {
        this._showTip("warning", "跳转页面失败，请重新跳转！");
        return;
      }
      let editHandover = this.handoverTableData.find(
        (handover) =>
          handover.handoverID == handoverID && (this.handoverData.handonFlag || handover.handoverClass == handoverClass)
      );
      editHandover && this.handoverEdit(editHandover);
    },
    /**
     * description: 获取班别班内交接班配置，交班类别下拉框数
     * return {*}
     */
    async getHandoverType() {
      let param = {
        typeValue: "HandOverShift",
      };
      await GetHandOverType(param).then((res) => {
        if (this._common.isSuccess(res)) {
          this.handoverCodeSelectList = res.data.childrenItem;
        }
      });
    },
    /**
     * description: 获取交接班表格数据
     * return {*}
     */
    async getShiftHandoverList() {
      let params = {
        inpatientID: this.patientData.inpatientID,
        handOverType: "HandOverShift",
        startDate: this.startDate,
        endDate: this.endDate,
        recordCode: this.chooseRecordCode,
      };
      this.loading = true;
      await GetShiftHandOver(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverTableData = res.data;
          this.getSpanArr(res.data);
          this.$nextTick(() => {
            this.$refs.handoverShiftTable.doLayout();
          });
        }
      });
    },
    /**
     * description: 关闭弹窗，没有保存操作不重新获取数据
     * return {*}
     */
    drawerClose() {
      this.openOrCloseDrawer(false);
      this.saveFlag && this.getShiftHandoverList();
    },
    /**
     * description: 弹窗开关
     * param {*} flag
     * param {*} title
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 编辑按钮打开抽屉，赋值数据
     * param {*} handover
     * return {*}
     */
    async handoverEdit(handover) {
      if (!handover.handoverNurseName) {
        this._showTip("warning", "尚未接班，请接班！");
        return;
      }
      this.saveFlag = false;
      this.currentHandover = handover ?? undefined;
      this.shiftHandoverDate = handover?.handoverDate ?? this._datetimeUtil.getNowDate();
      this.shiftHandoverTime = handover?.handoverTime ?? this._datetimeUtil.getNowTime("hh:mm");
      this.handoverStationID = handover?.stationID ?? this.patientData.stationID;
      this.handoverDepartmentID = handover?.departmentListID ?? this.patientData.departmentListID;
      this.drawerTitle = this.chooseRecordCode == "ShiftHandover" ? "班别交班" : "班内交班";
      //仅交接班护士可以保存修改，其他护士尽可查看
      if (this.user.userID != handover.handoverNurse) {
        this.saveButtonShowFlag = true;
      } else {
        this.saveButtonShowFlag = false;
      }
      if (this.refillFlag && this.refillFlag === "*") {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,handover.handoverNurse)
        this.saveButtonShowFlag = saveButtonFlag;
      }
      this.openOrCloseDrawer(true, this.drawerTitle);
      this.initHandover();
    },
    /**
     * description: 获取抽屉SBAR数据
     * return {*}
     */
    async initHandover() {
      let params = {
        handoverID: this.currentHandover.handoverID,
        handoverClass: this.currentHandover.handoverType,
      };
      this.layoutLoading = true;
      this.layoutLoadingText = "加载中……";
      GetShitHandoverSBAR(params).then((res) => {
        this.layoutLoading = false;
        if (this._common.isSuccess(res)) {
          this.handoverText = res.data?.sbarData ?? {};
        }
      });
    },
    /**
     * description: 获取保存View
     * return {*}
     */
    createSaveView() {
      let saveView = this._common.clone(this.currentHandover);
      saveView.stationID = this.handoverStationID;
      saveView.departmentListID = this.handoverDepartmentID;
      saveView.handoverDate = this.shiftHandoverDate;
      saveView.handoverTime = this.shiftHandoverTime;
      saveView.situation = this.handoverText.situation.value;
      saveView.background = this.handoverText.background.value;
      saveView.assement = this.handoverText.assement.value;
      saveView.recommendation = this.handoverText.recommendation.value;
      saveView.refillFlag = this.refillFlag;
      return saveView;
    },
    /**
     * description: 保存数据
     * return {*}
     */
    shiftHandoverSave() {
      let params = this.createSaveView();
      this.layoutLoading = true;
      this.layoutLoadingText = "保存中……";
      SaveShiftHandOver(params).then((res) => {
        this.layoutLoading = false;
        this.saveFlag = true;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功！");
          this.drawerClose();
        } else {
          this._showTip("warning", "保存失败！");
        }
      });
    },
    /**
     * description: 表格合并数据
     * param {*} data
     * return {*}
     */
    getSpanArr(data) {
      if (!data?.length) {
        return;
      }
      this.spanArr = [];
      let pos = 0;
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].handoverID == data[i - 1].handoverID) {
            this.spanArr[pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            pos = i;
          }
        }
      }
    },
    /**
     * description: 表格合并
     * param {*} row
     * param {*} column
     * param {*} rowIndex
     * param {*} columnIndex
     * return {*}
     */
    tableSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        //相同交接班数据合并
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
  },
};
</script>
<style lang="scss">
.handover-shift {
  .handover-record-select {
    margin: 5px 10px;
    width: 160px;
  }
  .title-span {
    font-weight: 100;
    font-size: 18px;
    .header-date {
      width: 140px;
    }
  }
  .main-table {
    margin: 10px 0px 0px;
  }
  .drawer-text {
    height: 100%;
  }
}
</style>
