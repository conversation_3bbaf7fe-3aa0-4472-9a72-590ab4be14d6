<!--
 * FilePath     : \src\pages\recordSupplement\assessRecordOutHospital\index.vue
 * Author       : 李青原
 * Date         : 2020-06-30 17:27
 * LastEditors  : 张现忠
 * LastEditTime : 2023-04-15 10:06
 * Description  : 补评估记录页面(出院病人)
--> 
<template>
  <div class="assess-record" v-loading="loading">
    <el-table border :data="assessRecord" highlight-current-row height="100%" v-if="assessRecord.length > 0">
      <el-table-column prop="recordsCode" header-align="center" label="评估名称" resizable></el-table-column>
      <el-table-column prop="sort" label="次数" resizable align="center"></el-table-column>
      <el-table-column label="评估日期" resizable align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.assessDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="assessTime" label="评估时间" resizable align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.assessTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="是否编辑" resizable align="center">
        <template slot-scope="scope">
          <i v-if="scope.row.tempSaveMark == 'T'" class="iconfont icon-check-mark"></i>
        </template>
      </el-table-column>
      <el-table-column prop="operation" label="操作" resizable align="center">
        <template slot-scope="scope">
          <router-link
            :to="{
              name: 'assessContent',
              query: {
                mainID: scope.row.id,
                bedNumber: scope.row.bedNumber,
                sort: scope.row.sort,
                assessMain: scope.row,
              },
            }"
          >
            <el-tooltip content="编辑">
              <i class="iconfont icon-edit"></i>
            </el-tooltip>
          </router-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- </div> -->
  </div>
</template>

<script>
import { GetMainByInpatient } from "@/api/Assess";
import baseLayout from "@/components/BaseLayout";
export default {
  components: {
    baseLayout,
  },
  props: {
    patient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  watch: {
    patient: {
      handler(newVal) {
        if (newVal) {
          this.getAssessRecord(newVal);
        } else {
          this.assessRecord = [];
        }
      },
    },
  },
  created() {
    this.getAssessRecord();
  },

  data() {
    return {
      //评估表格数据
      assessRecord: [],
      loading: false,
      //补录病人ChartNo
      chartNo: undefined,
      //评估列表信息
      supplementList: [],
    };
  },
  methods: {
    /**
     * description: 获取评估记录
     * return {*}
     * param {*} patient
     */    
    async getAssessRecord(patient) {
      this.assessRecord = [];
      if (!this.patient) return;
      this.loading = true;
      let params = {
        inpatientID: patient ? patient.inpatientID : this.patient.inpatientID,
      };
      //获取病人评估信息
      await GetMainByInpatient(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.assessRecord = result.data;
        }
      });
    },
  },
};
</script>

<style lang='scss'>
.assess-record {
  height: 100%;
  width: 100%;
}
</style>