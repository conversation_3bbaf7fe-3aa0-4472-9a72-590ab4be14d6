<!--
 * FilePath     : \src\autoPages\handover\multipleShiftHandover\components\shiftHandon.vue
 * Author       : 郭鹏超
 * Date         : 2023-05-05 11:21
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-09-23 23:18
 * Description  : 班别接班
 * CodeIterationRecord:
-->


<template>
  <base-layout v-loading="loading" :element-loading-text="loadingText" class="shift-handon">
    <div slot="header" class="multiple-shift-handon-header">
      <span>班别日期：</span>
      <el-date-picker
        @change="handoverDateChange"
        v-model="handoverDate"
        :clearable="false"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        class="handon-date"
      ></el-date-picker>
      <shift-selector
        :width="convertPX(163) + ''"
        :stationID="user.stationID"
        v-model="handoverShiftID"
        @select-item="shiftChange"
      ></shift-selector>
      <span>责任护士：</span>
      <el-select class="handon-nurse" @change="nurseChange" v-model="nurse">
        <el-option v-for="(item, index) in nurseList" :key="index" :value="item.userID" :label="item.name"></el-option>
      </el-select>
      <el-button class="handon-button" type="primary" icon="iconfont icon-handon" @click="handoverAllSave">
        接班
      </el-button>
    </div>
    <div class="multiple-shift-handon-content">
      <el-table
        :data="handoverList"
        height="100%"
        @selection-change="selectionHandover"
        ref="handoverTable"
        border
        stripe
      >
        <el-table-column
          type="selection"
          :width="convertPX(40)"
          align="center"
          :selectable="getDisableFlag"
          class-name="select"
        ></el-table-column>
        <el-table-column label="交班标识" :width="convertPX(120)">
          <template slot-scope="scope">
            <div :label="item" v-for="(item, index) in scope.row.keySigns" :key="index">
              <span v-if="item.isCheck">{{ item.signName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(110)" label="病人" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.bedNumber + "床" }}</div>
            <div>{{ scope.row.patientName }}</div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="handoffNurseName"
          :width="convertPX(110)"
          label="交班护士"
        ></el-table-column>
        <el-table-column align="center" :width="convertPX(90)" key="CompletionDegree" label="完成度">
          <template slot-scope="scope">
            {{ scope.row.completedSchedule + "/" + scope.row.totalSchedule }}
          </template>
        </el-table-column>
        <el-table-column label="S-现状">
          <template slot-scope="scope" v-if="scope.row.situation">
            <div v-html="scope.row.situation"></div>
          </template>
        </el-table-column>
        <el-table-column label="B-背景">
          <template slot-scope="scope">
            <div v-html="scope.row.background"></div>
          </template>
        </el-table-column>
        <el-table-column label="A-评估">
          <template slot-scope="scope">
            <div v-html="scope.row.assement"></div>
          </template>
        </el-table-column>
        <el-table-column label="R-建议">
          <template slot-scope="scope">
            <div v-html="scope.row.recommendation"></div>
          </template>
        </el-table-column>
        <el-table-column label="接班状态" :width="convertPX(120)" align="center">
          <template slot-scope="scope">
            <div v-if="!scope.row.handoverID">未交班</div>
            <div v-else>
              {{ scope.row.handonNurse ? "已接班" : "未接班" }}
            </div>
            <div>{{ scope.row.planScheduleFlag ? "已排程" : "未排程" }}</div>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(120)" align="center" label="详情">
          <template slot-scope="scope">
            <body-image
              v-if="scope.row.handoverID"
              :type="'button'"
              :handoverID="scope.row.handoverID"
              :patientName="scope.row.patientName"
            ></body-image>
            <el-button v-if="scope.row.handoverID" @click="getShiftPlan(scope.row.inpatientID)" type="text">
              计划总览
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="计划总览"
      :visible.sync="planShowFlag"
      custom-class="no-footer"
    >
      <el-table class="dialogPlanTable" :span-method="cellMerge" border :data="planList" height="100%">
        <el-table-column prop="problem" label="护理问题/集束护理" :min-width="convertPX(160)"></el-table-column>
        <el-table-column prop="nursingGoal" :width="convertPX(120)" label="护理目标"></el-table-column>
        <el-table-column prop="intervention" label="措施"></el-table-column>
        <el-table-column prop="frequency" label="频次" :width="convertPX(120)"></el-table-column>
        <el-table-column prop="frequenyDescription" label="频次说明"></el-table-column>
        <el-table-column label="开始日期" align="center" :width="convertPX(180)">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="结束日期" align="center" :width="convertPX(180)">
          <template>
            <template slot-scope="scope">
              <span v-formatTime="{ value: scope.row.endDate, type: 'date' }"></span>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="批量接班汇总"
      width="80%"
      :visible.sync="checkResultShowFlag"
      class="all-item"
    >
      <el-table border :data="checkResult" row-key="handoverID">
        <el-table-column align="center" :width="convertPX(70)" prop="bedNum" label="床号"></el-table-column>
        <el-table-column
          prop="patientName"
          :width="convertPX(100)"
          label="姓名"
          header-align="center"
        ></el-table-column>
        <el-table-column
          :width="convertPX(160)"
          align="center"
          :label="item.description"
          :key="index"
          v-for="(item, index) in checkResultColumn"
        >
          <template slot-scope="scope">
            <i v-if="!scope.row[item.typeValue]" class="iconfont icon-check-mark"></i>
          </template>
        </el-table-column>
        <el-table-column prop="isSuccess" :width="convertPX(80)" align="center" label="接班结果" header-align="center">
          <template slot-scope="scope">
            <i v-if="scope.row.isSuccess" class="iconfont icon-check-mark"></i>
          </template>
        </el-table-column>
        <el-table-column
          prop="checkResultStr"
          :min-width="convertPX(100)"
          label="状态"
          header-align="center"
        ></el-table-column>
      </el-table>
      <div slot="footer">
        <el-button type="primary" @click="closeCheckResult">确 认</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import shiftSelector from "@/components/selector/shiftSelector";
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetAttendanceNurse } from "@/api/Handover/HandoverCommonUse";
import { GetPatientInterventionList } from "@/api/Intervention";
import { GetBySettingTypeCodeByArray } from "@/api/Setting";
import { GetShiftHandonSBARTableList, ShiftHandon } from "@/api/Handover/MultipleShifHandover";
import progressView from "@/components/progressView";
import bodyImage from "@/components/bodyImage";
export default {
  components: {
    baseLayout,
    shiftSelector,
    progressView,
    bodyImage,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      loading: false,
      loadingText: "",
      handoverDate: undefined,
      handoverShiftID: undefined,
      nurseList: [],
      stationShifts: [],
      nurse: undefined,
      handoverList: [],
      saveHandoverList: [],
      //进度条开关
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      planShowFlag: false,
      planList: [],
      spanArr: [],
      checkResultColumn: [],
      checkResult: [],
      checkResultShowFlag: false,
    };
  },
  async beforeMount() {
    this.startDateTime = this.endDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
    await this.getPageSetting();
  },
  methods: {
    /**
     * description: 获取接班记录
     * return {*}
     */
    getHandoverList() {
      if (!this.handoverDate || !this.handoverShiftID || !this.nurse) {
        return;
      }
      let params = {
        recordsCode: "ShiftHandover",
        handoverClass: "HandOn",
        shiftDate: this.handoverDate,
        shiftID: this.handoverShiftID,
        nurserID: this.nurse,
        stationID: this.user.stationID,
      };
      this.loading = true;
      this.loadingText = "加载中……";
      GetShiftHandonSBARTableList(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverList = res.data?.handoverData ?? [];
          this.handoverCheckData = res.data?.checkResult ?? [];
        }
      });
    },
    /**
     * description: 批量接班确认
     * return {*}
     */
    handoverAllSave() {
      if (!this.saveCheck()) {
        return;
      }
      //交班信息确认
      let string = this.getHandoverInfo();
      this.$confirm(string, "提示", {
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        customClass: "multi-handon-msgbox-class",
      }).then(() => {
        this.multipleSaveHandover();
      });
    },
    /**
     * description: 批量接班
     * return {*}
     */
    async multipleSaveHandover() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.checkResult = [];
      this.progressFlag = true;
      if(!this.nurse){
        this._showTip("warning", "请选择接班人！");
        return;
      }
      for (let i = 0; i < this.saveHandoverList.length; i++) {
        const handover = this.saveHandoverList[i];
        let saveFlag = await this.shiftHandon(handover);
        let progress = (((i + 1) / this.saveHandoverList.length) * 100).toFixed(0);
        this.messageData[0].value = Number(progress);
        this.messageData[saveFlag ? 1 : 2].value += handover.bedNumber + "床-" + handover.patientName;
      }
    },
    /**
     * description: 班别接班
     * param {*} handover
     * return {*}
     */
    async shiftHandon(handover) {
      let params = {
        shiftDate: this.handoverDate,
        shiftID: this.handoverShiftID,
        handoverCommonSaveView: {
          handoverID: handover.handoverID,
          inpatientID: handover.inpatientID,
          handoverNurse: this.nurse,
          recordsCode: "ShiftHandover",
          handoverClass: "HandOn",
        },
      };
      let saveFlag = false;
      await ShiftHandon(params).then((res) => {
        if (this._common.isSuccess(res)) {
          saveFlag = true;
          res.data.bedNum = handover.bedNumber;
          res.data.patientName = handover.patientName;
          this.checkResult.push(res.data);
        }
      });
      return saveFlag;
    },
    /**
     * description: 显示护理计划
     * param {*} inpatientID
     * return {*}
     */
    getShiftPlan(inpatientID) {
      let params = {
        inpatientID: inpatientID,
      };
      GetPatientInterventionList(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.length == 0) {
            this._showTip("warning", "未查到病人计划！");
          } else {
            this.planList = response.data;
            this.getSpanArr(this.planList);
            this.planShowFlag = true;
          }
        }
      });
    },
    /**
     * description: 关闭接班结果
     * return {*}
     */
    closeCheckResult() {
      this.checkResultShowFlag = false;
      this.getHandoverList();
    },
    /**
     * description: 获取接班结果表格表头
     * return {*}
     */
    getSaveResultColumn() {
      let params = {
        settingTypeCode: "HandOffPatientCheck,HandoffNurseCheck",
      };
      GetBySettingTypeCodeByArray(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (!response?.data?.length) {
            return;
          }
          for (let i = 0; i < response.data.length; i++) {
            //typeValue首字母小写
            let str = response.data[i].typeValue;
            let strArr = str.split("_");
            response.data[i].typeValue = strArr?.length && strArr[0];
            response.data[i].typeValue = response.data[i].typeValue.replace(strArr[0][0], strArr[0][0].toLowerCase());
          }
        }
        this.checkResultColumn = response.data.filter((m) => m.typeValue != "deathPatient");
      });
    },
    /**
     * description: 获取合并数组
     * param {*} data
     * return {*}
     */
    getSpanArr(data) {
      this.spanArr = [];
      let pos = 0;
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].problem == data[i - 1].problem) {
            this.spanArr[pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            pos = i;
          }
        }
      }
    },
    /**
     * description: 相同项合并
     * param {*} rowIndex
     * param {*} columnIndex
     * return {*}
     */
    cellMerge({ rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        //合并第一列和第二列
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    /**
     * description: 进度条关闭函数
     * return {*}
     */
    progressClose() {
      this.progressFlag = false;
      this.checkResultShowFlag = true;
    },
    /**
     * description: 交班检核
     * return {*}
     */
    saveCheck() {
      if (!this.saveHandoverList.length) {
        this._showTip("warning", "请先勾选汇总患者！");
        return false;
      }
      return true;
    },
    /**
     * description: 交班信息确认
     * return {*}
     */
    getHandoverInfo() {
      let repeatCommit = [];
      let commitArray = [];
      for (let i = 0; i < this.saveHandoverList.length; i++) {
        let item = this.saveHandoverList[i];
        commitArray.push(item.bedNumber + "床");
        if (item.handonDate) {
          //已存在 是重复接班
          repeatCommit.push(item.bedNumber + "床");
        }
      }
      let message =
        "<span>您要接班的床位是:</span><strong style='color:red;'>" + commitArray.toString() + "</strong><br/>";
      if (repeatCommit.length > 0) {
        message += "<span>重复接班床位:</span><strong style='color:red;'>" + repeatCommit.toString() + "</strong><br>";
        message += "<strong style='color:red;' >重复接班会覆盖上一次接班记录</strong>";
      }
      return message;
    },
    /**
     * description: 交班时间变化
     * return {*}
     */
    async handoverDateChange() {
      await this.getAttendanceNurse();
      await this.getHandoverList();
    },
    /**
     * description: 班别变化
     * return {*}
     */
    async shiftChange() {
      await this.getAttendanceNurse();
      await this.getHandoverList();
    },
    /**
     * description: 护士变化
     * return {*}
     */
    nurseChange() {
      this.getHandoverList();
    },
    /**
     * description: 获取页面配置
     * return {*}
     */
    async getPageSetting() {
      const methodArr = [this.getHandonShift, this.getAttendanceNurse, this.getHandoverList, this.getSaveResultColumn];
      for (const method of methodArr) {
        await method();
      }
    },
    /**
     * description: 获取当前班别
     * return {*}
     */
    async getHandonShift() {
      await GetNowStationShiftData().then((res) => {
        if (this._common.isSuccess(res)) {
          //班别日期和班别初始化
          this.handoverDate = res.data?.shiftDate ?? undefined;
          this.stationShifts = res.data?.stationShifts ?? [];
        }
      });
    },
    /**
     * description: 获取派班护士
     * return {*}
     */
    async getAttendanceNurse() {
      let params = {
        shiftDate: this.handoverDate,
        stationID: this.user.stationID,
        shiftID: this.handoverShiftID,
      };
      await GetAttendanceNurse(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.nurseList = res.data;
          this.nurse = undefined;
          if (!res.data?.length || !this.user) {
            return;
          }
          if (this.nurseList.find((nurse) => nurse.userID == this.user.userID)) {
            this.nurse = this.user.userID;
          }
        }
      });
    },
    /**
     * description: 获取勾选接班数据
     * param {*} selection
     * return {*}
     */
    selectionHandover(selection) {
      this.saveHandoverList = selection;
    },
    /**
     * description: 不符合条件禁用
     * param {*} row
     * return {*}
     */
    getDisableFlag(row) {
      return !!row.handoverID || !!row.result;
    },
  },
};
</script>

<style lang="scss" >
.shift-handon {
  height: 100%;
  .multiple-shift-handon-header {
    .handon-date {
      width: 163px;
    }
    .handon-nurse {
      width: 163px;
    }
    .handon-button {
      float: right;
      margin-top: 10px;
    }
  }
  .multiple-shift-handon-content {
    height: 100%;
    .el-button--small {
      padding: 0;
      margin: 0;
    }
  }
  .all-item {
    line-height: 27px;
    .item-name {
      text-align: right;
      display: inline-block;
    }
  }
}
</style>