<!--
 * FilePath     : \ccc.web\src\pages\dictionaryMaintain\documentChange\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-06-27 08:25
 * LastEditors  : 郭鹏超
 * LastEditTime : 2021-07-13 18:30
 * Description  : 病历转换(电子病历评审使用)
-->
<template>
  <base-layout v-loading="loadingFlag" element-loading-text="加载中……" class="document-change" header-height="auto">
    <div slot="header">
      <search-patient-data @selectPatientData="initData" @change="change"></search-patient-data>
      <div class="bottom">
        <span>病历完整病人住院号:</span>
        <el-input style="width: 160px" v-model="intactLocalCaseNumber"></el-input>
        <el-button icon="iconfont icon-save-button" @click="saveData" type="primary">开始转换</el-button>
      </div>
    </div>
    <div class="document-change-content">
      <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
      <div class="top">
        <el-radio-group @change="search" v-model="changeFlag">
          <el-radio-button :label="false">未转换</el-radio-button>
          <el-radio-button :label="true">已转换</el-radio-button>
        </el-radio-group>
        <span>病区:</span>
        <station-selector v-model="currentStation" label="" width="160"></station-selector>
        <span>月份:</span>
        <el-date-picker
          value-format="yyyy/M"
          v-model="month"
          type="month"
          style="width: 100px"
          placeholder="选择月"
        ></el-date-picker>
        <span>科室:</span>
        <dept-selector label="" width="140" v-model="currentDepartment" :stationID="currentStation"></dept-selector>
        <span>性别:</span>
        <el-select style="width: 100px" v-model="genderCode" placeholder="请选择">
          <el-option label="" value=""></el-option>
          <el-option label="男" value="1"></el-option>
          <el-option label="女" value="2"></el-option>
        </el-select>
        <el-button @click="search" class="query-button" icon="iconfont icon-search">查询</el-button>
      </div>
      <el-table
        @select="getChangeDocumentInpatient"
        ref="documentChangeTable"
        height="calc(100% - 45px)"
        :data="patientTableList"
        border
        stripe
      >
        <el-table-column :selectable="checkSelect" type="selection" width="50" align="center"></el-table-column>
        <el-table-column prop="localCaseNumber" label="住院号" width="100"></el-table-column>
        <el-table-column prop="patientName" align="center" label="姓名" width="100"></el-table-column>
        <el-table-column prop="gender" align="center" label="性别" width="60"></el-table-column>
        <el-table-column prop="bedNumber" align="center" label="床号" width="100"></el-table-column>
        <el-table-column prop="stationName" label="病区" min-width="100"></el-table-column>
        <el-table-column prop="departmentListName" label="科别" min-width="100"></el-table-column>
        <el-table-column label="入院日期时间" width="160" align="center">
          <template slot-scope="scope">
            <span
              v-formatTime="{
                value: scope.row.admissionDateTimeView,
                type: 'date',
              }"
            ></span>
            <span
              v-formatTime="{
                value: scope.row.admissionDateTimeView,
                type: 'time',
              }"
            ></span>
          </template>
        </el-table-column>
        <el-table-column label="出院日期时间" width="160" align="center">
          <template slot-scope="scope">
            <span
              v-formatTime="{
                value: scope.row.dischargeDateTimeView,
                type: 'date',
              }"
            ></span>
            <span
              v-formatTime="{
                value: scope.row.dischargeDateTimeView,
                type: 'time',
              }"
            ></span>
          </template>
        </el-table-column>
        <el-table-column prop="changeName" align="center" label="转换人" width="100"></el-table-column>
        <!-- <el-table-column label="病历转换" align="center" width="100">
          <template slot-scope="scope">
            <div>
              {{ scope.row.changeFlag ? "已转换" : "未转换" }}
            </div>
          </template>
        </el-table-column> -->
        <!-- <el-table-column align="center" label="操作" width="60">
          <template slot-scope="scope">
            <el-tooltip content="病案查询">
              <i class="iconfont icon-pdf" @click="jump(scope.row.localCaseNumber)"></i>
            </el-tooltip>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import progressView from "@/components/progressView";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData.vue";
import { mapGetters } from "vuex";
import { DocumentChangeStart, GetDocumentChangePatientList } from "@/api/DocumentChange";
export default {
  components: {
    baseLayout,
    stationSelector,
    deptSelector,
    progressView,
    searchPatientData,
  },
  data() {
    return {
      loadingFlag: false,
      //替换病历列表
      documentTableList: [
        {
          name: "护理记录单",
          fileClassID: 1,
        },
        {
          name: "护理计划单",
          fileClassID: 14,
        },
      ],
      dialogVisible: false,
      changeFlag: false,
      currentStation: undefined,
      currentDepartment: undefined,
      month: undefined,
      genderCode: undefined,
      localCaseNumber: undefined,
      intactLocalCaseNumber: undefined,
      patientTableList: [],
      changeDocumentPatientList: [],
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "转换成功",
          value: "",
        },
        {
          label: "转换失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      //进度条开关
      progressFlag: false,
    };
  },
  methods: {
    //病人搜索
    search() {
      let params = {
        month: this.month,
        stationID: this.currentStation,
        departmentID: this.currentDepartment,
        genderCode: this.genderCode,
        changeFlag: this.changeFlag,
      };
      this.loadingFlag = true;
      GetDocumentChangePatientList(params).then((res) => {
        this.loadingFlag = false;
        if (this._common.isSuccess(res)) {
          if (res.data.length) {
            let newTableList = [];
            res.data.forEach((item) => {
              let { basicData, ...changeData } = item;
              let tableItem = { ...changeData, ...item.basicData };
              newTableList.push(tableItem);
            });
            this.patientTableList = newTableList;
          } else {
            this.patientTableList = [];
          }
        }
      });
    },
    //获取勾选病人
    getChangeDocumentInpatient(list, row) {
      if (list.length > 10) {
        this._showTip("warning", "一次最多转换10位病人病历");
        this.$refs.documentChangeTable.toggleRowSelection(row, false);
        return;
      }
      this.changeDocumentPatientList = list;
    },
    //开始转换
    async saveData() {
      if (!this.intactLocalCaseNumber) {
        this._showTip("warning", "请输入完整病历病人住院号");
        return;
      }
      if (this.changeDocumentPatientList.length == 0) {
        this._showTip("warning", "请勾选需要转换的病人");
        return;
      }
      let successMessage = "";
      let failMessage = "";
      this.progressFlag = true;
      for (let i = 0; i < this.changeDocumentPatientList.length; i++) {
        //配置进度条内容
        let progress = (((i + 1) / this.changeDocumentPatientList.length) * 100).toFixed(0);
        let params = {
          localCaseNumber: this.intactLocalCaseNumber,
          ChangeInpatientID: this.changeDocumentPatientList[i].inpatientID,
          FileClassList: this.documentTableList,
        };
        await DocumentChangeStart(params).then((res) => {
          if (res.code == 1) {
            if (successMessage) {
              successMessage += "、" + this.changeDocumentPatientList[i].patientName;
            } else {
              successMessage = this.changeDocumentPatientList[i].patientName;
            }
          } else {
            if (failMessage) {
              failMessage += "、" + this.changeDocumentPatientList[i].patientName;
            } else {
              failMessage = this.changeDocumentPatientList[i].patientName;
            }
          }
        });
        //配置进度条内容
        this.messageData[0].value = Number(progress);
        this.messageData[1].value = successMessage;
        this.messageData[2].value = failMessage;
      }
      this.search();
    },
    //病案查询跳转
    jump(localCaseNumber) {
      this.$router.push({
        name: "document",
        query: {
          caseNumber: localCaseNumber,
        },
      });
    },
    //已转换不允许勾选
    checkSelect(row) {
      return !row.changeFlag;
    },
    //进度条重置
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    //进度条关闭函数
    progressClose() {
      this.progressFlag = false;
      this.renewMessageData();
    },
    initData(patient) {
      if (patient) {
        this.intactLocalCaseNumber = patient.localCaseNumber;
      }
    },
    change() {
      this.intactLocalCaseNumber = undefined;
    },
  },
};
</script>

<style lang="scss">
.document-change {
  .document-change-content {
    height: 100%;
    background-color: #fff;
    .top {
      height: 45px;
      line-height: 45px;
    }
  }
}
</style> 