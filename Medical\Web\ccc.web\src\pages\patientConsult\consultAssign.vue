<!--
 * FilePath     : \src\pages\patientConsult\consultAssign.vue
 * Author       : 郭自飞
 * Date         : 2020-03-31 15:33
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-17 17:50
 * Description  : 会诊指派
 -->
<template>
  <base-layout show-header class="consult-assign">
    <div slot="header">
      <div class="consult-radio">
        <el-radio-group v-model="consultRadio" @change="consultClassification()">
          <el-radio-button :label="true">未指派</el-radio-button>
          <el-radio-button :label="false">已指派</el-radio-button>
        </el-radio-group>
      </div>
      <span>病区:</span>
      <el-select v-model="assignStationID" class="objective" placeholder="请选择" @change="queryConsult">
        <el-option
          v-for="item in stationList"
          :key="item.stationID"
          :label="item.stationName"
          :value="item.stationID"
        ></el-option>
      </el-select>
    </div>

    <el-table
      height="100%"
      :data="patientConsultList"
      v-loading="loading"
      border
      stripe
      row-key="patientConsultID"
      :expand-row-keys="expands"
      @expand-change="loadData"
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <el-table :data="scope.row.children" border stripe class="nesting-table" height="100%">
            <el-table-column show-overflow-tooltip label="目的" header-align="center" min-width="100" align="left">
              <template slot-scope="scope">
                <span>{{ scope.row.mainContent + "-" + scope.row.detailContent }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="replyStationNmae" label="回复病区" min-width="85" align="center"></el-table-column>
            <el-table-column label="回复日期" width="160" align="center">
              <template slot-scope="scope">
                <span
                  v-formatTime="{
                    value: scope.row.replyDate,
                    type: 'dateTime',
                  }"
                ></span>
              </template>
            </el-table-column>
            <el-table-column prop="assignEmployeeName" label="指派人员" min-width="80" align="center"></el-table-column>
            <el-table-column label="操作" width="60" align="center">
              <template slot-scope="scope">
                <el-tooltip content="指派">
                  <i class="iconfont icon-assign" @click="consultAssignDialog(scope.row)"></i>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column prop="patientName" label="病人" min-width="60" align="center"></el-table-column>
      <el-table-column prop="localCaseNumber" label="住院号" width="110" align="center"></el-table-column>
      <el-table-column
        prop="consultStationNmae"
        header-align="center"
        label="发起病区"
        min-width="120"
        align="left"
      ></el-table-column>
      <el-table-column prop="consultDate" label="发起日期" width="160" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.consultDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="consultEmployeeName" label="发起人员" width="80" align="center"></el-table-column>
      <el-table-column label="目的" header-align="center" show-overflow-tooltip min-width="100" align="left">
        <template slot-scope="scope">
          <span>{{ scope.row.mainContent + "-" + scope.row.detailContent }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="replyStationNmae"
        header-align="center"
        label="会诊回复病区"
        min-width="85"
        align="left"
      ></el-table-column>
      <el-table-column label="回复日期" width="160" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.replyDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="指派时间" width="160" align="center">
        <template slot-scope="scope">
          <span
            v-formatTime="{
              value: scope.row.assignDate,
              type: 'dateTime',
            }"
          ></span>
        </template>
      </el-table-column>
      <el-table-column prop="assignEmployeeName" label="指派人员" width="80" align="center"></el-table-column>
      <el-table-column prop="emergencyName" label="类别" width="70" align="center"></el-table-column>
      <el-table-column label="操作" width="60" align="center">
        <template slot-scope="scope">
          <el-tooltip v-if="!scope.row.hasChildren" content="指派">
            <i class="iconfont icon-assign" @click="consultAssignDialog(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      title="会诊指派"
      :visible.sync="dialogFormVisible"
      width="590px"
      v-dialogDrag
      v-loading="dialogLoading"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      element-loading-text="加载中……"
    >
      <div class="dag-div">
        <span>指派目的:</span>
        <el-input :readonly="true" class="objective" v-model="columnData.mainContent"></el-input>
        <span>详细目的:</span>
        <el-input :readonly="true" class="objective" v-model="columnData.detailContent"></el-input>
      </div>
      <div class="date-pick-div">
        <span>指派人员:</span>
        <el-select v-model="assignEmployeeID" class="objective" @change="getGoalPhoneNumber">
          <el-option
            v-for="item in consultGoalEmployeeLsit"
            :key="item.employeeID"
            :label="item.employeeName"
            :value="item.employeeID"
          ></el-option>
        </el-select>
        <span>指派时间:</span>
        <el-date-picker
          v-model="assignDate"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="选择日期时间"
        ></el-date-picker>
      </div>
      <div class="tip">
        <span class="tip-span">备注:</span>
        <el-input
          type="textarea"
          :readonly="true"
          class="consult-input"
          v-model="columnData.consultContent"
          resize="none"
        ></el-input>
      </div>

      <div class="phoneNumber">
        <span class="phoneNumber-span">会诊人联系方式:{{ consultPhoneNumber }}</span>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="consultAssign">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import { GetPatientConsultBySationID, UpdateConsult, GetConsultGoalEmployee } from "@/api/PatientConsult";
import { GetEmployeeSwitchStationList } from "@/api/User";
import { GetStationList } from "@/api/Station";
import { GetUserInfo } from "@/api/User";
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
  },
  props: {
    refillFlag: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      assignStationID: undefined,
      stationList: [],
      patientConsultList: [],
      loading: false,
      dialogLoading: false,
      columnData: {},
      dialogFormVisible: false,
      assignEmployeeID: undefined,
      consultPhoneNumber: "",
      consultGoalEmployeeLsit: [],
      consultRadio: true,
      clonePatientConsultList: [],
      expands: [],
      assignDate: this._datetimeUtil.getNow(),
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      if (this.user) {
        this.assignStationID = this.user.stationID;
        this.queryConsult();
      }
      GetEmployeeSwitchStationList().then((res) => {
        if (this._common.isSuccess(res)) {
          this.stationList = res.data;
          if (this.stationList.length == 0) {
            GetStationList().then((res) => {
              if (this._common.isSuccess(res)) {
                let list = res.data.filter((item) => {
                  return item.id == this.user.stationID;
                });
                this.stationList.push({
                  stationID: list[0].id,
                  stationName: list[0].stationName,
                });
              }
            });
          }
        }
      });
    },
    /**
     * @description: 根据病区查询数据
     * @return
     * @param id
     */
    queryConsult(id) {
      this.loading = true;
      let params = {
        stationID: this.assignStationID,
      };
      if (id) {
        params.stationID = id;
      }
      GetPatientConsultBySationID(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.patientConsultList = res.data.filter((item) => {
            return !item.cancelEmployeeID == true;
          });
          this.clonePatientConsultList = this._common.clone(this.patientConsultList);
          this.clonePatientConsultList.forEach((element) => {
            let index = 0;
            if (element.children) {
              element.children.forEach((item) => {
                if (item.assignEmployeeName) {
                  index += 1;
                }
              });
              if (index == element.children.length) {
                element.assignEmployeeName = "已指派";
              }
            }
          });
          this.consultClassification();
        }
      });
    },
    /**
     * @description: 指派弹框
     * @return
     * @param data
     */
    consultAssignDialog(data) {
      this.dialogFormVisible = true;
      this.columnData = this._common.clone(data);
      this.assignEmployeeID = data.assignEmployeeID;
      if (data.assignDate) {
        this.assignDate = data.assignDate;
      }
      this.getGoalEmployee(data.consultDetailID);
    },
    /**
     * @description: 指派
     * @return
     */
    consultAssign() {
      if (!this.assignEmployeeID) {
        this._showTip("warning", "请选择指派人员");
        return;
      }
      if (!this.columnData.signOffMainID && this.columnData.consultGroupID) {
        this._showTip("warning", "未通过审批");
        return;
      }
      if (this.columnData.replyEmployeeID) {
        this._showTip("warning", "会诊已回复,无法修改");
        return;
      }
      this.dialogLoading = true;
      this.columnData.assignEmployeeID = this.assignEmployeeID;
      if (this.assignDate) {
        this.columnData.assignDate = this._datetimeUtil.formatDate(this.assignDate, "yyyy-MM-dd hh:mm");
      } else {
        this.columnData.assignDate = this._datetimeUtil.getNow();
      }
      this.$set(this.columnData, "refillFlag", this.refillFlag);
      return UpdateConsult(this.columnData).then((res) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(res)) {
          this.dialogFormVisible = false;
          this._showTip("success", "指派成功");
          this.queryConsult();
        }
      });
    },
    /**
     * @description: 获取被邀请会诊人手机号码
     * @return
     * @param id
     */
    getGoalPhoneNumber(id) {
      let parms = {
        UserId: id,
      };
      GetUserInfo(parms).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultPhoneNumber = res.data[0].phoneNumber;
        }
      });
    },
    /**
     * @description: 获取会诊人员
     * @return
     * @param id
     */
    getGoalEmployee(id) {
      let prams = {
        consultGoalID: id,
        stationID: this.assignStationID,
      };
      GetConsultGoalEmployee(prams).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultGoalEmployeeLsit = res.data.filter((item) => {
            return item.stationID == this.assignStationID || item.stationID == 0;
          });
        }
      });
    },
    loadData(row) {
      if (row.hasChildren) {
        var index = this.expands.indexOf(row.patientConsultID);
        if (index >= 0) {
          this.expands.splice(index, 1);
          return;
        }
        this.expands.push(row.patientConsultID);
      } else {
        this.expands = [];
      }
    },
    /**
     * @description: 会诊分类
     * @return
     */
    consultClassification() {
      //指派
      if (this.consultRadio) {
        this.patientConsultList = this.clonePatientConsultList.filter((element) => {
          return !element.assignEmployeeName == true;
        });
      } else {
        this.patientConsultList = this.clonePatientConsultList.filter((element) => {
          return !element.assignEmployeeName == false;
        });
      }
    },
  },
};
</script>
<style lang="scss">
.consult-assign {
  .query-button {
    float: right;
    margin-top: 15px;
  }
  .consult-radio {
    display: inline-block;
    margin-right: 20px;
  }
  .objective {
    width: 150px;
  }
  .date-pick-div {
    margin-bottom: 15px;
    .el-input__inner {
      width: 150px;
    }
  }
  .dag-div {
    margin-bottom: 15px;
  }
  .tip {
    height: 260px;
    margin-bottom: 10px;
    .tip-span {
      float: left;
    }
    .consult-input {
      width: 90.5%;
      height: 100%;
      .el-textarea__inner {
        margin-left: 5px;
        height: 100%;
      }
    }
  }
  .el-table__expanded-cell {
    .nesting-table {
      width: calc(100% - 10px);
    }
  }
  .el-dialog {
    height: auto;
  }
  .phoneNumber {
    .phoneNumber-span {
      margin-left: 5%;
    }
  }
}
</style>
