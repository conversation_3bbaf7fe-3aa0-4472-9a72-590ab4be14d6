{"name": "ccc.web", "version": "1.0.0", "description": "medical care direct web", "author": "SJZ\\sujunzhi <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node build/build.js"}, "volta": {"node": "14.8.0"}, "dependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/runtime": "^7.20.13", "@tinymce/tinymce-vue": "1.1.2", "@vue/babel-preset-app": "^5.0.8", "axios": "0.19.0", "babel-polyfill": "^6.26.0", "babel-runtime": "^6.26.0", "core-js": "^3.27.2", "echarts": "4.5.0", "element-ui": "2.12.0", "file-saver": "2.0.2", "lib-flexible": "^0.3.2", "node-sass": "4.14.1", "pdfjs-dist": "2.3.200", "postcss-plugin-px2rem": "^0.8.1", "qrcodejs2": "0.0.2", "qs": "6.9.0", "sass-loader": "7.3.1", "sass-resources-loader": "2.0.1", "screenfull": "^4.2.1", "script-loader": "0.7.2", "sortablejs": "^1.15.0", "style-loader": "1.0.0", "tinymce": "4.9.7", "umy-ui": "1.1.6", "v-charts": "1.19.0", "v-contextmenu": "2.8.0", "vue": "2.5.2", "vue-hot-reload-api": "^2.3.4", "vue-i18n": "8.26.5", "vue-pdf": "4.0.0", "vue-router": "3.0.1", "vue-runtime-helpers": "1.1.2", "vuex": "3.1.1", "xlsx": "0.16.8"}, "devDependencies": {"@babel/cli": "^7.20.7", "@babel/core": "^7.20.12", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-classes": "^7.20.7", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "autoprefixer": "7.1.2", "babel-helper-vue-jsx-merge-props": "2.0.3", "babel-loader": "8.0.4", "babel-plugin-syntax-jsx": "6.18.0", "babel-plugin-transform-vue-jsx": "3.5.0", "babel-preset-stage-2": "6.22.0", "chalk": "2.0.1", "copy-webpack-plugin": "4.0.1", "css-loader": "0.28.0", "extract-text-webpack-plugin": "3.0.0", "file-loader": "1.1.4", "friendly-errors-webpack-plugin": "1.6.1", "html-webpack-plugin": "2.30.1", "node-notifier": "5.1.2", "optimize-css-assets-webpack-plugin": "3.2.0", "ora": "1.2.0", "portfinder": "1.0.13", "postcss-import": "11.0.0", "postcss-loader": "2.0.8", "postcss-url": "7.2.1", "rimraf": "2.6.0", "semver": "5.3.0", "shelljs": "0.7.6", "uglifyjs-webpack-plugin": "1.1.1", "url-loader": "0.5.8", "vue-loader": "13.3.0", "vue-style-loader": "3.0.1", "vue-template-compiler": "2.5.2", "webpack": "3.6.0", "webpack-bundle-analyzer": "2.9.0", "webpack-dev-server": "2.9.1", "webpack-merge": "4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}