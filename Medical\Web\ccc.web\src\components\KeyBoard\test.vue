<template>
  <div class="warm">
      <x-input :value="value1" name="qq" @input="updateValue($event,'value1')" title="测试" readonly="readonly" :show-clear="false" @click.native="showKeyBoard($event)" />
      <x-input :value="value2" name="qq" @input="updateValue($event,'value2')" title="测试" readonly="readonly" :show-clear="false" @click.native="showKeyBoard($event)" />
      <x-input :value="value3" name="qq" @input="updateValue($event,'value3')" title="测试" readonly="readonly" :show-clear="false" @click.native="showKeyBoard($event)" />
      
      <key-board :show="isShowKeyBoard" :output="el" typeName="qq" @hide="hideKeyBoard"></key-board>
  </div>
</template>
<script>
import keyBoard from '@/components/KeyBoard/KeyBoard'
import { XInput } from 'vux';
export default {
  components: {XInput,keyBoard},
  data() {
    return {
      isShowKeyBoard: false,
      value1:'',
      value2:'',
      value3:'',
      el:'',
      valueName: ''
    };
  },
  methods: {
    showKeyBoard(e){
      this.el = e.target
      this.isShowKeyBoard = true
    },
    hideKeyBoard(){
      this.isShowKeyBoard = false
       this.el = e.target
    },
    updateValue(value, valueName){
      this[valueName] = value
    },
  }
};
</script>
<style>
.warm {
  width: 100%;
  height: 100%;
}
.test {
  width: 100%;
  height: calc(100% - 46px);
  border: 0;
}
</style>