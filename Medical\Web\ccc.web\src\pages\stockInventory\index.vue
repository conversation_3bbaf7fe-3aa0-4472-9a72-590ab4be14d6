<!--
 * FilePath     : \src\pages\stockInventory\index.vue
 * Author       : 孟昭永
 * Date         : 2021-04-25 11:26
 * LastEditors  : 张现忠
 * LastEditTime : 2022-08-25 16:01
 * Description  : 
-->
<template>
  <base-layout class="stock-inventory">
    <div slot="header">
      <span>日期：</span>
      <el-date-picker
        class="date-picker"
        v-model="inventoryDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        @change="changeDate"
      ></el-date-picker>
      <shift-selector
        label="班别"
        v-model="inventoryShift"
        :stationID="inventoryStation"
        width="180px"
        @select="selectShift"
      ></shift-selector>
      <nurse-selector
        label="盘点人员"
        v-model="inventorNurse"
        :stationID="inventoryStation"
        width="180px"
      ></nurse-selector>
      <el-button class="save-button" type="primary" icon="iconfont icon-save-button" @click="saveInventory">
        保 存
      </el-button>
    </div>
    <div slot-scope="layout" :style="{ height: layout.height + 'px' }">
      <div class="left-wrap">
        <el-table
          ref="inventoryTable"
          class="table-main"
          :data="tableMain"
          v-loading="tableMainLoading"
          border
          stripe
          height="100%"
          highlight-current-row
          @row-click="clickRow"
        >
          <el-table-column prop="recordCodeName" label="盘点表" header-align="center"></el-table-column>
          <el-table-column prop="status" label="状态" width="60" align="center"></el-table-column>
        </el-table>
      </div>
      <div class="right-wrap" :style="{ height: layout.height + 'px' }">
        <ux-grid
          class="table-detail"
          :data="tableDetail"
          v-loading="tableDetailLoading"
          stripe
          keep-source
          :height="layout.height"
          :edit-config="{ trigger: 'click', mode: 'cell' }"
        >
          >
          <ux-table-column field="items" title="药品名称" width="420" header-align="center"></ux-table-column>
          <ux-table-column field="needAmount" title="应有量" width="90" header-align="center" edit-render>
            <template slot-scope="scope">
              <el-input v-model="scope.row.needAmount"></el-input>
            </template>
          </ux-table-column>
          <ux-table-column field="amount" title="实际量" width="90" header-align="center" edit-render>
            <template slot-scope="scope">
              <el-input v-model="scope.row.amount"></el-input>
            </template>
          </ux-table-column>
          <ux-table-column field="amount" title="盘点量" width="90" header-align="center" edit-render>
            <template slot-scope="scope">
              <el-input v-model="scope.row.amount"></el-input>
            </template>
          </ux-table-column>
          <ux-table-column field="description" title="备注" width="180" header-align="center" edit-render>
            <template slot-scope="scope">
              <el-input v-model="scope.row.description"></el-input>
            </template>
          </ux-table-column>
        </ux-grid>
      </div>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout.vue";
import shiftSelector from "@/components/selector/shiftSelector";
import nurseSelector from "@/components/selector/nurseSelector";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetStockInventoryMain, GetStockInventoryDetail, SaveStockInventory } from "@/api/StockInventory";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  components: {
    baseLayout,
    shiftSelector,
    nurseSelector,
  },

  created() {
    this.init();
  },
  data() {
    return {
      //盘点日期
      inventoryDate: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      //盘点班别
      inventoryShift: undefined,
      //盘点人员
      inventorNurse: undefined,
      //盘点病区
      inventoryStation: undefined,
      tableMainLoading: false,
      tableDetailLoading: false,
      //盘点主表
      tableMain: [],
      //盘点明细表
      tableDetail: [],
      //盘点表
      inventorySheet: undefined,
      //盘点表类型
      inventorySheetType: undefined,
      //盘点表序号
      shiftStockMainID: undefined,
    };
  },

  methods: {
    //保存盘点数据
    async saveInventory() {
      let params = {
        shiftStockMainID: this.shiftStockMainID,
        stockDate: this.inventoryDate,
        stationID: this.inventoryStation,
        stockShift: this.inventoryShift,
        stockNurse: this.inventorNurse,
        recordCode: this.inventorySheet,
        recordType: this.inventorySheetType,
        StockDetailList: this.tableDetail,
      };
      if (this.inventorySheet == null) {
        this._showTip("warning", "表编号不能为空");
        return;
      }
      await SaveStockInventory(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
        }
      });
      await this.getList();
    },

    //手动选中行
    async clickRow(row) {
      if (!row) {
        return;
      }
      this.shiftStockMainID = row.shiftStockMainID;
      this.inventorySheet = row.recordCode;
      this.inventorySheetType = row.recordType;
      let params = {
        stationID: row.stationID,
        recordCode: row.recordCode,
        shiftStockMainID: row.shiftStockMainID,
        stockDate: this.inventoryDate,
        inventoryShift: this.inventoryShift,
      };
      await this.getTableDetail(params);
    },

    //改变日期刷新数据
    async changeDate() {
      this.inventorySheet = undefined;
      await this.getList();
    },
    //改变班别刷新数据
    async selectShift() {
      this.inventorySheet = undefined;
      await this.getList();
    },
    //初始化
    async init() {
      this.inventoryStation = this.user.stationID;
      this.inventorNurse = this.user.userID;
      await this.getNowShift();
      await this.getList();
    },

    //获取当前病区班别
    async getNowShift() {
      await GetNowStationShiftData().then((result) => {
        if (this._common.isSuccess(result)) {
          let infos = result.data;
          this.inventoryShift = infos.nowShift.id;
        }
      });
    },

    //获取主表信息
    async getTableMain() {
      this.tableMainLoading = true;
      let params = {
        inventoryStation: this.inventoryStation,
        inventoryDate: this.inventoryDate,
        inventoryShift: this.inventoryShift,
        inventorNurse: this.inventorNurse,
      };
      await GetStockInventoryMain(params).then((result) => {
        this.tableMainLoading = false;
        if (this._common.isSuccess(result)) {
          this.tableMain = result.data;
        }
      });
    },

    //获取从表信息
    async getTableDetail(val) {
      // this.tableDetailLoading = true;
      let params = {
        stationID: val.stationID,
        recordCode: val.recordCode,
        shiftStockMainID: val.shiftStockMainID,
        stockDate: val.stockDate,
        inventoryShift: val.inventoryShift,
      };
      await GetStockInventoryDetail(params).then((result) => {
        // this.tableDetailLoading = false;
        if (this._common.isSuccess(result)) {
          this.tableDetail = result.data;
        }
      });
    },
    //刷新后获取列表
    async getList() {
      await this.getTableMain();
      if (this.tableMain && this.tableMain.length > 0) {
        let row = this.tableMain.find((item) => item.recordCode == this.inventorySheet);
        if (row) {
          this.$nextTick(() => {
            this.$refs.inventoryTable.setCurrentRow(row);
          });
          await this.clickRow(row);
        } else {
          this.$nextTick(() => {
            this.$refs.inventoryTable.setCurrentRow(this.tableMain[0]);
          });
          await this.clickRow(this.tableMain[0]);
        }
      }
    },
  },
};
</script>
<style lang="scss">
.stock-inventory {
  min-width: 900px;
  .left-wrap {
    float: left;
    height: 100%;
    .table-main {
      width: 300px;
    }
  }
  .right-wrap {
    float: right;
    height: 100%;
    width: calc(100% - 320px);
  }
  .save-button {
    float: right;
    margin-top: 10px;
  }
}
</style>