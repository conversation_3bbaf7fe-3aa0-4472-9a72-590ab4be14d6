/*
 * FilePath     : \src\api\Handover\MultipleShifHandover.js
 * Author       : 郭鹏超
 * Date         : 2023-05-07 15:39
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-28 16:23
 * Description  :批量交班API
 * CodeIterationRecord:
 */
import http from "../../utils/ajax";
const baseUrl = "/MultipleShifHandover";
const urls = {
  GetShiftHandoffSBARTableList: baseUrl + "/GetShiftHandoffSBARTableList",
  GetShiftHandonSBARTableList: baseUrl + "/GetShiftHandonSBARTableList",
  ShiftHandoff: baseUrl + "/ShiftHandoff",
  InShiftHandoff: baseUrl + "/InShiftHandoff",
  ShiftHandon: baseUrl + "/ShiftHandon",
  InShiftHandon: baseUrl + "/InShiftHandon",
  DeleteShiftHandover: baseUrl + "/DeleteShiftHandover",
  UpdateShiftSBAR: baseUrl + "/UpdateShiftSBAR",
  GetShitHandoverSBAR: baseUrl + "/GetShitHandoverSBAR",
};
//获取交班表格数据
export const GetShiftHandoffSBARTableList = (params) => {
  return http.get(urls.GetShiftHandoffSBARTableList, params);
};

//获取接班表格数据
export const GetShiftHandonSBARTableList = (params) => {
  return http.get(urls.GetShiftHandonSBARTableList, params);
};
//班别交班
export const ShiftHandoff = (params) => {
  return http.post(urls.ShiftHandoff, params);
};
//班内交班
export const InShiftHandoff = (params) => {
  return http.post(urls.InShiftHandoff, params);
};
//班别接班
export const ShiftHandon = (params) => {
  return http.post(urls.ShiftHandon, params);
};
//班内接班
export const InShiftHandon = (params) => {
  return http.post(urls.InShiftHandon, params);
};
//交班删除
export const DeleteShiftHandover = (params) => {
  return http.get(urls.DeleteShiftHandover, params);
};
//交班记录修改SBAR
export const UpdateShiftSBAR = (params) => {
  return http.post(urls.UpdateShiftSBAR, params);
};
//初始化已交班记录
export const GetShitHandoverSBAR = (params) => {
  return http.get(urls.GetShitHandoverSBAR, params);
};
