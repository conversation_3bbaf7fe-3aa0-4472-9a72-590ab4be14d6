/*
 * FilePath     : \src\api\AutoTrack.js
 * Author       : 孟昭永
 * Date         : 2023-11-16 15:41
 * LastEditors  : 孟昭永
 * LastEditTime : 2023-11-22 11:18
 * Description  : 行为日志自动跟踪上报的API
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
const baseUrl = "/AutoTrack";

export const urls = {
  ReportTrackPageView: baseUrl + "/ReportTrackPageView",
  GetTrackPageViewList: baseUrl + "/GetTrackPageViewList",
  GetPageView: baseUrl + "/GetPageView"
};
//上报前端埋点跟踪的页面浏览行为数据
export const ReportTrackPageView = params => {
  return http.post(urls.ReportTrackPageView, params);
};
//获取前端埋点跟踪的页面
export const GetTrackPageViewList = params => {
  return http.get(urls.GetTrackPageViewList, params);
};
//获取页面访问量
export const GetPageView = params => {
  return http.get(urls.GetPageView, params);
};
