<!--
 * FilePath     : \src\pages\recordSupplement\nursingRecord\components\recordTable.vue
 * Author       : 来江禹
 * Date         : 2022-11-10 15:52
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-10 17:38
 * Description  :
 * CodeIterationRecord: 2023-07-14 3581 作为护理人员，我需要在记录补录画面可以补录健康教育单和翻身记录单，以利出院患者病历补录完整 -杨欣欣
-->
<template>
  <div>
    <el-tooltip v-if="rowData.bringTPRTime" :content="rowData.bringTPRTime">
      <color-picker v-if="rowData.controlerType == 'P'" v-model="rowData.value" />
      <span v-else-if="rowData.controlerType == 'C'">
        {{ rowData.value ? "√" : "" }}
      </span>
      <div v-else-if="rowData.controlerType == 'MDL'" class="omit-if-overflow">
        {{ rowData.subItems.map(({ label }) => label).join("，") }}
      </div>
      <div v-else-if="rowData.controlerType == 'DL'" class="omit-if-overflow">
        {{ rowData.subItems[0].label }}
      </div>
      <div v-else :class="[cssClass, 'omit-if-overflow']">
        {{ rowData.value }}
      </div>
    </el-tooltip>
    <template v-else>
      <color-picker v-if="rowData.controlerType == 'P'" v-model="rowData.value" />
      <span v-else-if="rowData.controlerType == 'C'">
        {{ rowData.value ? rowData.value : "" }}
      </span>
      <div v-else-if="rowData.controlerType == 'MDL'" class="omit-if-overflow">
        {{ rowData.subItems.map(({ label }) => label).join("，") }}
      </div>
      <div v-else-if="rowData.controlerType == 'DL'" class="omit-if-overflow">
        {{ rowData.subItems[0].label }}
      </div>
      <div v-else :class="[cssClass, 'omit-if-overflow']">
        <el-tooltip :content="rowData.value">
          <span>{{ rowData.value }}</span>
        </el-tooltip>
      </div>
    </template>
  </div>
</template>
<script>
import colorPicker from "@/components/colorPicker/colorPicker";
export default {
  components: {
    colorPicker,
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    cssClass: {
      type: String,
      default: "",
    },
  },
};
</script>