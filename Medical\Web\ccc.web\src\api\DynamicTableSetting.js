
import http from "../utils/ajax";
const baseUrl = "/DynamicTableSetting";
export const url = {
  GetDynamicTableList: baseUrl + "/GetDynamicTableList",
  RecoverDynamicTableSetting: baseUrl + "/RecoverDynamicTableSetting",
  SaveDynamicTableSettingList: baseUrl + "/SaveDynamicTableSettingList",
  GetDynamicTableHeader: baseUrl + "/GetDynamicTableHeader"
}
/**
 * @description: 获取表格动态列配置
 * @param params
 * @return
 */
export const GetDynamicTableList = params => {
  return http.get(url.GetDynamicTableList, params)
}
/**
 * @description: 恢复默认设置
 * @param params
 * @return
 */
export const RecoverDynamicTableSetting = params => {
  return http.post(url.RecoverDynamicTableSetting, params)
}
/**
 * @description: 保存方法
 * @param params
 * @return
 */
export const SaveDynamicTableSettingList = params => {
  return http.post(url.SaveDynamicTableSettingList, params)
}
/**
 * @description: 获取动态表头数据
 * @param params
 * @return
 */
export const GetDynamicTableHeader = params => {
  return http.get(url.GetDynamicTableHeader, params)
}

