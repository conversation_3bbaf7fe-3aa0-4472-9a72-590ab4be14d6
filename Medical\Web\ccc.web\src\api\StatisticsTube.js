/*
 * FilePath     : \src\api\StatisticsTube.js
 * Author       : 马超
 * Date         : 2023-11-27 17:36
 * LastEditors  : 马超
 * LastEditTime : 2023-11-30 17:15
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
const baseUrl = "/statisticsTube";

export const urls = {
  GetPatientTubeRecord: baseUrl + "/GetPatientTubeRecord",
  GetTubeDictionary: baseUrl + "/GetTubeDictionary",
  GetStatisTubeAssessView: baseUrl + "/GetStatisTubeAssessView"
};

// 获取护理单元
export const GetPatientTubeRecord = params => {
  return http.get(urls.GetPatientTubeRecord, params);
};
//获取导管字典
export const GetTubeDictionary = params => {
  return http.get(urls.GetTubeDictionary, params);
};
//获取导管字典
export const GetStatisTubeAssessView = params => {
  return http.get(urls.GetStatisTubeAssessView, params);
};
