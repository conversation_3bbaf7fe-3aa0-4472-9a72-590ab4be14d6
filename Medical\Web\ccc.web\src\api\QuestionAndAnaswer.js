import http from '../utils/ajax'
import qs from "qs";
const baseUrl = '/QuestionAndAnaswer'

export const urls = {
  GetQuestionAndAnaswers: baseUrl + "/GetQuestionAndAnaswers",
  SaveQuestionAndAnaswer: baseUrl + "/SaveQuestionAndAnaswer",
  UpdateQuestionAndAnaswer: baseUrl + "/UpdateQuestionAndAnaswer",
  DeleteQuestionAndAnaswer: baseUrl + "/DeleteQuestionAndAnaswer",
}
// 获取错误问题记录
export const GetQuestionAndAnaswers = () => {
  return http.get(urls.GetQuestionAndAnaswers)
}
//保存错误记录
export const SaveQuestionAndAnaswer = (params) => {
  return http.post(urls.SaveQuestionAndAnaswer, params)
}
//修改错误记录
export const UpdateQuestionAndAnaswer = (params) => {
  return http.post(urls.UpdateQuestionAndAnaswer, params)
}
//删除错误记录
export const DeleteQuestionAndAnaswer = (params) => {
  return http.post(urls.DeleteQuestionAndAnaswer, qs.stringify(params))
}
