/*
 * FilePath     : \ccc.web\src\utils\i18n\lang\en\index.js
 * Author       : 苏军志
 * Date         : 2021-10-30 09:33
 * LastEditors  : 孟昭永
 * LastEditTime : 2021-11-16 08:47
 * Description  : 英文语言包
 */

import common from "./common";
import patientTittle from "./components/patientTittle";
import login from "./pages/login";
import mainLayout from "./pages/mainLayout";
import io from "./pages/io/io";
import ioRecordMaintenance from "./pages/io/ioRecordMaintenance";
import ioRecord from "./pages/io/ioRecord";
import drainageStatistics from "./pages/io/drainageStatistics";
import ioStatistics from "./pages/io/ioStatistics";
import tprChart from "./pages/patientHomePage/tprChart";
import patientHomePage from "./pages/patientHomePage/patientHomePage";

export default {
  // 公共部分
  ...common,

  //====组件部分
  ...patientTittle,

  //===========下面是各画面独有，建议以页面名称为对象名=========
  // 登录画面login
  ...login,
  // 主路由画面mainLayout
  ...mainLayout,
  // 出入量主页io/index
  ...io,
  // 出入量维护画面ioRecordMaintenance
  ...ioRecordMaintenance,
  // 出入量记录ioRecord
  ...ioRecord,
  // 引流液统计
  ...drainageStatistics,
  // IO统计
  ...ioStatistics,
  ///病人主页-生命体征
  ...tprChart,
  // 病人主页
  ...patientHomePage
};
