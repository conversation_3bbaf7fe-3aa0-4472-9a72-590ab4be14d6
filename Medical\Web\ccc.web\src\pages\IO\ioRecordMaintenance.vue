<!--
 * FilePath     : \src\pages\IO\ioRecordMaintenance.vue
 * Author       : 郭鹏超
 * Date         : 2022-06-14 14:29
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-05 14:45
 * Description  : 出入水量录入数据
 * CodeIterationRecord: 2022-09-24 补录状态下增加病区、科室列 -杨欣欣
 *                      2022-10-12 3010-作为IT人员,我需要优化迭代入出量保存与导管维护记录保存相关逻辑,以利于逻辑完整与后期迭代 -苏军志
                        2022-11-28 2869-原IO弹窗迭代为专项IO跳转 -杨欣欣
-->
<template>
  <base-layout
    class="io-record-maintenance"
    header-height="auto"
    v-loading="loading"
    :element-loading-text="elementLoadingText"
  >
    <div slot="header">
      <div class="left-wrap">
        <span>{{ ioStatistics.switchLabel }}</span>
        <el-switch v-model="queryByShift" />
        <div class="where" v-show="queryByShift">
          <span class="label">{{ ioStatistics.startDate }}</span>
          <el-date-picker
            v-model="startShiftTime"
            value-format="yyyy-MM-dd"
            type="date"
            :placeholder="placeholderDate"
            class="date-picker"
          ></el-date-picker>
          <shift-selector
            :stationID="stationID"
            @select-item="changeShift($event, 'startShift')"
            v-model="startShift"
            label="班别："
            width="70"
          ></shift-selector>
          <span class="label">{{ ioStatistics.endDate }}</span>
          <el-date-picker
            v-model="endShiftTime"
            value-format="yyyy-MM-dd"
            type="date"
            :placeholder="placeholderDate"
            class="date-picker"
          ></el-date-picker>
          <shift-selector
            @select-item="changeShift($event, 'endShift')"
            :stationID="stationID"
            v-model="endShift"
            label="班别："
            width="70"
          ></shift-selector>
        </div>
        <div class="where" v-show="!queryByShift">
          <span class="label">{{ ioRecord.startTime }}</span>
          <el-date-picker
            v-model="startTime"
            value-format="yyyy-MM-dd HH:mm"
            format="yyyy-MM-dd HH:mm"
            type="datetime"
            :placeholder="placeholderDate"
            class="datetime-picker"
          ></el-date-picker>
          <span class="label">{{ ioRecord.endTime }}</span>
          <el-date-picker
            v-model="endTime"
            value-format="yyyy-MM-dd HH:mm"
            format="yyyy-MM-dd HH:mm"
            type="datetime"
            :placeholder="placeholderDate"
            class="datetime-picker"
          ></el-date-picker>
        </div>
        <station-selector
          v-if="stationID && patient"
          v-model="stationID"
          :label="label.station"
          :inpatientID="patient ? patient.inpatientID : ''"
          width="140"
        ></station-selector>
        <el-button class="query-button" icon="iconfont icon-search" @click="getRecord">
          {{ queryButton }}
        </el-button>
      </div>
    </div>
    <div class="io-record">
      <base-layout class="io-table">
        <div slot="header">
          <el-radio-group v-model="ioType" @change="getRecord">
            <el-radio-button label="I" :disabled="$route.query.ioType == 'O'">{{ addIoRecord.input }}</el-radio-button>
            <el-radio-button label="O" :disabled="$route.query.ioType == 'I'">{{ addIoRecord.output }}</el-radio-button>
          </el-radio-group>
          <div class="top-btn" v-if="!readonly">
            <el-button
              class="add-button"
              icon="iconfont icon-add"
              v-if="!(sourceID && ioType && ioKind)"
              @click="ioAdd()"
            >
              {{ button.add }}
            </el-button>
            <el-button type="primary" icon="iconfont icon-save-button " class="save-button" @click="saveData">
              {{ button.save }}
            </el-button>
          </div>
        </div>
        <el-table
          v-if="ioType == 'I'"
          ref="inputTable"
          border
          stripe
          class="input-table"
          :data="ioPutData"
          height="100%"
        >
          <el-table-column type="selection" :width="convertPX(40)" align="center"></el-table-column>
          <template v-if="supplementFlag">
            <!-- 病区 -->
            <el-table-column :label="ioRecordMaintenanceTexts.station" :width="tableWidth.station" align="center">
              <template slot-scope="scope">
                <station-selector
                  v-model="scope.row.stationID"
                  label=""
                  :inpatientID="scope.row.inpatientID"
                  :width="ioRecordMaintenanceTexts.station"
                  @change="selectRow('inputTable', scope.row)"
                  @default-select-deptID="setRowDefaultSelect($event, 'inputTable', 'departmentID')"
                  @default-select-bedID="setRowDefaultSelect($event, 'inputTable', 'bedID')"
                />
              </template>
            </el-table-column>
            <!-- 科室 -->
            <el-table-column :label="ioRecordMaintenanceTexts.department" :width="tableWidth.department" align="center">
              <template slot-scope="scope">
                <dept-selector
                  v-model="scope.row.departmentID"
                  label=""
                  :stationID="scope.row.stationID"
                  :width="ioRecordMaintenanceTexts.department"
                  @change="selectRow('inputTable', scope.row)"
                />
              </template>
            </el-table-column>
            <!-- 床位 -->
            <el-table-column :label="ioRecordMaintenanceTexts.bed" :width="tableWidth.bed" align="center">
              <template slot-scope="scope">
                <bed-selector
                  v-model="scope.row.bedID"
                  label=""
                  :stationID="scope.row.stationID"
                  :width="ioRecordMaintenanceTexts.bed"
                  @select-item="scope.row.bedNumber = $event"
                  @change="selectRow('inputTable', scope.row)"
                />
              </template>
            </el-table-column>
          </template>
          <!-- 日期 -->
          <el-table-column
            :sortable="true"
            :sort-method="dateTimeSort"
            :label="ioRecordMaintenanceTexts.date"
            :width="tableWidth.ioDate"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal" v-formatTime="{ value: scope.row.ioDate, type: 'date' }"></span>
              <el-date-picker
                v-else
                v-model="scope.row.ioDate"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                type="date"
                class="date-picker"
                @change="selectRow('inputTable', scope.row)"
              ></el-date-picker>
            </template>
          </el-table-column>
          <!-- 时间 -->
          <el-table-column :label="ioRecordMaintenanceTexts.time" :width="tableWidth.ioTime" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal" v-formatTime="{ value: scope.row.ioTime, type: 'time' }"></span>
              <el-time-picker
                v-else
                v-model="scope.row.ioTime"
                value-format="HH:mm"
                format="HH:mm"
                class="time-picker"
                @change="selectRow('inputTable', scope.row)"
              ></el-time-picker>
            </template>
          </el-table-column>
          <!-- 类别 -->
          <el-table-column :label="ioRecordMaintenanceTexts.type" :width="tableWidth.ioKind" header-align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.id">
                {{ scope.row.intakeOutputKindName }}
              </span>
              <el-select
                v-else
                @change="
                  getIntakeOutputSettingOption(scope.row, 'inputTable');
                  clearSelectOption(scope.row, 'ioKind');
                "
                v-model="scope.row.intakeOutputKind"
                placeholder=""
              >
                <el-option
                  v-for="(item, index) in scope.row.intakeOutputKindOption"
                  :key="index"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <!-- 项目 -->
          <el-table-column
            :label="ioRecordMaintenanceTexts.item"
            :width="tableWidth.ioItem"
            header-align="center"
            align="left"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.id">
                {{ scope.row.intakeOutputSettingName }}
              </span>
              <el-select
                v-else
                :disabled="!scope.row.intakeOutputSettingOption || scope.row.intakeOutputSettingOption.length == 0"
                v-model="scope.row.selectIoItemIndex"
                @change="
                  changeItem('inputTable', scope.row);
                  clearSelectOption(scope.row, 'ioItem');
                "
                class="io-item-select"
              >
                <el-option
                  v-for="(item, index) in scope.row.intakeOutputSettingOption"
                  :key="index"
                  :label="item.intakeOutput"
                  :value="index"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            :label="ioRecordMaintenanceTexts.inputType"
            :width="tableWidth.inPutType"
            header-align="center"
            align="left"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.inPutType }}</span>
              <el-select
                v-else
                :disabled="
                  !scope.row.inPutTypeOption ||
                  scope.row.inPutTypeOption.length == 0 ||
                  scope.row.intakeOutputKind == '110'
                "
                v-model="scope.row.inPutType"
                @change="selectRow('inputTable', scope.row)"
              >
                <el-option
                  v-for="(item, index) in scope.row.inPutTypeOption"
                  :key="index"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            :label="ioRecordMaintenanceTexts.inputContent"
            :min-width="tableWidth.inPutContent"
            header-align="center"
            align="left"
          >
            <div slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.inPutContent }}</span>
              <template v-else>
                <el-input
                  v-if="ioType == 'I'"
                  type="textarea"
                  autosize
                  placeholder="请输入内容"
                  v-model="scope.row.inPutContent"
                  @change="selectRow('inputTable', scope.row)"
                ></el-input>
              </template>
            </div>
          </el-table-column>
          <el-table-column
            :label="ioRecordMaintenanceTexts.amount"
            :width="tableWidth.amount"
            header-align="center"
            align="left"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.intakeOutputVolume }}</span>
              <el-input
                v-else
                class="volume-input"
                v-model="scope.row.intakeOutputVolume"
                @change="selectRow('inputTable', scope.row)"
                @blur="validateByTextEntry(scope.row, 'intakeOutputVolume')"
              ></el-input>
              <span class="unit">{{ scope.row.unit }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="ioRecordMaintenanceTexts.remarks"
            header-align="center"
            :min-width="tableWidth.remarks"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.intakeOutputNote }}</span>
              <el-input
                v-else
                v-model="scope.row.intakeOutputNote"
                @change="selectRow('inputTable', scope.row)"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!supplementFlag"
            :label="addIoRecord.bringToNursingRecord"
            align="center"
            :width="tableWidth.bringToNursingRecord"
          >
            <template slot-scope="scope">
              <el-checkbox
                :disabled="scope.row.isTotal || readonly"
                v-model="scope.row.bringToNursingRecords"
                @change="selectRow('inputTable', scope.row)"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column :label="addIoRecord.informPhysician" align="center" :width="tableWidth.informPhysician">
            <template slot-scope="scope">
              <el-checkbox
                :disabled="scope.row.isTotal || readonly"
                v-model="scope.row.informPhysician"
                @change="selectRow('inputTable', scope.row)"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!readonly"
            :label="label.operation"
            :width="tableWidth.operation"
            align="center"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-tooltip content="复制">
                <i class="iconfont icon-copy" @click="copy(scope.row)"></i>
              </el-tooltip>
              <el-tooltip :content="tip.delete">
                <i
                  :class="['iconfont', 'icon-del', { 'not-allowed-click': scope.row.unDeleted }]"
                  @click="deleteIoRecord(scope.row.id, scope.$index, 'ioPutData')"
                ></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-if="ioType == 'O'" ref="outTable" stripe class="out-table" border :data="ioOutData" height="100%">
          <el-table-column type="selection" :width="convertPX(40)" align="center"></el-table-column>
          <template v-if="supplementFlag">
            <!-- 病区 -->
            <el-table-column
              v-if="supplementFlag"
              :label="ioRecordMaintenanceTexts.station"
              :width="tableWidth.station"
              align="center"
            >
              <template slot-scope="scope">
                <station-selector
                  v-model="scope.row.stationID"
                  label=""
                  :inpatientID="scope.row.inpatientID"
                  @change="selectRow('outTable', scope.row)"
                  @default-select-deptID="setRowDefaultSelect($event, 'outTable', 'departmentID')"
                  @default-select-bedID="setRowDefaultSelect($event, 'outTable', 'bedID')"
                />
              </template>
            </el-table-column>
            <!-- 科室 -->
            <el-table-column
              v-if="supplementFlag"
              :label="ioRecordMaintenanceTexts.department"
              :width="tableWidth.department"
              align="center"
            >
              <template slot-scope="scope">
                <dept-selector
                  v-model="scope.row.departmentID"
                  label=""
                  :stationID="scope.row.stationID"
                  @change="selectRow('outTable', scope.row)"
                />
              </template>
            </el-table-column>
            <!-- 床位 -->
            <el-table-column :label="ioRecordMaintenanceTexts.bed" :width="tableWidth.bed" align="center">
              <template slot-scope="scope">
                <bed-selector
                  v-model="scope.row.bedID"
                  label=""
                  :stationID="scope.row.stationID"
                  :width="ioRecordMaintenanceTexts.bed"
                  @select-item="scope.row.bedNumber = $event"
                  @change="selectRow('outTable', scope.row)"
                />
              </template>
            </el-table-column>
          </template>
          <el-table-column
            :sortable="true"
            :sort-method="dateTimeSort"
            :label="ioRecordMaintenanceTexts.date"
            :width="tableWidth.ioDate"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal" v-formatTime="{ value: scope.row.ioDate, type: 'date' }"></span>
              <el-date-picker
                v-else
                v-model="scope.row.ioDate"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                type="date"
                class="date-picker"
                @change="
                  selectRow('outTable', scope.row);
                  getIntakeOutputSettingOption(scope.row, 'outTable');
                "
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column :label="ioRecordMaintenanceTexts.time" :width="tableWidth.ioTime" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal" v-formatTime="{ value: scope.row.ioTime, type: 'time' }"></span>
              <el-time-picker
                v-else
                v-model="scope.row.ioTime"
                value-format="HH:mm"
                format="HH:mm"
                class="time-picker"
                @change="
                  selectRow('outTable', scope.row);
                  getIntakeOutputSettingOption(scope.row, 'outTable');
                "
              ></el-time-picker>
            </template>
          </el-table-column>
          <el-table-column :label="ioRecordMaintenanceTexts.type" :width="tableWidth.ioKind" header-align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.id">
                {{ scope.row.intakeOutputKindName }}
              </span>
              <el-select
                v-else
                @change="
                  getIntakeOutputSettingOption(scope.row, 'outTable');
                  clearSelectOption(scope.row, 'ioKind');
                "
                v-model="scope.row.intakeOutputKind"
              >
                <el-option
                  v-for="(item, index) in scope.row.intakeOutputKindOption"
                  :key="index"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            :label="ioRecordMaintenanceTexts.item"
            :min-width="tableWidth.ioItem"
            header-align="center"
            align="left"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.id">
                {{ scope.row.intakeOutputSettingName }}
              </span>
              <el-select
                v-else
                :disabled="!scope.row.intakeOutputSettingOption || scope.row.intakeOutputSettingOption.length == 0"
                v-model="scope.row.selectIoItemIndex"
                placeholder=""
                class="io-item-select"
                @change="
                  getIoAllOptions(
                    scope.row.selectIoItemIndex,
                    scope.row.intakeOutputSettingOption,
                    scope.row,
                    'outTable'
                  );
                  clearSelectOption(scope.row, 'ioItem');
                "
              >
                <el-option
                  v-for="(item, index) in scope.row.intakeOutputSettingOption"
                  :key="index"
                  :label="item.intakeOutput"
                  :value="index"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            :label="ioRecordMaintenanceTexts.numberOfTimes"
            :width="tableWidth.intakeOutputTimes"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.intakeOutputTimes }}</span>
              <el-input
                v-else
                v-model="scope.row.intakeOutputTimes"
                :disabled="
                  !scope.row.intakeOutputKind ||
                  !(scope.row.intakeOutputKind == '210' || scope.row.intakeOutputKind == '220')
                "
                @change="selectRow('outTable', scope.row)"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            :label="ioRecordMaintenanceTexts.amount"
            :width="tableWidth.amount"
            header-align="center"
            align="left"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.intakeOutputVolume }}</span>
              <el-input
                v-else
                class="volume-input"
                v-model="scope.row.intakeOutputVolume"
                @change="selectRow('outTable', scope.row)"
                @blur="validateByTextEntry(scope.row, 'intakeOutputVolume')"
              ></el-input>
              <span class="unit">{{ scope.row.unit ? scope.row.unit : addIoRecord.unit }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="ioRecordMaintenanceTexts.character" :width="tableWidth.character" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.characteristicID }}</span>
              <el-select
                v-else
                :disabled="!scope.row.characteristicOption || scope.row.characteristicOption.length == 0"
                v-model="scope.row.characteristicID"
                @change="selectRow('outTable', scope.row)"
              >
                <el-option
                  v-for="(item, index) in scope.row.characteristicOption"
                  :key="index"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="ioRecordMaintenanceTexts.smell" :width="tableWidth.smell" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.smellID }}</span>
              <el-select
                v-else
                :disabled="!scope.row.smellOption || scope.row.smellOption.length == 0"
                v-model="scope.row.smellID"
                @change="selectRow('outTable', scope.row)"
              >
                <el-option
                  v-for="(item, index) in scope.row.smellOption"
                  :key="index"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="ioRecordMaintenanceTexts.color" :width="tableWidth.color" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.color }}</span>
              <color-picker
                v-else
                :colorArray="scope.row.colorOption"
                v-model="scope.row.color"
                width="100px"
                @change="
                  (item) => {
                    if (item) {
                      selectRow('outTable', scope.row);
                    }
                  }
                "
              />
            </template>
          </el-table-column>
          <el-table-column
            :label="ioRecordMaintenanceTexts.remarks"
            header-align="center"
            :min-width="tableWidth.remarks"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isTotal">{{ scope.row.intakeOutputNote }}</span>
              <el-input
                v-else
                v-model="scope.row.intakeOutputNote"
                @change="selectRow('outTable', scope.row)"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!supplementFlag"
            :label="addIoRecord.bringToNursingRecord"
            align="center"
            :width="tableWidth.bringToNursingRecord"
          >
            <template slot-scope="scope">
              <el-checkbox
                :disabled="scope.row.isTotal || readonly"
                v-model="scope.row.bringToNursingRecords"
                @change="selectRow('outTable', scope.row)"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column :label="addIoRecord.informPhysician" align="center" :width="tableWidth.informPhysician">
            <template slot-scope="scope">
              <el-checkbox
                :disabled="scope.row.isTotal || readonly"
                v-model="scope.row.informPhysician"
                @change="selectRow('outTable', scope.row)"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!readonly"
            :label="label.operation"
            :width="tableWidth.operation"
            fixed="right"
            align="center"
          >
            <template slot-scope="scope">
              <el-tooltip content="复制">
                <i class="iconfont icon-copy" @click="copy(scope.row)"></i>
              </el-tooltip>
              <el-tooltip :content="tip.delete">
                <i
                  :class="['iconfont', 'icon-del', { 'not-allowed-click': scope.row.unDeleted }]"
                  @click="deleteIoRecord(scope.row.id, scope.$index, 'ioOutData')"
                ></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </base-layout>
    </div>
  </base-layout>
</template>

<script>
import { mapGetters } from "vuex";
import shiftSelector from "@/components/selector/shiftSelector";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import colorPicker from "@/components/colorPicker/colorPicker";
import baseLayout from "@/components/BaseLayout";
import { GetIOItem, GetClinicSettingByTypeCode, GetIOSetting } from "@/api/Setting";
import { GetNowStationShiftData } from "@/api/StationShift";
import { Save, Delete, GetIntakeOutputRecord, GetIntakeOutputRecordView, GetIoAllOption } from "@/api/IO";
import bedSelector from "@/components/selector/bedSelector";
import { GetTextEntriesByRecordsCode } from "@/api/TextEntry";
import { validateByTextEntry } from "@/utils/textEntryValidate";

export default {
  components: {
    baseLayout,
    shiftSelector,
    stationSelector,
    deptSelector,
    colorPicker,
    bedSelector,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      loading: false,
      elementLoadingText: "",
      patientScheduleMainID: undefined,
      startTime: "",
      endTime: "",
      startShift: "",
      endShift: "",
      startShiftTime: "",
      endShiftTime: "",
      queryByShift: true,
      shiftInfo: undefined,
      stationID: undefined,
      sourceID: undefined,
      sourceType: undefined,
      ioOutData: [],
      ioPutData: [],
      addIoView: [],
      // 患者信息
      patient: undefined,
      // 补录标记
      supplementFlag: "",
      ioType: "I",
      readonly: false,
      // 作为跳转页会传递进来，若指定，类别列将只有一类选项
      ioKind: undefined,
      // 从导管跳转而来
      tubeRecordID: undefined,
      tubeCareMainID: undefined,
      //从伤口专项跳转
      patientWoundRecordID: undefined,
      patientWoundCareMainID: undefined,
      woundKind: undefined,
      ioSettingList: [],
      // 上下限配置
      volumeTextEntries: [],
      //TextEntry检核函数
      validateByTextEntry,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      inpatient: "getPatientInfo",
    }),
    placeholder() {
      return this.$t("placeholder");
    },
    label() {
      return this.$t("label");
    },
    button() {
      return this.$t("button");
    },
    loadingText() {
      return this.$t("loadingText");
    },
    tip() {
      return this.$t("tip");
    },
    ioRecordMaintenanceTexts() {
      return this.$t("ioRecordMaintenance");
    },
    addIoRecord() {
      return this.$t("ioRecordMaintenance.addIoRecord");
    },
    ioRecord() {
      return this.$t("ioRecord");
    },
    tableWidth() {
      return this.$t("ioRecordMaintenance.tableWidth");
    },
    placeholderDate() {
      return this.$t("placeholder.date");
    },
    ioStatistics() {
      return this.$t("ioStatistics");
    },
    queryButton() {
      return this.$t("button.query");
    },
    isLinkForm() {
      return Boolean(this.$route.query.sourceID);
    },
  },
  watch: {
    //在院病人信息
    "inpatient.inpatientID": {
      async handler(newVal) {
        if (!newVal) return;
        this.supplementFlag = false;
        this.patient = this.inpatient;
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      async handler(newVal) {
        if (!newVal) return;
        this.supplementFlag = true;
        this.patient = this.supplemnentPatient;
      },
      immediate: true,
    },
    "patient.inpatientID": {
      async handler(newVal) {
        if (!newVal) return;
        this.ioType = this.$route.query.ioType || "I";
        this.ioKind = this.$route.query.ioKind;
        this.getIoAddView();
        await this.getRecord();
        // 切换病人后，重置相关缓存
        this._common.session("IOItem", {});
        this._common.session("IOInputType", []);
      },
      immediate: true,
    },
    stationID: {
      async handler(newV) {
        if (!newV) return;
        await this.initQueryParams(true);
        await this.getRecord();
      },
    },
    queryByShift: {
      async handler() {
        await this.initQueryParams();
        await this.getRecord();
      },
    },
  },
  async mounted() {
    // 进入时，重置相关缓存
    this._common.session("IOItem", {});
    this._common.session("IOInputType", []);
    this.getIoTextEntriesByRecordsCode();
    this.stationID = this.user.stationID;
    this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
    this.sourceID = this.$route.query.sourceID;
    this.sourceType = this.$route.query.sourceType;
    this.readonly = this.$route.query.readonly;
    this.tubeRecordID = this.$route.query.tubeRecordID;
    this.tubeCareMainID = this.$route.query.tubeCareMainID;
    this.patientWoundRecordID = this.$route.query.patientWoundRecordID;
    this.patientWoundCareMainID = this.$route.query.patientWoundCareMainID;
    this.woundKind = this.$route.query.woundKind;
    //设置病人头是否可以切换
    if (this.patientScheduleMainID || this.sourceID) {
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
    await this.getIOSetting();
  },
  methods: {
    async changeShift(shift, typeName) {
      this[typeName] = shift?.id;
      await this.getRecord();
    },
    async initQueryParams(flag) {
      if (this.queryByShift) {
        await this.getShiftDate(flag);
      } else {
        let nowDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.startTime = nowDate + " 00:00";
        this.endTime = nowDate + " 23:59";
      }
    },
    async getShiftDate(flag) {
      let tempFlag = false;
      if (flag) {
        tempFlag = true;
      } else {
        if (this.shiftInfo) {
          let date = this.shiftInfo.shiftDate;
          this.startShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
          this.endShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
        } else {
          tempFlag = true;
        }
      }
      if (tempFlag) {
        let params = {
          stationID: this.stationID,
        };
        await GetNowStationShiftData(params).then((res) => {
          if (this._common.isSuccess(res) && res.data) {
            this.shiftInfo = res.data;
            let date = this.shiftInfo.shiftDate;
            this.startShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
            this.endShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
          }
        });
      }
    },
    async getIoAllOptions(index, options, row, type) {
      let sucOption = options[index];
      if (!sucOption) {
        this._showTip("warning", "获取颜色性状失败");
        return;
      }
      row.intakeOutputSettingID = sucOption.id;
      if (row.intakeOutputKind == "245") {
        this.$set(row, "patientWoundRecordID", sucOption.fullCode);
      }
      this.selectRow(type, row);
      let iOItemCache = this._common.session("IOItem");
      const sessionSetting = iOItemCache[sucOption.assessListID];
      if (sessionSetting) {
        this.$set(row, "colorOption", sessionSetting.colorOption);
        this.$set(row, "characteristicOption", sessionSetting.characteristicOption);
        this.$set(row, "smellOption", sessionSetting.smellOption);
      } else {
        let params = {
          assessListID: sucOption.assessListID,
        };
        await GetIoAllOption(params).then((res) => {
          if (this._common.isSuccess(res) && res.data) {
            this.$set(row, "colorOption", res.data.colorOption);
            this.$set(row, "characteristicOption", res.data.characteristicOption);
            this.$set(row, "smellOption", res.data.smellOption);
            iOItemCache[sucOption.assessListID] = res.data;
            this._common.session("IOItem", iOItemCache);
          }
        });
      }
      this.defaultTextEntry(row, sucOption.assessListID);
    },
    /**
     * description: 切换类别时，获取类别对应的项目列表
     * param {*} row 当前行数据
     * param {*} type 标记当前是什么大类，输入 or 输出
     * param {*} copyFlag 是否是复制按钮调用标记
     * return {*}
     */
    async getIntakeOutputSettingOption(row, type, copyFlag = false) {
      if (!row || !row.intakeOutputKind) {
        return;
      }
      if ((!row || !row.intakeOutputKind) && !copyFlag) {
        this._showTip("warning", "获取项目失败");
        return;
      }
      if (!row.intakeOutputKind) {
        return;
      }
      let iOItemCache = this._common.session("IOItem");
      // 对应类别已缓存，直接使用
      // 如果点击的是复制或者当前行是复制出来的数据，不重置项目
      if ((copyFlag || row.copyFlag) && iOItemCache[row.intakeOutputKind]) {
        this.$set(row, "intakeOutputSettingOption", iOItemCache[row.intakeOutputKind]);
      } else {
        let params = {
          kind: row.intakeOutputKind,
          inpatientID: this.patient.inpatientID,
          date: row.ioDate,
          time: row.ioTime,
          index: Math.random(),
        };
        await GetIOItem(params).then((response) => {
          if (this._common.isSuccess(response)) {
            if (
              row.intakeOutputSettingOption &&
              JSON.parse(JSON.stringify(row.intakeOutputSettingOption)) === JSON.parse(JSON.stringify(response.data))
            ) {
              return;
            }
            if (!copyFlag && !row.id) {
              this.$set(row, "selectIoItemIndex", undefined);
              this.$set(row, "intakeOutputSettingID", undefined);
              this.clearSelectOption(row, "ioItem");
            }
            this.$set(row, "intakeOutputSettingOption", response.data);
            // 添加一个缓存数据，key为kind
            iOItemCache[row.intakeOutputKind] = response.data;
            this._common.session("IOItem", iOItemCache);
          }
        });
      }

      // 弹窗跳转有传tubeRecordID，过滤下拉项目、自动选中当前项
      if (this.tubeRecordID) {
        const option = row.intakeOutputSettingOption.find((option) => option.fullCode == this.tubeRecordID);
        if (option) {
          this.$set(row, "intakeOutputSettingOption", [option]);
        }
      }
      //由伤口跳转而来，若为主记录跳转，插入一条项目选项
      if (this.patientWoundRecordID) {
        //patientWoundRecordID包含temp，说明此时伤口主记录还未保存，插入一条该伤口的项目
        if (this.woundKind && this.ioSettingList && this.patientWoundRecordID.includes("temp")) {
          let ioSetting = this.ioSettingList.find((m) => m.fullCode == this.woundKind);
          if (ioSetting) {
            row.intakeOutputSettingOption.unshift({
              id: ioSetting.id,
              intakeOutput: ioSetting.intakeOutput,
              fullCode: this.patientWoundRecordID,
            });
          }
        }
        //下拉项目只能选择跳转过来的伤口，避免护士选择其他的伤口
        const option = row.intakeOutputSettingOption.find((option) => option.fullCode == this.patientWoundRecordID);
        if (option) {
          this.$set(row, "intakeOutputSettingOption", [option]);
        }
      }
      await this.getInputOption(row);
    },
    /**
     * description:临时方法，处理妇幼危重单数据源，后续调整
     * param {*}
     * return {*}
     */
    async getInputOption(row) {
      let option = [];
      let optionTemp = [];
      let setting = [];
      let sessionSetting = this._common.session("IOInputType");
      if (sessionSetting && sessionSetting.length > 0) {
        setting = sessionSetting;
      } else {
        let params = {
          settingTypeCode: "InputType",
        };
        await GetClinicSettingByTypeCode(params).then((res) => {
          if (this._common.isSuccess(res)) {
            if (res.data) {
              setting = res.data;
              this._common.session("IOInputType", res.data);
            }
          }
        });
      }
      setting.forEach((element) => {
        if (element.typeValue.includes("111")) {
          let optionItem = {
            key: element.sort,
            value: element.description,
          };
          option.push(optionItem);
        }
        if (element.typeValue.includes("Intravenous")) {
          let optionTempItem = {
            key: element.sort,
            value: element.description,
          };
          optionTemp.push(optionTempItem);
        }
      });
      if (row.intakeOutputKind == "111") {
        this.$set(row, "inPutTypeOption", option);
      } else {
        this.$set(row, "inPutTypeOption", optionTemp);
      }
    },
    /**
     * description:获取IO新增空白View
     * param {*}
     * return {*}
     */
    getIoAddView() {
      let params = {
        inpatientID: this.patient.inpatientID,
        ioKind: this.ioKind,
        ioType: this.ioType,
      };

      GetIntakeOutputRecordView(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.addIoView = res.data;
        }
      });
    },
    /**
     * description: 新增记录
     * param {*} copyIORow
     * return {*}
     */
    ioAdd(copyIORow) {
      if (this.ioType == "I") {
        this.ioPutAdd(copyIORow);
      }
      if (this.ioType == "O") {
        this.ioOutAdd(copyIORow);
      }
    },
    /**
     * description: 新增出量记录
     * param {*} copyIORow
     * return {*}
     */
    ioOutAdd(copyIORow) {
      if (!this.addIoView.outList || !this.addIoView.outList.length) {
        this._showTip("warning", "新增失败");
        return;
      }
      let row = this._common.clone(this.addIoView.outList[0]);

      row.ioDate = this._datetimeUtil.getNowDate();
      row.ioTime = this._datetimeUtil.getNowTime("hh:mm");
      if (copyIORow) {
        row = copyIORow()(row);
      }
      this.ioOutData.unshift(row);
      this.selectRow("outTable", row);
    },
    /**
     * description: 新增入量记录
     * param {*} copyIORow
     * return {*}
     */
    ioPutAdd(copyIORow) {
      if (!this.addIoView.putList || !this.addIoView.putList.length) {
        this._showTip("warning", "新增失败");
        return;
      }
      let row = this._common.clone(this.addIoView.putList[0]);
      row.ioDate = this._datetimeUtil.getNowDate();
      row.ioTime = this._datetimeUtil.getNowTime("hh:mm");
      if (copyIORow) {
        row = copyIORow()(row);
      }
      this.ioPutData.unshift(row);
      this.selectRow("inputTable", row);
    },
    /**
     * description:复制按钮方法
     * param {*} row
     * return {*}
     */
    async copy(row) {
      if (this.tubeCareMainID) {
        this._showTip("warning", "跳转页面不可删除数据，请到出入量专项操作！");
        return;
      }
      if (!row) {
        this._showTip("warning", "复制失败");
        return;
      }
      let copyIORow = undefined;
      // TODO:两个函数有重复部分，可以抽公共
      if (this.ioType == "I") {
        await this.getIntakeOutputSettingOption(row, "inputTable", true);
        copyIORow = () => {
          const copyRow = row;
          return function (addRow) {
            if (copyRow.intakeOutputKind) {
              addRow.intakeOutputKind = Number(copyRow.intakeOutputKind);
            }
            addRow.IntakeOutputSettingID = copyRow.intakeOutputSettingID;
            addRow.inPutType = copyRow.inPutType;
            addRow.inPutContent = copyRow.inPutContent;
            addRow.intakeOutputVolume = copyRow.intakeOutputVolume;
            addRow.IntakeOuputNote = copyRow.intakeOutputNote;
            addRow.unit = copyRow.unit;
            addRow.supplementFlag = copyRow.supplementFlag;
            addRow.bringToNursingRecords = copyRow.bringToNursingRecords;
            addRow.intakeOutputNote = copyRow.intakeOutputNote;
            addRow.informPhysician = copyRow.informPhysician;
            addRow.intakeOutputSettingID = row.intakeOutputSettingID;
            addRow.intakeOutputSettingName = copyRow.intakeOutputSettingName;
            addRow.intakeOutputSettingOption = copyRow.intakeOutputSettingOption;
            addRow.copyFlag = true;
            if (copyRow.intakeOutputSettingOption?.length > 0) {
              addRow.selectIoItemIndex = addRow.intakeOutputSettingOption.findIndex(
                (m) => m.id == addRow.intakeOutputSettingID
              );
            }
            return addRow;
          };
        };
      }
      if (this.ioType == "O") {
        await this.getIntakeOutputSettingOption(row, "outTable", true);
        copyIORow = () => {
          const copyRow = row;
          return function (addRow) {
            if (copyRow.intakeOutputKind) {
              addRow.intakeOutputKind = Number(copyRow.intakeOutputKind);
            }
            addRow.intakeOutputKindName = copyRow.intakeOutputKindName;
            addRow.intakeOutputSettingID = copyRow.intakeOutputSettingID;
            addRow.intakeOutputSettingName = copyRow.intakeOutputSettingName;
            addRow.intakeOutputTimes = copyRow.intakeOutputTimes;
            addRow.intakeOutputVolume = copyRow.intakeOutputVolume;
            addRow.characteristicID = copyRow.characteristicID;
            addRow.characteristicOption = copyRow.characteristicOption;
            addRow.smellID = copyRow.smellID;
            addRow.smellOption = copyRow.smellOption;
            addRow.color = copyRow.color;
            addRow.colorOption = copyRow.colorOption;
            addRow.intakeOutputNote = copyRow.intakeOutputNote;
            addRow.intakeOutputVolume = copyRow.intakeOutputVolume;
            addRow.IntakeOuputNote = copyRow.intakeOutputNote;
            addRow.unit = copyRow.unit;
            addRow.supplementFlag = copyRow.supplementFlag;
            addRow.bringToNursingRecords = copyRow.bringToNursingRecords;
            addRow.intakeOutputNote = copyRow.intakeOutputNote;
            addRow.informPhysician = copyRow.informPhysician;
            addRow.intakeOutputSettingID = copyRow.intakeOutputSettingID;
            addRow.intakeOutputSettingOption = copyRow.intakeOutputSettingOption;
            addRow.copyFlag = true;
            if (copyRow.intakeOutputSettingOption?.length > 0) {
              addRow.selectIoItemIndex = addRow.intakeOutputSettingOption.findIndex(
                (m) => m.id == addRow.intakeOutputSettingID
              );
            }
            return addRow;
          };
        };
      }
      this.ioAdd(copyIORow);
    },
    async getRecord() {
      this.ioOutData = [];
      this.ioPutData = [];
      if (this.loading || !this.patient) {
        return;
      }
      if (this.queryByShift && (!this.startShiftTime || !this.endShiftTime || !this.startShift || !this.endShift)) {
        this.loading = false;
        return;
      }
      if (!this.queryByShift && (!this.startTime || !this.endTime)) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        stationID: this.stationID,
        ioType: this.ioType,
        index: Math.random(),
        sourceID: this.sourceID?.replace("temp", ""),
        ioKind: this.ioKind,
      };
      if (this.queryByShift) {
        params.startTime = this.startShiftTime;
        params.endTime = this.endShiftTime;
        params.startShift = this.startShift;
        params.endShift = this.endShift;
      } else {
        params.startTime = this.startTime;
        params.endTime = this.endTime;
      }
      this.elementLoadingText = this.loadingText.load;
      this.loading = true;
      await GetIntakeOutputRecord(params).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response) && response.data) {
          if (this.ioType == "I") {
            this.ioPutData = response.data.putList;
            // 若是跳转页面，且返回数据为空，则自动新增一行
            this.sourceID && this.ioPutData.length === 0 && this.ioAdd();
            this.$nextTick(() => {
              this.$refs.inputTable && this.$refs.inputTable.doLayout();
            });
          }
          if (this.ioType == "O") {
            this.ioOutData = response.data.outList;
            // 若是跳转页面，且返回数据为空，则自动新增一行
            this.sourceID && this.ioOutData.length === 0 && this.ioAdd();
            this.$nextTick(() => {
              this.$refs.outTable && this.$refs.outTable.doLayout();
            });
          }
        }
      });
    },
    /**
     * description: 行数据异动时自动选择当前行
     * param {*} type 类型  输入 or 输出
     * param {*} row 当前异动行
     * return {*}
     */
    selectRow(type, row) {
      this.$nextTick(() => {
        let table = this.$refs[type];
        if (table) {
          table.toggleRowSelection(row, true);
        }
      });
    },
    /**
     * description: 获取异动行数据
     * param {*}
     * return {*}
     */
    getSelectData() {
      let outTable = this.$refs["outTable"];
      let outTableSelect = [];
      let inputTableSelect = [];
      if (outTable) {
        outTableSelect = outTable.selection;
      }
      let inputTable = this.$refs["inputTable"];
      if (inputTable) {
        inputTableSelect = inputTable.selection;
      }
      let data = {
        outTableRows: outTableSelect,
        inputTableRows: inputTableSelect,
      };
      return data;
    },
    /**
     * description: 批量保存
     * param {*}
     * return {*}
     */
    async saveData() {
      let data = this.getSelectData();
      let outTableRows = data.outTableRows;
      let inputTableRows = data.inputTableRows;
      if ((!outTableRows || outTableRows.length <= 0) && (!inputTableRows || inputTableRows.length <= 0)) {
        this._showTip("warning", this.ioRecordMaintenanceTexts.batchSaveTip);
        return;
      }
      let saveList = [];
      // 输出
      if (outTableRows && outTableRows.length > 0) {
        for (let i = 0; i < outTableRows.length; i++) {
          let row = outTableRows[i];
          let ret = this.checkSaveRow(row, 2);
          if (ret) {
            this._showTip("warning", "<font color='#ff00ff'>输出记录的</font>第" + (i + 1) + "行，" + ret);
            return;
          }
          saveList.push(this.createSaveParam(row, 2));
        }
      }
      // 输入
      if (inputTableRows && inputTableRows.length > 0) {
        for (let i = 0; i < inputTableRows.length; i++) {
          let row = inputTableRows[i];
          let ret = this.checkSaveRow(row, 1);
          if (ret) {
            this._showTip("warning", "<font color='#ff00ff'>输入记录的</font>第" + (i + 1) + "行，" + ret);
            return;
          }
          saveList.push(this.createSaveParam(row, 1));
        }
      }
      await this.batchSave(saveList);
    },
    /** 批量保存
     * description:
     * param {*} saveList
     * return {*}
     */
    async batchSave(saveList) {
      if (!saveList || saveList.length <= 0) {
        return;
      }
      this.elementLoadingText = this.loadingText.save;
      this.loading = true;
      let params = {
        ioList: saveList,
        inPatient: {
          id: this.inpatient.inpatientID,
          patientID: this.inpatient.patientID,
          chartNo: this.inpatient.chartNo,
          caseNumber: this.inpatient.caseNumber,
          nursingLevel: this.inpatient.nursingLevelCode,
          stationID: this.inpatient.stationID,
          departmentListID: this.inpatient.departmentListID,
          bedID: this.inpatient.bedID,
          bedNumber: this.inpatient.bedNumber
        }
      };
      
      await Save(params).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          this._showTip("success", this.loadingText.saveSuccess);
          this.getRecord();
        } else {
          this._showTip("warning", "保存失败");
        }
      }).catch(() => {
        this.loading = false;
        this._showTip("warning", "保存失败");
      });
    },
    /**
     * description: 记录删除
     * param {*} id
     * param {*} index
     * param {*} tableName
     * return {*}
     */
    async deleteIoRecord(id, index, tableName) {
      if (this.tubeCareMainID) {
        this._showTip("warning", "跳转页面不可删除数据，请到出入量专项操作！");
        return;
      }
      if (!id && !index && index != 0) {
        this._showTip("warning", "删除失败");
        return;
      }
      this._deleteConfirm(this.tip.deleteConfirm, (flag) => {
        if (flag) {
          if (id) {
            this.loading = true;
            let params = { id: id, supplementFlag: this.supplementFlag };
            Delete(params).then((response) => {
              this.loading = false;
              if (this._common.isSuccess(response)) {
                this._showTip("success", "删除成功");
                this.getRecord();
              }
            });
          } else {
            this[tableName].splice(index, 1);
            this._showTip("success", "删除成功");
          }
        }
      });
    },
    /**
     * description: 检核保存数据完整性
     * param {*} row 数据行
     * param {*} selectIoType 类型 1输入  2输出
     * return {*}
     */
    checkSaveRow(row, selectIoType) {
      //请选择项目！
      if (!row.intakeOutputKind || (row.intakeOutputSettingOption?.length && !row.id && !row.intakeOutputSettingID)) {
        return this.addIoRecord.saveTipItem;
      }
      if (
        selectIoType == 2 &&
        (row.intakeOutputKind == "210" || row.intakeOutputKind == "220") &&
        !row.intakeOutputVolume &&
        !row.intakeOutputTimes &&
        !row.characteristicID &&
        !row.smellID &&
        !row.intakeOutputNote &&
        !row.color &&
        row.intakeOutputSettingOption &&
        row.intakeOutputSettingOption.length
      ) {
        let setting = row.intakeOutputSettingOption[row.selectIoItemIndex];
        if (setting && !setting.nullEnable) {
          //【颜色】、【性状】、【气味】、【量】、【次数】、【备注】必须录入一个！
          return this.addIoRecord.saveTipAttribute;
        }
      }
      // 【科室】不可为空
      if (this.supplementFlag && !row.departmentID) {
        return this.ioRecordMaintenanceTexts.noDeptTip;
      }
      if (row.intakeOutputTimes && isNaN(row.intakeOutputTimes)) {
        // 【次数】请输入数值
        return this.addIoRecord.saveTipTimes1;
      }
      if (row.intakeOutputTimes < 0) {
        //次数不能为负值！
        return this.addIoRecord.saveTipTimes2;
      }
      if (row.intakeOutputTimes && !this._regularNumber(row.intakeOutputTimes)) {
        //【次数】请输入正整数
        return this.addIoRecord.saveTipTimes3;
      }
      if (row.intakeOutputVolume && isNaN(row.intakeOutputVolume)) {
        //【量】请输入数值
        return this.addIoRecord.saveTipAmount1;
      }
      if (row.intakeOutputVolume && !this._regularDecimals(row.intakeOutputVolume)) {
        //【量】请输入数值且最多两位小数
        return this.addIoRecord.saveTipAmount2;
      }
      return "";
    },
    /**
     * description: 组装保存参数
     * param {*} row 数据行
     * param {*} selectIoType 类型 1输入  2输出
     * return {*}
     */
    createSaveParam(row, selectIoType) {
      let params = {
        ID: row.id,
        InpatientID: row.inpatientID,
        DepartmentID: row.departmentID ? row.departmentID : 0,
        StationID: row.stationID ? row.stationID : 0,
        BedID: row.bedID ? row.bedID : 0,
        BedNumber: row.bedNumber,
        ioDate: row.ioDate,
        ioTime: row.ioTime,
        intakeOutputKind: row.intakeOutputKind,
        IntakeOutputVolume: row.intakeOutputVolume,
        IntakeOuputNote: row.intakeOutputNote,
        BringToNursingRecords: row.bringToNursingRecords,
        InformPhysician: row.informPhysician,
        SupplementFlag: this.supplementFlag,
        SourceID: row.sourceID || this.sourceID,
        SourceType: this.sourceType ? this.sourceType : "IO",
        NursingLevel: this.patient.nursingLevelCode,
        sourceID: this.patientWoundRecordID,
        index: Math.random(),
        isLinkForm: this.isLinkForm,
      };
      // 排程串进来，写入PatientScheduleMainID
      if (this.patientScheduleMainID) {
        params.PatientScheduleMainID = this.patientScheduleMainID;
      }
      if (row.intakeOutputSettingID) {
        params.IntakeOutputSettingID = row.intakeOutputSettingID;
      }
      if (selectIoType == 1) {
        params.InPutType = row.inPutType;
        params.InPutContent = row.inPutContent;
        //换行字符串替换为"+"
        if (params.InPutContent && params.InPutContent.includes("\n")) {
          params.InPutContent = params.InPutContent.replace(/\n/g, "+");
        }
      } else {
        params.Color = row.color;
        params.CharacteristicID = row.characteristicID;
        params.SmellID = row.smellID;
        params.IntakeOutputTimes = row.intakeOutputTimes;
        // 关联导管字段处理
        if (row.intakeOutputKind == "240" || row.intakeOutputKind == "210") {
          //修改
          if (row.id) {
            params.PatientTubeRecordID = row.patientTubeRecordID;
            params.PatientTubeCareMainID = row.patientTubeCareMainID;
          } else {
            params.PatientTubeRecordID =
              this.tubeRecordID || row?.intakeOutputSettingOption?.length
                ? row.intakeOutputSettingOption[row.selectIoItemIndex].fullCode
                : "";
            params.PatientTubeCareMainID = this.tubeCareMainID;
          }
        }
        //伤口
        if (row.intakeOutputKind == "245" && !params.sourceID) {
          params.sourceType = "Wound";
        }
        // 取气味显示名称
        if (row.smellID) {
          let smell = row.smellOption.find((smell) => smell.key == row.smellID);
          if (smell) {
            row.smell = smell.value;
            params.Smell = smell.value;
          }
        }
        // 取性状显示名称
        if (row.characteristicID) {
          let characteristic = row.characteristicOption.find(
            (characteristic) => characteristic.key == row.characteristicID
          );
          if (characteristic) {
            row.characteristic = characteristic.value;
            params.Characteristic = characteristic.value;
          }
        }
      }
      return params;
    },
    /**
     * description: 表格数据排序
     * param {*} row1
     * param {*} row2
     * return {*}
     */
    dateTimeSort(row1, row2) {
      //数据有问题默认正序
      if (!row1 || !row2 || !row1.ioDate || !row1.ioTime || !row2.ioDate || !row2.ioTime) {
        return -1;
      }
      let dateTime1 =
        this._datetimeUtil.formatDate(row1.ioDate, "yyyy-MM-dd") +
        " " +
        this._datetimeUtil.formatDate(row1.ioTime, "hh:mm");
      let dateTime2 =
        this._datetimeUtil.formatDate(row2.ioDate, "yyyy-MM-dd") +
        " " +
        this._datetimeUtil.formatDate(row2.ioTime, "hh:mm");
      if (this._datetimeUtil.getTimeDifference(dateTime2, dateTime1, undefined, "M") > 0) {
        return -1;
      } else {
        return 1;
      }
    },
    /**
     * description: 切换项目时，补充SettingID
     * param {*} type
     * param {*} row
     * return {*}
     */
    changeItem(type, row) {
      let setting = row.intakeOutputSettingOption[row.selectIoItemIndex];
      if (setting) {
        this.$set(row, "intakeOutputSettingID", setting.id);
        this.defaultTextEntry(row, setting.assessListID);
      }
      this.selectRow(type, row);
    },
    /**
     * description: 切换类别、项目时，清空性状、气味、[项目]选中项目
     * param {*} row 当前行数据
     * param {*} changeAttrName 切换的列名
     * return {*}
     */
    clearSelectOption(row, changeAttrName) {
      // 切换类别，项目也重置
      if (changeAttrName === "ioKind") {
        this.$set(row, "selectIoItemIndex", undefined);
      }
      row.inPutType = undefined;
      row.characteristicID = undefined;
      row.smellID = undefined;
      row.color = undefined;
    },
    setRowDefaultSelect(defaultSelectValue, recordType, attrName) {
      const tableData = recordType === "inputTable" ? this.ioPutData : this.ioOutData;
      // 有值使用已有的，只有新增时才给默认
      if (!tableData.length || tableData[0][attrName]) {
        return;
      }
      tableData[0][attrName] = defaultSelectValue || undefined;
    },

    /**
     * description: 获取IO配置
     * return {*}
     */
    async getIOSetting() {
      let params = {
        kind: this.ioKind,
      };
      await GetIOSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.ioSettingList = response.data;
        }
      });
    },
    /**
     * description: 获取上下限配置
     * return {*}
     */
    getIoTextEntriesByRecordsCode() {
      GetTextEntriesByRecordsCode({ recordsCode: "IntakeOutput_Volume" }).then((response) => {
        if (this._common.isSuccess(response)) {
          this.volumeTextEntries = response.data;
        }
      });
    },
    /**
     * @description: 设置默认文本输入规则
     * @param row
     * @param assessListID
     * @return
     */
    defaultTextEntry(row, assessListID) {
      // 获取文本输入规则
      const volumeTextEntry = this.volumeTextEntries.find((item) => item.assessListID == assessListID);
      this.$set(row, "textEntry", !volumeTextEntry ? undefined : { intakeOutputVolume: volumeTextEntry });
      // 验证文本输入规则
      this.validateByTextEntry(row, "intakeOutputVolume");
    },
  },
};
</script>

<style lang="scss">
.io-record-maintenance {
  .left-wrap {
    float: left;
    .where {
      display: inline-block;
      .label {
        margin-left: 10px;
      }
      .date-picker {
        width: 110px;
      }
      .datetime-picker {
        width: 150px;
      }
    }
  }
  .right-wrap {
    float: right;
  }
  .io-record {
    height: 100%;
    .io-table {
      width: 100%;
      float: left;
      box-sizing: border-box;
      .top-btn {
        float: right;
      }
    }
    .date-picker {
      width: 100% !important;
    }
    .time-picker {
      width: 100% !important;
    }
    .out-table,
    .input-table {
      input {
        padding: 0 5px;
      }
      .volume-input {
        width: calc(100% - 35px);
      }
      .unit {
        width: 30px;
        font-size: 14px;
      }
      .has-gutter {
        .el-checkbox {
          display: none;
        }
      }
      .io-item-select {
        width: 100%;
      }
      .el-select {
        .el-input__inner {
          padding: 5px;
        }
        .el-input__suffix {
          right: 0;
        }
      }
      .cell {
        font-size: 13px;
      }
      //阻止图标点击事件的发生
      .not-allowed-click {
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }
}
</style>
