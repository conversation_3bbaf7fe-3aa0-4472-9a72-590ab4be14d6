import http from "../utils/ajax";
import common from "../utils/common";
import qs from "qs";
const baseUrl = "/Handover";

export const urls = {
  GetHandoverByHandoverRecordID: baseUrl + "/GetHandoverByHandoverRecordID",
  GetHandoverByChartNo: baseUrl + "/GetHandoverByChartNo",
  UpdateHandoverRecord: baseUrl + "/UpdateHandoverRecord",
  DeleteHandoverByID: baseUrl + "/DeleteHandoverByID",
  AddHandoverRecord: baseUrl + "/AddHandoverRecord",
  UpdateHandoverReduction: baseUrl + "/UpdateHandoverReduction",
  GetNurseStationID: baseUrl + "/GetNurseStationID",
  GetKeyHandOver: baseUrl + "/GetKeyHandOver",
  GetLastHandOver: baseUrl + "/GetLastHandOver",
  GetPatientVitalSign: baseUrl + "/GetPatientVitalSign",
  GetMultiHandOvers: baseUrl + "/GetMultiHandOvers",
  GetBodyImg: baseUrl + "/GetBodyImg",
  SaveSBARHandover: baseUrl + "/SaveSBARHandover",
  UpdateSBARHandover: baseUrl + "/UpdateSBARHandover",
  CheckUnHandon: baseUrl + "/CheckUnHandon",
  GetPatientHandoverView: baseUrl + "/GetPatientHandoverView",
  Handon: baseUrl + "/Handon",
  GetPatientAssessHandovers: baseUrl + "/GetPatientAssessHandovers",
  UpdateAssessHandover: baseUrl + "/UpdateAssessHandover",
  SaveAssessHandover: baseUrl + "/SaveAssessHandover",
  GetPatientSBARHandovers: baseUrl + "/GetPatientSBARHandovers",
  DelTempSaveData: baseUrl + "/DelTempSaveData",
  GetHandOver: baseUrl + "/GetHandOver",
  GetHandoverIDByOperationNo: baseUrl + "/GetHandoverIDByOperationNo",
  DeleteHandOverByHandOverID: baseUrl + "/DeleteHandOverByHandOverID",
  GetShiftHandOver: baseUrl + "/GetShiftHandOver",
  SaveShiftHandOver: baseUrl + "/SaveShiftHandOver"
};

//根据HandoverRecordID查询交接班信息
export const GetHandoverByHandoverRecordID = params => {
  return http.get(urls.GetHandoverByHandoverRecordID, params);
};
//根据chartNo查询出交班记录信息
export const GetHandoverByChartNo = params => {
  return http.get(urls.GetHandoverByChartNo, params);
};
//修改交班记录信息
export const UpdateHandoverRecord = params => {
  return http.post(urls.UpdateHandoverRecord, params);
};
//删除交班记录
export const DeleteHandoverByID = params => {
  return http.post(urls.DeleteHandoverByID, qs.stringify(params));
};
//添加一条交班记录
export const AddHandoverRecord = params => {
  return http.post(urls.AddHandoverRecord, params);
};
//还原交班记录信息
export const UpdateHandoverReduction = params => {
  return http.post(
    common.toUrl(urls.UpdateHandoverReduction, qs.stringify(params))
  );
};
//查询护士姓名
export const GetNurseStationID = params => {
  return http.get(common.toUrl(urls.GetNurseStationID, qs.stringify(params)));
};
//取得重点交班
export const GetKeyHandOver = params => {
  // return http.get(common.toUrl(urls.GetKeyHandOver, qs.stringify(params)));
  return http.get(urls.GetKeyHandOver, params);
};
//获得病人最近一次交班数据
export const GetLastHandOver = params => {
  return http.get(urls.GetLastHandOver, params);
};
//获得病人最近一次生命体征
export const GetPatientVitalSign = params => {
  return http.get(urls.GetPatientVitalSign, params);
};

//取得身体部位
export const GetBodyImg = params => {
  return http.get(urls.GetBodyImg, params);
};

export const SaveSBARHandover = params => {
  return http.post(urls.SaveSBARHandover, qs.stringify(params));
};
export const UpdateSBARHandover = params => {
  return http.post(urls.UpdateSBARHandover, qs.stringify(params));
};

//交班检核
export const CheckUnHandon = params => {
  return http.get(urls.CheckUnHandon, params);
};

//取得班别日期起讫时间交班内容
export const GetPatientHandoverView = params => {
  return http.post(urls.GetPatientHandoverView, qs.stringify(params));
};

// 获取批量交班内容
export const GetMultiHandOvers = params => {
  return http.get(urls.GetMultiHandOvers, params);
};

//接班
export const Handon = params => {
  return http.post(urls.Handon, qs.stringify(params));
};

//获取交班内容
export const GetPatientSBARHandovers = params => {
  return http.get("/Handover/GetPatientSBARHandovers", params);
};

//获取评估
export const GetPatientAssessHandovers = params => {
  return http.post(urls.GetPatientAssessHandovers, qs.stringify(params));
};
//修改评估
export const UpdateAssessHandover = params => {
  return http.post(urls.UpdateAssessHandover, params);
};
//新增评估
export const SaveAssessHandover = params => {
  return http.post(urls.SaveAssessHandover, params);
};

//删除暂存导管和压力性损伤
export const DelTempSaveData = params => {
  return http.post(urls.DelTempSaveData, qs.stringify(params));
};
//获取护士班次病人交班情况
export const GetHandOver = params => {
  return http.get(urls.GetHandOver, params);
};
// 根据手术序号获取手术交接班记录序号
export const GetHandoverIDByOperationNo = params => {
  return http.get(urls.GetHandoverIDByOperationNo, params);
};
//根据handOverID删除数据
export const DeleteHandOverByHandOverID = params => {
  return http.get(urls.DeleteHandOverByHandOverID, params);
};
//获取班别班内交接数据
export const GetShiftHandOver = params => {
  return http.get(urls.GetShiftHandOver,params);
};
//保存编辑班别班内交接数据
export const SaveShiftHandOver = params => {
  return http.post(urls.SaveShiftHandOver,params);
};
