<!--
 * FilePath     : \src\components\table\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-11-08 14:26
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-04-26 11:26
 * Description  : 表格封装组件
 参数说明:
headerList: [
   {
     label: "日期"                       表格列名
     prop: "assessDate"                  列绑定值
     align: "center"                     内容位置
     headerAlign: ""                     列名内容位置
     tableColumnWidth: "110"             列宽度
     minWidthFlag: false                 是否为min-width最小列度
     columnClassName: ""                 列class名
     columnStyle: "date"                 列内容种类
     fixedPosition: ""                   固定位置
     slotName: ""                        插槽名称
     children: []                        多级表头使用
     options: [                          下拉框 多选框使用
       {key:"",value:""}
     ]
   }
 ]
   表格列支持类型
     columnStyle:
             Date      日期选择框
             Time      时间选择框
             date      日期文字内容 自动显示为(yyyy-MM-dd)
             time      时间文字内容 自动显示为(hh:mm)
             dateTime  日期时间文字内容 自动显示为(yyyy-MM-dd hh:mm)
             text      文字(支持Html)
             select    下拉框
             input     输入框
             check     多选框
             slot      插槽 配合slotName使用 用于组件处理不了的逻辑时使用
提供方法：doLayout，直接调用el-table的doLayout方法
-->
<template>
  <div class="components-table">
    <dynamic-table-setting
      class="dynamic-table"
      v-model="dynamicTableList"
      @recoverSetting="recoverSetting"
      @saveData="saveData"
      v-if="showDynamicColumnSetting"
    ></dynamic-table-setting>
    <el-table
      ref="table"
      height="100%"
      width="100%"
      @row-click="rowClick"
      @cell-click="cellClick"
      border
      stripe
      :data="value"
      v-bind="$attrs"
      :row-class-name="getRowClassName"
      :cell-style="getCellStyle"
      header-row-class-name="main-record-header-row"
    >
      <el-table-column
        v-for="(item, index) in headerList"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :width="!item.minWidthFlag ? convertPX(item.tableColumnWidth) : item.minWidthFlag"
        :min-width="item.minWidthFlag ? convertPX(item.tableColumnWidth) : item.minWidthFlag"
        :header-align="item.headerAlign"
        :align="item.align"
        :fixed="item.fixedPosition"
        :sortable="item.sortFlag"
      >
        <template slot="header" slot-scope="scope">
          <span v-html="item.label"></span>
          <span v-if="false">{{ scope }}</span>
        </template>
        <template slot-scope="scope">
          <headerList :item="item" :scope="scope"></headerList>
          <div v-if="item.columnStyle == 'slot' && item.slotName">
            <slot :name="item.slotName" :row="scope.row"></slot>
          </div>
        </template>
        <template v-if="item.children.length">
          <el-table-column
            v-for="(childrenItem, index) in item.children"
            :key="index"
            :prop="childrenItem.prop"
            :width="!childrenItem.minWidthFlag ? convertPX(childrenItem.tableColumnWidth) : childrenItem.minWidthFlag"
            :min-width="
              childrenItem.minWidthFlag ? convertPX(childrenItem.tableColumnWidth) : childrenItem.minWidthFlag
            "
            :header-align="childrenItem.herderPosition"
            :align="childrenItem.position"
            :fixed="item.fixedPosition"
            :sortable="item.sortFlag"
          >
            <template slot="header" slot-scope="scope">
              <span v-html="childrenItem.label"></span>
              <span v-if="false">{{ scope }}</span>
            </template>
            <template slot-scope="scope">
              <headerList :item="childrenItem" :scope="scope"></headerList>
              <div v-if="childrenItem.columnStyle == 'slot' && item.slotName">
                <slot :name="childrenItem.slotName" :row="scope.row"></slot>
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import headerList from "./columnItem";
import dynamicTableSetting from "@/components/table/dynamicTableSetting.vue";
export default {
  components: {
    headerList,
    dynamicTableSetting,
  },
  props: {
    headerList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    value: {
      type: Array,
      default: () => {
        return [];
      },
    },
    dynamicTableList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    showDynamicColumnSetting: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    // 添加单元格特殊样式
    getCellStyle: {
      type: Function,
      default: () => {
        return ()=>{};
      },
    },
  },
  data() {
    return {};
  },
  watch: {
    headerList: {
      immediate: true,
      handler(newVal, oldVal) {
        this.doLayout();
      },
    },
  },
  methods: {
    //行点击事件
    rowClick(row) {
      this.$emit("rowClick", row);
    },
    /**
     * @description: 单元格点击事件
     */
    cellClick(row, column, cell, event) {
      this.$emit("cellClick", row, column, cell, event);
    },
    doLayout() {
      this.$nextTick(() => {
        if (this.$refs.table) {
          this.$refs.table.doLayout();
        }
      });
    },
    /**
     * @description: 设置行Class
     * @return
     */
    getRowClassName: ({ rowIndex }) => `row-${rowIndex} main-record-row`,
    /**
     * @description: 滚动到指定行
     * @param rowIndex 行索引，不传默认滚动到最后一行
     * @return
     */
    scrollIntoView(rowIndex = this.value.length - 1) {
      if (rowIndex < 0) {
        return;
      }
      // 滚动至指定行
      const el = this.$el.querySelector(`.row-${rowIndex}`);
      if (el) {
        el?.scrollIntoView();
      }
    },
    /**
     * @description: 恢复默认配置
     * @return
     */
    recoverSetting() {
      this.$emit("recoverSetting");
    },
    /**
     * @description: 保存数据
     * @return
     */
    saveData() {
      this.$emit("saveData", this.value);
    },
  },
};
</script>

<style lang="scss">
.components-table {
  height: 100%;
  .dynamic-table {
    float: right;
    margin: 0px 10px 10px 0px;
  }
}
</style>