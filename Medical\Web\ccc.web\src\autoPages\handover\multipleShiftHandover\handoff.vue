<!--
 * FilePath     : \src\autoPages\handover\multipleShiftHandover\handoff.vue
 * Author       : 郭鹏超
 * Date         : 2023-05-05 11:16
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-07-10 16:46
 * Description  : 批量交班
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="multiple-handoff">
    <div slot="header">
      <el-radio-group v-model="recordsCodeIndex">
        <el-radio :label="0">班别交班</el-radio>
        <el-radio :label="1">班内交班</el-radio>
      </el-radio-group>
    </div>
    <div class="multiple-handoff-content">
      <component v-if="handoverTypeArr[recordsCodeIndex]" :is="handoverTypeArr[recordsCodeIndex]"></component>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import shiftHandoff from "./components/shiftHandoff";
import inShiftHandoff from "./components/inShiftHandoff";
export default {
  components: {
    baseLayout,
    shiftHandoff,
    inShiftHandoff,
  },
  data() {
    return {
      recordsCodeIndex: 0,
      handoverTypeArr: ["shiftHandoff", "inShiftHandoff"],
    };
  },
};
</script>

<style lang="scss" >
.multiple-handoff {
  height: 100%;
  .multiple-handoff-content {
    height: 100%;
  }
}
//message组件挂载至body  目前没找到对应属性开关
.multi-handoff-msgbox-class {
  border: 0;
  .el-message-box__header {
    background-color: $base-color;
    color: #ffffff;
  }
  .el-message-box__title,
  .el-icon-close:before {
    color: #ffffff;
  }
}
</style>