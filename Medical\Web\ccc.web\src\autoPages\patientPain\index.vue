<!--
 * FilePath     : \src\autoPages\patientPain\index.vue
 * Author       : 苏军志
 * Date         : 2021-07-21 09:17
 * LastEditors  : 马超
 * LastEditTime : 2025-07-10 16:47
 * Description  : 新版本疼痛评估
 * CodeIterationRecord: 2022-08-18 2876-专项增加带入护理记录选框 -杨欣欣
                        2023-06-27 3587-护理评估按钮跳转专项录入的内容评估时间跟护理评估保持一致 -杨欣欣
-->
<template>
  <specific-care
    class="inpatient-pain"
    v-loading="loading"
    element-loading-text="加载中……"
    v-model="showDrawerFlag"
    :editFlag="showEditButton"
    :showRecordArr="showRecordArr"
    :drawerTitle="drawerTitle"
    :handOverFlag="handOverArr"
    :nursingRecordFlag="nursingRecordArr"
    :informPhysicianFlag="informPhysicianArr"
    :drawerSize="supplementFlag ? '80%' : ''"
    :previewFlag = previewFlag
    @getHandOverFlag="getHandOverFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    @mainAdd="recordAddOrModify"
    @maintainAdd="careMainAddOrModify"
    @getMainFlag="currentRecord"
    @save="saveSelect"
    @cancel="drawerClose"
    @getInformPhysicianFlag="getInformPhysicianFlag"
  >
    <!-- 主记录 -->
    <div slot="main-record">
      <packaging-table
        v-model="recordTableList"
        :headerList="mainHeaderList"
        @rowClick="currentTableList"
      >
        <template slot="startDate" slot-scope="scope">
          <span v-formatTime="{ value: scope.row.startDate, type: 'datetime' }"></span>
        </template>
        <template slot="endDate" slot-scope="scope">
          <span v-formatTime="{ value: scope.row.endDate, type: 'datetime' }"></span>
        </template>
        <template slot="operate" slot-scope="scope">
          <template v-if="!scope.row.endDate">
            <el-tooltip content="修改">
              <i class="iconfont icon-edit" @click.stop="recordAddOrModify(scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click.stop="deleteByPainRecodeID(scope.row)"></i>
            </el-tooltip>
          </template>
        </template>
      </packaging-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <packaging-table
        v-model="careMainTableList"
        :headerList="maintainHeaderList"
        :loading="detailLoading"
        ref="careMainTable"
      >
        <template slot="assessDate" slot-scope="scope">
          <span v-formatTime="{ value: scope.row.assessDate, type: 'dateTime' }"></span>
        </template>
        <template slot="careIntervention" slot-scope="scope">
          <span v-html="scope.row.careIntervention"></span>
        </template>
        <template slot="content" slot-scope="scope">
          <span v-html="scope.row.content"></span>
        </template>
        <template slot="operate" slot-scope="scope">
          <el-tooltip content="修改" v-if="scope.row.numberOfAssessment != 1">
            <i class="iconfont icon-edit" @click="careMainAddOrModify(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除" v-if="scope.row.numberOfAssessment != 1">
            <i class="iconfont icon-del" @click="deletePatientPainMain(scope.row)"></i>
          </el-tooltip>
        </template>
      </packaging-table>
    </div>
    <!-- 评估内容 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
    >
      <div slot="header">
        <span class="label">评估时间：</span>
        <el-date-picker
          class="date-picker"
          v-model="performDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        />
        <el-time-picker
          class="time-picker"
          v-model="performTime"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        />
        <station-selector v-model="performStationID" label="评估病区：" width="160" />
        <dept-selector label="" width="130" v-model="performDepartmentID" :stationID="performStationID" />
      </div>
      <tabs-layout
        ref="tabsLayout"
        :template-list="templateDatas"
        :gender="inpatient ? inpatient.genderCode : ''"
        :checkFlag="true"
        :model="isEdit ? 'edit' : 'add'"
        @change-values="changeValues"
        @checkTN="checkTN"
      />
    </base-layout>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import bodyPart from "@/components/bodyPart";
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index.vue";
import { GetDynamicTableHeader } from "@/api/DynamicTableSetting";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { mapGetters } from "vuex";
import {
  SavePatientPain,
  DeleteByPainRecodeID,
  GetPainAssessView,
  GetPainRecordsByInpatientID,
  GetPainCareByRecordID,
  SavePainCare,
  DeletePatientPainMain,
} from "@/api/PatientPain";
import { GetBringToShiftSetting, GetClinicalSettingByTypeCode } from "@/api/Setting.js";
import { GetAssessRecordsCodeByDeptID, GetRecordsCodeInfoByRecordCode } from "@/api/Assess";
export default {
  components: {
    specificCare,
    tabsLayout,
    bodyPart,
    deptSelector,
    stationSelector,
    baseLayout,
    packagingTable,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      inpatient: "getPatientInfo",
    }),
  },
  watch: {
    "inpatient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (!newVal) return;
        this.patientInfo = this.inpatient;
        this.supplementFlag = "";
        this.init();
      },
    },
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patientInfo = this.supplemnentPatient;
          this.supplementFlag = "*";
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      patientInfo: undefined,
      loading: false,
      showDrawerFlag: false,
      showRecordArr: [true, false],
      drawerTitle: "",
      handOverArr: [true, false],
      nursingRecordArr: [true, false],
      settingBringToNursingRecord: false,
      informPhysicianArr: [true, false],
      currentRecordData: undefined,
      recordTableList: [],
      careMainTableList: [],
      //科别对疼痛评估内容
      departmentToAssess: {},
      //评估模板数据
      templateDatas: [],
      assessDatas: [],
      performStationID: undefined,
      performDepartmentID: undefined,
      performDate: undefined,
      performTime: undefined,
      patientPainCareMainID: "",
      patientPainRecordID: "",
      patientCareMainID: "",
      isEdit: false,
      patientScheduleMainID: "",
      checkTNFlag: true,
      // 是否带交班
      bringToShift: false,
      // 是否能编辑\删除该数据
      showEditButton: true,
      //路由数据
      assessMainID: undefined,
      drawerLoading: false,
      drawerLoadingText: "加载中……",
      detailLoading: false,
      assessSort: undefined,
      sourceID: undefined,
      sourceType: undefined,
      settingHandOver: false,
      // 疼痛记录显示样式
      detailFormat: false,
      //补录标记
      supplementFlag: undefined,
      previewFlag : false,
      mainHeaderList: [], // 主记录动态表头
      maintainHeaderList: [], // 维护记录动态表头
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      inpatient: "getPatientInfo",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  async mounted() {
    this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
    this.assessMainID = this.$route.query.num;
    this.assessSort = this.$route.query.sort;
    this.sourceID = this.$route.query.sourceID ? this.$route.query.sourceID : this.$route.query.num;
    this.sourceType = this.$route.query.sourceType;
    if (this.patientScheduleMainID || this.assessMainID || this.sourceID) {
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
    await this.getBringHandOverSetting();
    await this.getBringToNursingRecordSetting();
    await this.getDetailFormat();
    await this.getDynamicTableHeaders();
  },
  methods: {
    /**
     * description: 初始加载
     * return {*}
     */
    init() {
      //获取是否带入交班配置
      this.performStationID = this.patientInfo.stationID;
      this.performDepartmentID = this.patientInfo.departmentListID;
      this.recordTableList = [];
      this.showRecordArr = [true, false];
      this.getRecordTableList();
    },
    /**
     * description: 获取主记录表格数据
     * return {*}
     */
    async getRecordTableList() {
      if (!this.patientInfo) {
        return;
      }
      let params = {
        inpatientID: this.patientInfo.inpatientID,
      };
      this.loading = true;
      //获取病人疼痛记录
      await GetPainRecordsByInpatientID(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.recordTableList = res.data;
        }
      });
    },
    /**
     * description: 主记录新增或修改
     * return {*}
     * param {*} item
     */
    async recordAddOrModify(item) {
      this.openOrCloseDrawer(true, "疼痛记录维护");
      this.drawerLoading = true;
      this.templateDatas = [];
      this.drawerLoadingText = "加载中……";
      await this.getAssessRecordsCode("PainManagementStart");
      let params = {
        recordsCode: this.departmentToAssess.recordsCode,
        age: this.patientInfo.age,
        gender: this.patientInfo.genderCode,
        departmentListID: this.patientInfo.departmentListID,
        dateOfBirth: this.patientInfo.dateOfBirth,
        stationID: this.patientInfo.stationID,
        inpatientID: this.patientInfo.inpatientID,
      };
      this.showEditButton = true;
      if (item) {
        this.handOverArr = [true, item.bringToShift];
        if (!this.supplementFlag) {
          this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
        }
        this.$set(this.informPhysicianArr, 1, item.informPhysician);
        //是否仅本人操作
        this.showEditButton = await this._common.checkActionAuthorization(this.user, item.addEmployeeID);
        //判断是否可修改该数据
        let ret = await this._common.getEditAuthority(
          item.patientPainRecordID,
          "PatientPainRecord",
          !!this.supplementFlag
        );
        if (ret) {
          this.showEditButton = false;
          this._showTip("warning", ret);
        }
        if (this.supplementFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(this.user.userID,false,true,item.addEmployeeID);
        this.previewFlag = !saveButtonFlag;
      }
        this.isEdit = true;
        this.patientPainRecordID = item.patientPainRecordID;
        this.patientCareMainID = item.patientStartPainCareMainID;
        this.performStationID = item.stationID;
        this.performDepartmentID = item.departmentListID;
        this.performDate = this._datetimeUtil.formatDate(item.startDate, "yyyy-MM-dd");
        this.performTime = this._datetimeUtil.formatDate(item.startTime, "hh:mm");
        params.patientCareMainID = this.patientCareMainID;
      } else {
        if (this.supplementFlag) {
          this.nursingRecordArr = [false, false];
        } else {
          this.nursingRecordArr = [true, this.settingBringToNursingRecord];
        }
        this.handOverArr = [true, this.settingHandOver];
        this.$set(this.informPhysicianArr, 1, false);
        this.isEdit = false;
        this.patientPainRecordID = "";
        this.patientCareMainID = "";
        this.performStationID = this.patientInfo.stationID;
        this.performDepartmentID = this.patientInfo.departmentListID;
        this.performDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.performTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
      }
      this.getPainAssessView(params);
    },
    /**
     * description: 维护记录新增或修改
     * return {*}
     * param {*} item
     */
    async careMainAddOrModify(item) {
      if (!this.currentRecordData) {
        this._showTip("warning", "未选择需要维护的主记录");
        return;
      }
      if (!item && this.currentRecordData.endDate) {
        this._showTip("warning", "该记录已停止,无法新增维护记录");
        return;
      }
      this.openOrCloseDrawer(true, "疼痛评估维护");
      this.templateDatas = [];
      this.patientPainRecordID = this.currentRecordData.patientPainRecordID;
      let params = {
        age: this.patientInfo.age,
        gender: this.patientInfo.genderCode,
        departmentListID: this.patientInfo.departmentListID,
        dateOfBirth: this.patientInfo.dateOfBirth,
        stationID: this.patientInfo.stationID,
        patientPainRecordID: this.patientPainRecordID,
        inpatientID: this.patientInfo.inpatientID,
      };
      this.showEditButton = true;
      this.isEdit = false;
      this.drawerLoading = true;
      this.drawerLoadingText = "加载中……";
      if (item) {
        this.handOverArr = [true, item.bringToShift];
        this.$set(this.nursingRecordArr, 1, item.bringToNursingRecord);
        this.$set(this.informPhysicianArr, 1, item.informPhysician);
        //是否仅本人操作
        this.showEditButton = await this._common.checkActionAuthorization(this.user, item.userID);
        //判断是否可修改该数据
        let ret = await this._common.getEditAuthority(
          item.patientPainCareMainID,
          "PatientPainCareMain",
          !!this.supplementFlag
        );
        if (ret) {
          this.showEditButton = false;
          this._showTip("warning", ret);
        }
        if (this.supplementFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(this.user.userID,false,true,item.userID);
        this.previewFlag = !saveButtonFlag;
      }
        if (item.recordsCode.indexOf("Start") != -1) {
          this.isEdit = true;
        }
        await this.getAssessRecordsCode("", item.recordsCode);
        this.performDate = this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd");
        this.performTime = this._datetimeUtil.formatDate(item.assessTime, "hh:mm");
        this.performStationID = item.stationID;
        this.performDepartmentID = item.departmentListID;
        this.patientCareMainID = item.patientPainCareMainID;
        params.patientCareMainID = item.patientPainCareMainID;
      } else {
        this.handOverArr = [true, this.settingHandOver];
        this.$set(this.nursingRecordArr, 1, this.settingBringToNursingRecord);
        this.informPhysicianArr = [true, false];
        this.$set(this.informPhysicianArr, 1, false);
        await this.getAssessRecordsCode("PainManagementMaintain");
        this.performDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.performTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
        this.performStationID = this.patientInfo.stationID;
        this.performDepartmentID = this.patientInfo.departmentListID;
        this.patientCareMainID = "";
      }
      params.recordsCode = this.departmentToAssess.recordsCode;
      this.getPainAssessView(params);
    },
    /**
     * description: 获取表单编号
     * param {*} type 当前表单类别
     * param {*} recordsCode 当前表单编号
     * return {*}
     */
    async getAssessRecordsCode(type, recordsCode) {
      let params = {
        departmentListID: this.patientInfo.departmentListID,
      };
      if (recordsCode) {
        params.recordcode = recordsCode;
        await GetRecordsCodeInfoByRecordCode(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.departmentToAssess = result.data;
          } else {
            this.drawerLoading = false;
          }
        });
      } else {
        let params = {
          mappingType: type,
          InpatientID: this.patientInfo.inpatientID,
          departmentListID: this.patientInfo.departmentListID,
          age: this.patientInfo.age,
        };
        await GetAssessRecordsCodeByDeptID(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.departmentToAssess = result.data;
          } else {
            this.drawerLoading = false;
          }
        });
      }
    },
    /**
     * description: 获取配置模板
     * param {*} params
     * return {*}
     */
    getPainAssessView(params) {
      this.assessDatas = [];
      GetPainAssessView(params).then((result) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 监听配置模板值变化
     * param {*} data 模板值
     * return {*}
     */
    changeValues(data) {
      this.assessDatas = data;
    },
    /**
     * description: 检核TN值
     * param {*} flag 检核结果
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 弹窗保存选择
     * return {*}
     */
    saveSelect() {
      if (!this.performStationID || !this.performDepartmentID) {
        this._showTip("warning", "请选择病区科室");
        return;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      let recordsCode = this.departmentToAssess.recordsCode;
      if (recordsCode == "PainManagementStart") {
        this.recordSave();
      }
      if (recordsCode == "PainManagementMaintain") {
        this.careMainSave();
      }
    },
    /**
     * description: 主记录保存
     * return {*}
     */
    recordSave() {
      let details = this.getDetails();
      if (!details || details.length <= 0) {
        return;
      }
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      let patientPainAssessView = {
        patientPainCareMainID: this.patientCareMainID,
        interventionMainID: this.departmentToAssess.interventionMainID,
        recordsCode: this.departmentToAssess.recordsCode,
        patientScheduleMainID: this.patientScheduleMainID,
        inpatientID: this.patientInfo.inpatientID,
        record: {
          PatientPainRecordID: this.patientPainRecordID,
          inpatientID: this.patientInfo.inpatientID,
          stationID: this.performStationID,
          departmentListID: this.performDepartmentID,
          startDate: this.performDate,
          startTime: this.performTime,
        },
        bringToShift: this.bringToShift,
        bringToNursingRecord: this.nursingRecordArr[1],
        informPhysician: this.informPhysicianArr[1],
        details: details,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        SupplementFlag: this.supplementFlag,
      };
      //填入评估ID
      if (this.assessMainID) {
        patientPainAssessView.AssessMainID = this.assessMainID;
        patientPainAssessView.AssessSort = this.assessSort;
      }
      return SavePatientPain(patientPainAssessView).then((res) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(res)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", "保存成功");
          this.bringToShift = this.settingHandOver;
          this.departmentToAssess = {};
          this.showRecordArr = [true, false];
          this.currentRecord(true);
        }
      });
    },
    /**
     * description: 维护记录保存
     * return {*}
     */
    careMainSave() {
      let details = this.getDetails();
      if (!details || details.length <= 0) {
        return;
      }
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      let patientPainCare = {
        recordsCode: this.departmentToAssess.recordsCode,
        interventionMainID: this.departmentToAssess.interventionMainID,
        patientPainRecordID: this.patientPainRecordID,
        patientPainCareMainID: this.patientCareMainID,
        assessDate: this.performDate,
        assessTime: this.performTime,
        stationID: this.performStationID,
        departmentListID: this.performDepartmentID,
        details: details,
        inpatientID: this.patientInfo.inpatientID,
        patientScheduleMainID: this.patientScheduleMainID,
        bringToShift: this.bringToShift,
        bringToNursingRecord: this.nursingRecordArr[1],
        informPhysician: this.informPhysicianArr[1],
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        supplementFlag: this.supplementFlag,
      };
      if (!patientPainCare.patientScheduleMainID) {
        let matchedItem = this.careMainTableList.find(
          (m) => patientPainCare.patientPainCareMainID === m.patientPainCareMainID
        );
        if (matchedItem) {
          patientPainCare.patientScheduleMainID = matchedItem.patientScheduleMainID;
        }
      }
      SavePainCare(patientPainCare).then((res) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(res)) {
          this.openOrCloseDrawer(false);
          this.bringToShift = this.settingHandOver;
          this._showTip("success", "保存成功");
          this.getCareMainTableList();
        }
      });
    },
    /**
     * description: 获取评估模板明细数据
     * return {*}
     */
    getDetails() {
      let details = [];
      if (!this.assessDatas || this.assessDatas.length <= 0) {
        // 没有选择数据
        this._showTip("warning", "请选择评估内容");
        return details;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return details;
      }
      this.assessDatas.forEach((data) => {
        let item = {
          assessListID: data.assessListID,
          assessValue: data.assessValue,
          assessListGroupID: data.assessListGroupID,
        };
        details.push(item);
      });
      return details;
    },
    /**
     * description: 点击主记录获取对应维护记录
     * return {*}
     * param {*} item
     */
    currentTableList(item) {
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.currentRecordData = item;
        this.recordTableList = [item];
        this.getCareMainTableList();
      }
    },
    /**
     * description: 主记录勾选
     * return {*}
     * param {*} flag
     */
    async currentRecord(flag) {
      if (flag) {
        await this.getRecordTableList();
        this.careMainTableList = [];
        if (this.isEdit && this.recordTableList.length) {
          this.isEdit = false;
          this.careMainTableList = [];
        } else {
          this.currentRecordData = undefined;
        }
      } else {
        if (!this.currentRecordData && this.recordTableList.length) {
          this.currentRecordData = this.recordTableList[0];
          this.recordTableList = [this.recordTableList[0]];
          this.getCareMainTableList();
        }
      }
    },
    /**
     * description: 获取维护记录表格数据
     * return {*}
     */
    getCareMainTableList() {
      if (!this.currentRecordData) {
        return;
      }
      this.detailLoading = true;
      let params = {
        painRecordID: this.currentRecordData.patientPainRecordID,
      };
      GetPainCareByRecordID(params).then((res) => {
        this.detailLoading = false;
        if (this._common.isSuccess(res)) {
          this.careMainTableList = res.data;
          this.$nextTick(() => {
            this.$refs.careMainTable?.doLayout();
          });
        }
      });
    },
    /**
     * description: 带入交班标记回传
     * param {*} flag
     * return {*}
     */
    getHandOverFlag(flag) {
      this.bringToShift = flag;
    },
    /**
     * description: 带入护理记录标记回传
     * param {*} flag
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.nursingRecordArr[1] = flag;
    },
    /**
     * description: 删除疼痛主记录
     * return {*}
     * param {*} row
     */
    async deleteByPainRecodeID(row) {
      //是否仅本人操作
      this.showEditButton = await this._common.checkActionAuthorization(this.user, row.addEmployeeID);
      if (!this.showEditButton) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (this.supplementFlag === "*") {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,row.addEmployeeID);
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this.showEditButton = true;
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(
        row.patientPainRecordID,
        "PatientPainRecord",
        !!this.supplementFlag
      );
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
        return;
      }
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          // 确认删除
          let prams = {
            painRecordID: row.patientPainRecordID,
          };
          this.loading = true;
          DeleteByPainRecodeID(prams).then((res) => {
            this.loading = false;
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getRecordTableList();
              this.showRecordArr = [true, false];
            }
          });
        }
      });
    },
    /**
     * description: 删除疼痛维护记录
     * return {*}
     * param {*} item
     */
    async deletePatientPainMain(item) {
      //是否仅本人操作
      this.showEditButton = await this._common.checkActionAuthorization(this.user, item.userID);
      if (!this.showEditButton) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (this.supplementFlag === "*") {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,item.userID);
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      let id = item.patientPainCareMainID;
      this.showEditButton = true;
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, "PatientPainCareMain", !!this.supplementFlag);
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
        return;
      }
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          this.loading = true;
          // 确认删除
          let prams = {
            patientPainCareMainID: id,
          };
          DeletePatientPainMain(prams).then((res) => {
            this.loading = false;
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              if (item.recordsCode.indexOf("End") != -1) {
                this.isEdit = true;
                this.currentRecord(true);
              } else {
                this.getCareMainTableList();
              }
            }
          });
        }
      });
    },
    /**
     * description: 获取疼痛评估单格式(True:详细/False:简式)
     * return {*}
     */
    async getDetailFormat() {
      this.interventionID = undefined;
      let params = {
        settingType: "PainDocumentFormat",
        settingCode: "DetailFormat",
      };
      await GetClinicalSettingByTypeCode(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.detailFormat = result.data.settingValue === "True";
        }
      });
    },
    /**
     * description: 获取是否带入交班配置
     * return {*}
     */
    async getBringHandOverSetting() {
      let params = {
        special: "Pain",
      };
      await GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.handOverArr[0]) {
            this.settingHandOver = res.data;
            this.handOverArr = [true, this.settingHandOver];
            this.bringToShift = res.data;
          }
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    async getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "PainAutoInterventionToRecord",
      };
      await GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingBringToNursingRecord = res.data;
        }
      });
    },
    /**
     * description: 打开或关闭抽屉
     * return {*}
     * param {*} flag
     * param {*} title
     */
    openOrCloseDrawer(flag, title) {
      this.showDrawerFlag = flag;
      this.drawerTitle =
        this.patientInfo.bedNumber +
        "床-" +
        this.patientInfo.patientName +
        "【" +
        this.patientInfo.gender +
        "-" +
        (this.patientInfo.ageDetail ? this.patientInfo.ageDetail : "") +
        "】-- " +
        title;
    },
    /**
     * description: 抽屉关闭逻辑
     * return {*}
     */
    drawerClose() {
      this.openOrCloseDrawer(false);
      this.patientPainRecordID = undefined;
      this.patientCareMainID = undefined;
    },
    /**
     * description: 通知医师标记
     * return {*}
     * param {*} flag
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * @description: 获取动态表头
     */
    async getDynamicTableHeaders() {
      console.log("获取动态表头");
      
      // 主记录表头
      await GetDynamicTableHeader({
        tableType: "PainRecordTable",
        tableSubType: "PainRecordTable",
        hospitalID: this.hospitalInfo.hospitalID,
        index: Math.random(),
      }).then(res => {
        if (this._common.isSuccess(res)) {
          this.mainHeaderList = res.data;
        }
      });
      // 维护记录表头
      await GetDynamicTableHeader({
        tableType: "PainCareTable",
        tableSubType: "PainCareTable",
        hospitalID: this.hospitalInfo.hospitalID,
        index: Math.random(),
      }).then(res => {
        if (this._common.isSuccess(res)) {
          this.maintainHeaderList = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.inpatient-pain {
  .date-picker {
    width: 120px !important;
  }
  .time-picker {
    width: 80px !important;
  }
  .station-selector .label {
    margin-left: 0px;
  }
}
</style>