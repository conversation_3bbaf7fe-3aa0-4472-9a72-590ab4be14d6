<!--
 * FilePath     : \src\autoPages\handover\patientHandon\patientHandon.vue
 * Author       : 郭鹏超
 * Date         : 2023-05-20 10:44
 * LastEditors  : 来江禹
 * LastEditTime : 2023-08-14 09:13
 * Description  : 病人接班页面
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="patient-handon" headerHeight="auto" v-loading="loading" element-loading-text="加载中……">
    <div slot="header">
      <span>班别开始日期：</span>
      <el-date-picker
        v-model="startDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        class="patient-handon-date"
        :picker-options="pickerOptionscreate"
        laceholder="选择日期"
      ></el-date-picker>
      <span class="label">班别结束日期：</span>
      <el-date-picker
        v-model="endDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        :picker-options="pickerOptionsend"
        class="patient-handon-date"
        laceholder="选择日期"
      ></el-date-picker>
      <span class="label">交接类别：</span>
      <el-select
        v-model="handoverType"
        @change="getHandOverType(undefined, handoverType, 'handoverTypeItems')"
        placeholder="请选择"
        class="patient-handon-type"
      >
        <el-option v-for="item in handoverTypes" :key="item.key" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <span class="label">交接子类：</span>
      <el-select v-model="handoverTypeItem" placeholder="请选择" class="patient-handon-type">
        <el-option
          v-for="item in handoverTypeItems"
          :key="item.key"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button class="query-button" icon="iconfont icon-search" @click="getHandoverData">查询</el-button>
    </div>
    <el-table :data="handoverData" border stripe height="100%" row-key="handoverID">
      <el-table-column
        :resizable="false"
        prop="handoverTypeDescription"
        label="交接类别"
        header-align="center"
      ></el-table-column>
      <el-table-column :resizable="false" prop="handoffStation" label="交班病区" align="center"></el-table-column>
      <el-table-column prop="handoffDepartment" label="交班科别" :resizable="false" align="center"></el-table-column>
      <el-table-column label="评估日期" :resizable="false" :width="convertPX(180)" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.handoffDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="评估时间" :resizable="false" :width="convertPX(110)" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.handoffTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="handoverShift" label="交班班别" :resizable="false" align="center"></el-table-column>
      <el-table-column prop="handoffNurse" label="交班护士" :resizable="false" header-align="center"></el-table-column>
      <el-table-column prop="handonStation" label="接班病区" :resizable="false" align="center"></el-table-column>
      <el-table-column prop="handonDepartment" label="接班科别" :resizable="false" align="center"></el-table-column>
      <el-table-column label="交接日期" :resizable="false" align="center" :width="convertPX(180)">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.handonDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="交接时间" :resizable="false" align="center" :width="convertPX(110)">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.handonTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="handonNurse" label="接班护士" :resizable="false" header-align="center"></el-table-column>
      <el-table-column prop="progress" label="进度" align="center">
        <template v-if="scope.row.handoverType != 'DischargeAssess'" slot-scope="scope">
          <span>{{ scope.row.handonNurse ? "已接班" : "未接班" }}</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(80)" label="操作">
        <template slot-scope="scope">
          <!-- 班别接班没有单独页面 先暂时不开 -->
          <el-tooltip v-if="scope.row.router" content="修改">
            <i class="iconfont icon-edit save" @click="updateHandover(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="dialogTitle"
      fullscreen
      v-if="showSbarFlag"
      :visible.sync="showSbarFlag"
      custom-class="patient-handon-dialog"
      :modal="false"
    >
      <div class="dialog-content">
        <component :is="activeName" :handoverData="currenrHandover"></component>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import { GetSelectSetting } from "@/api/Setting";
import { GetPatientHandoverView } from "@/api/Handover";
import baseLayout from "@/components/BaseLayout";
import transferHandover from "@/autoPages/handover/transferHandover.vue";
import dischargeHandover from "@/autoPages/handover/dischargeHandover.vue";
import operationHandover from "@/autoPages/handover/operationHandover/index.vue";
import shiftHandover from "@/autoPages/handover/shiftHandover/index.vue";
//引用病人基本信息组件
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    transferHandover,
    dischargeHandover,
    operationHandover,
    shiftHandover,
  },
  props: {
    inpatientID: {
      type: String,
      required: true,
    },
    dischargedPreview: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  watch: {
    inpatientID: {
      handler(newVal) {
        this.getHandoverData();
      },
    },
  },

  data() {
    let _this = this;
    return {
      //交班汇整开始日期时间
      startDate: this._datetimeUtil.formatDate(new Date()),
      //交班汇整结束日期时间
      endDate: this._datetimeUtil.formatDate(new Date()),
      //交班内容
      handoverData: undefined,
      //交班类别配置
      handoverTypes: [],
      handoverType: undefined,
      handoverTypeItems: [],
      handoverTypeItem: undefined,
      //数据加载
      loading: false,
      pickerOptionscreate: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(_this.endDate).getTime();
        },
      },
      pickerOptionsend: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(_this.startDate).getTime() - 8.64e7;
        },
      },
      dialogTitle: undefined,
      showSbarFlag: false,
      activeName: undefined,
      currenrHandover: {},
    };
  },
  mounted() {
    this.getHandOverType();
    this.getHandoverData();
  },
  methods: {
    /**
     * description: 获取交班表格数据
     * return {*}
     */
    async getHandoverData() {
      let params = {
        inpatientID: this.inpatientID,
        startDate: this.startDate,
        endDate: this.endDate,
        handoverType: this.handoverType,
        recordsCode: this.handoverTypeItem,
      };
      this.loading = true;
      //数据清空
      this.handoverData = [];
      await GetPatientHandoverView(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result) && result.data) {
          this.handoverData = result.data;
        }
      });
    },
    /**
     * description: 接班跳转
     * param {*} row
     * return {*}
     */
    updateHandover(row) {
      this.currenrHandover = {
        handoverID: row.handoverID,
        handoverClass: "HandOn",
        handonFlag: !!row.handonNurse,
        //调整班别班内交班页面增加传入参数
        handoverType: this.handoverTypeItem ?? row?.recordsCode,
        startDate: this.startDate,
        endDate: this.endDate,
      };
      this.showSbarFlag = true;
      this.dialogTitle = row.handoverTypeDescription;
      this.activeName = row.router;
    },
    /**
     * description: 获取交班类型
     * params {*}
     * return {*}
     * param {*} typeCode
     * param {*} typeValue
     * param {*} key
     */
    async getHandOverType(typeCode = "HandoverSetting", typeValue = "HandoverCategory", key = "handoverTypes") {
      this.handoverTypeItem = undefined;
      let params = {
        typeCode,
        typeValue,
        addDefaultFlag: true,
        index: Math.random(),
      };
      await GetSelectSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this[key] = res.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.patient-handon {
  .patient-handon-date {
    width: 180px;
  }
  .patient-handon-type {
    width: 220px;
  }
  .label {
    margin-left: 10px;
  }
  .patient-handon-dialog {
    .el-dialog__body {
      height: calc(100% - 25px);
      .dialog-content {
        height: calc(100% - 25px);
      }
    }
  }
}
</style>
