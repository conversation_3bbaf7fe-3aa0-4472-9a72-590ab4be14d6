<!--
 * FilePath     : \src\components\progressView.vue
 * Author       : 郭鹏超
 * Date         : 2021-03-17 15:42
 * LastEditors  : 苏军志
 * LastEditTime : 2023-01-17 17:47
 * Description  : 进度条
-->
<template>
  <div class="progress">
    <div class="progress-content">
      <!-- 头部进度条 -->
      <div class="progress-table">
        <div class="progress-label">{{ percentageTable[0].label }}</div>
        <div class="progress-value">
          <el-progress :percentage="percentageTable[0].value"></el-progress>
        </div>
      </div>
      <!-- 提示信息表格 -->
      <el-table :max-height="convertPX(200)" class="message-table" :data="messageTable" border>
        <el-table-column prop="label" label="进度" :width="convertPX(124)"></el-table-column>
        <el-table-column prop="value" label="消息">
          <template slot-scope="scope">
            <span v-html="scope.row.value"></span>
          </template>
        </el-table-column>
      </el-table>
      <div class="progress-bottom">
        <el-button v-if="percentageTable[0].value == 100" type="primary" @click="close()">确 定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: () => {
        return [
          {
            label: "进度",
            value: 0,
          },
          {
            label: "保存成功",
            value: "",
          },
          {
            label: "保存失败",
            value: "",
          },
          {
            label: "提示",
            value: "",
          },
        ];
      },
    },
  },
  watch: {
    tableData: {
      handler(newVal) {
        if (newVal.length) {
          //分离表格数据
          this.percentageTable = [newVal[0]];
          this.messageTable = [newVal[1], newVal[2], newVal[3]];
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    //添加进度条定时器
    this.addInterval();
  },
  data() {
    return {
      flag: false,
      messageTable: [],
      percentageTable: [],
      interval: undefined,
    };
  },
  beforeDestroy() {
    //页面销毁清除定时器
    clearInterval(this.interval);
  },
  methods: {
    close() {
      this.$emit("closeProgress");
    },
    addInterval() {
      this.interval = setInterval(() => {
        if (this.percentageTable[0].value < 100) {
          this.percentageTable[0].value = Number(this.percentageTable[0].value + 1);
        }
      }, 4000);
    },
  },
};
</script>

<style lang="scss" >
.progress {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1001;
  .progress-content {
    z-index: 1002;
    position: absolute;
    transform: translateY(-50%) translateX(-50%);
    top: 50%;
    left: 50%;
    background-color: #fff;
    margin: 0 auto;
    width: 500px;
    padding: 10px;
    max-height: 300px;
    .progress-table {
      height: 35px;
      border: 1px solid #cccccc;
      border-bottom: none;
      box-sizing: border-box;
      display: flex;
      & > div {
        float: left;
        height: 35px;
        line-height: 35px;
      }
      .progress-label {
        color: #606266;
        padding-left: 8px;
        width: 164px;
        border-right: 1px solid #cccccc;
        box-sizing: border-box;
        font-size: 16px;
      }
      .progress-value {
        flex: auto;
        width: 100%;
        padding: 10px;
        box-sizing: border-box;
        .el-progress {
          top: -3px;
          .el-progress-bar {
            width: 95%;
          }
          .el-progress__text {
            font-size: 16px !important;
          }
        }
      }
    }
    .message-table {
      .el-table__header-wrapper {
        display: none;
      }
      th.is-leaf {
        background-color: #fff;
      }
    }
    .progress-bottom {
      width: 100%;
      height: 35px;
      line-height: 35px;
      text-align: right;
      margin-top: 5px;
    }
  }
}
</style>