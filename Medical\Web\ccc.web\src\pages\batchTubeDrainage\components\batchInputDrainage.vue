<!--
 * FilePath     : \src\pages\batchTubeDrainage\components\batchInputDrainage.vue
 * Author       : 胡长攀
 * Date         : 2024-04-20 10:55
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-06-18 11:33
 * Description  : 批量录入引流液
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="batch-input-drainage" v-loading.fullscreen.lock="loading" element-loading-text="加载中……">
    <div slot="header">
      <div class="header-left">
        <label v-if="showAllFlag">显示全科：</label>
        <el-switch v-if="showAllFlag" v-model="showAll" @change="changeTime" />
        <label>班别日期:</label>
        <el-date-picker
          type="date"
          placeholder="选择班别日期"
          v-model="shiftDate"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          class="top-shift-pick"
        ></el-date-picker>
        <shift-selector :stationID="stationID" v-model="shiftID"></shift-selector>
        <div class="time-line" v-if="typeIndex === 1">
          <label>时段:</label>
          <el-select
            class="shiftTime-select"
            v-model="formatData.shiftTimes"
            @change="changeTime"
            placeholder="请选择时间段"
          >
            <el-option
              v-for="(shiftTime, index) in shiftTimeList"
              :key="index"
              :label="shiftTime"
              :value="shiftTime"
            ></el-option>
          </el-select>
          <!-- 新增加的控制全局的时间插件 -->
          <label>执行时间:</label>
          <el-time-picker
            v-model="allExecuteTime"
            type="time"
            value-format="HH:mm"
            format="HH:mm"
            style="width: 80px"
            @change="changeAllExecuteTime()"
          ></el-time-picker>
        </div>
      </div>
      <div class="header-right">
        <el-button
          v-if="showPDFButton"
          class="print-button"
          icon="iconfont icon-print"
          @click="getBatchRecordDrainagePDF"
        >
          引流液记录表打印
        </el-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="saveTubeDrainageOutput()">保存</el-button>
      </div>
      <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    </div>
    <el-radio-group class="radio-group" @change="changeTime" v-model="typeIndex">
      <el-radio-button :label="1">新增记录</el-radio-button>
      <el-radio-button :label="2">历史记录</el-radio-button>
    </el-radio-group>
    <el-table
      ref="multipleTable"
      height="calc(100% - 30px)"
      :data="multiOutput"
      border
      stripe
      highlight-current-row
      @selection-change="selectItem"
      class="el-table-class"
    >
      <!-- 多选列 -->
      <el-table-column type="selection" width="55" header-align="center" align="center" fixed></el-table-column>
      <el-table-column v-if="showAllFlag" label="姓名" width="100px" align="center">
        <template slot-scope="scope">
          <span>{{ `${scope.row.bedNumber}-${scope.row.patientName}` }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排程时间" width="80px" align="center">
        <template slot-scope="schedule">
          <span v-formatTime="{ value: schedule.row.scheduleTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="ioKind" label="类别" width="80px" align="center"></el-table-column>
      <el-table-column
        prop="tubeName"
        label="项目"
        header-align="center"
        align="left"
        min-width="130px"
      ></el-table-column>
      <el-table-column prop="color" label="颜色" width="90px" align="center">
        <template slot-scope="scope">
          <color-picker
            :colorArray="scope.row.colorDict"
            v-model="scope.row.color16bit"
            width="85px"
            @change="check('color', scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="characteristic" label="性状" width="105px" align="center">
        <template slot-scope="scope">
          <el-select
            v-model="scope.row.characteristicID"
            placeholder="请选择"
            @change="check(scope.row.characteristicID, scope.row)"
          >
            <el-option
              v-for="item in scope.row.characteristicDict"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="气味" width="100px" align="center">
        <template slot-scope="scope">
          <el-select v-model="scope.row.smellID" placeholder="请选择" @change="check(scope.row.smellID, scope.row)">
            <el-option
              v-for="item in scope.row.smellDict"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="intakeOutputVolume" width="90px" label="量(ml)" align="center">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.intakeOutputVolume"
            class="input-width"
            @keyup.native="oninput(scope.row)"
            @blur="
              check(scope.row.intakeOutputVolume, scope.row);
              validateByTextEntry(scope.row, 'intakeOutputVolume');
            "
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="intakeOutputNote" min-width="100px" label="备注" align="center">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.intakeOutputNote"
            style="width: 100%"
            @blur="check(scope.row.intakeOutputNote, scope.row)"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="带入护理记录" min-width="60px" align="center">
        <template slot-scope="scope">
          <el-checkbox
            v-model="scope.row.bringToNursingRecords"
            @change="check(scope.row.bringToNursingRecords, scope.row)"
          ></el-checkbox>
        </template>
      </el-table-column>
      <el-table-column label="执行日期" width="100px" align="center" fixed="right">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.ioDate"
            type="date"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            @blur="check(scope.row.ioDate, scope.row)"
          ></el-date-picker>
        </template>
      </el-table-column>
      <el-table-column label="执行时间" width="80px" align="center" fixed="right">
        <template slot-scope="scope">
          <el-time-picker
            v-model="scope.row.ioTime"
            type="time"
            value-format="HH:mm"
            format="HH:mm"
            @blur="check(scope.row.ioTime, scope.row)"
            style="width: 60px"
          ></el-time-picker>
        </template>
      </el-table-column>
    </el-table>
    <!-- 批量引流液录入PDF -->
    <el-dialog
      title="批量引流液录入"
      :close-on-click-modal="false"
      :visible.sync="drainagePDFDialogVisible"
      append-to-body
      fullscreen
    >
      <iframe
        :src="pdfPath"
        type="application/x-google-chrome-pdf"
        width="100%"
        height="100%"
        frameborder="1"
        scrolling="auto"
      />
    </el-dialog>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetBatchRecordDrainage, SaveDrainageOutput, GetMultiHistory, GetBatchRecordDrainagePDF } from "@/api/IO";
import colorPicker from "@/components/colorPicker/colorPicker";
import { mapGetters } from "vuex";
import { GetShiftTimeLine } from "@/api/Setting";
import progressView from "@/components/progressView";
import shiftSelector from "@/components/selector/shiftSelector";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { validateByTextEntry } from "@/utils/textEntryValidate";
export default {
  components: {
    baseLayout,
    colorPicker,
    progressView,
    shiftSelector,
  },
  props: {
    // 是否显示全科开关
    showAllFlag: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      allExecuteTime: this._datetimeUtil.getNowDate("hh:mm"),
      //加载
      loading: false,
      //批量输入数据
      multiOutput: [],
      //执行日期
      scheduleDate: undefined,
      //执行时间
      scheduleTime: undefined,
      //科别序号
      departmentListID: undefined,
      //病区序号
      stationID: undefined,
      //选中的所有数据
      allData: undefined,
      //班别ID
      shiftID: undefined,
      //班别日期
      shiftDate: undefined,
      //时间段数据
      shiftTimeList: [],
      //当前数据
      nowShiftData: "",
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      formatData: {
        flag: "1",
        scheduleDate: "",
        shiftID: "",
        shiftTimes: "",
      },
      //进度条开关
      progressFlag: false,
      //页签默许于新增
      typeIndex: 1,
      //是否选择了显示全科
      showAll: false,
      showPDFButton: false,
      drainagePDFDialogVisible: false,
      pdfPath: "",
      //TextEntry检核函数
      validateByTextEntry,
    };
  },
  async created() {
    this.getShowPDFButtonSetting();
  },
  computed: {
    ...mapGetters({
      currentPatient: "getCurrentPatient",
    }),
  },
  watch: {
    "currentPatient.inpatientID": {
      handler(newValue) {
        if (newValue) {
          this.init(false);
        }
      },
      immediate: true,
    },
    shiftID: {
      handler() {
        if (this.shiftDate) {
          this.init();
        }
      },
    },
    shiftDate: {
      handler() {
        if (this.shiftID) {
          this.init();
        }
      },
    },
  },
  methods: {
    changeAllExecuteTime() {
      this.multiOutput.forEach((item) => {
        item.ioTime = this.allExecuteTime;
      });
    },
    async init(flag = true) {
      //获得当前班别
      if (!this.shiftDate || !this.shiftID) {
        await this.getCurrentShift();
      }
      //获取执行时间
      this.scheduleDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.scheduleTime = this._datetimeUtil.getNowDate("hh:mm");
      this.formatData.scheduleDate = this.scheduleDate;
      this.formatData.shiftID = this.shiftID;
      //取得班别时段
      await this.getShiftTimeList();
      if (flag) {
        this.formatData.shiftTimes = this.getNowShiftTime(this.formatData.shiftID);
      }
      //时间发生变更取得导管数据
      await this.changeTime();
    },
    /**
     * description: 获取班别时间段数据
     * return {*}
     */
    async getShiftTimeList() {
      //添加判断,当前页签时历史页签的时候不再获取班别时间段
      if (this.typeIndex == 2) {
        return;
      }
      if (!this.formatData.shiftID) {
        return;
      }
      let params = {
        shiftID: this.formatData.shiftID,
        index: Math.random(),
      };
      await GetShiftTimeLine(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.shiftTimeList = res.data;
        }
      });
    },
    /**
     * description: 获取班别对应时间段
     * return {*}
     * param {*} value
     */
    getNowShiftTime(value) {
      if (value == this.nowShiftData.nowShift.id) {
        let nowTime = this._datetimeUtil.formatDate(new Date(), "hh:mm");
        let shiftTime = this.shiftTimeList.find((item) => {
          return nowTime >= item.split("-")[0] && nowTime <= item.split("-")[1];
        });
        if (!shiftTime) {
          shiftTime = this.shiftTimeList[0];
        }
        return shiftTime;
      } else {
        return this.shiftTimeList[0];
      }
    },
    /**
     * description: 变更时间段事件
     * return {*}
     */
    async changeTime() {
      //遮罩开始
      this.loading = true;
      this.multiOutput = [];
      let params = {
        inpatientID: this.currentPatient.inpatientID,
        departmentListID: this.departmentListID,
        shiftDate: this.shiftDate,
        stationID: this.stationID,
        shiftID: this.shiftID,
        showAll: this.showAll,
        showAllFlag: this.showAllFlag,
      };
      if (this.typeIndex == 1) {
        await this.getBatchRecordDrainage(params);
      } else {
        await this.getMultiHistory(params);
      }
    },
    getShowPDFButtonSetting() {
      const param = {
        SettingTypeCode: "ShowDrainagePDFFlag",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.showPDFButton = response.data;
        }
      });
    },
    /**
     * @description: 获取批量评估引流液数据（新增记录）
     * @param params
     * @return
     */
    async getBatchRecordDrainage(params) {
      //只有当处于新增的页签时，才使用时间段
      if (!this.formatData.shiftTimes) {
        return;
      }
      params.shiftTimes = this.formatData.shiftTimes;
      //获得需要评估引流液的导管
      await GetBatchRecordDrainage(params).then((result) => {
        //遮罩结束
        this.loading = false;
        if (this._common.isSuccess(result)) {
          if (result.data == null) {
            return;
          }
          this.multiOutput = this.formatDateTime(result.data);
          this.changeAllExecuteTime();
          //防止oldData随着数据的更改联动到
          this.oldData = this._common.clone(this.multiOutput);
        }
      });
    },
    getBatchRecordDrainagePDF() {
      let params = {
        inpatientID: this.currentPatient.inpatientID,
        departmentListID: this.departmentListID,
        shiftDate: this.shiftDate,
        stationID: this.stationID,
        shiftID: this.shiftID,
        showAll: this.showAll,
        showAllFlag: this.showAllFlag,
        shiftTimes: this.formatData.shiftTimes,
      };
      GetBatchRecordDrainagePDF(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (!res.data.includes("pdf")) {
            this._showTip("warning", "当前时段无待录入引流液数据！");
            return;
          }
          this.drainagePDFDialogVisible = true;
          this.pdfPath = res.data;
        }
      });
    },
    /**
     * @description:获取批量评估引流液数据（历史记录）
     * @return
     */
    async getMultiHistory(params) {
      //获取历史纪录
      await GetMultiHistory(params).then((result) => {
        //遮罩结束
        this.loading = false;
        if (this._common.isSuccess(result)) {
          if (result.data) {
            this.multiOutput = this.formatDateTime(result.data);
            //防止oldData随着数据的更改联动到
            this.oldData = this._common.clone(this.multiOutput);
          }
        }
      });
    },
    /**
     * description: 获得当前班别日期和ID
     * params {*}
     * return {*}
     */
    async getCurrentShift() {
      await GetNowStationShiftData().then((res) => {
        if (this._common.isSuccess(res)) {
          this.nowShiftData = res.data;
          this.stationID = this.nowShiftData.nowShift.stationID;
          this.shiftDate = this._datetimeUtil.formatDate(this.nowShiftData.shiftDate, "yyyy-MM-dd");
        }
      });
    },
    /**
     * description: 如果时间为null，取当前数据
     * params {*}
     * return {*}
     * param {*} value
     */
    formatDateTime(value) {
      value.map((item) => {
        if (!item.ioDate || !item.ioTime) {
          item.ioDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
          item.ioTime = this._datetimeUtil.getNowTime("hh:mm");
        } else {
          //格式化时间格式，解决检查数据是否改变的时候,时间比较不正确的问题
          item.ioDate = this._datetimeUtil.formatDate(item.ioDate, "yyyy-MM-dd");
          item.ioTime = this._datetimeUtil.formatDate(item.ioTime, "hh:mm");
        }
      });
      return value;
    },
    oninput(row) {
      row.intakeOutputVolume = this._decimalUtil.decimalRound(row.intakeOutputVolume, "RoundDown", 2);
    },
    /**
     * description: 保存批量执行的引流液
     * params {*} null
     * return {*}
     */
    async saveTubeDrainageOutput() {
      let successMessage = "";
      let failMessage = "";
      if (!this.allData || this.allData == null || this.allData.length <= 0) {
        this._showTip("warning", "没有数据被勾选");
        return;
      }

      let cloneData = this._common.clone(this.allData);
      //进度条开关
      this.progressFlag = true;
      for (let i = 0; i < cloneData.length; i++) {
        let item = cloneData[i];
        let params = {
          characteristicID: item.characteristicID,
          color: item.color16bit,
          inpatientID: item.inpatientID,
          intakeOutputSettingID: item.intakeOutputSettingID,
          intakeOutputVolume: item.intakeOutputVolume,
          ioDate: item.ioDate,
          ioTime: item.ioTime,
          patientTubeCareMainID: item.patientTubeCareMainID,
          patientTubeRecordID: item.patientTubeRecordID,
          smellID: item.smellID,
          intakeOutputKind: item.intakeOutputKind,
          departmentListID: 0,
          stationID: this.stationID,
          recordsCode: item.recordsCode,
          //从后端已经获取到对应的排程ID
          patientScheduleMainID: item.patientScheduleMainID,
          patientIntakeOutputID: item.patientIntakeOutputID,
          intakeOutputNote: item.intakeOutputNote,
          bringToNursingRecords: item.bringToNursingRecords ? item.bringToNursingRecords : false,
        };
        //取得气味
        item.smellDict.forEach((smells) => {
          if (smells.key == item.smellID) {
            params.smell = smells.value;
          }
        });
        //取得性状
        item.characteristicDict.forEach((characteristics) => {
          if (characteristics.key == item.characteristicID) {
            params.characteristic = characteristics.value;
          }
        });
        //获取保存数据提示字符出
        let messageItem =
          item.bedNumber +
          "-" +
          item.patientName +
          "-" +
          this._datetimeUtil.formatDate(item.scheduleTime, "hh:mm") +
          "-" +
          item.tubeName;
        //提交
        await SaveDrainageOutput(params).then((result) => {
          if (result.code == 1) {
            //保存成功拼接成功字符
            successMessage = i == 0 ? messageItem : successMessage + "," + messageItem;
            //保存成功取消勾选
            this.$refs.multipleTable.clearSelection(cloneData[i], false);
            this.typeIndex = 2;
          } else {
            failMessage = failMessage + " " + messageItem;
          }
        });
        //配置进度条内容
        let progress = (((i + 1) / cloneData.length) * 100).toFixed(0);
        //配置进度条内容
        this.messageData[0].value = Number(progress);
        this.messageData[1].value = successMessage;
        this.messageData[2].value = failMessage;
        this.messageData[3].value = "";
      }
    },
    /**
     * description: 操作行的时候自动勾选第一列的多选图标
     * params {*}
     * return {*}
     * param {*} value
     * param {*} row
     */
    check(value, row) {
      //判断数据是否改变
      for (let i = 0; this.oldData.length; i++) {
        let element = this.oldData[i];
        if (
          element.scheduleDate == row.scheduleDate &&
          element.scheduleTime == row.scheduleTime &&
          element.patientTubeRecordID == row.patientTubeRecordID
        ) {
          //先量后气味和性状
          if (this.compareVal(row, element)) {
            this.$refs.multipleTable.toggleRowSelection(row, true);
          } else {
            //解决点击颜色组件的清除按钮会将所有选中的复选框去掉的问题
            if (value == "color") {
              this.$refs.multipleTable.toggleRowSelection(row, false);
            } else {
              this.$refs.multipleTable.clearSelection(row, true);
            }
          }
          return;
        }
      }
    },
    /**
     * description: 比较判断数据是否发生修改
     * params {*}
     * return {bool}:改变返回true，不变返回false
     */
    compareVal(modifyValue, sourceValue) {
      //解决''与null判断的时候（保证''与null代表相同的含义）
      let sourceIntakeOutputNote = sourceValue["intakeOutputNote"] ? sourceValue["intakeOutputNote"] : "";
      let modifyIntakeOutputNote = modifyValue["intakeOutputNote"] ? modifyValue["intakeOutputNote"] : "";
      let sourceIntakeOutputVolume = sourceValue["intakeOutputVolume"] ? sourceValue["intakeOutputVolume"] : -1;
      let modifyIntakeOutputVolume = modifyValue["intakeOutputVolume"] ? modifyValue["intakeOutputVolume"] : -1;
      if (
        sourceIntakeOutputVolume != modifyIntakeOutputVolume ||
        sourceValue["smellID"] != modifyValue["smellID"] ||
        sourceValue["characteristicID"] != modifyValue["characteristicID"] ||
        sourceValue["ioDate"] != modifyValue["ioDate"] ||
        sourceValue["ioTime"] != modifyValue["ioTime"] ||
        sourceIntakeOutputNote != modifyIntakeOutputNote ||
        sourceValue["color16bit"] != modifyValue["color16bit"] ||
        sourceValue["bringToNursingRecords"] != modifyValue["bringToNursingRecords"]
      ) {
        return true;
      } else {
        return false;
      }
    },
    //勾选数据
    selectItem(item) {
      this.allData = item;
    },
    //进度条重置
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    //进度条关闭函数
    progressClose() {
      this.progressFlag = false;
      this.renewMessageData();
      this.init();
    },
  },
};
</script>
<style lang="scss">
.batch-input-drainage {
  height: 100%;
  .header-left {
    float: left;
    .label {
      margin-left: 10px;
    }
    .top-shift-pick {
      width: 130px;
    }
    .time-line {
      margin-left: 10px;
      display: inline-block;
      .shiftTime-select {
        margin-left: 10px;
        width: 120px;
      }
    }
  }
  .header-right {
    float: right;
  }
  .radio-group {
    height: 30px;
    line-height: 30px;
  }
  .el-table-class {
    .el-input__prefix {
      display: none;
    }
    .el-input__inner {
      padding-left: 5px;
      //width: 93px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 85px;
    }
  }
}
</style>

