<!--
 * FilePath     : \src\pages\dictionaryMaintain\systemVersion\index.vue
 * Author       : 苏军志
 * Date         : 2022-05-08 11:27
 * LastEditors  : 苏军志
 * LastEditTime : 2022-11-15 14:45
 * Description  : 系统版本更新伟华画面
 * CodeIterationRecord: 
-->

<template>
  <base-layout class="version-log" v-loading="loading" element-loading-text="加载中……">
    <div slot="header">
      <span>更新内容：</span>
      <el-input class="version-input" v-model="inputContext" placeholder="请输入内容" @keyup.enter.native="search">
        <i slot="append" class="iconfont icon-search" @click="search"></i>
      </el-input>
      <div class="right-btn">
        <el-button class="add-button" icon="iconfont icon-add" @click="addOrModify()">新增</el-button>
      </div>
    </div>
    <el-table height="100%" :data="versionList" highlight-current-row border class="version-table">
      <el-table-column prop="version" label="版本号" align="center" width="80"></el-table-column>
      <el-table-column label="新增功能" header-align="center" min-width="200" class-name="update-content">
        <template slot-scope="scope">
          <span v-html="scope.row.addFunction"></span>
        </template>
      </el-table-column>
      <el-table-column label="问题修复" header-align="center" min-width="200" class-name="update-content">
        <template slot-scope="scope">
          <span v-html="scope.row.bugFix"></span>
        </template>
      </el-table-column>
      <el-table-column label="系统优化" header-align="center" min-width="200" class-name="update-content">
        <template slot-scope="scope">
          <span v-html="scope.row.systemOptimization"></span>
        </template>
      </el-table-column>
      <el-table-column label="说明文档" header-align="center" min-width="100" class-name="update-content">
        <template slot-scope="scope">
          <div v-if="scope.row.fileList && scope.row.fileList.length > 0">
            <span
              class="file-name"
              v-for="(file, index) in scope.row.fileList"
              :key="index"
              @click="versionFilePreview(index, scope.row.fileList, scope.row.version)"
            >
              {{ index + 1 + "、" + file.name }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" width="110">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.updateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="70">
        <template slot-scope="scope">
          <span v-if="scope.row.statusType == 0">已登记</span>
          <span v-if="scope.row.statusType == 1">预更新</span>
          <span v-if="scope.row.statusType == 2">更新中</span>
          <span v-if="scope.row.statusType == 3">已更新</span>
          <span v-if="scope.row.statusType == 4">已退版</span>
          <span v-if="scope.row.statusType == 5">已作废</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="95">
        <template slot-scope="scope">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="addOrModify(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteVersion(scope.row.systemVersionRecordID)"></i>
          </el-tooltip>
          <el-tooltip
            v-if="[0, 1, 2, 3].indexOf(scope.row.statusType) != -1"
            :content="
              scope.row.statusType == 0
                ? '发送预更新广播'
                : scope.row.statusType == 1
                ? '发送开始更新广播'
                : '发送更新完成广播'
            "
          >
            <i
              :class="['iconfont icon-message message', 'status' + scope.row.statusType]"
              @click="sendUpdateMessage(scope.row)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 新增/修改弹窗 -->
    <el-dialog
      v-if="showDialog"
      title="系统版本维护"
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="showDialog"
      custom-class="version-update-maintain"
      v-loading="dialogLoading"
      element-loading-text="保存中……"
    >
      <div class="update-info">
        <span class="label">版本号：</span>
        <el-input
          v-model="version"
          placeholder="请输入版本号"
          class="version"
          :disabled="systemVersionRecordID && systemVersionRecordID.length > 0"
        ></el-input>
        <span class="label">更新时间：</span>
        <el-date-picker
          v-model="updateTime"
          type="datetime"
          placeholder="选择发布时间"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          class="update-time"
        ></el-date-picker>
        <span class="label">预计更新时长：</span>
        <el-input v-model="updateDuration" placeholder="请输入时长" class="update-duration"></el-input>
        分钟
        <file-upload v-model="fileList"></file-upload>
      </div>
      <div class="update-content">
        <div class="content add-function">
          <label class="content-label">新增功能：</label>
          <rich-text
            class="content-input"
            :size="{ width: '100%', height: '100%' }"
            :wordNumber="2000"
            v-model="addFunction"
          ></rich-text>
        </div>
        <div class="content bug-fix">
          <label class="content-label">问题修复：</label>
          <rich-text
            class="content-input"
            :size="{ width: '100%', height: '100%' }"
            :wordNumber="2000"
            v-model="bugFix"
          ></rich-text>
        </div>
        <div class="content system-optimization">
          <label class="content-label">系统优化：</label>
          <rich-text
            class="content-input"
            :size="{ width: '100%', height: '100%' }"
            :wordNumber="2000"
            v-model="systemOptimization"
          ></rich-text>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="saveVersion">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="filePreviewDialogTitle"
      :visible.sync="showFilePreview"
      custom-class="no-footer"
    >
      <file-preview
        v-if="showFilePreview"
        :defaultFileIndex="defaultPreviewFileIndex"
        :datas="filePreviewData"
      ></file-preview>
    </el-dialog>
  </base-layout>
</template>

<script>
import { GetVersionBySystemCode, SaveSystemVersion, DeleteVersionByID, SendUpdateMessage } from "@/api/SystemVersion";
import baseLayout from "@/components/BaseLayout";
import fileUpload from "@/components/FileUpload";
import richText from "@/components/RichText";
import filePreview from "@/components/FilePreview";
export default {
  components: {
    baseLayout,
    richText,
    fileUpload,
    filePreview,
  },
  data() {
    return {
      inputContext: "",
      loading: false,
      versionList: [],
      allVersionList: [],
      showDialog: false,
      dialogLoading: false,
      systemVersionRecordID: undefined,
      version: "",
      updateTime: undefined,
      updateDuration: 15,
      addFunction: "",
      bugFix: "",
      systemOptimization: "",
      fileList: [],
      showFilePreview: false,
      filePreviewDialogTitle: "",
      filePreviewData: [],
      defaultPreviewFileIndex: false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    init() {
      let params = {
        systemCode: "CCC",
      };
      this.loading = true;
      GetVersionBySystemCode(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.versionList = res.data;
          this.allVersionList = this._common.clone(this.versionList);
        }
      });
    },
    /**
     * description: 模糊查询
     * param {*}
     * return {*}
     */
    search() {
      if (!this.inputContext) {
        this.versionList = this.allVersionList;
        return;
      }
      this.versionList = this.allVersionList.filter((version) => {
        let addFunction = version.addFunction.replace(/<[^>]+>/g, "");
        let bugFix = version.bugFix.replace(/<[^>]+>/g, "");
        let systemOptimization = version.systemOptimization.replace(/<[^>]+>/g, "");
        if (
          addFunction.indexOf(this.inputContext) != -1 ||
          bugFix.indexOf(this.inputContext) != -1 ||
          systemOptimization.indexOf(this.inputContext) != -1
        ) {
          return true;
        }
        return false;
      });
    },
    /**
     * description: 新增/修改
     * param {*} row
     * return {*}
     */
    addOrModify(row) {
      this.showDialog = true;
      if (row) {
        this.systemVersionRecordID = row.systemVersionRecordID;
        this.version = row.version;
        this.updateTime = row.updateTime;
        this.updateDuration = row.updateDuration;
        this.addFunction = row.addFunction;
        this.bugFix = row.bugFix;
        this.systemOptimization = row.systemOptimization;
        this.fileList = row.fileList ? row.fileList : [];
      } else {
        this.systemVersionRecordID = undefined;
        this.version = "";
        this.updateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
        this.updateDuration = 15;
        this.addFunction = "";
        this.bugFix = "";
        this.systemOptimization = "";
        this.fileList = [];
      }
    },
    /**
     * description: 版本说明文件预览
     * param {*} index 文件序号
     * param {*} fileList 文件列表
     * param {*} version 当前选择行的版本号
     * return {*}
     */
    versionFilePreview(index, fileList, version) {
      this.filePreviewData = [];
      this.filePreviewDialogTitle = `${version}版本说明文档`;
      this.defaultPreviewFileIndex = index;
      fileList.forEach((file) => {
        this.filePreviewData.push(file.filePath);
      });
      this.showFilePreview = true;
    },
    /**
     * description: 保存版本记录
     * param {*}
     * return {*}
     */
    saveVersion() {
      if (!this.systemVersionRecordID && !this.version) {
        this._showTip("warning", "版本号不能为空！");
        return;
      }
      console.log(this.versionList.findIndex((version) => version.version == this.version));
      if (this.versionList.findIndex((version) => version.version == this.version) != -1) {
        this._showTip("warning", `${this.version}版本已存在,请进行修改或先删除再新增。`);
        return;
      }
      if (!this.updateTime) {
        this._showTip("warning", "更新时间不能为空！");
        return;
      }
      if (!this.updateDuration) {
        this._showTip("warning", "更新时长不能为空！");
        return;
      }
      if (!this.addFunction && !this.bugFix && !this.systemOptimization) {
        this._showTip("warning", "更新内容不能为空！");
        return;
      }
      this.getFileList().then((fileList) => {
        let params = {
          systemCode: "CCC",
          version: this.version,
          updateTime: this.updateTime,
          updateDuration: this.updateDuration,
          addFunction: this.addFunction,
          bugFix: this.bugFix,
          systemOptimization: this.systemOptimization,
        };
        if (this.systemVersionRecordID) {
          params.systemVersionRecordID = this.systemVersionRecordID;
        }
        if (fileList && fileList.length > 0) {
          params.fileList = fileList;
        }
        this.dialogLoading = true;
        SaveSystemVersion(params).then((res) => {
          this.dialogLoading = false;
          if (this._common.isSuccess(res)) {
            this._showTip("success", "保存成功！");
            this.showDialog = false;
            this.init();
          }
        });
      });
    },
    /**
     * description: 组装文件集合
     * param {*}
     * return {*}
     */
    getFileList() {
      return new Promise((resolve, reject) => {
        let fileList = [];
        if (!this.fileList || this.fileList.length <= 0) {
          resolve(fileList);
        }
        for (let i = 0; i < this.fileList.length; i++) {
          let file = this.fileList[i];
          if (file.fileID) {
            let newFile = {
              fileID: file.fileID,
              name: file.name,
            };
            fileList.push(newFile);
            if (i == this.fileList.length - 1) {
              resolve(fileList);
            }
            continue;
          }
          this.getBase64(file.raw).then((resBase64) => {
            let base64file = resBase64.split(",")[1];
            if (base64file) {
              let newFile = {
                name: file.name,
                file: base64file,
              };
              fileList.push(newFile);
              if (i == this.fileList.length - 1) {
                resolve(fileList);
              }
            }
          });
        }
      });
    },
    /**
     * description: 将文件转换成数据流
     * param {*}
     * return {*}
     */
    getBase64(file) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader();
        let fileResult = "";
        //开始传
        reader.readAsDataURL(file);
        reader.onload = function () {
          fileResult = reader.result;
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(fileResult);
        };
      });
    },
    /**
     * description: 删除版本记录
     * param {*} systemVersionRecordID 版本记录ID
     * return {*}
     */
    deleteVersion(systemVersionRecordID) {
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          // 确认删除
          let params = {
            systemVersionRecordID: systemVersionRecordID,
          };
          DeleteVersionByID(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功！");
              this.init();
            }
          });
        }
      });
    },
    sendUpdateMessage(row) {
      let params = {
        systemVersionRecordID: row.systemVersionRecordID,
        version: row.version,
        updateDuration: row.updateDuration,
        statusType: row.statusType,
        addFunction: row.addFunction,
        bugFix: row.bugFix,
        systemOptimization: row.systemOptimization,
      };
      SendUpdateMessage(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "发送成功！");
          this.init();
        }
      });
    },
  },
};
</script>

<style lang="scss">
.version-log {
  .version-input {
    width: 300px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .right-btn {
    float: right;
  }
  .version-table {
    .update-content .cell {
      .file-name {
        display: inline-block;
        cursor: pointer;
        &:hover {
          color: $base-color;
        }
      }
    }
    .iconfont.message {
      &.status1 {
        color: #ff0000;
      }
      &.status2 {
        color: #0000ff;
      }
      &.status3 {
        color: $base-color;
      }
    }
  }
  .version-update-maintain.el-dialog {
    .update-info {
      .label {
        margin-left: 10px;
        white-space: nowrap;
      }
      display: flex;
      align-items: center;
      .version {
        width: 110px;
        .el-input.is-disabled .el-input__inner {
          background-color: #eee;
          color: #606266;
          border: 1px solid #ccc;
        }
      }
      .update-time {
        width: 160px;
      }
      .update-duration {
        width: 75px;
        margin-right: 5px;
      }
    }
    .update-content {
      display: flex;
      width: 100%;
      margin-top: 10px;
      padding-right: 10px;
      box-sizing: border-box;
      height: calc(100% - 65px);
      .content {
        width: 100%;
        height: 100%;
        margin-left: 10px;
        .content-label {
          margin-left: 5px;
        }
        .content-input {
          margin-top: 2px;
          p {
            padding: 5px 6px 1px 6px;
          }
          p + p {
            padding: 1px 6px;
          }
        }
      }
    }
  }
}
</style>
