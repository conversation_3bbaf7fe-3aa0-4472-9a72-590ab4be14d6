<!--
 * FilePath     : \src\autoPages\singlePatientBatchExecution\batchExecutionSchedule.vue
 * Author       : 苏军志
 * Date         : 2022-05-23 11:38
 * LastEditors  : 张现忠
 * LastEditTime : 2025-01-06 14:25
 * Description  : 单病人批量执行排程画面
 * CodeIterationRecord: 2744-作为IT人员，我需要排程执行疼痛及批量执行，可以跳转专项护理 2022-07-07 杨欣欣
                        2807-部分：ICU默认展开收缩的树形节点行 2022-08-02 zxz
                        2811-作为数据配置人员，我需要合并监测生命体征和心电监护措施内容，以利临床排程操作 2022-08-20 En
                        2869-导管跳转IO改版 2022-12-01 杨欣欣
-->
<template>
  <base-layout class="batch-execution-schedule" v-loading="loading" :element-loading-text="loadingText">
    <div slot="header" class="header">
      <span class="label">班别日期：</span>
      <el-date-picker
        type="date"
        placeholder="选择班别日期"
        v-model="shiftDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        class="shift-date"
      ></el-date-picker>
      <shift-selector
        :width="convertPX(110) + ''"
        label="班别："
        v-model="shiftID"
        :stationID="stationID"
      ></shift-selector>
      <span class="label">排程频率：</span>
      <el-select v-model="timeInterval" placeholder="请选择频率" class="time-interval">
        <el-option
          v-for="(item, index) in timeIntervalList"
          :key="index"
          :label="item.description"
          :value="item.settingValue"
        ></el-option>
      </el-select>
      <div class="header-btn">
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
      </div>
    </div>
    <div class="execution-schedule-wrap" slot-scope="layout" ref="layout">
      <div :class="['execution-schedule-left', { 'show-right': toggleRight }]">
        <u-table
          ref="scheduleTable"
          use-virtual
          show-header-overflow
          show-body-overflow
          row-id="id"
          row-key
          :highlight-current-row="false"
          :height="layout.height"
          :row-class-name="getTableRowClass"
          :tree-props="{ hasChildren: 'hasChildren', children: 'children' }"
          :tree-config="{
            children: 'children',
            expandAll: false,
            trigger: 'cell',
          }"
          :row-height="convertPX(42)"
        >
          <u-table-column
            v-for="(column, index) in columns"
            :key="index"
            :label-class-name="column.hasChild ? 'whole-point' : ''"
            :min-width="convertPX(column.width)"
            :tree-node="index == 0"
            :align="index == 0 ? 'left' : 'center'"
            :fixed="index == 0 ? 'left' : undefined"
          >
            <span slot="header" slot-scope="scope" @dblclick="showBatchExecuteTimeSchedule(scope.$index)">
              {{ column.title }}
            </span>
            <template slot-scope="scope">
              <span
                v-if="index == 0"
                v-longpress="{
                  function: 'showMessage',
                  params: scope.row[0].showMessage,
                }"
                :title="scope.row[0].showMessage"
              >
                {{ scope.row[0].interventionName }}
              </span>
              <template v-if="index > 0 && scope.row[index].hasSchedule">
                <!-- 排程主项 -->
                <template v-if="scope.row[0].type == 'main'">
                  <el-tooltip content="执行排程" v-if="scope.row[index].status == '0'">
                    <i class="iconfont icon-execute" @click="showExecuteSchedule(scope.row[0], scope.row[index])"></i>
                  </el-tooltip>
                  <el-tooltip content="修改排程" v-if="scope.row[index].status == '1'">
                    <i class="iconfont icon-edit" @click="showExecuteSchedule(scope.row[0], scope.row[index])"></i>
                  </el-tooltip>
                </template>
                <!-- 排程细项 -->
                <template v-else>
                  <template v-if="scope.row[0].style == 'C'">
                    <div>
                      <el-checkbox
                        v-model="scope.row[index].scheduleData"
                        @change="changeItem(scope.row[0], scope.row[index], index)"
                      ></el-checkbox>
                    </div>
                  </template>
                  <template v-if="scope.row[0].style == 'T'">
                    <el-input
                      v-model="scope.row[index].scheduleData"
                      :name="'input-' + index + '-' + scope.$index"
                      @keyup.native.stop="switchFocus"
                      @change="changeItem(scope.row[0], scope.row[index], index)"
                    ></el-input>
                  </template>
                  <template v-if="scope.row[0].style == 'TN'">
                    <el-input
                      :readonly="isReadOnly"
                      :name="'input-' + index + '-' + scope.$index"
                      @keyup.native.stop="switchFocus"
                      v-model="scope.row[index].scheduleData"
                      @click.native="showKeyBoard($event)"
                      @change="changeItem(scope.row[0], scope.row[index], index)"
                    ></el-input>
                  </template>
                  <template v-if="scope.row[0].style == 'DL'">
                    <el-select
                      v-model="scope.row[index].scheduleData"
                      :multiple="scope.row[0].isMultiple"
                      @change="changeItem(scope.row[0], scope.row[index], index)"
                    >
                      <el-option
                        v-for="(item, dlIndex) in scope.row[0].options"
                        :key="dlIndex"
                        :label="item.label"
                        :value="item.assessListID == 0 ? item.value : item.assessListID"
                      ></el-option>
                    </el-select>
                  </template>
                  <template v-if="scope.row[0].style == 'B'">
                    <el-badge
                      v-if="scope.row[index].scheduleData"
                      :class="[
                        'badge-item',
                        {
                          'is-string': !/^\d+$/.test(scope.row[index].scheduleData),
                        },
                      ]"
                      :is-dot="scope.row[index].scheduleData != ''"
                    >
                      <el-button size="mini" type="primary" @click="openButtonDialog(scope.row, index)">
                        {{ formatButtonShowName(scope.row[0].interventionName) }}
                      </el-button>
                    </el-badge>
                    <el-button v-else size="mini" type="primary" @click="openButtonDialog(scope.row, index)">
                      {{ formatButtonShowName(scope.row[0].interventionName) }}
                    </el-button>
                  </template>
                </template>
              </template>
            </template>
          </u-table-column>
        </u-table>
      </div>
      <!-- 排程执行边栏，展示当前时间段内的所有排程 -->
      <div class="execution-schedule-right" v-if="toggleRight">
        <div class="right-main-wrap">
          <template v-if="executeScheduleParams && executeScheduleParams.length > 0">
            <div
              :class="['right-main', { multiple: executeScheduleParams.length > 1 }]"
              v-for="(executeSchedule, index) in executeScheduleParams"
              :key="index"
            >
              <div class="intervention-name">
                {{ executeSchedule.scheduleTime + "-" + executeSchedule.interventionName }}
              </div>
              <schedule-perform
                :ref="'schedulePerform' + index"
                hideOperate
                notDialog
                :params="executeSchedule"
              ></schedule-perform>
            </div>
          </template>
          <div v-else class="no-data">排程执行面板</div>
        </div>
        <div class="right-footer">
          <el-button @click="switchRight">关闭</el-button>
          <el-button
            v-if="executeScheduleParams && executeScheduleParams.length > 0"
            type="primary"
            @click="rightSave()"
          >
            保存
          </el-button>
        </div>
      </div>
      <i :class="['toggle-right', 'iconfont', 'icon-back', { opened: toggleRight }]" @click="switchRight"></i>
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="extExecuteTitle"
        fullscreen
        custom-class="external-url no-footer"
        :visible.sync="showExtExecuteFlag"
      >
        <iframe
          v-if="showExtExecuteFlag"
          ref="showExtExecuteDialog"
          width="100%"
          height="100%"
          frameborder="0"
        ></iframe>
      </el-dialog>
      <progress-view v-if="progressFlag" @closeProgress="progressClose" :tableData="messageData"></progress-view>
      <key-board
        v-tobody="{ id: 'key-board' }"
        :show="isShowKeyBoard"
        :output="el"
        typeName="TN"
        @hide="hideKeyBoard"
      ></key-board>
      <!-- 按钮弹出框 -->
      <el-dialog
        :title="buttonName"
        :close-on-click-modal="false"
        :visible.sync="showButtonDialog"
        fullscreen
        custom-class="no-footer specific-care-view"
        append-to-body
      >
        <iframe ref="buttonDialog" width="100%" height="99%" frameborder="0"></iframe>
      </el-dialog>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import shiftSelector from "@/components/selector/shiftSelector";
import keyBoard from "@/components/KeyBoard/KeyBoard";
import progressView from "@/components/progressView";
import schedulePerform from "@/pages/schedule/components/schedulePerform";
import { mapGetters } from "vuex";
import { GetClinicalBySettingTypeCode } from "@/api/Setting";
import { GetNowStationShiftData } from "@/api/StationShift";
import {
  GetPatientScheduleForBatchExecution,
  SavePatientScheduleSingle,
  SaveScheduleAssess,
  GetScheuleExcuteStatus,
  GetButtonData,
} from "@/api/PatientSchedule";
import { PerformMedicine } from "@/api/MedicineSchedule";
import { SavePatientScore } from "@/api/PatientScore";
export default {
  components: {
    baseLayout,
    shiftSelector,
    keyBoard,
    progressView,
    schedulePerform,
  },
  data() {
    return {
      // 提示开关
      loading: false,
      // 提示文本
      loadingText: "加载中……",
      // 班别日期
      shiftDate: "",
      // 班别ID
      shiftID: "",
      // 病区序号
      stationID: "",
      // 表格列时间频率
      timeInterval: "",
      // 排程频率列表
      timeIntervalList: [],
      // 表格列
      columns: [],
      // 表格数据
      rows: [],
      // 是否显示右侧执行排程面板
      toggleRight: false,
      // TN类是否可编辑
      isReadOnly: false,
      // 是否显示虚拟数字键盘
      isShowKeyBoard: false,
      // 虚拟数字键盘绑定输入框
      el: undefined,
      // 表格异动数据
      changeData: [],
      // 要保存的数据集合
      saveDatas: [],
      // 批量执行排程所需参数集合
      executeScheduleParams: [],
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      //进度条开关
      progressFlag: false,
      // 显示执行L类排程对话框
      showExtExecuteFlag: false,
      // 执行L类排程对话框标题
      extExecuteTitle: "",
      // 当前执行的L类排程信息
      extScheduleInfo: undefined,
      // 画面中所有input框，用于获取焦点
      inputs: [],
      // 按钮类相关变量
      buttonName: "",
      showButtonDialog: false,
      buttonInterventionDetailID: 0,
      // 标识该专项来自哪条排程
      sourceID: "",
      // 时间段下标，标识点击的哪一时间段的按钮
      timeIndex: undefined,
      // 当前选择的排程细项描述
      scheduleItem: undefined,
      //默认树形结构的展开和闭合
      defaultExpandAll: false,
      // 监测生命体征排程号
      vitalSignsInterventionID: 6633,
      checkResult: true,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  watch: {
    patientInfo: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.stationID = newValue.stationID;
          this.getPatientScheduleList();
        } else {
          this.columns = [];
          this.rows = [];
        }
      },
    },
    shiftDate(newValue) {
      if (newValue) {
        this.getPatientScheduleList();
      }
    },
    shiftID(newValue) {
      if (newValue) {
        this.getPatientScheduleList();
      }
    },
    stationID(newValue) {
      if (newValue) {
        this.getPatientScheduleList();
      }
    },
    timeInterval(newValue) {
      if (newValue) {
        this.getPatientScheduleList();
      }
    },
    async showExtExecuteFlag(newValue) {
      if (!newValue && this.extScheduleInfo) {
        this.saveDatas.push(this.extScheduleInfo);
        await this.refreshData();
      }
    },
    showButtonDialog(newVal) {
      if (!newVal) {
        this.updateButton();
      }
    },
  },
  created() {
    this.getNowStationShiftData();
    this.getTimeIntervalList();
  },
  methods: {
    /**
     * description: 获取当前病区所属班别
     * param {*}
     * return {*}
     */
    getNowStationShiftData() {
      GetNowStationShiftData().then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.shiftDate = result.data.shiftDate;
        }
      });
    },
    /**
     * description: 获取排程频率列表
     * param {*}
     * return {*}
     */
    getTimeIntervalList() {
      let params = {
        settingTypeCode: "BatchPerformTimeInterval",
        firstSpace: false,
      };
      GetClinicalBySettingTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.timeIntervalList = res.data;
          if (this.timeIntervalList && this.timeIntervalList.length > 0) {
            this.timeInterval = this.timeIntervalList[0].settingValue;
          }
        }
      });
    },

    /**
     * description: 获取患者排程清单
     * param {*}
     * return {*}
     */
    async getPatientScheduleList() {
      if (!this.shiftDate || !this.shiftID || !this.stationID || !this.timeInterval || !this.patientInfo) {
        return;
      }
      this.executeScheduleParams = undefined;
      this.toggleRight = false;
      let params = {
        shiftDate: this.shiftDate,
        shiftID: this.shiftID,
        stationID: this.stationID,
        inpatientID: this.patientInfo.inpatientID,
        batchPerformTimeInterval: this.timeInterval,
      };
      this.columns = [];
      this.rows = [];
      this.changeData = [];
      this.loading = true;
      this.loadingText = "加载中……";

      await GetPatientScheduleForBatchExecution(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result) && result.data) {
          if (result.data.columns) {
            this.columns = result.data.columns;
          }
          if (result.data.rows) {
            if (this.$refs.scheduleTable) {
              this.rows = result.data.rows;
              //ICU默认展开所有可收缩的树形子节点(如果参数为null，默认false)
              this.defaultExpandAll = result.data.expandAll || false;
              this.$refs.scheduleTable.treeConfig.expandAll = result.data.expandAll || false;
              this.$refs.scheduleTable.reloadData(this.rows);
              this.initChangeItem();
              // 数据渲染完，重置表格，防止样式错乱
              this.$nextTick(() => {
                if (this.$refs.scheduleTable) {
                  this.$refs.scheduleTable.doLayout();
                }
                this.getAllInput();
              });
            }
          }
        }
      });
    },
    /**
     * description: 已有数据回显时记录异动，保证数据一致性
     * param {*}
     * return {*}
     */
    initChangeItem() {
      this.changeData = [];
      this.rows.forEach((row) => {
        if (row.children && row.children.length > 0) {
          row.children.forEach((item) => {
            for (let index in item) {
              if (index > 0 && item[index].hasSchedule && item[index].scheduleData + "" != "") {
                this.changeItem(item[0], item[index], index, true);
              }
            }
          });
        }
      });
    },
    /**
     * description: 排程数据改变时，打变化标记，为保存数据做准备
     * param {*} schedule 排程内容
     * param {*} item 异动的时间点项目
     * return {*}
     */
    changeItem(schedule, item, columnIndex, initFlag) {
      if (item.scheduleData == null || item.scheduleData == "null") {
        item.scheduleData = "";
      }
      // 由于u-table的虚拟表格数据未双向绑定，这里需要把异动的数据记录下来
      // 处理异动数据，这里只有DL类需要特殊处理
      if (schedule.style == "DL") {
        let values = [];
        if (schedule.isMultiple) {
          values = item.scheduleData;
        } else {
          // 单选值处理下 用户选择的空白数据不保存
          if (item.scheduleData != "0") {
            values.push(item.scheduleData);
          }
        }
        schedule.options.forEach((option) => {
          // 先删除所有的选项
          this.delChangeData(schedule.mainID, option.value, columnIndex);
          // 再把本次勾选的值，新增异动
          if (values && values.length > 0) {
            values.forEach((value) => {
              if (option.assessListID == value || option.value == value) {
                let data = {
                  columnIndex: columnIndex,
                  mainID: schedule.mainID,
                  interventionDetailID: option.value,
                  assessListID: option.assessListID,
                  scheduleData: option.label,
                  deleteFlag: false,
                  initFlag: initFlag,
                  updateFlag: !initFlag,
                };
                this.addChangeData(data);
              }
            });
          }
        });
      } else {
        let scheduleData = item.scheduleData;
        let deleteFlag = false;
        if (scheduleData + "" == "" || schedule.style == "B") {
          this.delChangeData(schedule.mainID, schedule.id, columnIndex);
          return;
        }
        // TN类需要检核
        if (schedule.style == "TN") {
          let checkTN = {
            controlerType: schedule.style,
            itemName: schedule.interventionName,
            assessValue: item.scheduleData,
            decimal: schedule.decimal,
            upError: schedule.upError,
            lowError: schedule.lowError,
            checkLevelDict: schedule.checkLevelDict,
          };
          // 检核TN条件，不符合检核，清空排程数据
          let result = this._common.checkAssessTN(checkTN);
          item.scheduleData = result.value;
          if (!result.flag) {
            return;
          }
        }
        if (schedule.style == "C") {
          scheduleData = schedule.interventionName;
          if (!item.scheduleData) {
            deleteFlag = true;
          }
        }
        let data = {
          columnIndex: columnIndex,
          mainID: schedule.mainID,
          interventionDetailID: schedule.id,
          assessListID: schedule.assessListID,
          scheduleData: scheduleData,
          deleteFlag: deleteFlag,
          initFlag: initFlag,
          updateFlag: !initFlag,
        };
        this.addChangeData(data);
      }
    },
    /**
     * description: 添加异动记录
     * param {*} data 异动记录
     * return {*}
     */
    addChangeData(data) {
      // 如果有就替换  没有就添加
      let index = this.changeData.findIndex((tempData) => {
        return (
          tempData.mainID == data.mainID &&
          tempData.interventionDetailID == data.interventionDetailID &&
          tempData.columnIndex == data.columnIndex
        );
      });
      if (index !== -1) {
        // 对已执行过的数据进行特殊处理
        if (this.changeData[index].initFlag) {
          data.initFlag = true;
          if (this.changeData[index].oldData == data.scheduleData) {
            data.updateFlag = false;
          } else {
            data.updateFlag = true;
            data.oldData = this.changeData[index].scheduleData;
          }
        }
        this.changeData.splice(index, 1, data);
      } else {
        this.changeData.push(data);
      }
    },
    /**
     * description: 异动记录打删除标记
     * param {*}
     * return {*}
     */
    delChangeData(mainID, interventionDetailID, columnIndex) {
      let index = this.changeData.findIndex((tempData) => {
        return (
          tempData.mainID == mainID &&
          tempData.interventionDetailID == interventionDetailID &&
          tempData.columnIndex == columnIndex
        );
      });
      if (index !== -1) {
        this.changeData[index].deleteFlag = true;
        // 非初始话的才需要清空数据
        if (this.changeData[index].initFlag) {
          this.changeData[index].oldData = this.changeData[index].scheduleData;
        } else {
          this.changeData[index].scheduleData = "";
        }
      }
    },

    /**
     * description: 独立执行排程
     * param {*} row
     * return {*}
     */
    showExecuteSchedule(schedule, item) {
      // 执行L类排程,弹窗
      if (schedule.interventionType == "L") {
        this.extPatientScheduleMainID = item.patientScheduleMainID;
        this.extScheduleInfo = {
          interventionType: schedule.interventionType,
          params: {
            patientScheduleMainID: item.patientScheduleMainID,
            interventionID: schedule.id,
          },
        };
        if (schedule.performUrl) {
          this.extExecuteTitle =
            this.patientInfo.patientName +
            "【" +
            this.patientInfo.gender +
            "-" +
            this.patientInfo.ageDetail +
            "】" +
            "-- " +
            schedule.interventionName.replace("<br>", "；").replace("</br>", "；");
          this.showExtExecuteFlag = true;
          let performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
          let performTime = this._datetimeUtil.getNowTime("hh:mm");
          if (item.performDate) {
            performDate = this._datetimeUtil.formatDate(item.performDate, "yyyy-MM-dd");
          }
          if (item.performTime) {
            performTime = this._datetimeUtil.formatDate(item.performTime, "hh:mm");
          }
          let performUrl = schedule.performUrl;
          performUrl +=
            (schedule.performUrl.includes("?") ? "&" : "?") +
            `patientScheduleMainID=${item.patientScheduleMainID}` +
            "&inpatientID=" +
            this.patientInfo.inpatientID +
            "&scheduleDate=" +
            performDate +
            "&scheduleTime=" +
            performTime +
            "&completeMark=" +
            item.status +
            "&stationID=" +
            this.patientInfo.stationID +
            "&departmentListID=" +
            this.patientInfo.departmentListID +
            "&nursingLevel=" +
            this.patientInfo.nursingLevel +
            "&isDialog=true";

          this.$nextTick(() => {
            this.$refs.showExtExecuteDialog.contentWindow.location.replace(performUrl);
          });
        }
      } else {
        if (!this.toggleRight) {
          this.toggleRight = true;
        }
        this.executeScheduleParams = [];
        let executeScheduleParam = this.getExecuteScheduleParams(schedule, item);
        if (executeScheduleParam) {
          this.executeScheduleParams.push(executeScheduleParam);
        }
      }
    },
    /**
     * description: 批量执行同时间点所有排程
     * param {*} columnIndex 时间点所在列
     * return {*}
     */
    showBatchExecuteTimeSchedule(columnIndex) {
      if (!this.rows || this.rows.length <= 0) {
        return;
      }
      if (!this.toggleRight) {
        this.toggleRight = true;
      }
      let executeScheduleParams = [];
      this.rows.forEach((row) => {
        if (row[columnIndex].hasSchedule && row[0].interventionType != "L") {
          let executeScheduleParam = this.getExecuteScheduleParams(row[0], row[columnIndex]);
          if (executeScheduleParam) {
            executeScheduleParams.push(executeScheduleParam);
          }
        }
      });
      this.executeScheduleParams = executeScheduleParams;
    },
    /**
     * description: 根据排程的interventionType组装执行排程需要的参数及数据
     * param {*} schedule
     * param {*} item
     * return {*}
     */
    getExecuteScheduleParams(schedule, item) {
      let executeScheduleParam = {
        interventionType: schedule.interventionType,
        interventionID: schedule.id,
        interventionName: schedule.interventionName,
        performUrl: schedule.performUrl,
        actionType: schedule.actionType,
        instrumentTimeRange: schedule.instrumentTimeRange,
        // 暂时不包含药嘱数据，先不传orderCode
        // orderCode:schedule.orderCode,
        performDate: item.performDate,
        performTime: item.performTime,
        bringToNursingRecord: item.bringToNursingRecords,
        bringToShift: item.bringToShift,
        informPhysician: item.informPhysician,
        performComment: item.performComment,
        patientScheduleMainID: item.patientScheduleMainID,
        patientAssessMainID: item.patientAssessMainID,
        patientInterventionID: item.patientInterventionID,
        performEmployeeID: item.performEmployeeID,
        scheduleDate: item.scheduleDate,
        scheduleTime: item.scheduleTime,
        stationID: this.patientInfo.stationID,
        inpatientID: this.patientInfo.inpatientID,
      };
      return executeScheduleParam;
    },
    /**
     * description: 打开或关闭右侧面板
     * param {*}
     * return {*}
     */
    switchRight() {
      this.toggleRight = !this.toggleRight;
      // 如果关闭，清空右侧面板的数据
      // if (this.toggleRight) {
      //   this.executeScheduleParams = [];
      // }
      this.$nextTick(() => {
        if (this.$refs.scheduleTable) {
          this.$refs.scheduleTable.doLayout();
        }
      });
    },
    /**
     * description: 右侧排程执行面板的保存按钮
     * param {*}
     * return {*}
     */
    rightSave() {
      if (!this.executeScheduleParams || this.executeScheduleParams.length <= 0) {
        return;
      }
      this.$nextTick(() => {
        let length = this.executeScheduleParams.length;
        this.saveDatas = [];
        for (let index = 0; index < length; index++) {
          let schedulePerform = this.$refs["schedulePerform" + index][0];
          if (schedulePerform && schedulePerform.getSaveData) {
            let saveData = schedulePerform.getSaveData();
            if (saveData) {
              let data = {
                interventionType: this.executeScheduleParams[index].interventionType,
                actionType: this.executeScheduleParams[index].actionType,
                interventionName: this.executeScheduleParams[index].interventionName,
                scheduleDate: this.executeScheduleParams[index].scheduleDate,
                scheduleTime: this.executeScheduleParams[index].scheduleTime,
                params: saveData,
              };
              this.saveDatas.push(data);
            }
          }
        }
        this.batchExecuteTimeSchedule();
      });
    },
    /**
     * description: 保存排程数据
     * param {*}
     * return {*}
     */
    async save() {
      if (!this.changeData || this.changeData.length <= 0) {
        this._showTip("warning", "没有措施需要保存！");
        return;
      }
      let scheduleData = [];
      // 先组出要保存的数据
      this.changeData.forEach((data) => {
        let index = scheduleData.findIndex((tempData) => {
          return tempData.columnIndex == data.columnIndex && tempData.mainID == data.mainID;
        });
        if (index != -1) {
          scheduleData[index].details.push(data);
        } else {
          let temp = {
            columnIndex: data.columnIndex,
            mainID: data.mainID,
            details: [data],
          };
          scheduleData.push(temp);
        }
      });
      // 组装要保存的数据格式
      this.saveDatas = [];
      let performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      let performTime = this._datetimeUtil.getNowTime("hh:mm");
      scheduleData.forEach((data) => {
        let row = this.rows.find((temp) => {
          return temp[0].id == data.mainID;
        });
        if (row) {
          let saveFlag = false;
          let changeDetail = data.details.find((detail) => detail.updateFlag || (detail.initFlag && detail.deleteFlag));
          // 如果已执行的数据有异动，需要保存
          if (changeDetail) {
            saveFlag = true;
          }
          if (saveFlag) {
            // 获取需要保存的排程明细数据
            let details = [];
            data.details.forEach((detail) => {
              if (!detail.deleteFlag && detail.mainID != this.vitalSignsInterventionID) {
                details.push({
                  assessListID: detail.assessListID,
                  scheduleData: detail.scheduleData,
                  interventionDetailID: detail.interventionDetailID,
                });
              }
              //监测生命体征保存使用A类保存方法
              if (detail.mainID == this.vitalSignsInterventionID && !detail.deleteFlag) {
                details.push({
                  assessListID: detail.assessListID,
                  assessValue: detail.scheduleData,
                  assessListGroupID: detail.assessListGroupID,
                });
              }
            });
            let scheduleMain = row[data.columnIndex];
            let saveData = {
              inpatientID: this.patientInfo.inpatientID,
              bringToNursingRecords: scheduleMain.bringToNursingRecords,
              bringToShift: scheduleMain.bringToShift,
              patientInterventionID: scheduleMain.patientInterventionID,
              patientAssessMainID: scheduleMain.patientAssessMainID,
              patientScheduleMainID: scheduleMain.patientScheduleMainID,
              interventionID: data.mainID,
              performComment: "",
              saveData: details,
              scheduleDate: scheduleMain.performDate ? scheduleMain.performDate : performDate,
              scheduleTime: scheduleMain.performTime ? scheduleMain.performTime : performTime,
              userID: this.user.userID,
            };
            let tempData = {
              interventionType: row[0].interventionType,
              actionType: row[0].actionType,
              interventionName: row[0].interventionName,
              scheduleDate: scheduleMain.scheduleDate,
              scheduleTime: scheduleMain.scheduleTime,
              params: saveData,
            };
            this.saveDatas.push(tempData);
          }
        }
      });
      await this.batchExecuteTimeSchedule();
    },
    /**
     * description: 批量执行排程
     * param {*}
     * return {*}
     */
    async batchExecuteTimeSchedule() {
      if (!this.saveDatas || this.saveDatas.length <= 0) {
        this._showTip("warning", "没有措施需要保存！");
        return;
      }
      let count = this.saveDatas.length;
      if (count == 1) {
        this.loading = true;
        this.loadingText = "保存中……";
        let data = this.saveDatas[0];
        await this.executeSchedule(data.interventionType, data.actionType, data.params, true, "");
      } else {
        this.progressFlag = true;
        //循环保存排程
        for (let i = 0; i < count; i++) {
          let data = this.saveDatas[i];
          let interventionInfo = data.scheduleTime + "-" + data.interventionName;
          await this.executeSchedule(data.interventionType, data.actionType, data.params, false, interventionInfo);
          //设置进度条进度
          this.messageData[0].value = Number((((Number(i) + 1) / count) * 100).toFixed(0));
        }
      }
    },
    /**
     * description: 按照interventionType类型执行排程
     * param {*} interventionType 排程执行类型
     * param {*} actionType 措施类型
     * param {*} params 排程执行参数
     * param {*} isSingle   是否单条排程
     * param {*} interventionInfo 批量执行时排程提示信息
     * return {*}
     */
    async executeSchedule(interventionType, actionType, params, isSingle, interventionInfo) {
      if (interventionType == "A") {
        // 执行A类排程
        if (params.saveData && params.saveData.length > 0) {
          params.details = params.saveData;
        }
        await SaveScheduleAssess(params).then(async (result) => {
          await this.executeResult(result, isSingle, interventionInfo);
        });
      } else if (interventionType == "S") {
        // 执行S类排程
        await SavePatientScore(params).then(async (result) => {
          await this.executeResult(result, isSingle, interventionInfo);
        });
      } else {
        // 执行D类或T类排序
        // 执行药嘱类排程
        if (actionType == 5) {
          await PerformMedicine(params).then(async (result) => {
            await this.executeResult(result, isSingle, interventionInfo);
          });
        } else {
          // 执行非药嘱类排程
          await SavePatientScheduleSingle(params).then(async (result) => {
            await this.executeResult(result, isSingle, interventionInfo);
          });
        }
      }
    },
    /**
     * description: 执行排程结果处理
     * param {*} result 执行排程结果
     * param {*} isSingle 是否是单条排程
     * param {*} interventionInfo 批量执行时排程提示信息
     * return {*}
     */
    async executeResult(result, isSingle, interventionInfo) {
      if (isSingle) {
        this.loading = false;
      }
      if (this._common.isSuccess(result)) {
        if (isSingle) {
          this._showTip("success", "保存成功！");
          await this.refreshData();
        } else {
          this.messageData[1].value += interventionInfo + "、";
        }
      } else {
        if (!isSingle) {
          this.messageData[2].value += interventionInfo + "、";
        }
      }
    },
    /**
     * description: 保存完刷新局部数据状态
     * param {*}
     * return {*}
     */
    async refreshData() {
      // 清空右侧面板的数据
      this.executeScheduleParams = [];
      if (!this.saveDatas || this.saveDatas.length <= 0) {
        return;
      }
      for (let index = 0; index < this.saveDatas.length; index++) {
        let item = this.saveDatas[index];
        if (item.params) {
          // S类排程参数处理下
          if (item.interventionType == "S") {
            item.params.interventionID = item.params.NursingInterventionMainID;
            item.params.patientScheduleMainID = item.params.SourceID;
          }
          for (let i = 0; i < this.rows.length; i++) {
            // 先找到对应排程
            if (item.params.interventionID == this.rows[i][0].id) {
              let columnIndex = undefined;
              // 然后找到对应的时间点的排程
              for (let key in this.rows[i]) {
                if (item.params.patientScheduleMainID == this.rows[i][key].patientScheduleMainID) {
                  columnIndex = key;
                  break;
                }
              }
              if (columnIndex > 0) {
                if (item.interventionType == "L") {
                  // L类未执行的才调用API判断是否执行，已执行的不处理
                  if (this.rows[i][columnIndex].status != "1") {
                    // 调用API判断是否执行成功
                    let params = {
                      patientScheduleMainID: item.params.patientScheduleMainID,
                    };
                    await GetScheuleExcuteStatus(params).then((res) => {
                      if (this._common.isSuccess(res)) {
                        if (res.data == "1") {
                          // 执行成功
                          this.rows[i][columnIndex].status = "1";
                        }
                      }
                    });
                  }
                } else {
                  // 将排程状态改为已执行
                  this.rows[i][columnIndex].status = "1";
                  this.rows[i][columnIndex].bringToNursingRecords = item.params.bringToNursingRecords;
                  this.rows[i][columnIndex].bringToShift = item.params.bringToShift;
                  this.rows[i][columnIndex].informPhysician = item.params.informPhysician;
                  this.rows[i][columnIndex].performComment = item.params.performComment;
                }
              }
              //如果D类或T类排程，并且有明细数据，则处理排程明细回显
              if (
                item.params &&
                item.params.saveData &&
                this.rows[i].children &&
                columnIndex > 0 &&
                (item.interventionType == "D" ||
                  item.interventionType == "T" ||
                  (item.interventionType == "A" && item.params.interventionID == this.vitalSignsInterventionID))
              ) {
                let id = "interventionDetailID";
                let childID = "id";
                let dlChildID = "value";
                let childValue = "scheduleData";
                if (item.interventionType == "A" && item.params.interventionID == this.vitalSignsInterventionID) {
                  id = "assessListID";
                  childID = "assessListID";
                  dlChildID = "assessListID";
                  childValue = "assessValue";
                }
                let details = item.params.saveData;
                this.rows[i].children.forEach((child) => {
                  child[columnIndex].scheduleData = "";
                  if (child[0].style == "DL") {
                    // 下拉框特殊处理
                    let value = [];
                    child[0].options.forEach((option) => {
                      let detail = details.find((detail) => {
                        return detail[id] + "" == option[dlChildID];
                      });
                      if (detail) {
                        value.push(option.assessListID == 0 ? option.value : option.assessListID);
                      }
                    });
                    if (value.length > 0) {
                      // 单选
                      if (!child[0].isMultiple && value.length == 1) {
                        child[columnIndex].scheduleData = value[0];
                      } else {
                        // 多选
                        child[columnIndex].scheduleData = value;
                      }
                    } else {
                      child[columnIndex].scheduleData = "";
                    }
                  } else {
                    // 非下拉框处理
                    let detail = details.find((detail) => detail[id] == child[0][childID]);
                    if (detail) {
                      if (child[0].style == "C") {
                        child[columnIndex].scheduleData = true;
                      } else {
                        child[columnIndex].scheduleData = detail[childValue];
                      }
                    } else {
                      // 保存明细里没有，说明前端已取消勾选
                      child[columnIndex].scheduleData = "";
                    }
                  }
                });
              }
              break;
            }
          }
        }
      }
      this.$refs.scheduleTable.reloadData(this.rows);
      this.initChangeItem();
      // 数据渲染完，重置表格，防止样式错乱
      this.$nextTick(() => {
        if (this.$refs.scheduleTable) {
          this.$refs.scheduleTable.doLayout();
        }
      });
      //保存后，按照最初的状态显示
      if (this.defaultExpandAll) {
        //展开
        this.$refs.scheduleTable.setAllTreeExpansion();
      } else {
        //收缩表格中的treeNode
        this.$refs.scheduleTable.clearTreeExpand();
      }

      this.saveDatas = [];
      // 如果右侧面板处于打开状态，则关闭
      if (this.toggleRight) {
        this.switchRight();
      }
    },
    /**
     * description: 获取表格当前行的class
     * param {*} row 当前行
     * return {*}
     */
    getTableRowClass({ row }) {
      return row[0].type == "main" ? "main-row" : "sub-row";
    },
    /**
     * description: 长按项目显示项目说明信息
     * param {*} messageContent 要显示的说明信息
     * return {*}
     */
    showMessage(messageContent) {
      if (!messageContent) {
        return;
      }
      this._showMessage({
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
    /**
     * description: 判断是否需要显示虚拟数字键盘
     * param {*} e 当前鼠标事件
     * return {*}
     */
    showKeyBoard(e) {
      // 判断浏览器类型，PC返回true，移动端返回false
      if (this._common.isPC()) {
        this.isReadOnly = false;
        return;
      }
      this.isReadOnly = true;
      this.el = e.target;
      this.isShowKeyBoard = true;
    },
    /**
     * description: 隐藏虚拟数字键盘
     * param {*}
     * return {*}
     */
    hideKeyBoard() {
      this.el = undefined;
      this.isShowKeyBoard = false;
    },
    /**
     * description: 进度条重置
     * param {*}
     * return {*}
     */
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    /**
     * description: 进度条关闭函数
     * param {*}
     * return {*}
     */
    async progressClose() {
      this.progressFlag = false;
      this.renewMessageData();
      // 刷新保存后的数据状态
      await this.refreshData();
    },
    /**
     * description: 获取所有input框
     * param {*}
     * return {*}
     */
    getAllInput() {
      if (this.$refs.layout) {
        this.inputs = this.$refs.layout.getElementsByTagName("input");
        let inputList = [];
        for (let index = 0; index < this.inputs.length; index++) {
          let input = this.inputs[index];
          if (input.name && input.name.indexOf("input") != -1) {
            let strs = input.name.split("-");
            if (strs.length == 3) {
              let x = strs[1];
              let y = strs[2];
              input.x = x;
              input.y = y;
              inputList.push(input);
            }
          }
        }
        this.inputs = inputList;
      }
    },
    /**
     * description: 切换焦点事件
     * param {*} e事件本身
     * return {*}
     */
    switchFocus(e) {
      // 没有获取到input框集合，不处理
      if (!this.inputs || this.inputs.length <= 0) {
        return;
      }
      let keyCodes = [37, 38, 39, 40];
      // 非上下左右键，不处理
      if (keyCodes.indexOf(e.keyCode) == -1) {
        return;
      }
      let input = this.inputs.find((input) => input.name == e.target.name);
      // 当前input框不在input框集合中，不处理
      if (!input || !input.name || input.name.indexOf("-") == -1) {
        return;
      }
      let rowInputs = undefined;
      let colunmInputs = undefined;
      let nextInput = undefined;
      let yIndex = -1;
      let xIndex = -1;

      //左右
      if (e.keyCode == 37 || e.keyCode == 39) {
        // 先找到同行的所有input框
        rowInputs = this.inputs.filter((tempInput) => tempInput.y == input.y);
        // 排序
        rowInputs = rowInputs.sort(this.compare("x"));
        xIndex = rowInputs.findIndex((rowInput) => rowInput.name == input.name);
        // 左
        if (e.keyCode == 37) {
          // 如果是第一个，不处理
          if (xIndex == 0) {
            return;
          }
          nextInput = rowInputs[xIndex - 1];
        }
        // 右
        if (e.keyCode == 39) {
          // 如果只有一个或者是最后一个，不处理
          if (rowInputs.length == 1 || (rowInputs.length > 0 && xIndex == rowInputs.length - 1)) {
            return;
          }
          nextInput = rowInputs[xIndex + 1];
        }
      }
      // 上下
      if (e.keyCode == 38 || e.keyCode == 40) {
        // 先找到同列的所有input框
        colunmInputs = this.inputs.filter((tempInput) => tempInput.x == input.x);
        // 排序
        colunmInputs = colunmInputs.sort(this.compare("y"));
        yIndex = colunmInputs.findIndex((rowInput) => rowInput.name == input.name);
        // 上
        if (e.keyCode == 38) {
          // 如果是第一个，不处理
          if (yIndex == 0) {
            return;
          }
          nextInput = colunmInputs[yIndex - 1];
        }
        // 下
        if (e.keyCode == 40) {
          // 如果只有一个或者是最后一个，不处理
          if (colunmInputs.length == 1 || (colunmInputs.length > 0 && yIndex == colunmInputs.length - 1)) {
            return;
          }
          nextInput = colunmInputs[yIndex + 1];
        }
      }
      // 切换焦点
      input.blur();
      nextInput.focus();
      // 获得焦点后将现有值全选
      nextInput.selectionStart = 0;
      nextInput.selectionEnd = nextInput.value.length;
    },
    /**
     * description: 数组排序
     * param {*} property 排序依据的属性
     * param {*} sortType 排序方式，A升序，D降序，不传默认升序
     * return {*}
     */
    compare(property, sortType) {
      if (!sortType) {
        sortType = "A";
      }
      //property:根据什么属性排序
      return (item1, item2) => {
        var value1 = item1[property];
        var value2 = item2[property];
        if (sortType == "A") {
          //升序排序
          return value1 - value2;
        } else {
          //降序排序
          return value2 - value1;
        }
      };
    },
    /**
     * description: 打开弹窗，跳转专项
     * param {*} scheduleItemRow 子行，排程细项行
     * param {*} colIndex 点击项目时，按钮所属的列下标，用于定位时间段
     * return {*}
     */
    openButtonDialog(scheduleItemRow, colIndex) {
      this.timeIndex = 0;
      this.scheduleItem = undefined;
      this.sourceID = "";
      this.buttonInterventionDetailID = 0;
      this.timeIndex = colIndex;
      // 当前排程的字典信息
      this.scheduleItem = scheduleItemRow[0];
      /* 
        定位所属的主行，包含了：
        [0]：排程的类别字典信息
        [1~max]：各时间段属于此类排程的信息
        children数组：此类排程的评估细项，每一个children为一个数组，下标0为此细项的字典信息，其他标记各时间段是否有排程及细项的执行数据
        id：此排程的措施mainID
      */
      let scheduleRow = this.rows.find((row) => row.id == this.scheduleItem.mainID);
      if (!scheduleRow || scheduleRow.count == 0) {
        return;
      }
      // 根据下标确定时间段，取出对应的ScheduleMainID
      this.sourceID = scheduleRow[this.timeIndex].patientScheduleMainID;

      this.buttonInterventionDetailID = this.scheduleItem.id;
      this.buttonName = this.scheduleItem.interventionName;
      let url = this.scheduleItem.linkForm;
      if (!url) {
        return;
      }

      this.showButtonDialog = true;
      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.patientInfo.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&patientScheduleMainID=${this.sourceID}` +
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        "&isDialog=true" +
        `&sourceID=${this.sourceID}` +
        "&sourceType=Schedule";
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 对评估细项的值进行更新
     * param {*}
     * return {*}
     */
    async updateButton() {
      let item = await this.getButtonValue();
      if (!item) {
        return;
      }
      // 定位所属的主行
      let scheduleRow = this.rows.find((row) => row.id == this.scheduleItem.mainID);
      if (!scheduleRow || !scheduleRow.children) {
        return;
      }
      // 此类排程所拥有的细项
      let children = scheduleRow.children;
      // 找出所点击的排程的细项
      let child = children.find((child) => {
        return child[0].id == this.buttonInterventionDetailID;
      });
      if (!child) {
        return;
      }
      // 更改对应的ScheduleData
      child[this.timeIndex].scheduleData = item.assessValue;
      // 将异动信息同步至changeData
      this.changeItem(child[0], child[this.timeIndex], this.timeIndex);
      // TODO: 暂时屏蔽，重新渲染会导致此前录入的其他数据无法显示
      // 重新渲染数据
      // this.$refs.scheduleTable.reloadData(this.rows);
      // this.initChangeItem();
      // // 数据渲染完，重置表格，防止样式错乱
      // this.$nextTick(() => {
      //   if (this.$refs.scheduleTable) {
      //     this.$refs.scheduleTable.doLayout();
      //   }
      // });
    },
    /**
     * description: 更新按钮回显数据
     * params {*}
     * return {*}
     */
    async getButtonValue() {
      let item = undefined;
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        nursingInterventionDetailID: this.buttonInterventionDetailID,
        sourceID: this.sourceID,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: 格式化按钮类的显示名称
     * param {*} showName
     * return {*}
     */
    formatButtonShowName(showName) {
      if (showName) {
        const index = showName.lastIndexOf("(");
        return index != -1 ? showName.substring(0, index).trim() : showName;
      } else {
        return "";
      }
    },
  },
};
</script>

<style lang="scss">
.batch-execution-schedule {
  width: 100%;
  height: 100%;
  .header {
    .label {
      font-size: 14px;
      margin-left: 10px;
    }
    .shift-date {
      width: 160px;
    }
    .el-input__inner {
      border-color: #c0c0c0;
    }
    .el-input__suffix {
      top: -2px;
    }
    .header-btn {
      float: right;
    }
    .time-interval {
      width: 140px;
    }
  }
  .execution-schedule-wrap {
    height: 100%;
    width: 100%;
    .execution-schedule-left {
      float: left;
      height: 100%;
      width: 100%;
      &.show-right {
        width: calc(100% - 436px);
      }
      .el-table {
        th {
          cursor: pointer;
          .whole-point {
            color: #ff0000;
          }
        }
        .sub-row .cell--tree-node .pl-tree-cell {
          padding-left: 5px;
        }
        sup {
          top: 3px;
          right: 8px;
        }
        .main-row {
          background-color: #f0efef;
          .iconfont {
            font-size: 26px;
            margin: 2px 0 0 0;
          }
        }
        /* 下拉框 */
        .el-select {
          .el-input__inner {
            padding: 0px 16px 0 5px;
            color: $base-color;
          }
          /* 多选 */
          .el-select__tags {
            height: 32px;
            line-height: 32px;
            max-width: none !important;
            span:not(.el-tag) {
              display: inline-block;
              width: 100%;
              height: 32px;
              line-height: 30px;
              text-align: left;
            }
            .el-tag {
              margin: 0;
              border: 0;
              background-color: transparent;
              & + .el-tag {
                padding-left: 0;
              }
              span {
                width: auto;
              }
              .el-select__tags-text {
                font-weight: initial;
              }
              .el-tag__close {
                display: none;
              }
            }
          }
        }
        .el-checkbox__inner {
          border-color: #808080;
        }
        .el-input__inner {
          border-color: #c0c0c0;
        }
      }
    }
    .execution-schedule-right {
      float: right;
      height: 100%;
      width: 420px;
      margin-left: 10px;
      background-color: #ffffff;
      padding: 3px;
      box-sizing: border-box;
      .right-main-wrap {
        height: calc(100% - 34px);
        overflow: auto;
        .right-main {
          height: calc(100% - 5px);
          &.multiple {
            height: auto;
            .schedule-perform {
              height: auto;
              .base-layout {
                height: auto;
              }
              .base-content {
                overflow: visible;
                height: auto;
                .base-content-wrap {
                  position: relative !important;
                  height: auto;
                }
              }
            }
          }
          .intervention-name {
            font-weight: bold;
            background-color: #ffe0ac;
            height: 28px;
            line-height: 26px;
            font-size: 16px;
            margin: 5px 10px 0 10px;
            padding: 2px 10px;
            box-sizing: border-box;
          }
          .schedule-perform {
            height: calc(100% - 34px);
            /* D类排程执行组件样式 */
            .schedule-monitor .monitor-wrap {
              padding: 0;
              overflow: hidden;
              height: auto;
              .machine-data {
                position: initial;
                width: 90%;
                margin: 0 0 10px 26px;
              }
              .el-timeline {
                position: initial;
                padding-left: 10px;
              }
            }
            /* A类排程执行组件样式 */
            .schedule-assess {
              .tabs-layout.schedule {
                height: auto;
                .el-tabs__content {
                  padding: 0 5px;
                }
                .schedule-bring {
                  height: auto;
                }
              }
            }
            /* S类排程执行组件样式 */
            .schedule-score .schedule-score-table,
            .schedule-score .schedule-score-table .el-table__body-wrapper {
              height: auto !important;
            }
          }
        }
        .no-data {
          text-align: center;
          font-size: 30px;
        }
      }
      .right-footer {
        height: 30px;
        text-align: right;
        margin: 2px;
      }
    }
    .toggle-right {
      position: absolute;
      right: 0;
      top: 50%;
      cursor: pointer;
      z-index: 100;
      font-size: 20px;
      font-weight: bold;
      border-radius: 100px;
      width: 36px;
      height: 36px;
      color: #ffffff;
      background-color: rgba(0, 0, 0, 0.2);
      padding: 4px 5px 4px 6px;
      box-sizing: border-box;
      &.opened {
        right: 400px;
        transform: rotate(180deg);
        padding: 4px 6px 4px 5px;
      }
    }
    .specific-care-view {
      background-color: #f3f3f3;
      iframe {
        height: 99%;
        border: none;
      }
    }
  }
  .external-url.el-dialog {
    background-color: #f3f3f3;
    .el-dialog__body {
      padding: 5px;
      height: calc(100% - 20px);
      iframe {
        height: calc(100% - 5px);
      }
    }
  }
}
</style>
