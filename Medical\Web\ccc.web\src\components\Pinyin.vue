<!--
 * FilePath     : \src\components\Pinyin.vue
 * Author       : 郭鹏超
 * Date         : 2020-05-13 09:50
 * LastEditors  : 来江禹
 * LastEditTime : 2023-10-30 10:27
 * Description  : 简拼查询组件
 -->
/* 组件使用说明
使用该组件可传入四个参数：
value：string类型 选填 简拼组件输入框的默认初始值，需使用V-model动态绑定；
tableName：string类型 必填 为简拼查询的类型；
inputWidth：string类型 选填 默认值为200px 控制简拼输入框的宽度 支持 auto px 百分比 数字；
selectBoole：布尔类型 选填 默认为false 为是否显示下拉开框的开关
两个事件发射：
input事件：抛出简拼输入框的值 用于输入框数据的双向绑定
postData事件：抛出简拼搜索到数据 当显示下拉框时抛出点击哪一项的数据 当不显示下拉框时 抛出搜索的所有数据
参考案例：pages>Tube>index.vue文件中
<pinyin @postData="getPinyinData" inputWidth="190" tableName="TubeList" v-model="inputData" class="pinyin-input"></pinyin>
*/
<template>
  <div
    :style="{
      width: fixInputWidth,
      borderColor: borderBoole ? '#14d8d8' : '#fff',
    }"
    class="pin-yin"
  >
    <el-input class="text-input" v-model="inputData" placeholder="输入查询内容首字母">
      <!-- <el-button  > -->
      <i @click="getSourchData" slot="append" class="iconfont icon-search"></i>
    </el-input>
    <el-input
      @blur="
        borderBoole = false;
        ulBoole = false;
      "
      @keyup.native="fixData"
      @focus="
        borderBoole = true;
        passwordInputType = 'password';
      "
      class="password-input"
      v-model="inputData"
      :type="passwordInputType"
    ></el-input>
    <ul v-if="ulBoole && selectBoole" class="select-ul">
      <li v-for="(item, index) in sourchData" :key="index" @mousedown="postData(item)">
        {{ item.name }}
      </li>
    </ul>
  </div>
</template>

<script>
import { GetByListByPinyin } from "@/api/PinyinListMaintain";
export default {
  props: {
    value: {
      //输入框默认值
      type: String,
      default: "",
    },
    tableName: {
      //查询数据表
      type: String,
      default: "",
    },
    inputWidth: {
      //输入框宽度
      type: String,
      default: "200px",
    },
    selectBoole: {
      //是否需要下拉列表
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      passwordInputType: "text",
      inputData: "",
      borderBoole: false,
      sourchData: [],
      ulBoole: false,
      fixInputWidth: "",
    };
  },
  mounted() {
    this.passwordInputType = "text";
  },
  watch: {
    value: {
      handler(newValue) {
        this.inputData = newValue;
        this.fixBal();
      },
      immediate: true,
    },
    inputData: {
      handler(newValue) {
        this.$emit("input", this.inputData);
      },
      immediate: true,
    },
  },
  methods: {
    //调整输入框宽度数据
    fixBal() {
      this.fixInputWidth = this._common.getHeigt(this.inputWidth);
    },
    //抛出数据
    postData(item) {
      this.$emit("postData", item);
      this.ulBoole = false;
    },
    //修改输入框内容
    fixData(keyData) {
      //防止已选导管后输入框为文字是调用搜索函数
      let reg = /^[0-9a-zA-Z]+$/;
      //如果输入框内为汉字并且输入框不为空 return
      if (!reg.test(this.inputData) && this.inputData != "") {
        //输入的不是简拼 置空搜索数据数组
        this.sourchData = [];
        return;
      }
      //按键不为回车键
      if (!(keyData.keyCode == 13)) {
        this.inputData = this.inputData.toUpperCase();
      } else {
        this.getSourchData();
      }
    },
    //获取输入框搜索的数据
    getSourchData() {
      this.passwordInputType = "text";
      if (!this.inputData) {
        this._showTip("warning", "请输入查询简评内容！");
        return;
      }
      let params = {
        tableName: this.tableName,
        pinyin: this.inputData,
      };
      GetByListByPinyin(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.selectBoole) {
            //实现下拉列表点击功能
            this.$set(this, "sourchData", res.data);
          } else {
            //直接抛出搜索数据
            this.$emit("postData", res.data);
          }
          this.ulBoole = true;
        }
      });
    },
  },
};
</script>
<style lang="scss">
* {
  margin: 0;
  padding: 0;
}
.pin-yin {
  position: relative;
  height: 32px;
  line-height: 32px;
  width: 200px;
  border: 1px solid #fff;
  border-radius: 5px;
  padding-bottom: 1px;
  &:active {
    border-color: #14d8d8;
  }
  .password-input,
  .text-input {
    width: 100%;
    height: 31px;
    position: absolute;
    top: 0;
    left: 0;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }

  .password-input {
    width: calc(100% - 42px);
    z-index: 10;
    opacity: 0;
  }
  .select-ul {
    width: 100%;
    height: auto;
    max-height: 400px;
    position: absolute;
    z-index: 10;
    top: 34px;
    left: 0;
    background-color: #f3f3f3;
    border: 1px solid #ccc;
    overflow: hidden;
    overflow-y: auto;
    border-radius: 5px;
    li {
      list-style: none;
      background-color: #fff;
      height: 30px;
      line-height: 30px;
      margin-bottom: 1px;
      padding: 0 10px;
    }
  }
}
</style>
