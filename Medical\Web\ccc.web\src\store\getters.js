/*
 * FilePath     : \src\store\getters.js
 * Author       : 郭自飞
 * Date         : 2019-10-22 08:40
 * LastEditors  : 来江禹
 * LastEditTime : 2023-04-05 17:37
 * Description  :
 */
const getters = {
  isLogin: state => state.auth.isLogin,
  getToken: state => state.auth.token,
  getMQMessageCount: state => state.session.mqMessageCount,
  getCurrentPatient: state => state.session.currentPatient,
  getPatientInfo: state => {
    if (state.session.patientInfo && state.session.patientInfo.inpatientID) {
      return state.session.patientInfo;
    } else {
      return undefined;
    }
  },
  getUser: state => state.session.user,
  getHospitalList: state => state.session.hospitalList,
  getHospitalInfo: state => state.session.hospitalInfo,
  getLanguageList: state => state.session.languageList,
  getLanguage: state => state.session.language,
  getReadOnly: state => state.session.readOnly,
  getRemBaseSize: state => state.session.remBaseSize,
  getLoginParams: state => state.session.loginParams,
  getExternalParams: state => state.session.externalParams,
  getStationList: state => state.session.stationList,
  getDeptList: state => state.session.deptList,
  getBedList: state => state.session.bedList,
  getBedNumberWidth: state => state.session.bedNumberWidth,
  getStationNurseList: state => state.session.stationNurseList,
  getSpecialDrawerHeightSwitch: state => state.session.specialDrawerHeightSwitch
};
export default getters;
