/*
 * FilePath     : \ccc.web\src\api\MedicineSchedule.js
 * Author       : 郭自飞
 * Date         : 2020-03-22 08:19
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-21 17:09
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/MedicineSchedule";
import qs from "qs";

export const urls = {
  PerformMedicine: baseUrl + "/PerformMedicine",
  GetPatientMedicineSchedule: baseUrl + "/GetPatientMedicineSchedule",
  GetPatientMedicineScheduleByTime:
    baseUrl + "/GetPatientMedicineScheduleByTime",
  CancelMedicine: baseUrl + "/CancelMedicine",
  GetOrdersTask: "/order/GetOrdersTask",
  GetMedicineTORecordList: baseUrl + "/GetMedicineTORecordList",
  DeleteMedicineRecord: baseUrl + "/DeleteMedicineRecord",
  AddMedicineRecord: baseUrl + "/AddMedicineRecord",
  GetStationMedicineSchedule: baseUrl + "/GetStationMedicineSchedule",
};

// 执行医嘱
export const PerformMedicine = (params) => {
  return http.post(urls.PerformMedicine, qs.stringify(params));
};
// 获取当起迄时间内病人给药信息;
export const GetPatientMedicineScheduleByTime = (params) => {
  return http.get(urls.GetPatientMedicineScheduleByTime, params);
};
// 获取当前班次的病人给药信息
export const GetPatientMedicineSchedule = (params) => {
  return http.get(urls.GetPatientMedicineSchedule, params);
};
// 取消给药
export const CancelMedicine = (params) => {
  return http.post(urls.CancelMedicine, qs.stringify(params));
};
// 根据groupID获取任务
export const GetOrdersTask = (params) => {
  return http.get(urls.GetOrdersTask, params);
};
// 根据时间和班次获取带入护理记录单的给药排程
export const GetMedicineTORecordList = (params) => {
  return http.get(urls.GetMedicineTORecordList, params);
};
// 取消带入护理记录单的给药排程
export const DeleteMedicineRecord = (params) => {
  return http.post(urls.DeleteMedicineRecord, qs.stringify(params));
};
// 给药排程加入护理记录单
export const AddMedicineRecord = (params) => {
  return http.post(urls.AddMedicineRecord, params);
};
//获取给药排程
export const GetStationMedicineSchedule = (params) => {
  return http.get(urls.GetStationMedicineSchedule, params);
};
