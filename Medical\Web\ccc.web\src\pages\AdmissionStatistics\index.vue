<!--
 * FilePath     : \ccc.web\src\pages\AdmissionStatistics\index.vue
 * Author       : 王浩杰
 * Date         : 2021-11-17 09:17
 * LastEditors  : 杨欣欣
 * LastEditTime : 2021-12-17 15:13
 * Description  : 
-->
<template>
  <base-layout headerHeight="auto" class="admission-statistics">
    <div slot="header">
      <span>请选择月份：</span>
      <el-date-picker
        class="month-picker"
        v-model="month"
        type="month"
        placeholder="选择月份"
        format="yyyy-MM"
        value-format="yyyy-MM"
        @change="getTime"
      ></el-date-picker>

      <el-button class="print-button" icon="iconfont icon-arrow-download" @click="exportToExcel()">导出EXCEL</el-button>
    </div>

    <el-table
      slot-scope="data"
      :height="data.height"
      border
      :data="admissions"
      v-loading="loading"
      element-loading-text="加载中……"
    >
      <el-table-column prop="year" label="年" align="center"></el-table-column>
      <el-table-column prop="month" label="月" align="center"></el-table-column>
      <el-table-column prop="stationName" label="病区名称" align="center"></el-table-column>
      <el-table-column prop="departmentName" label="科室名称" align="center"></el-table-column>
      <el-table-column prop="admissionCount" label="入院人数" align="center"></el-table-column>
      <el-table-column prop="dischargeCount" label="出院人数" align="center"></el-table-column>
      <el-table-column prop="admissionAssess" label="入院评估人数" align="center"></el-table-column>
      <el-table-column prop="physicalAssess" label="整体评估人数" align="center"></el-table-column>
      <el-table-column prop="nursingProblems" label="护理问题人数" align="center"></el-table-column>
      <el-table-column prop="nursingClusterOrder" label="集束护理人数" align="center"></el-table-column>
      <el-table-column prop="dischargeNote" label="出院小结人数" align="center"></el-table-column>
      <el-table-column prop="dischargeEvaluate" label="出院评价人数" align="center"></el-table-column>
      <el-table-column prop="noDischargeEvaluate" label="无出院评价人数" align="center"></el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { export_json_to_excel } from "@/vendor/Export2Excel.js";
import { SystemOperationStatistics } from "@/api/AdmissionStatistics.js";
export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      admissions: [],
      month: undefined,
      loading: true,
    };
  },
  created() {
    this.month = this._datetimeUtil.getNow("yyyy-MM");
    this.getTime();
  },
  methods: {
    //导出Excel表格通用
    exportToExcel() {
      if (this.admissions.length != 0) {
        let mainData = this.admissions;

        const mainHeader = [
          "年",
          "月",
          "病区名称",
          "科室名称",
          "入院人数",
          "出院人数",
          "入院评估人数",
          "整体评估人数",
          "护理问题人数",
          "集束护理人数",
          "出院小结人数",
          "出院评价人数",
          "无出院评价人数",
        ];
        const mainFilterVal = [
          "year",
          "month",
          "stationName",
          "departmentName",
          "admissionCount",
          "dischargeCount",
          "admissionAssess",
          "physicalAssess",
          "nursingProblems",
          "nursingClusterOrder",
          "dischargeNote",
          "dischargeEvaluate",
          "noDischargeEvaluate",
        ];
        const dataMain = this.formatJson(mainFilterVal, mainData);
        export_json_to_excel(mainHeader, dataMain, this._datetimeUtil.getNowDate("yyyyMMdd") + "病区统计清单");
      }
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
    getTime() {
      this.loading = true;
      var params = {
        yearMonth: this.month,
      };
      SystemOperationStatistics(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.admissions = result.data;
          this.loading = false;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.admission-statistics {
  .month-picker {
    width: 100px;
  }
  .print-button {
    float: right;
    margin: 4px;
  }
}
</style>