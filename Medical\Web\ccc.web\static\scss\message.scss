/* this._showTip文字行间距 */
.el-message__content {
  line-height: 20px;
}
.show-message.el-message {
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
}
.el-message .el-icon-close {
  right: 8px;
  &::before {
    color: #ff0000;
    font-size: 18px;
    font-weight: 600;
  }
}
//成功提示
.el-message--success {
  border-color: #aee2d7;
}
//警告提示
.el-message--warning {
  background-color: #f5e4cd;
  border-color: #f5e4cd;
  .el-message__icon,
  .el-message__content {
    color: #ef5b09;
  }
}
//错误提示
.el-message--error {
  .el-message__icon,
  .el-message__content {
    color: #ff001e;
  }
}
.el-message--success,
.el-message--warning,
.el-message--error {
  .el-message__content {
    font-size: 15px;
    font-weight: bold;
  }
}
