/*
 * FilePath     : \src\utils\decimalUtil.js
 * Author       : 李正元
 * Date         : 2020-05-09 10:37
 * LastEditors  : 苏军志
 * LastEditTime : 2021-04-11 12:08
 * Description  :
 */
let decimalRound = (value, rule, num) => {
  var ret = value;
  if (value && !isNaN(value)) {
    var valueNum;
    if (typeof value === "string") {
      valueNum = parseFloat(value);
    } else {
      valueNum = value;
    }
    if ((valueNum + "").indexOf(".") > 0 && rule && num >= 0) {
      if (rule === "Round") {
        ret = Math.round(valueNum * Math.pow(10, num)) / Math.pow(10, num);
      } else if (rule === "RoundDown") {
        ret = parseInt(valueNum * Math.pow(10, num)) / Math.pow(10, num);
      } else if (rule === "RoundUp") {
        ret = Math.ceil(valueNum * Math.pow(10, num)) / Math.pow(10, num);
      }
    }
  } 
  return ret;
};
export default {
  decimalRound
};
