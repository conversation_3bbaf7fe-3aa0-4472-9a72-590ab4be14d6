<!--
 * FilePath     : \src\components\queryFieldInput.vue
 * Author       : 马超
 * Date         : 2024-09-19 14:42
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-03-13 11:16
 * Description  :
 * CodeIterationRecord: 病案查询组件
 -->
<template>
  <div class="query-field-input" :class="{ horizontal: direction, vertical: !direction }">
    <p v-for="(item, index) in queryField" :key="index">
      <label>{{ item.description }}:</label>
      <el-input
        v-model="formData[item.typeValue]"
        class="search-input"
        :placeholder="`请输入${item.description}`"
        @input="clearData(item.typeValue)"
        @keyup.enter.native="getPatientByDynamicParam(item.typeValue, formData[item.typeValue])"
        :disabled="disabled"
      >
        <i
          slot="append"
          class="iconfont icon-search"
          @click="getPatientByDynamicParam(item.typeValue, formData[item.typeValue])"
        ></i>
      </el-input>
    </p>
  </div>
</template>

<script>
import { GetPatientByDynamicParam } from "@/api/Inpatient";
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    queryField: {
      type: Array,
      required: true,
    },
    direction: {
      type: Boolean,
      default: false,
    },
    defaultValue: {
      type: Object,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formData: {},
    };
  },
  computed: {
    isLoading: {
      get() {
        return this.loading;
      },
      set(val) {
        this.$emit("update:loading", val);
      },
    },
  },
  watch: {
    defaultValue: {
      handler(val) {
        if (val && this.queryField.length > 0) {
          this.$set(this.formData, val.attribute, val.attributeValue);
          this.getPatientByDynamicParam(val.attribute, val.attributeValue);
        }
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * 清空除去当前属性的数据
     * @param attribute 属性名称
     */
    async clearData(attribute) {
      let value = this.formData[attribute];
      this.formData = {};
      this.formData[attribute] = value;
    },
    /**
     * 根据动态参数获取病人信息
     * @param attribute 属性名称
     * @param value 属性值
     */
    async getPatientByDynamicParam(attribute, attributeValue) {
      this.isLoading = true;
      const params = {
        attribute: attribute,
        value: attributeValue,
        index: Math.random(),
      };
      await GetPatientByDynamicParam(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.$emit("data", res.data);
        }
        this.isLoading = false;
      });
    },
  },
};
</script>

<style lang="scss">
.query-field-input {
  input {
    padding-left: 5px;
  }
  p {
    height: 40px;
    line-height: 40px;
  }
  .search-input {
    width: 70%;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
    label {
      font-size: 13px;
    }
  }
  /* 横向排列 */
  &.horizontal {
    display: flex;
    flex-wrap: wrap;
    p {
      margin-right: 5px;
    }
  }
  /* 纵向排列 */
  &.vertical {
    display: block;
  }
}
</style>
