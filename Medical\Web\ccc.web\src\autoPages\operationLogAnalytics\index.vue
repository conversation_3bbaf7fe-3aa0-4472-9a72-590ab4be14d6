<!--
 * FilePath     : \src\autoPages\operationLogAnalytics\index.vue
 * Author       : 孟昭永
 * Date         : 2023-11-19 15:31
 * LastEditors  : 孟昭永
 * LastEditTime : 2023-11-24 10:14
 * Description  : 页面访问量操作日志统计分析
 * CodeIterationRecord: 
-->
<template>
  <el-container class="operation-analytics-container">
    <el-aside class="operation-analytics-aside">
      <el-tree :data="treeData" show-checkbox default-expand-all node-key="id" ref="tree" highlight-current
        :check-on-click-node="true" :props="defaultProps" @check="getCheckPage"></el-tree>
    </el-aside>
    <el-main class="operation-analytics-main">
      <base-layout>
        <div slot="header">
          <span>开始时间</span>
          <el-date-picker v-model="startDateTime" type="datetime" value-format="yyyy-MM-dd HH:mm"
            format="yyyy-MM-dd HH:mm" placeholder="选择日期时间"></el-date-picker>
          <span>结束时间</span>
          <el-date-picker v-model="endDateTime" type="datetime" value-format="yyyy-MM-dd HH:mm" format="yyyy-MM-dd HH:mm"
            placeholder="选择日期时间"></el-date-picker>
          <user-selector label="操作人员" v-model="userID" clearable filterable remoteSearch
            :stationID="stationID"></user-selector>
          <el-button class="query-button" icon="iconfont icon-search" @click="getPageView()">查询</el-button>
        </div>
        <div class="operation-analytics-bar" v-loading="loading" :element-loading-text="'加载中...'">
          <ve-bar :data="chartData" :settings="chartSettings" :extend="chartExtend" :height="'100%'"
            :grid="chartGrid"></ve-bar>
        </div>
      </base-layout>
    </el-main>
  </el-container>
</template>

<script>
import { GetPageView, GetTrackPageViewList } from "@/api/AutoTrack";
import baseLayout from "@/components/BaseLayout";
import userSelector from "@/components/selector/userSelector";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    userSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      endDateTime: undefined,
      startDateTime: undefined,
      userID: undefined,
      stationID: undefined,
      //要加载的图表数据
      chartData: { rows: [] },
      //V-chart图表的基础配置
      chartSettings: {},
      //额外配置-echarts里面的参数
      chartExtend: {},
      //坐标轴距离容器的距离
      chartGrid: {
        containLabel: true,
        top: 30,
        right: 30,
        bottom: 30,
        left: 10,
      },
      //菜单数据
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      menuListIDstr: undefined,
      loading: false,
    };
  },

  async created() {
    this.stationID = this.user.stationID;
    this.endDateTime = this._datetimeUtil.getNow();
    this.startDateTime = this._datetimeUtil.addDate(this.endDateTime, -1);
    //获取页面清单
    await this.getTrackPageViewList();
    //获取默认选中的派班页面-66是菜单的menuListID
    this.$refs.tree.setCheckedKeys([66]);
    await this.getCheckPage();
  },
  methods: {
    /**
     * description: 获取选中的页面
     * return {*}
     * param {*}
     */
    async getCheckPage() {
      this.menuListIDstr = undefined;
      let checkedKeys = this.$refs.tree.getCheckedKeys();
      if (checkedKeys.length == 0) {
        this._showTip("warning", "请选择要查看的项目！");
        return;
      }
      this.menuListIDstr = checkedKeys.join(",");
      await this.getPageView();
    },
    /**
     * description: 获取前端埋点跟踪的页面
     * return {*}
     * param {*}
     */
    async getTrackPageViewList() {
      let params = {
        system: "CCC",
        menuType: "MainMenu",
        clientType: "1",
      };
      await GetTrackPageViewList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.treeData = result.data;
        }
      });
    },
    /**
     * description: 获取页面访问量
     * return {*}
     * param {*}
     */
    async getPageView() {
      if (!this.menuListIDstr) {
        this._showTip("warning", "请选择要查看的项目！");
        return;
      }
      this.loading = true;
      let params = {
        menuListIDstr: this.menuListIDstr,
        stratDateTime: this.startDateTime,
        endDateTime: this.endDateTime,
        userID: this.userID,
      };
      await GetPageView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.chartData.rows = result.data;
        }
        this.loading = false;
      });
      //设置V-Chart的Extend和settings
      this.setChartOption();
    },
    /**
     * description: 设置V-Chart的Extend和settings
     * return {*}
     * param {*}
     */
    setChartOption() {
      let noData = "暂无数据";
      let chartDataLength = this.chartData.rows.length;
      let startValue = chartDataLength - 40 >= 0 ? chartDataLength - 40 : 0;
      let xAxisMaxValue = 0;
      if (chartDataLength > 0) {
        xAxisMaxValue = Math.ceil(this.chartData.rows[0].pageView / 10) * 10;
        noData = "";
      }
      //设置chartExtend
      this.chartExtend = {
        yAxis: {
          //坐标轴刻度标签的相关设置
          axisLabel: {
            show: true,
            interval: "0", //强制显示所有标签
          },
        },
        dataZoom: [
          {
            show: true,
            startValue: startValue,
            endValue: chartDataLength,
            yAxisIndex: 0,
            zoomLock: true,
          },
        ],
        series: {
          barWidth: 10,
        },
        graphic: {
          type: "text",
          left: "center",
          top: "45%",
          style: {
            text: noData,
            textAlign: "center",
            fill: "#ccc",
            fontSize: 20,
          },
        },
      };
      //设置chartSettings
      this.chartSettings = {
        metrics: ["pageView"],
        dimension: ["userName"],
        labelMap: {
          pageView: "页面访问量（次）", //指标别名
        },
        dataOrder: {
          label: "pageView",
          order: "desc", //数据排序
        },
        itemStyle: {
          normal: {
            label: { show: true, position: "right" }, //显示数字
          },
        },
        max: [xAxisMaxValue],
      };
    },
  },
};
</script>

<style lang="scss">
.operation-analytics-container {
  border: 1px solid #ccc;
  height: 100%;
  box-sizing: border-box;

  .operation-analytics-aside {
    background-color: #fff;
    width: 215px;
    height: 100%;
    border-right: 1px solid #ccc;
  }

  .operation-analytics-main {
    padding: 10px;
    height: 100%;
    position: relative;

    .operation-analytics-bar {
      height: 100%;
    }
  }
}
</style>