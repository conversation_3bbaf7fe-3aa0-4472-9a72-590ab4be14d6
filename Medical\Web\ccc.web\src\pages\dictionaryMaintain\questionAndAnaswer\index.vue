<!--
 * FilePath     : \src\pages\dictionaryMaintain\questionAndAnaswer\index.vue
 * Author       : 郭自飞
 * Date         : 2020-04-30 16:25
 * LastEditors  : 苏军志
 * LastEditTime : 2020-10-16 15:27
 * Description  : 常见问题
 -->
<template>
  <base-layout class="question-main">
    <div slot="header">
      <el-input
        class="question-input"
        v-model="inputContext"
        placeholder="请输入问题"
        @keyup.enter.native="search()"
      ></el-input>
      <el-button @click="search()" class="query-button" icon="iconfont icon-search">查询</el-button>
      <div class="right-btn">
        <el-button class="add-button" @click="saveDialog('')" icon="iconfont icon-add">新增</el-button>
      </div>
    </div>
    <el-table :data="questionList" border height="100%">
      <el-table-column prop="questionAndAnaswerID" label="问题ID" width="70" align="center"></el-table-column>
      <el-table-column prop="question" label="问题" header-align="center" min-width="150" align="left">
        <template slot-scope="questionAndAnaswer">
          <span v-html="questionAndAnaswer.row.question"></span>
        </template>
      </el-table-column>
      <el-table-column prop="anaswer" label="解决方法" header-align="center" min-width="150" align="left">
        <template slot-scope="questionAndAnaswer">
          <span v-html="questionAndAnaswer.row.anaswer"></span>
        </template>
      </el-table-column>
      <el-table-column prop="addDate" label="新增时间" width="160" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.addDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="70" header-align="center" align="left">
        <template slot-scope="scope">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="saveDialog(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteQuestion(scope.row.questionAndAnaswerID)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      width="700px"
      title="登记问题"
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      v-loading="loading"
    >
      <div>
        <span>常见问题:</span>
        <RichText :size="{ height: '150px', width: '90%' }" :wordNumber="1000" v-model="questionData"></RichText>
      </div>
      <div>
        <span>解决办法:</span>
        <RichText :size="{ height: '150px', width: '90%' }" :wordNumber="1000" v-model="answerData"></RichText>
      </div>
      <div slot="footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveQuestion">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import RichText from "@/components/RichText";
import {
  GetQuestionAndAnaswers,
  SaveQuestionAndAnaswer,
  UpdateQuestionAndAnaswer,
  DeleteQuestionAndAnaswer,
} from "@/api/QuestionAndAnaswer";
import baseLayout from "@/components/BaseLayout";
export default {
  components: {
    baseLayout,
    RichText,
  },
  data() {
    return {
      //文本编辑插件绑定值
      questionData: "",
      answerData: "",
      questionList: [], //常见问题列表
      cloneQuestionList: [],
      loading: false,
      dialogFormVisible: false, //增加弹出框
      dialogFormVisibleUpdate: false, //修改弹出框
      savaData: {
        anaswer: "",
        question: "",
      },
      isAdd: false,
      //搜索框输入内容
      inputContext: "",
    };
  },
  mounted() {
    this.getAllQuestion();
    // tinymce.init({});
  },
  methods: {
    //查询所有的问题记录
    getAllQuestion() {
      GetQuestionAndAnaswers().then((res) => {
        if (this._common.isSuccess(res)) {
          this.questionList = res.data;
          this.cloneQuestionList = this._common.clone(this.questionList);
        }
      });
    },
    //模糊查询
    search() {
      this.questionList = this.cloneQuestionList.filter((eleemnt) => {
        let str = eleemnt.question.replace(/<[^>]+>/g, "");
        if (str.indexOf(this.inputContext) != -1) {
          return eleemnt.question;
        }
      });
    },
    deleteQuestion(id) {
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          // 确认删除
          let params = {
            questionAndAnaswerID: id,
          };
          DeleteQuestionAndAnaswer(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getAllQuestion();
            }
          });
        }
      });
    },
    saveDialog(data) {
      this.dialogFormVisible = true;
      this.isAdd = true;
      this.savaData = {
        anaswer: "",
        question: "",
      };
      //新增置空
      this.questionData = "";
      this.answerData = "";
      if (data) {
        this.isAdd = false;
        this.savaData = this._common.clone(data);
        //修改赋值
        this.questionData = data.question;
        this.answerData = data.anaswer;
      }
    },
    //保存记录
    saveQuestion() {
      //保存赋值
      this.savaData.question = this.questionData;
      this.savaData.anaswer = this.answerData;
      if (!this.savaData.question || !this.savaData.anaswer) {
        this._showTip("success", "请填写完整记录信息");
        return;
      }
      this.loading = true;
      if (this.isAdd) {
        return SaveQuestionAndAnaswer(this.savaData).then((res) => {
          this.loading = false;
          if (this._common.isSuccess(res)) {
            this._showTip("success", "保存成功！");
            this.dialogFormVisible = false;
            this.getAllQuestion();
          }
        });
      } else {
        return UpdateQuestionAndAnaswer(this.savaData).then((res) => {
          this.loading = false;
          if (this._common.isSuccess(res)) {
            this._showTip("success", "修改成功！");
            this.dialogFormVisible = false;
            this.getAllQuestion();
          }
        });
      }
    },
  },
};
</script>
<style lang="scss">
.question-main {
  .question-input {
    width: 200px;
  }
  .right-btn {
    float: right;
  }
  .el-table__body-wrapper {
    p {
      margin-block-start: 1px;
      margin-block-end: 1px;
    }
  }
  .el-dialog {
    height: auto;
  }
}
</style>
