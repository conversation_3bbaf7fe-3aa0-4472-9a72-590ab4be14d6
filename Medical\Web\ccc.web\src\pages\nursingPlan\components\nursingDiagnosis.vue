<!--
 * FilePath     : \src\pages\nursingPlan\components\nursingDiagnosis.vue
 * Author       : 李青原
 * Date         : 2020-05-05 03:45
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-06-11 17:46
 * Description  : 护理诊断页面
 -->
<template>
  <base-layout class="nursing-diagnosis" v-loading="loading" :element-loading-text="loadingText">
    <div slot="header" class="top">
      <span class="label">诊断日期：</span>
      <el-date-picker
        v-model="diagnosisDate"
        :disabled="!isFirst"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 120px"
      ></el-date-picker>
      <span class="label">诊断时间：</span>
      <el-time-picker
        v-model="diagnosisTime"
        :disabled="!isFirst"
        format="HH:mm"
        value-format="HH:mm"
        placeholder="选择时间"
        style="width: 80px"
      ></el-time-picker>
      <div class="top-button">
        <lab-result-button :case-number="inpatientinfo.caseNumber"></lab-result-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
      </div>
    </div>
    <div class="question">
      <div class="diagnosis-question">
        <!-- 问题 -->
        <el-table
          border
          stripe
          :data="tableData"
          @row-click="rowClick"
          @select="hadCheck"
          ref="table"
          :highlight-current-row="true"
          height="100%"
          :header-cell-class-name="disabledSelection"
        >
          <el-table-column type="selection" align="center" :width="convertPX(40)"></el-table-column>
          <el-table-column label="问题（P）">
            <template slot-scope="scope">
              <i class="iconfont icon-info" @click="showMessage(scope.row.showMessage)"></i>
              <span :class="{ mark: scope.row.alreadyExit }">{{ scope.row.problem }}</span>
            </template>
          </el-table-column>
          <el-table-column label="排序" width="60px" align="center">
            <template slot-scope="scope">
              {{ scope.row.sort == 0 ? "" : scope.row.sort }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="diagnosis-relation">
        <div class="current-problem" v-if="currentProblem">
          {{ currentProblem.problem + "(" + currentProblem.showMessage + ")" }}
        </div>
        <div class="content">
          <!-- 相关因素 -->
          <div class="diagnosis-related">
            <el-table height="100%" :row-class-name="tableRowClassName" :data="relatedFactors" border stripe>
              <el-table-column label="相关因素(E)" prop="relatedFactor"></el-table-column>
            </el-table>
          </div>
          <!-- 定义特征 -->
          <div class="diagnosis-features">
            <el-table height="100%" :data="signAndSymptom" border stripe>
              <el-table-column label="定义特征(S)" prop="signAndSymptom"></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </base-layout>
</template>
<script>
import { GetPatientProblemView, Save } from "@/api/PatientProblem";
import BaseLayout from "@/components/BaseLayout";
import labResultButton from "@/components/button/labResultButton";
export default {
  components: {
    BaseLayout,
    labResultButton,
  },
  props: {
    inpatientinfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    lastAssessDateTime: {
      type: String,
      default: undefined,
    },
    admissionDateTime: {
      type: String,
      default: undefined,
    },
  },
  watch: {
    "inpatientinfo.inpatientID": {
      handler(newV) {
        this.tableData = [];
        this.relatedFactors = [];
        this.signAndSymptom = [];
        this.currentProblem = undefined;
        if (!newV) return;
        this.getData();
      },
      immediate: true,
    },
  },
  data() {
    return {
      tableData: [],
      relatedFactors: [],
      signAndSymptom: [],
      loading: false,
      currentProblem: undefined,
      loadingText: "加载中……",
      diagnosisDate: undefined,
      diagnosisTime: undefined,
      // 标识是否首次保存
      isFirst: false,
      patientAssessTime: undefined,
    };
  },
  created() {
    this.diagnosisDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
    this.diagnosisTime = this._datetimeUtil.getNowTime("hh:mm");
  },
  methods: {
    getData() {
      if (this.inpatientinfo == null) {
        this._showTip("未查询到病人信息！");
        return;
      }
      this.loadingText = "加载中……";
      let params = {
        inpatientID: this.inpatientinfo.inpatientID,
      };
      this.loading = true;
      GetPatientProblemView(params).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          this.tableData = response.data;
          this.writeBack();
        }
      });
    },
    save() {
      if (this.tableData.length <= 0) {
        return;
      }
      let saveTime =
        this._datetimeUtil.formatDate(this.diagnosisDate, "yyyy-MM-dd") +
        " " +
        this._datetimeUtil.formatDate(this.diagnosisTime, "hh:mm");
      let assessTime = undefined;
      //有评估取护理评估时间 没有取入院时间
      if (this.lastAssessDateTime) {
        assessTime = this._datetimeUtil.formatDate(this.lastAssessDateTime, "yyyy-MM-dd hh:mm");
      } else {
        assessTime = this._datetimeUtil.formatDate(this.admissionDateTime, "yyyy-MM-dd hh:mm");
      }
      if (saveTime < assessTime) {
        this._showTip("warning", "时间不能早于护理评估时间！");
        return;
      }
      if (
        this._datetimeUtil.getTimeDifference(saveTime, this._datetimeUtil.getNow("yyyy-MM-dd hh:mm"), "date", "M") < 0
      ) {
        this._showTip("warning", "时间不能晚于当前时间！");
        return;
      }
      let saveData = this._common.clone(this.tableData);
      saveData.forEach((data) => {
        // 新增问题添加开始日期和开始时间
        if (data.choose && !data.patientProblemID) {
          data.startDate = this.diagnosisDate;
          data.startTime = this.diagnosisTime;
        } else {
          data.startDate = undefined;
          data.startTime = undefined;
        }
      });
      this.loadingText = "保存中……";
      this.loading = true;
      return Save(saveData).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          this._showTip("success", "保存成功！");
          this.loadingText = "加载中……";
          this.$emit("jump-page");
        }
      });
    },
    rowClick(row, column, event) {
      this.currentProblem = row;
      if (this.$refs.table) {
        this.$refs.table.setCurrentRow(row);
      }
      this.relatedFactors = [];
      this.relatedFactors = row.patientProblemRelatedFactors;
      this.signAndSymptom = [];
      this.signAndSymptom = row.patientProblemSignAndSymptom;
      this.$forceUpdate();
    },
    hadCheck(selection, row) {
      //this.$refs.table.setCurrentRow(row);
      this.rowClick(row);
      let flag = false;
      let i = 0;
      //检查此次修改为移除还是添加
      let item = selection.find((item) => {
        return item === row;
      });
      if (item) {
        flag = true;
      }
      for (i; i < selection.length; i++) {
        selection[i].choose = true;
        this.$set(selection[i], "sort", i + 1);
      }
      if (!flag) {
        this.tableData.forEach((item) => {
          if (item === row) {
            item.choose = false;
            this.$set(item, "sort", 0);
          }
        });
      }
    },
    disabledSelection(row) {
      if (row.columnIndex === 0) {
        return "DisableSelection";
      }
    },
    showMessage(messageContent) {
      this._showMessage({
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 5000,
      });
    },
    //回写勾选 基于table selection 集合
    writeBack() {
      this.isFirst = true;
      if (this.tableData) {
        let tempData = this.tableData.filter((data) => {
          return data.startDate && data.startTime;
        });
        if (tempData && tempData.length > 0) {
          // 非首次，不能调整时间
          this.isFirst = false;
          // 取最早护理问题是开始时间
          tempData.sort((data1, data2) => {
            let time1 = data1.startDate + " " + data1.startTime;
            let time2 = data2.startDate + " " + data2.startTime;
            return time1 > time2 ? 1 : time1 < time2 ? -1 : 0;
          });
          this.diagnosisDate = tempData[0].startDate;
          this.diagnosisTime = tempData[0].startTime;
        }
      }
      this.$nextTick(() => {
        //TODO:待优化
        // let length = this.tableData.length;
        // let sortMax = 0;
        // // 可能出现sort比
        // for (let index = 0; index < this.tableData.length; index++) {
        //   const element = this.tableData[index];
        //   sortMax = element.sort >= sortMax ? element.sort : sortMax;
        // }
        // for (let i = 1; i < sortMax + 1; i++) {
        //   this.tableData.forEach((element) => {
        //     if (element.sort == i) {
        //       if (this.$refs.table) this.$refs.table.toggleRowSelection(element, true);
        //     }
        //   });
        // }
        if (this.$refs.table) {
          for (let index = 0; index < this.tableData.length; index++) {
            if (this.tableData[index].choose) {
              this.$refs.table.toggleRowSelection(this.tableData[index], true);
            }
          }
        }
      });
    },
    tableRowClassName: function (row, index) {
      if (row.row.show) {
        return "";
      }
      return "hide-show";
    },
  },
};
</script>

<style lang="scss">
.nursing-diagnosis {
  .top {
    .label {
      margin-left: 10px;
    }

    .top-button {
      float: right;
    }
  }

  .question {
    display: flex;
    height: 100%;

    .diagnosis-question {
      width: 33.3%;
      padding: 0 3px;
      box-sizing: border-box;
      height: 100%;

      .el-table .DisableSelection .cell .el-checkbox__inner {
        display: none;
        position: relative;
      }

      .el-table .DisableSelection .cell:before {
        content: "";
        position: absolute;
      }

      .mark {
        color: #ff7400;
      }
    }

    .diagnosis-relation {
      width: 66.6%;
      display: flex;
      flex-direction: column;
      height: 100%;

      .current-problem {
        font-size: 14px;
        height: 20px;
        line-height: 20px;
        // font-weight: 600;
        vertical-align: bottom;
        background-color: #fce8d8;
        padding-left: 10px;
      }

      .content {
        display: flex;
        height: calc(100% - 18px);
        width: 100%;

        .diagnosis-related {
          width: 50%;
          box-sizing: border-box;
          padding: 0 3px;
          height: 100%;
        }

        .diagnosis-features {
          box-sizing: border-box;
          padding: 0 3px;
          width: 50%;
          height: 100%;
        }

        .hide-show {
          display: none;
        }
      }
    }
  }
}
</style>
