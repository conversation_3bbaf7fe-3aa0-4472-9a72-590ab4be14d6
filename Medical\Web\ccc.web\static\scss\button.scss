.el-button {
  border-radius: 5px;
  margin-left: 5px;

  [class^="icon-"],
  [class*=" icon-"] {
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    vertical-align: baseline;
    display: inline-block;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 12px;
    color: #ffffff !important;
    margin: 0 4px 0 0 !important;
    height: auto;
    // width: 12px;
  }
}

.el-button [class*="el-icon-"]+span {
  margin-left: 3px;
}

.el-button+.el-button {
  margin-left: 5px;
}

.el-button--small {
  padding: 6px 8px;
}

.el-button--mini {
  padding: 3px 6px;
}

.add-button {
  background: #14d8d8;
  border-color: #14d8d8;
  color: #fff;

  &:hover {
    background: #14d8d8;
    border-color: #14d8d8;
    color: #fff;
    opacity: 0.7;
  }

  &:focus {
    background: #14d8d8;
    border-color: #14d8d8;
    color: #fff;
  }
}

.edit-button {
  background: #409eff;
  border-color: #409eff;
  color: #fff;

  &:hover {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
    opacity: 0.7;
  }

  &:focus {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
  }
}



.print-button {
  background: #ff7400;
  border-color: #ff7400;
  color: #fff;

  &:hover {
    background: #ff7400;
    border-color: #ff7400;
    opacity: 0.7;
    color: #fff;
  }

  &:focus {
    background: #ff7400;
    border-color: #ff7400;
    color: #fff;
  }
}

.query-button {
  background: #8cc63e;
  border-color: #8cc63e;
  color: #fff;

  &:hover {
    background: #8cc63e;
    border-color: #8cc63e;
    opacity: 0.7;
    color: #fff;
  }

  &:focus {
    background: #8cc63e;
    border-color: #8cc63e;
    color: #fff;
  }
}

.stop-button {
  background: #707070;
  border-color: #707070;
  color: #fff;

  &:hover {
    background: #707070;
    border-color: #707070;
    opacity: 0.7;
    color: #fff;
  }

  &:focus {
    background: #707070;
    border-color: #707070;
    color: #fff;
  }
}


.history-button {
  background: #b644cd;
  border-color: #b644cd;
  color: #fff;

  &:hover {
    background: #b644cd;
    border-color: #b644cd;
    color: #fff;
    opacity: 0.7;
  }

  &:focus {
    background: #b644cd;
    border-color: #b644cd;
    color: #fff;
  }
}