/*
 * FilePath     : \static\scss\menu.scss
 * Author       : 苏军志
 * Date         : 2020-07-04 18:37
 * LastEditors  : 苏军志
 * LastEditTime : 2022-12-30 15:52
 * Description  : 左侧导航菜单样式
 */

/* 定义变量 */
// 距顶部
$top: 50px;
// 菜单宽度
$menu-width: 140px;
// 收缩时菜单宽度
$collapse-menu-width: 64px;
// 菜单收起时项目高度
$collapse-menu-item-height: 64px;
// 菜单高度
$menu-height: calc(100% - #{$top});
// 菜单项目高度
$menu-item-height: 50px;
// 子菜单高度
$child-menu-item-height: 32px;
// iconfont图标字体大小
$iconfont-font-size: 24px;
// 菜单展开标识箭头的字体大小
$icon-arrow-font-size: 18px;
$font-size: 14px;
// 当前菜单
$active-menu-color: #7b7de3;
// 菜单前景色
$menu-font-color: var(--menu-font-color);

/* 样式 */
.el-menu {
  height: $menu-height;
  border: 0;
  overflow-x: hidden;
  overflow-y: auto;
  position: initial;

  &::-webkit-scrollbar {
    display: none;
  }

  .el-menu-item {
    min-width: $menu-width  !important;
  }

  .el-menu-item,
  .el-submenu__title {
    height: $menu-item-height  !important;
    line-height: $menu-item-height - 10px !important;
    padding: 0 3px !important;
    border-bottom: 1px dashed #ffffff !important;
    box-sizing: border-box;
    color: $menu-font-color  !important;
    font-size: $font-size;

    i {
      font-size: $iconfont-font-size;
      color: $menu-font-color;
      margin: 2px 5px !important;

      &.el-submenu__icon-arrow {
        right: 10px;
        font-size: $icon-arrow-font-size;
        top: 25%;
      }
    }
  }

  .el-submenu.is-opened .el-menu.el-menu--inline {

    .el-menu-item,
    .el-submenu__title {
      height: $child-menu-item-height  !important;
      line-height: $child-menu-item-height - 5px !important;
      padding-left: 30px !important;
      box-sizing: border-box;
    }

    /* 三级菜单 */
    .el-submenu.is-opened .el-menu.el-menu--inline {

      .el-menu-item,
      .el-submenu__title {
        padding-left: 50px !important;
      }
    }
  }

  /* 菜单项被选中样式 */
  &.el-menu--collapse .el-submenu.is-active .el-submenu__title,
  &.el-menu--collapse .el-submenu.is-opened .el-submenu__title,
  .el-submenu .el-menu-item:hover,
  .el-submenu.is-active.el-submenu__title,
  .el-submenu__title:hover,
  .el-menu-item:hover,
  .el-menu-item.is-active {
    background-color: $active-menu-color  !important;
    color: #ffffff !important;

    i {
      color: #ffffff !important;
    }
  }

  /* 收起后菜单样式 */
  &.el-menu--collapse {
    width: $collapse-menu-width  !important;

    .el-submenu.is-opened {

      /* 收缩时，展开二级菜单时，右测显示展开三角形 */
      .el-submenu__title:before {
        content: "";
        width: 0;
        height: 0;
        border: 10px solid transparent;
        /*调整小三角形的大小*/
        border-right-color: #ffffff;
        /*小三角的颜色*/
        position: absolute;
        right: 0px;
        /*调整小三角形的位置*/
        margin-top: 20px;
        /*调整小三角形的位置*/
      }
    }

    .el-menu-item {
      min-width: $collapse-menu-width  !important;
    }

    .el-menu-item,
    .el-submenu__title {
      height: $collapse-menu-item-height  !important;
      line-height: $collapse-menu-item-height / 2 - 6px !important;
      text-align: center;

      i {
        margin-top: 8px !important;
      }
    }

    .el-submenu {
      .el-submenu__icon-arrow {
        display: none;
      }
    }
  }
}

/* 二级菜单及子菜单样式 */
.el-menu--vertical {
  background-color: #ffffff;
  box-shadow: 2px 0 12px 0 rgba(0, 0, 0, 0.1);

  .el-menu--popup-right-start {
    min-width: $menu-width;
    margin: 0;
    background-color: #ffffff !important;
    box-shadow: none;
    padding: 0;

    .el-submenu.is-active .el-submenu__title,
    .el-submenu .el-menu-item:hover,
    .el-submenu__title:hover,
    .el-menu-item:hover,
    .el-menu-item.is-active {
      background-color: $active-menu-color  !important;
      color: #ffffff !important;

      .iconfont {
        color: #ffffff;
      }

      .el-submenu__icon-arrow {
        color: #ffffff !important;
      }
    }

    .el-menu-item,
    .el-submenu__title {
      height: $child-menu-item-height  !important;
      line-height: $child-menu-item-height - 4px !important;
      background-color: #eff8f3 !important;
      color: #000000 !important;
      border-bottom: 1px dashed $base-color  !important;
      padding: 0 10px 0 20px !important;
    }

    .el-submenu {
      &.is-active {

        i,
        .el-submenu__title {
          color: #ffffff !important;
        }
      }

      i {
        font-size: $iconfont-font-size;
        color: #000000;
        margin-left: 5px;

        &.el-submenu__icon-arrow {
          right: 5px;
          font-size: $icon-arrow-font-size;
          margin-top: 0 !important;
        }
      }

      /* 三阶菜单 */
      .el-menu--vertical {
        box-shadow: 2px 0 5px 0 rgba(0, 0, 0, 0.1);
        border-left: 3px solid $active-menu-color;
      }
    }
  }
}