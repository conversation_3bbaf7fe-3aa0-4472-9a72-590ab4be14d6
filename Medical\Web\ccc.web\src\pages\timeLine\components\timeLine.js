﻿var DCTemperatureControl = new Object();

var resultHTML = null;

DCTemperatureControl.PostInfoToBackGroundWithoutCallBack = function(
  url,
  content,
  async
) {
  var xmlhttp = null;
  if (window.XMLHttpRequest) {
    // code for all new browsers
    xmlhttp = new XMLHttpRequest();
  } else if (window.ActiveXObject) {
    // code for IE5 and IE6
    xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
  }
  if (async == null || async == undefined) {
    async = false;
  }
  if (xmlhttp !== null) {
    xmlhttp.withCredentials = true;
    xmlhttp.open("POST", url, async);
    if (content !== null) {
      xmlhttp.send(content);
    } else {
      xmlhttp.send();
    }
    resultHTML = xmlhttp.responseText;
  } else {
    if (promptError) {
      alert("浏览器不支持XMLHTTP.");
      return false;
    }
  }
  return false;
};

export default function BindingDCTemperatureControl(ctl) {
  if (ctl !== null) {
    ctl.RaiseEvent = function() {
      alert("搞事");
    };

    ctl.Init = function() {
      var divv = document.createElement("div");
      divv.id = "dctimelineprimarycontent";
      ctl.appendChild(divv);
      document.TemperatureControl = ctl;
    };

    ctl.OnLinkClick = function(linkString) {
      if (
        ctl.EventLinkClick != null &&
        typeof ctl.EventLinkClick == "function"
      ) {
        ctl.EventLinkClick.call(ctl, linkString);
      }
    };

    ctl.AddValuePointByXml = function(name, xmlstring, async) {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "addvaluepointbyxml" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324"; //最后一个目测多余但现在必须得加上

      var content = "valuepointxml=" + xmlstring + "&valuepointname=" + name;
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(url, content);
    };

    ctl.AddValuePoints = function(datas, async) {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "addvaluepoints" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";

      //简单校验一下参数，确保datas的基本结构
      if (Array.isArray(datas) == false) {
        console.log("参数必须为数组");
        return;
      }
      for (var i = 0; i < datas.length; i++) {
        var data = datas[i];
        if (data.Name == null || typeof data.Name != "string") {
          console.log(
            "参数数组中每一项必须拥有Name属性且为字符串用于指定一组数据点的名称"
          );
          return;
        }
        if (data.Datas != null && Array.isArray(data.Datas) == false) {
          console.log(
            "参数数组中每一项的Datas属性必须为数组用于指定该名称下的数据点集合"
          );
          return;
        }
        // 循环将首字母大写，非dll代码
        for (var j = 0; j < data.Datas.length; j++) {
          if (typeof data.Datas[j] == "object") {
            for (var key in data.Datas[j]) {
              data.Datas[j][
                key.substring(0, 1).toUpperCase() + key.substring(1)
              ] = data.Datas[j][key];
              delete data.Datas[j][key];
            }
          }
        }
      }
      var content = "valuepoints=" + encodeURIComponent(JSON.stringify(datas));
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(
        url,
        content,
        false
      );
    };

    ctl.SetDocumentConfigXml = function(xmlstring, async) {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "setdocumentconfigxml" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";
      var content = "documentconfigxml=" + xmlstring;
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(url, content);
    };

    ctl.LoadDocument = function(loadhintstring, async) {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "loaddocument" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";
      var content = "loaddocumentinfo=" + loadhintstring;
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(url, content);
    };

    ctl.SaveDocument = function(savehintstring, async) {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "savedocument" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";
      var content = "savedocumentinfo=" + savehintstring;
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(url, content);
    };

    //wyc20200529添加设置参数值
    ctl.SetParameterValue = function(dataarray, async) {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "setparametervalue" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";
      var datasobj = {
        datas: dataarray
      };
      var content = "parametervalue=" + JSON.stringify(datasobj);
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(url, content);
    };

    ctl.LoadDocumentFromFrontEndString = function(documentxmlstring, async) {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "loaddocumentfromfrontend" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";
      var content = "loaddocumentinfo=" + documentxmlstring;
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(
        url,
        content,
        async
      );
    };

    ctl.SaveDocumentToFrontEndString = function() {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "savedocumenttofrontend" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";
      var content = "1=1";
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(
        url,
        content,
        false
      );
      return resultHTML;
    };

    ctl.FileNew = function() {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "filenew" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";
      var content = "1=1";
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(
        url,
        content,
        false
      );
    };

    ctl.RefreshView = function() {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");

      var contentpixelheight = ctl.getAttribute("contentpixelheight");
      var pixelpagespacing = ctl.getAttribute("pixelpagespacing");
      var height = ctl.getAttribute("dc_height");
      var pageshadow = ctl.getAttribute("pageshadow");

      var url =
        servicepageurl +
        "?method=" +
        "refreshview" +
        "&sessionname=" +
        sessionname +
        "&contentpixelheight=" +
        contentpixelheight +
        "&pixelpagespacing=" +
        pixelpagespacing +
        "&dc_height=" +
        height +
        "&pageshadow=" +
        pageshadow +
        "&serviceflag=fdjia8324";
      var content = null;
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(
        url,
        content,
        false
      );
      var divprimarycontent = document.getElementById(
        "dctimelineprimarycontent"
      ); //这里的ID要和后台代码对应
      divprimarycontent.innerHTML = resultHTML;
      var list = divprimarycontent.getElementsByTagName("img");
      for (var i = 0; i < list.length; i++) {
        var img = list[i];
        if (
          img.className === "dctimelinebigimg" ||
          img.className === "dctimelinesmallimage"
        ) {
          var src = img.src;
          var index = src.indexOf("?");
          src = src.substr(index);
          src = servicepageurl + src;
          img.src = src;
        }
      }
    };

    //获得时间轴页面需要修改后台的代码
    ctl.SetDocumentViewMode = function(mode) {
      if (mode === "Normal" || mode === "Timeline" || mode === "Page") {
        var servicepageurl = ctl.getAttribute("servicepageurl");
        var sessionname = ctl.getAttribute("documentbuffername");
        var url =
          servicepageurl +
          "?method=" +
          "setdocumentviewmode" +
          "&sessionname=" +
          sessionname +
          "&serviceflag=fdjia8324";
        var content = "documentviewmode=" + mode;
        DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(url, content);
      }
    };

    ctl.SetDocumentPageIndex = function(pageindex, async) {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "setdocumentpageindex" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";
      var content = "pageindex=" + pageindex;
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(url, content);
    };

    ctl.SetRegisterCode = function(registercode, async) {
      var servicepageurl = ctl.getAttribute("servicepageurl");
      var sessionname = ctl.getAttribute("documentbuffername");
      var url =
        servicepageurl +
        "?method=" +
        "setregistercode" +
        "&sessionname=" +
        sessionname +
        "&serviceflag=fdjia8324";
      var content = "registercode=" + registercode;
      DCTemperatureControl.PostInfoToBackGroundWithoutCallBack(url, content);
    };

    ctl.PrintDocument = function() {
      var contentHtml = "";
      var images = document.getElementsByClassName("dctimelinebigimg");
      if (images.length > 0) {
        contentHtml = images[0].parentElement.innerHTML;
      }
      var ps =
        "@page{margin-left:0cm;margin-top:0cm;margin-right:0cm;margin-bottom:0cm;}";
      var html = "<html><head><style> P{margin:0px}  " + ps + " </style>";
      html =
        html +
        "</head><body style='padding-left:1px;padding-top:0px;padding-right:0px;padding-bottom:0px;margin:0px'>";
      html = html + contentHtml;
      html = html + "</body></html>";

      var wind = window.open(
        "",
        "_blank",
        "menubar=no,toolbar=no,location=no,scrollbars=no,resizable=yes"
      );
      wind.document.write(html);
      $(wind.document).ready(function() {
        setTimeout(function() {
          //wind.alert("喵了个咪");
          wind.print();
          wind.close();
        }, 1500);
      });
    };
  }
}
