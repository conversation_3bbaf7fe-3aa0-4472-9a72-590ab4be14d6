<!--
 * FilePath     : \src\components\button\labResultButton.vue
 * Author       : 郭鹏超
 * Date         : 2025-06-11 16:43
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-06-20 15:46
 * Description  : 检验查看按钮
 * CodeIterationRecord: 
 -->
<template>
  <div class="lab-result-button" v-if="showFlag">
    <el-button type="danger" icon="iconfont icon-chart" @click="handleShowLabResult">检验检查数据</el-button>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="检验检查"
      :visible.sync="showTestExamine"
      fullscreen
      custom-class="link no-footer"
      :append-to-body="true"
    >
      <iframe v-if="showTestExamine" ref="testExamineDialog" width="100%" height="100%"></iframe>
    </el-dialog>
  </div>
</template>

<script>
import { GetTestReportURL } from "@/api/External";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { GetViewShow } from "@/api/Setting";
import { mapGetters } from "vuex";

export default {
  name: "LabResultButton",
  props: {
    caseNumber: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      showFlag: false,
      showTestReportNewPageFlag: false,
      showTestExamine: false,
    };
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
    }),
  },
  beforeMount() {
    this.getShowFlag();
    this.getShowTestReportNewPageFlag();
  },
  methods: {
    /**
     * description: 查看检验数据
     * return {*}
     */
    handleShowLabResult() {
      this.showTestReportNewPageFlag ? this.showTestReportNewPage() : this.showTestReportDialog();
    },
    /**
     * @description: 新开页面
     * @return
     */
    showTestReportNewPage() {
      const caseNumber = this.caseNumber ?? this.patientInfo.caseNumber;
      if (!caseNumber) {
        this._showTip("warning", "获取检验地址失败！");
        return;
      }
      let params = {
        caseNumber: caseNumber,
      };
      GetTestReportURL(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.length > 0) {
            window.open(result.data, "_blank");
          } else {
            this._showTip("warning", "获取地址失败");
          }
        }
      });
    },
    /**
     * @description: 弹窗内嵌呈现
     * @return
     */
    showTestReportDialog() {
      this.showTestExamine = true;
      this.$nextTick(() => {
        this.$refs.testExamineDialog.contentWindow.location.replace("/testExamine?isDialog=true");
      });
    },
    /**
     * @description: 获取是否新开页面开关
     * @return
     */
    getShowTestReportNewPageFlag() {
      let paramTest = {
        settingTypeCode: "ShowTestReportNewPageCode",
      };
      GetSettingSwitchByTypeCode(paramTest).then((response) => {
        if (this._common.isSuccess(response)) {
          this.showTestReportNewPageFlag = response.data;
        }
      });
    },
    /**
     * @description: 获取是否呈现检验按钮开关
     * @return
     */
    getShowFlag() {
      let params = {
        settingTypeCode: "ViewDispable",
        typeValue: "CheckButton",
      };
      GetViewShow(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.showFlag = response.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.lab-result-button {
  display: inline;
}
</style>
