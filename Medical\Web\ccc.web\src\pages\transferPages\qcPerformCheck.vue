<!--
 * FilePath     : \ccc.web\src\pages\transferPages\qcPerformCheck.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 郭鹏超
 * LastEditTime : 2021-01-22 10:59
 * Description  : 串三级指控维护
--> 
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
// 代码需要迁移，暂时串到nursing
import { getOldNursingUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      user: "getUser",
    }),
  },
  watch: {
    $route: {
      immediate: true,
      handler(newVal) {
        this.url =
          getOldNursingUrl() +
          "export/qcPerformCheck?token=" +
          this.token +
          "&formType=" +
          newVal.query.type +
          "&stationID=" +
          this.user.stationID;
      },
    },
  },
};
</script>
