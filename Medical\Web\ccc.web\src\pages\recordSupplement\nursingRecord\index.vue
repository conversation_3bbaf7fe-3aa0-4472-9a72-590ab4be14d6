<!--
 * FilePath     : \src\pages\recordSupplement\nursingRecord\index.vue
* Author       : 赵路广
 * Date         : 2019-10-01 08:00
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-22 14:27
 * Description  : 新旧护理记录切换页
 * CodeIterationRecord: 2023-06-19 3581 作为护理人员，我需要在记录补录画面可以补录健康教育单和翻身记录单，以利出院患者病历补录完整 -杨欣欣
-->
<template>
  <base-layout class="nursing-record" header-height="auto" :showHeader="showModel == 'page'">
    <search-patient-data
      slot="header"
      @selectPatientData="selectPatientData"
      @change="change"
      v-if="showModel == 'page'"
    ></search-patient-data>
    <div class="layout-wrap">
      <component
        v-if="patientInfo && componentName"
        :is="componentName"
        :patientinfo="patientInfo"
        :model="showModel"
        :dataSource="dataSource"
        :supplemnentPatient="supplemnentPatient"
      ></component>
    </div>
  </base-layout>
</template>

<script>
import { GetClinicSettingByTypeCode } from "@/api/Setting";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData.vue";
import baseLayout from "@/components/BaseLayout";
import nursingRecordOld from "./nursingRecordOld.vue";
import nursingRecord from "@/autoPages/recordSupplement/components/nursingRecord";
export default {
  components: {
    baseLayout,
    searchPatientData,
    nursingRecordOld,
    nursingRecord,
  },
  data() {
    return {
      componentName: "",
      patientInfo: undefined,
      switchSettings: [],
      // 病历与切换时间点的TypeValue对照关系
      dataSourceTypeValueMapping: {
        nursingRecord: "NewNursingRecodLimitDateTime",
        healthEducationRecord: "HealthEduRecordLimitDateTime",
        turningRecord: "TurningRecordPdfLimitDateTime",
      },
    };
  },
  props: {
    model: {
      type: String,
      default: "page",
    },
    patientinfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    index: {
      type: Number,
      default: 1,
    },
    dataSource: {
      type: String,
      default: "nursingRecord",
    },
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  watch: {
    index: {
      handler(newIndex) {
        if (newIndex == 1) {
          this.change();
        } else {
          this.selectPatientData(this.patientinfo);
        }
      },
      immediate: true,
    },
    dataSource: {
      handler() {
        // 非护理记录单补录，直接使用新补录页面
        this.change();
        this.getComponentName();
        if (this.model) {
          this.showModel = this.model;
        }
        if (this.$route.query.model) {
          this.showModel = this.$route.query.model;
        }
      },
      immediate: true,
    },
  },
  async created() {
    // 首次进入，获取切换配置
    await this.getLimitDate();
  },
  methods: {
    /**
     * description: 重新搜索隐藏表格头部
     * param {*}
     * return {*}
     */
    change() {
      this.componentName = "";
    },
    /**
     * description: 查询病人
     * param {*} val
     * return {*}
     */
    selectPatientData(val) {
      this.patientInfo = val;
      this.getComponentName();
    },
    /**
     * description: 获取切换时间配置
     * return {*}
     */
    async getLimitDate() {
      let params = {
        settingTypeCode: "NewNursingRecord",
        index: Math.random(),
      };
      await GetClinicSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.switchSettings = res.data;
          this.getComponentName();
        }
      });
    },
    /**
     * description: 根据目前的切换配置，决定使用新/旧护理记录补录组件
     * param {*}
     * return {*}
     */
    getComponentName() {
      const typeValue = this.dataSourceTypeValueMapping[this.dataSource];
      const settingValue = this.switchSettings.find((m) => m.typeValue == typeValue)?.settingValue;

      if (!settingValue || !this.patientInfo || !this.patientInfo.admissionDateTimeView) {
        this.componentName = "";
        return;
      }
      let admissionDate = this._datetimeUtil.formatDate(this.patientInfo.admissionDateTimeView, "yyyy-MM-dd hh:mm");
      let limitDate = this._datetimeUtil.formatDate(settingValue, "yyyy-MM-dd hh:mm");
      if (admissionDate <= limitDate) {
        // 非护理记录单补录，不提供早于切换时间的病历信息补录
        if (this.dataSource != "nursingRecord") {
          this._showTip("warning", `入院时间在${limitDate}之前的病人不可补录此病历！`);
          return;
        }
        this.componentName = "nursingRecordOld";
      } else {
        this.componentName = "nursingRecord";
      }
    },
  },
};
</script>
<style lang="scss">
.base-layout.nursing-record {
  height: 100%;
  .base-header {
    padding: 0 0 10px 0;
  }
  .layout-wrap {
    height: 100%;
    .layout-top {
      padding: 0 10px;
      .data-select {
        width: 120px;
      }
      .tip {
        margin-left: 20px;
        color: #ff0000;
      }
      .top-btn {
        float: right;
      }
    }
  }
}
</style>
