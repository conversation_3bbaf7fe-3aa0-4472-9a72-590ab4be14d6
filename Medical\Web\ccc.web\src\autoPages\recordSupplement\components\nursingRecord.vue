<!--
 * FilePath     : \ccc.web\src\autoPages\recordSupplement\components\nursingRecord.vue
 * Author       : 郭鹏超
 * Date         : 2022-05-24 18:03
 * LastEditors  : LX
 * LastEditTime : 2025-07-18 15:48
 * Description  : 新版护理记录补录
 * CodeIterationRecord: 3029-作为护理人员，我需要进行记录补录的时候，可以对输入的非法数据进行提醒或者限制录入，以利于护理人员输入数据正确
                        3304-作为护理人员，我需要记录补录新增时默认当前登陆账户病区且过滤病人病区，以减少病区错选问题 -杨欣欣
                        3455-作为护理人员，我需要记录补录功能操作界面可更便捷，以便补充记录内容 -杨欣欣
                        3581 作为护理人员，我需要在记录补录画面可以补录健康教育单和翻身记录单，以利出院患者病历补录完整 -杨欣欣
                        4033-作为护理人员，我需要新增完护理记录可以默认带入护理记录单，以提高补录效率
-->
<template>
  <div class="patient-nursing-record">
    <base-layout v-loading="loading" element-loading-text="加载中……">
      <div class="nursing-record-header" slot="header">
        <label>日期:</label>
        <el-date-picker
          class="data-select"
          v-model="performDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          @change="getTableData()"
        ></el-date-picker>
        <label>科室:</label>
        <el-select
          v-model="departmentListID"
          class="department-select"
          placeholder="请选择科室"
          @change="selectChange('department')"
          :disabled="departmentOption.length === 1"
        >
          <el-option
            v-for="(dept, index) in departmentOption"
            :key="index"
            :label="dept.value"
            :value="dept.id"
          ></el-option>
        </el-select>
        <el-select
          v-model="fileClassID"
          placeholder="请选择记录单"
          class="nursing-record-select"
          @change="selectChange('nursingRecord')"
          v-if="nursingRecordOption.length > 1"
        >
          <el-option
            v-for="(nursingRecordFile, index) in nursingRecordOption"
            :key="index"
            :label="nursingRecordFile.value"
            :value="nursingRecordFile.id"
          />
        </el-select>
        <el-button
          class="add-button nursing-record-header-add-button"
          @click="recordAddOrUpdate()"
          icon="iconfont icon-add"
        >
          新增
        </el-button>
      </div>
      <div class="nursing-record-content">
        <el-table ref="newNursingTable" height="100%" :data="tableData" border stripe>
          <el-table-column prop="performDate" label="执行日期" :width="convertPX(165)" align="center"></el-table-column>
          <el-table-column prop="performTime" label="执行时间" :width="convertPX(107)" align="center"></el-table-column>
          <el-table-column
            prop="stationName"
            label="病区"
            :width="convertPX(149)"
            header-align="center"
          ></el-table-column>
          <el-table-column
            prop="departmentName"
            label="科别"
            :width="convertPX(125)"
            header-align="center"
          ></el-table-column>
          <el-table-column
            v-for="(columns, index) in tableColumns"
            :key="index"
            :prop="columns.prop"
            :min-width="columns.tableColumnWidth"
            :align="columns.align"
            :header-align="columns.headerAlign || 'center'"
          >
            <template slot="header" slot-scope="scope">
              <span v-html="columns.label" class="first-level-column-label"></span>
              <span v-if="false">{{ scope }}</span>
            </template>
            <template v-if="columns.children.length > 0">
              <el-table-column
                v-for="(twoColumns, index) in columns.children"
                :key="index"
                :prop="twoColumns.prop"
                :min-width="twoColumns.tableColumnWidth"
                :align="twoColumns.align"
                :header-align="twoColumns.headerAlign || 'center'"
              >
                <template slot="header" slot-scope="scope">
                  <span v-html="twoColumns.label"></span>
                  <span v-if="false">{{ scope }}</span>
                </template>
                <template slot-scope="scope">
                  <record-table
                    v-if="scope.row[twoColumns.prop]"
                    :rowData="scope.row[twoColumns.prop]"
                    :cssClass="getValueClass(scope.row, scope.row[twoColumns.prop])"
                  ></record-table>
                </template>
              </el-table-column>
            </template>
            <template v-if="columns.children.length == 0 && scope.row[columns.prop]" slot-scope="scope">
              <record-table
                :class="[
                  'record-content',
                  { 'has-ai-flag': columns.prop == 168 && scope.row[columns.prop].aiProofreadFlag },
                ]"
                :rowData="scope.row[columns.prop]"
                :cssClass="getValueClass(scope.row, scope.row[columns.prop])"
              ></record-table>
              <!-- 病情观察且经过AI润色 显示AI图标 -->
              <el-tooltip
                v-if="columns.prop == 168 && scope.row[columns.prop].aiProofreadFlag"
                content="此内容已经过AI处理，点击查看原始内容"
              >
                <span class="ai-proofread" @click="getAIProofreadOriginalText(scope.row[columns.prop])">AI</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作" header-align="center" :width="convertPX(120)" align="center" fixed="right">
            <template slot-scope="scope">
              <el-tooltip content="修改">
                <i class="iconfont icon-edit" @click="recordAddOrUpdate(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="复测">
                <i
                  :class="[getRetestFlag(scope.row) ? 'iconfont icon-retest' : 'iconfont icon-retest hide']"
                  @click="nursingRecordRetest(scope.row)"
                ></i>
              </el-tooltip>
              <el-tooltip content="删除">
                <i @click="nursingRecordDelete(scope.row)" class="iconfont icon-del"></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </base-layout>
    <!-- 新增抽屉 -->
    <el-drawer
      :append-to-body="false"
      :wrapperClosable="false"
      custom-class="add-drawer"
      :title="dialogTitle"
      :visible.sync="showDrawer"
      :destroy-on-close="true"
      :direction="direction"
      size="80%"
    >
      <div class="drawer-content">
        <base-layout headerHeight="auto">
          <div slot="header">
            <station-department-bed-date
              v-if="showDrawer"
              :switch="componentsSwitch"
              :stDeptBed="saveView"
              :enteredStationList="enteredStationList"
              @custCkick="getComponentData"
              @emitEnteredStationList="getEnteredStationList"
            />
            <user-selector
              v-model="saveView.userID"
              :stationID="saveView.stationID"
              label="执行人："
              clearable
              filterable
              remoteSearch
              :width="convertPX(199) + 'px'"
              :disabled="disabledFlag"
            />
            <template v-if="dataSource === 'nursingRecord'">
              <div class="bring-time-check-all">
                <label>带入时间：</label>
                <el-select @change="changeAllTimeSpan" v-model="bringTPRCommonTime" placeholder="请选择">
                  <el-option
                    v-for="(point, index) in temperaturePoints"
                    :key="index"
                    :label="point.value"
                    :value="point.value"
                    @click.native="clearAllTpr(point.value)"
                  ></el-option>
                </el-select>
              </div>
              <div class="bring-nursing-record-check-all">
                <span class="label">带入护理记录全选：</span>
                <el-checkbox @change="changeAllNursingRecordFlag" v-model="allNursingRecordFlag" />
              </div>
            </template>
          </div>
          <div class="add-record-content">
            <div class="tables">
              <el-table
                v-for="(table, index) in tables"
                :key="index"
                :header-cell-style="headerMethod"
                :span-method="(params) => arraySpanMethod(params, index)"
                row-class-name="tables-row"
                :class="['add-record-table', { alone: tables.length == 1 }]"
                :data="table.data"
                :ref="'table' + table.key"
                border
              >
                <el-table-column prop="oneLabel" :min-width="convertPX(200)" label="项目" align="center" />
                <el-table-column prop="twoLabel" :min-width="convertPX(200)" label="项目" align="center" />
                <el-table-column :min-width="convertPX(150)" label="数值" align="center">
                  <template slot-scope="scope">
                    <el-checkbox
                      v-if="scope.row.controlerType == 'C'"
                      v-model="scope.row.value"
                      :checked="!!scope.row.value"
                      :true-label="scope.row.twoLabel ? scope.row.twoLabel : scope.row.oneLabel"
                      false-label=""
                    />
                    <el-select
                      v-else-if="scope.row.controlerType == 'MDL'"
                      v-model="scope.row.subItems"
                      multiple
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="(option, index) in scope.row.selectList"
                        :key="index"
                        :label="option.label"
                        :value="option"
                      ></el-option>
                    </el-select>
                    <el-select
                      v-else-if="scope.row.controlerType == 'DL'"
                      v-model="scope.row.subItems[0]"
                      clearable
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="(option, index) in scope.row.selectList"
                        :key="index"
                        :label="option.label"
                        :value="option"
                      ></el-option>
                    </el-select>
                    <el-input
                      v-else-if="scope.row.controlerType == 'TN'"
                      v-model="scope.row.value"
                      name="TN"
                      @change="showLimit(scope.row)"
                      @keyup.38.native.stop="enterEvent($event.target, $event.code)"
                      @keyup.40.native.stop="enterEvent($event.target, $event.code)"
                      @keyup.39.native.stop="enterEvent($event.target, $event.code)"
                      @keyup.37.native.stop="enterEvent($event.target, $event.code)"
                      @keyup.enter.native.stop="enterEvent($event.target, $event.code)"
                    ></el-input>
                    <color-picker
                      v-else-if="scope.row.controlerType == 'P'"
                      :colorArray="scope.row.colorList"
                      v-model="scope.row.value"
                    />
                    <el-input
                      v-else
                      name="TN"
                      v-model="scope.row.value"
                      @keyup.38.native.stop="enterEvent($event.target, $event.code)"
                      @keyup.40.native.stop="enterEvent($event.target, $event.code)"
                      @keyup.39.native.stop="enterEvent($event.target, $event.code)"
                      @keyup.37.native.stop="enterEvent($event.target, $event.code)"
                      @keyup.enter.native.stop="enterEvent($event.target, $event.code)"
                    ></el-input>
                  </template>
                </el-table-column>
                <template v-if="dataSource === 'nursingRecord'">
                  <el-table-column :width="convertPX(140)" label="带入体温单" align="center">
                    <template slot-scope="scope">
                      <el-select
                        v-if="scope.row.tprFlag == '1'"
                        :disabled="scope.row.bringTPRTimeFlag"
                        v-model="scope.row.bringTPRTime"
                        placeholder="请选择"
                        class="time-span"
                        @change="timeIsSuccess"
                      >
                        <el-option
                          v-for="(point, index) in temperaturePoints"
                          :key="index"
                          :label="point.value"
                          :value="point.value"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column :width="convertPX(115)" prop="value" label="带入护理记录" align="center">
                    <template slot-scope="scope">
                      <el-checkbox v-model="scope.row.nursingRecordFlag" />
                    </template>
                  </el-table-column>
                </template>
              </el-table>
            </div>
            <div class="intervention" v-if="intervention">
              <div class="intervention-label">
                <div>{{ intervention.oneLabel }}：</div>
                <div>
                  带入护理记录：
                  <el-checkbox v-model="intervention.nursingRecordFlag" />
                </div>
              </div>
              <el-input class="intervention-content" type="textarea" :rows="rows" v-model="intervention.value" />
            </div>
          </div>
        </base-layout>
      </div>

      <div class="drawer-footer">
        <el-button @click="showDrawer = false">取消</el-button>
        <el-button v-if="saveFlag" type="primary" @click="saveNursingRecord()">确定</el-button>
      </div>
    </el-drawer>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      custom-class="ai-text-dialog"
      title="AI处理前后对比"
      :visible.sync="aiTextVisible"
      v-if="aiTextVisible"
    >
      <div class="label">原始内容：</div>
      <div class="text">{{ aiTextObj.originalText }}</div>
      <div class="label">AI处理后的内容：</div>
      <div class="text">{{ aiTextObj.proofreadText }}</div>
    </el-dialog>
  </div>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
import stationDepartmentBedDate from "@/pages/recordSupplement/components/stationDepartmentBedDate.vue";
import userSelector from "@/components/selector/userSelector";
import recordTable from "@/pages/recordSupplement/nursingRecord/components/recordTable";
import {
  GetNursingRecordTableColumns,
  GetNursingRecordTableData,
  NewNursingRecordSave,
  NursingRecordDelete,
  GetRetestNursingRecordDetailData,
  GetAllNursingRecordFileClass,
  GetDepartmentsByPerformDate,
} from "@/api/NewNursingRecord";
import { GetVitalSignPoint, GetClinicalBySettingTypeCode } from "@/api/Setting";
import { GetBringToNursingRecordFlagSetting } from "@/api/SettingDescription";
import { GetAIProofreadOriginalText } from "@/api/AIAssistant";
import colorPicker from "@/components/colorPicker/colorPicker";
import { GetStationList } from "@/api/Station";
export default {
  components: {
    baseLayout,
    stationDepartmentBedDate,
    userSelector,
    recordTable,
    colorPicker,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
    direction() {
      if (this._common.isPC()) {
        // PC端 默认下向上弹
        return "btt";
      } else {
        if (document.body.clientWidth > document.body.clientHeight) {
          // 平板  横屏从右向左弹
          return "rtl";
        } else {
          // 平板 竖屏从下向上弹
          return "btt";
        }
      }
    },
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    supplementPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    dataSource: {
      type: String,
      default: "nursingRecord",
    },
  },
  data() {
    return {
      loading: false,
      tableColumns: [],
      tableData: [],
      performDate: undefined,
      //弹窗标题
      dialogTitle: "",
      showDrawer: false,
      bringTPRCommonTime: undefined,
      allNursingRecordFlag: false,
      // 体温单时间点集合
      temperaturePoints: [],
      //表格合并变量
      spanArr: [],
      pos: 0,
      //保存View
      saveView: {
        inpatientID: "",
        patientID: "",
        stationID: "",
        departmentListID: "",
        bedNumber: "",
        bedID: "",
        assessDate: undefined,
        assessTime: undefined,
        performDate: "",
        performTime: "",
        userID: "",
        details: [],
      },
      // 分组后的数据，其中的元素是Object，拥有key和data两个Property，前端呈现
      tables: [],
      departmentOption: [],
      departmentListID: undefined,
      //复测配置
      retestSetting: [],
      //病情观察内容EMRSourceID
      observeSourceID: "168",
      nursingRecordOption: [],
      fileClassID: 1,
      // 设置病区科室床位日期组件是否显示
      componentsSwitch: {
        stationSwitch: true,
        departmentListSwitch: true,
        bedNumberSwitch: true,
        dateTimeSwitch: true,
      },
      inputs: [],
      // 抽屉中的观察措施
      intervention: undefined,
      // 底部措施的行数
      rows: 1,
      // 新数据的conditionValue
      newDataConditionValue: undefined,
      // 新数据的conditionType
      newDataConditionType: undefined,
      //新增时带入护理记录单标识
      addBringNursingRecordFlag: false,
      //带入护理记录单开关
      bringNursingRecordFlag: false,
      //新增默认病区
      defaultStationID: undefined,
      //新增默认科室
      defaultDepartmentListID: undefined,
      //患者经历病区
      enteredStationList: [],
      //补录患者标记
      supplementFlag: false,
      //禁用标记
      disabledFlag: false,
      //保存按钮
      saveFlag: true,
      aiTextVisible: false,
      aiTextObj: {},
    };
  },
  watch: {
    "patientInfo.inpatientID": {
      async handler(newValue) {
        if (!newValue) {
          return;
        }
        await this.init();
      },
      immediate: true,
    },
    showDrawer(newValue) {
      if (!newValue) {
        return;
      }
      this.setInterventionRows();
    },
    async dataSource() {
      await this.init();
    },
    performDate: {
      async handler(newValue) {
        if (!newValue) return;
        await this.GetAllDepartments();
        await this.setMappingVariables()[this.dataSource]();
        await Promise.all([this.getTableColumns(), this.getTableData()]);
      },
      immediate: true,
    },
  },
  mounted() {
    this.getRetestSetting();
    this.getTemperaturePoints();
    this.performDate = this._datetimeUtil.getNowDate();
    this.getAddNursingRecordFlagSwitch();
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    async init() {
      this.loading = true;
      if (this.supplementPatient) {
        this.supplementFlag = true;
      }
      await this.GetAllDepartments();
      await this.setMappingVariables()[this.dataSource]();
      await Promise.all([this.getTableColumns(), this.getTableData(), this.getEnteredStationList()]);
      this.loading = false;
    },
    /**
     * description: 获取设置当前补录的病历ID的函数
     * return {*}
     */
    setMappingVariables() {
      return {
        nursingRecord: async () => await this.getAllNursingRecord(),
        healthEducationRecord: () => (this.fileClassID = 4),
        turningRecord: () => (this.fileClassID = 18),
      };
    },
    /**
     * description: 拿到新增抽屉中所有的name为'TN'的所有input
     * return {*}
     */
    getAllInputs() {
      this.inputs = [];
      this.tables.forEach(({ key }) => {
        const tableInputs = [];
        let table = this.$refs["table" + key];
        const inputs = table?.[0]?.$el?.getElementsByTagName("input") ?? [];
        for (const input of inputs) {
          if (input?.name === "TN") {
            tableInputs.push(input);
          }
        }
        this.inputs.push(tableInputs);
      });
    },
    /**
     * description: 上下左右键切换输入框
     * param {*} nowInput 当前输入框DOM
     * param {*} keyCode 当前按下又弹起的键
     * return {*}
     */
    enterEvent(nowInput, keyCode) {
      // 获取当前输入框所在位置
      let currentTableIndex = this.inputs.findIndex((row) => row.includes(nowInput));
      let currentInputIndex = this.inputs[currentTableIndex].indexOf(nowInput);
      const mod = (x, n) => (x % n < 0 ? (x % n) + n : x % n);
      if (keyCode === "ArrowUp") {
        // 向上，下标前移
        currentInputIndex = mod(currentInputIndex - 1, this.inputs[currentTableIndex].length);
      }
      if (keyCode === "ArrowDown" || keyCode === "Enter") {
        // 向下，下标后移
        currentInputIndex = (currentInputIndex + 1) % this.inputs[currentTableIndex].length;
      }
      if (keyCode === "ArrowLeft") {
        // 向左，Table下标前移
        currentTableIndex = mod(currentTableIndex - 1, this.inputs.length);
        // Input下标处理，若当前下标超过下一Table的长度，则重定向到首位
        const nextTableLength = this.inputs[currentTableIndex].length;
        currentInputIndex > nextTableLength && (currentInputIndex = 0);
      }
      if (keyCode === "ArrowRight") {
        // 向右，Table下标后移
        currentTableIndex = (currentTableIndex + 1) % this.inputs.length;
        // Input下标处理，若当前下标超过下一Table的长度，则重定向到首位
        const nextTableLength = this.inputs[currentTableIndex].length;
        currentInputIndex > nextTableLength && (currentInputIndex = 0);
      }
      this.inputs[currentTableIndex][currentInputIndex].focus();
    },
    /**
     * description: TN类输入框提示
     * param {*} row
     * return {*}
     */
    showLimit(row) {
      let param = {
        itemName: row.oneLabel ? row.oneLabel : row.twoLabel,
        assessValue: row.value,
        decimal: row.decimal,
        upError: row.upError,
        lowError: row.lowError,
        controlerType: row.controlerType,
        textInput: row.textInput,
        checkLevelDict: row.checkLevelDict,
      };
      let result = this._common.checkAssessTN(param);
      if (result && !result.flag) {
        row.value = undefined;
      }
    },
    /**
     * description: 获取表格列配置
     * param {*}
     * return {*}
     */
    async getTableColumns() {
      this.tableColumns = [];
      let params = {
        departmentListID: this.departmentListID,
        age: this.patientInfo?.age,
        fileClassID: this.fileClassID,
        dataSource: this.dataSource,
      };
      await GetNursingRecordTableColumns(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tableColumns = res.data;
        }
      });
    },
    /**
     * description: 获取表格数据
     * param {*}
     * return {*}
     */
    async getTableData() {
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        departmentListID: this.departmentListID,
        performDate: this.performDate,
        age: this.patientInfo?.age,
        fileClassID: this.fileClassID,
        dataSource: this.dataSource,
      };
      await GetNursingRecordTableData(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tableData = res.data;
          // 这里不加setTimeout操作了会错行，原因未知
          setTimeout(() => {
            if (this.$refs.newNursingTable) {
              this.$refs.newNursingTable.doLayout();
            }
          }, 100);
        }
      });
    },
    /**
     * description: 新增/修改点击弹窗
     * param {*} row
     * return {*}
     */
    async recordAddOrUpdate(row) {
      this.saveView.inpatientID = this.patientInfo.inpatientID;
      this.saveView.patientID = this.patientInfo.patientID;
      if (row) {
        this.dialogTitle = `修改${row.retestFlag ? "复测" : "护理记录"}`;
        this.saveView.stationID = row.stationID;
        this.saveView.departmentListID = row.departmentListID;
        this.saveView.bedNumber = row.bedNumber;
        this.saveView.bedID = row.bedID;
        this.saveView.assessDate = row.performDate;
        this.saveView.assessTime = row.performTime;
        this.saveView.userID = row.userID;
        this.saveView.sourceMainIDs = row.sourceMainIDs;
        this.addBringNursingRecordFlag = false;
        ({ disabledFlag: this.disabledFlag, saveButtonFlag: this.saveFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          this.supplementFlag,
          row.userID
        ));
      } else {
        this.dialogTitle = "新增护理记录";
        this.saveFlag = true;
        this.saveView.stationID = this.defaultStationID ?? this.patientInfo.stationID;
        this.saveView.departmentListID = this.defaultDepartmentListID ?? this.patientInfo.departmentListID;
        this.saveView.bedNumber = this.patientInfo.bedNumber;
        this.saveView.bedID = this.patientInfo.bedID;
        this.saveView.assessDate = this._datetimeUtil.getNowDate();
        this.saveView.assessTime = this._datetimeUtil.getNowTime("hh:mm");
        this.saveView.userID = this.user.userID;
        this.addBringNursingRecordFlag = this.bringNursingRecordFlag;
      }
      // 构建Details和Tables
      ({ details: this.saveView.details, tables: this.tables } = this.getDetailsAndTables(row));
      // 构造行合并依据
      this.getSpanArr(this.saveView.details);
      // 将观察措施放到最底部
      this.intervention = this.saveView.details.find((m) => m.prop == this.observeSourceID);
      this.showDrawer = true;
      this.$nextTick(() => {
        this.getAllInputs();
      });
    },
    /**
     * description: 记录新增或修改
     * param {*}
     * return {*}
     */
    saveNursingRecord() {
      this.saveView.performDate = this.saveView.assessDate;
      this.saveView.performTime = this.saveView.assessTime;
      this.saveView.fileClassID = this.fileClassID;
      this.saveView.dataSource = this.dataSource;
      if (this.saveView && !this.saveView.userID) {
        this._showTip("warning", "请选择执行人");
        return;
      }
      if (this.addBringNursingRecordFlag) {
        for (const detail of this.saveView.details) {
          if (detail.value) {
            detail.nursingRecordFlag = true;
          }
        }
      }
      NewNursingRecordSave(this.saveView).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功!");
          this.showDrawer = false;
          this.getTableData();
        }
      });
    },
    /**
     * description: 记录删除
     * param {*}
     * return {*}
     */
    async nursingRecordDelete(row) {
      let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
        this.user.userID,
        false,
        this.supplementFlag,
        row.userID
      );
      if (!saveButtonFlag) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = this.getDeleteParams(row);
          NursingRecordDelete(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getTableData();
            }
          });
        }
      });
    },
    /**
     * description: 组装
     * param {*} row
     * return {*}
     */
    getDeleteParams(row) {
      let params = {
        sourceIDs: [],
        emrFieldIDs: [],
        fileClassID: this.fileClassID,
        dataSource: this.dataSource,
      };
      if (!row || Object.keys(row).length == 0) {
        return params;
      }
      // 遍历动态列描述对象，填充sourceIDs、emrFieldIDs数组
      Object.keys(row)
        .filter((m) => !isNaN(m))
        .reduce((accParams, currentKey) => {
          const cellSourceIDs = this.getSourceIDArr(row[currentKey]);
          accParams.sourceIDs.push(...cellSourceIDs);
          accParams.emrFieldIDs.push(Number(currentKey));
          return accParams;
        }, params);
      return params;
    },
    /**
     * description: 根据标题配置，组装抽屉中表格所需的必要数据
     * param {*} row 当前行数据，若点击新增，此值为undefined
     * return {*} 包含Details和Tables数组
     */
    getDetailsAndTables(row) {
      const details = [];
      const tables = [];
      // 预定义构造Tables的实现
      const buildTables = (detail) => {
        if (detail.prop == this.observeSourceID) {
          return;
        }
        const table = tables.find((m) => detail.placementLocation === m.key);
        if (table) {
          table.data.push(detail);
        } else {
          tables.push({
            key: detail.placementLocation,
            data: [detail],
          });
        }
      };

      for (const column of this.tableColumns) {
        const { children, label: parentColumnName } = column;
        // 有子级标题，使用子级标题构造Detail
        if (children && children.length > 0) {
          for (const childColumn of children) {
            const { label: childColumnName } = childColumn;
            const detail = this.getDetail(childColumn, parentColumnName, childColumnName, row);
            details.push(detail);
            buildTables(detail);
          }
        } else {
          const detail = this.getDetail(column, parentColumnName, undefined, row);
          details.push(detail);
          buildTables(detail);
        }
      }
      return { details, tables };
    },
    /**
     * description: 组装抽屉所需的Detail
     * param {*} column 当前列的样式配置
     * param {*} parentColumnName 一阶样式名称
     * param {*} childColumnName 二阶样式名称
     * param {*} row 当前修改的数据，若点击新增，此值为undefined
     * return {*}
     */
    getDetail(column, parentColumnName, childColumnName, row) {
      const { prop } = column;
      // 修改时，才有值
      const rowProp = row?.[prop];
      // 组装外面表格中每一列的对象
      const { conditionType, conditionValue } = this.getConditionTypeAndValue(row);
      let detail = {
        oneLabel: parentColumnName.replace("<br>", ""),
        twoLabel: childColumnName?.replace("<br>", ""),
        prop: prop,
        tprFlag: column.tprFlag,
        showNursingRecordFlag: column.showNursingRecordFlag,
        nursingRecordFlag: rowProp?.nursingRecordFlag ?? false,
        tprScheduleID: rowProp?.tprScheduleID,
        retestTprScheduleID: rowProp?.retestTprScheduleID,
        bringTPRTime: rowProp?.bringTPRTime,
        conditionType: rowProp?.conditionType ?? conditionType,
        conditionValue: rowProp?.conditionValue ?? conditionValue,
        sourceIDArr: this.getSourceIDArr(rowProp),
        value: rowProp?.value ?? "",
        controlerType: column.controlerType,
        selectList: column.selectList,
        lowError: column.lowError,
        upError: column.upError,
        colorList: column.colorList,
        decimal: column.decimal,
        placementLocation: column.placementLocation,
        subItems: this._common.clone(rowProp?.subItems) ?? [],
        subItemSourceIDMap: rowProp?.subItemSourceIDMap,
        columnMergeFlag: column.columnMergeFlag,
        textInput: column.textInput,
        checkLevelDict: column.checkLevelDict,
      };
      // 复测特殊处理
      if (row?.retestFlag && this.retestSetting.includes((item) => prop == item.typeValue)) {
        detail.bringTPRTime = true;
      }
      return detail;
    },
    /**
     * description: 获取当前单元格所关联的sourceIDs
     * param {*} cell 当前单元格
     * return {*}
     */
    getSourceIDArr(cell) {
      let sourceIDs = undefined;

      let controlTypeSelect = ["DL", "MDL"];
      if (!cell) {
        return [];
      }
      // 单元格合并逻辑和下拉框组件,修改操作都传参sourceIDArr（单元格合并，原本代码就是为下拉框组件调整的，解决下拉框组件不配置合并单元格时，正确回传sourceIDArr）
      if (cell?.columnMergeFlag || controlTypeSelect.includes(cell.controlerType)) {
        const values = Object.values(cell.subItemSourceIDMap).flat();
        sourceIDs = [...new Set(values)];
      } else {
        sourceIDs = Array.isArray(cell?.sourceIDs) ? cell.sourceIDs : [cell.sourceIDs];
      }
      return sourceIDs ?? [];
    },
    /**
     * description: 批量勾选取消带入护理记录单
     * param {*}
     * return {*}
     */
    changeAllNursingRecordFlag() {
      if (this.saveView.details?.length) {
        this.saveView.details.forEach((detail) => {
          this.$set(detail, "nursingRecordFlag", this.allNursingRecordFlag);
        });
      }
    },
    /**
     * description: 获取经历科室
     * return {*}
     */
    async GetAllDepartments() {
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        departmentID: this.patientInfo.departmentListID,
        performDate: this.performDate,
        index: Math.random(),
      };

      await GetDepartmentsByPerformDate(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.departmentOption = res.data;
          const exist = this.departmentOption.find((item) => item.id === this.departmentListID);
          if (!exist) {
            this.departmentListID = this.departmentOption[0]?.id;
          }
        }
      });
    },
    /**
     * description: 获取体温单时间点集合
     * param {*}
     * return {*}
     */
    getTemperaturePoints() {
      this.temperaturePoints = [];
      GetVitalSignPoint().then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          // 用于下拉框的不选择项
          this.temperaturePoints.push({
            value: undefined,
          });
          result.data.forEach((temperaturePoint) => {
            if (temperaturePoint.value) {
              temperaturePoint.value = this._datetimeUtil.formatDate(temperaturePoint.value, "hh:mm");
            }
            this.temperaturePoints.push(temperaturePoint);
          });
        }
      });
    },
    /**
     * description: 表头合并
     * param {*} row
     * param {*} columnIndex
     * return {*}
     */
    headerMethod({ row, columnIndex }) {
      this.$set(row[0], "colSpan", 0);
      this.$set(row[1], "colSpan", 2);
      if (columnIndex === 0) {
        return { display: "none" };
      }
    },
    /**
     * description: 组装行合并使用的中间变量，若其中值为：1->不合并，大于1->合并多少行，0->此行被前面行所合并
     * param {*} details 当前修改记录行的所有Detail数据
     * return {*}
     */
    getSpanArr(details) {
      this.spanArr = [];
      for (var i = 0; i < details.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (details[i].oneLabel == details[i - 1].oneLabel) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    /**
     * description: 表格内容合并
     * param {*} rowIndex 当前单元格的行下标
     * param {*} columnIndex 当前单元格的列下标
     * param {*} currentTableIndex 抽屉中当前表格的下标
     * return {*}
     */
    arraySpanMethod({ row, rowIndex, columnIndex }, currentTableIndex) {
      let currentRowIndex = rowIndex;
      // 第二个Table起，需要结合先前的Table长度计算出偏移量
      if (currentTableIndex > 0) {
        // acc为累计值
        const offset = this.tables
          .slice(0, currentTableIndex)
          .reduce((acc, currentTable) => acc + currentTable.data.length, 0);
        currentRowIndex = offset + rowIndex;
      }
      let _row = 1;
      let _col = 1;
      //合并第一列
      if (columnIndex === 0) {
        _row = this.spanArr[currentRowIndex];
        // 列是否合并看当前行是否存在child
        _col = row.twoLabel ? 1 : 2;
      }
      // 第二列取决于第一列的值
      if (columnIndex === 1) {
        _col = row.twoLabel ? 1 : 0;
      }
      return {
        rowspan: _row,
        colspan: _col,
      };
    },
    /**
     * description: 获取是否为复测标记
     * param {*} row
     * return {*}
     */
    getRetestFlag(row) {
      let values = Object.values(row);
      if (!values?.length) {
        return false;
      }
      return values.some((value) => value && typeof value === "object" && value.retestFlag);
    },
    /**
     * description: 点击复测按钮回显数据
     * param {*} row 当前行数据
     * return {*}
     */
    async nursingRecordRetest(row) {
      this.saveView = {};
      let params = this.getRetestParams(row);
      await GetRetestNursingRecordDetailData(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data && res.data.details && res.data.details.length) {
            res.data.details.forEach((detail) => {
              if (detail.bringTPRTime) {
                detail.bringTPRTime = this._datetimeUtil.formatDate(detail.bringTPRTime, "hh:mm");
              }
            });
            this.getSpanArr(res.data.details);
            // 设置呈现
            this.tables = [
              {
                key: 1,
                data: res.data.details.filter((m) => m.prop != this.observeSourceID),
              },
            ];
            this.intervention = res.data.details.find((m) => m.prop == this.observeSourceID);
            this.saveView = res.data;
            this.saveView.stationID = res.data.stationID;
            this.saveView.departmentListID = res.data.departmentListID;
            this.saveView.bedNumber = res.data.bedNumber;
            this.saveView.bedId = res.data.bedID;
            this.saveView.assessDate = res.data.performDate;
            this.saveView.assessTime = res.data.performTime;
            this.showDrawer = true;
            this.dialogTitle = "新增复测";
          }
        }
      });
      this.$nextTick(() => {
        this.getAllInputs();
      });
    },
    /**
     * description: 组装获取复测API参数
     * param {*} row 当前行数据
     * return {*}
     */
    getRetestParams(row) {
      let params = {
        sourceIDs: [],
        emrFieldIDs: [],
      };
      if (!row || Object.keys(row).length == 0) {
        return params;
      }
      // 遍历动态列描述对象，填充sourceIDs、emrFieldIDs数组
      Object.keys(row)
        .filter((m) => !isNaN(m))
        .reduce((accParams, currentKey) => {
          // 没有retestFlag，跳过当前key
          if (!row[currentKey]?.retestFlag) {
            return accParams;
          }
          const cellSourceIDs = this.getSourceIDArr(row[currentKey]);
          accParams.sourceIDs.push(...cellSourceIDs);
          accParams.emrFieldIDs.push(Number(currentKey));
          return accParams;
        }, params);
      return params;
    },
    /**
     * description: 获取复测配置
     * param {*}
     * return {*}
     */
    getRetestSetting() {
      this.retestSetting = [];
      let params = {
        settingTypeCode: "TPRRetestSourceID",
        firstSpace: false,
      };
      GetClinicalBySettingTypeCode(params).then((res) => {
        if (this._common.isSuccess(res) && res.data && res.data.length) {
          this.retestSetting = res.data;
        }
      });
    },
    /**
     * description: 获取新增护理记录是否默认带入护理记录开关
     * param {*}
     * return {*}
     */
    getAddNursingRecordFlagSwitch() {
      let param = {
        settingTypeCode: "SupplyBringToNursingRecordFlag",
      };
      GetBringToNursingRecordFlagSetting(param).then((result) => {
        if (this._common.isSuccess(result)) {
          this.bringNursingRecordFlag = result.data;
        }
      });
    },
    /**
     * description: 替换颜色
     * param {*} row
     * param {*} item
     * return {*}
     */
    getValueClass(row, item) {
      if (row.retestFlag && item.tprScheduleID) {
        return "is-retest";
      }
      if (item.bringTPRTime) {
        return "is-bring-tpr";
      }
      return "";
    },
    /**
     * description: 超出时间段提醒
     * param {*} value
     * return {*}
     */
    timeIsSuccess(value) {
      let sucTimeData = this.temperaturePoints.find(
        (point) => point.value && this._datetimeUtil.formatDate(point.value, "hh:mm") == value
      );
      if (!sucTimeData) {
        return false;
      }
      let formatStartTime = this._datetimeUtil.formatDate(sucTimeData.startTime, "hh:mm");
      let formatEndTime = this._datetimeUtil.formatDate(sucTimeData.endTime, "hh:mm");
      if (!(this.saveView.assessTime >= formatStartTime && this.saveView.assessTime <= formatEndTime)) {
        this._showTip(
          "warning",
          "执行时间在" +
            formatStartTime +
            "与" +
            formatEndTime +
            "之间才能带入体温单" +
            this._datetimeUtil.formatDate(value, "hh:mm") +
            "时间段"
        );
        return false;
      }
      return true;
    },
    /**
     * description: 批量调整带入护理记录单时间
     * param {*}
     * return {*}
     */
    changeAllTimeSpan() {
      if (this.saveView.details.length == 0) {
        return;
      }
      if (!this.timeIsSuccess(this.bringTPRCommonTime)) {
        return;
      }
      this.saveView.details.forEach((detail) => {
        if (!detail.bringTPRTime && detail.tprFlag == "1") {
          detail.bringTPRTime = this.bringTPRCommonTime;
        }
      });
    },
    /**
     * description: 所有数据取消带入护理记录单
     * param {*} point
     * return {*}
     */
    clearAllTpr(point) {
      if (!point) {
        this.saveView.details.forEach((detail) => {
          detail.bringTPRTime = "";
        });
      }
    },
    /**
     * description: 下拉框选项切换，重新获取标题、内容
     * return {*}
     */
    async selectChange(changeSelector) {
      // 切换科室，重新获取科室对应的护理记录单类别
      if (changeSelector === "department") {
        await this.setMappingVariables()[this.dataSource]();
      }
      this.loading = true;
      await Promise.all([this.getTableColumns(), this.getTableData()]);
      this.loading = false;
    },
    /**
     * description: 获取此科室所有护理记录单类别
     * return {*}
     */
    async getAllNursingRecord() {
      let params = {
        performDate: this.performDate,
        departmentListID: this.departmentListID,
        age: this.patientInfo.age,
        inpatientID: this.patientInfo.inpatientID,
      };
      await GetAllNursingRecordFileClass(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.nursingRecordOption = res.data;
          this.fileClassID = this.nursingRecordOption[0]?.id;
        }
      });
    },
    /**
     * description: 回调更新对象成员
     * param {*} tempStDeptBed
     * return {*}
     */
    getComponentData(tempStDeptBed) {
      this.saveView.stationID = tempStDeptBed.stationID;
      this.saveView.departmentListID = tempStDeptBed.departmentListID;
      this.saveView.bedNumber = tempStDeptBed.bedNumber;
      this.saveView.bedId = tempStDeptBed.bedId;
      this.saveView.assessDate = tempStDeptBed.assessDate;
      this.saveView.assessTime = tempStDeptBed.assessTime;
    },
    /**
     * description: 动态计算底部的观察措施的行数，使其不超过可视范围
     * return {*} 返回计算后的行数(最多8行)，若计算异常返回1
     */
    setInterventionRows() {
      this.$nextTick(() => {
        if (!this.showDrawer) {
          this.rows = 1;
          return;
        }

        const tableEl = document.querySelector(".tables");
        const viewportEl = document.querySelector(".drawer-content .base-content-wrap");
        const rowEl = document.querySelector(".tables-row");

        if (!tableEl || !viewportEl || !rowEl) {
          this.rows = 1;
          return;
        }

        const tableHeight = tableEl.offsetHeight;
        const viewportHeight = viewportEl.clientHeight;
        const maxInputHeight = viewportHeight - tableHeight - this.convertPX(55);
        const lineHeight = parseInt(window.getComputedStyle(rowEl).lineHeight);
        this.rows = maxInputHeight && lineHeight ? Math.floor(maxInputHeight / lineHeight) : 1;
        if (this.rows > 8) {
          this.rows = 8;
        }
      });
    },
    /**
     * @description: 获取当前行的conditionType和conditionValue。若点击的新增则放置默认的值，若点击修改则使用当前行其它列的值
     * @param row 当前行数据
     * @return
     */
    getConditionTypeAndValue(row) {
      if (row) {
        for (const key in row) {
          if (row[key].conditionType && row[key].conditionValue) {
            return {
              conditionType: row[key].conditionType,
              conditionValue: row[key].conditionValue,
            };
          }
          return {
            conditionType: undefined,
            conditionValue: undefined,
          };
        }
      }
      if (this.dataSource === "healthEducationRecord") {
        return {
          conditionType: "ActionType",
          conditionValue: "3",
        };
      }
      if (this.dataSource === "turningRecord") {
        return {
          conditionType: "NursingInterventionMain",
          conditionValue: "6010",
        };
      }
      return {
        conditionType: undefined,
        conditionValue: undefined,
      };
    },
    /**
     * description: 获取患者经历病区列表
     * return {*}
     */
    async getEnteredStationList() {
      let params = {
        inpatientID: this.patientInfo.inpatientID,
      };
      await GetStationList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.enteredStationList = result.data;
          let filterData = result.data.find((station) => station.id == this.user.stationID);
          if (filterData) {
            this.defaultStationID = filterData.id || undefined;
            this.defaultDepartmentListID = filterData.departmentListID || undefined;
          }
        }
      });
    },
    /**
     * @description: 获取AI润色的原始文本
     * @param cellData 单元格数据
     * @return
     */
    getAIProofreadOriginalText(cellData) {
      this.aiTextObj = {};
      let params = {
        sourceTable: "NursingInterventionDetail",
        sourceID: cellData.logID,
        sourceField: "DataValue",
      };
      GetAIProofreadOriginalText(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (!result.data) {
            return;
          }
          this.aiTextVisible = true;
          this.aiTextObj = {
            originalText: result.data,
            proofreadText: cellData.value,
          };
        }
      });
    },
  },
};
</script>

<style lang="scss">
.patient-nursing-record {
  height: 100%;
  .nursing-record-header {
    .data-select {
      width: 155px;
    }
    .department-select {
      width: 210px;
    }
    .nursing-record-select {
      width: 210px;
    }
    .nursing-record-header-temperature,
    .nursing-record-header-add-button {
      float: right;
      margin-right: 10px;
      margin-top: 8px;
    }
  }
  .nursing-record-content {
    height: 100%;
    .first-level-column-label {
      font-size: 16px;
    }
    .hide {
      visibility: hidden;
    }
    .is-bring-tpr {
      color: $base-color;
    }
    .is-retest {
      color: #0000ff;
    }
    .omit-if-overflow {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .record-content {
      display: inline-block;
      width: 100%;
      &.has-ai-flag {
        width: calc(100% - 40px);
      }
      & > div {
        width: 100%;
      }
    }
    .ai-proofread {
      width: 40px;
      padding: 0 5px;
      box-sizing: border-box;
      color: transparent;
      font-size: 12px;
      cursor: pointer;
      font-style: italic;
      background: linear-gradient(to right, #ff0000, #ff7d00, #ff00ff, #0000ff, #ff0000, #ff7d00, #ff00ff, #0000ff);
      background-clip: text;
      background-size: 200% 100%;
      animation: masked-animation 1s infinite linear;
    }
    @keyframes masked-animation {
      0% {
        background-position: 0 0;
      }
      100% {
        background-position: -100%, 0;
      }
    }
  }
  .add-drawer {
    background-color: #f3f3f3;

    .el-drawer__header span {
      outline: none;
    }
    .el-drawer__body {
      height: calc(100% - 35px);
      .drawer-content {
        height: calc(100% - 33px);
        .base-header .label {
          margin-left: 5px;
          display: inline-block;
        }
        .bring-time-check-all {
          display: inline-block;
          label {
            width: 20px;
          }
          .el-select {
            width: 105px;
          }
        }
        .bring-nursing-record-check-all {
          display: inline-block;
          width: 270px;
        }
        .base-content {
          background-color: #fff;
          .add-record-content {
            margin: 10px;
            .tables {
              display: flex;
              // 两个表格两端对齐
              justify-content: space-around;
              .add-record-table {
                flex: 0 0 49%;
              }
              // 仅有一个表格时，占据剩余空间
              .add-record-table.alone {
                flex: 1;
              }
              /* 下拉框 */
              .el-select {
                width: 100%;
                .el-input__inner {
                  padding: 0px 16px 0 5px;
                  color: $base-color;
                }
                /* 多选 */
                .el-select__tags {
                  height: 32px;
                  line-height: 32px;
                  max-width: none !important;
                  span:not(.el-tag) {
                    display: inline-block;
                    width: 100%;
                    height: 32px;
                    line-height: 30px;
                    text-align: left;
                  }
                  .el-tag {
                    margin: 0;
                    border: 0;
                    background-color: transparent;
                    & + .el-tag {
                      padding-left: 0;
                    }
                    span {
                      width: auto;
                    }
                    .el-select__tags-text {
                      font-weight: initial;
                    }
                    .el-tag__close {
                      display: none;
                    }
                  }
                }
              }
            }
            // 底部的观察措施
            .intervention {
              margin-top: 20px;
              display: flex;
              align-items: center;
              .intervention-label {
                width: 17%;
                line-height: 30px;
                flex-direction: column;
              }
            }
          }
        }
      }
    }
    .drawer-footer {
      bottom: 5px;
      background-color: #fff;
      .bring-checkbox {
        margin-right: 15px;
      }
    }
  }
  .temperature-list-dialog {
    & .el-dialog__body {
      height: calc(100% - 35px);
    }
  }
  .ai-text-dialog {
    .label {
      font-weight: bold;
      margin: 0 20px 5px 20px;
    }
    .text {
      margin: 0 20px 30px 20px;
      padding: 10px 20px;
      box-sizing: border-box;
      border: 2px solid #ccc;
    }
  }
}
</style>