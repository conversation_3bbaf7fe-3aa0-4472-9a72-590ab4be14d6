/*
 * FilePath     : \ccc.web\src\api\CheckAuthority.js
 * Author       : 孟昭永
 * Date         : 2021-06-02 15:48
 * LastEditors  : 孟昭永
 * LastEditTime : 2021-11-01 15:38
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/CheckAuthority";

export const urls = {
  GetEditAuthority: baseUrl + "/GetEditAuthority"
};

export const GetEditAuthority = params => {
  return http.get(urls.GetEditAuthority, params);
};
