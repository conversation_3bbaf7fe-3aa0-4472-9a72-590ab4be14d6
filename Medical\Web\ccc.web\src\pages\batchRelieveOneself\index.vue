<!--
 * FilePath     : \src\pages\batchRelieveOneself\index.vue
 * Author       : 郭鹏超
 * Date         : 2020-08-13 16:24
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-06-18 15:26
 * Description  : 批量录入大小便
-->
<template>
  <base-layout class="batch-relieve">
    <div class="batch-relieve-top" slot="header">
      <label v-if="showALLLable">显示全科:</label>
      <el-switch
        v-if="showALLLable"
        @change="getTabelData()"
        :active-value="0"
        :inactive-value="1"
        v-model="formatData.flag"
      />
      <label>日期:</label>
      <el-date-picker
        type="date"
        placeholder="选择班别日期"
        v-model="formatData.scheduleDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        class="top-data-pick"
      ></el-date-picker>
      <shift-selector :stationID="stationID" v-model="formatData.shiftID"></shift-selector>
      <shift-times-selector @select="changeShiftTimes" width="110" :shiftID="formatData.shiftID"></shift-times-selector>
      <el-button @click="getTabelData()" class="query-button" icon="iconfont icon-search">查询</el-button>
      <div class="header-right">
        <label v-if="tableData.length">执行时间：</label>
        <el-time-picker
          v-if="tableData.length"
          v-model="performTime"
          value-format="HH:mm"
          format="HH:mm"
          class="time-picker"
          @change="changePerFormTime()"
        ></el-time-picker>
        <el-button type="primary" icon="iconfont icon-save-button" @click="batchRelieveOneself()">保存</el-button>
      </div>
      <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    </div>
    <div slot-scope="layout" :style="{ height: layout.height + 'px' }" class="batch-relieve-content">
      <u-table
        v-loading="loading"
        element-loading-text="加载中……"
        :data="tableData"
        border
        stripe
        use-virtual
        highlight-current-row
        :height="layout.height"
        :row-height="43"
        :span-method="objectSpanMethod"
        ref="ioTable"
      >
        <u-table-column type="selection" width="55" align="center" fixed></u-table-column>
        <u-table-column prop="basicInformation" fixed="left" label="姓名" width="200" align="center"></u-table-column>
        <u-table-column
          prop="intakeOutputKindName"
          fixed="left"
          label="种类"
          width="60"
          align="center"
        ></u-table-column>
        <u-table-column label="项目" width="140" align="center">
          <template slot-scope="scope">
            <el-select
              @change="getArrData(scope.row)"
              v-model="scope.row.relieveOneselfContent.intakeOutputSettingID"
              placeholder="请选择"
              style="width: 96%"
            >
              <el-option
                v-for="(item, index) in scope.row.intakeOutputSettingIDArr"
                :key="index"
                :label="item.intakeOutput"
                :value="item.id"
              ></el-option>
            </el-select>
          </template>
        </u-table-column>
        <u-table-column label="颜色" width="40" align="center">
          <template slot-scope="scope">
            <color-picker
              :colorArray="scope.row.colorArr"
              v-model="scope.row.relieveOneselfContent.color"
              width="98%"
              class="relievr-color-picker"
            />
          </template>
        </u-table-column>
        <u-table-column label="性状" width="125" align="center">
          <template slot-scope="scope">
            <el-select
              @change="getName(scope.row, 'characteristic')"
              v-model="scope.row.relieveOneselfContent.characteristicID"
              style="width: 96%"
            >
              <el-option
                v-for="(item, index) in scope.row.characteristicArr"
                :key="index"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
          </template>
        </u-table-column>
        <u-table-column label="气味" width="125" align="center">
          <template slot-scope="scope">
            <el-select
              @change="getName(scope.row, 'smell')"
              v-model="scope.row.relieveOneselfContent.smellID"
              style="width: 96%"
            >
              <el-option
                v-for="(item, index) in scope.row.smellArr"
                :key="index"
                :label="item.value"
                :value="item.key"
              ></el-option>
            </el-select>
          </template>
        </u-table-column>
        <u-table-column label="量" width="100" align="center">
          <template slot-scope="scope">
            <el-input
              class="volume-input"
              v-model="scope.row.relieveOneselfContent.intakeOutputVolume"
              @blur="validateByTextEntry(scope.row.relieveOneselfContent, 'intakeOutputVolume')"
            >
              <template slot="append">ml</template>
            </el-input>
          </template>
        </u-table-column>
        <u-table-column label="次数" width="80" align="center">
          <template slot-scope="scope">
            <el-input
              :disabled="
                (scope.row.relieveOneselfContent.intakeOutputSettingID != 106 &&
                  scope.row.relieveOneselfContent.intakeOutputKind == '210') ||
                (scope.row.relieveOneselfContent.intakeOutputSettingID != 107 &&
                  scope.row.relieveOneselfContent.intakeOutputKind == '220')
              "
              v-model="scope.row.relieveOneselfContent.intakeOutputTimes"
              style="width: 60px"
            ></el-input>
          </template>
        </u-table-column>
        <u-table-column label="备注" min-width="200" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.relieveOneselfContent.intakeOuputNote" style="width: 100%"></el-input>
          </template>
        </u-table-column>
        <u-table-column label="执行日期" width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.relieveOneselfContent.ioDate"
              type="date"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              style="width: 85px"
            ></el-date-picker>
          </template>
        </u-table-column>
        <u-table-column label="执行时间" width="62" align="center" fixed="right">
          <template slot-scope="scope">
            <el-time-picker
              v-model="scope.row.relieveOneselfContent.ioTime"
              type="time"
              value-format="HH:mm"
              format="HH:mm"
              style="width: 47px"
            ></el-time-picker>
          </template>
        </u-table-column>
        <u-table-column label="操作" width="80" align="center" fixed="right">
          <template slot-scope="scope">
            <el-tooltip content="保存">
              <i
                v-if="scope.row.saveFlag || scope.row.id"
                class="iconfont icon-edit"
                @click="relieveOneselfSave(scope.row)"
              ></i>
              <i v-else class="iconfont icon-save" @click="relieveOneselfSave(scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="清除">
              <i class="iconfont icon-clear" @click="clearRowData(scope.row)"></i>
            </el-tooltip>
          </template>
        </u-table-column>
      </u-table>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import colorPicker from "@/components/colorPicker/colorPicker";
import progressView from "@/components/progressView";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetBatchRelieveOneselfByTime } from "@/api/PatientSchedule";
import { GetIOItem, GetColorByAssessListIDArr } from "@/api/Setting";
import { BatchSave, GetOutputAttributeByAssessListIDArr } from "@/api/IO";
import shiftSelector from "@/components/selector/shiftSelector";
import shiftTimesSelector from "@/components/selector/shiftTimesSelector";
import { validateByTextEntry } from "@/utils/textEntryValidate";
export default {
  components: {
    baseLayout,
    colorPicker,
    progressView,
    shiftSelector,
    shiftTimesSelector,
  },
  data() {
    return {
      loading: false,
      nowShiftData: "", //当时数据
      assessListIDArr: [],
      fecesArr: [], //大便项目数组
      urinateArr: [], //小便项目数组
      colorArr: [],
      characteristicArr: [],
      smellArr: [],
      //是否显示选择全科按钮，默认显示
      showALLLable: true,
      //默认初始化
      initFlag: true,
      formatData: {
        flag: "1",
        scheduleDate: "",
        shiftID: "",
        shiftTimes: "",
      },
      tableData: [], //表格数据
      originalData: [], // 表格原始数据，用于判断是否更改
      clearData: {
        characteristic: undefined,
        characteristicID: undefined,
        color: undefined,
        intakeOuputNote: undefined,
        intakeOutputTimes: undefined,
        intakeOutputVolume: undefined,
        smell: undefined,
        smellID: undefined,
      },
      //合并单元格index
      rowIndexArr: [],
      rowSpan: undefined,
      // 是否显示批量保存进度框
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      performTime: "",
      stationID: "",
      //TextEntry检核函数
      validateByTextEntry,
    };
  },
  props: {
    inpatientID: {
      type: String,
      default: () => {
        return undefined;
      },
    },
    index: {
      type: Number,
      default: 1,
    },
  },
  watch: {
    //用于页签点击事件触发后的响应
    index: {
      immediate: true,
      handler(newValue) {
        //只有页面跳转的才可以不显示按钮，1是index 的默认值,所以添加判断
        if (newValue !== 1) {
          //设置不显示按钮（全科）
          this.showALLLable = false;
          this.initFlag = false;
          this.init();
        }
      },
    },
    // 监听数据变化，判断是否勾选
    tableData: {
      deep: true,
      handler(newValue) {
        if (newValue && newValue.length > 0 && this.originalData && this.originalData.length > 0) {
          for (let i = 0; i < newValue.length; i++) {
            let isChange = false;
            if (
              JSON.stringify(newValue[i].relieveOneselfContent) !=
              JSON.stringify(this.originalData[i].relieveOneselfContent)
            ) {
              isChange = true;
            }
            this.$refs.ioTable.toggleRowSelection([
              {
                row: newValue[i],
                selected: isChange,
              },
            ]);
          }
        }
      },
    },
  },
  mounted() {
    if (this.initFlag) {
      this.init();
    }
  },
  methods: {
    /**
     * description: 初始化函数
     * return {*}
     */
    async init() {
      if (this.$route.query) {
        this.stationID = this.$route.query.stationID;
      }
      this.assessListIDArr = [];
      await this.getNowShiftData();
      await this.getIOItem(210, "urinateArr");
      await this.getIOItem(220, "fecesArr");
      await this.getSelectList();
      await this.getTabelData();
    },
    /**
     * description: 获取当前班别数据
     * return {*}
     */
    async getNowShiftData() {
      //请求时,添加一个随机参数,防止切换不同页签的时候被拦截导致获取失败
      let params = {
        index: Math.random(),
      };
      await GetNowStationShiftData(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.nowShiftData = res.data;
          this.stationID = res.data.nowShift.stationID;
          this.formatData.scheduleDate = this._datetimeUtil.formatDate(this.nowShiftData.shiftDate, "yyyy-MM-dd");
        }
      });
    },
    /**
     * description: 切换班别时间段
     * return {*}
     */
    changeShiftTimes(value) {
      this.formatData.shiftTimes = value;
      this.getTabelData();
    },
    /**
     * description: 获取表格数据
     * return {*}
     */
    async getTabelData() {
      this.loading = true;
      this.tableData = [];
      this.originalData = [];
      let fixParams = {
        startTime: this.formatData.shiftTimes.split("-")[0],
        endTime: this.formatData.shiftTimes.split("-")[1],
        index: Math.random(),
      };
      fixParams = Object.assign(fixParams, this.formatData);
      let { shiftTimes, ...params } = fixParams;
      //处理父组件传值InpatientID的时候，获取单病人的批量录入大小便,不使用时间段查询
      params.inpatientID = this.inpatientID;

      await GetBatchRelieveOneselfByTime(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data && res.data.length) {
            //表格数据处理
            this.tableData = this.fixTableData(res.data);
            this.originalData = this._common.clone(this.tableData);
            this.performTime = this._datetimeUtil.getNowTime();
          } else {
            this.tableData = [];
            this.originalData = [];
          }
        }
      });
    },
    /**
     * description: 表格数据处理
     * return {*}
     * param {*} value
     */
    fixTableData(value) {
      value.map((item) => {
        if (!item.relieveOneselfContent.ioDate || !item.relieveOneselfContent.ioTime) {
          item.relieveOneselfContent.ioDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
          item.relieveOneselfContent.ioTime = this._datetimeUtil.getNowTime("hh:mm");
        }
        item.colorArr = [];
        if (item.relieveOneselfContent.intakeOutputKind == 210) {
          //小便
          this.$set(item, "intakeOutputSettingIDArr", this.urinateArr);
        }
        if (item.relieveOneselfContent.intakeOutputKind == 220) {
          //大便
          this.$set(item, "intakeOutputSettingIDArr", this.fecesArr);
        }
        // 颜色特殊处理下，将后端传的null转换为空字符串，方便后面比较
        if (item.relieveOneselfContent.color == null) {
          item.relieveOneselfContent.color = "";
        }
        this.getArrData(item);
      });
      this.setIndex(value);
      return value;
    },
    /**
     * description: 获取表格中各个下拉框数据
     * return {*}
     * param {*} value
     */
    getArrData(value) {
      let assessListID = "";
      if (value.relieveOneselfContent.intakeOutputKind == "210") {
        let assessListData = this.urinateArr.find(
          (item) => item.id == value.relieveOneselfContent.intakeOutputSettingID
        );
        if (assessListData) {
          assessListID = assessListData.assessListID;
        }
      }
      if (value.relieveOneselfContent.intakeOutputKind == "220") {
        let assessListData = this.fecesArr.find((item) => item.id == value.relieveOneselfContent.intakeOutputSettingID);
        if (assessListData) {
          assessListID = assessListData.assessListID;
        }
      }
      value.colorArr = this.colorArr.filter((item) => item.assessListID == assessListID);
      value.smellArr = this.smellArr.filter((item) => item.assessListID == assessListID);
      value.characteristicArr = this.characteristicArr.filter((item) => item.assessListID == assessListID);
    },
    //获取项目下拉框数据
    async getIOItem(kind, name) {
      let params = {
        kind: kind,
      };
      await GetIOItem(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this[name] = res.data;
          if (this[name].length > 0) {
            this[name] = this[name].filter((item) => !item.code);
            this.assessListIDArr = [...this.assessListIDArr, ...this.getAssessIDArr(this[name])];
          }
        }
      });
    },

    /**
     * description: 获取颜色性状气味下拉框数据
     * return {*}
     * param {*} value
     */
    async getSelectList(value) {
      let param = {
        assessListIDArr: this.assessListIDArr,
      };
      await GetColorByAssessListIDArr(param).then((res) => {
        if (this._common.isSuccess(res)) {
          this.colorArr = res.data;
        }
      });
      // 获取性状
      param.attributeKind = "C";
      await GetOutputAttributeByAssessListIDArr(param).then((res) => {
        if (this._common.isSuccess(res)) {
          this.characteristicArr = res.data;
          if (this.characteristicArr.length) {
            this.characteristicArr.forEach((item) => (item.key = Number(item.key)));
          }
        }
      });
      // 获取气味
      param.attributeKind = "S";
      await GetOutputAttributeByAssessListIDArr(param).then((res) => {
        if (this._common.isSuccess(res)) {
          this.smellArr = res.data;
          if (this.smellArr.length) {
            this.smellArr.forEach((item) => (item.key = Number(item.key)));
          }
        }
      });
    },
    /**
     * description: 获取性状和气味名称
     * return {*}
     * param {*} row
     * param {*} name
     */
    getName(row, name) {
      let arr = name + "Arr";
      let id = row.relieveOneselfContent[name + "ID"];
      let item = row[arr].find((item) => id == item.key);
      if (item) {
        row.relieveOneselfContent[name] = item.value;
      }
    },
    /**
     * description: 保存数据检核
     * return {*}
     * param {*} row
     * param {*} isBatch
     */
    saveDataCheck(row, isBatch) {
      let checkData = row.relieveOneselfContent;
      let tempMessage = row.basicInformation + "-" + row.intakeOutputKindName;
      //检核次数
      if (checkData.intakeOutputTimes && (isNaN(checkData.intakeOutputTimes) || checkData.intakeOutputTimes < 0)) {
        if (isBatch) {
          let tip = tempMessage + "的【次数】必须为数值并且大于等于零";
          if (this.messageData[3].value) {
            this.messageData[3].value += "<br>" + tip;
          } else {
            this.messageData[3].value = tip;
          }
        } else {
          this._showTip("error", "【次数】必须为数值并且大于等于零");
        }
        return false;
      }
      //检核量
      if (checkData.intakeOutputVolume && (isNaN(checkData.intakeOutputVolume) || checkData.intakeOutputVolume < 0)) {
        if (isBatch) {
          let tip = tempMessage + "的【量】必须为数值并且大于等于零";
          if (this.messageData[3].value) {
            this.messageData[3].value += "<br>" + tip;
          } else {
            this.messageData[3].value = tip;
          }
        } else {
          this._showTip("error", "【量】必须为数值并且大于等于零");
        }
        return false;
      }
      //检核执行时间是否大于当前时间
      if (!this.checkTime(checkData.ioDate, checkData.ioTime, isBatch, tempMessage)) {
        return false;
      }
      return true;
    },
    /**
     * description: 大小便保存
     * return {*}
     * param {*} row
     * param {*} isBatch
     */
    async relieveOneselfSave(row, isBatch) {
      let tempMessage = row.basicInformation + "-" + row.intakeOutputKindName;
      let check = this.saveDataCheck(row, isBatch);
      if (check) {
        this.getName(row, "characteristic");
        this.getName(row, "smell");
        let params = row.relieveOneselfContent;
        params.stationID = row.stationID;
        if (row.id) {
          params.id = row.id;
        }
        if (!isBatch) {
          this.loading = true;
        }
        await BatchSave(params).then((res) => {
          if (!isBatch) {
            this.loading = false;
          }
          if (this._common.isSuccess(res)) {
            //新增保存重新获取数据
            if (isBatch) {
              // 批量保存，组装成功消息
              if (this.messageData[1].value) {
                this.messageData[1].value += "、" + tempMessage;
              } else {
                this.messageData[1].value = tempMessage;
              }
            } else {
              this._showTip("success", "保存成功");
              if (!row.id) {
                this.getTabelData();
              }
            }
          } else {
            // 检核失败
            if (isBatch) {
              // 批量保存，组装失败消息
              if (this.messageData[2].value) {
                this.messageData[2].value += "、" + tempMessage;
              } else {
                this.messageData[2].value = tempMessage;
              }
            }
          }
        });
      } else {
        // 检核失败
        if (isBatch) {
          // 批量保存，组装失败消息
          if (this.messageData[2].value) {
            this.messageData[2].value += "、" + tempMessage;
          } else {
            this.messageData[2].value = tempMessage;
          }
        }
      }
    },
    /**
     * description: 批量保存大小便
     * return {*}
     */
    async batchRelieveOneself() {
      let saveRows = this.$refs.ioTable.getCheckboxRecords();
      if (saveRows.length <= 0) {
        this._showTip("warning", "请勾选需要保存的数据！");
        return;
      }
      this.progressFlag = true;
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
      for (let i = 0; i < saveRows.length; i++) {
        await this.relieveOneselfSave(saveRows[i], true);
        this.messageData[0].value = Number((((i + 1) / saveRows.length) * 100).toFixed(0));
      }
    },
    /**
     * description: 清除某行数据
     * return {*}
     * param {*} row
     */
    clearRowData(row) {
      Object.assign(row.relieveOneselfContent, this.clearData);
    },
    /**
     * description: 合并单元格
     * return {*}
     * param {*} row
     * param {*} column
     * param {*} rowIndex
     * param {*} columnIndex
     */
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        const rowSpan = this.rowIndexArr[rowIndex];
        const colSpan = rowSpan > 0 ? 1 : 0;
        return {
          rowspan: rowSpan,
          colspan: colSpan,
        };
      }
    },
    /**
     * description: 组装assessIDArr
     * return {*}
     * param {*} arr
     */
    getAssessIDArr(arr) {
      let idArr = [];
      arr.forEach((item) => {
        idArr.push(item.assessListID);
      });
      return idArr;
    },
    /**
     * description: 设置合并单元格
     * return {*}
     * param {*} value
     */
    setIndex(value) {
      this.rowIndexArr = [];
      for (var i = 0; i < value.length; i++) {
        if (i === 0) {
          this.rowIndexArr.push(1);
          this.rowSpan = 0;
        } else {
          if (value[i].basicInformation === value[i - 1].basicInformation) {
            this.rowIndexArr[this.rowSpan] += 1;
            this.rowIndexArr.push(0);
          } else {
            this.rowIndexArr.push(1);
            this.rowSpan = i;
          }
        }
      }
    },
    /**
     * description: 进度条重置
     * return {*}
     */
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
    },
    /**
     * description: 进度条关闭函数
     * return {*}
     */
    progressClose() {
      this.progressFlag = false;
      // 有一条成功就刷新数据
      if (this.messageData[1].value) {
        this.getTabelData();
      }
      this.renewMessageData();
    },
    /**
     * description: 批量更改执行时间
     * return {*}
     */
    changePerFormTime() {
      if (!this.checkTime(this.formatData.scheduleDate, this.performTime, false)) {
        this.formatData.scheduleDate = this._datetimeUtil.getNowDate();
        this.performTime = this._datetimeUtil.getNowTime();
        return;
      }
      this.tableData.forEach((item) => {
        //已执行返回
        if (item.id) {
          return;
        }
        item.relieveOneselfContent["ioTime"] = this.performTime;
      });
    },
    /**
     * description: 检核时间
     * param {*} performDate 日期
     * param {*} performTime 时间
     * param {*} isSave 是否批量保存
     * param {*} tempMessage 批量保存提示信息
     * return {*}
     */
    checkTime(performDate, performTime, isBatchSave, tempMessage) {
      let nowData = this._datetimeUtil.getNowDate();
      let nowTime = this._datetimeUtil.getNowTime("hh:mm");
      // 对传过来的参数进行格式化
      performDate = this._datetimeUtil.formatDate(performDate, "yyyy-MM-dd");
      performTime = this._datetimeUtil.formatDate(performTime, "hh:mm");
      if (performDate > nowData || (performDate == nowData && performTime > nowTime)) {
        if (isBatchSave) {
          let tip = tempMessage + "的【执行时间】不能大于当前时间";
          if (this.messageData[3].value) {
            this.messageData[3].value += "<br>" + tip;
          } else {
            this.messageData[3].value = tip;
          }
          return false;
        }
        this._showTip("warning", "执行时间不能超过当前时间!");
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="scss">
.batch-relieve {
  height: 100%;
  .batch-relieve-top {
    background-color: #fff;
    padding-left: 0;
    .top-data-pick {
      width: 130px;
    }
    label {
      margin-left: 20px;
    }
    .shift-select {
      width: 80px;
    }
    .shiftTime-select {
      width: 120px;
    }
    .header-right {
      float: right;
      .time-picker {
        width: 80px;
      }
    }
  }
  .batch-relieve-content {
    height: 100%;
    .el-input__prefix {
      display: none;
    }
    .el-input__inner {
      padding-left: 5px;
    }
    .volume-input {
      width: 80px;
      .el-input-group__append {
        padding: 0 5px;
      }
    }
    .relievr-color-picker .show {
      .show-color {
        width: 100%;
      }
      .show-name {
        display: none;
      }
    }
  }
}
</style>
