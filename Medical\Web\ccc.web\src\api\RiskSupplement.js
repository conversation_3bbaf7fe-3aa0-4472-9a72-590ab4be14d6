/*
 * FilePath     : \src\api\RiskSupplement.js
 * Author       : 郭鹏超
 * Date         : 2021-08-14 11:00
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:48
 * Description  :风险补录
 */
import http from "../utils/ajax";
const baseUrl = "/RiskSupplement";

export const urls = {
  ScoreSupplementSave: baseUrl + "/ScoreSupplementSave",
  ScoreSupplementDelete: baseUrl + "/ScoreSupplementDelete"
};
// 风险补录新增
export const ScoreSupplementSave = params => {
  return http.post(urls.ScoreSupplementSave, params);
};
//风险补录删除
export const ScoreSupplementDelete = params => {
  return http.get(urls.ScoreSupplementDelete, params);
};
