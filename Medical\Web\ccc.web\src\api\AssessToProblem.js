/*
 * FilePath     : \src\api\AssessToProblem.js
 * Author       : 李正元
 * Date         : 2020-05-08 15:53
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:43
 * Description  : 评估对应诊断
 */
import http from "../utils/ajax";
const baseUrl = "/AssessToProblem";
export const urls = {
  GetAssessToProblemByProblemID: baseUrl + "/GetAssessToProblemByProblemID",
  UpdateAssessToProblem: baseUrl + "/UpdateAssessToProblem"
};

//2020-05-08因应评估对诊断权重维护新增此方法
export const GetAssessToProblemByProblemID = params => {
  return http.get(urls.GetAssessToProblemByProblemID, params);
};

//2020-05-09因应评估对诊断权重维护新增此方法
export const UpdateAssessToProblem = params => {
  return http.post(urls.UpdateAssessToProblem, params);
};
