<!--
 * FilePath     : \src\autoPages\tube\statisticDischargeTube.vue
 * Author       : 马超
 * Date         : 2023-11-27 15:53
 * LastEditors  : 马超
 * LastEditTime : 2024-04-24 16:25
 * Description  :
 * CodeIterationRecord:
-->
<template>
  <specific-care
    class="patient-statistic-tube"
    v-model="showTemplateFlag"
    :recordTitleSlotFalg="true"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :editFlag="false"
    :careMainAddFlag="false"
    :recordAddFlag="false"
    @maintainAdd="tubeDetailView"
    @getMainFlag="getMainFlag"
    @cancel="drawerClose"
    :mainTableHeight="tableOneRowHeight"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <!-- 标题 -->
    <div slot="record-title">
      <span>出院起始日期：</span>
      <el-date-picker
        v-model="startDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="选择开始日期"
      ></el-date-picker>
      <span>出院结束日期：</span>
      <el-date-picker
        v-model="endDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="选择结束日期"
      ></el-date-picker>
      <span>病区：</span>
      <station-selector v-model="stationID" clearable label="" width="200" :hospitalFlag="true"></station-selector>
      <span>住院号：</span>
      <el-input v-model="chartNo" class="search-input" placeholder="请输入住院号"></el-input>
      <span>导管名称：</span>
      <el-select v-model="tubeID" filterable clearable>
        <el-option v-for="item in tubeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
      </el-select>
      <el-button class="query-button" icon="iconfont icon-search" @click="showRecordArr = [true, false]">
        查询
      </el-button>
    </div>
    <div slot="main-record">
      <!-- 主记录列表 -->
      <el-table
        @row-click="tubeRecordClick"
        :data="tubeRecordList"
        ref="tubeRecordTable"
        border
        stripe
        height="100%"
        row-class-name="main-record-row"
        header-row-class-name="main-record-header-row"
      >
        <el-table-column prop="stationName" label="病区" align="center"></el-table-column>
        <el-table-column prop="chartNo" label="住院号" align="center"></el-table-column>
        <el-table-column prop="patientName" label="姓名" align="center"></el-table-column>
        <el-table-column prop="gender" label="性别" align="center"></el-table-column>
        <el-table-column prop="bedNumber" label="床位" align="center"></el-table-column>
        <el-table-column prop="tubeName" label="导管名称" align="center"></el-table-column>
        <el-table-column prop="tubeRiskLevel" label="风险类别" align="center">
          <template slot-scope="scope">
            <span :style="getLevelStyle(scope.row)">
              {{ scope.row.tubeRiskLevel }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="occuredDate" label="插管日期" align="center"></el-table-column>
        <el-table-column prop="removeDate" label="拔管日期" align="center"></el-table-column>
        <el-table-column prop="tubeUseDay" label="留置天数" align="center"></el-table-column>
        <el-table-column prop="lastMaintenDateTime" label="最后维护日期" align="center"></el-table-column>
      </el-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <packaging-table v-model="tubeCareMainList" :headerList="tubeCareMainColumnList">
        <!-- 评估类型 插槽 -->
        <div slot="assessType" slot-scope="row">
          <span v-if="row.row.recordsCode.indexOf('Start') != -1">开始评估</span>
          <span v-else-if="row.row.recordsCode.indexOf('End') != -1 || row.row.recordsCode.indexOf('UEX') != -1">
            结束评估
          </span>
          <span v-else>例行评估</span>
        </div>
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="row">
          <el-tooltip content="明细查看" placement="top">
            <div class="iconfont icon-preview" @click="tubeDetailView(row.row)"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
      v-if="drawerType == 2"
      class="tube-care-main-add"
    >
      <div slot="header"></div>
      <div>
        <tabs-layout :template-list="templateDatas" ref="tabsLayout" />
      </div>
    </base-layout>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import { GetTubeCare } from "@/api/Tube";
import packagingTable from "@/components/table/index";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { GetPatientTubeRecord, GetTubeDictionary, GetStatisTubeAssessView } from "@/api/statisticsTube";
import { mapGetters } from "vuex";
export default {
  components: {
    specificCare,
    stationSelector,
    tabsLayout,
    baseLayout,
    packagingTable,
  },
  data() {
    return {
      tubeID: undefined,
      tubeList: [],
      endDate: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      startDate: this._datetimeUtil.addDate(this._datetimeUtil.getNow(), -7, "yyyy-MM-dd"),
      stationID: undefined,
      chartNo: undefined,
      loading: false,
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      tableOneRowHeight: undefined,
      tubeRecordList: [],
      currentTubeRecord: undefined,
      tubeCareMainColumnList: [],
      tubeCareMainList: [],
      drawerType: undefined,
      tubeList: [],
      drawerLoading: false,
      drawerLoadingText: undefined,
      templateDatas: [],
    };
  },
  created() {
    this.stationID = this.user.stationID;
    this.getTubeDictionary();
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  methods: {
    /**
     * description: 获取主记录数据
     * param {*} type
     * return {*}
     */
    getTubeRecordList() {
      this.tubeRecordList = [];
      let params = {
        startDate: this.startDate,
        endDate: this.endDate,
        stationID: this.stationID,
        chartNo: this.chartNo,
        tubeID: this.tubeID,
        index: Math.random(),
      };
      this.loading = true;
      GetPatientTubeRecord(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.tubeRecordList = result.data;
          this.loading = false;
        }
      });
    },
    /**
     * description: 获取导管字典
     * param {*}
     * return {*}
     */
    getTubeDictionary() {
      GetTubeDictionary().then((result) => {
        if (this._common.isSuccess(result)) {
          this.tubeList = result.data;
        }
      });
    },
    /**
     * description: 获取维护记录表头
     * param {*}
     * return {*}
     */
    async getTableHeaderList() {
      if (!this.currentTubeRecord) {
        return;
      }
      let params = {
        fileClassID: 11,
        fileClassSub: this.currentTubeRecord.tubeType,
        useDescription: "1||Table",
      };
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tubeCareMainColumnList = res.data;
        }
      });
    },
    /**
     * description: 获取维护记录表格数据
     * param {*}
     * return {*}
     */
    async getTubeCareMainList() {
      if (!this.currentTubeRecord) {
        this._showTip("warning", "获取导管维护记录失败");
        return;
      }
      let params = {
        patientTubeRecordID: this.currentTubeRecord.id,
      };
      this.loading = true;
      await GetTubeCare(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.tubeCareMainList = result.data;
          //字体加粗
          if (this.tubeCareMainList.length) {
            this.tubeCareMainList.forEach((item) => {
              item.extendItem = this.overStrikingText(item.extendItem);
              item.careIntervention = this.overStrikingText(item.careIntervention);
            });
          }
        }
      });
    },
    /**
     * description: 主记录点击
     * param {*} row
     * return {*}
     */
    async tubeRecordClick(row) {
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.$set(this, "tubeRecordList", [row]);
        this.currentTubeRecord = row;

        this.$nextTick(() => {
          this.tableOneRowHeight = this._common.getTableOneRowHeight(
            this.$refs.tubeRecordTable?.$el,
            ".main-record-row",
            ".main-record-header-row"
          );
        });
        await this.getTableHeaderList();
        await this.getTubeCareMainList();
      }
    },
    /**
     * description: 明细记录查看
     * param {*}
     * return {*}
     */
    tubeDetailView(row = undefined) {
      this.drawerType = 2;
      this.openOrCloseDrawer(true, "明细查看");
      this.getAssessView(row);
    },
    //获取主记录
    getMainFlag() {
      this.currentTubeRecord = undefined;
      this.getTubeRecordList();
    },
    //弹窗关闭
    drawerClose() {
      this.openOrCloseDrawer(false);
    },
    //弹窗开关
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 加粗字体
     * param {*} text
     * return {*}
     */
    overStrikingText(text) {
      if (!text) {
        return;
      }
      text = "<b>" + text;
      text = text.replace(/：/g, "：</b>");
      text = text.replace(/；/g, "；<b>");
      return text;
    },
    /**
     * description:获取评估模板
     * param {*} reasonRecordsCode
     * return {*}
     */
    async getAssessView(row) {
      this.templateDatas = [];
      this.drawerLoading = true;
      this.drawerLoadingText = "加载中……";
      let params = {
        inpatientID: row.inpatientID,
        recordsCode: row.recordsCode,
        age: this.currentTubeRecord.age,
        gender: this.currentTubeRecord.gender,
        departmentListID: row.departmentListID,
        dateOfBirth: this.currentTubeRecord.dateOfBirth,
        sourceID: this.currentTubeRecord ? this.currentTubeRecord.id : undefined,
        sourceType: this.currentTubeRecord ? "TubeMaintain" : undefined,
        patientTubeCareMainID: row.patientTubeCareMainID,
      };
      if (this.currentTubeRecord) {
        params.patientTubeRecordID = this.currentTubeRecord.id;
      }
      await GetStatisTubeAssessView(params).then((result) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
     /**
     * description:获取风险级别样式
     * param {*} tube
     * return {*}
     */
     getLevelStyle(tube) {
      return {
        margin: "auto",
        lineHeight: "24px",
        width: "36px",
        color: "#ffffff",
        fontSize: "14px",
        borderRadius: "4px",
        backgroundColor: tube.tubeLevelColor,
      };
    },
  },
};
</script>

<style lang="scss">
.patient-statistic-tube {
  .search-input {
    width: 130px;
  }

  &.specific-care .specific-care-content {
    .main-record .main-record-header {
      .record-title {
        display: inline-block;
        height: 50px;
        line-height: 50px;
        font-size: inherit;
        font-weight: inherit;
      }
    }
  }
}
</style>
