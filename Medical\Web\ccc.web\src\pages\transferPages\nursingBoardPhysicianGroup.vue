<!--
 * FilePath     : \src\pages\transferPages\nursingBoardPhysicianGroup.vue
 * Author       : 郭鹏超
 * Date         : 2021-12-09 08:52
 * LastEditors  : 马超
 * LastEditTime : 2024-11-19 08:50
 * Description  : 值班医师关系维护
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
// 代码需要迁移，暂时串到nursing
import { getNursingBoard } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      user: "getUser",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  created() {
    this.url =
      getNursingBoard() +
      "physicianGroup?token=" +
      this.token +
      "&stationID=" +
      this.user.stationID +
      "&userID=" +
      this.user.userID +
      "&hospitalID=" +
      this.hospitalInfo.hospitalID;
      console.log("nursingBoardPhysicianGroup", this.url);
  },
};
</script>