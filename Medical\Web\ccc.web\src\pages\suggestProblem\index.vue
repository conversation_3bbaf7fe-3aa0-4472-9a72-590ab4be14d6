<!--
 * FilePath     : \src\pages\suggestProblem\index.vue
 * Author       : 石高阳
 * Date         : 2020-10-14 09:48
 * LastEditors  : 苏军志
 * LastEditTime : 2022-06-13 18:46
 * Description  : 诊断审核
-->

<template>
  <base-layout class="suggest-problem">
    <div class="suggest-problem-top" slot="header">
      <span>日期：</span>
      <el-date-picker
        v-model="queryCriteria.startTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 109px"
        placeholder="选择日期"
        :picker-options="pickerOptionscreate"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        v-model="queryCriteria.endTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 109px"
        placeholder="选择日期"
        :picker-options="pickerOptionsend"
      ></el-date-picker>
      &nbsp;
      <span>病区：</span>
      <station-selector
        @change="getStationNurse"
        clearable
        placeholder="请选择"
        v-model="stationList.label"
        label=""
        width="150px"
      >
        <el-option v-for="item in stationList" :key="item.value" :value="item.value" :label="item.label"></el-option>
      </station-selector>
      <nurse-selector v-model="userID" width="110px" :stationID="this.user.stationID"></nurse-selector>
      &nbsp;
      <span>病案号：</span>
      <el-input
        v-model="chartNo"
        placeholder="住院号"
        class="search-input"
        style="width: 125px"
        @keyup.enter.native="selectSuggest"
      >
        <i slot="append" class="iconfont icon-search" @click="selectSuggest"></i>
      </el-input>
      <el-button class="print-button" @click="exportSuggest" icon="iconfont icon-arrow-download">EXCEL</el-button>
    </div>
    <div class="suggest-problem-content" v-loading="loading" element-loading-text="加载中……">
      <el-table :data="suggestData" height="100%" row-key="assessMainID" border stripe>
        <!-- type="expand"展开项表格用 -->
        <el-table-column type="expand">
          <template slot-scope="suggestData">
            <el-table :data="suggestData.row.suggestProblems" border stripe>
              >
              <el-table-column prop="suggestLevel" label="建议等级"></el-table-column>
              <el-table-column prop="nursingProblem" label="护理问题"></el-table-column>
              <el-table-column prop="suggestProblem" label="建议问题"></el-table-column>
              <el-table-column prop="checkProblem" label="勾选问题"></el-table-column>
              <el-table-column prop="nurseAddProblem" label="手动加入问题"></el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column prop="chartNo" label="病案号" min-width="68"></el-table-column>
        <el-table-column prop="patientName" label="病人姓名"></el-table-column>
        <el-table-column prop="admissionDate" label="入院日期" min-width="84"></el-table-column>
        <el-table-column prop="admissionDay" label="入院天数" min-width="60"></el-table-column>
        <el-table-column prop="surgeryDate" label="手术日期" min-width="84"></el-table-column>
        <el-table-column prop="assessDate" label="评估日期" min-width="84"></el-table-column>
        <el-table-column prop="nursingLevel" label="护理级别"></el-table-column>
        <el-table-column prop="diagnosis" label="诊断" min-width="105"></el-table-column>
        <el-table-column prop="numberOfAssessment" label="评估次数"></el-table-column>
        <el-table-column prop="addEmployee" label="评估人员"></el-table-column>
        <el-table-column prop="suggestProblemCount" label="建议数"></el-table-column>
        <el-table-column prop="hightSuggest" label="强推荐数"></el-table-column>
        <el-table-column prop="checkProblemCount" label="勾选数"></el-table-column>
        <el-table-column prop="nurseAddProblemCount" label="加入数"></el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>

<script>
import { mapGetters } from "vuex";
import stationSelector from "@/components/selector/stationSelector";
import nurseSelector from "@/components/selector/nurseSelector";
import BaseLayout from "@/components/BaseLayout";
import { GetNurse } from "@/api/User";
import { GetSuggestProblem } from "@/api/RecordQuality";
import { export_json_to_excel } from "@/vendor/Export2Excel.js";
export default {
  components: {
    BaseLayout,
    nurseSelector,
    stationSelector,
  },
  data() {
    let that = this;
    return {
      queryCriteria: {
        startTime: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
        endTime: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
      },
      pickerOptionscreate: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.queryCriteria.endTime).getTime();
        },
      },
      pickerOptionsend: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.queryCriteria.startTime).getTime() - 8.64e7;
        },
      },
      loading: false,
      //护士id
      userID: "",
      //病区清单
      stationList: [],
      //护士清单
      nurseList: [],
      //病案号
      chartNo: "",
      //建议及勾选问题资料
      suggestData: [],
      //病区序号
      stationID: "",
      //选择到的病区
      selectStation: [],
      //选择到的人
      selectNurse: [],
    };
  },
  created() {
    //取得医院代码
    this.hospitalID = this.hospitalInfo.hospitalID;
    //默许查询起迄日期
    this.queryCriteria.startTime = this._datetimeUtil.getNowDate();
    this.queryCriteria.endTime = this._datetimeUtil.getNowDate();
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  methods: {
    getStationNurse(index) {
      this.nurseList = [];
      this.selectStation = index;
      let params = {
        stationID: index,
      };
      GetNurse(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let datainfo = result.data;
          datainfo.forEach((item) => {
            const RowItem = {
              key: item.userID,
              value: item.userID,
              label: item.userID + "-" + item.name,
            };
            this.nurseList.push(RowItem);
          });
        } else {
          this._showTip("warning", result.message);
        }
      });
    },
    getNurse(index) {
      this.selectNurse = index;
    },
    selectSuggest(index) {
      if (!this.conditionCheck()) {
        return;
      }
      let tempStation = this.selectStation;
      if (this.selectStation.length == 0) {
        tempStation = "";
      }
      let tempNurseID = this.selectNurse;
      if (this.selectNurse.length == 0) {
        tempNurseID = "";
      }
      //清除数据
      let params = {
        startDate: this.queryCriteria.startTime,
        endDate: this.queryCriteria.endTime,
        stationID: this.stationList.label,
        nurseID: this.userID,
        chartNo: this.chartNo,
      };
      GetSuggestProblem(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.suggestData = result.data;
        }
      });
    },
    conditionCheck() {
      if (this.queryCriteria.startTime == null) {
        this._showTip("warning", "起始日期尚未选择");
        return false;
      }
      if (this.queryCriteria.endTime == null) {
        this._showTip("warning", "結束日期尚未选择");
        return false;
      }
      return true;
    },
    exportSuggest(index) {
      let mainData = this.suggestData;
      if (mainData.length == 0) {
        return;
      }
      let detailData = [];
      for (let i = 0; i < mainData.length; i++) {
        for (let j = 0; j < mainData[i].suggestProblems.length; j++) {
          detailData.push(mainData[i].suggestProblems[j]);
        }
        delete mainData[i].suggestProblems;
      }
      const mainHeader = [
        "病案号",
        "病人",
        "入院日期",
        "入院天数",
        "手术日期",
        "评估日期",
        "护理级别",
        "诊断",
        "评估次数",
        "评估人员",
        "建议数",
        "强推荐数",
        "勾选数",
        "加入数",
      ];
      const mainFilterVal = [
        "chartNo",
        "patientName",
        "admissionDate",
        "admissionDay",
        "surgeryDate",
        "assessDate",
        "nursingLevel",
        "diagnosis",
        "numberOfAssessment",
        "addEmployee",
        "suggestProblemCount",
        "hightSuggest",
        "checkProblemCount",
        "nurseAddProblemCount",
      ];

      const detailHeader = ["病案号", "护理级别", "建议等级", "护理问题", "建议问题", "勾选问题", "手动加入问题"];
      const detailFilterVal = [
        "chartNo",
        "nursingLevel",
        "suggestLevel",
        "nursingProblem",
        "suggestProblem",
        "checkProblem",
        "nurseAddProblem",
      ];
      const dataMain = this.formatJson(mainFilterVal, mainData);
      const dataDetail = this.formatJson(detailFilterVal, detailData);
      export_json_to_excel(mainHeader, dataMain, "建议诊断");
      export_json_to_excel(detailHeader, dataDetail, "建议诊断明细");
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
  },
};
</script>

<style lang="scss">
.suggest-problem {
  height: 100%;
  overflow-x: hidden;

  .suggest-problem-top {
    min-width: 970px;
    padding-bottom: 10px;

    .search-input {
      width: 50%;
      .el-input-group__append {
        padding: 0 5px;
      }
      i {
        color: #8cc63e;
      }
    }
    .print-button {
      float: right;
      margin-top: 8px;
    }
  }

  .suggest-problem-content {
    height: 100%;
  }
}
</style>