/*
 * FilePath     : \ccc.web\src\api\ArteriovenousFistula.js
 * Author       : 杨欣欣
 * Date         : 2022-10-08 16:12
 * LastEditors  : 杨欣欣
 * LastEditTime : 2022-10-08 16:14
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientArteriovenousFistula";

export const urls = {
  GetArteriovenousFistulaRecordList:
    baseUrl + "/GetArteriovenousFistulaRecordList",
  GetArteriovenousFistulaCareMainListByRecordID:
    baseUrl + "/GetArteriovenousFistulaCareMainListByRecordID",
  GetArteriovenousFistulaAssessView:
    baseUrl + "/GetArteriovenousFistulaAssessView",
  GetArteriovenousFistulaRecordsCodeInfo:
    baseUrl + "/GetArteriovenousFistulaRecordsCodeInfo",
  GetBodyPartSort: baseUrl + "/GetBodyPartSort",
  AddArteriovenousFistulaRecord: baseUrl + "/AddArteriovenousFistulaRecord",
  AddArteriovenousFistulaCare: baseUrl + "/AddArteriovenousFistulaCare",
  UpdateArteriovenousFistulaRecord:
    baseUrl + "/UpdateArteriovenousFistulaRecord",
  UpdateArteriovenousFistulaCare: baseUrl + "/UpdateArteriovenousFistulaCare",
  DeleteArteriovenousFistulaByID: baseUrl + "/DeleteArteriovenousFistulaByID",
  DeleteArteriovenousFistulaCare: baseUrl + "/DeleteArteriovenousFistulaCare"
};
// 保存主记录
export const AddArteriovenousFistulaRecord = params => {
  return http.post(urls.AddArteriovenousFistulaRecord, params);
};
// 更新主记录
export const UpdateArteriovenousFistulaRecord = params => {
  return http.post(urls.UpdateArteriovenousFistulaRecord, params);
};
// 获取记录列表
export const GetArteriovenousFistulaRecordList = params => {
  return http.get(urls.GetArteriovenousFistulaRecordList, params);
};
// 获取维护记录列表
export const GetArteriovenousFistulaCareMainListByRecordID = params => {
  return http.get(urls.GetArteriovenousFistulaCareMainListByRecordID, params);
};
// 获取部位编号
export const GetBodyPartSort = params => {
  return http.get(urls.GetBodyPartSort, params);
};
// 保存维护记录
export const AddArteriovenousFistulaCare = params => {
  return http.post(urls.AddArteriovenousFistulaCare, params);
};
// 更新维护记录
export const UpdateArteriovenousFistulaCare = params => {
  return http.post(urls.UpdateArteriovenousFistulaCare, params);
};
// 获取评估模板
export const GetArteriovenousFistulaAssessView = param => {
  return http.get(urls.GetArteriovenousFistulaAssessView, param);
};
// 获取ArteriovenousFistula对应的DepartmentToAssessInfo记录
export const GetArteriovenousFistulaRecordsCodeInfo = param => {
  return http.get(urls.GetArteriovenousFistulaRecordsCodeInfo, param);
};
// 删除一条维护记录
export const DeleteArteriovenousFistulaCare = param => {
  return http.post(urls.DeleteArteriovenousFistulaCare, qs.stringify(param));
};

// 删除主记录
export const DeleteArteriovenousFistulaByID = params => {
  return http.post(urls.DeleteArteriovenousFistulaByID, qs.stringify(params));
};
