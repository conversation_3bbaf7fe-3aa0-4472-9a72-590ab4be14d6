<!--
 * FilePath     : \src\autoPages\stationToJobTip\index.vue
 * Author       : 来江禹
 * Date         : 2024-01-04 11:47
 * LastEditors  : 来江禹
 * LastEditTime : 2024-07-12 15:19
 * Description  : 待办任务列表
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="station-job-tip">
    <div class="top" slot="header">
      <span>
        查看全科：
        <el-switch v-model="allPatientFlag" @change="filterByAttendance()"></el-switch>
      </span>
      <el-button class="query-button top-button" icon="iconfont icon-search" @click="getTableData(true)">
        查询
      </el-button>
    </div>
    <el-table
      class="job-tip-table"
      :data="stationToJobTip"
      v-loading="loading"
      element-loading-text="加载中……"
      height="100%"
      ref="jobTipTable"
      border
      stripe
    >
      <el-table-column prop="patientName" label="患者" :width="convertPX(250)"></el-table-column>
      <el-table-column
        v-for="(item, index) in childrenColumn"
        :label="item.label"
        :key="index"
        :min-width="convertPX(200)"
        align="center"
      >
        <template slot-scope="scope">
          <el-tooltip :content="getContent(scope.row[item.prop])" v-if="scope.row[item.prop]">
            <span>{{ scope.row }}</span>
            <i class="iconfont icon-jump-page" @click="jumpPage(scope.row, scope.row[item.prop])"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetStationToJobTipTableHeader, GetStationToJobTipList } from "@/api/JobTip";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      showAssessFlag: false,
      showRiskFlag: false,
      showClusterCareFlag: false,
      childrenColumn: [],
      stationToJobTip: [],
      allPatientFlag: false,
      copyStationToJobTip: [],
      loading: false,
    };
  },
  async created() {
    this.loading = true;
    await this.getTableColumn();
    this.getTableData();
  },
  methods: {
    /**
     * @description: 获取表头内容，工作提醒项目配置
     * @return
     */
    async getTableColumn() {
      await GetStationToJobTipTableHeader().then((res) => {
        if (this._common.isSuccess(res)) {
          this.childrenColumn = res.data;
        }
      });
    },
    /**
     * @description: 获取表格数据
     * @return
     */
    async getTableData(queryFlag = false) {
      if (this.$route.params?.stationJopTipList && !queryFlag) {
        this.loading = false;
        this.stationToJobTip = this.$route.params.stationJopTipList;
        this.copyStationToJobTip = this.$route.params.stationJopTipList;
        this.filterByAttendance();
        return;
      }
      if (queryFlag) {
        this.loading = true;
        this.stationToJobTip = [];
        this.copyStationToJobTip = [];
      }
      let params = {
        stationID: this.user.stationID,
      };
      await GetStationToJobTipList(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.stationToJobTip = res.data;
          this.copyStationToJobTip = res.data;
          this.filterByAttendance();
        }
      });
    },
    /**
     * @description: 跳转页面
     * @param row
     * @param router
     * @return
     */
    jumpPage(row, router) {
      this.setPatientInfo(row.inpatientData);
      this.$router.push({ path: router });
    },
    /**
     * @description: 跳转页面写入缓存患者信息
     * @param patientInfo
     * @return
     */
    setPatientInfo(patientInfo) {
      if (!patientInfo) {
        return;
      }
      let currentPatient = {
        bedNumber: patientInfo.bedNumber,
        inpatientID: patientInfo.inpatientID,
        stationID: patientInfo.stationID,
        caseNumber: patientInfo.caseNumber,
        chartNo: patientInfo.chartNo,
        admissionDate: patientInfo.admissionDate,
        localCaseNumber: patientInfo.localCaseNumber,
        departmentCode: patientInfo.departmentCode,
      };
      this.$store.commit("session/setCurrentPatient", currentPatient);
      this.$store.commit("session/setPatientInfo", undefined);
    },
    /**
     * @description: 过滤派班数据
     * @return
     */
    filterByAttendance() {
      if (!this.allPatientFlag) {
        let filterList = this.copyStationToJobTip.filter((tip) => tip.attendanceNurseID == this.user.userID);
        if (filterList.length <= 0) {
          this.stationToJobTip = [];
          this._showTip("warning", "您尚未进行派班,请查看全科患者！(已重新派班请点击查询按钮)");
          return;
        }
        this.stationToJobTip = filterList;
        this.$nextTick(() => {
          this.$refs.jobTipTable.doLayout();
        });
        return;
      }
      if (this.allPatientFlag) {
        this.stationToJobTip = this.copyStationToJobTip;
        this.$nextTick(() => {
          this.$refs.jobTipTable.doLayout();
        });
        return;
      }
    },
    /**
     * @description: 获取提示内容
     * @param prop
     * @return
     */
    getContent(prop) {
      var content = "跳转编辑";
      switch (prop) {
        case "/assess":
          content = "跳转护理评估";
          break;
        case "/nursingPlan?index=0":
          content = "跳转风险评分";
          break;
        case "/nursingPlan?index=4":
          content = "跳转集束护理";
          break;
        case "/tube":
          content = "跳转专项导管";
          break;
        case "/patientObserve":
          content = "跳转病情观察";
          break;
        case "/orderCheck":
          content = "跳转医嘱审核";
          break;
        default:
          break;
      }
      return content;
    },
  },
};
</script>
<style lang="scss">
.station-job-tip {
  .top {
    .top-button {
      float: right;
      margin-top: 15px;
    }
  }
}
</style>