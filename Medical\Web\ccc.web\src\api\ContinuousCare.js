/*
 * FilePath     : d:\ccc\web\ccc.web\src\api\ContinuousCare.js
 * Author       : 曹恩
 * Date         : 2020-10-22 14:03
 * LastEditors  : 曹恩
 * LastEditTime : 2020-11-18 15:06
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/ContinuousCare";

export const urls = {
  GetDischargePatientsByQuery: baseUrl + "/GetDischargePatientsByQuery",
  GetDischargePatientsEmrFile: baseUrl + "/GetDischargePatientsEmrFile"
};

//查询出院病人
export const GetDischargePatientsByQuery = params => {
  return http.get(urls.GetDischargePatientsByQuery, params);
};
export const GetDischargePatientsEmrFile = params => {
  return http.get(urls.GetDischargePatientsEmrFile, params);
};
