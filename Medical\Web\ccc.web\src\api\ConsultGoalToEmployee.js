/*
 * FilePath     : \ccc.web\src\api\ConsultGoalToEmployee.js
 * Author       : 郭自飞
 * Date         : 2020-10-26 16:29
 * LastEditors  : 郭自飞
 * LastEditTime : 2020-10-27 10:38
 * Description  :会诊人员信息维护
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/ConsultGoalToEmployee";

export const urls = {
  GetByConsultGoalID: baseUrl + "/GetByConsultGoalID",
  DeleteConsultEmployee: baseUrl + "/DeleteConsultEmployee",
  SaveConsultEmployee: baseUrl + "/SaveConsultEmployee"
};

//获取会诊人员信息
export const GetByConsultGoalID = params => {
  return http.get(urls.GetByConsultGoalID, params);
};
//删除会诊人员信息
export const DeleteConsultEmployee = params => {
  return http.post(urls.DeleteConsultEmployee, qs.stringify(params));
};
//保存会诊人员信息
export const SaveConsultEmployee = params => {
  return http.post(urls.SaveConsultEmployee, params);
};
