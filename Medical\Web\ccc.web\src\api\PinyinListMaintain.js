/*
 * FilePath     : \projectManagement.webe:\CCC3.1\Medical\Web\ccc.web\src\api\PinyinListMaintain.js
 * Author       : 郭鹏超
 * Date         : 2020-06-14 15:58
 * LastEditors  : 马超
 * LastEditTime : 2023-04-11 11:46
 * Description  :简拼API
 */
import http from "../utils/ajax";
const baseUrl = "/PinyinListMaintain";
import qs from "qs";

export const urls = {
  GetByListByPinyin: baseUrl + "/GetByListByPinyin",
  GetPinyinIndexs: baseUrl + "/GetPinyinIndexs",
  GetTableNameData: baseUrl + "/GetTableNameData",
  SaveOnePinyinIndexInfo: baseUrl + "/SaveOnePinyinIndexInfo",
  UpDateOnePinyinIndexInfo: baseUrl + "/UpDateOnePinyinIndexInfo",
  DeleteOnePinyinIndexInfo: baseUrl + "/DeleteOnePinyinIndexInfo",
  SetAllObservatePinyin: baseUrl + "/SetAllObservatePinyin"
};
//根据数据表名称获取简拼项目
export const GetByListByPinyin = params => {
  return http.get(urls.GetByListByPinyin, params);
};
//获取简拼数据表的所有内容
export const GetPinyinIndexs = () => {
  return http.get(urls.GetPinyinIndexs);
};
//获取简拼数据表的所有tableName
export const GetTableNameData = () => {
  return http.get(urls.GetTableNameData);
};
//添加一条简拼数据
export const SaveOnePinyinIndexInfo = params => {
  return http.post(urls.SaveOnePinyinIndexInfo, params);
};
//修改一条简拼数据
export const UpDateOnePinyinIndexInfo = params => {
  return http.post(urls.UpDateOnePinyinIndexInfo, params);
};
//删除一条简拼数据
export const DeleteOnePinyinIndexInfo = params => {
  return http.post(urls.DeleteOnePinyinIndexInfo, qs.stringify(params));
};
//写所有简拼数据的中文汉字首字母
export const SetAllObservatePinyin = params => {
  return http.post(urls.SetAllObservatePinyin, qs.stringify(params));
};
