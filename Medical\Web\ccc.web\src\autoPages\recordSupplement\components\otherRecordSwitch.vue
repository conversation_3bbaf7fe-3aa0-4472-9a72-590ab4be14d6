<!--
 * FilePath     : \src\autoPages\recordSupplement\components\otherRecordSwitch.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-17 17:24
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-22 09:46
 * Description  : 
 * CodeIterationRecord: 
 -->
<template>
  <div class="other-record-switch">
    <el-tabs class="tabs" v-model="activeComponentIndex">
      <el-tab-pane
        v-for="({ description }, index) in childComponents"
        :key="index"
        :label="description"
        :name="index.toString()"
      />
    </el-tabs>
    <div class="tabs-content">
      <component
        :is="childComponents[activeComponentIndex].settingValue"
        :patient="patient"
        :patientinfo="patient"
        :supplementPatient="supplemnentPatient"
        refillFlag
      ></component>
    </div>
  </div>
</template>

<script>
import turningRecord from "./turningRecord";
import healthEducationRecord from "./healthEducationRecord";
import giveMedicineRecord from "./giveMedicineRecord";
import monitorItem from "@/pages/recordSupplement/patientNursingRecordDetailSupplement/index";
export default {
  components: {
    turningRecord,
    healthEducationRecord,
    giveMedicineRecord,
    monitorItem,
  },
  props: {
    patient: {
      type: Object,
      default: () => {},
    },
    supplemnentPatient: {
      type: Object,
      default: () => {},
    },
    childComponents: {
      type: Array,
      required: true,
    },
  },
  mounted() {
    console.log("mounted", this.supplemnentPatient);
  },
  data() {
    return {
      activeComponentIndex: 0,
    };
  },
};
</script>

<style lang="scss">
.other-record-switch {
  height: 100%;
  .tabs {
    height: 40px;
  }
  .tabs-content {
    height: calc(100% - 40px);
  }
}
</style>