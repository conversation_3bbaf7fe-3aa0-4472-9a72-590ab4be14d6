<!--
 * FilePath     : \src\autoPages\patientBlood\bloodVerificationRecycle.vue
 * Author       : 来江禹
 * Date         : 2025-04-04 08:36
 * LastEditors  : 来江禹
 * LastEditTime : 2025-06-12 17:25
 * Description  : 血袋回收接收
 * CodeIterationRecord:
 -->
<template>
  <div class="blood-verification-recycle">
    <base-layout v-loading="loading" element-loading-text="加载中……">
      <div class="blood-header" slot="header">
        <span>
          开始时间：
          <el-date-picker
            v-model="startDate"
            type="datetime"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            class="blood-header-time"
            placeholder="请选择开始时间"
            @change="getBloodBagMainList"
          ></el-date-picker>
        </span>
        <span>
          结束时间：
          <el-date-picker
            v-model="endDate"
            type="datetime"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            class="blood-header-time"
            placeholder="请选择结束时间"
            @change="getBloodBagMainList"
          ></el-date-picker>
        </span>
        <label>姓名:</label>
        <el-input v-model="patientName" class="search-inpatient-input" placeholder="请输入姓名">
          <i slot="append" class="iconfont icon-search" @click="getBloodBagMainList"></i>
        </el-input>
        <label>住院号:</label>
        <el-input v-model="localCaseNumber" class="search-inpatient-input" placeholder="请输入住院号">
          <i slot="append" class="iconfont icon-search" @click="getBloodBagMainList"></i>
        </el-input>
        <!-- 扫描功能解决后打开 -->
        <!-- <el-button class="print-button right-button" icon="iconfont icon-blood-recycle" @click="bloodDialog(true)">血袋回收</el-button>
        <el-button class="add-button right-button" icon="iconfont icon-blood-verification" @click="bloodDialog(false)">血袋接收</el-button> -->
      </div>
      <div class="blood-content">
        <el-table height="100%" :data="bloodTableData" border stripe>
          <el-table-column prop="patientName" label="姓名" :width="convertPX(135)" align="center"></el-table-column>
          <el-table-column
            prop="localCaseNumber"
            label="住院号"
            :width="convertPX(160)"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="bloodDonorBagsCode"
            label="血袋码"
            :width="convertPX(160)"
            align="center"
          ></el-table-column>
          <el-table-column prop="sendBloodDate" label="发血时间" :width="convertPX(160)" align="center">
            <template slot-scope="scope">
              <span v-formatTime="{ value: scope.row.sendBloodDate, type: 'dateTime' }"></span>
            </template>
          </el-table-column>
          <el-table-column prop="bloodType" label="血型" :width="convertPX(80)" align="center"></el-table-column>
          <el-table-column
            prop="bloodName"
            label="血液成分"
            :min-width="convertPX(245)"
            align="center"
          ></el-table-column>
          <el-table-column prop="bloodUnit" label="单位" :width="convertPX(65)" align="center"></el-table-column>
          <el-table-column
            prop="stationVerificationEmployeeName1"
            label="核收人1"
            :width="convertPX(90)"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="stationVerificationEmployeeName2"
            label="核收人2"
            :width="convertPX(90)"
            align="center"
          ></el-table-column>
          <el-table-column prop="stationVerificationDate" label="核收时间" :width="convertPX(160)" align="center">
            <template slot-scope="scope">
              <span v-formatTime="{ value: scope.row.stationVerificationDate, type: 'dateTime' }"></span>
            </template>
          </el-table-column>
          <el-table-column
            prop="stationRecycleEmployeeName1"
            label="回收人1"
            :width="convertPX(90)"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="stationRecycleEmployeeName2"
            label="回收人2"
            :width="convertPX(90)"
            align="center"
          ></el-table-column>
          <el-table-column prop="stationRecycleDate" label="回收时间" :width="convertPX(160)" align="center">
            <template slot-scope="scope">
              <span v-formatTime="{ value: scope.row.stationRecycleDate, type: 'dateTime' }"></span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="70" align="center">
            <template slot-scope="scope">
              <el-tooltip content="血袋接收">
                <div
                  :class="['iconfont icon-blood-verification', { hidden: scope.row.stationVerificationDate }]"
                  @click="bloodDialog(false, scope.row)"
                ></div>
              </el-tooltip>
              <el-tooltip content="血袋回收">
                <div
                  :class="['iconfont icon-blood-recycle', { hidden: scope.row.stationRecycleDate }]"
                  @click="bloodDialog(true, scope.row)"
                ></div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-dialog custom-class="blood-dialog" :visible.sync="dialogVisible" :title="dialogTitle">
        <!-- 扫描功能解决后打开 -->
        <!-- <div class="dialog-scanner">
          <label>扫描：</label>
          <el-input class="scanner-input" v-model="rawScanData" type="text" @change="checkBloodStatus" />
        </div> -->
        <el-form
          class="dialog-from"
          :model="dialogBloodData"
          label-position="right"
          label-width="100px"
          v-if="verificationFlag || recycleFlag"
        >
          <el-row :gutter="20">
            <el-col :span="10">
              <el-form-item label="住院号：">
                <span>{{ dialogBloodData.localCaseNumber }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="姓名：">
                <span>{{ dialogBloodData.patientName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10">
              <el-form-item label="性别：">
                <span>{{ dialogBloodData.gender }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="年龄：">
                <span>{{ dialogBloodData.age }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10">
              <el-form-item label="病区：">
                <span>{{ dialogBloodData.stationName }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="科室：">
                <span>{{ dialogBloodData.departmentName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10">
              <el-form-item label="血袋血型：">
                <span>{{ dialogBloodData.bloodDonorABO }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="RH：">
                <span>{{ dialogBloodData.bloodDonorRH }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10">
              <el-form-item label="患者血型：">
                <span>{{ dialogBloodData.patientABO }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="RH：">
                <span>{{ dialogBloodData.patientRH }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="20">
              <el-form-item label="血袋号：">
                <span>{{ dialogBloodData.bloodDonorBagsCode }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="20">
              <el-form-item label="成分名称：">
                <span>{{ dialogBloodData.bloodName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="20">
              <el-form-item label="失效时间：">
                <span v-formatTime="{ value: dialogBloodData.expiryDate, type: 'dateTime' }"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="20">
              <el-form-item label="执行人：">
                <user-selector
                  v-model="dialogBloodData.executeUserID"
                  width="200px"
                  clearable
                  filterable
                  sessionUser
                  remoteSearch
                  label=""
                ></user-selector>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="20">
              <el-form-item label="核对人：" v-if="verificationFlag">
                <user-selector
                  v-model="dialogBloodData.checkUserID"
                  width="200px"
                  clearable
                  filterable
                  remoteSearch
                  label=""
                ></user-selector>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer">
          <el-button type="primary" v-if="verificationFlag" @click="saveBloodVerification">接收血袋</el-button>
          <el-button type="primary" v-if="recycleFlag" @click="saveBloodRecycle">回收血袋</el-button>
        </div>
      </el-dialog>
    </base-layout>
  </div>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import userSelector from "@/components/selector/userSelector";
import { mapGetters } from "vuex";
import {
  GetBloodBagMainList,
  CheckBloodIsReception,
  SaveBloodReception,
  SaveBloodRecycle,
  GetScannedBloodBagMainData,
} from "@/api/PatientTransfusion";
export default {
  components: {
    baseLayout,
    userSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser"
    }),
  },
  data() {
    return {
      loading: false,
      bloodTableData: [],
      //开始日期时间
      startDate: undefined,
      //结束日期时间
      endDate: undefined,
      localCaseNumber: "",
      patientName: "",
      dialogVisible: false,
      dialogTitle: "",
      dialogBloodData: {},
      // 扫描数据
      rawScanData: "",
      verificationFlag: false,
      recycleFlag: false,
      // 是否为血袋回收操作
      isRecycleFlag: false
    };
  },
  mounted() {
    //初始化
    this.init();
  },
  methods: {
    init() {
       if(this.$route && this.$route.query?.caseNumber){
        this.localCaseNumber = this.$route.query?.caseNumber.split(".")[0];
       }
      let nowDate = this._datetimeUtil.getNow("yyyy-MM-dd");
      this.startDate = this._datetimeUtil.addDate(nowDate, -3);
      this.endDate = this._datetimeUtil.addDate(nowDate, 4);
      this.getBloodBagMainList();
    },
    /**
     * @description: 获取血袋信息
     * @return {*}
     */
    async getBloodBagMainList() {
      if (this.startDate > this.endDate) {
        this._showTip("warning", "开始时间不能大于结束时间！");
        return;
      }
      let params = {
        startDate: this.startDate,
        endDate: this.endDate,
        localCaseNumber: this.localCaseNumber,
        patientName: this.patientName,
      };
      await GetBloodBagMainList(params).then((res) => {
        this.bloodTableData = res.data;
      });
    },
    /**
     * @description: 检核血袋信息
     * @return {*}
     */
    checkBloodStatus() {
      const scanData = this.rawScanData;
      if (!scanData || scanData.split(";").length <= 1) {
        return;
      }
      const scanParts = scanData.split(";");
      let params = {
        transfusionInfo: scanData,
      };
      this.rawScanData = scanParts[0];
      CheckBloodIsReception(params).then((result) => {
        if (this._common.isSuccess(result.data)) {
          this.handleBloodStatusResult(result);
        }
      });
    },
    /**
     * @description: 处理血袋状态检查结果
     * @param {Object} result 检查结果
     * @return {void}
     */
    handleBloodStatusResult(result) {
      const { code } = result.data;
      const { dialogTitle } = this;
      switch (code) {
        case "0":
          this._showTip("warning", result.data.errorLog);
          return;
        case "1":
          this.verificationFlag = true;
          return;
        case "2":
          if (dialogTitle === "血袋接收") {
            this._showTip("warning", "血袋已接收");
          } else if (dialogTitle === "血袋回收") {
            this._showTip("warning", "输血未结束");
          }
          return;
        case "3":
          if (dialogTitle === "血袋接收") {
            this._showTip("warning", "血袋已接收");
            return;
          } else if (dialogTitle === "血袋回收") {
            this.recycleFlag = true;
          }
          break;
        case "4":
          this._showTip("warning", "血袋已回收");
          return;
        default:
          break;
      }
      this.dialogBloodData = Object.assign({}, this.dialogBloodData, result.data);
    },
    /**
     * @description: 血袋接收
     * @return {*}
     */
    saveBloodVerification() {
      if (!this.dialogBloodData.executeUserID) {
        this._showTip("warning", "执行人不能为空！");
        return;
      }
      if (!this.dialogBloodData.checkUserID) {
        this._showTip("warning", "核对人不能为空！");
        return;
      }
      if (this.dialogBloodData.executeUserID == this.dialogBloodData.checkUserID) {
        this._showTip("warning", "执行人和核对人不能为同一人！");
        return;
      }
      let params = {
        transfusionId: this.dialogBloodData.transfusionID,
        bloodDonorBagsCode: this.dialogBloodData.bloodDonorBagsCode,
        bloodNumber: this.dialogBloodData.bloodNumber,
        outerBloodCode: this.dialogBloodData.outerBloodCode,
        stationVerificationEmployeeID2: this.dialogBloodData.checkUserID,
        stationVerificationEmployeeID1: this.dialogBloodData.executeUserID,
      };
      SaveBloodReception(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.dialogVisible = false;
          this._showTip("success", "接收成功");
          this.getBloodBagMainList();
        }
      });
    },
    /**
     * @description: 血袋回收
     * @return {*}
     */
    saveBloodRecycle() {
      if (!this.dialogBloodData.executeUserID) {
        this._showTip("warning", "执行人不能为空！");
        return;
      }
      let params = {
        transfusionId: this.dialogBloodData.transfusionID,
        bloodDonorBagsCode: this.dialogBloodData.bloodDonorBagsCode,
        bloodNumber: this.dialogBloodData.bloodNumber,
        outerBloodCode: this.dialogBloodData.outerBloodCode,
        stationRecycleEmployeeID2: this.dialogBloodData.executeUserID,
        stationRecycleEmployeeID1: this.dialogBloodData.executeUserID,
      };
      SaveBloodRecycle(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.dialogVisible = false;
          this._showTip("success", "回收成功");
          this.getBloodBagMainList();
        }
      });
    },
    /**
     * @description: 打开弹窗
     * @param {*} isRecycleFlag 是否是回收
     * @param {*} bloodRow 血袋信息
     * @return {*}
     */
    bloodDialog(isRecycleFlag, bloodRow) {
      if (isRecycleFlag && !bloodRow.stationVerificationDate) {
        this._showTip("warning", "血袋还未接收，不能进行回收");
        return;
      }
      this.isRecycleFlag = isRecycleFlag;
      this.verificationFlag = false;
      this.recycleFlag = false;
      this.dialogBloodData = bloodRow ? bloodRow : {};
      this.rawScanData = undefined;
      this.dialogVisible = true;

      this.dialogTitle = isRecycleFlag ? "血袋回收" : "血袋接收";
      if (bloodRow) {
        this.getScannedBloodBagMainData(bloodRow);
      }
    },
    /**
     * @description: 获取血袋信息详情
     * @param {*} bloodRow 血袋信息
     * @return {*}
     */
    getScannedBloodBagMainData(bloodRow) {
      let params = {
        BloodDonorBagsCode: bloodRow.bloodDonorBagsCode,
        BloodNumber: bloodRow.bloodNumber,
        OuterBloodCode: bloodRow.outerBloodCode,
        InpatientID: bloodRow.inpatientID,
        isRecycleFlag: this.isRecycleFlag,
      };
      GetScannedBloodBagMainData(params).then((resp) => {
        if (this._common.isSuccess(resp) && resp.data) {
          this.handleBloodStatusResult(resp);
          return;
        }
        this._showTip("error", "获取血袋信息详情失败");
      });
    },
  },
};
</script>
<style lang="scss">
.blood-verification-recycle {
  height: 100%;
  .blood-header {
    margin-bottom: 15px;
    .blood-header-time {
      width: 120px;
    }
    .right-button {
      float: right;
      margin-top: 10px;
    }
    .search-inpatient-input {
      width: 160px;
      margin-right: 10px;
      .el-input-group__append {
        padding: 0 5px;
        color: #8cc63e;
      }
    }
  }
  .blood-content {
    height: calc(100% - 60px);
    .icon-blood-recycle {
      color: #14d8d8;
    }
    .icon-blood-verification {
      color: #ff7400;
    }
    .hidden {
      visibility: hidden;
    }
  }
  .blood-dialog {
    width: 800px;
    height: 720px;
    .el-dialog__body {
      width: 100%;
      margin-right: -20px;
    }
    .dialog-scanner {
      margin: 0px 0px 10px 35px;
      font-size: 14px;
      .scanner-input {
        width: 656px;
      }
      .el-input__inner {
        color: #818386;
      }
    }
    .dialog-from {
      margin-left: 35px;
      width: 700px;
      border: 0 solid #dcdfe6;
      border-width: 0 1px 1px;
      .el-row {
        border-top: 1px solid #dcdfe6;
        width: 100%;
        display: flex;
        height: 50px;
        margin-left: 0 !important;
        margin-right: 0 !important;
      }
      .el-col {
        margin-bottom: 0;
        flex: 1;
        padding-left: 0 !important;
        padding-right: 0 !important;
        .el-form-item {
          margin-bottom: 0;
          padding: 10px;
          height: calc(100% - 20px);
          position: relative;
          .el-form-item__label {
            color: #000000;
            background-color: #f1f3f7;
            height: 100%;
            width: 100%;
            padding: 0;
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: right;
            padding-right: 10px;
          }
          .el-form-item__content {
            color: #303133;
            line-height: 32px;
            margin-left: 60px;
            height: 100%;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}
</style>