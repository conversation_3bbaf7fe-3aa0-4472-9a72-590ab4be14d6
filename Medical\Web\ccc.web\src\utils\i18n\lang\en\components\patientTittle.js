/*
 * FilePath     : \src\utils\i18n\lang\en\components\patientTittle.js
 * Author       : 苏军志
 * Date         : 2021-11-11 08:43
 * LastEditors  : 苏军志
 * LastEditTime : 2022-12-10 14:29
 * Description  : 病人头组件语言包
 */
export default {
  patientTittle: {
    refreshDiagnostics: "Refresh Diagnostics",
    diagnosis: "Diagnosis",
    bedNumberTip: "Please enter the bed number!",
    patientName: "Patient Name",
    localCaseNumber: "MRN",
    attendingDoctor: "Attending Physician",
    gender: "Gender",
    admissionTime: "Admission Date",
    chiefComplaint: "Chief Complaint",
    principalNurse: "Incharge Nurse",
    age: "Age",
    birthday: "Birthday",
    nursingLevel: "Nursing Level",
    allergicHistory: "Allergic History",
    widths: {
      firstColumn: 95,
      thirdColumn: 70,
      fifthColumn: 70,
      seventhColumn: 70
    }
  }
};
