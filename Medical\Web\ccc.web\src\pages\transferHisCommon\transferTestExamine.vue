<!--
 * FilePath     : \src\pages\transferHisCommon\transferTestExamine.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-14 14:50
 * Description  : 串检验检查
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { GetTestReportURL } from "@/api/TestExamine";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  watch: {
    patientInfo(newValue) {
      if (newValue) {
        this.init();
      }
    },
  },
  created() {
    if (this.patientInfo) {
      this.init();
    }
    this._sendBroadcast("setPatientSwitch", !this.$route.query?.isDialog);
  },
  methods: {
    init() {
      let params = {
        caseNumber: this.patientInfo.caseNumber,
      };
      GetTestReportURL(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (!result.data) {
            this._showTip("warning", "获取检验数据失败！");
            return;
          }
          this.url = `${result.data}&patientInfo=${JSON.stringify(this.patientInfo)}&token=${this.token}`;
        }
      });
    },
  },
};
</script>
