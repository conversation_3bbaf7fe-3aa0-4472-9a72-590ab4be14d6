<!--
 * FilePath     : \src\autoPages\handover\multipleShiftHandover\components\inShiftHandoff.vue
 * Author       : 郭鹏超
 * Date         : 2023-05-05 11:16
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-06-11 10:49
 * Description  : 班内交班
 * CodeIterationRecord:
-->

<template>
  <base-layout v-loading="loading" :element-loading-text="loadingText" class="in-shift-handoff">
    <div slot="header" class="multiple-in-shift-handoff-header">
      <span>班别日期：</span>
      <el-date-picker
        @change="handoverDateChange"
        v-model="handoverDate"
        :clearable="false"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        class="handoff-date"
      ></el-date-picker>
      <shift-selector
        :width="convertPX(163) + ''"
        :stationID="user.stationID"
        v-model="handoverShiftID"
        @select-item="shiftChange"
      ></shift-selector>
      <span>责任护士：</span>
      <el-select class="handoff-nurse" @change="nurseChange" v-model="nurse">
        <el-option v-for="(item, index) in nurseList" :key="index" :value="item.userID" :label="item.name"></el-option>
      </el-select>
      <span>时间：</span>
      <el-date-picker
        class="handoff-date-time"
        v-model="startDateTime"
        type="datetime"
        :clearable="false"
        format="yyyy-MM-dd HH:mm"
        value-format="yyyy-MM-dd HH:mm"
      ></el-date-picker>
      <span>--</span>
      <el-date-picker
        class="handoff-date-time"
        v-model="endDateTime"
        type="datetime"
        :clearable="false"
        format="yyyy-MM-dd HH:mm"
        value-format="yyyy-MM-dd HH:mm"
      />
      <el-button class="handoff-button" type="primary" icon="iconfont icon-summation" @click="handoverAllSave">
        交班汇总
      </el-button>
    </div>
    <div class="multiple-in-shift-handoff-content">
      <el-table
        ref="handoverTable"
        :data="handoverList"
        height="100%"
        @selection-change="selectionHandover"
        border
        stripe
      >
        <el-table-column type="selection" :width="convertPX(40)" align="center" class-name="select"></el-table-column>
        <el-table-column width="110" label="病人" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.bedNumber + "床" }}</div>
            <div>{{ scope.row.patientName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="S-现状">
          <template slot-scope="scope" v-if="scope.row.situation">
            <div v-html="scope.row.situation"></div>
          </template>
        </el-table-column>
        <el-table-column label="B-背景">
          <template slot-scope="scope">
            <div v-html="scope.row.background"></div>
          </template>
        </el-table-column>
        <el-table-column label="A-评估">
          <template slot-scope="scope">
            <div v-html="scope.row.assement"></div>
          </template>
        </el-table-column>
        <el-table-column label="R-建议">
          <template slot-scope="scope">
            <div v-html="scope.row.recommendation"></div>
          </template>
        </el-table-column>
        <el-table-column label="人体图" :width="convertPX(90)" align="center">
          <template slot-scope="scope">
            <p v-if="scope.row.handoverID">
              <body-image
                :type="'button'"
                :handoverID="scope.row.handoverID"
                :patientName="scope.row.patientName"
              ></body-image>
            </p>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(110)" label="接班护士">
          <template slot-scope="scope">
            <div>{{ scope.row.handonNurseName }}</div>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(110)" align="center" label="编辑">
          <template slot-scope="scope">
            <p v-if="scope.row.handoverID != null">
              <el-tooltip content="修改" v-if="!scope.row.handonNurse">
                <i class="iconfont icon-edit" @click="showHandoverSBAR(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="删除">
                <i class="iconfont icon-del" @click="deleteHandover(scope.row)"></i>
              </el-tooltip>
            </p>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="dialogTitle"
      fullscreen
      v-if="showSbarFlag"
      :visible.sync="showSbarFlag"
    >
      <base-layout>
        <div slot="header">
          <div class="button-area">
            <el-button class="edit-button" icon="iconfont icon-refresh" @click="sbarData = cloneSbarDate">
              还 原
            </el-button>
            <el-button type="primary" icon="iconfont icon-save-button" @click="saveSBAR">保 存</el-button>
          </div>
        </div>
        <handover-rich-text v-model="sbarData" />
      </base-layout>
    </el-dialog>
  </base-layout>
</template>

<script>
import shiftSelector from "@/components/selector/shiftSelector";
import baseLayout from "@/components/BaseLayout";
import progressView from "@/components/progressView";
import HandoverRichText from "@/autoPages/handover/components/HandoverRichText";
import { mapGetters } from "vuex";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetAttendanceNurse } from "@/api/Handover/HandoverCommonUse";
import {
  GetShiftHandoffSBARTableList,
  InShiftHandoff,
  DeleteShiftHandover,
  GetShitHandoverSBAR,
  UpdateShiftSBAR,
} from "@/api/Handover/MultipleShifHandover";
import bodyImage from "@/components/bodyImage";
export default {
  components: {
    baseLayout,
    shiftSelector,
    progressView,
    HandoverRichText,
    bodyImage,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      loading: false,
      loadingText: "",
      handoverDate: undefined,
      handoverShiftID: undefined,
      stationShifts: [],
      nurseList: [],
      nurse: undefined,
      startDateTime: undefined,
      endDateTime: undefined,
      handoverList: [],
      saveHandoverList: [],
      //进度条开关
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      updateHandover: {},
      sbarData: {},
      cloneSbarDate: [],
      showSbarFlag: false,
      dialogTitle: "",
    };
  },
  async beforeMount() {
    await this.getPageSetting();
  },
  methods: {
    /**
     * description: 获取交班记录
     * return {*}
     */
    getHandoverList() {
      if (!this.handoverDate || !this.handoverShiftID || !this.nurse) {
        return;
      }
      let params = {
        recordsCode: "TurnHandover",
        handoverClass: "HandOff",
        shiftDate: this.handoverDate,
        shiftID: this.handoverShiftID,
        nurserID: this.nurse,
        stationID: this.user.stationID,
      };
      this.loading = true;
      this.loadingText = "加载中……";
      GetShiftHandoffSBARTableList(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverList = res.data;
          if (this.handoverList?.length) {
            this.handoverList.forEach((handover) => {
              this.$nextTick(() => {
                !handover.checkResult &&
                  this.$refs.handoverTable?.toggleRowSelection(handover, !handover.handoverID ?? true);
              });
            });
          }
        }
      });
    },
    /**
     * description: 批量保存确认
     * return {*}
     */
    handoverAllSave() {
      if (!this.saveCheck()) {
        return;
      }
      //交班信息确认
      let string = this.getHandoverInfo();
      this.$confirm(string, "提示", {
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        customClass: "multi-handoff-msgbox-class",
      }).then(() => {
        this.multipleSaveHandover();
      });
    },
    /**
     * description: 批量保存
     * return {*}
     */
    async multipleSaveHandover() {
      this.progressFlag = true;
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      for (let i = 0; i < this.saveHandoverList.length; i++) {
        const handover = this.saveHandoverList[i];
        //有接班护士不得重新汇总
        if (handover.handonDate) {
          continue;
        }
        let saveFlag = await this.inShiftHandoff(handover);
        //计算进度条
        let progress = (((i + 1) / this.saveHandoverList.length) * 100).toFixed(0);
        this.messageData[0].value = Number(progress);
        //1:交班成功 2:交班失败
        this.messageData[saveFlag ? 1 : 2].value += " " + handover.bedNumber + "床-" + handover.patientName;
      }
    },
    /**
     * description: 接班保存
     * param {*} handover
     * return {*}
     */
    async inShiftHandoff(handover) {
      let params = {
        shiftDate: this.handoverDate,
        shiftID: this.handoverShiftID,
        inShiftStartDate: this.startDateTime,
        inShiftEndDate: this.endDateTime,
        handoverCommonSaveView: {
          handoverID: handover.handoverID,
          inpatientID: handover.inpatientID,
          handoverNurse: this.nurse,
          recordsCode: "TurnHandover",
          handoverClass: "HandOff",
        },
      };
      let saveFlag = false;
      await InShiftHandoff(params).then((res) => {
        if (this._common.isSuccess(res)) {
          saveFlag = true;
        }
      });
      return saveFlag;
    },
    /**
     * description: 修改SABR初始化
     * param {*} handover
     * return {*}
     */
    showHandoverSBAR(handover) {
      this.updateHandover = handover;
      this.dialogTitle = handover.bedNumber + "床--" + handover.patientName;
      let params = {
        handoverID: handover.handoverID,
        handoverClass: "HandOff",
      };
      GetShitHandoverSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.sbarData = res.data?.sbarData ?? {};
          this.cloneSbarDate = this._common.clone(this.sbarData);
          this.showSbarFlag = true;
        }
      });
    },
    /**
     * description: SABR修改保存
     * return {*}
     */
    saveSBAR() {
      let params = {
        handoverCommonSaveView: {
          handoverID: this.updateHandover.handoverID,
          handoverClass: "HandOff",
          situation: this.sbarData?.situation?.value,
          background: this.sbarData?.background?.value,
          assement: this.sbarData?.assement?.value,
          recommendation: this.sbarData?.recommendation?.value,
          bodyPartImage: this.sbarData?.bodyPartImage?.value,
        },
      };
      UpdateShiftSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.getHandoverList();
          this.showSbarFlag = false;
        }
      });
    },
    /**
     * description: 删除交班记录
     * param {*} handover
     * return {*}
     */
    deleteHandover(handover) {
      let params = {
        handoverID: handover.handoverID,
      };
      this._deleteConfirm("确定要删除此条班内交班记录么？", (flag) => {
        flag &&
          DeleteShiftHandover(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getHandoverList();
            }
          });
      });
    },
    /**
     * description: 进度条关闭函数
     * return {*}
     */
    progressClose() {
      this.progressFlag = false;
      this.getHandoverList();
    },
    /**
     * description: 交班检核
     * return {*}
     */
    saveCheck() {
      if (this.endDateTime < this.startDateTime) {
        this._showTip("warning", "结束时间不得小于开始时间");
        return false;
      }
      if (!this.saveHandoverList.length) {
        this._showTip("warning", "请先勾选汇总患者！");
        return false;
      }
      if (!this.nurse) {
        this._showTip("warning", "请选择交班人！");
        return false;
      }
      return true;
    },
    /**
     * description: 保存确认
     * return {*}
     */
    getHandoverInfo() {
      let sucShit = this.stationShifts.find((shift) => shift.id == this.handoverShiftID);
      let string =
        '<div >交班日期：<span style="color:red"> ' +
        this._datetimeUtil.formatDate(this.handoverDate, "yyyy-MM-dd") +
        "</span> </div>";
      string += '<div >交班班别：<span  style="color:red" >' + sucShit?.shiftName ?? "" + "</span></div> ";
      string += "<div  >确定要执行吗？</div>";
      return string;
    },
    /**
     * description: 获取勾选交班记录
     * param {*} selection
     * return {*}
     */
    selectionHandover(selection) {
      this.saveHandoverList = selection;
    },
    /**
     * description: 交班时间调整
     * return {*}
     */
    async handoverDateChange() {
      this.getInShiftHandoverStartAndEndDateTime();
      await this.getAttendanceNurse();
      await this.getHandoverList();
    },
    /**
     * description: 班别调整
     * return {*}
     */
    async shiftChange() {
      this.getInShiftHandoverStartAndEndDateTime();
      await this.getAttendanceNurse();
      await this.getHandoverList();
    },
    /**
     * description: 护士调整
     * return {*}
     */
    nurseChange() {
      this.getHandoverList();
    },
    /**
     * description: 获取页面配置
     * return {*}
     */
    async getPageSetting() {
      const methodArr = [this.getHandoffShift, this.getAttendanceNurse, this.getHandoverList];
      for (const method of methodArr) {
        await method();
      }
    },
    /**
     * description: 获取当前班别
     * return {*}
     */
    async getHandoffShift() {
      let params = {
        stationID: this.user.stationID,
      };
      await GetNowStationShiftData(params).then((res) => {
        if (this._common.isSuccess(res)) {
          //班别日期和班别初始化
          this.handoverDate = res.data?.shiftDate ?? undefined;
          this.stationShifts = res.data?.stationShifts ?? [];
          this.getInShiftHandoverStartAndEndDateTime();
        }
      });
    },
    /**
     * description: 组装开始和结束时间
     * return {*}
     */
    getInShiftHandoverStartAndEndDateTime() {
      let sucShift = this.stationShifts.find((shift) => shift.id == this.handoverShiftID);

      this.endDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      if (!sucShift) {
        this.startDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      } else {
        this.startDateTime =
          this._datetimeUtil.formatDate(this.handoverDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(sucShift.shiftStartTime, "hh:mm");
      }
    },
    /**
     * description: 获取派班护士
     * return {*}
     */
    async getAttendanceNurse() {
      let params = {
        shiftDate: this.handoverDate,
        stationID: this.user.stationID,
        shiftID: this.handoverShiftID,
      };
      await GetAttendanceNurse(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.nurseList = res.data;
          this.nurse = undefined;
          if (!res.data?.length || !this.user) {
            return;
          }
          if (this.nurseList.find((nurse) => nurse.userID == this.user.userID)) {
            this.nurse = this.user.userID;
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" >
.in-shift-handoff {
  height: 100%;
  .multiple-in-shift-handoff-header {
    .handoff-date {
      width: 163px;
    }
    .handoff-nurse {
      width: 163px;
    }
    .handoff-date-time {
      width: 223px;
    }
    .handoff-button {
      float: right;
      margin-top: 10px;
    }
  }
  .multiple-in-shift-handoff-content {
    height: 100%;
    .el-button--small {
      padding: 0;
      margin: 0;
    }
  }
  .button-area {
    float: right;
  }
}
</style>