/*
 * FilePath     : \src\utils\i18n\lang\zh\common.js
 * Author       : 苏军志
 * Date         : 2021-11-01 11:56
 * LastEditors  : 苏军志
 * LastEditTime : 2022-06-19 16:22
 * Description  : 公共部分
 */
export default {
  // 按钮显示文字
  button: {
    add: "新增",
    modify: "修改",
    query: "查询",
    save: "保存",
    delete: "删除",
    print: "打印",
    edit: "编辑",
    stop: "停止",
    export: "导出",
    cancel: "取消",
    confirm: "确定",
    back: "返回"
  },
  // loading提示文字
  loadingText: {
    load: "加载中……",
    save: "保存中……",
    delete: "删除中……",
    saveSuccess: "保存成功！",
    deleteSuccess: "删除成功！",
    updateSuccess: "更新成功！"
  },
  //tooltip显示文字
  tip: {
    modify: "修改",
    save: "保存",
    delete: "删除",
    print: "打印",
    edit: "编辑",
    stop: "停止",
    export: "导出",
    systemTip: "系统提示",
    dataEmpty: "暂无数据",
    operationAuthority: "非本人不可操作！",
    deleteConfirm: "确定要删除数据吗？"
  },
  // 选择框、输入框、日期时间等组件提示文字
  placeholder: {
    station: "请选择病区",
    shift: "请选择班别",
    date: "请选择日期",
    time: "请选择时间"
  },
  // 标签显示文字
  label: {
    systemName: "全智能护理信息系统",
    shiftDate: "班别日期：",
    shift: "班别：",
    date: "日期：",
    time: "时间：",
    operation: "操作",
    station: "病区："
  }
};
