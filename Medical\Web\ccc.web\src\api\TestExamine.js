import http from "../utils/ajax";
const baseUrl = "/testExamine";

export const urls = {
  // GetTestExamine: baseUrl + "/GetTestExamine",
  // GetTestItems: baseUrl + "/GetTestItems",
  // GetExamItems: baseUrl + "/GetExamItems",
  // GetListReports: baseUrl + "/GetListReports",
  // GetListReportByItem: baseUrl + "/GetListReportByItem",
  // GetLayoutReports: baseUrl + "/GetLayoutReports"
  GetTestReportURL: "/External/GetTestReportURL"
};

// export const GetTestExamine = param => {
//   return http.get(urls.GetTestExamine, param);
// };
// export const GetTestItems = param => {
//   return http.get(urls.GetTestItems, param);
// };
// export const GetExamItems = param => {
//   return http.get(urls.GetExamItems, param);
// };
// export const GetListReports = param => {
//   return http.get(urls.GetListReports, param);
// };
// export const GetListReportByItem = param => {
//   return http.get(urls.GetListReportByItem, param);
// };
// export const GetLayoutReports = param => {
//   return http.get(urls.GetLayoutReports, param);
// };
export const GetTestReportURL = param => {
  return http.get(urls.GetTestReportURL, param);
};
