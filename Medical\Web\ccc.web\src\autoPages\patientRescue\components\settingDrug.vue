<template>
  <div class="rescue-drug">
    <el-table
      border
      :data="drugTableData"
      :header-cell-style="{
        'background-color': '#ffffff',
        border: '0',
      }"
    >
      <el-table-column label="药品" min-width="240">
        <template slot-scope="scope">
          <el-select placeholder="请选择" @change="drugChange(scope.$index)" v-model="scope.row.medicationID">
            <el-option
              v-for="item in drugList"
              :key="item.clinicSettingID"
              :label="item.description"
              :value="item.clinicSettingID"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="剂量(mg)" width="100">
        <template slot-scope="scope">
          <el-input
            @input="numberTest(scope.row, 'medicationDose')"
            v-model="scope.row.medicationDose"
            placeholder="请输入内容"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="溶剂" width="142">
        <template slot-scope="scope">
          <el-select placeholder="请选择" @change="drugChange(scope.$index)" v-model="scope.row.solventID">
            <el-option
              v-for="item in solventList"
              :key="item.clinicSettingID"
              :label="item.description"
              :value="item.clinicSettingID"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="剂量(ml)" width="100">
        <template slot-scope="scope">
          <el-input
            @input="numberTest(scope.row, 'solventDose')"
            v-model="scope.row.solventDose"
            placeholder="请输入内容"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="途径" min-width="160">
        <template slot-scope="scope">
          <el-select placeholder="请选择" @change="drugChange(scope.$index)" v-model="scope.row.routeID">
            <el-option
              v-for="item in channelList"
              :key="item.clinicSettingID"
              :label="item.description"
              :value="item.clinicSettingID"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="速度" width="100">
        <template slot-scope="scope">
          <el-input
            @input="numberTest(scope.row, 'routeSpeed')"
            v-model="scope.row.routeSpeed"
            placeholder="请输入速度"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column width="50" label="操作">
        <template slot-scope="scope">
          <el-tooltip content="删除" v-if="scope.$index !== 0">
            <i class="iconfont icon-del" @click="deleteRow(scope.$index)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div>
      <el-button class="add-button" icon="iconfont icon-add" @click="addRow()"></el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
      required: true,
    },
    channelList: {
      type: Array,
      default: () => [],
    },
    solventList: {
      type: Array,
      default: () => [],
    },
    drugList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    drugTableData: {
      get() {
        return this.value;
      },
      set(val) {
        val.forEach((drugData) => {
          if (drugData.medicationID === 0) {
            drugData.medicationID = "";
            drugData.medicationDose = undefined;
          }
        });
        this.$emit("input", val);
      },
    },
  },
  methods: {
    /**
     * @description: 选项重置
     * @param index 索引
     * @return
     */
    drugChange(index) {
      !this.drugTableData[index].medicationID && (this.drugTableData[index].medicationDose = "");
      !this.drugTableData[index].solventID && (this.drugTableData[index].solventDose = "");
      !this.drugTableData[index].routeID && (this.drugTableData[index].routeSpeed = "");
      this.$emit("input", this.drugTableData);
    },
    /**
     * @description: 文本内容验证
     * @param row 当前行
     * @param name 字段名
     * @return
     */
    numberTest(row, name) {
      if (isNaN(row[name]) || !row[name].trim()) {
        row[name] = undefined;
      }
      this.$emit("input", this.drugTableData);
    },
    /**
     * @description: 删除当前行
     * @param index 索引
     * @return
     */
    deleteRow(index) {
      if (this.drugTableData.length === 1) {
        this._showTip("warning", "至少需要一行使用药物数据！");
        return;
      }
      this.drugTableData.splice(index, 1);
    },
    /**
     * @description: 添加使用药物行
     * @return
     */
    addRow() {
      const params = {
        medicationID: "",
        medicationDose: undefined,
        solventID: "",
        solventDose: undefined,
        routeID: "",
        routeSpeed: undefined,
      };
      this.drugTableData.push(params);
      this.$emit("input", this.drugTableData);
    },
  },
};
</script>
<style lang="scss">
.rescue-drug {
  display: flex;
}
</style>