/*
 * FilePath     : \src\utils\ajax.js
 * Author       : xml
 * Date         : 2019-11-18 07:43
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-08-15 08:36
 * Description  :
 */
import axios from "axios";
import common from "./common";
import { keys } from "@/utils/setting";
import showTip from "@/utils/toast";
import qs from "qs";

axios.defaults.headers.post["Content-Type"] =
  "application/x-www-form-urlencoded;charset=UTF-8";

// 定义取消请求对象
const CancelToken = axios.CancelToken;
// 获取key
const getUrlKey = (config, isRemove) => {
  let url = config.baseURL + config.url;
  let urlKey = url + "||" + config.method;
  if (config.method === "get") {
    if (!isRemove) {
      urlKey = encodeURIComponent(
        urlKey + "||" + JSON.stringify(config.params)
      );
    }
  } else {
    if (!isRemove) {
      urlKey = encodeURIComponent(urlKey + "||" + JSON.stringify(config.data));
    }
  }
  return urlKey;
};
// 根据key获取请求对象
const isExistRequest = (urlKey) => {
  if (urlKey) {
    let request = window._apiRequestList.find((request) => {
      return request.key == urlKey;
    });
    if (request) {
      return true;
    }
  }
  return false;
};

const removeRequest = function (config) {
  // 请求结束把本次请求从请求集合中移除
  let urlKey = getUrlKey(config, true);
  if (urlKey) {
    window._apiRequestList = window._apiRequestList.filter((request) => {
      return request.key.indexOf(urlKey) != -1;
    });
  }
};
const errorCallBackMap = new Map([
  [
    401,
    () => {
      common.session(keys.token, "");
      showTip.showTip("warning", "登陆超时！请重新登录！");
    },
  ],
  [601, () => showTip.showTip("warning", "患者病历已封存，不可修改！")],
  [500, (error) => showTip.showTip("warning", error.response.data.message)],
]);
/**
 * 请求拦截
 */
axios.interceptors.request.use((config) => {
  // 头部设置token
  config.headers[keys.token] = common.session(keys.token);
  //返回前端操作路由地址
  let params = {
    path: common.session("pagePath"),
  };
  //将操作路由放入请求头
  config.headers["operation-parameters"] = JSON.stringify(params);
  // 设置取消机制
  let tempCancel = undefined;
  config.cancelToken = new CancelToken((cancel) => {
    tempCancel = cancel;
  });
  // 判断当前请求是否存在
  let urlKey = getUrlKey(config);
  if (isExistRequest(urlKey)) {
    // 已存在相同请求则把本次请求取消
    tempCancel("repeat||" + decodeURIComponent(urlKey));
  } else {
    // 不存在 则把本次请求添加到请求集合里
    let request = {
      key: urlKey,
      cancel: tempCancel,
    };
    window._apiRequestList.push(request);
  }
  return config;
});
/**
 * 响应拦截
 */
axios.interceptors.response.use(
  (response) => {
    // 增加延迟，相同请求不得在短时间内重复发送
    setTimeout(() => {
      removeRequest(response.config);
    }, 1000);
    return response;
  },
  (error) => {
    // 打印取消请求信息
    if (axios.isCancel(error) && error && error.message) {
      // 目前手动取消请求信息格式：type||url||method||params,type标识取消原因类型
      let info = error.message.split("||");
      if (info && info.length == 4) {
        if (info[0] == "repeat") {
          console.groupCollapsed("拦截到重复请求，取消本次请求");
        } else if (info[0] == "refresh") {
          console.groupCollapsed("页面刷新，取消未完成的请求");
        } else {
          console.groupCollapsed("页面切换，取消未完成的请求");
        }
        console.log("url:", info[1]);
        console.log("method:", info[2]);
        console.log("params:", qs.parse(JSON.parse(info[3])));
        console.log("history:", window._apiRequestList);
        console.groupEnd();
      } else {
        console.log(
          "%c" + error ? error.message : "请求取消",
          "color:#ff0000;"
        );
      }
      return error;
    }
    if (errorCallBackMap.has(error.response?.status)) {
      errorCallBackMap.get(error.response.status)(error);
    }
    // 增加延迟，相同请求不得在短时间内重复发送
    setTimeout(() => {
      removeRequest(error.config);
    }, 1000);
    return error;
  }
);

export default {
  setMedicalApiUrl(url) {
    axios.defaults.baseURL = url;
  },
  get(url, params = {}) {
    return new Promise((resolve, reject) => {
      axios
        .get(url, {
          params,
        })
        .then((res) => {
          if (res) {
            resolve(res.data);
          }
        })
        .catch((error) => {
          showTip.showTip("error", error);
        });
    });
  },
  post(url, params = {}) {
    return new Promise((resolve, reject) => {
      axios
        .post(url, params)
        .then((res) => {
          if (res) {
            resolve(res.data);
          }
        })
        .catch((error) => {
          showTip.showTip("error", error);
        });
    });
  },
};
