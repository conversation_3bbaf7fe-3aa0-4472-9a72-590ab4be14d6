<!--
 * FilePath     : \src\pages\attendance\components\attendanceCard.vue
 * Author       : 孟昭永
 * Date         : 2020-07-18 14:32
 * LastEditors  : 孟昭永
 * LastEditTime : 2023-02-26 11:46
 * Description  : allChecked组件元素全选，getTabelData异动组件数据后刷新，postItemData抛出组件选中的值

--> 

<template>
  <div class="attendance-card">
    <el-card class="box-card" shadow="hover" :body-style="{ padding: '0px' }" @click.native="selectCard(item)">
      <div class="select-card">
        <i v-if="item.criticallyFlag" class="iconfont icon-event-critically">{{ item.criticallyFlag }}</i>
        <el-tooltip placement="top" :content="item.bedNumber + '床'">
          <div
            class="bed"
            v-if="item.nursingLevelColor && item.nursingLevelColor != '#ffffff'"
            :style="'background-color:' + item.nursingLevelColor"
          >
            {{ item.bedNumber + "床" }}
          </div>
        </el-tooltip>
        <el-tooltip placement="top" :content="item.bedNumber + '床'">
          <div
            class="bed"
            v-if="!item.nursingLevelColor || item.nursingLevelColor == '#ffffff'"
            style="background-color: #ffffff; color: #000000; border-bottom: 1px solid #ebeef5"
          >
            {{ item.bedNumber + "床" }}
          </div>
        </el-tooltip>
        <!-- <el-badge value="*" class="inpatient-head-flag"> -->
        <div class="inpatient-head">
          <i v-if="item.genderCode == '1' || item.gender == '1'" class="iconfont icon-male-patients"></i>
          <i v-else-if="item.genderCode == '2' || item.gender == '2'" class="iconfont icon-female-patients"></i>
        </div>
        <!-- </el-badge> -->

        <div
          :class="{
            'inpatient-name': item.patientName.length > 3,
            'inpatient-name-plus': item.patientName.length <= 3,
          }"
        >
          {{ item.patientName }}
        </div>

        <i
          v-if="item.attendanceID"
          class="iconfont icon-del-select-mark"
          @click.stop="delelteAttendancec(item.attendanceID)"
        ></i>
        <i
          v-if="item.inpatientID && !item.attendanceID"
          :class="{ iconfont: true, '': !selectBoole, 'icon-select-mark': selectBoole }"
          @click.stop="selectAttendance(item)"
        ></i>
      </div>
    </el-card>

    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      v-if="this.patientDetailInfo"
      :title="'患者详情-' + patientDetailInfo.stationName"
      :visible.sync="inpatientDialogVisible"
      width="700px"
    >
      <div class="table-info">
        <div class="table-title">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</div>
        <div class="table-content">{{ patientDetailInfo.patientName }}</div>
        <div class="table-title">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别：</div>
        <div class="table-content">{{ patientDetailInfo.gender }}</div>

        <div class="table-title">床&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;号：</div>
        <div class="table-content">{{ patientDetailInfo.bedNumber }}</div>
        <div class="table-title">年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;龄：</div>
        <div class="table-content">{{ patientDetailInfo.ageDetail }}</div>

        <div class="table-title">病&nbsp;&nbsp;案&nbsp;&nbsp;号：</div>
        <div class="table-content">{{ patientDetailInfo.chartNo }}</div>
        <div class="table-title">住院日期：</div>
        <div class="table-content">{{ patientDetailInfo.admissionDate.substring(0, 10) }}</div>

        <div class="table-title">护理级别：</div>
        <div class="table-content">{{ patientDetailInfo.nursingLevel }}</div>
        <div class="table-title">主治医师：</div>
        <div class="table-content">{{ patientDetailInfo.physicianName }}</div>

        <div class="table-title">主责护士：</div>
        <div class="table-content">{{ patientDetailInfo.careNurse1 }}</div>
        <div class="table-title">次责护士：</div>
        <div class="table-content">{{ patientDetailInfo.careNurse2 }}</div>

        <div class="table-row-title">诊断</div>
        <div class="text-info" v-if="patientDetailInfo.diagnosis">{{ patientDetailInfo.diagnosis }}</div>
        <div class="text-info" v-else>&nbsp;</div>

        <div class="table-row-title">主诉</div>
        <div class="text-info" v-if="patientDetailInfo.chiefComplaint">{{ patientDetailInfo.chiefComplaint }}</div>
        <div class="text-info" v-else>&nbsp;</div>

        <div class="table-row-title">护理问题</div>
        <div class="text-info">
          <div class="row-info" v-for="(item, index) in patientDetailInfo.patientProblem" :key="index">
            {{ index + 1 + "." + item }}
          </div>
          <div>&nbsp;</div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="inpatientDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="inpatientDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { ClearCare, GetPatientDetail } from "@/api/Attendance";
export default {
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
      require: true,
    },
    checked: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
      user: "getUser",
    }),
  },
  watch: {
    checked: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue) {
          this.selectBoole = true;
          this.$emit("postItemData", this.item, this.selectBoole);
        } else {
          this.selectBoole = false;
          this.$emit("postItemData", this.item, this.selectBoole);
        }
      },
    },
  },
  data() {
    return {
      //是否选中
      selectBoole: false,
      //患者信息
      patientDetailInfo: undefined,
      //患者信息dialog开关
      inpatientDialogVisible: false,
    };
  },

  methods: {
    selectCard(value) {
      if (value.attendanceID) {
        let params = {};
        params.inpatientID = value.inpatientID;
        params.stationID = value.stationID;
        params.stationShiftID = value.stationShiftID;
        params.attendanceDate = value.attendanceDate;
        params.shift = undefined;
        GetPatientDetail(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.patientDetailInfo = result.data;
          }
        });
        this.inpatientDialogVisible = true;
      } else {
        this.selectBoole = !this.selectBoole;
        this.$emit("postItemData", value, this.selectBoole);
      }
    },
    delelteAttendancec(value) {
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          let ids = [];
          ids.push(value);
          let params = {
            attendanceIDS: ids,
            operatorID: this.user.userID,
          };
          ClearCare(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功");
              this.$emit("getTabelData");
            }
          });
        }
      });
    },
    selectAttendance(item) {
      this.selectBoole = !this.selectBoole;
      this.$emit("postItemData", item, this.selectBoole);
    },
  },
};
</script>

<style lang="scss">
.attendance-card {
  float: left;
  position: relative;
  .box-card {
    width: 80px;
    height: 65px;
    margin: 5px;
    .select-card {
      cursor: pointer;
      .bed {
        text-align: center;
        color: #ffffff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .inpatient-head {
        float: left;
        width: 25px;
        margin-top: 3px;
      }
      .inpatient-head-flag {
        float: left;
      }
      .inpatient-name {
        line-height: 17px;
        float: left;
        width: 50px;
        font-size: 14px;
        // padding-top: 5px;
      }
      .inpatient-name-plus {
        line-height: 16px;
        float: left;
        width: 50px;
        font-size: 14px;
        // padding-top: 3px;
        margin-top: 10px;
      }
    }
    .iconfont.icon-del-select-mark {
      position: absolute;
      font-size: 20px;
      bottom: 6px;
      right: 3px;
    }
    .iconfont.icon-select-mark {
      color: #ff7400;
      position: absolute;
      font-size: 20px;
      bottom: 1px;
      right: 3px;
    }
    .iconfont.icon-event-critically {
      position: absolute;
      font-size: 12px;
      font-weight: bold;
      color: #ff0000;
      width: 15px;
      height: 17px;
      border-radius: 0 0 10px 0;
      background-color: #ffffff;
    }
  }
  .table-info {
    margin-left: 30px;
    font-size: 18px;
    .table-title {
      display: inline-block;
      width: 100px;
      margin-bottom: 5px;
    }
    .table-content {
      display: inline-block;
      width: 200px;
      margin-bottom: 10px;
    }
    .table-row {
      padding: 5px;
      .column-left {
        position: relative;
        font-weight: bold;
        text-align: right;
        width: 110px;
        display: inline-block;
      }
      .column-right {
        position: absolute;
        left: 300px;
        font-weight: bold;
        text-align: right;
        width: 110px;
        display: inline-block;
      }
      .content-right {
        position: absolute;
        left: 410px;
      }
    }
    .table-row-title {
      padding: 10px 0px;
    }
    .text-info {
      width: 600px;
      padding: 10px;
      border: 1px solid Gainsboro;
    }
  }
}
</style>

