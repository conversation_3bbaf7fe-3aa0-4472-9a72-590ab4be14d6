<!--
 * FilePath     : \src\pages\dictionaryMaintain\virtualStation\index.vue
 * Author       : 张现忠
 * Date         : 2020-10-24 11:37
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-16 08:25
 * Description  : 虚拟病区维护
-->
<template>
  <base-layout class="virtual-station">
    <div slot="header" align="left">
      <el-input
        v-model="virtualName"
        placeholder="虚拟病区"
        class="search-input"
        @keyup.enter.native="findVirtualStationList"
      >
        <i slot="append" class="iconfont icon-search" @click="findVirtualStationList"></i>
      </el-input>

      <el-button class="add-button" icon="iconfont icon-add" @click="addVirtualStation('')">新增</el-button>
    </div>

    <el-table :data="virtualStationList" border stripe style="width: 100%" height="100%">
      <el-table-column prop="virtualName" label="虚拟病区" header-align="left"></el-table-column>
      <el-table-column prop="virtualStationCode" label="虚拟病区状态码" align="center"></el-table-column>
      <el-table-column prop="stationName" label="病区" header-align="left"></el-table-column>
      <el-table-column prop="hisStationCode" label="HIS病区状态码" align="center"></el-table-column>
      <el-table-column prop="modifyPersonName" label="修改人员" align="center"></el-table-column>
      <el-table-column label="修改日期" header-align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.modifyDate" v-formatTime="{ value: scope.row.modifyDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <!-- 详情按钮 -->
          <el-tooltip content="查看更多">
            <i class="iconfont icon-more" @click="getVirtualBedList(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="addVirtualStation(scope.row)"></i>
          </el-tooltip>
          <!-- 删除按钮 -->
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteVirtualStationList(scope.row.virtualStationID)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 新增和修改虚拟病区的dialog -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      class="dialog-virtual-station"
      title="虚拟病区数据"
      :visible.sync="addOrModifyStationDialogVisible"
      width="400px"
    >
      <el-form ref="virtualStation" :model="virtualStation" label-width="125px" class="station-form">
        <el-form-item label="真实病区:">
          <el-select
            v-model="virtualStation.stationID"
            filterable
            placeholder="请选择"
            @change="bindStationID"
            style="width: 200px"
          >
            <el-option
              v-for="item in stationList"
              :key="item.id"
              :label="item.stationName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="HIS病区状态码:">
          <el-input v-model="virtualStation.hisStationCode" style="width: 200px" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="虚拟病区:">
          <el-input
            v-model="virtualStation.virtualName"
            placeholder="请输入虚拟病区名！"
            maxlength="10"
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item label="虚拟病区状态码:">
          <el-input
            v-model="virtualStation.virtualStationCode"
            maxlength="10"
            onkeyup="value=value.replace(/[\W]/g,'') "
            onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))"
            placeholder="请输入数字与字母！"
            style="width: 200px"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addOrModifyStationDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="insert">确定</el-button>
      </div>
    </el-dialog>
    <!-- 虚拟床位 -->
    <el-dialog
      title="虚拟床位"
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="virtualBedDialogVisible"
      class="virtual-bed"
    >
      <div class="dialog-virtualBed">
        <el-input v-model="hisBedNumber" class="search-input" placeholder="床位" @keyup.enter.native="findVirtualBed">
          <i slot="append" class="iconfont icon-search" @click="findVirtualBed"></i>
        </el-input>
      </div>
      <div class="virtual-add">
        <el-button class="add-button" icon="iconfont icon-add" @click="addBed()">新增</el-button>
      </div>
      <el-table :data="virtualBedList" border stripe>
        <el-table-column property="hisBedNumber" label="HIS病区床位号" header-align="center"></el-table-column>
        <el-table-column property="virtualStationName" label="虚拟病区名字" header-align="center"></el-table-column>
        <el-table-column property="hisStationCode" label="HIS病区代码" header-align="center"></el-table-column>
        <el-table-column property="modifyPersonName" label="修改人员"></el-table-column>
        <el-table-column label="修改日期" header-align="center">
          <template slot-scope="scope">
            <span
              v-if="scope.row.modifyDate"
              v-formatTime="{ value: scope.row.modifyDate, type: 'yyyy-MM-dd hh:mm' }"
            ></span>
          </template>
        </el-table-column>
        <el-table-column prop="operate" label="操作" align="center" width="50">
          <template slot-scope="scope">
            <!-- 删除按钮 -->
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="deleteVirtualBed(scope.row.hisBedNumber)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        class="dialog-add-virtual-bed"
        width="320px"
        title="虚拟病区床位新增"
        :visible.sync="addBedDialogVisible"
        append-to-body
      >
        <span slot="footer" class="dialog-footer">
          <el-button @click="addBedDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBed">确定</el-button>
        </span>
        <el-form ref="virtualBed" :model="virtualBed" label-width="90px">
          <el-form-item label="HIS床位号:">
            <el-select v-model="virtualBed.hisBedNumber" filterable placeholder="请选择">
              <el-option
                v-for="item in bedList"
                :key="item.id"
                :label="item.bedNumber"
                :value="item.bedNumber"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-dialog>
  </base-layout>
</template>
<script>
import { GetAllVirtualBedList, DeleteVirtualBedList, SaveVirtualBedList } from "@/api/VirtualBed";
import {
  SaveVirtualStation,
  UpdateVirtualStation,
  DeleteVirtualStation,
  GetAllVirtualStation,
} from "@/api/VirtualStation";
import { GetStationList } from "@/api/Station";
import { GetBedListByStationId } from "@/api/BedList";
import baseLayout from "@/components/BaseLayout";
export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      //一条虚拟病区数据传到详情里面的新增和删除
      queryVirtualStation: {},
      //获取详情的stationID
      detailsStationID: "",
      //新增修改病区弹窗显示
      addOrModifyStationDialogVisible: false,
      //虚拟床位弹窗显示
      virtualBedDialogVisible: false,
      //显示新增床位弹框
      addBedDialogVisible: false,
      //新增修改的保存标志
      saveflag: false,
      virtualName: "",
      //his床号
      hisBedNumber: "",
      //病区码
      stationCode: "",
      //虚拟病区
      virtualStation: {
        virtualStationID: "",
        virtualStationCode: "",
        virtualName: "",
        stationID: "",
        stationName: "",
        hisStationCode: "",
        modifyPersonID: "",
        modifyDate: "",
        modifyPersonName: "",
      },
      //病区列表
      stationList: [],
      //床位列表
      bedList: [],
      //虚拟床位
      virtualBed: {
        hisBedNumber: "",
        virtualStationName: "",
        virtualStationID: "",
        hISStationCode: "",
        modifyPersonID: "",
        modifyPersonName: "",
        modifyDate: "",
      },
      //虚拟床位列表
      virtualBedList: [],
      //虚拟病区列表
      virtualStationList: [],
      //备份虚拟病区列表
      backUpVirtualStationList: [],
      //备份虚拟床位列表
      backUpVirtualBedList: [],
    };
  },

  mounted() {
    this.getStationList();
    this.getAllVirtualStationList();
  },
  methods: {
    //获得所有的虚拟病区列表
    getAllVirtualStationList() {
      GetAllVirtualStation().then((response) => {
        if (this._common.isSuccess(response)) {
          this.virtualStationList = response.data;
          this.backUpVirtualStationList = this.virtualStationList;
        }
      });
    },
    getBedList(val) {
      GetBedListByStationId({ stationID: val }).then((response) => {
        if (this._common.isSuccess(response)) {
          this.bedList = response.data ? response.data : [];
          //床位排序
          this.bedList.sort((a, b) => {
            return a.id - b.id;
          });
        }
      });
    },
    //获得真实病区
    getStationList() {
      GetStationList().then((response) => {
        if (this._common.isSuccess(response)) {
          this.stationList = response.data;
        }
      });
    },

    //绑定HISStationCode
    bindStationID(val) {
      var list = this.stationList.filter((station) => {
        return station.id == val;
      });
      if (list) {
        this.virtualStation.hisStationCode = list[0].stationCode;
      }
    },
    //查询虚拟床位详情
    getVirtualBedList(val) {
      this.virtualStation = this._common.clone(val);
      this.virtualBedList = [];
      //防止多次点击未处理
      GetAllVirtualBedList({
        virtualStationID: this.virtualStation.virtualStationID,
      }).then((result) => {
        if (this._common.isSuccess(result)) {
          this.virtualBedList = result.data;
          this.backUpVirtualBedList = this.virtualBedList;
        }
      });
      this.queryVirtualStation = this.virtualStation;
      this.virtualBedDialogVisible = true;
      this.detailsStationID = val.stationID;
    },
    //病区模糊查询
    findVirtualStationList() {
      var list = (this.virtualStationList = this.backUpVirtualStationList.filter((item) => {
        return item.virtualName.indexOf(this.virtualName) != -1;
      }));
    },
    //床位模糊查询
    findVirtualBed() {
      var list = (this.virtualBedList = this.backUpVirtualBedList.filter((item) => {
        return item.hisBedNumber.indexOf(this.hisBedNumber) != -1;
      }));
      this.virtualBedList = list;
    },
    //新增虚拟病区
    addVirtualStation(val) {
      this.addOrModifyStationDialogVisible = true;
      if (!val) {
        //新增
        this.saveflag = true;
        this.virtualStation = {};
      } else {
        //修改
        this.virtualStation = this._common.clone(val);
        this.saveflag = false;
      }
    },
    //新增修改
    insert() {
      if (!this.saveflag) {
        // 有待验证
        return UpdateVirtualStation(this.virtualStation).then((result) => {
          if (this._common.isSuccess(result)) {
            this.getAllVirtualStationList();
            this._showTip("success", "修改成功");
          }
          this.addOrModifyStationDialogVisible = false;
        });
      } else {
        // 有待验证
        return SaveVirtualStation(this.virtualStation).then((result) => {
          if (this._common.isSuccess(result)) {
            this.getAllVirtualStationList();
            this._showTip("success", "新增成功");
          }
          this.addOrModifyStationDialogVisible = false;
        });
      }
    },
    //床位新增
    addBed() {
      this.addBedDialogVisible = true;
      this.virtualBed = {};
      this.getBedList(this.detailsStationID);
    },
    submitBed() {
      this.virtualBed.virtualStationID = this.virtualStation.virtualStationID;
      this.virtualBed.hisStationCode = this.virtualStation.hisStationCode;
      return SaveVirtualBedList(this.virtualBed).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "新增成功");
          this.addBedDialogVisible = false;
          this.getVirtualBedList(this.queryVirtualStation);
        }
      });
    },
    deleteVirtualBed(hisBedNumber) {
      this._deleteConfirm("确认要删除此记录吗？", (flag) => {
        if (flag) {
          let params = {
            HisBedNumber: hisBedNumber,
            VirtualStationID: this.queryVirtualStation.virtualStationID,
          };
          // 添加一个return
          return DeleteVirtualBedList(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this.getVirtualBedList(this.queryVirtualStation);
              this._showTip("success", "删除成功");
            }
          });
        }
      });
    },
    deleteVirtualStationList(virtualStationID) {
      let params = {
        virtualStationID: virtualStationID,
      };
      this._deleteConfirm("确认要删除此记录吗？", (flag) => {
        if (flag) {
          return DeleteVirtualStation(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this.getAllVirtualStationList();
              this._showTip("success", "删除成功");
              this.addOrModifyStationDialogVisible = false;
            }
          });
        }
      });
    },
  },
};
</script>
<style lang="scss">
.virtual-station {
  padding: 10px;
  width: 100%;
  height: 100%;
  .add-button {
    float: right;
    //为了和搜索框对齐
    margin-top: 8px;
  }
  .search-input {
    width: 200px;
    .el-input-group__append {
      padding: 0 5px;
      i {
        color: #8cc63e;
      }
    }
  }
  .dialog-virtual-station {
    .el-dialog {
      height: 420px;
    }
    .station-form {
      margin-top: 10px;
      height: 300px;
    }
  }

  .virtual-bed {
    //限制新增床位的弹出框的最小宽度，不知道原本是如何限制的，待优化
    min-width: 850px;
    .dialog-virtualBed {
      float: left;
      height: 50px;
      line-height: 45px;
    }
    .virtual-add {
      button {
        margin-top: 8px;
        float: right;
      }
    }
  }
}

.dialog-add-virtual-bed {
  .el-dialog {
    height: 40%;
    .el-dialog__footer {
      margin-right: -5px;
    }
  }
}
</style>
