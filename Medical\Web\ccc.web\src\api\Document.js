/*
 * FilePath     : \src\api\Document.js
 * Author       : 李青原
 * Date         : 2020-03-30 08:19
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-13 23:34
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/documentQuery";
const documentUrl = "/Document";
const emrUrl = "/EMR";

export const urls = {
  GetDocumentViews: baseUrl + "/GetPatientDocumentView",
  GetPatientDocument: baseUrl + "/GetPatientDocument",
  GetPatientDocumentByID: baseUrl + "/GetPatientDocumentByID",
  DelPatientFile: baseUrl + "/DelPatientFile",
  GetPatientDocuments: baseUrl + "/GetPatientDocuments",
  GetShowDataByChartNo: baseUrl + "/GetShowDataByChartNo",
  GetNursingFilePrintPermissions: baseUrl + "/GetNursingFilePrintPermissions",
  //依据病历码取得住院病人病历
  GetPatientRecord: baseUrl + "/GetPatientRecord",
  GetNurseHandover: documentUrl + "/GetNurseHandover",
  GetPatientHandover: documentUrl + "/GetPatientHandover",
  GetDischargeAssessPDF: documentUrl + "/GetDischargeAssessPDF",
  GetDischargePlan: documentUrl + "/GetDischargePlan",
  GetNotificationDocument: documentUrl + "/GetNotificationDocument",
  CreateNurseEMRMain: emrUrl + "/CreateNurseEMRMain",
  PrintHandoverSummaryPDF: documentUrl + "/PrintHandoverSummaryPDF",
  GetHisDocumentUrl: baseUrl + "/GetHisDocumentUrl",
  GetSignDocument: documentUrl + "/GetSignDocument",
  //上传签名文件
  UploadSignedDocument: baseUrl + "/UploadSignedDocument",
  GetBatchMonitoringPDF: documentUrl + "/GetBatchMonitoringPDF",
};
// 获取病人文档视图集合  参数：病人ID
export const GetDocumentViews = (params) => {
  return http.get(urls.GetDocumentViews, params);
};

//获取病人文档  参数：文档ID
export const GetPatientDocument = (params) => {
  return http.get(urls.GetPatientDocument, params);
};

export const GetPatientDocumentByID = (params) => {
  return http.get(urls.GetPatientDocumentByID, params);
};

//删除病人文档  参数：文档ID
export const DelPatientFile = (params) => {
  return http.post(urls.DelPatientFile, params);
};

//获取病人文档  参数：多组文档ID
export const GetPatientDocuments = (params) => {
  return http.get(urls.GetPatientDocuments, params);
};

//获取显示数据
export const GetShowDataByChartNo = (params) => {
  return http.get(urls.GetShowDataByChartNo, params);
};

//获取文件打印权限
export const GetNursingFilePrintPermissions = () => {
  return http.get(urls.GetNursingFilePrintPermissions);
};

//依据病历码取得住院病人病历
export const GetPatientRecord = (params) => {
  return http.get(urls.GetPatientRecord, params);
};

//根据病区获取病区交班记录单
export const GetNurseHandover = (params) => {
  return http.get(urls.GetNurseHandover, params);
};

//根据病人获取病区交班记录单
export const GetPatientHandover = (params) => {
  return http.get(urls.GetPatientHandover, params);
};

//获取出院PDF
export const GetDischargeAssessPDF = (params) => {
  return http.get(urls.GetDischargeAssessPDF, params);
};

//获取出院小结
export const GetDischargePlan = (params) => {
  return http.get(urls.GetDischargePlan, params);
};

//获取告知单
export const GetNotificationDocument = (params) => {
  return http.get(urls.GetNotificationDocument, params);
};

//生成电子病历
export const CreateNurseEMRMain = (params) => {
  return http.get(urls.CreateNurseEMRMain, params);
};

//打印交班报告单
export const PrintHandoverSummaryPDF = (params) => {
  return http.post(urls.PrintHandoverSummaryPDF, params);
};

//获取HIS的电子病历地址
export const GetHisDocumentUrl = (params) => {
  return http.get(urls.GetHisDocumentUrl, params);
};

//获取签名文档
export const GetSignDocument = (params) => {
  return http.post(urls.GetSignDocument, params);
};
//上传签名告知书文档
export const UploadSignedDocument = (params) => {
  return http.post(urls.UploadSignedDocument, params);
};
// 获取批量监测排程表
export const GetBatchMonitoringPDF = (params) => {
  return http.get(urls.GetBatchMonitoringPDF, params);
};
