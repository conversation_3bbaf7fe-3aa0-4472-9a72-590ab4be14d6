<template>
  <div class="employee-info">
    {{ label }}
    <input type="text" class="employee-id" v-model="employeeID" :style="inputStyle" @keyup="setTimeout" :disabled = "disabled"/>
    <div :style="nameStyle" :class="['name', { error: !flag }]">
      {{ flag ? nursingInfo.name : message }}
    </div>
  </div>
</template>

<script>
import { GetUserInfo } from "@/api/User";
var timeout;
export default {
  // input 宽度 高度自定义
  props: {
    width: { type: String, default: "100px" },
    height: { type: Number, default: 20 },
    fontSize: { type: Number, default: 14 },
    value: { type: String, default: "" },
    label: { type: String, default: "" },
    disabled: { type: Boolean, default: false },
  },
  computed: {
    inputStyle() {
      return {
        display: "inline-block",
        height: this._convertUtil.getHeigt(this.width, true) + "px",
        width: this._convertUtil.getHeigt(this.width, true),
        padding: "3px 14px",
        fontSize: this.fontSize + "px",
      };
    },
    nameStyle() {
      return {
        height: this._convertUtil.getHeigt(this.width, true) + 8 + "px",
        lineHeight: this._convertUtil.getHeigt(this.width, true) + 8 + "px",
        fontSize: this.fontSize + "px",
      };
    },
  },
  data() {
    return {
      employeeID: "",
      nursingInfo: {},
      regExp: /^[^\u4e00-\u9fa5]{0,}$/,
      flag: false,
      message: "请输入工号！",
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        this.employeeID = newValue;
        if (newValue) {
          this.getNursingInfo();
        } else {
          this.flag = false;
          this.message = "请输入工号";
          this.nursingInfo = {};
        }
      },
    },
  },
  methods: {
    //根据工号显示姓名;
    getNursingInfo() {
      if (!this.employeeID) {
        this.flag = false;
        this.message = "请输入工号";
        this.$emit("input", undefined);
        this.$emit("patientInfo", undefined);
        return;
      }

      if (!this.regExp.test(this.employeeID)) {
        this.flag = false;
        this.message = "不能包括中文！";
        // this.$emit("input", undefined);
        this.$emit("patientInfo", undefined);
        return;
      }

      let params = { UserId: this.employeeID };
      GetUserInfo(params).then((response) => {
        if (response.code && response.data.length > 0) {
          this.nursingInfo = response.data[0];
          this.flag = true;
          this.$emit("patientInfo", this.nursingInfo);
        } else {
          this.flag = false;
          this.$emit("input", undefined);
          this.$emit("patientInfo", undefined);
          this.message = "未查询到此工号！";
        }
      });
    },
    setTimeout() {
      let _this = this;
      // 先清除上一个定时器
      clearTimeout(timeout);
      timeout = setTimeout(function () {
        _this.$emit("input", _this.employeeID);
      }, 1500);
    },
  },
};
</script>

<style>
.employee-info {
  display: inline-block;
}
.employee-info .employee-id {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}
.employee-info .employee-id:focus {
  outline: none;
  border-color: #4abfa2;
}
.employee-info .name {
  display: inline-block;
  padding-left: 6px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.employee-info .error {
  color: red;
}
</style>
