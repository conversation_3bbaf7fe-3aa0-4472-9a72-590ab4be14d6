<!--
 * FilePath     : \ccc.web\src\pages\patientAllergy\index.vue
 * Author       : 孟昭永
 * Date         : 2022-03-03 17:36
 * LastEditors  : 郭鹏超
 * LastEditTime : 2022-10-14 14:38
 * Description  : 
-->
<template>
  <div class="allergy-skin-tabs">
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="(component, index) in components"
        :key="index"
        :label="component.label"
        :name="component.name"
      >
        <component v-if="patient && activeName == component.name" :is="component.name" :patient="patient"></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
import allergy from "@/pages/patientAllergy/components/allergy";
import skinTest from "@/pages/patientAllergy/components/skinTest";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";

export default {
  components: {
    baseLayout,
    allergy,
    skinTest,
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
    }),
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      typeIndex: "1",
      componentArr: [
        {
          index: 0,
          label: "过敏史",
          name: "allergy",
        },
        {
          index: 1,
          label: "皮试记录",
          name: "skinTest",
        },
      ],
      components: [],
      activeName: "allergy",
      patient: undefined,
      skinTestShowFlag: false,
    };
  },
  watch: {
    "patientInfo.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.patient.supplemnentFlag = undefined;
        }
      },
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.patient.supplemnentFlag = "*";
        }
      },
    },
  },
  beforeMount() {
    //过敏页签放入
    this.components = [this.componentArr[0]];
    let param = {
      SettingTypeCode: "SkinTestShowFlag",
    };
    GetSettingSwitchByTypeCode(param).then((response) => {
      if (this._common.isSuccess(response)) {
        //皮试页签放入  response.data为false &&后边不执行
        response.data && (this.components = [...this.components, this.componentArr[1]]);
      }
    });
  },
};
</script>
<style lang="scss">
.allergy-skin-tabs {
  height: 100%;
  .el-tabs {
    height: 100%;
    .el-tabs__content {
      height: calc(100% - 50px);
      .el-tab-pane {
        height: 100%;
      }
    }
  }
}
</style>