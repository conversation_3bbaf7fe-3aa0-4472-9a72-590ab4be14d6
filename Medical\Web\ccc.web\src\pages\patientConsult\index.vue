<!--
 * FilePath     : \src\pages\patientConsult\index.vue
 * Author       : 郭自飞
 * Date         : 2020-01-14 14:16
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-16 08:37
 * Description  : 会诊发起
 * CodeIterationRecord: 2022-05-03 2572 新增会诊发起评价
-->
<template>
  <base-layout show-header class="patient-consult">
    <div slot="header" class="consult-header">
      <span>病区:</span>
      <el-select v-model="stationID" placeholder="请选择" @change="clickSearch">
        <el-option
          v-for="item in inpatientStaionList"
          :key="item.id"
          :label="item.stationName"
          :value="item.id"
        ></el-option>
      </el-select>
      <div class="header-btn">
        <el-button class="add-button" icon="iconfont icon-add" @click="suplementaryDate('')">新增</el-button>
      </div>
    </div>
    <el-table
      height="100%"
      :data="consultList"
      v-loading="loading"
      border
      stripe
      row-key="patientConsultID"
      :expand-row-keys="expands"
      @expand-change="loadData"
      ref="patientConsultTable"
    >
      >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <el-table :data="scope.row.children" height="100%" stripe border class="nesting-table">
            <el-table-column
              prop="mainContent"
              label="目的"
              header-align="center"
              min-width="100"
              align="left"
            ></el-table-column>
            <el-table-column prop="replyStationNmae" label="回复病区" min-width="85" align="center"></el-table-column>
            <el-table-column label="回复日期" width="120" align="center">
              <template slot-scope="scope">
                <span
                  v-formatTime="{
                    value: scope.row.replyDate,
                    type: 'dateTime',
                  }"
                ></span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="consultAssignSationList.ConsultAssignFlag"
              prop="assignEmployeeName"
              label="指派人员"
              min-width="80"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="consultedEmployeeName"
              label="会诊人"
              min-width="80"
              align="center"
            ></el-table-column>
            <el-table-column label="状态" min-width="60" align="center">
              <template slot-scope="scope">
                <span
                  v-if="scope.row.replyStationNmae || consultAssignSationList.ConsultAssignFlag"
                  :class="{
                    'no-reply': scope.row.consultState == '未指派' || scope.row.consultState == '已申请,待会诊',
                  }"
                >
                  {{ scope.row.consultState }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="70" header-align="center" align="left">
              <template slot-scope="consultOperation">
                <el-tooltip content="回复">
                  <i class="iconfont icon-reply" @click="replyDialog(consultOperation.row)"></i>
                </el-tooltip>
                <el-tooltip content="修改">
                  <i class="iconfont icon-edit" @click="suplementaryDate(consultOperation.row)"></i>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="consultStationNmae"
        label="发起病区"
        min-width="70"
        align="left"
      ></el-table-column>
      <el-table-column prop="consultDate" label="发起日期" width="90" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.consultDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="consultEmployeeName" label="发起人员" min-width="70" align="center"></el-table-column>
      <el-table-column header-align="center" label="目的" min-width="100" align="left">
        <template slot-scope="scope">
          <span>{{ scope.row.mainContent + "-" + scope.row.detailContent }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="replyStationNmae" label="回复病区" min-width="70" align="left"></el-table-column>
      <el-table-column label="回复日期" width="90" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.replyDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="replyEmployeeName" label="回复人员" width="70" align="center"></el-table-column>
      <el-table-column prop="replyContent" label="回复内容" min-width="410" align="left"></el-table-column>
      <el-table-column
        v-if="consultAssignSationList.ConsultAssignFlag"
        prop="assignEmployeeName"
        label="指派人员"
        width="70"
        align="center"
      ></el-table-column>
      <el-table-column prop="consultedEmployeeName" label="会诊人" width="70" align="center"></el-table-column>
      <el-table-column prop="emergencyName" label="类别" width="60" align="center"></el-table-column>
      <el-table-column label="状态" min-width="60" align="center">
        <template slot-scope="scope">
          <span
            :class="{
              'no-reply': scope.row.consultState === '未指派' || scope.row.consultState === '已申请,待会诊',
            }"
          >
            {{ scope.row.consultState }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="90" header-align="center" align="left" fixed="right">
        <template slot-scope="consultOperation">
          <div class="opt">
            <el-tooltip v-if="!consultOperation.row.hasChildren" content="修改">
              <i class="iconfont icon-edit" @click="suplementaryDate(consultOperation.row)"></i>
            </el-tooltip>
          </div>
          <div class="opt">
            <el-tooltip content="评价">
              <i class="iconfont icon-record-evaluate" @click="getConsultAssessView(consultOperation.row)"></i>
            </el-tooltip>
          </div>
          <div class="opt">
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="delectConsult(consultOperation.row)"></i>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="send-consult">
      <el-dialog
        :title="consultTittle"
        :visible.sync="dialogFormVisible"
        width="800px"
        v-dialogDrag
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        v-loading="dialogLoading"
        element-loading-text="请稍等……"
      >
        <div class="dag-div" v-if="!multidisciplinaryFlag">
          <span>目的：</span>
          <el-select
            class="consult-margin"
            v-model="consultGoal.consultMainID"
            placeholder="请选择"
            @change="getConsultGoalById"
          >
            <el-option
              v-for="item in consultGoalOne"
              :key="item.consultGoalID"
              :label="item.content"
              :value="item.consultGoalID"
            ></el-option>
          </el-select>
          <el-select v-model="consultDetailID" placeholder="请选择" @change="getGoalEmployee">
            <el-option
              v-for="item in consultGoalTwo"
              :key="item.consultGoalID"
              :label="item.content"
              :value="item.consultGoalID"
            ></el-option>
          </el-select>
          <span v-if="!consultAssignSationList.ConsultAssignFlag">人员：</span>
          <el-select
            v-if="!consultAssignSationList.ConsultAssignFlag"
            v-model="consultGoal.consultedEmployeeID"
            @change="getGoalPhoneNumber"
          >
            <el-option
              v-for="item in consultGoalEmployeeLsit"
              :key="item.employeeID"
              :label="item.employeeName"
              :value="item.employeeID"
            ></el-option>
          </el-select>
        </div>
        <div class="dag-div">
          <span v-if="consultAssignSationList.ConsultAssignFlag">科室：</span>
          <el-select
            class="consult-margin"
            v-if="consultAssignSationList.ConsultAssignFlag"
            v-model="consultAssignSationID"
          >
            <el-option
              v-for="item in consultAssignSationList.stationList"
              :key="item.id"
              :label="item.stationName"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
        <div class="dag-div">
          <span>时间：</span>
          <el-date-picker
            class="consult-margin"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            v-model="consultDate"
            type="datetime"
            placeholder="选择日期时间"
          ></el-date-picker>
        </div>
        <div
          class="multidisciplinary-div"
          v-show="multidisciplinaryFlag"
          v-for="(template, index) in consultGoalList"
          :key="index"
        >
          <span>目的：</span>
          <el-select
            v-model="template.consultMainID"
            placeholder="请选择"
            @change="getMultidisciplinaryConsultGoalById(index)"
            class="consult-margin"
          >
            <el-option
              v-for="item in consultGoalOne"
              :key="item.consultGoalID"
              :label="item.content"
              :value="item.consultGoalID"
            ></el-option>
          </el-select>
          <el-select
            v-model="template.consultDetailID"
            placeholder="请选择"
            @change="getmultidisciplinaryGoalEmployee(index)"
          >
            <el-option
              v-for="item in template.consultGoalTwoList"
              :key="item.consultGoalID"
              :label="item.content"
              :value="item.consultGoalID"
            ></el-option>
          </el-select>
          <span v-if="!consultAssignSationList.ConsultAssignFlag">人员：</span>
          <el-select
            v-if="!consultAssignSationList.ConsultAssignFlag"
            v-model="template.goalEmployee"
            @change="changeConsultGoalJudge"
          >
            <el-option
              v-for="item in template.goalEmployeeList"
              :key="item.employeeID"
              :label="item.employeeName"
              :value="item.employeeID"
            ></el-option>
          </el-select>
          <span v-if="consultAssignSationList.ConsultAssignFlag">科室：</span>
          <el-select
            v-if="consultAssignSationList.ConsultAssignFlag"
            v-model="template.stationID"
            @change="changeConsultGoalJudge"
          >
            <el-option
              v-for="item in template.consultAssignSationList"
              :key="item.id"
              :label="item.stationName"
              :value="item.id"
            ></el-option>
          </el-select>
          <el-button
            v-if="index == 0"
            class="add-button"
            icon="iconfont icon-add"
            @click="multidisciplinaryConsult()"
          ></el-button>
          <i v-if="index >= 2" class="iconfont icon-del" @click="cancelColumn(index)"></i>
        </div>
        <div class="tip">
          <span>发起内容：</span>
          <limit-text-box
            class="consult-input"
            v-model="consultContent"
            :maxLength="800"
            :rows="10"
            width="545"
          ></limit-text-box>
        </div>
        <div class="phone-number">
          <span v-show="!consultAssignSationList.ConsultAssignFlag" class="phone-number-span">
            会诊人联系方式：{{ ConsultPhoneNumber }}
          </span>
          <el-checkbox class="emergency" v-if="!multidisciplinaryFlag" v-model="consultGoal.emergencyFlag">
            急会诊标记
          </el-checkbox>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button v-if="checkResult" type="primary" @click="saveConsult">确定</el-button>
        </div>
      </el-dialog>
    </div>
    <div class="reply">
      <el-dialog
        title="回复内容"
        :visible.sync="dialogTableVisible"
        width="600px"
        v-loading="dialogLoading"
        v-dialogDrag
        :close-on-click-modal="false"
        element-loading-text="请稍等……"
      >
        <el-form :model="replyData">
          <el-form-item label="目的:" label-width="75px">
            <el-input v-model="replyData.detailContent" :readonly="true"></el-input>
          </el-form-item>
          <el-form-item label="发起内容:" label-width="75px">
            <el-input
              type="textarea"
              v-model="replyData.consultContent"
              :autosize="{ minRows: 3, maxRows: 6 }"
              resize="none"
              :readonly="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="回复内容:" label-width="75px">
            <limit-text-box
              class="consult-input"
              v-model="replyContent"
              :maxLength="666"
              :rows="10"
              width="475"
            ></limit-text-box>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="reply()">回 复</el-button>
        </div>
      </el-dialog>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="会诊评价"
      :visible.sync="dialogVisible"
      v-loading="dialogLoading"
      element-loading-text="加载中……"
    >
      <div class="date-pick-div">
        <span>评价时间：</span>
        <el-date-picker
          v-model="evaluateDate"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="选择日期时间"
        ></el-date-picker>
      </div>
      <tabs-layout ref="tabsLayout" :template-list="consultAssessTemplate" @change-values="changeValues"></tabs-layout>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConsultEvaluate">确定</el-button>
      </span>
    </el-dialog>
  </base-layout>
</template>
<script>
import { GetStationList } from "@/api/Station";
import stationSelector from "@/components/selector/stationSelector";
import {
  GetPatientConsult,
  ReplyConsult,
  GetConsultGoal,
  GetConsultGoalById,
  GetConsultGoalEmployee,
  SaveConsult,
  UpdateConsult,
  DelectConsultByID,
  GetConsultAssign,
  SaveMultidisciplinaryCousult,
  GetConsultAssessView,
  SaveConsultDetail,
  GetConsultRecord,
} from "@/api/PatientConsult";
import { GetUserInfo } from "@/api/User";
import baseLayout from "@/components/BaseLayout";
import tabsLayout from "@/components/tabsLayout/index";
import LimitTextBox from "@/components/LimitTextBox";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    tabsLayout,
    stationSelector,
    LimitTextBox,
  },
  props: {
    supplementPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      //科别
      optionsOfDepartmentList: [],
      consultList: [],
      dialogTableVisible: false,
      replyData: {},
      replyContent: "",
      dialogLoading: false,
      dialogFormVisible: false,
      consultGoalOne: [],
      consultGoalTwo: [],
      ConsultPhoneNumber: "",
      loading: false,
      save: "",
      contextNumber: 0,
      stationID: undefined,
      consultDetailID: undefined,
      consultContent: "",
      consultGoal: {
        consultMainID: undefined,
        emergencyFlag: false,
        consultedEmployeeID: undefined,
      },
      multidisciplinaryFlag: false,
      consultGoalList: [],
      consultGoalEmployeeLsit: [],
      inpatientStaionList: [],
      cloneList: [],
      //会诊人员指派配置和科室
      consultAssignSationList: [],
      consultAssignSationID: undefined,
      consultGoalTwoName: "",
      consultTittle: "",
      isContext: true,
      expands: [],
      checkResult: true,
      //评价模版内容
      consultAssessTemplate: [],
      //会诊评价模版Code
      recordsCode: undefined,
      //弹窗
      dialogVisible: false,
      //组件返回数据
      tabsListData: [],
      //会诊ID
      patientConsultID: undefined,
      consultDate: this._datetimeUtil.getNow(),
      evaluateDate: this._datetimeUtil.getNow(),
      //补录标记
      refillFlag: "",
      inpatient: undefined,
    };
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      user: "getUser",
    }),
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.inpatient = this.patientInfo;
          this.refillFlag = "";
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplementPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.inpatient = this.supplementPatient;
          this.refillFlag = "*";
        }
      },
      immediate: true,
    },
    consultDetailID(newVal) {
      if (!newVal) return;
      this.context();
    },
    multidisciplinaryFlag(newVal) {
      if (newVal == true) {
        this.multidisciplinaryConsult();
        this.consultGoal = {
          consultMainID: undefined,
          emergencyFlag: false,
          consultedEmployeeID: undefined,
        };
        this.consultContent = "";
      }
    },
    "inpatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.getAll();
        }
      },
      immediate: true,
    },
  },
  //初始化
  mounted() {
    if (this.inpatient) {
      this.getAll();
    }
    this.getConsultGoal();
    this.getConsultAssign();
  },
  methods: {
    /**
     * description: 获取患者所有会诊信息
     * return {*}
     */
    getAll() {
      this.stationID = this.inpatient.stationID;
      this.loading = true;
      let params = {
        inpatientID: this.inpatient.inpatientID,
        index: Math.random(),
      };
      GetPatientConsult(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultList = res.data;
          this.loading = false;
          this.cloneList = this._common.clone(this.consultList);
          if (!this.refillFlag) {
            this.consultList = this.cloneList.filter((item) => {
              return item.stationID == this.inpatient.stationID;
            });
          }
          this.getStationList();
          setTimeout(() => {
            if (this.$refs.patientConsultTable) {
              this.$refs.patientConsultTable.doLayout();
            }
          }, 100);
        }
      });
    },
    /**
     * description: tittle组装
     * return {*}
     * param {*} name
     */
    consultTittleContext(name) {
      this.consultTittle =
        name +
        "--" +
        this.inpatient.patientName.trim() +
        "【" +
        this.inpatient.gender +
        "-" +
        this.inpatient.ageDetail +
        "】--" +
        this.inpatient.diagnose;
    },
    /**
     * description: 会诊内容新增弹框修改
     * return {*}
     * param {*} data
     */
    async suplementaryDate(data) {
      if (!this.inpatient.inpatientID) {
        this._showTip("warning", "请选择病人");
        return;
      }
      this.isContext = true;
      if (data) {
        if (data.replyContent) {
          this._showTip("warning", "会诊人员已回复,无法修改");
          return;
        }
        if (this.consultAssignSationList.ConsultAssignFlag) {
          if (data.assignEmployeeID) {
            this._showTip("warning", "会诊人员已指派，无法修改");
            return;
          }
        }
        if (this.refillFlag && this.refillFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            data.addEmployeeID
          );
          this.checkResult = saveButtonFlag;
        }
        this.isContext = false;
        this.consultTittleContext("会诊修改");
        this.save = false;
        this.contextNumber = 1;
        this.dialogFormVisible = true;
        //修改数据回显
        let template = this._common.clone(data);
        this.consultGoal.patientConsultID = template.patientConsultID;
        this.consultGoal.emergencyFlag = template.emergencyFlag == "1" ? true : false;
        this.consultGoal.consultMainID = template.consultMainID;
        this.getConsultGoalById(template.consultMainID, template.consultDetailID);
        this.getGoalEmployee(template.consultDetailID);
        this.consultContent = template.consultContent;
        this.consultGoal.consultedEmployeeID = template.consultedEmployeeID;
        this.getGoalPhoneNumber(template.consultedEmployeeID);
        this.consultAssignSationID = template.replyStationID;
        if (template.consultDate) {
          this.consultDate = this._datetimeUtil.formatDate(template.consultDate, "yyyy-MM-dd hh:mm");
        }
      } else {
        this.consultTittleContext("会诊发起");
        this.save = true;
        this.dialogFormVisible = true;
        this.consultGoal = {
          consultMainID: undefined,
          emergencyFlag: false,
          consultedEmployeeID: undefined,
        };
        this.consultContent = "";
        this.consultDetailID = undefined;
        this.consultAssignSationID = undefined;
        this.$set(this.consultGoal, "emergencyFlag", false);
      }
    },
    /**
     * description: 初始化行和增加行
     * return {*}
     */
    multidisciplinaryConsult() {
      if (this.consultGoalList.length < 2) {
        this.consultGoalList = [];
        for (let index = 0; index < 2; index++) {
          let template = {
            consultDetailID: undefined,
            consultMainID: undefined,
            stationID: undefined,
            goalEmployee: undefined,
            goalEmployeeList: [],
            consultGoalTwoList: [],
            consultAssignSationList: [],
          };
          this.consultGoalList.push(template);
        }
      } else {
        if (this.judgeAll()) {
          let template = {
            consultDetailID: undefined,
            consultMainID: undefined,
            stationID: undefined,
            goalEmployee: undefined,
            goalEmployeeList: [],
            consultGoalTwoList: [],
            consultAssignSationList: [],
          };
          this.consultGoalList.push(template);
        }
      }
    },
    /**
     * description: 删除行
     * return {*}
     * param {*} index
     */
    cancelColumn(index) {
      if (this.consultGoalList.length > 2) {
        this.consultGoalList.splice(index, 1);
      }
    },
    /**
     * description: 新增修改
     * return {*}
     */
    saveConsult() {
      if (!this.addDataJudge()) {
        return;
      }
      this.dialogLoading = true;
      if (this.save) {
        //新增
        let prams = {
          inpatientID: this.inpatient.inpatientID,
          emergencyFlag: this.consultGoal.emergencyFlag ? "1" : "0",
          consultMainID: this.consultGoal.consultMainID,
          consultDetailID: this.consultDetailID,
          consultContent: this.consultContent,
          consultedEmployeeID: this.consultGoal.consultedEmployeeID,
          replyStationID: this.consultAssignSationID,
          DetailContent: this.consultGoalTwoName,
          recordsCode: this.recordsCode,
          consultDate: this.consultDate,
          consultStationID: this.stationID,
          refillFlag: this.refillFlag,
        };
        return SaveConsult(prams).then((res) => {
          this.dialogLoading = false;
          if (this._common.isSuccess(res)) {
            this.dialogFormVisible = false;
            this._showTip("success", "会诊已发起");
            this.getAll();
          }
        });
      } else {
        let prams = {
          patientConsultID: this.consultGoal.patientConsultID,
          consultMainID: this.consultGoal.consultMainID,
          emergencyFlag: this.consultGoal.emergencyFlag ? "1" : "0",
          consultDetailID: this.consultDetailID,
          replyStationID: this.consultAssignSationID,
          consultContent: this.consultContent,
          consultedEmployeeID: this.consultGoal.consultedEmployeeID,
          DetailContent: this.consultGoalTwoName,
          consultDate: this._datetimeUtil.formatDate(this.consultDate, "yyyy-MM-dd hh:mm"),
          refillFlag: this.refillFlag,
        };
        return UpdateConsult(prams).then((res) => {
          this.dialogLoading = false;
          if (this._common.isSuccess(res)) {
            this.dialogFormVisible = false;
            this._showTip("success", "修改成功");
            this.getAll();
            this.consultDate = this._datetimeUtil.getNowDate("yyyy-MM-dd hh:mm");
          }
        });
      }
    },
    /**
     * description: 新增时判断
     * return {*}
     */
    addDataJudge() {
      if (!this.consultContent) {
        this._showTip("warning", "请填写备注");
        return false;
      }
      if (this.multidisciplinaryFlag) {
        if (this.judgeAll()) {
          this.saveMultidisciplinary();
        } else {
          this._showTip("warning", "请完整填写会诊信息");
        }
        return false;
      } else if (
        (!this.consultGoal.consultedEmployeeID || !this.consultGoal.consultMainID) &&
        (!this.consultAssignSationID || !this.consultDetailID)
      ) {
        this._showTip("warning", "请按照顺序填写会诊信息");
        return false;
      }
      return true;
    },
    /**
     * description: 确认新增多学科会诊
     * return {*}
     */
    saveMultidisciplinary() {
      this.dialogLoading = true;
      //数据组装保存多学科会诊
      let consultList = [];
      this.consultGoalList.forEach((element) => {
        let consult = {
          inpatientID: this.inpatient.inpatientID,
          EmergencyFlag: "2",
          consultContent: this.consultContent,
          consultMainID: element.consultMainID,
          consultDetailID: element.consultDetailID,
          consultedEmployeeID: element.goalEmployee,
          replyStationID: element.stationID,
        };
        consultList.push(consult);
      });
      return SaveMultidisciplinaryCousult(consultList).then((res) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "新增成功");
          this.dialogFormVisible = false;
          this.getAll();
        }
      });
    },
    /**
     * description: 回复弹框
     * return {*}
     * param {*} data
     */
    replyDialog(data) {
      if (this.consultAssignSationList.ConsultAssignFlag) {
        if (!data.assignEmployeeID) {
          this._showTip("warning", "尚未指定会诊人员");
          return;
        }
      }
      this.dialogTableVisible = true;
      this.replyData = this._common.clone(data);
      if (!data.replyContent) {
        this.replyContent = "";
      } else {
        this.replyContent = data.replyContent;
      }
    },
    /**
     * description: 确认回复
     * return {*}
     */
    reply() {
      this.dialogLoading = true;
      if (this.dialogLoading) {
        this.replyData.replyContent = this.replyContent;
        this.$set(this.replyData, "refillFlag", this.refillFlag);
        ReplyConsult(this.replyData).then((res) => {
          this.dialogLoading = false;
          if (this._common.isSuccess(res)) {
            this.dialogTableVisible = false;
            this._showTip("success", res.message);
            this.getAll();
          }
        });
      }
    },
    /**
     * description: 获取被邀请会诊人手机号码
     * return {*}
     * param {*} id
     */
    getGoalPhoneNumber(id) {
      let parms = {
        UserId: id,
      };
      GetUserInfo(parms).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.length > 0) {
            this.ConsultPhoneNumber = res.data[0].phoneNumber;
          }
        }
      });
    },
    /**
     * description: 查询所有科室
     * return {*}
     */
    getStationList() {
      GetStationList().then((res) => {
        if (this._common.isSuccess(res)) {
          this.optionsOfDepartmentList = res.data;
          this.inpatientStaionList = [];
          if (!this.refillFlag) {
            if (this.consultList.length > 0) {
              this.consultList.forEach((element) => {
                let list = this.optionsOfDepartmentList.filter((item2) => {
                  return item2.id == element.stationID;
                });
                let temporary = this.inpatientStaionList.filter((item3) => {
                  return item3.id == list[0].id;
                });
                if (temporary.length == 0) {
                  this.inpatientStaionList.push(list[0]);
                }
              });
              this.$set(this.consultGoal, "stationID", this.inpatient.stationID);
            } else {
              let list = this.optionsOfDepartmentList.filter((item2) => {
                return item2.id == this.inpatient.stationID;
              });
              this.inpatientStaionList.push(list[0]);
              this.stationID = this.inpatient.stationID;
            }
          } else {
            this.inpatientStaionList = res.data;
          }
        }
      });
    },
    /**
     * description: 科室筛选
     * return {*}
     * param {*} id
     */
    clickSearch(id) {
      if (this.cloneList.length > 0) {
        this.consultList = this.cloneList.filter((item) => {
          return item.consultStationID == id;
        });
      }
    },
    /**
     * description: 删除会诊记录
     * return {*}
     * param {*} data
     */
    async delectConsult(data) {
      if (this.refillFlag && this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          data.addEmployeeID
        );
        this.checkResult = saveButtonFlag;
      } else {
        this.checkResult = await this._common.checkActionAuthorization(this.user, data.consultEmployeeID);
      }
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (data.replyContent) {
        this._showTip("warning", "会诊已回复,无法删除");
        return;
      }
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          // 确认删除
          let prams = {
            id: data.patientConsultID,
          };
          DelectConsultByID(prams).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getAll();
            }
          });
        }
      });
    },
    /**
     * description: 获取会诊目标一阶层
     * return {*}
     */
    getConsultGoal() {
      this.consultAssignSationID = "";
      let prams = {
        level: 1,
      };
      GetConsultGoal(prams).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultGoalOne = res.data;
        }
      });
    },
    /**
     * description: 根据会诊一阶目的查询二阶目的
     * return {*}
     * param {*} id
     * param {*} consultDetailID
     */
    getConsultGoalById(id, consultDetailID) {
      //清空后面两个选项
      let prams = {
        consultMainID: id,
      };
      this.consultAssignSationID = undefined;
      this.consultDetailID = undefined;
      this.consultGoal.consultedEmployeeID = undefined;
      this.ConsultPhoneNumber = "";
      GetConsultGoalById(prams).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultGoalTwo = res.data;
          this.consultDetailID = consultDetailID;
        }
      });
    },
    /**
     * description: 多学科会诊获取二阶目的
     * return {*}
     * param {*} index
     */
    getMultidisciplinaryConsultGoalById(index) {
      if (this.judgeAll()) {
        this.context(this.consultGoalList);
      }
      let prams = {
        consultMainID: this.consultGoalList[index].consultMainID,
      };
      this.consultGoalList[index].stationID = undefined;
      this.consultGoalList[index].consultDetailID = undefined;
      this.consultGoalList[index].goalEmployee = undefined;
      this.consultGoalList[index].goalEmployeeList = [];
      this.consultGoalList[index].consultAssignSationList = [];
      GetConsultGoalById(prams).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultGoalList[index].consultGoalTwoList = res.data;
        }
      });
    },
    /**
     * description: 多学科会诊获取人员
     * return {*}
     * param {*} index
     */
    getmultidisciplinaryGoalEmployee(index) {
      if (this.judgeAll()) {
        this.context(this.consultGoalList);
      }
      let prams = {
        consultGoalID: this.consultGoalList[index].consultDetailID,
        index: index,
      };
      this.consultGoalList[index].sationID = undefined;
      this.consultGoalList[index].goalEmployee = undefined;
      if (this.consultAssignSationList.ConsultAssignFlag) {
        this.getConsultAssign(prams);
        return;
      }
      GetConsultGoalEmployee(prams).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultGoalList[index].goalEmployeeList = res.data;
        }
      });
    },
    changeConsultGoalJudge() {
      if (this.judgeAll()) {
        this.context(this.consultGoalList);
      }
    },
    /**
     * description: 判断是否全选
     * return {*}
     */
    judgeAll() {
      let index = 0;
      this.consultGoalList.forEach((element) => {
        if (element.stationID && element.consultDetailID && element.consultMainID) {
          if (this.consultAssignSationList.ConsultAssignFlag) {
            if (element.stationID) {
              index += 1;
            }
          } else {
            if (element.goalEmployee) {
              index += 1;
            }
          }
        }
      });
      if (index == this.consultGoalList.length) {
        return true;
      } else {
        return false;
      }
    },
    /**
     * description: 获取会诊指派配置
     * return {*}
     * param {*} prams
     */
    getConsultAssign(prams) {
      GetConsultAssign(prams).then((res) => {
        if (this._common.isSuccess(res)) {
          try {
            this.consultGoalList[prams.index].consultAssignSationList = res.data.stationList;
          } catch (err) {
            this.consultAssignSationList = res.data;
          }
        }
      });
    },
    /**
     * description: 自动组装会诊信息
     * return {*}
     * param {*} list
     */
    context(list) {
      if (this.contextNumber == 1) {
        this.contextNumber = 0;
        return;
      }
      this.consultGoalTwoName = "";
      if (list) {
        for (let index = 0; index < list.length; index++) {
          let consultGoal = list[index].consultGoalTwoList.filter((element) => {
            return element.consultGoalID == list[index].consultDetailID;
          });
          if (index != list.length - 1) {
            this.consultGoalTwoName += consultGoal[0].content + ",";
          } else {
            this.consultGoalTwoName += consultGoal[0].content;
          }
        }
      } else {
        let consultGoal = this.consultGoalTwo.filter((element) => {
          return element.consultGoalID == this.consultDetailID;
        });
        this.consultGoalTwoName = consultGoal[0].content;
      }
      let ageStr;
      if (this.inpatient.ageDetail == null) {
        this.inpatient.ageDetail = "";
      }
      let age = Number.parseInt(this.inpatient.ageDetail) + "";
      if (age.length != this.inpatient.ageDetail.length) {
        ageStr = "年龄:" + this.inpatient.ageDetail;
      } else {
        ageStr = "";
      }
      this.consultContent =
        this.inpatient.stationName +
        this.inpatient.bedNumber +
        "床患者" +
        this.inpatient.patientName.trim() +
        "," +
        this.inpatient.gender +
        "," +
        ageStr +
        "," +
        "诊断是:" +
        this.inpatient.diagnose.trim() +
        ",因" +
        this.consultGoalTwoName +
        "需进行会诊。";
    },
    /**
     * description: 获取会诊人员
     * return {*}
     * param {*} id
     */
    getGoalEmployee(id) {
      let consultGoal = this.consultGoalTwo.find((item) => {
        return item.consultGoalID == id;
      });
      if (consultGoal) {
        this.recordsCode = consultGoal.recordsCode;
      }
      let prams = {
        consultGoalID: id,
      };
      this.consultAssignSationID = undefined;
      if (this.consultAssignSationList.ConsultAssignFlag) {
        this.getConsultAssign(prams);
        return;
      }
      this.consultGoal.consultedEmployeeID = undefined;
      this.ConsultPhoneNumber = "";
      GetConsultGoalEmployee(prams).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultGoalEmployeeLsit = res.data;
        }
      });
    },
    loadData(row) {
      if (row.hasChildren) {
        var index = this.expands.indexOf(row.patientConsultID);
        if (index >= 0) {
          this.expands.splice(index, 1);
          return;
        }
        this.expands.push(row.patientConsultID);
      } else {
        this.expands = [];
      }
    },
    changeValues(data) {
      //模板数据
      this.tabsListData = data;
    },
    /**
     * description: 获取会诊评价模版
     * param {row} 当前操作会诊
     * return {*}
     */
    getConsultAssessView(row) {
      if (!row.replyEmployeeID) {
        this._showTip("warning", "尚未回复会诊");
        return;
      }
      if (!row.replyConsultEvaluate) {
        this._showTip("warning", "尚未评价此次会诊申请");
        return;
      }
      this.consultAssessTemplate = [];
      this.patientConsultID = row.patientConsultID;
      this.recordsCode = row.recordsCode;
      this.dialogLoading = true;
      let params = {
        PatientConsultID: row.patientConsultID,
        recordsCode: "Initiate" + row.recordsCode,
        inpatientID: row.inpatientID,
      };
      let patientConsultParams = {
        patientConsultID: row.patientConsultID,
      };
      GetConsultRecord(patientConsultParams).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.initiateConsultEvaluateDate) {
            this.evaluateDate = this._datetimeUtil.formatDate(res.data.initiateConsultEvaluateDate, "yyyy-MM-dd hh:mm");
          }
        }
      });
      GetConsultAssessView(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.consultAssessTemplate = res.data;
          this.dialogVisible = true;
          this.dialogLoading = false;
        }
      });
    },
    /**
     * description: 会诊评价保存
     * param {*}
     * return {*}
     */
    saveConsultEvaluate() {
      this.dialogLoading = true;
      let list = [];
      if (!this.tabsListData.length) {
        this.dialogVisible = false;
        return list;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        this.dialogVisible = false;
        return list;
      }
      this.tabsListData.forEach((element) => {
        let item = {
          assessListID: element.assessListID,
          assessValue: element.assessValue,
          assessListGroupID: element.assessListGroupID,
          assessValue: element.assessValue,
          controlerType: element.controlerType,
        };
        if (element.controlerType.trim() == "C" || element.controlerType.trim() == "R") {
          item.assessValue = "";
        } else {
          item.assessValue = element.assessValue;
        }
        list.push(item);
      });
      let params = {
        recordsCode: "Initiate" + this.recordsCode,
        patientConsultID: this.patientConsultID,
        PatientConsultDetailList: list,
      };
      if (this.evaluateDate) {
        params.evaluateDate = this._datetimeUtil.formatDate(this.evaluateDate, "yyyy-MM-dd hh:mm");
      } else {
        params.evaluateDate = this._datetimeUtil.getNow();
      }
      SaveConsultDetail(params).then((res) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "会诊评价保存成功");
          this.dialogVisible = false;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.patient-consult {
  .consult-header {
    .header-btn {
      float: right;
    }
  }
  .opt {
    display: inline-block;
    width: 16px;
    height: 18px;
    margin: 0 3px;
  }
  .send-consult {
    .el-dialog {
      margin-top: 20vh !important;
    }
    .dag-div {
      margin-left: 2%;
      margin-bottom: 15px;
      margin-top: 10px;
      .el-input__inner {
        width: 168px;
      }
    }
    .multidisciplinary-div {
      margin-left: 2%;
      margin-bottom: 15px;
      margin-top: 10px;
      .el-input__inner {
        width: 155px;
      }
    }
    .el-textarea__inner {
      width: 560px;
      float: right;
      height: 250px !important;
      margin-right: 5px;
      min-width: 100px;
    }

    .tip {
      margin-bottom: 25px;
      height: 68%;
      margin-left: 10px;
    }
    .consult-input {
      margin-left: 70px;
      margin-top: -20px;
    }
    .consult-margin {
      margin-left: 22px;
    }
    .el-dialog {
      height: auto;
    }
    .phone-number {
      .phone-number-span {
        margin-left: 2%;
      }
      .emergency {
        float: right;
        margin-right: 15px;
      }
    }
  }
  .reply {
    .el-dialog {
      height: 450px;
      margin-top: 20vh !important;
    }
  }
  .no-reply {
    font-weight: 50px;
    color: #ea4380;
  }
  .el-table__expanded-cell {
    .nesting-table {
      width: calc(100% - 50px);
    }
  }
}
</style>
