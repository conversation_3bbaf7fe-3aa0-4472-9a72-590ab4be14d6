/*
 * FilePath     : \ccc.web\src\api\user.js
 * Author       : 郭自飞
 * Date         : 2019-10-25 16:41
 * LastEditors  : 孟昭永
 * LastEditTime : 2025-04-18 17:37
 * Description  :
 */
import qs from "qs";
import http from "../utils/ajax";
const baseUrl = "/user";

export const urls = {
  userLogin: baseUrl + "/GetUserPass",
  GetSession: baseUrl + "/GetSession",
  GetNurse: baseUrl + "/GetNurse",
  GetUserInfo: baseUrl + "/GetUserInfo",
  GeElectBoardSwitchStationList: baseUrl + "/GeElectBoardSwitchStationList",
  GetUserRoleByStationID: baseUrl + "/GetUserRoleByStationID",
  UpdataUserById: baseUrl + "/UpdataUserById",
  GetUserRoleByUserID: baseUrl + "/GetUserRoleByUserID",
  GetEmployeeSwitchStationList: baseUrl + "/GetEmployeeSwitchStationList",
  UpdateSessionStationID: baseUrl + "/UpdateSessionStationID",
  UserCheck: baseUrl + "/UserCheck",
  GetUserByStationID: baseUrl + "/GetUserByStationID",
  GetUserByKeyword: baseUrl + "/GetUserByKeyword",
  SetSessionByHospitalLanguage: baseUrl + "/SetSessionByHospitalLanguage",
  GetSignLoginStatus: baseUrl + "/GetSignLoginStatus",
  GenCAQrCode: baseUrl + "/GenCAQrCode",
  CheckCASignInfo: baseUrl + "/CheckCASignInfo",
  GetIsHeadNurse: baseUrl + "/GetIsHeadNurse",
  CheckLoginAndCA: baseUrl + "/CheckLoginAndCA",
  SyncEmployeeCAData: baseUrl + "/SyncEmployeeCAData",
};

// 登陆
export const userLogin = (params) => {
  return http.post(urls.userLogin, params);
};
export const GetSession = () => {
  return http.get(urls.GetSession);
};
export const GetNurse = (params) => {
  return http.get(urls.GetNurse, params);
};

export const GetUserInfo = (params) => {
  return http.get(urls.GetUserInfo, params);
};

export const GeElectBoardSwitchStationList = (params) => {
  return http.get(urls.GeElectBoardSwitchStationList, params);
};

// 根据StationID获取护士信息
export const GetUserRoleByStationID = (params) => {
  return http.get(urls.GetUserRoleByStationID, params);
};

export const UpdataUserById = (params) => {
  return http.post(urls.UpdataUserById, params);
};
export const GetUserRoleByUserID = (params) => {
  return http.get(urls.GetUserRoleByUserID, params);
};
//获取人员可切换病区
export const GetEmployeeSwitchStationList = (params) => {
  return http.get(urls.GetEmployeeSwitchStationList, params);
};
// 更新缓存
export const UpdateSessionStationID = (params) => {
  return http.post(urls.UpdateSessionStationID, qs.stringify(params));
};
// 中山登录
export const UserCheck = (params) => {
  return http.post(urls.UserCheck, qs.stringify(params));
};
// 根据病区序号获取用户信息
export const GetUserByStationID = (params) => {
  return http.get(urls.GetUserByStationID, params);
};
// 根据关键字获取用户信息
export const GetUserByKeyword = (params) => {
  return http.get(urls.GetUserByKeyword, params);
};
// 针对第三方跳转CCC时，无Token跳转，后端设置模拟session
export const SetSessionByHospitalLanguage = (params) => {
  return http.post(urls.SetSessionByHospitalLanguage, qs.stringify(params));
};
//账号CA校验
export const GetSignLoginStatus = (params) => {
  return http.get(urls.GetSignLoginStatus, params);
};
/**
 * 获取CA扫码登录二维码（宏力CA用）
 * @param {*} params
 * @returns
 */
export const GenCAQrCode = (params) => {
  return http.post(urls.GenCAQrCode, qs.stringify(params));
};
/**
 *  获取CA授权用户信息（宏力CA用，定时循环扫描）
 * @param {*} params
 * @returns
 */
export const CheckCASignInfo = (params) => {
  return http.post(urls.CheckCASignInfo, qs.stringify(params));
};
/**
 * 获取是否是护士长
 * @param {*} params
 * @returns
 */
export const GetIsHeadNurse = (params) => {
  return http.get(urls.GetIsHeadNurse, params);
};
/**
 * 检核比对登录账户与CA账户的一致性
 * @param {*} params
 * @returns
 */
export const CheckLoginAndCA = (params) => {
  return http.get(urls.CheckLoginAndCA, params);
};
/**
 * 同步CA数据
 * @param {*} params
 * @returns
 */
export const SyncEmployeeCAData = (params) => {
  return http.post(urls.SyncEmployeeCAData, params);
};
