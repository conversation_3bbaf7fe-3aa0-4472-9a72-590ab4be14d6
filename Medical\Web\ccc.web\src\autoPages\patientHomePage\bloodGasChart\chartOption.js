/*
 * FilePath     : \src\autoPages\patientHomePage\bloodGasChart\chartOption.js
 * Author       : 来江禹
 * Date         : 2023-06-06 15:40
 * LastEditors  : 来江禹
 * LastEditTime : 2023-06-06 16:04
 * Description  : 
 * CodeIterationRecord: 
 */

export const setBloodChartOption = (chart, _this) => {
  let symbolSize = _this.convertPX(8);
  let fontSize = _this.convertPX(15);
  let yTitleFontSize = _this.convertPX(12);
  let yAxisLine = {
    show: true,
    lineStyle: {
      width: _this.convertPX(1)
    }
  };
  let yAxisTick = {
    show: true,
    lineStyle: {
      width: _this.convertPX(1)
    }
  };
  let yRightAxisLabel = {
    show: true,
    fontWeight: "bold",
    fontSize: _this.convertPX(13),
    margin: _this.convertPX(8),
    padding: [0, 5, 0, 0]
  };
  let yLeftAxisLabel = {
    show: true,
    fontWeight: "bold",
    fontSize: _this.convertPX(13),
    margin: _this.convertPX(8),
    padding: [0, 0, 0, 4]
  };
  let seriesLabel = {
    show: false,
    position: "top",
    fontWeight: "bold",
    fontSize: fontSize
  };
  let seriesOptions = {
    type: "line",
    connectNulls: true,
    smooth: false,
    showAllSymbol: true,
    label: seriesLabel,
    symbolSize: symbolSize
    };
  chart.setOption({
    xAxis:{
      axisLabel: {
      formatter: val => {
        let newVal = _this._datetimeUtil.formatDate(val, "MM-dd hh:mm");
        return newVal.split(" ").join("\n");
      },
      textStyle: {
        fontWeight: "bold",
        fontSize: _this.convertPX(14)
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#000000",
        width: _this.convertPX(1)
      }
    }
  },
    yAxis: [
      {
        position: "left",
        offset: _this.convertPX(45),
        type: "value",
        name: "mmHg",
        min: -1000, //Y轴最小值
        max: 1000, //Y轴最大值
        interval: 200, //Y轴间隔
        nameTextStyle: {
          padding: [_this.convertPX(5), 0, _this.convertPX(5), _this.convertPX(-45)],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        //坐标轴线
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yLeftAxisLabel
      },
      {
        position: "left",
        type: "value",
        name: "mmol/L",
        min: 0, //Y轴最小值
        max: 200, //Y轴最大值
        interval: 20, //Y轴间隔
        nameTextStyle: {
          padding: [0, 0, _this.convertPX(5), _this.convertPX(-25)],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yRightAxisLabel
      },
      {
        position: "right",
        type: "value",
        name: "%",
        min: 0, //Y轴最小值
        max: 100, //Y轴最大值
        interval: 10, //Y轴间隔
        nameTextStyle: {
          padding: [0, _this.convertPX(-30), _this.convertPX(5), _this.convertPX(-35)],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        //坐标轴线
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yLeftAxisLabel
      },
    ],
       series:[
      { yAxisIndex: 2, ...seriesOptions }, // 1 pH
      { yAxisIndex: 0,...seriesOptions }, // 2 PCO2
      { yAxisIndex: 0,...seriesOptions }, // 3 PO2
      { yAxisIndex: 1,...seriesOptions }, // 4 Na+
      { yAxisIndex: 1,...seriesOptions }, // 5 K+
      { yAxisIndex: 1,...seriesOptions }, // 6 Lac
      { yAxisIndex: 1,...seriesOptions }, // 7 HCO3
      { yAxisIndex: 1,...seriesOptions }, // 8 TCO2
      { yAxisIndex: 1,...seriesOptions }, // 9 BE
      { yAxisIndex: 2,...seriesOptions }, // 10 So2c
      { yAxisIndex: 2,...seriesOptions }, // 11 FiO2
      { yAxisIndex: 0,...seriesOptions } // 12 A-aDO2
      ]
  });
};
export const grid = {
  top: 60,
  height: "auto",
  bottom: 5,
  right: 10,
  left: 10
};

export const tooltip = {
  trigger: "axis",
  // 将 tooltip 框限制在图表的区域内
  confine: true,
  axisPointer: {
    animation: false
  }
};
export const extend = {
  "xAxis.0.axisLabel.formatter": val => {
    return val.split(" ").join("\n");
  },
  series: {
    type: "line",
    connectNulls: true,
    smooth: false
  }
};
