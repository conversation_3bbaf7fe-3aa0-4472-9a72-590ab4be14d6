<!--
 * FilePath     : \src\pages\pumping\index.vue
 * Author       : 郭鹏超
 * Date         : 2020-07-16 18:14
 * LastEditors  : 来江禹
 * LastEditTime : 2023-11-29 09:32
 * Description  : 泵入
-->
<template>
  <specific-care
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :careMainAddFlag="careMainAddFlag"
    :previewFlag="!checkResult"
    :recordTitleSlotFalg="true"
    @getMainFlag="clickRecordFlag"
    @mainAdd="recordAdd"
    @maintainAdd="careMianAdd"
    @save="saveSelect"
    @cancel="showTemplateFlag = false"
    class="pumping"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <!-- 顶部记录总览 -->
    <div slot="record-title">
      <span>主记录</span>
      <el-button @click="showAllCareMain" type="primary" icon="iconfont icon-curriculum">药物维护记录总览</el-button>
    </div>
    <!-- 主记录 -->
    <div slot="main-record">
      <el-table :data="recordList" @row-click="recordRowClick" height="100%" border stripe>
        <el-table-column label="发生科室" prop="departmentName" width="120" align="center"></el-table-column>
        <el-table-column label="通路" prop="pathName" width="90" align="center">
          <template slot-scope="scope">
            <div>
              {{ "通路" + scope.row.sort }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="泵入药物" prop="drugName" min-width="95"></el-table-column>
        <el-table-column label="建议剂量" prop="suggestedDose" width="160" align="center"></el-table-column>
        <el-table-column label="常规系数" prop="coefficient" width="120" align="center"></el-table-column>
        <el-table-column prop="" label="开始日期" width="100" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="开始时间" width="80" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="结束日期" width="100" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.endDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="结束时间" width="80" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.endTime, type: 'time' }"></span>
          </template>
        </el-table-column>

        <el-table-column label="泵入剂量（ml）" align="center" prop="dosage" min-width="150"></el-table-column>
        <el-table-column label="备注" prop="remark" min-width="150"></el-table-column>
        <el-table-column label="记录人" prop="nurseName" width="70" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" header-align="center" align="center" width="90">
          <template slot-scope="scope">
            <el-tooltip v-if="!scope.row.endDate" content="停止">
              <div @click.stop="pumpingEnd(scope.row)" class="iconfont icon-stop"></div>
            </el-tooltip>
            <el-tooltip v-else content="续泵">
              <div @click.stop="careMianAdd(scope.row)" class="iconfont icon-execute"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="deleteRecordBtn(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <el-table class="care-main-table" :data="careMainList" height="100%" ref="careMainAddTable" border stripe>
        <el-table-column
          v-for="(item, index) in careMainListHerder"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="!item.minWidthFlag ? item.width : item.minWidthFlag"
          :min-width="item.minWidthFlag ? item.width : item.minWidthFlag"
          :header-align="item.herderPosition"
          :align="item.position"
          :fixed="item.fixFlag"
        >
          <template slot-scope="scope">
            <headerList :item="item" :scope="scope"></headerList>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="70">
          <template slot-scope="scope">
            <el-tooltip :content="`${scope.row.newFlag ? '保存' : '修改'}`">
              <div
                @click.stop="careMainSave(scope.row, 'PumpingMaintain')"
                :class="`iconfont icon-${scope.row.newFlag ? 'save' : 'edit'}`"
              ></div>
            </el-tooltip>
            <el-tooltip
              :style="{
                visibility: scope.row.recordsCode != 'PumpingStart' && !scope.row.newFlag ? 'visible' : 'hidden',
              }"
              content="删除"
            >
              <div @click.stop="careMainDelete(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 抽屉 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="layoutLoading"
      :element-loading-text="layoutText"
    >
      <div slot="header">
        <span class="label" v-if="recordsCode == 'PumpingStart'">执行日期:</span>
        <el-date-picker
          v-if="recordsCode == 'PumpingStart'"
          v-model="performDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          style="width: 120px"
          @change="changeRecord($event, 'startDate')"
        ></el-date-picker>
        <el-time-picker
          v-if="recordsCode == 'PumpingStart'"
          v-model="performTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          style="width: 80px"
          @change="changeRecord($event, 'startTime')"
        ></el-time-picker>
        <span class="label">执行病区:</span>
        <station-selector
          @select="changeRecord($event, 'stationID')"
          v-model="stationID"
          label=""
          width="160"
        ></station-selector>
        <dept-selector
          @select="changeRecord($event, 'departmentListID')"
          label=""
          width="140"
          v-model="departmentListID"
          :stationID="stationID"
        ></dept-selector>
        <el-select
          v-if="recordsCode == 'PumpingStart'"
          @change="GetPumpingRecordAddData"
          v-model="medicineTypeKind"
          placeholder="请选择"
        >
          <el-option
            v-for="(item, index) in medicineTypeList"
            :key="index"
            :label="item.typeName"
            :value="item.typeKind"
          ></el-option>
        </el-select>
      </div>
      <el-table
        v-if="recordsCode == 'PumpingStart'"
        v-loading="loading"
        element-loading-text="加载中……"
        class="care-main-table"
        :data="recordAddData"
        height="100%"
        ref="recordAddTable"
        border
        stripe
        @selection-change="selectItem"
      >
        <el-table-column type="selection" :width="convertPX(40)" align="center"></el-table-column>
        <el-table-column label="日期" width="100">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.startDate"
              type="date"
              :clearable="false"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width: 100%"
              @blur="toggleSelection(scope.row, true)"
            ></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="时间" width="65">
          <template slot-scope="scope">
            <el-time-picker
              v-model="scope.row.startTime"
              value-format="HH:mm"
              format="HH:mm"
              style="width: 100%"
              @blur="toggleSelection(scope.row, true)"
            ></el-time-picker>
          </template>
        </el-table-column>
        <el-table-column label="药物" prop="drugName" min-width="60"></el-table-column>
        <el-table-column label="建议剂量" prop="suggestedDose" width="130"></el-table-column>
        <el-table-column label="常规系数" prop="coefficient" width="130"></el-table-column>
        <el-table-column label="频次" width="100">
          <template slot-scope="scope">
            <el-select
              style="width: 100%"
              v-model="scope.row.frequency"
              placeholder="请选择"
              @blur="toggleSelection(scope.row, true)"
            >
              <el-option
                v-for="(item, index) in frequencyList"
                :key="index"
                :label="item.label"
                :value="Number(item.value)"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="备注" min-width="120">
          <template slot-scope="scope">
            <el-input
              @blur="toggleSelection(scope.row, true)"
              style="width: 100%"
              v-model="scope.row.remark"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
      <el-table
        v-if="recordsCode == 'PumpingEnd'"
        class="care-main-table"
        :data="endCareMainList"
        height="100%"
        ref="pumpingEndTable"
        border
        stripe
      >
        <el-table-column
          v-for="(item, index) in endHeader"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="!item.minWidthFlag ? item.width : item.minWidthFlag"
          :min-width="item.minWidthFlag ? item.width : item.minWidthFlag"
          :header-align="item.herderPosition"
          :align="item.position"
          :fixed="item.fixFlag"
        >
          <template slot-scope="scope">
            <headerList :item="item" :scope="scope"></headerList>
          </template>
        </el-table-column>
      </el-table>
    </base-layout>

    <!-- 维护记录总览 -->
    <el-dialog
      slot="drawer-dialog"
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="allCareMainFlag"
      title="维护记录总览"
      fullscreen
      custom-class="all-careMain"
      v-loading="loading"
      element-loading-text="加载中……"
      @close="dialogClose"
    >
      <base-layout header-height="auto" v-loading="layoutLoading" :element-loading-text="layoutText">
        <div slot="header">
          <span class="label">执行日期:</span>
          <el-date-picker
            v-model="careMainDate"
            type="date"
            :clearable="false"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            style="width: 120px"
            @change="GetPumpingCareMainList()"
          ></el-date-picker>
        </div>
        <el-table
          v-loading="diaLoading"
          element-loading-text="加载中……"
          class="care-main-table"
          :data="allCareMainList"
          height="100%"
          ref="allCareMainAddTable"
          border
          stripe
          :span-method="cellMerge"
        >
          <el-table-column
            v-for="(item, index) in careMainListHerder"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :width="!item.minWidthFlag ? item.width : item.minWidthFlag"
            :min-width="item.minWidthFlag ? item.width : item.minWidthFlag"
            :header-align="item.herderPosition"
            :align="item.position"
            :fixed="item.fixFlag"
          >
            <template slot-scope="scope">
              <headerList :item="item" :scope="scope"></headerList>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" header-align="center" width="70">
            <template slot-scope="scope">
              <el-tooltip content="修改">
                <div @click.stop="careMainSave(scope.row, 'PumpingMaintain')" class="iconfont icon-edit"></div>
              </el-tooltip>
              <el-tooltip v-if="scope.row.recordsCode != 'PumpingStart'" content="删除">
                <div @click.stop="careMainDelete(scope.row)" class="iconfont icon-del"></div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </base-layout>
    </el-dialog>
  </specific-care>
</template>

<script>
import { mapGetters } from "vuex";
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import baseLayout from "@/components/BaseLayout";
import headerList from "./component/headerList";
import {
  GetPumpingDrugType,
  GetPumpingDrugFrequency,
  GetPumpingAddRecordTableView,
  GetPumpingtableHeader,
  PumingRecordSave,
  GetPumpingRecordTableData,
  GetPumpingCareMainTableData,
  PumingCareMianSave,
  DeletePumpingRecord,
  DeletePumpingCareMain,
  GetNewCareMain,
  PumpingEndCheck,
} from "@/api/Pumping";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
      patient: "getPatientInfo",
    }),
  },
  components: {
    specificCare,
    stationSelector,
    deptSelector,
    baseLayout,
    headerList,
  },
  data() {
    return {
      //加载
      loading: false,
      layoutLoading: false,
      layoutText: undefined,
      diaLoading: false,
      //组件变量
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      //主记录变量
      recordList: [],
      recordAddData: [],
      currentRecord: undefined,
      //维护记录变量
      careMainList: [],
      careMainListHerder: [],
      frequencyList: [],
      careMainAddFlag: true,
      //弹窗变量
      performDate: undefined,
      performTime: undefined,
      stationID: undefined,
      departmentListID: undefined,
      medicineTypeKind: "All",
      medicineTypeList: [],
      recordsCode: undefined,
      //记录总览变量
      allCareMainFlag: false,
      careMainDate: undefined,
      allCareMainList: [],
      endHeader: [],
      endCareMainList: [],
      spanArr: [],
      pos: 0,
      checkResult: true,
    };
  },
  watch: {
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.getPumpingRecordList();
        }
      },
      immediate: true,
    },
    //动态控制维护新增按钮
    currentRecord: {
      handler(newVal) {
        this.careMainAddFlag = newVal && !newVal.endDate && !newVal.endTime ? true : false;
      },
      deep: true,
      immediate: true,
    },
  },
  beforeMount() {
    this.init();
  },
  methods: {
    //页面初始化
    init() {
      this.getDrugType();
      this.getDrugFrequency();
      this.getPumpingTableHeader();
    },

    /*主记录CRUD -----------------------------------------*/

    //获取主记录表格数据
    getPumpingRecordList(recordID) {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      if (recordID) {
        params.recordID = recordID;
      }
      this.loading = true;
      GetPumpingRecordTableData(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.recordList = res.data;
          this.currentRecord = recordID && this.recordList?.length ? this.recordList[0] : undefined;
        }
      });
    },

    //新增泵入主记录
    recordAdd(item) {
      this.checkResult = true;
      this.GetPumpingRecordAddData();
      this.openOrCloseDrawer(true, "新增泵入药物");
      this.recordsCode = "PumpingStart";
      this.performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = this._datetimeUtil.getNowTime("hh:mm");
      this.stationID = this.patient.stationID;
      this.departmentListID = this.patient.departmentListID;
    },

    //获取新增药物弹窗表格数据
    GetPumpingRecordAddData() {
      let params = {
        pumpingDrugType: this.medicineTypeKind,
        inpatientID: this.patient.inpatientID,
      };
      GetPumpingAddRecordTableView(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.recordAddData = res.data;
          this.changeRecord(this.stationID, "stationID");
          this.changeRecord(this.departmentListID, "departmentListID");
        }
      });
    },

    //泵入主记录停止
    async pumpingEnd(row) {
      this.currentRecord = row;
      this.recordsCode = "PumpingEnd";
      this.stationID = this.patient.stationID;
      this.departmentListID = this.patient.departmentListID;
      this.openOrCloseDrawer(true, "泵入停止");
      this.endCareMainList = await this.getNewCareMainTableData(row.patientPumpingRecordID, "PumpingEnd");
    },

    saveSelect() {
      if (this.recordsCode == "PumpingStart") {
        this.pumpingStartSave();
      }
      if (this.recordsCode == "PumpingEnd") {
        this.pumpingEndSave();
      }
    },

    //泵入主记录保存
    async pumpingStartSave() {
      if (this.allData.length == 0) {
        this._showTip("warning", "请勾选需要新增的泵入药物");
        return;
      }
      this.layoutLoading = true;
      this.layoutText = "保存中……";
      let sucFlag = true;
      for (let i = 0; i < this.allData.length; i++) {
        await PumingRecordSave(this.allData[i]).then((res) => {
          if (res.code == 0) {
            sucFlag = false;
          }
        });
      }
      this.layoutLoading = false;
      this.layoutText = "";
      this._showTip(sucFlag ? "success" : "error", sucFlag ? "保存成功" : "保存失败");
      this.openOrCloseDrawer(false, "");
      this.getPumpingRecordList();
    },
    //泵入停止保存
    async pumpingEndSave() {
      let row = {};
      Object.assign(row, ...this.endCareMainList);
      //检核是否可以泵入停止
      let message = await this.pumpingEndCheck(row.patientPumpingRecordID, row.performDate, row.performTime);
      if (message) {
        this._showTip("warning", message);
        return;
      }
      if (!Object.keys(row).length) {
        this._showTip("warning", "保存失败");
        return;
      }
      row.stationID = this.stationID;
      row.departmentListID = this.departmentListID;
      row.recordsCode = this.recordsCode;
      this.layoutLoading = true;
      this.layoutText = "保存中……";
      await this.careMainSave(row, "PumpingEnd");
      this.layoutLoading = false;
      this.layoutText = "";
      this._showTip("success", "保存成功");
      this.openOrCloseDrawer(false, "");
    },

    //泵入主记录删除
    async deleteRecordBtn(item) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, item.nurseName);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (!item.patientPumpingRecordID) {
        this._showTip("warning", "删除失败");
        return;
      }
      let params = {
        recordID: item.patientPumpingRecordID,
      };
      this._deleteConfirm("", (flag) => {
        if (flag) {
          DeletePumpingRecord(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.fixTable();
              this.getPumpingRecordList();
            }
          });
        }
      });
    },

    /*维护记录CRUD -----------------------------------------*/

    //主记录点击
    async recordRowClick(item) {
      this.currentRecord = item;
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.recordList = [item];
        await this.GetPumpingCareMainList();
        this.$nextTick(() => {
          this.$refs.careMainAddTable.doLayout();
        });
      }
    },

    //获取维护记录表格或者记录总览数据
    GetPumpingCareMainList() {
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      if (this.allCareMainFlag) {
        this.allCareMainList = [];
        params.performDate = this.careMainDate;
        this.diaLoading = true;
      } else {
        params.recordID = this.currentRecord.patientPumpingRecordID;
        this.careMainList = [];
        this.loading = true;
      }
      GetPumpingCareMainTableData(params).then((res) => {
        this.diaLoading = false;
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data.length > 0) {
            res.data.forEach((item) => {
              if (item.frequency) {
                item.frequency = item.frequency + "";
              }
            });
          }
          if (this.allCareMainFlag) {
            this.allCareMainList = res.data;
            this.getSpanArr(this.allCareMainList);
          } else {
            this.careMainList = res.data;
          }
        }
      });
    },

    //新增泵入维护记录
    async careMianAdd(recordInfo) {
      if (!this.currentRecord) {
        this._showTip("warning", "请先选择泵入主记录");
        return;
      }
      let noPerformCareMian = this.careMainList.filter((m) => m.newFlag);
      if (noPerformCareMian.length >= 1) {
        this._showTip("warning", "请先执行未评估记录！");
        return;
      }
      let newCaraMain =
        recordInfo == null
          ? await this.getNewCareMainTableData(this.currentRecord.patientPumpingRecordID, "PumpingMaintain")
          : await this.getNewCareMainTableData(this.currentRecord.patientPumpingRecordID, "PumpingStart");
      if (newCaraMain.length) {
        newCaraMain[0].newFlag = true;
        newCaraMain[0].stationID = this.patient.stationID;
        newCaraMain[0].departmentListID = this.patient.departmentListID;
        this.careMainList.push(newCaraMain[0]);
      }
    },

    //维护记录保存
    async careMainSave(row, recordsCode) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, row.nurseName);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (!row.speed && row.recordsCode != "PumpingEnd") {
        this._showTip("warning", "请填写泵速!");
        return;
      }
      //自控次数检核
      if (row.validAutocontrol && row.tryAutocontrol && parseInt(row.validAutocontrol) > parseInt(row.tryAutocontrol)) {
        this._showTip("warning", "有效自控次数不能大于尝试自控次数!");
        return;
      }
      if (row.details.length > 0) {
        let details = [];
        row.details.forEach((item) => {
          if (row[item.prop]) {
            item.value = row[item.prop];
            details.push(item);
          }
        });
        row.details = details;
      }
      row.recordsCode = row.recordsCode ? row.recordsCode : recordsCode;
      await this.careMainSaveAsync(row);
    },
    async careMainSaveAsync(row) {
      this.loading = true;
      await PumingCareMianSave(row).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          if (row.recordsCode == "PumpingEnd") {
            this.$set(row, "endDate", row.performDate);
            this.$set(row, "endTime", row.performTime);
          }
          this.getPumpingRecordList(row.patientPumpingRecordID);
          this.GetPumpingCareMainList();
        }
      });
    },

    //维护记录删除
    async careMainDelete(item) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, item.nurseName);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (!item.patientPumpingCareMainID) {
        this._showTip("warning", "删除失败");
        return;
      }
      let params = {
        careMainID: item.patientPumpingCareMainID,
      };
      this._deleteConfirm("", (flag) => {
        if (flag) {
          DeletePumpingCareMain(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getPumpingRecordList(item.patientPumpingRecordID);
              this.GetPumpingCareMainList();
            }
          });
        }
      });
    },

    //维护记录总览
    async showAllCareMain() {
      this.allCareMainFlag = true;
      this.loading = true;
      this.careMainDate = this._datetimeUtil.getNowDate();
      await this.GetPumpingCareMainList();
      this.loading = false;
    },

    //主记录勾选
    clickRecordFlag(flag) {
      if (flag) {
        this.getPumpingRecordList();
        this.currentRecord = undefined;
        this.careMainList = [];
      } else {
        if (!this.currentRecord && this.recordList.length) {
          this.currentRecord = this.recordList[0];
          this.GetPumpingCareMainList();
        }
      }
    },

    //弹窗开关函数
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      // this.drawerTitle = title;
      this.drawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
    },

    //勾选数据
    selectItem(item) {
      this.allData = item;
    },

    //动态更改保存数据
    changeRecord(value, prop) {
      if (!this.recordAddData.length) {
        return;
      }
      this.recordAddData.forEach((item) => (item[prop] = value));
    },

    //异动病人数据自动勾选
    toggleSelection(row, flag) {
      if (row) {
        this.$refs.recordAddTable.toggleRowSelection(row, true);
      } else {
        this.$refs.recordAddTable.clearSelection();
      }
    },

    //获取药物类别
    getDrugType() {
      GetPumpingDrugType().then((res) => {
        if (this._common.isSuccess(res)) {
          this.medicineTypeList = res.data;
        }
      });
    },

    //获取药物频次
    getDrugFrequency() {
      GetPumpingDrugFrequency().then((res) => {
        if (this._common.isSuccess(res)) {
          this.frequencyList = res.data;
        }
      });
    },

    //获取表格表头
    async getPumpingTableHeader(type = undefined, kind = undefined) {
      let params = {
        type,
        kind,
      };
      await GetPumpingtableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.careMainListHerder = res.data;
          this.endHeader = res.data.filter(
            (item) => item.prop != "frequency" && item.prop != "speed" && item.prop != "nurseName"
          );
        }
      });
    },

    async getNewCareMainTableData(recordID, recordsCode) {
      let returnData = [];
      let params = {
        recordID,
        recordsCode,
      };
      await GetNewCareMain(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.length > 0) {
            res.data.forEach((item) => {
              if (item.frequency) {
                item.frequency = item.frequency + "";
              }
            });
          }
          returnData = res.data;
        }
      });
      return returnData;
    },
    fixTable() {
      this.showRecordArr = [true, false];
      this.currentRecord = undefined;
      this.careMainList = [];
    },

    //总览弹窗关闭
    dialogClose() {
      this.showRecordArr = [true, false];
      this.careMainList = [];
      this.getPumpingRecordList();
    },
    async pumpingEndCheck(recordID, endDate, endTime) {
      let message = "";
      let params = {
        recordID,
        endDate,
        endTime,
      };
      await PumpingEndCheck(params).then((res) => {
        if (this._common.isSuccess(res)) {
          message = res.data;
        }
      });
      return message;
    },
    //获取合并数组
    getSpanArr(data) {
      this.spanArr = [];
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].sort == data[i - 1].sort) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    //相同项合并
    cellMerge({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        //合并第一列和第二列
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
  },
};
</script>

<style lang='scss' >
.pumping {
  height: 100%;
  .medicine-list {
    height: 100%;
    overflow-y: auto;
    padding: 10px;
    box-sizing: border-box;
    border: 1px solid #dcdfe6;
  }
  .care-main-table {
    .cell {
      .time-column {
        width: 62px;
      }
      .el-input__prefix {
        display: none;
      }

      .el-input {
        padding: 0;
        text-align: center;
        .el-input__inner {
          padding: 0 5px;
        }
      }
      .el-input--suffix {
        .el-input__inner {
          text-align: left;
        }
      }
      .el-checkbox {
        float: left;
      }
      .one-check {
        float: none;
      }
    }
  }
}
</style>