<template>
  <div class="key-board">
    <div class="mask" v-if="isShow" @click="ctrlValue('hide-keyboard', 'hide-keyboard')"></div>
    <transition name="show">
      <div class="key-main" v-if="isShow" @click="(event) => event.stopPropagation()">
        <table>
          <tr v-for="(row, rIndex) in rows" :key="rIndex">
            <td v-for="(col, cIndex) in row.cols" :key="cIndex">
              <div v-if="col.className == 'num'" class="btn" :class="col.className" @click="numValue(col.value)">
                {{ col.value }}
              </div>
              <div v-if="col.className == 't-num'" class="btn" :class="col.className" @click="tNumValue(col.value)">
                {{ col.value }}
              </div>
              <div
                v-if="col.className == 'prev'"
                class="btn"
                :class="col.className"
                @click="ctrlValue(col.className, col.value)"
              >
                <i class="iconfont icon-previous" />
              </div>
              <div
                v-if="col.className == 'next'"
                class="btn"
                :class="col.className"
                @click="ctrlValue(col.className, col.value)"
              >
                <i class="iconfont icon-next" />
              </div>
              <div
                v-if="col.className == 'del'"
                class="btn"
                :class="col.className"
                @click="ctrlValue(col.className, col.value)"
              >
                <i class="iconfont icon-backspace" />
              </div>
              <div
                v-if="col.className == 'clear'"
                class="btn"
                :class="col.className"
                @click="ctrlValue(col.className, col.value)"
              >
                清空
              </div>
              <div
                v-if="col.className == 'hide-keyboard'"
                class="btn"
                :class="col.className"
                @click="ctrlValue(col.className, col.value)"
              >
                <i class="iconfont icon-hide-keyboard" />
              </div>
            </td>
          </tr>
        </table>
      </div>
    </transition>
  </div>
</template>
<script>
import "./css/keyBoard.css";
import "./iconfont/iconfont.css";
export default {
  props: {
    show: {
      // 是否显示键盘
      require: true,
      default: false,
    },
    output: {
      // 接收键盘值的Dom
      require: true,
    },
    typeName: {},
  },
  data() {
    return {
      // 是否显示键盘
      isShow: false,
      // 接收键盘值的Dom
      outputDom: undefined,
      // 键盘上按键
      rows: [
        {
          cols: [
            {
              className: "t-num",
              value: "35.",
            },
            {
              className: "num",
              value: "1",
            },
            {
              className: "num",
              value: "2",
            },
            {
              className: "num",
              value: "3",
            },
            {
              className: "prev",
              value: "prev",
            },
          ],
        },
        {
          cols: [
            {
              className: "t-num",
              value: "36.",
            },
            {
              className: "num",
              value: "4",
            },
            {
              className: "num",
              value: "5",
            },
            {
              className: "num",
              value: "6",
            },
            {
              className: "next",
              value: "next",
            },
          ],
        },
        {
          cols: [
            {
              className: "t-num",
              value: "37.",
            },
            {
              className: "num",
              value: "7",
            },
            {
              className: "num",
              value: "8",
            },
            {
              className: "num",
              value: "9",
            },
            {
              className: "del",
              value: "del",
            },
          ],
        },
        {
          cols: [
            {
              className: "t-num",
              value: "38.",
            },
            {
              className: "num",
              value: "/",
            },
            {
              className: "num",
              value: "0",
            },
            {
              className: "num",
              value: ".",
            },
            {
              className: "clear",
              value: "clear",
            },
          ],
        },
        {
          cols: [
            {
              className: "t-num",
              value: "39.",
            },
            {
              className: "t-num",
              value: "40.",
            },
            {
              className: "t-num",
              value: "41.",
            },
            {
              className: "t-num",
              value: "NA",
            },
            {
              className: "hide-keyboard",
              value: "hide-keyboard",
            },
          ],
        },
      ],
    };
  },
  watch: {
    show(newValue) {
      if (newValue) {
        this.isShow = newValue;
      } else {
        this.isShow = newValue;
        if (this.outputDom) {
          this.outputDom.classList.remove("focus-border");
        }
      }
    },
    output(newValue) {
      this.outputDom = newValue;
      if (this.outputDom && this.outputDom.tagName == "INPUT") {
        this.isShow = true;
        this.outputDom.classList.add("focus-border");
      }
    },
  },
  methods: {
    // 数字类0-9、/、.原样追加输出
    numValue(value) {
      if (this.outputDom) {
        this.outputDom.value += value;
        this.triggerChange();
      }
    },
    // 体温类35.-41.、NA原样输出，不追加
    tNumValue(value) {
      if (this.outputDom) {
        this.outputDom.value = value;
        this.triggerChange();
      }
    },
    // 特殊类型的按钮
    ctrlValue(className, value) {
      // 隐藏键盘
      if (className === "hide-keyboard") {
        if (this.outputDom) {
          this.outputDom.focus();
          this.outputDom.blur();
          this.outputDom.classList.remove("focus-border");
        }
        this.$emit("hide");
      }
      // 上一个焦点
      if (className === "prev") {
        this.setDomFocus("prev");
      }
      // 下一个焦点
      if (className === "next") {
        this.setDomFocus("next");
      }
      // 删除
      if (className === "del") {
        if (this.outputDom) {
          let retValue = this.outputDom.value;
          retValue = retValue.substring(0, retValue.length - 1);
          this.outputDom.value = retValue;
          this.triggerChange();
        }
      }
      // 清空
      if (className === "clear") {
        if (this.outputDom) {
          this.outputDom.value = "";
          this.triggerChange();
        }
      }
    },
    // 设置焦点
    setDomFocus(moveFlag) {
      let elements;
      // 获取指定name的列表
      if (this.typeName) {
        elements = document.getElementsByName(this.typeName);
      } else {
        elements = document.getElementsByTagName("INPUT");
      }
      for (var i = 0; i < elements.length; i++) {
        if (elements[i] == this.outputDom) {
          if (moveFlag == "prev") {
            // 前一个且当前是第一个则直接返回
            if (i == 0) {
              return;
            }
            this.outputDom.focus();
            this.outputDom.blur();
            this.outputDom.removeAttribute("readonly");
            this.outputDom.classList.remove("focus-border");
            this.outputDom = elements[i - 1];
            this.outputDom.setAttribute("readonly", "readonly");
            this.outputDom.focus();
            this.addEvent();
            this.outputDom.classList.add("focus-border");
          } else {
            // 后一个且当前是最后一个
            if (i == elements.length - 1) {
              return;
            }
            this.outputDom.focus();
            this.outputDom.blur();
            this.outputDom.removeAttribute("readonly");
            this.outputDom.classList.remove("focus-border");
            this.outputDom = elements[i + 1];
            this.outputDom.setAttribute("readonly", "readonly");
            this.outputDom.focus();
            this.addEvent();
            this.outputDom.classList.add("focus-border");
          }
          break;
        }
      }
    },
    // 添加监听事件
    addEvent() {
      if (this.outputDom.addEventListener) {
        this.outputDom.addEventListener("input", function () {});
      } else {
        this.outputDom.attachEvent("oninput", function () {});
      }
    },
    // 手动触发事件
    triggerChange() {
      if (this.outputDom.fireEvent) {
        this.outputDom.fireEvent("oninput");
      } else {
        let ev = document.createEvent("HTMLEvents");
        ev.initEvent("input", false, true);
        this.outputDom.dispatchEvent(ev);
      }
    },
  },
};
</script>