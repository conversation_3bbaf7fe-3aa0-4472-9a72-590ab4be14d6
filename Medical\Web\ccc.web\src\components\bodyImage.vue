<!--
 * FilePath     : \src\components\bodyImage.vue
 * Author       : 来江禹
 * Date         : 2024-06-03 09:52
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-29 19:42
 * Description  : 查看人体图组件
 * CodeIterationRecord:
 -->
<template>
  <div class="body-image">
    <el-button v-if="type == 'button'" type="text" @click="openBodyImg()">人形图</el-button>
    <el-tooltip v-if="type == 'tooltip'" content="人体图">
      <i @click="openBodyImg()" class="iconfont icon-body"></i>
    </el-tooltip>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="bodyDialogTitle"
      :visible.sync="showBodyImageFlag"
      :append-to-body="true"
      custom-class="body-image-dialog"
    >
      <img :src="bodyImageStr" class="body-pic" />
    </el-dialog>
  </div>
</template>
<script>
import { GetBodyImg } from "@/api/Handover";
export default {
  props: {
    //人体图显示类型：tooltip图标，button：按钮文字
    type: {
      type: String,
      default: "tooltip",
    },
    handoverID: {
      type: String,
      default: "",
    },
    //查看人体图患者姓名
    patientName: {
      type: String,
      default: "",
    },
    bodyStr: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      //弹窗标题
      bodyDialogTitle: "",
      //人体图显示开关
      showBodyImageFlag: false,
      //人体图图片
      bodyImageStr: "",
    };
  },
  methods: {
    /**
     * @description: 查看人体图
     * @return
     */
    async openBodyImg() {
      this.bodyDialogTitle = `${this.patientName} - 人体图`;
      this.showBodyImageFlag = true;
      // 如果有传人体图字符串，直接展示
      if (this.bodyStr) {
        this.bodyImageStr = `data:image/png;base64,${this.bodyStr}`;
        return;
      }
      let params = {
        handOverID: this.handoverID,
      };
      await GetBodyImg(params).then((response) => {
        if (response) {
          this.bodyImageStr = `data:image/png;base64,${response.data}`;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.body-image {
  .icon-body {
    color: $base-color;
  }
}
.body-image-dialog.el-dialog {
  margin-top: 14vh !important;
  width: 30%;
  height: 60%;
  .body-pic {
    width: 100%;
  }
}
</style>
