<!--
 * FilePath     : \src\pages\externalTransfer.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-19 15:10
 * Description  : 第三方系统串CCC，统一通过此页面跳转
 * 其他系统跳转链接示例：
 * 1、http://localhost:8088/externalTransfer?token=xxx&clientType=1&language=1&functionID=0&caseNumber=xxx&stationCode=xxx&isDialog=true
 * 2、http://localhost:8088/externalTransfer?clientType=1&language=1&functionID=0&caseNumber=xxx&userId=xx&password=xxx&isDialog=true
 * 地址：http://localhost:8080/externalTransfer
 * 参数：
 * 1、token：验证密钥
 * 2、clientType：系统类别，1PC端，2移动端
 * 3、functionID：功能ID，对应MenuList表中的MenuListID，根据此ID找到要跳转的路由,为0则跳主页
 * 4、caseNumber：病人住院号，根据此号确定病人信息
 * 5、stationCode：病区代码
-->
<template></template>
<script>
import { GetHospitalList, GetHospitalInfo, GetLanguage, GetAppConfigSetting } from "@/api/Setting";
import {
  UserCheck,
  GetSession,
  userLogin,
  UpdateSessionStationID,
  SetSessionByHospitalLanguage,
  CheckLoginAndCA,
  SyncEmployeeCAData,
} from "@/api/User";
import { GetInpatientDataViewByCaseNumber } from "@/api/Inpatient";
import { GetSettingSwitch } from "@/api/SettingDescription";
import { GetStationListByCode } from "@/api/Station";
import { GetMenuByID } from "@/api/Menu";
import { mapGetters } from "vuex";
import { wp } from "@/utils/web-proxy";
export default {
  data() {
    return {
      // 身份验证token
      token: undefined,
      // 客户端类型
      clientType: undefined,
      // 功能ID
      functionID: undefined,
      // 患者住院号
      caseNumber: undefined,
      // 病区序号
      stationCode: undefined,
      // 银川市一单点登录Token
      oauthToken: undefined,
      // 当前医院
      hospital: undefined,
      // 系统使用语言
      language: 1,
      // 用户名
      userId: "",
      // 密码
      password: "",
      // 登录用户
      user: "",
      // 传入加密后的数据
      encryptionParameters: "",
      // 银川妇幼单点登录Token
      accessToken: undefined,
      // 不需要验证Token，默认需要
      noToken: false,
      //Ukey拔出时清除登录状态API
      removeSessionAPI: undefined,
      //该医院是否使用UKey并进行登录检核
      checkLoginAndUkeyCAFlag: false,
    };
  },
  computed: {
    ...mapGetters({
      localLanguageList: "getLanguageList",
    }),
  },
  async created() {
    await this.getRemoveSessionAPI();
    // 系统更新后跳转此画面进行用户认证
    let transferParams = this.$route.params;
    if (transferParams && transferParams.refreshFlag) {
      // 刷新画面为更新后的系统
      window.location.reload();
    }
    // 将跳转参数保存到session，为系统更新后自动刷新做准备
    this.$store.commit("session/setExternalParams", this.$route.query);
    this.accessToken = this.$route.query.access_token;
    this.noToken = this.$route.query.noToken;
    this.oauthToken = this.$route.query.oauth_token;
    this.token = this.$route.query.token;
    this.clientType = this.$route.query.clientType;
    this.hospitalID = this.$route.query.hospitalID;
    this.functionID = this.$route.query.functionID;
    this.caseNumber = this.$route.query.caseNumber;
    this.stationCode = this.$route.query.stationCode;
    this.encryptionParameters = this.$route.query.encryptionParameters;
    this.skipCA = this.$route.query.skipCA;
    if (this.$route.query.language) {
      this.language = this.$route.query.language;
    } else {
      this.language = 1;
    }
    await this.getLanguage();
    this.userId = this.$route.query.userId;
    this.password = this.$route.query.password;
    if (!this.clientType) {
      if (this._common.isPC()) {
        this.clientType = 1;
      } else {
        this.clientType = 3;
      }
    }
    await this.init();
  },
  methods: {
    async init() {
      // 无Token访问
      if (this.noToken) {
        // 必传参数检核
        if (!this.hospitalID) {
          this._showTip("warning", "缺少参数：hospitalID");
          return;
        }
        if (!this.stationCode) {
          this._showTip("warning", "缺少参数：stationCode参数");
          return;
        }
        if (!this.language) {
          this._showTip("warning", "缺少参数：language参数");
          return;
        }
        //
        let params = {
          hospitalID: this.hospitalID,
          language: this.language,
        };
        let token = "";
        await SetSessionByHospitalLanguage(params).then((res) => {
          token = res.data ?? "";
        });
        token && this.$store.commit("auth/setToken", token);
        // 必传参数存session，方便业务页面调用判断
        this._common.session("noToken", this.noToken);
        this._common.session("hospitalID", this.hospitalID);
        this.jumpPage();
        return;
      }
      if (this.hospitalID) {
        let params = {
          hospitalID: this.hospitalID,
        };
        await GetHospitalInfo(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.hospital = result.data;
          }
        });
      } else {
        await GetHospitalList().then((result) => {
          if (this._common.isSuccess(result) && result.data) {
            this.hospital = result.data[0];
          }
        });
      }
      this.$store.commit("session/setHospitalInfo", this.hospital);
      if (this.hospital) {
        // 单点登录，目前是银川市一及银川妇幼
        if (this.hospital.singleSignOn && this.hospital.hospitalID !== "1") {
          let params = {
            userId: "",
            password: "",
            language: this.language,
            clientType: this.clientType,
            hospitalID: this.hospital.hospitalID,
            singleSignOn: true,
          };
          if (this.oauthToken) {
            params.extenion = this.oauthToken;
          }
          //宁夏妇幼，单点登陆和跳转同步进行时，密码优先
          if (this.hospital.hospitalID == "5") {
            if (this.accessToken) {
              params.extenion = this.accessToken;
            } else {
              params.userId = this.userId;
              params.password = this.password;
            }
          }
          // }
          //银川单点登录需要每次登录
          if (this.hospital.hospitalID == "3") {
            this.$store.commit("auth/exit");
            this.$store.commit("session/setUser", undefined);
            this.$store.commit("session/setCurrentPatient", undefined);
            this.$store.commit("session/setPatientInfo", undefined);
          }
          await userLogin(params).then(async (result) => {
            if (this._common.isSuccess(result)) {
              if (result.data && result.data.token && result.data.token != "null") {
                await this.setLoginInfo(result.data.token, result.data.hasCA);
                return;
              } else {
                if (result.data) {
                  window.location.href = result.data;
                }
                return;
              }
            } else {
              // 页面跳转
              this.jumpPage();
              return;
            }
          });
          return;
        }
        // 宏力集成平台跳转
        if (this.accessToken && this.hospital.hospitalID === "1") {
          // 验证token是否有效
          let params = {
            token: this.accessToken,
          };
          await UserCheck(params).then(async (result) => {
            if (this._common.isSuccess(result)) {
              await this.setLoginInfo(this.accessToken, result.data.hasCA);
            }
          });
          return;
        }
        if (this.token) {
          if (!this.stationCode) {
            this._showTip("warning", "获取科室失败！");
            return;
          }
          // 验证token是否有效
          let params = {
            token: this.token,
            clientType: this.clientType,
            stationCode: this.stationCode,
            caseNumber: this.caseNumber,
          };
          await UserCheck(params).then(async (result) => {
            if (this._common.isSuccess(result)) {
              await this.setLoginInfo(this.token, result.data.hasCA);
            }
          });
          return;
        }
        if (this.encryptionParameters) {
          let param = {
            SettingTypeCode: "CheckLoginAndUkeyCA",
            HospitalID: this.hospital.hospitalID,
          };
          await GetSettingSwitch(param).then((response) => {
            if (this._common.isSuccess(response)) {
              this.checkLoginAndUkeyCAFlag = response.data;
            }
          });
          let checkSuccess = await this.getCALoginCheck();
          let params = {
            encryptionParameters: this.encryptionParameters,
            ukeyPassedFlag: checkSuccess,
          };
          await userLogin(params).then(async (result) => {
            if (this._common.isSuccess(result)) {
              if (result.data && result.data.token && result.data.token != "null") {
                if (result.data.linkParameter) {
                  this.token = result.data.token;
                  this.clientType = result.data.linkParameter.clientType;
                  this.functionID = result.data.linkParameter.functionID;
                  this.caseNumber = result.data.linkParameter.caseNumber;
                  this.stationCode = result.data.linkParameter.stationCode;
                  this.encryptionParameters = result.data.linkParameter.encryptionParameters;
                  this.userId = result.data.linkParameter.userId;
                  if (result.data.linkParameter.language) {
                    this.language = result.data.linkParameter.language;
                  } else {
                    this.language = 1;
                  }
                  let languageItem = this.languageList.find((language) => {
                    return language.languageID == this.language;
                  });
                  if (languageItem) {
                    this.setLanguage(languageItem);
                  }
                  await this.setLoginInfo(result.data.token, result.data.hasCA, result.data.linkParameter);
                  return;
                } else {
                  this.jumpPage();
                  return;
                }
              } else {
                this.jumpPage();
                return;
              }
            } else {
              // 页面跳转
              this.jumpPage();
              return;
            }
          });

          return;
        } else {
          // 其他系统带userId和password跳转，模拟登录
          if (!this.userId || !this.password) {
            this._showTip("warning", "用户名或密码为空！");
            return;
          }
          let params = {
            userId: this.userId,
            password: this.password,
            language: this.language,
            clientType: this.clientType,
            hospitalID: this.hospital.hospitalID,
          };
          await userLogin(params).then(async (result) => {
            if (this._common.isSuccess(result)) {
              if (result.data && result.data.token && result.data.token != "null") {
                this.token = result.data.token;
                await this.setLoginInfo(result.data.token, result.data.hasCA);
                return;
              } else {
                this.jumpPage();
                return;
              }
            } else {
              // 页面跳转
              this.jumpPage();
              return;
            }
          });
          return;
        }
      }
      // 页面跳转
      this.jumpPage();
    },
    // 设置登录相关信息
    async setLoginInfo(token, hasCA, param) {
      if (token) {
        this.token = token;
      }
      //登录成功
      this.$store.commit("auth/login");
      this.$store.commit("auth/setToken", token);
      // 获取session
      await GetSession().then((result) => {
        if (this._common.isSuccess(result)) {
          let user = {
            roles: result.data.authorityID,
            userID: result.data.userID,
            userName: result.data.employeeName,
            stationID: result.data.stationID,
            stationCode: result.data.stationCode,
          };
          this.user = user;
          // 组装登录参数，为系统更新后自动重新登录准备
          let params = {
            userId: result.data.userName,
            password: result.data.password,
            language: result.data.languageID,
            clientType: result.data.clientType,
            hospitalID: result.data.hospitalID,
          };
          this.$store.commit("session/setUser", user);
          this.$store.commit("session/setLoginParams", params);
          if (hasCA && !this.skipCA) {
            this.$router.replace({ path: "/caAuthorize" });
          } else {
            // 页面跳转
            this.jumpPage(param);
          }
        }
      });
    },
    // 页面跳转
    async jumpPage(param) {
      if (!this.noToken && !this.token) {
        this.$router.replace({ path: "/login" });
        return;
      }
      if (this.stationCode) {
        let params = {
          stationCode: this.stationCode,
        };
        let stationID = "";
        await GetStationListByCode(params).then((result) => {
          if (this._common.isSuccess(result) && result.data) {
            stationID = result.data.id;
            if (this.noToken) {
              this._common.session("stationID", stationID);
              this.$route.query.stationID = stationID;
            }
          }
        });
        // 如果传的病区和当前护士所属病区不一致，则以传入为主,更新后端缓存
        if (!this.noToken && this.user.stationID != stationID) {
          await this.updateSessionStationID(stationID);
        }
      }
      this.$store.commit("session/setCurrentPatient", {});
      // 获取病人信息
      if (this.caseNumber) {
        let stationID = "";
        let params = {
          caseNumber: this.caseNumber,
        };
        await GetInpatientDataViewByCaseNumber(params).then((result) => {
          if (this._common.isSuccess(result)) {
            stationID = result.data.stationID;
            let currentPatient = {
              bedNumber: result.data.bedNumber,
              inpatientID: result.data.inpatientID,
              stationID: result.data.stationID,
              caseNumber: result.data.caseNumber,
              chartNo: result.data.chartNo,
              admissionDate: result.data.admissionDate,
              departmentCode: result.data.departmentCode,
              localCaseNumber: result.data.localCaseNumber,
            };
            this.$store.commit("session/setCurrentPatient", currentPatient);
            // 解决同床位出院病人
            if (result.data.dischargeFlag) {
              this.$route.query.inpatientID = result.data.inpatientID;
              this.$route.query.isDischarge = true;
              if (param) {
                param.inpatientID = result.data.inpatientID;
                param.isDischarge = true;
              }
            } else {
              this.$route.query.bedNumber = result.data.bedNumber;
              if (param) {
                param.bedNumber = result.data.bedNumber;
              }
            }
          }
        });
        // 如果传的病人所属病区和当前护士所属病区不一致，则以病人为主,更新后端缓存
        if (!this.noToken && this.user.stationID != stationID) {
          await this.updateSessionStationID(stationID);
        }
      }
      //系统更新后跳转此画面进行用户认证，此时需要回到认证前画面
      if (this.router) {
        // 跳转到相应的路由
        this.$router.replace(this.router);
        return;
      }
      // 获取功能对应的菜单
      let menuParams = {
        menuListID: this.functionID,
      };
      if (!this.functionID || this.functionID == 0) {
        this.$router.replace({ path: "/patientList", query: this.$route.query });
      } else {
        await GetMenuByID(menuParams).then((result) => {
          if (this._common.isSuccess(result)) {
            // 跳转到相应的路由
            this.$router.replace({ path: result.data.router, query: param ? param : this.$route.query });
          }
        });
      }
    },
    // 更新服务端缓存
    async updateSessionStationID(stationID) {
      this.user.stationID = stationID;
      this.$store.commit("session/setUser", this.user);
      let params = {
        stationID: stationID,
      };
      await UpdateSessionStationID(params).then((result) => {
        if (this._common.isSuccess(result)) {
        }
      });
    },
    async getLanguage() {
      if (this.localLanguageList && this.localLanguageList.length > 0) {
        this.languageList = this.localLanguageList;
      } else {
        await GetLanguage().then((result) => {
          if (this._common.isSuccess(result)) {
            if (result.data) {
              this.languageList = [];
              result.data.forEach((language) => {
                this.languageList.push({
                  languageID: language.language,
                  languageName: language.description.trim(),
                  languageLocale: language.typeValue.trim(),
                });
              });
              this.$store.commit("session/setLanguageList", this.languageList);
            }
          }
        });
      }
      let languageItem = this.languageList.find((language) => {
        return language.languageID == this.language;
      });
      if (languageItem) {
        this.setLanguage(languageItem);
      }
    },
    // 设置当前语言
    setLanguage(language) {
      this.languageID = language.languageID;
      this.$store.commit("session/setLanguage", language.languageLocale);
      this._common.storage("language", language.languageLocale);
      this.$i18n.locale = language.languageLocale;
    },
    // 获取CA登录检核
    async getCAUserInfoPromise() {
      return new Promise((resolve, reject) => {
        wp.ca.getCAUserInfo(this.removeSessionAPI, (response) => {
          if (response.data) {
            resolve({
              caUserID: response.data.CAUserID,
              stampImageBase64: response.data.StampImageBase64,
            });
          } else {
            reject(console.log("getCAUserInfo调用异常"));
          }
        });
      });
    },

    // 在 async 函数中使用 await 来确保顺序执行
    async getCALoginCheck() {
      let checkSuccess = false;
      if (!this.checkLoginAndUkeyCAFlag) {
        return checkSuccess;
      }
      try {
        let { caUserID, stampImageBase64 } = await this.getCAUserInfoPromise();
        if (!caUserID || !stampImageBase64) {
          return checkSuccess;
        }
        // 检核比对登录账户与CA账户的一致性
        let checkParams = {
          encryptionParameters: this.encryptionParameters,
          caUserID: caUserID,
        };
        let result = await CheckLoginAndCA(checkParams);
        if (this._common.isSuccess(result)) {
          if (result.data) {
            checkSuccess = result.data;
            if (checkSuccess) {
              // 呼叫同步CA
              let params = {
                CAUserID: caUserID,
                StampImageBase64: stampImageBase64,
              };
              await SyncEmployeeCAData(params);
            }
          }
        }
      } catch (error) {
        console.log("getCALoginCheck调用错误");
      }
      return checkSuccess;
    },
    //获取Ukey拔出时清除登录状态的API
    async getRemoveSessionAPI() {
      let params = {
        settingType: "Configs",
        settingCode: "RemoveSessionAPI",
      };
      await GetAppConfigSetting(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.removeSessionAPI = result.data;
        }
      });
    },
  },
};
</script>
