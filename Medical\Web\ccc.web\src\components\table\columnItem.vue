<!--
 * FilePath     : \ccc.web\src\components\table\headerList.vue
 * Author       : 郭鹏超
 * Date         : 2021-09-22 10:53
 * LastEditors  : 郭鹏超
 * LastEditTime : 2021-11-18 16:51
 * Description  : 表头组件
-->
<template>
  <div>
    <div v-if="item.columnStyle == 'Date'">
      <el-date-picker
        v-model="scope.row[item.prop]"
        type="date"
        :clearable="false"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 100%"
      ></el-date-picker>
    </div>
    <!-- 时间 -->
    <div v-if="item.columnStyle == 'Time'">
      <el-time-picker
        v-model="scope.row[item.prop]"
        value-format="HH:mm"
        format="HH:mm"
        style="width: 100%"
      ></el-time-picker>
    </div>
    <!-- 日期时间文字 -->
    <div v-if="item.columnStyle.indexOf('date') != -1 || item.columnStyle.indexOf('time') != -1">
      <div v-formatTime="{ value: scope.row[item.prop], type: item.columnStyle }"></div>
    </div>
    <!--文字 -->
    <div v-if="item.columnStyle == 'text'">
      <div v-html="scope.row[item.prop]"></div>
    </div>
    <div v-if="item.columnStyle == 'select'">
      <el-select style="width: 100%" v-model="scope.row[item.prop]" placeholder="请选择">
        <el-option
          v-for="(item, index) in item.options"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
    <div v-if="item.columnStyle == 'input'">
      <el-input style="width: 100%" v-model="scope.row[item.prop]"></el-input>
    </div>
    <div v-if="item.columnStyle == 'check' && item.options">
      <el-checkbox-group v-model="scope.row[item.prop]">
        <el-checkbox v-for="(item, index) in item.options" :key="index" :label="item.value">
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: {},
    },
    scope: {
      type: Object,
      default: {},
    },
  },
};
</script>
