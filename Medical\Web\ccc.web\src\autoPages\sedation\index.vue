<!--
 * FilePath     : \src\autoPages\sedation\index.vue
 * Author       : 曹恩
 * Date         : 2023-08-21 11:00
 * LastEditors  : 马超
 * LastEditTime : 2024-10-09 15:21
 * Description  : 镇静评估专项
 * CodeIterationRecord: 
-->
<template>
  <specific-care
    class="patient-sedation"
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    :editFlag="checkResult"
    :drawerSize="supplementFlag ? '80%' : ''"
    :previewFlag = previewFlag
    @mainAdd="careMainAdd"
    @save="saveSedation"
    @getHandOverFlag="getHandOverFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @cancel="drawerClose"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <!-- 维护记录 -->
    <div slot="main-record">
      <packaging-table v-model="careMainTableData" :headerList="mianTableHeader">
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope">
          <el-tooltip content="修改" placement="top">
            <div class="iconfont icon-edit" @click="careMainAdd(scope.row)"></div>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <div class="iconfont icon-del" @click="deleteCareMain(scope.row)"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 弹窗内容 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
    >
      <div slot="header">
        <span class="label">日期:</span>
        <el-date-picker
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          class="drawer-content-header-date"
        ></el-date-picker>
        <el-time-picker
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          class="drawer-content-header-time"
        ></el-time-picker>
        <station-selector v-model="stationID" label="病区:" width="170"></station-selector>
        <dept-selector label="" width="150" v-model="departmentListID" :stationID="stationID"></dept-selector>
        <nurse-selector label="记录人员：" width="100" v-model="userID" :stationID="stationID" :disabled = "selectDisabled"></nurse-selector>
      </div>
      <tabs-layout ref="tabsLayout" :template-list="templateDatas" @change-values="changeValues" />
    </base-layout>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import nurseSelector from "@/components/selector/nurseSelector";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import { mapGetters } from "vuex";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import { GetBringToShiftSetting } from "@/api/Setting";
import { GetBringToNursingRecordFlagSetting } from "@/api/SettingDescription";
import {
  GetSedationAssessView,
  GetSedationTableView,
  SaveSedationCare,
  DeleteSedationCare,
} from "@/api/PatientSedation";
export default {
  components: {
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    baseLayout,
    nurseSelector,
    packagingTable,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      loading: false,
      patient: undefined,
      showTemplateFlag: false,
      drawerTitle: undefined,
      careMainTableData: [],
      drawerLoading: false,
      drawerLoadingText: undefined,
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      departmentListID: undefined,
      userID: undefined,
      templateDatas: [],
      assessDatas: [],
      handOverArr: [true, false],
      informPhysicianArr: [true, false],
      bringToNursingRecordArr: [true, false],
      settingHandOver: false,
      settingNursingRecord: false,
      sourceID: undefined,
      sourceType: undefined,
      supplementFlag: undefined,
      checkResult: true,
      mianTableHeader: [],
      careMainID: undefined,
      recordsCodeInfo: {},
      previewFlag: false,
      selectDisabled: false,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
    }),
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.supplementFlag = undefined;
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.supplementFlag = "*";
          this.bringToNursingRecordArr = [false, false];
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    async init() {
      if (this.$route.query.patientScheduleMainID) {
        this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
      }
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
      if (this.patientScheduleMainID || this.assessMainID || this.sourceID || this.sourceType) {
        this._sendBroadcast("setPatientSwitch", false);
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
      await this.getCareMainTableHeader();
      this.getBringHandOverSetting();
      this.getBringToNursingRecordFlagSetting();
      this.getTableView();
    },
    /*-------------专项护理组件逻辑-------------*/
    /**
     * description: 组件回传交班flag
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 通知医师标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.bringToNursingRecordArr[1] = flag;
    },
    /**
     * description: 弹窗关闭
     * param {*}
     * return {*}
     */
    drawerClose() {
      this.recordID = undefined;
      this.careMainID = undefined;
      this.templateDatas = [];
      this.assessDatas = [];
      this.showTemplateFlag = false;
    },
    /**
     * description: 弹窗开关
     * param {*} flag 开关动作
     * param {*} title 弹窗标题
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 评估组件回传数据
     * param {*} datas
     * return {*}
     */
    changeValues(datas) {
      this.assessDatas = datas;
    },
    /**
     * description: 获取主记录表头
     * return {*}
     * param {*}
     */
    async getCareMainTableHeader() {
      let params = {
        fileClassID: 116,
        fileClassSub: "SedationCareMain",
        useDescription: "1||Table",
      };
      this.mianTableHeader = [];
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.mianTableHeader = res.data;
        }
      });
    },
    /**
     * description: 获取是否带入交班配置
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "PatientSedation",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.handOverArr[0]) {
            this.settingHandOver = res.data;
          }
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * return {*}
     */
    getBringToNursingRecordFlagSetting() {
      let params = {
        settingTypeCode: "SedationAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },
    /**
     * description: 新增/修改镇静评估
     * return {*}
     */
    async careMainAdd(row) {
      this.openOrCloseDrawer(true, "镇静评估");
      if (row) {
        //是否仅本人操作
        this.checkResult = await this._common.checkActionAuthorization(this.user, row.userID);
        if (!this.checkResult) {
          this.openOrCloseDrawer(false);
          this._showTip("warning", "非本人不可操作");
          return;
        }
        if (this.supplementFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(this.user.userID,false,true,row.userID);
        this.selectDisabled = disabledFlag;
        this.previewFlag = !saveButtonFlag;
      }
        //修改
        this.careMainID = row.patientSedationCareMainID;
        this.assessDate = this._datetimeUtil.formatDate(row.assessDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(row.assessTime, "hh:mm");
        this.stationID = row.stationID;
        this.departmentListID = row.departmentListID;
        this.userID = row.userID;
      } else {
        if (this.supplementFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(this.user.userID,true,true,"");
        this.selectDisabled = disabledFlag;
        this.previewFlag = !saveButtonFlag;
      }
        //新增
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowDate("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.bedID = this.patient.bedID;
        this.bedNumber = this.patient.bedNumber;
        this.userID = this.user.userID;
      }
      this.$set(this.handOverArr, 1, row ? row.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, row && row.informPhysician ? true : false);
      this.$set(this.bringToNursingRecordArr, 1, row ? row.bringToNursingRecord : this.settingNursingRecord);
      await this.getAssessTemplate();
    },
    /**
     * description: 获取评估模板
     * param {*}
     * return {*}
     */
    async getAssessTemplate() {
      let param = {
        inpatientID: this.patient.inpatientID,
        departmentListID: this.patient.departmentListID,
        mappingType: "SedationAssess",
        age: this.patient.age,
      };
      this.drawerLoading = true;
      this.drawerLoadingText = "加载中……";
      await GetAssessRecordsCodeByDeptID(param).then((result) => {
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo) {
        this.openOrCloseDrawer(false);
        this._showTip("warning", "找不到评估模板！");
        this.drawerLoading = false;
        this.drawerLoadingText = "";
        return;
      }
      let params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        careMainID: this.careMainID,
        stationID: this.stationID,
        departmentListID: this.patient.departmentListID,
        age: this.patient.age,
        gender: this.patient.genderCode,
        dateOfBirth: this.patient.dateOfBirth,
      };
      this.templateDatas = [];
      await GetSedationAssessView(params).then((result) => {
        this.drawerLoading = false;
        this.drawerLoadingText = "";
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 镇静评估保存
     * return {*}
     */
    saveSedation() {
      if (!this.saveCheck) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        stationID: this.stationID,
        departmentListID: this.departmentListID,
        assessDate: this.assessDate,
        assessTime: this.assessTime,
        recordsCode: this.recordsCodeInfo.recordsCode,
        interventionID: this.recordsCodeInfo.interventionMainID,
        bringToShift: this.handOverArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
        informPhysician: this.informPhysicianArr[1],
        details: this.getDetails(),
        refillFlag: this.supplementFlag,
        patientScheduleMainID: this.patientScheduleMainID,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
      };
      if (this.careMainID) {
        params.patientSedationCareMainID = this.careMainID;
      }
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      SaveSedationCare(params).then((res) => {
        this.drawerLoading = false;
        this.drawerLoadingText = "";
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.openOrCloseDrawer(false);
          this.getTableView();
        }
      });
    },
    /**
     * description: 保存检核
     * param {*}
     * return {*}
     */
    saveCheck() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return false;
      }
      if (!this.departmentListID) {
        this._showTip("warning", "请选择科室");
        return false;
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        return false;
      }
      return true;
    },
    /**
     * description: 组装保存detail数据
     * return {*}
     */
    getDetails() {
      let details = [];
      if (!this.assessDatas.length) {
        return details;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return details;
      }
      this.assessDatas.forEach((item) => {
        let detail = {
          assessListID: item.assessListID,
          assessListGroupID: item.assessListGroupID,
        };
        if (item.controlerType.trim() == "C" || item.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = item.assessValue;
        }
        details.push(detail);
      });
      return details;
    },
    /**
     * description: 获取镇静评估表格数据
     * return {*}
     */
    getTableView() {
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      this.loading = true;
      GetSedationTableView(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.careMainTableData = res.data;
        }
      });
    },
    /**
     * description: 删除镇静评估数据
     * * param {*} row 当前行数据
     * return {*}
     */
    async deleteCareMain(row) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, row.userID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (!row || !row.patientSedationCareMainID) {
        return;
      }
      if (this.supplementFlag === "*") {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,row.userID);
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            careMainID: row.patientSedationCareMainID,
            supplementFlag: this.supplementFlag,
          };
          this.loading = true;
          DeleteSedationCare(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this.loading = false;
              this._showTip("success", "删除成功");
              this.getTableView();
            }
          });
        }
      });
    },
  },
};
</script>

<style  lang="scss" >
.patient-sedation {
  .base-header {
    .drawer-content-header-date {
      width: 120px;
    }
    .drawer-content-header-time {
      width: 80px;
    }
  }
}
</style>