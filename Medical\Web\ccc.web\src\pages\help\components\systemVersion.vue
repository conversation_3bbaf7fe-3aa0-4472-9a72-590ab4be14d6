<!--
 * FilePath     : \src\pages\help\components\systemVersion.vue
 * Author       : 苏军志
 * Date         : 2022-09-08 15:01
 * LastEditors  : 苏军志
 * LastEditTime : 2022-09-13 10:01
 * Description  : 版本更新日志
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="system-version" v-loading="loading" element-loading-text="加载中……">
    <div slot="header">
      <span>更新内容：</span>
      <el-input class="version-input" v-model="versionContext" placeholder="请输入内容" @keyup.enter.native="search()">
        <i slot="append" class="iconfont icon-search" @click="search"></i>
      </el-input>
    </div>
    <div class="version-collapse">
      <el-collapse class="collapse-boder" :accordion="true" v-for="(item, index) in versionList" :key="index">
        <el-collapse-item>
          <template slot="title">
            <span class="title-span">
              版本：{{
                item.version + " —— 更新时间：" + _datetimeUtil.formatDate(item.updateTime, "yyyy-MM-dd hh:mm")
              }}
            </span>
          </template>
          <div class="version-context-wrap">
            <div class="version-context">
              <div class="context">
                <span class="label">新增功能</span>
                <div class="add-function" v-html="item.addFunction"></div>
              </div>
              <div class="context">
                <span class="label">新增功能</span>
                <div class="bug-fix" v-html="item.bugFix"></div>
              </div>
              <div class="context">
                <span class="label">新增功能</span>
                <div class="system-optimization" v-html="item.systemOptimization"></div>
              </div>
            </div>
            <div class="file-list" v-if="item.fileList && item.fileList.length > 0">
              附件：
              <span
                class="file-name"
                v-for="(file, index) in item.fileList"
                :key="index"
                @click="versionFilePreview(index, item.fileList, item.version)"
              >
                {{ index + 1 + "、" + file.name }}
              </span>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="filePreviewDialogTitle"
      :visible.sync="showFilePreview"
      custom-class="no-footer"
    >
      <file-preview
        v-if="showFilePreview"
        :defaultFileIndex="defaultPreviewFileIndex"
        :datas="filePreviewData"
      ></file-preview>
    </el-dialog>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import filePreview from "@/components/FilePreview";
import { GetVersionBySystemCode } from "@/api/SystemVersion";
export default {
  components: {
    baseLayout,
    filePreview,
  },
  data() {
    return {
      loading: false,
      versionContext: "",
      versionList: [],
      cloneVersionList: [],
      showFilePreview: false,
      filePreviewDialogTitle: "",
      filePreviewData: [],
      defaultPreviewFileIndex: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    init() {
      let params = {
        systemCode: "CCC",
        updateFlag: true,
      };
      this.loading = true;
      GetVersionBySystemCode(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.versionList = res.data;
          this.cloneVersionList = this._common.clone(this.versionList);
        }
      });
    },
    //模糊查询
    search() {
      if (!this.versionContext) {
        this.versionList = this.cloneVersionList;
        return;
      }
      this.versionList = this.cloneVersionList.filter((version) => {
        let addFunction = version.addFunction.replace(/<[^>]+>/g, "");
        let bugFix = version.bugFix.replace(/<[^>]+>/g, "");
        let systemOptimization = version.systemOptimization.replace(/<[^>]+>/g, "");
        if (
          addFunction.indexOf(this.versionContext) != -1 ||
          bugFix.indexOf(this.versionContext) != -1 ||
          systemOptimization.indexOf(this.versionContext) != -1
        ) {
          return true;
        }
        return false;
      });
    },
    /**
     * description: 版本说明文件预览
     * param {*} index 文件序号
     * param {*} fileList 文件列表
     * param {*} version 当前选择行的版本号
     * return {*}
     */
    versionFilePreview(index, fileList, version) {
      this.filePreviewData = [];
      this.filePreviewDialogTitle = `${version}版本说明文档`;
      this.defaultPreviewFileIndex = index;
      fileList.forEach((file) => {
        this.filePreviewData.push(file.filePath);
      });
      this.showFilePreview = true;
    },
  },
};
</script>

<style lang="scss">
.system-version {
  .version-input {
    width: 200px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .version-collapse {
    .el-collapse-item {
      &.is-active {
        .title-span {
          color: $base-color;
          font-weight: bold;
        }
      }
      .title-span {
        margin-left: 10px;
      }
    }
    .el-collapse-item__content {
      padding: 0 10px 5px 10px;
      .version-context-wrap {
        .version-context {
          display: flex;
          background-color: #fffff0;
          .context {
            width: calc(100% / 3);
            margin-left: 10px;
            padding: 5px 10px;
            border: 1px solid $base-color;
            &:first-child {
              margin-left: 0;
            }
            .label {
              color: #ff0000;
              font-weight: bold;
            }
          }
        }
        .file-list {
          margin-top: 5px;
          .file-name {
            display: inline-block;
            margin-right: 10px;
            cursor: pointer;
            &:hover {
              color: $base-color;
              border-bottom: 1px solid $base-color;
            }
          }
        }
      }
    }
  }
}
</style>