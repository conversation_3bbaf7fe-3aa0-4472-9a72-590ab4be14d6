<!--
 * FilePath     : \src\autoPages\dictionaryMaintain\queryNursingIntervention.vue
 * Author       : 来江禹
 * Date         : 2024-05-23 11:35
 * LastEditors  : 来江禹
 * LastEditTime : 2024-06-07 16:25
 * Description  : 措施库编码及定义查询页面
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="search-intervention-main" headerHeight="auto" :showFooter="true">
    <div slot="header">
      <span>CCC码：</span>
      <el-input
        v-model="interventionCode"
        @keyup.enter.native="filterData"
        placeholder="输入措施内容名称"
        class="search-input"
        clearable
        @clear="filterData"
      >
        <i slot="append" class="iconfont icon-search" @click="filterData"></i>
      </el-input>
      <label v-if="actionTypeArr.length">措施类别：</label>
      <el-select
        v-if="actionTypeArr.length"
        v-model="actionType"
        clearable
        class="search-select"
        placeholder="请选择措施类型"
        @change="filterData"
      >
        <el-option v-for="(item, index) in actionTypeArr" :key="index" :label="item" :value="item"></el-option>
      </el-select>
      <label v-if="actionTypeNameArr.length">活动类型：</label>
      <el-select
        v-if="actionTypeNameArr.length"
        clearable
        filterable
        v-model="actionTypeName"
        class="search-select"
        placeholder="请选择活动类型"
        @change="filterData"
      >
        <el-option v-for="(item, index) in actionTypeNameArr" :key="index" :label="item" :value="item"></el-option>
      </el-select>
      <span>措施内容：</span>
      <el-input
        v-model="intervention"
        @keyup.enter.native="filterData"
        placeholder="输入措施内容名称"
        class="search-input"
        clearable
        @clear="filterData"
      >
        <i slot="append" class="iconfont icon-search" @click="filterData"></i>
      </el-input>
      <span>措施说明：</span>
      <el-input
        v-model="inforbuttonContent"
        @keyup.enter.native="filterData"
        placeholder="输入措施说明内容"
        class="search-input"
        clearable
        @clear="filterData"
      >
        <i slot="append" class="iconfont icon-search" @click="filterData"></i>
      </el-input>
    </div>
    <div slot-scope="data" class="data-table">
      <el-table
        :height="data.height - 10"
        v-loading="loading"
        element-loading-text="加载中……"
        border
        stripe
        :data="tableDataList"
        @cell-click="handleCellClick"
      >
        <el-table-column prop="id" label="流水号" :width="convertPX(120)" align="center"></el-table-column>
        <el-table-column prop="interventionCode" label="CCC码" :width="convertPX(150)" align="center"></el-table-column>
        <el-table-column prop="actionType" label="措施类别" :width="convertPX(220)" align="center"></el-table-column>
        <el-table-column
          prop="actionTypeName"
          label="活动类型"
          :width="convertPX(120)"
          align="center"
        ></el-table-column>
        <el-table-column prop="intervention" label="措施内容" :width="convertPX(180)"></el-table-column>
        <el-table-column prop="inforbuttonContent" label="措施说明" :min-width="convertPX(400)"></el-table-column>
      </el-table>
    </div>
    <div slot="footer">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-sizes="[25, 50, 100]"
        :page-size="pageSize"
        :total="tableDataCount"
        layout="sizes, prev, pager, next"
      ></el-pagination>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout.vue";
import { GetNursingInterventionMainList } from "@/api/Setting";
export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      tableDataList: [],
      nursingInterventionMainList: [],
      copyNursingInterventionMainList: [],
      loading: false,
      currentPage: 1,
      pageSize: 25,
      tableDataCount: undefined,
      intervention: "",
      inforbuttonContent: "",
      actionTypeArr: [],
      actionType: "",
      actionTypeNameArr: [],
      actionTypeName: "",
      interventionCode: "",
    };
  },
  created() {
    this.getTableData();
  },
  methods: {
    /**
     * @description: 获取表格数据
     * @return
     */
    getTableData() {
      GetNursingInterventionMainList().then((res) => {
        if (this._common.isSuccess(res)) {
          this.nursingInterventionMainList = res.data;
          this.copyNursingInterventionMainList = res.data;
          let actionTypes = this.nursingInterventionMainList.map((item) => item.actionType);
          this.actionTypeArr = [...new Set(actionTypes)];
          let actionTypeNames = this.nursingInterventionMainList.map((item) => item.actionTypeName);
          this.actionTypeNameArr = [...new Set(actionTypeNames)];
          this.tableDataList = this.currentChangePage(this.pageSize, this.currentPage);
          this.tableDataCount = this.nursingInterventionMainList.length;
        }
      });
    },
    /**
     * @description: 分页
     * @param size
     * @param current
     * @return
     */
    currentChangePage(size, current) {
      const tablePush = [];
      let array = JSON.parse(JSON.stringify(this.nursingInterventionMainList));
      array.forEach((item, index) => {
        if (size * (current - 1) <= index && index <= size * current - 1) {
          item.editFlag = false;
          tablePush.push(item);
        }
      });
      return tablePush;
    },
    /**
     * @description: 切换页数配置
     * @param val
     * @return
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.tableDataList = this.currentChangePage(val, this.currentPage);
    },
    /**
     * @description: 切页
     * @param val
     * @return
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      this.tableDataList = this.currentChangePage(this.pageSize, val);
    },
    /**
     * @description: 筛选数据方法
     * @return
     */
    filterData() {
      this.nursingInterventionMainList = [...this.copyNursingInterventionMainList];
      const filters = [
        { property: "intervention", value: this.intervention },
        { property: "inforbuttonContent", value: this.inforbuttonContent },
        { property: "actionType", value: this.actionType },
        { property: "actionTypeName", value: this.actionTypeName },
        { property: "interventionCode", value: this.interventionCode },
      ];
      filters.forEach((filter) => {
        if (filter.value) {
          this.nursingInterventionMainList = this.nursingInterventionMainList.filter((main) =>
            main[filter.property].includes(filter.value)
          );
        }
      });
      this.currentPage = 1;
      this.tableDataList = this.currentChangePage(this.pageSize, this.currentPage);
      this.tableDataCount = this.nursingInterventionMainList.length;
    },
    /**
     * @description: 单元格点击方法
     * @param row
     * @param column
     * @return
     */
    handleCellClick(row, column) {
      if (["interventionCode", "actionType", "actionTypeName"].includes(column.property)) {
        // 获取点击的单元格内容
        const cellContent = row[column.property];
        // 将内容复制到剪贴板
        this.copyToClipboard(cellContent);
      }
    },
    /**
     * @description: 把内容赋值到剪贴板
     * @param text
     * @return
     */
    copyToClipboard(text) {
      // 创建一个临时文本区域元素
      const textarea = document.createElement("textarea");
      // 将要复制的文本内容设置到文本区域
      textarea.textContent = text;
      // 将文本区域添加到DOM中
      document.body.appendChild(textarea);
      // 选择文本区域内的文本
      textarea.select();
      // 执行复制命令
      document.execCommand("copy");
      // 从DOM中移除文本区域
      document.body.removeChild(textarea);
      this._showTip("success", "内容已复制到剪贴板");
    },
  },
};
</script>

<style lang="scss">
.search-intervention-main {
  height: calc(100% - 15px);
  .search-input {
    width: 200px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .search-select {
    width: 180px;
  }
}
</style>