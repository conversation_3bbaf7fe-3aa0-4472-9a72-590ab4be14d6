<!--
 * FilePath     : \src\pages\nursingPlan\components\planOverview.vue
 * Author       : 苏军志
 * Date         : 2020-05-08 15:25
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-16 08:34
 * Description  : 
 -->
<template>
  <base-layout show-header>
    <div slot="header" class="nursing-plan-top">
      <div class="switch">
        显示措施说明：
        <el-switch @change="getData" v-model="switchIntervention" />
      </div>
      <div v-if="showSwitch" class="switch">
        集束护理：
        <el-checkbox v-model="switchClusterCareData" @change="getInterventionData" />
      </div>
      <div v-if="showSwitch" class="switch">
        护理问题：
        <el-checkbox v-model="switchNursingProblemData" @change="getInterventionData" />
      </div>
    </div>
    <div v-loading="loading" element-loading-text="加载中……" class="plan-wrap">
      <el-table
        v-loading="tableLoading"
        element-loading-text="展开中……"
        :span-method="cellMerge"
        border
        :data="showInterventionList"
        :row-class-name="tableRowClassName"
        class="planTable"
        height="90%"
        @row-click="addStyle"
      >
        <el-table-column label="护理问题/集束问题" prop="problem" width="160">
          <template slot-scope="intervention">
            {{ intervention.row.problem }}
            <span v-if="intervention.row.problemSourceInfo && intervention.row.problemSourceInfo.content">
              {{ "(" + intervention.row.problemSourceInfo.content + ")" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="护理目标" prop="nursingGoal" align="center" width="100"></el-table-column>
        <el-table-column label="措施" prop="intervention" min-width="200"></el-table-column>
        <el-table-column
          v-if="switchIntervention"
          label="措施说明"
          prop="inforbuttonContent"
          align="length"
          header-align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column label="频次" prop="frequency" width="100"></el-table-column>
        <el-table-column
          label="频次说明"
          prop="frequenyDescription"
          width="160"
          align="left"
          header-align="center"
        ></el-table-column>
        <el-table-column label="开始日期" prop="startDate" width="110" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="结束日期" prop="endDate" align="center" width="110">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.endDate, type: 'date' }"></span>
          </template>
        </el-table-column>
      </el-table>
      <el-button v-show="interventionList.length" class="show-button" type="primary" @click="save">展开排程</el-button>
    </div>
  </base-layout>
</template>

<script>
import { GetPatientInterventionList } from "@/api/Intervention";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { AddScheduleNow } from "@/api/PatientSchedule";
import baseLayout from "@/components/BaseLayout";
export default {
  props: {
    inpatientinfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  components: {
    baseLayout,
  },
  data() {
    return {
      loading: false,
      tableLoading: false,
      interventionList: [],
      showInterventionList: [],
      spanArr: [],
      pos: 0,
      problemID: "",
      switchIntervention: false,
      switchClusterCareData: true,
      switchNursingProblemData: true,
      showSwitch: false,
    };
  },
  mounted() {
    this.getShowSwitchSetting();
  },
  watch: {
    "inpatientinfo.inpatientID": {
      handler(newValue) {
        if (!newValue) return;
        this.getData();
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 展开排程
     * return {*}
     */
    save() {
      if (this.inpatientinfo == null) {
        this._showTip("error", "未查询到病人信息！");
        return;
      }
      let params = {
        inPatientID: this.inpatientinfo.inpatientID,
      };
      this.tableLoading = true;
      return AddScheduleNow(params).then((res) => {
        this.tableLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "排程展开成功！");
        }
      });
    },
    /**
     * description: 获取计划总览数据
     * return {*}
     */
    getData() {
      if (this.inpatientinfo == null) {
        this._showTip("error", "未查询到病人信息！");
        return;
      }
      this.loading = true;
      let params = {
        inPatientID: this.inpatientinfo.inpatientID,
        inforbutton: this.switchIntervention,
      };
      this.interventionList = [];
      GetPatientInterventionList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.loading = false;
          this.interventionList = res.data;
          this.getInterventionData();
          this.addClass(res.data);
          this.getSpanArr(res.data);
        }
      });
    },
    /**
     * description: 获取合并数组
     * return {*}
     * param {*} data
     */
    getSpanArr(data) {
      this.spanArr = [];
      this.spanTwoArr = [];
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].problem == data[i - 1].problem) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    /**
     * description: 相同项合并
     * return {*}
     * param {*} row
     * param {*} column
     * param {*} rowIndex
     * param {*} columnIndex
     */
    cellMerge({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        //合并第一列和第二列
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    /**
     * description: 同一问题添加相同class
     * return {*}
     * param {*} value
     */
    addClass(value) {
      var oldI;
      for (let i = 0; i < value.length; i++) {
        value[i].classValue = "click";
        if (i == 0) {
          oldI = i;
          value[i].classClickValue = "click_" + oldI;
        } else {
          if (value[i].problemID == value[i - 1].problemID) {
            value[i].classClickValue = "click_" + oldI;
          } else {
            oldI = i;
            value[i].classClickValue = "click_" + oldI;
          }
        }
      }
    },
    tableRowClassName(row) {
      return row.row.classValue + " " + row.row.classClickValue;
    },
    addStyle(row) {
      let tableView = document.getElementsByClassName("planTable")[0];
      let trViewList = tableView.getElementsByClassName(row.classValue);
      let clickTrViewList = tableView.getElementsByClassName(row.classClickValue);
      for (let i = 0; i < trViewList.length; i++) {
        trViewList[i].classList.remove("current-row");
      }
      for (let i = 0; i < clickTrViewList.length; i++) {
        clickTrViewList[i].classList.add("current-row");
      }
    },
    /**
     * description: 获取集束护理措施和护理问题措施
     * return {*}
     */
    getInterventionData() {
      this.showInterventionList = [];
      if (this.switchClusterCareData && this.switchNursingProblemData) {
        this.showInterventionList = this.interventionList;
      }
      if (this.switchClusterCareData) {
        this.showInterventionList = this.interventionList.filter((item) => {
          return item.problemType == "O";
        });
      }
      if (this.switchNursingProblemData) {
        let nursingProblemIntervention = this.interventionList.filter((item) => {
          return item.problemType != "O";
        });
        this.showInterventionList = this.showInterventionList.concat(nursingProblemIntervention);
      }
      this.getSpanArr(this.showInterventionList);
      this.addClass(this.showInterventionList);
    },
    /**
     * description: 获取显示措施说明开关
     * return {*}
     */
    getShowSwitchSetting() {
      let params = {
        settingTypeCode: "FilterPatientInterventionSwitch",
      };
      GetSettingSwitchByTypeCode(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.showSwitch = response.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.plan-wrap {
  height: 100%;
}
.show-button {
  margin-top: 10px !important;
}
.nursing-plan-top {
  width: 100%;
  .switch {
    margin-right: 5px;
    display: inline-block;
    .el-input {
      width: 110px;
      .el-input__inner {
        padding: 0 5px;
      }
      .el-input-group__append {
        padding: 0 5px;
      }
      i {
        color: #8cc63e;
      }
    }
  }
}
</style>
