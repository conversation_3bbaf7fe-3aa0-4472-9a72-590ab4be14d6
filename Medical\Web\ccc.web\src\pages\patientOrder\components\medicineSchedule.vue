<template>
  <base-layout headerHeight="auto" class="schedule-medicine">
    <div class="medicine-schedule-top" slot="header">
      <span class="label">开始日期:</span>
      <el-date-picker
        v-model="apiNeedData.startDate"
        type="date"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        :editable="false"
        style="width: 120px"
        size="mini"
        :clearable="false"
        :picker-options="checkStartDate"
        @change="getMedicTableData()"
      ></el-date-picker>
      <span class="label">开始时间:</span>
      <el-time-picker
        v-model="apiNeedData.startTime"
        style="width: 120px"
        size="mini"
        value-format="HH:mm"
        :editable="false"
        format="HH:mm"
        :clearable="false"
        @change="getMedicTableData()"
      ></el-time-picker>
      <span class="label">结束日期:</span>
      <el-date-picker
        v-model="apiNeedData.endDate"
        type="date"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        :picker-options="checkEndDate"
        :editable="false"
        style="width: 120px"
        size="mini"
        :clearable="false"
        @change="getMedicTableData()"
      ></el-date-picker>
      <span class="label">结束时间:</span>
      <el-time-picker
        v-model="apiNeedData.endTime"
        style="width: 120px"
        size="mini"
        value-format="HH:mm"
        :editable="false"
        format="HH:mm"
        :clearable="false"
        @change="getMedicTableData()"
      ></el-time-picker>
      <span class="label" v-if="this.stationMedicineFlag">病区:</span>
      <station-selector
        v-if="this.stationMedicineFlag"
        v-model="apiNeedData.stationID"
        hospitalFlag
        label=""
        width="120px"
        @change="getMedicTableData()"
      ></station-selector>
      <br />
      <span class="label">执行分类:</span>
      <el-select style="width: 120px" size="mini" v-model="filterData.medicineType" placeholder="请选择">
        <el-option
          v-for="item in medicineTypeOptions"
          :key="item.typeValue"
          :label="item.description"
          :value="item.description"
        ></el-option>
      </el-select>
      <span class="label">类型:</span>
      <el-radio-group v-model="filterData.orderType" size="mini">
        <el-radio-button label="全部">全部</el-radio-button>
        <el-radio-button label="长期">长期</el-radio-button>
        <el-radio-button label="临时">临时</el-radio-button>
      </el-radio-group>
      <span class="label">状态:</span>
      <el-radio-group v-model="filterData.typeIndex" size="mini">
        <el-radio-button v-if="stationMedicineFlag" label="1">全部</el-radio-button>
        <el-radio-button v-for="item in medicineStatusItems" :key="item.statusCode" :label="item.statusCode">
          {{ item.statusName }}
        </el-radio-button>
      </el-radio-group>
      <span class="label" v-if="this.stationMedicineFlag">显示出院患者：</span>
      <el-switch
        v-if="this.stationMedicineFlag"
        v-model="apiNeedData.searchAllPatientDataSwitch"
        @change="getMedicTableData()"
      ></el-switch>
      <span class="label" v-if="this.stationMedicineFlag">显示在科患者：</span>
      <el-switch v-if="this.stationMedicineFlag" v-model="apiNeedData.searchInStationSwitch"></el-switch>
      <span class="count">筛选出{{ medicalList.length }}条</span>
    </div>
    <div class="medicine-schedule-content" v-loading="loading" :element-loading-text="loadingText">
      <el-table :data="medicalList" border stripe highlight-current-row height="100%">
        <template v-if="this.stationMedicineFlag">
          <el-table-column key="1" prop="bedNumber" label="床位" width="80" align="left" sortable></el-table-column>
          <el-table-column key="2" prop="patientName" label="姓名" width="80" align="left" sortable></el-table-column>
        </template>
        <el-table-column key="3" prop="orderType" label="类型" width="55" align="center"></el-table-column>
        <el-table-column key="4" label="医嘱内容" min-width="180" align="center" :class-name="'child'">
          <template slot-scope="scope">
            <div
              :class="['order-content', { 'high-risk': value.highRiskFlag }]"
              v-for="(value, index) in scope.row.orderContents"
              :key="index"
            >
              {{ value.content }}
            </div>
          </template>
        </el-table-column>
        <el-table-column key="5" label="剂量" width="110" align="center" :class-name="'child'">
          <template slot-scope="scope">
            <div class="order-dose" v-for="(value, index) in scope.row.orderContents" :key="index">
              {{ value.totalVolume }}
            </div>
          </template>
        </el-table-column>
        <el-table-column key="6" prop="orderRule" label="途径" min-width="90" align="center"></el-table-column>
        <el-table-column key="7" prop="frequency" label="频次" min-width="60" align="center"></el-table-column>
        <el-table-column key="6" label="预执行日期" min-width="100" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.scheduleDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column key="7" label="预执行时间" min-width="95" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.scheduleTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          key="8"
          label="执行日期"
          min-width="80"
          align="center"
          v-if="(filterData.typeIndex != 0 && this.singleMedicineFlag) || this.stationMedicineFlag"
        >
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.performDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          key="9"
          label="执行时间"
          min-width="65"
          align="center"
          v-if="(filterData.typeIndex != 0 && this.singleMedicineFlag) || this.stationMedicineFlag"
        >
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.performTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          key="10"
          prop="performEmployeeName"
          label="执行护士"
          min-width="85"
          align="center"
          v-if="(filterData.typeIndex != 0 && this.singleMedicineFlag) || this.stationMedicineFlag"
        ></el-table-column>
        <el-table-column key="11" v-if="this.stationMedicineFlag" label="结束日期" min-width="80" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.endDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column key="12" v-if="this.stationMedicineFlag" label="结束时间" min-width="65" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.endTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          key="13"
          v-if="this.stationMedicineFlag"
          prop="endEmployeeName"
          label="结束护士"
          min-width="85"
          align="center"
        ></el-table-column>
        <el-table-column
          key="14"
          label="取消日期"
          min-width="80"
          align="center"
          v-if="(filterData.typeIndex == 4 && this.singleMedicineFlag) || this.stationMedicineFlag"
        >
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.cancelDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          key="15"
          label="取消时间"
          min-width="65"
          align="center"
          v-if="(filterData.typeIndex == 4 && this.singleMedicineFlag) || this.stationMedicineFlag"
        >
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.cancelTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          key="16"
          prop="cancelEmployeeName"
          label="取消护士"
          min-width="85"
          align="center"
          v-if="(filterData.typeIndex == 4 && this.singleMedicineFlag) || this.stationMedicineFlag"
        ></el-table-column>
        <el-table-column
          key="17"
          prop="orderStatusName"
          label="医嘱状态"
          min-width="60"
          align="center"
        ></el-table-column>
        <el-table-column
          key="11"
          v-if="this.singleMedicineFlag && filterData.typeIndex == 0"
          label="操作"
          min-width="80"
          align="center"
        >
          <template slot-scope="scope">
            <div class="operation">
              <el-tooltip content="取消" placement="top">
                <i class="iconfont icon-cancel" @click="cancel(scope.row.groupID)"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>

<script>
import BaseLayout from "@/components/BaseLayout";
import { GetStationMedicineSchedule, GetPatientMedicineScheduleByTime, CancelMedicine } from "@/api/MedicineSchedule";
import { GetScheduleTop } from "@/api/Setting";
import stationSelector from "@/components/selector/stationSelector";
export default {
  components: {
    BaseLayout,
    stationSelector,
  },
  props: {
    inpatientID: {
      type: String,
      default: undefined,
    },
    stationID: {
      type: Number,
      default: undefined,
    },
  },
  data() {
    return {
      apiNeedData: {
        stationID: 0,
        startDate: "",
        startTime: "00:00",
        endDate: "",
        endTime: "23:59",
        inpatientID: "",
        searchAllPatientDataSwitch: false,
      },
      filterData: {
        orderType: "全部",
        typeIndex: 0,
        medicineType: "全部",
      },
      medicineTypeOptions: [], //执行分类下拉框数据
      medicineData: [], //api获取表格数据
      medicalList: [], //筛选完之后的表格数据
      loading: false,
      loadingText: "加载中……",
      checkStartDate: {
        disabledDate: (time) => {
          //开始日期不得大于结束日期
          if (this.apiNeedData.endDate) {
            return time.getTime() > new Date(this.apiNeedData.endDate).getTime();
          }
        },
      },
      // 结束日期不得小于开始日期
      checkEndDate: {
        disabledDate: (time) => {
          if (this.apiNeedData.startDate) {
            return time.getTime() < new Date(this.apiNeedData.startDate).getTime() - 8.64e7;
          }
        },
      },
      // 个人给药页面标识
      singleMedicineFlag: false,
      // 病区给药页面标识
      stationMedicineFlag: false,
      // 给药状态
      medicineStatusItems: [],
    };
  },
  watch: {
    //监听筛选数据对象
    filterData: {
      handler(newValue) {
        this.getCurrentStatus();
      },
      deep: true,
    },
    inpatientID: {
      async handler(newValue) {
        this.apiNeedData.inpatientID = newValue;
        await this.getMedicTableData();
      },
      deep: true,
    },
  },
  async mounted() {
    this.apiNeedData.startDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
    this.apiNeedData.endDate = this.apiNeedData.startDate;
    this.apiNeedData.inpatientID = this.inpatientID;
    this.apiNeedData.stationID = this.stationID;
    this.getMedicineData();
    this.getMedicTableData();
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      if (this.stationID && !this.inpatientID) {
        this.apiNeedData.stationID = this.stationID;
        this.stationMedicineFlag = true;
      }
      if (!this.stationID && this.inpatientID) {
        this.apiNeedData.inpatientID = this.inpatientID;
        this.singleMedicineFlag = true;
      }
      return;
    },
    //获取药品下拉框数据
    getMedicineData() {
      let params = {
        settingTypeCode: "MedicineType",
      };
      GetScheduleTop(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.medicineTypeOptions = res.data;
          this.medicineTypeOptions.unshift({
            description: "全部",
          });
        }
      });
    },
    //获取医嘱表格数据
    async getMedicTableData() {
      if (!this.apiNeedData.stationID && !this.apiNeedData.inpatientID) {
        return;
      }
      this.loading = true;
      let response;
      if (this.apiNeedData.stationID) {
        response = await GetStationMedicineSchedule(this.apiNeedData);
      } else if (this.apiNeedData.inpatientID) {
        response = await GetPatientMedicineScheduleByTime(this.apiNeedData);
      }
      if (!this._common.isSuccess(response)) {
        this.loading = false;
        return;
      }
      this.medicineData = response.data.status;
      this.getCurrentStatus();
      // 提取给药状态信息集合
      this.medicineStatusItems = (this.medicineData || []).map((item) => {
        const { patientMedicineSchedule, ...statusInfo } = item;
        return statusInfo;
      });
      this.loading = false;
    },
    //筛选表格数据
    getCurrentStatus() {
      if (this.filterData.typeIndex == 1 || this.filterData.typeIndex == undefined) {
        this.medicalList = [];
        this.medicineData.forEach((item) => {
          if (item.patientMedicineSchedule && item.patientMedicineSchedule.length > 0) {
            this.medicalList = this.medicalList.concat(item.patientMedicineSchedule);
          }
        });
      } else {
        this.medicalList = this.medicineData.find(
          (item) => item.statusCode == this.filterData.typeIndex
        ).patientMedicineSchedule;
      }
      if (this.filterData.orderType != "全部") {
        this.medicalList = this.medicalList.filter((item) => item.orderType == this.filterData.orderType);
      }
      if (this.filterData.medicineType != "全部") {
        this.medicalList = this.medicalList.filter((item) => item.medicineType == this.filterData.medicineType);
      }
    },
    //取消医嘱
    cancel(value) {
      let _this = this;
      this._deleteConfirm("医嘱任务取消后将无法恢复，确认要取消此任务吗？", (flag) => {
        if (flag) {
          let params = {
            groupID: value,
          };
          CancelMedicine(params).then(async (res) => {
            if (_this._common.isSuccess(res)) {
              //执行完重新执行获取数据函数
              await this.getMedicTableData();
              _this._showTip("success", "取消成功");
            }
          });
        }
      });
    },
  },
};
</script>
<style lang="scss">
.schedule-medicine {
  .medicine-schedule-top {
    line-height: 35px;
    padding: 8px 0;
    .where {
      display: inline-block;
    }
    .label {
      padding-left: 5px;
    }
    .count {
      position: absolute;
      right: 10px;
      color: #ff0000;
    }
  }
  .medicine-schedule-content {
    height: 100%;
    overflow: hidden;
    td.child .cell {
      padding: 0;
    }
    td.child .order-content,
    td.child .order-dose {
      padding: 0 10px;
      line-height: 26px;
      border-bottom: 1px solid #ebf7df;
    }
    td.child .order-content {
      text-align: left;
    }
    td.child .order-dose {
      text-align: right;
    }
    td.child .order-content.high-risk {
      color: #ff0000;
    }
    td.child .order-content:last-child,
    td.child .order-dose:last-child {
      border-bottom: 0;
    }
    .opt .icon {
      display: inline-block;
    }
    table tr td {
      padding: 3px 0;
    }
    .operation .icon {
      display: inline-block;
    }
  }
}
</style>
