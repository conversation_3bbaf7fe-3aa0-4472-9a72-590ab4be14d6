/*
 * FilePath     : \src\api\medicationClosedLoop.js
 * Author       : 杨欣欣
 * Date         : 2025-05-18 15:29
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-19 17:42
 * Description  : 药品闭环管理API
 * CodeIterationRecord: 
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/MedicationClosedLoopManage";

const urls = {
  GetMedications: baseUrl + "/GetMedications",
  RevocationMedication: baseUrl + "/RevocationMedication",
};

/**
 * @description: 获取药品闭环信息
 * @param params
 * @return 
 */
export const GetMedications = (params) =>
  http.get(urls.GetMedications, params);

/**
 * @description: 撤销药品
 * @param params
 * @return 
 */
export const RevocationMedication = (params) =>
  http.post(urls.RevocationMedication, qs.stringify(params));
