<!--
 * FilePath     : \src\pages\transferPages\transferFishboneStatistics.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 苏军志
 * LastEditTime : 2021-04-08 16:25
 * Description  : 串鱼骨图统计
--> 
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getStatisticsUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  created() {
    this.url =
      getStatisticsUrl() + "fishboneStatistics?token=" + this.token + "&hospitalID=" + this.hospitalInfo.hospitalID;
    if (this.$route.query.recordsCode) {
      this.url += "&recordsCode=" + this.$route.query.recordsCode;
    }
  },
};
</script>
<style lang="scss">
.transfer-statistics {
  height: 100%;
  width: 100%;
}
</style>
