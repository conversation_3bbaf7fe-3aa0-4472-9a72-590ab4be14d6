<!--
 * FilePath     : \src\autoPages\monitoringScheduler\component\cellTypes\tnCell.vue
 * Author       : 杨欣欣
 * Date         : 2024-06-24 14:36
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-19 17:49
 * Description  : TN单元格组件
 * CodeIterationRecord: 
 -->
<template>
  <div class="tn-cell">
    <el-input
      v-model="cell.assessValue"
      @input="checkValueAbnormal(), execFormula()"
      @change="$listeners.change(row), checkTNValue()"
      @click.native="$listeners['click-input']"
      name="TN"
      :readonly="!!cell.formula || isReadOnly"
      :class="cell.assessValueColor"
      v-direction="{
        x: column.index,
        y: rowIndex,
        bindingContext: parent,
      }"
    />
    <!-- 2：高温且复测 -->
    <el-checkbox
      v-if="!cell.formula && cell.assessListID == '1295' && (hiddenItem ? hiddenItem : cell).showFlag"
      :checked="(hiddenItem ? hiddenItem : cell).temperatureStatus == 2"
      @change="changeRetestCheckbox"
    />
  </div>
</template>
<script>
import { formulaExec } from "../../mixins/formulaExec";
export default {
  name: "tnCell",
  inject: ["validateInputValueAbnormal", "getPainScoreThreshold", "isReadOnly", "getIsCopy", "parent"],
  props: {
    row: {
      type: Object,
      required: true,
    },
    rowIndex: {
      type: Number,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    monitorColumnRelationMap: {
      type: Map,
      required: true,
    },
    columns: {
      type: Array,
    },
  },
  data() {
    return {
      cell: undefined,
      hiddenItem: undefined,
      // 联动的下拉框对象
      dropdownCell: undefined,
      // 当前TN项联动的下拉框的AssessListID
      relationAssessListID: undefined,
      // 下拉框默认值
      dropdownDefaultValue: undefined,
    };
  },
  watch: {
    /**
     * @description: TN值发生变化时，若此时不持有复制行，则更新关联单元格选择值
     * @return
     */
    "cell.assessValue"() {
      !this.getIsCopy() && this.updateRelationCell();
    },
  },
  created() {
    this.relationAssessListID = this.monitorColumnRelationMap.get(this.column.assessListID.toString());
    if (this.relationAssessListID) {
      this.dropdownCell = Object.values(this.row).find((cell) => cell.assessListID == this.relationAssessListID);
      this.dropdownDefaultValue = this.dropdownCell?.assessValue;
    }
    this.cell = this.row[this.column.index];
    this.hiddenItem = this.row[this.column.relationHiddenItemIndex];
  },
  mounted() {
    this.updateRelationCell();
  },
  methods: {
    /**
     * @description: 检查输入值是否符合规范 && 切换当前行复选框状态
     * @return
     */
    checkTNValue() {
      if (!this.cell.assessValue && this.cell.assessValue + "" !== "0") {
        return;
      }
      const checkTN = {
        controlerType: this.cell.style,
        itemName: this.cell.showName,
        assessValue: this.cell.assessValue,
        decimal: this.cell.decimal,
        upError: this.cell.upError,
        lowError: this.cell.lowError,
        checkLevelDict: this.cell.checkLevelDict,
      };
      // 检核疼痛阈值  配置来自SettingDescription 特殊处理
      if (this.cell.assessListID == "1299" && this.cell.assessValue >= this.getPainScoreThreshold()) {
        this._showTip("warning", "疼痛评分>=" + this.getPainScoreThreshold() + "分，请到专项护理填写");
        this.cell.assessValue = undefined;
        return false;
      }
      // 检核TN条件，不符合检核，清空排程数据
      const { value } = this._common.checkAssessTN(checkTN);
      this.cell.assessValue = value;
    },
    /**
     * @description: 改变复选框状态时，更新相关属性值
     * @param value 复选框状态
     * @return
     */
    changeRetestCheckbox(value) {
      const cell = this.hiddenItem || this.cell;
      // 勾选，设置温度状态为需复测；2：高温且复测；1：高温不复测
      cell.temperatureStatus = value ? 2 : 1;
    },
    /**
     * @description: 联动更新下拉框选值
     * @return
     */
    updateRelationCell() {
      if (!this.dropdownCell) {
        return;
      }
      if (this.cell.assessValue) {
        this.dropdownCell.disabled = false;
        if (!this.dropdownCell.assessValue) {
          this.dropdownCell.assessValue = this.dropdownDefaultValue;
        }
      } else {
        this.dropdownCell.disabled = true;
        this.dropdownCell.assessValue = "";
      }
    },
    /**
     * @description: 校验异常值
     * @return
     */
    checkValueAbnormal() {
      // 若无公式且无关联隐藏项，则仅校验输入值
      if (!this.cell.formula && !this.column.relationHiddenItemIndex) {
        this.validateInputValueAbnormal(this.cell);
      }
    },
    /**
     * @description: 执行计算公式，因计算公式设置为只读，只有计算公式的参数项目才会触发此方法
     */
    execFormula() {
      // 判断当前项目是否为本行内其他项目计算公式的参数
      Object.keys(this.row).forEach((key) => {
        if (!this.row[key].formula) {
          return;
        }
        const { params } = this.row[key].formula;
        // 如果是公式的参数执，计算公式并将结果值赋给对应的项目
        if (params?.find((param) => param.id === this.cell.assessListID)) {
          const { _, newValue } = formulaExec(this.row, this.columns, this.row[key]);
          this.row[key].assessValue = newValue;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.tn-cell {
  .el-input__inner {
    padding: 5px;
  }
  .el-input__suffix {
    right: 0;
  }
  .reduce {
    & > input {
      padding: 0;
      text-align: center;
    }
    width: 55px;
  }
  .red {
    & > input {
      color: #ff0000;
    }
    display: inline-block;
  }
}
</style>