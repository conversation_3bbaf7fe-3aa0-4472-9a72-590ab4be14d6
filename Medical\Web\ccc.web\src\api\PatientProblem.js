/*
 * FilePath     : \src\api\PatientProblem.js
 * Author       : 李青原
 * Date         : 2020-03-25 11:16
 * LastEditors  : 曹恩
 * LastEditTime : 2023-04-22 14:32
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientProblem";

export const urls = {
  GetHistoryPatientProblems: baseUrl + "/GetHistoryPatientProblems",
  GetPatientProblemInfo: baseUrl + "/GetPatientProblemInfo",
  GetPatientInterventionInfo: baseUrl + "/GetPatientInterventionInfo",
  EvaluationProblem: baseUrl + "/EvaluationProblem",
  GetPatientProblemView: baseUrl + "/GetPatientProblemView",
  Save: baseUrl + "/Save",
  GetPatientProblems: baseUrl + "/GetPatientProblems",
  SaveSort: baseUrl + "/SaveSort",
  DelePatientProblem: baseUrl + "/DelePatientProblem",
  GetSignByProblemID: baseUrl + "/GetSignByProblemID",
  StopPatientProblemByID: baseUrl + "/StopPatientProblemByID",
  UpdateProblemDateTime: baseUrl + "/UpdateProblemDateTime",
  SaveClusterProblem: baseUrl + "/SaveClusterProblem",
  SaveDeselectReason: baseUrl + "/SaveDeselectReason"
};

// 获取病人历史问题
export const GetHistoryPatientProblems = params => {
  return http.get(urls.GetHistoryPatientProblems, params);
};
// 获取病人护理问题
export const GetPatientProblemInfo = params => {
  return http.get(urls.GetPatientProblemInfo, params);
};

// 获取病人护理问题对应的护理措施
export const GetPatientInterventionInfo = params => {
  return http.get(urls.GetPatientInterventionInfo, params);
};
// 提交护理问题评价
export const EvaluationProblem = params => {
  return http.post(urls.EvaluationProblem, params);
};
// 获取护理诊断
export const GetPatientProblemView = params => {
  return http.get(urls.GetPatientProblemView, params);
};
// 提交护理诊断
export const Save = params => {
  return http.post(urls.Save, params);
};
// 获取护理问题
export const GetPatientProblems = params => {
  return http.get(urls.GetPatientProblems, params);
};
// 获取护理排序
export const SaveSort = params => {
  return http.post(urls.SaveSort, params);
};
// 删除护理问题
export const DelePatientProblem = params => {
  return http.post(urls.DelePatientProblem, qs.stringify(params));
};
// 获得相关因素
export const GetSignByProblemID = params => {
  return http.get(urls.GetSignByProblemID, params);
};
// 停止集束护理
export const StopPatientProblemByID = params => {
  return http.post(urls.StopPatientProblemByID, qs.stringify(params));
};
// 修改护理问题的开始和结束时间
export const UpdateProblemDateTime = params => {
  return http.post(urls.UpdateProblemDateTime, qs.stringify(params));
};
// 保存集束护理
export const SaveClusterProblem = params => {
  return http.post(urls.SaveClusterProblem, params);
};
// 保存取勾选措施原因
export const SaveDeselectReason = params => {
  return http.post(urls.SaveDeselectReason, params);
};
