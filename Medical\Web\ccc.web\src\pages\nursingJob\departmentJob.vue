<!--
 * FilePath     : \src\pages\nursingJob\departmentJob.vue
 * Author       : 郭鹏超
 * Date         : 2020-07-02 15:58
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-16 08:29
 * Description  : 岗位配置
-->
<template>
  <base-layout class="department-job" v-loading="loading" element-loading-text="加载中……">
    <div class="department-job-top" slot="header">
      <el-select v-model="stationID" @change="getJobByStationID" placeholder="请选择科室" style="width: 180px">
        <el-option
          v-for="item in optionsStationID"
          :key="item.stationID"
          :label="item.stationName"
          :value="item.stationID"
        ></el-option>
      </el-select>
      <el-button @click="handleAdd()" class="add-button" icon="iconfont icon-add" v-if="isButtonVisible">
        新增
      </el-button>
    </div>
    <div class="department-job-content">
      <el-table height="100%" :data="departmentJobList" border>
        <el-table-column label="科室名称" prop="departmentName"></el-table-column>
        <el-table-column label="岗位分组" prop="careGroupName"></el-table-column>
        <el-table-column label="岗位名称" prop="aliasName"></el-table-column>
        <el-table-column label="操作" width="80px">
          <template slot-scope="scope">
            <el-tooltip content="编辑">
              <i class="iconfont icon-edit" @click="handleEdit(scope.$index, scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="deleteJob(scope.$index, scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      custom-class="department-jop-dialog"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="width"
    >
      <div class="dialog-content">
        <label>科室名称:</label>
        <el-select
          :disabled="selectBoole"
          v-model="dialogSaveData.departmentID"
          placeholder="请选择科室"
          style="width: 180px"
        >
          <el-option
            v-for="item in optionsStationID"
            :key="item.stationID"
            :label="item.stationName"
            :value="item.stationID"
          ></el-option>
        </el-select>
        <label class="label-two">岗位分组:</label>
        <el-select
          v-model="dialogSaveData.careGroupID"
          placeholder="请选择岗位分组"
          style="width: 180px"
          @change="changeCareGroup"
        >
          <el-option
            v-for="item in careGroupList"
            :key="item.careGroupID"
            :label="item.careGroupName"
            :value="item.careGroupID"
          ></el-option>
        </el-select>
        <label class="label-two">岗位名称:</label>
        <el-select v-model="dialogSaveData.jobArchitectureID" placeholder="请选择岗位" style="width: 180px">
          <el-option v-for="item in optionsJobID" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addOrFix">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import { GetEmployeeSwitchStationList } from "@/api/User";
import { GetAllJobLevelData } from "@/api/Administration";
import { GetStationByID } from "@/api/Station";
import {
  GetCareGroupList, //根据指定病区所有的分组
  GetDepartmentJob,
  SaveDepartmentJob,
  UpdateDepartmentJob,
  DeleteDepartmentJob,
  CheckDepartmentJob,
  GetPostAdditionButtonSwitch,
} from "@/api/DepartmentJob";
import BaseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
export default {
  components: {
    BaseLayout,
  },
  data() {
    return {
      loading: false,
      dialogTitle: "", //弹窗标题
      dialogVisible: false,
      selectBoole: false, //控制弹窗科室选择框是否可编辑
      //外层病区(Select)数据绑定用(Model)
      stationID: "",
      //科室岗位清单,数据绑定用(Table)
      departmentJobList: [],
      //选择岗位,数据绑定用(Select)
      optionsJobID: [],
      //选择科室,数据绑定用(Select)
      optionsStationID: [],
      //弹窗保存数据
      dialogSaveData: {
        departmentID: "",
        jobArchitectureID: "",
        departmentJobID: "",
        aliasName: "",
        careGroupID: undefined,
        careGroupName: undefined,
      },
      careGroupList: [],
      value: "",
      isButtonVisible: false,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
    getAliasName() {
      return this.optionsJobID.find((item) => item.value == this.dialogSaveData.jobArchitectureID).label;
    },
  },
  created() {
    this.stationID = this.user.stationID;
    this.init();
  },
  mounted() {
    this.getCareGroupListData();
  },
  methods: {
    //页面初始化函数
    async init() {
      this.loading = true;
      //取得科室清单
      await this.getStationList();
      //取得岗位清单
      await this.getJobList();
      //获取表格数据
      await this.getJobByStationID();
      await this.GetSwitch();
      this.loading = false;
    },
    //查询
    getCareGroupListData() {
      let params = {
        stationID: this.user.stationID,
      };
      GetCareGroupList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.careGroupList = res.data;
        }
      });
    },
    //获取科室下拉框数据
    async getStationList() {
      await GetEmployeeSwitchStationList().then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data && res.data.length > 0) {
            this.optionsStationID = res.data;
          } else {
            let params = { ID: this.stationID };
            GetStationByID(params).then((res) => {
              if (this._common.isSuccess(res)) {
                this.optionsStationID.push({
                  stationID: res.data.id,
                  stationName: res.data.stationName,
                });
              }
            });
          }
        }
      });
    },
    //获取弹窗下拉框岗位数据
    async getJobList() {
      await GetAllJobLevelData().then((result) => {
        if (this._common.isSuccess(result)) {
          let List = result.data;
          List.forEach((item) => {
            const tableItem = {
              value: item.jobArchitectureID,
              label: item.jobTitle,
            };
            this.optionsJobID.push(tableItem);
          });
        }
      });
    },
    //获取表格数据
    async getJobByStationID() {
      if (this.stationID == -1) {
        return;
      }
      let selectStationID = {
        departmentID: this.stationID,
      };

      await GetDepartmentJob(selectStationID).then((res) => {
        if (this._common.isSuccess(res)) {
          this.departmentJobList = res.data;
          this.loading = false;
        }
      });
    },
    //获取新增按钮开关
    async GetSwitch() {
      await GetPostAdditionButtonSwitch().then((res) => {
        if (this._common.isSuccess(res)) {
          this.isButtonVisible = res.data;
          this.loading = false;
        }
      });
    },
    //点击新增
    handleAdd() {
      this.dialogVisible = true;
      this.selectBoole = false;
      this.dialogTitle = "岗位新增";
      Object.keys(this.dialogSaveData).forEach((key) => (this.dialogSaveData[key] = ""));
      let params = {
        departmentID: this.stationID,
        departmentJobID: 1,
      };
      this.dialogSaveData = Object.assign(this.dialogSaveData, params);
    },
    /**
     * @description: 岗位分组选择变更
     */
    changeCareGroup() {
      const careGroup = this.careGroupList.find((item) => item.careGroupID === this.dialogSaveData.careGroupID);
      if (careGroup) {
        this.dialogSaveData.careGroupName = careGroup.careGroupName;
      }
    },
    //点击表格维护
    handleEdit(index, row) {
      this.dialogVisible = true;
      this.selectBoole = true;
      this.dialogTitle = "岗位修改";
      let params = {
        departmentID: row.departmentID,
        jobArchitectureID: row.jobArchitectureID,
        departmentJobID: row.departmentJobID,
        careGroupID: row.careGroupID,
      };
      this.dialogSaveData = Object.assign(this.dialogSaveData, params);
    },
    //保存数据检核
    async checkSaveData() {
      let boole;
      let { departmentJobID, aliasName, ...params } = this.dialogSaveData;
      await CheckDepartmentJob(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data > 0) {
            this._showTip("error", "科室已有相同岗位");
            boole = true;
          } else {
            boole = false;
          }
        }
      });
      return boole;
    },
    //新增或者修改保存
    async addOrFix() {
      if (!this.dialogSaveData.careGroupID) {
        this._showTip("error", "请选择岗位分组");
        return;
      }
      if (!this.dialogSaveData.departmentID) {
        this._showTip("error", "请选择科室");
        return;
      }
      if (!this.dialogSaveData.jobArchitectureID) {
        this._showTip("error", "请选择岗位");
        return;
      }
      //保存数据检核
      let successBoole = await this.checkSaveData();
      if (successBoole) {
        return;
      }
      if (this.dialogTitle == "岗位新增") {
        var careGroup = this.careGroupList.find((item) => item.careGroupID == this.dialogSaveData.careGroupID);
        this.dialogSaveData.careGroupName = careGroup.careGroupName;
        this.dialogSaveData.aliasName = this.getAliasName;
        let { departmentJobID, ...params } = this.dialogSaveData;

        return await SaveDepartmentJob(params).then((res) => {
          if (this._common.isSuccess(res)) {
            this.getJobByStationID(params.departmentID);
            this._showTip("success", "保存成功");
            this.dialogVisible = false;
          }
        });
      }
      if (this.dialogTitle == "岗位修改") {
        this.dialogSaveData.aliasName = this.getAliasName;
        return await UpdateDepartmentJob(this.dialogSaveData).then((res) => {
          if (this._common.isSuccess(res)) {
            this.getJobByStationID(this.dialogSaveData.departmentID);
            this._showTip("success", "修改成功");
            this.dialogVisible = false;
          }
        });
      }
    },
    // 删除
    deleteJob(index, row) {
      this._deleteConfirm("此操作将永久删除, 是否继续?", (flag) => {
        if (flag) {
          let departmentJob = {
            departmentJobID: row.departmentJobID,
          };
          DeleteDepartmentJob(departmentJob).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功");
              this.getJobByStationID(row.departmentID);
            }
          });
        }
      });
    },
  },
};
</script>
<style lang="scss">
.department-job {
  height: 100%;
  .department-job-top {
    line-height: 50px;
    background-color: #fff;
    .add-button {
      float: right;
      margin-top: 10px;
    }
  }
  .department-job-content {
    height: 100%;
  }
  .department-jop-dialog {
    width: 825px;
    height: 180px;
    .label-two {
      margin-left: 10px;
    }
    .el-dialog__body {
      padding-top: 30px;
    }
  }
}
</style>
