<!--
 * FilePath     : \ccc.web\src\pages\patientOrder\medicineSchedule.vue
 * Author       : 苏军志
 * Date         : 2020-06-04 09:42
 * LastEditors  : 陈超然
 * LastEditTime : 2024-03-08 12:05
 * Description  : 医嘱执行页面 
-->
<template>
  <medicine-Schedule :inpatientID="currentPatient.inpatientID"></medicine-Schedule>
</template>

<script>
import medicineSchedule from "./components/medicineSchedule";
import { mapGetters } from "vuex";
export default {
  components: {
    medicineSchedule,
  },
  computed: {
    ...mapGetters({
      currentPatient: "getCurrentPatient",
    }),
  },
};
</script>
