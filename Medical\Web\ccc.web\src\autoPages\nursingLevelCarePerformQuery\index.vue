<!--
 * FilePath     : \src\autoPages\nursingLevelCarePerformQuery\index.vue
 * Author       : 胡长攀
 * Date         : 2024-04-27 09:30
 * LastEditors  : 马超
 * LastEditTime : 2025-07-07 10:59
 * Description  : 护理级别排程执行情况
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="nursing-level-care-perform-query" v-loading="loading" element-loading-text="加载中……">
    <div class="top" slot="header">
      <div class="top-left">
        <station-selector
          v-model="stationID"
          :userID="user.userID"
          :hospitalFlag="true"
          width="180"
          class="select-station"
          @select-item="changeStation"
          @change="getPatientNursingLevelSchedules"
        ></station-selector>
        <dept-selector
          :clearable="true"
          :disabled="deptSelectorDisabled"
          :width="convertPX(200) + 'px'"
          v-model="departmentListID"
          :stationID="stationID"
          @change="getPatientNursingLevelSchedules"
        />
        <label>选择时间:</label>
        <el-date-picker
          v-model="startDateTime"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          class="select-date"
          @change="getPatientNursingLevelSchedules"
        ></el-date-picker>
        -
        <el-date-picker
          v-model="endDateTime"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          class="select-date"
          @change="getPatientNursingLevelSchedules"
        ></el-date-picker>
        <label>护理级别:</label>
        <el-select
          class="measure-select"
          v-model="assessListIDs"
          multiple
          clearable
          collapse-tags
          placeholder="请选择护理级别"
          @change="getPatientNursingLevelSchedules"
        >
          <el-option
            v-for="item in nursingLevelInspectionItemList"
            :key="item.settingValue"
            :label="item.description"
            :value="item.settingValue"
          ></el-option>
        </el-select>
        <label>住院号:</label>
        <el-input
          v-model="localCaseNumber"
          class="search-inpatient-input"
          placeholder="请输入住院号"
          @change="getPatientNursingLevelSchedules()"
        >
          <i slot="append" class="iconfont icon-search" @click="getPatientNursingLevelSchedules()"></i>
        </el-input>
      </div>
      <div class="top-right">
        <export-excel :exportExcelOption="exportExcelOption" class="export-button" />
      </div>
    </div>
    <el-table
      class="nursing-level-schedule-data-table"
      :data="nursingLevelScheduleList"
      :span-method="spanRow"
      height="100%"
      empty-text="暂无数据"
      border
    >
      <el-table-column
        prop="localCaseNumber"
        label="住院号"
        align="center"
        :width="convertPX(150) + 'px'"
      ></el-table-column>
      <el-table-column prop="patientName" label="姓名" :width="`${convertPX(120)}px`"></el-table-column>
      <el-table-column prop="gender" label="性别" align="center" :width="`${convertPX(50)}px`"></el-table-column>
      <el-table-column prop="age" label="年龄" :width="`${convertPX(100)}px`"></el-table-column>
      <el-table-column prop="bedNumber" label="床号" align="center" :width="`${convertPX(100)}px`"></el-table-column>
      <el-table-column prop="departmentListName" label="科室" min-width="120px"></el-table-column>
      <el-table-column prop="stationName" label="病区" min-width="120px"></el-table-column>
      <el-table-column prop="count" label="次数" align="center" :width="`${convertPX(50)}px`"></el-table-column>
      <el-table-column prop="assessListName" label="护理措施" :width="`${convertPX(180)}px`"></el-table-column>
      <el-table-column prop="scheduleDateTime" label="排程时间" align="center" :width="`${convertPX(180)}px`">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.scheduleDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="performDateTime" label="执行时间" align="center" :width="`${convertPX(180)}px`">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.performDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="nurseEmployeeName" label="执行护士"></el-table-column>
      <el-table-column prop="content" label="执行说明" min-width="120px"></el-table-column>
    </el-table>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import deptSelector from "@/components/selector/deptSelector";
import stationSelector from "@/components/selector/stationSelector";
import exportExcel from "@/components/file/exportExcel.vue";
import { mapGetters } from "vuex";
import { GetClinicSettingByTypeCode } from "@/api/Setting";
import { GetPatientNursingLevelSchedules } from "@/api/PatientSchedule";
export default {
  components: {
    baseLayout,
    deptSelector,
    stationSelector,
    exportExcel,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      // 护理级别巡视项目
      nursingLevelInspectionItemList: [],
      // 表格数据
      nursingLevelScheduleList: [],
      // 科室
      departmentListID: undefined,
      //病区
      stationID: undefined,
      //开始时间
      startDateTime: undefined,
      //结束时间
      endDateTime: undefined,
      //措施ID
      assessListIDs: ["999999"],
      //住院号
      localCaseNumber: "",
      //是否禁用科室选择器
      deptSelectorDisabled: false,
      //加载
      loading: false,
      //表格合并变量
      spanArr: [],
      pos: 0,
      exportExcelOption: {
        tableData: [],
        columnData: {},
        fileName: "护理级别排程执行情况",
        sheetName: "护理级别排程执行情况",
        buttonName: "导出Excel",
      },
    };
  },

  mounted() {
    //初始化
    this.init();
    //获取护理级别巡视项目配置
    this.getNursingLevelInspectionItemSetting();
    //获取级别巡视排程数据
    this.getPatientNursingLevelSchedules();
  },
  methods: {
    /**
     * @description: 初始化
     * @return
     */
    init() {
      this.stationID = this.user.stationID;
      this.endDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      this.startDateTime = this._datetimeUtil.addDate(this.endDateTime, -1);
    },
    /**
     * @description: 获取护理级别巡视项目配置
     * @return
     */
    getNursingLevelInspectionItemSetting() {
      this.nursingLevelInspectionItemList = [];
      let params = {
        settingTypeCode: "NursingLevelInspectionItem",
      };
      GetClinicSettingByTypeCode(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.nursingLevelInspectionItemList = response.data;
          this.nursingLevelInspectionItemList.unshift({
            settingValue: "999999",
            description: "全部",
          });
        }
      });
    },
    /**
     * @description: 获取护理级别相关排程执行情况
     * @return
     */
    getPatientNursingLevelSchedules() {
      if (this.startDateTime > this.endDateTime) {
        this._showTip("warning", "开始时间不能大于结束时间！");
        return;
      }
      this.loading = true;
      this.nursingLevelScheduleList = [];
      //获取级别巡视排程数据
      let params = {
        departmentListID: this.departmentListID,
        stationID: this.stationID,
        startDateTime: this.startDateTime,
        endDateTime: this.endDateTime,
        assessListIDs: this.assessListIDs,
        localCaseNumber: this.localCaseNumber,
      };
      GetPatientNursingLevelSchedules(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.nursingLevelScheduleList = result.data;
          this.getSpanArr(this.nursingLevelScheduleList);
          // 设置导出Excel参数
          this.setExportExcelOption();
        }
      });
    },
    /**
     * @description: 设置导出Excel参数
     */
    setExportExcelOption() {
      // 表头字段与el-table一致
      this.exportExcelOption.columnData = {
        localCaseNumber: "住院号",
        patientName: "姓名",
        gender: "性别",
        age: "年龄",
        bedNumber: "床号",
        departmentListName: "科室",
        stationName: "病区",
        count: "次数",
        assessListName: "护理措施",
        scheduleDateTime: "排程时间",
        performDateTime: "执行时间",
        nurseEmployeeName: "执行护士",
        content: "执行说明",
      };
      this.exportExcelOption.tableData = this.nursingLevelScheduleList;
      this.exportExcelOption.buttonName = "导出";
    },
    /**
     * @description: 选择病区回调函数
     * @param station
     * @return
     */
    changeStation(station) {
      if (station.id === 999999) {
        this.deptSelectorDisabled = true;
        this.departmentListID = undefined;
        return;
      }
      this.deptSelectorDisabled = false;
    },

    /**
     * @description: 表格合并
     * @param data
     * @return
     */
    getSpanArr(data) {
      this.spanArr = [];
      if (!data) {
        return;
      }
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          let currentData = this.nursingLevelScheduleList[i];
          let previousData = this.nursingLevelScheduleList[i - 1];
          if (
            currentData.localCaseNumber === previousData.localCaseNumber &&
            currentData.patientName === previousData.patientName &&
            currentData.gender === previousData.gender &&
            currentData.age === previousData.age
          ) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    /**
     * @description: 行列合并
     * @return
     * @param row
     * @param column
     * @param rowIndex
     * @param columnIndex
     */
    spanRow({ row, column, rowIndex, columnIndex }) {
      if (columnIndex <= 3) {
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
  },
};
</script>
<style lang="scss">
.nursing-level-care-perform-query {
  .top {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    .top-left {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-end;
      gap: 10px;
    }
    .top-right {
      display: flex;
      align-items: flex-end;
    }
    .select-date {
      width: 160px;
    }
    .measure-select {
      width: 160px;
    }
    .search-inpatient-input {
      width: 160px;

      .el-input-group__append {
        padding: 0 5px;
        color: #8cc63e;
      }
    }
  }
}
</style>