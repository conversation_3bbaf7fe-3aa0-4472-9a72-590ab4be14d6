/*
 * FilePath     : \src\api\PatientCriticallyVisitsRecord.js
 * Author       : 胡长攀
 * Date         : 2024-06-26 16:36
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-06-26 17:04
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
const baseUrl = "/PatientCriticallyVisitsRecord";

export const urls = {
  // 获取危重访视记录
  GetCriticallyPatientList: baseUrl + "/GetCriticallyPatientList",
  // 保存危重访视记录
  SaveCriticallyPatientVisitsRecord: baseUrl + "/SaveCriticallyPatientVisitsRecord"
};

// 获取危重访视记录
export const GetCriticallyPatientList = params => {
  return http.get(urls.GetCriticallyPatientList, params);
};
// 保存危重访视记录
export const SaveCriticallyPatientVisitsRecord = params => {
  return http.post(urls.SaveCriticallyPatientVisitsRecord, params);
};