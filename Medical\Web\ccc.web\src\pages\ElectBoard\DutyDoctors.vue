<!--
 * FilePath     : \src\pages\ElectBoard\DutyDoctors.vue
 * Author       : 胡洋
 * Date         : 2020-09-22 10:46
 * LastEditors  : 苏军志
 * LastEditTime : 2020-10-28 14:57
 * Description  : 值班医师维护
-->
<template>
  <base-layout class="duty-doctor">
    <div slot="header" class="where-warp">
      <span class="text">病区：</span>
      <el-select
        class="station"
        :disabled="!multipleStationFlag"
        placeholder="请选择病区"
        v-model="selectStationID"
        @change="changeStation"
        style="width: 160px"
      >
        <el-option v-for="item in stationList" :key="item.id" :label="item.stationName" :value="item.id"></el-option>
      </el-select>
      <span class="text">科室：</span>
      <el-select placeholder="请选择科室" v-model="selectDepartmentID" style="width: 140px">
        <el-option v-for="item in departmentList" :key="item.id" :label="item.value" :value="item.id"></el-option>
      </el-select>
      <span class="text">日期：</span>
      <el-date-picker
        v-model="dutyDate"
        type="date"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 140px"
      ></el-date-picker>
      <span class="text">班次：</span>
      <el-select placeholder="请选择班次" v-model="dutyShiftID" style="width: 120px">
        <el-option v-for="item in shiftList" :key="item.id" :label="item.shiftName" :value="item.id"></el-option>
      </el-select>
      <div class="top-btn">
        <el-button class="edit-button" icon="iconfont icon-system-setting" @click="showFix">维护</el-button>
      </div>
    </div>
    <el-table
      :data="dutyDoctors"
      border
      stripe
      height="100%"
      row-class-name="doctor-row"
      v-loading="loadingDoctor"
      element-loading-text="加载中……"
    >
      <el-table-column prop="doctorName" label="医师姓名" align="center" />
      <el-table-column prop="title" label="职称" align="center" />
      <el-table-column prop="phoneNum" label="电话" align="center" />
    </el-table>
    <!-- 维护值班医师 -->
    <el-dialog
      title="维护值班医师"
      v-dialogDrag
      :visible.sync="show"
      :close-on-click-modal="false"
      custom-class="fix-doctor"
    >
      <el-table
        :data="doctors"
        border
        height="100%"
        width="100%"
        v-loading="loading"
        :element-loading-text="loadingText"
        row-class-name="doctor-row"
        element-loading-background="rgba(0, 0, 0, 0.5)"
      >
        <el-table-column label="医师姓名" align="center" min-width="130">
          <template slot-scope="doctor">
            {{ doctor.row.doctorName }}
          </template>
        </el-table-column>
        <el-table-column label="职称" align="center" min-width="180">
          <template slot-scope="doctor">
            {{ doctor.row.title }}
          </template>
        </el-table-column>
        <el-table-column label="电话" align="center" min-width="180">
          <template slot-scope="doctor">
            {{ doctor.row.phoneNum }}
          </template>
        </el-table-column>
        <el-table-column label="值班" align="center" width="110">
          <template slot-scope="doctor">
            <el-checkbox v-model="doctor.row.isDuty" />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button @click="close('cancel')">取消</el-button>
        <el-button type="primary" @click="close">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetDutyDoctors, SaveDoctor, GetDoctorWithDuty } from "@/api/ElectBoard";
import { GetStationList, GetDepartmentDataByStationID } from "@/api/Station";
import { GetByStationID } from "@/api/StationShift";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      //病区列表
      stationList: [],
      //科室列表
      departmentList: [],
      //当前病区ID
      selectStationID: "",
      //科室ID
      selectDepartmentID: "",
      loadingDoctor: false,
      //值班医师
      dutyDoctors: [],
      //遮罩
      loading: false,
      loadingText: "",
      //是否显示弹窗
      show: false,
      //医师
      doctors: [],
      //病区是否可选
      multipleStationFlag: true,
      dutyShiftID: undefined,
      //班别
      shiftList: [],
      //值班日期
      dutyDate: "",
    };
  },
  watch: {
    dutyDate() {
      this.GetDutyDoctorData();
      this._datetimeUtil.getNowDate("yyyy-MM-dd");
    },
    selectDepartmentID() {
      this.GetDutyDoctorData();
    },
    dutyShiftID() {
      this.GetDutyDoctorData();
    },
  },
  created() {
    this.dutyDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
    if (this.user.stationID) {
      this.selectStationID = parseInt(this.user.stationID);
      this.multipleStationFlag = false;
    }
    this.getStations();
  },
  methods: {
    changeStation() {
      this.getDepartments();
      this.getShifts();
    },
    // 获取病区列表
    getStations() {
      GetStationList().then((data) => {
        if (this._common.isSuccess(data)) {
          this.stationList = data.data;
          this.getDepartments();
          this.getShifts();
        }
      });
    },
    // 获取病区中的科室
    getDepartments() {
      if (!this.selectStationID) return;
      //科室
      let stationparams = {
        ID: this.selectStationID,
      };
      this.selectDepartmentID = "";
      this.departmentList = [];
      this.dutyDoctors = [];
      GetDepartmentDataByStationID(stationparams).then((data) => {
        if (this._common.isSuccess(data)) {
          this.departmentList = data.data;
        }
      });
    },
    getShifts() {
      if (!this.selectStationID) return;
      let params = {
        stationID: this.selectStationID,
      };
      this.dutyShiftID = "";
      this.shiftList = [];
      this.dutyDoctors = [];
      GetByStationID(params).then((data) => {
        if (this._common.isSuccess(data)) {
          this.shiftList = data.data;
        }
      });
    },
    GetDutyDoctorData() {
      if (!this.selectStationID) {
        return;
      }
      if (!this.selectDepartmentID) {
        return;
      }
      if (!this.dutyDate) {
        return;
      }
      if (!this.dutyShiftID) {
        return;
      }
      this.loadingDoctor = true;
      let params = {
        stationID: this.selectStationID,
        departmentID: this.selectDepartmentID,
        dutyDate: this.dutyDate,
        shiftID: this.dutyShiftID,
      };
      this.dutyDoctors = [];
      GetDutyDoctors(params).then((data) => {
        this.loadingDoctor = false;
        if (this._common.isSuccess(data)) {
          this.dutyDoctors = data.data;
        }
      });
    },
    showFix() {
      if (!this.selectStationID) {
        this._showTip("warning", "请选择病区");
        return;
      }
      if (!this.selectDepartmentID) {
        this._showTip("warning", "请选择科室");
        return;
      }
      if (!this.dutyDate) {
        this._showTip("warning", "请选择值班日期");
        return;
      }
      if (!this.dutyShiftID) {
        this._showTip("warning", "请选择值班班次");
        return;
      }
      this.show = true;
      this.loading = true;
      this.loadingText = "加载中……";
      let params = {
        stationID: this.selectStationID,
        departmentID: this.selectDepartmentID,
        dutyDate: this.dutyDate,
        shiftID: this.dutyShiftID,
        modifyPersonID: this.user.userID,
      };
      this.doctors = [];
      GetDoctorWithDuty(params).then((data) => {
        this.loading = false;
        if (this._common.isSuccess(data)) {
          this.doctors = data.data;
        }
      });
    },
    close(type) {
      if (type === "cancel") {
        this.show = false;
      } else {
        this.loading = true;
        this.loadingText = "保存中……";
        let saveData = [];
        this.doctors.forEach((doctor) => {
          if (doctor.isDuty) {
            let item = doctor;
            item.StationID = this.selectStationID;
            item.DutyDate = this.dutyDate;
            item.shiftID = this.dutyShiftID;
            item.ModifyDate = this._datetimeUtil.getNowDate("yyyy-MM-dd hh:mm:ss");
            item.DeleteFlag = "";
            item.ModifyPersonID = this.user.userID;
            saveData.push(item);
          }
        });
        let params = {
          stationID: this.selectStationID,
          departmentID: this.selectDepartmentID,
          dutyDate: this.dutyDate,
          shiftID: this.dutyShiftID,
          doctors: saveData,
        };
        return SaveDoctor(params).then((data) => {
          this.loading = false;
          if (this._common.isSuccess(data)) {
            this._showTip("success", "保存成功");
            this.show = false;
            this.GetDutyDoctorData();
          }
        });
      }
    },
  },
};
</script>
<style lang='scss'>
.duty-doctor {
  .where-warp {
    min-width: 900px;
    .text {
      margin-left: 5px;
      &:first-child {
        margin-left: 0;
      }
    }
    .top-btn {
      float: right;
      .icon-system-setting {
        margin-right: 6px !important;
      }
    }
  }
  .fix-doctor.el-dialog {
    height: 510px;
    width: 650px;
  }
}
</style>
