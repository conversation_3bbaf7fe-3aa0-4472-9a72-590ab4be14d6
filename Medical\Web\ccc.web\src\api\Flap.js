/*
 * FilePath     : \ccc.web\src\api\Flap.js
 * Author       : 杨欣欣
 * Date         : 2023-06-08 10:48
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-06-11 14:30
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/Flap";

export const urls = {
  GetFlapRecordList: baseUrl + "/GetFlapRecordList",
  GetFlapCareMainListByRecordID: baseUrl + "/GetFlapCareMainListByRecordID",
  GetFlapAssessView: baseUrl + "/GetFlapAssessView",
  GetFlapRecordsCodeInfo: baseUrl + "/GetFlapRecordsCodeInfo",
  GetImgPreviewData: baseUrl + "/GetImgPreviewData",
  AddFlapRecord: baseUrl + "/AddFlapRecord",
  AddFlapCare: baseUrl + "/AddFlapCare",
  AddFlapEnd: baseUrl + "/AddFlapEnd",
  UpdateFlapRecord: baseUrl + "/UpdateFlapRecord",
  UpdateFlapCare: baseUrl + "/UpdateFlapCare",
  DeleteFlapByID: baseUrl + "/DeleteFlapByID",
  DeleteFlapCare: baseUrl + "/DeleteFlapCare"
};

// 获取记录列表
export const GetFlapRecordList = params =>
  http.get(urls.GetFlapRecordList, params);
// 获取维护记录列表
export const GetFlapCareMainListByRecordID = params =>
  http.get(urls.GetFlapCareMainListByRecordID, params);
// 获取评估模板
export const GetFlapAssessView = params =>
  http.get(urls.GetFlapAssessView, params);
// 获取Flap对应的DepartmentToAssessInfo记录
export const GetFlapRecordsCodeInfo = params =>
  http.get(urls.GetFlapRecordsCodeInfo, params);
export const GetImgPreviewData = params =>
  http.get(urls.GetImgPreviewData, params);

// 新增主记录
export const AddFlapRecord = params => http.post(urls.AddFlapRecord, params);
// 新增维护记录
export const AddFlapCare = params => http.post(urls.AddFlapCare, params);
// 新增停止维护
export const AddFlapEnd = params => http.post(urls.AddFlapEnd, params);

// 更新主记录
export const UpdateFlapRecord = params =>
  http.post(urls.UpdateFlapRecord, params);
// 更新维护记录
export const UpdateFlapCare = params => http.post(urls.UpdateFlapCare, params);

// 删除一条维护记录
export const DeleteFlapCare = params =>
  http.post(urls.DeleteFlapCare, qs.stringify(params));
// 删除主记录
export const DeleteFlapByID = params =>
  http.post(urls.DeleteFlapByID, qs.stringify(params));
