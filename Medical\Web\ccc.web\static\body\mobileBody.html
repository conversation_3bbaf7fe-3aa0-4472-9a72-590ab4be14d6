﻿<html>
  <head>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
    <title>人体图</title>
    <script type="text/javascript" src="js/jquery-1.9.1.min.js"></script>
    <script type="text/javascript" src="js/common.js"></script>
    <script type="text/javascript" src="js/BodyPartService.js"></script>
    <script type="text/javascript" src="body.js"></script>
    <style>
      html,
      body {
        width: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
  </head>

  <body>
    <script type="text/javascript">
      //窗体改变大小时
      window.onresize = function () {
        $BodyMap.onresize();
      };
      window.onload = function () {
        //设置为可以翻转
        $BodyMap.setReverse(true);
        //加载人体图
        $BodyMap.onload({
          container: $(document.body),
          showTitile: false,
          title: {
            bodymap: "人体图",
            list: "列表",
            reverse: "转身",
          },
          selected: function (selectBodyPartList) {
            var ret = [];
            for (var i = 0; i < selectBodyPartList.length; i++) {
              ret.push({
                bodyPartCode: selectBodyPartList[i].code,
                // bodyPartName: selectBodyPartList[i].bodyPartName
                bodyPartName: selectBodyPartList[i].contentName,
              });
            }
            if (ret.length > 0) {
              if ($BodyMap.selectOneMode) {
                $Common.storage("bodyPart", ret[0]);
              } else {
                $Common.storage("bodyPart", ret);
              }
            } else {
              if ($BodyMap.selectOneMode) {
                $Common.storage("bodyPart", {});
              } else {
                $Common.storage("bodyPart", []);
              }
            }
          },
          cancelSelected: function (selectBodyPartList) {
            var ret = [];
            for (var i = 0; i < selectBodyPartList.length; i++) {
              ret.push({
                bodyPartCode: selectBodyPartList[i].code,
                bodyPartName: selectBodyPartList[i].contentName,
              });
            }
            if (ret.length > 0) {
              if ($BodyMap.selectOneMode) {
                $Common.storage("bodyPart", ret[0]);
              } else {
                $Common.storage("bodyPart", ret);
              }
            } else {
              if ($BodyMap.selectOneMode) {
                $Common.storage("bodyPart", {});
              } else {
                $Common.storage("bodyPart", []);
              }
            }
          },
        });
        var service = new BodyPartService();
        var type = $Common.getQueryString("type");
        var gender = $Common.getQueryString("gender");
        if (type == "Tube") {
          // 导管
          // 设置人体图为单选模式
          $BodyMap.setOneMode(true);
          var selectPart = $Common.storage("selectPart");
          var tubeID = $Common.getQueryString("tubeID");
          service.getBodyPart(tubeID, gender, function (result) {
            $BodyMap.loadBodyPart(result.data);
            if (selectPart && selectPart.bodyPartCode) {
              $BodyMap.setBodyPart([selectPart.bodyPartCode]);
            }
          });
        } else if (type == "Common") {
          // 通用
          // 设置人体图为单选模式
          $BodyMap.setOneMode(true);
          var selectPart = $Common.storage("selectPart");
          var recordsCode = $Common.getQueryString("recordsCode");
          if (recordsCode) {
            service.getListByRecordsCode(
              recordsCode,
              gender,
              function (result) {
                $BodyMap.loadBodyPart(result.data);
                if (selectPart && selectPart.bodyPartCode) {
                  $BodyMap.setBodyPart([selectPart.bodyPartCode]);
                }
              }
            );
          } else {
            service.getBodyPartList(gender, function (result) {
              $BodyMap.loadBodyPart(result.data);
              if (selectPart && selectPart.bodyPartCode) {
                $BodyMap.setBodyPart([selectPart.bodyPartCode]);
              }
            });
          }
        } else if (type == "CommonMulti") {
          // 通用
          // 设置人体图为多选模式
          $BodyMap.setOneMode(false);
          var selectPart = $Common.storage("selectPart");
          var recordsCode = $Common.getQueryString("recordsCode");
          if (recordsCode) {
            service.getListByRecordsCode(
              recordsCode,
              gender,
              function (result) {
                $BodyMap.loadBodyPart(result.data);
                if (selectPart && selectPart.length > 0) {
                  selectPart.forEach((part) => {
                    $BodyMap.setBodyPart([part.bodyPartCode]);
                  });
                }
              }
            );
          } else {
            service.getBodyPartList(gender, function (result) {
              $BodyMap.loadBodyPart(result.data);
              if (selectPart && selectPart.length > 0) {
                selectPart.forEach((part) => {
                  $BodyMap.setBodyPart([part.bodyPartCode]);
                });
              }
            });
          }
        }
        if ($BodyMap.selectOneMode) {
          $Common.storage("selectPart", {});
        } else {
          $Common.storage("selectPart", []);
        }
      };
    </script>
  </body>
</html>
