/*
 * FilePath     : \ccc.web\src\api\CRRTRecord.js
 * Author       : 杨欣欣
 * Date         : 2022-08-02 11:36
 * LastEditors  : 杨欣欣
 * LastEditTime : 2022-08-09 10:19
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientCRRT";

export const urls = {
  GetCRRTRecordList: baseUrl + "/GetCRRTRecordList",
  GetCRRTCareMainListByRecordID: baseUrl + "/GetCRRTCareMainListByRecordID",
  GetCRRTAssessView: baseUrl + "/GetCRRTAssessView",
  GetCRRTRecordsCodeInfo: baseUrl + "/GetCRRTRecordsCodeInfo",
  AddCRRTRecord: baseUrl + "/AddCRRTRecord",
  AddCRRTCare: baseUrl + "/AddCRRTCare",
  UpdateCRRTRecord: baseUrl + "/UpdateCRRTRecord",
  UpdateCRRTCare: baseUrl + "/UpdateCRRTCare",
  AddCRRTEnd: baseUrl + "/AddCRRTEnd",
  DeleteCRRTByID: baseUrl + "/DeleteCRRTByID",
  DeleteCRRTCare: baseUrl + "/DeleteCRRTCare"
};
// 保存主记录
export const AddCRRTRecord = params => {
  return http.post(urls.AddCRRTRecord, params);
};
// 更新主记录
export const UpdateCRRTRecord = params => {
  return http.post(urls.UpdateCRRTRecord, params);
};
// 获取记录列表
export const GetCRRTRecordList = params => {
  return http.get(urls.GetCRRTRecordList, params);
};
// 获取维护记录列表
export const GetCRRTCareMainListByRecordID = params => {
  return http.get(urls.GetCRRTCareMainListByRecordID, params);
};
// 保存维护记录
export const AddCRRTCare = params => {
  return http.post(urls.AddCRRTCare, params);
};
// 更新维护记录
export const UpdateCRRTCare = params => {
  return http.post(urls.UpdateCRRTCare, params);
};
// 获取评估模板
export const GetCRRTAssessView = param => {
  return http.get(urls.GetCRRTAssessView, param);
};
// 获取CRRT对应的DepartmentToAssessInfo记录
export const GetCRRTRecordsCodeInfo = param => {
  return http.get(urls.GetCRRTRecordsCodeInfo, param);
};
// 删除一条维护记录
export const DeleteCRRTCare = param => {
  return http.post(urls.DeleteCRRTCare, qs.stringify(param));
};

// 删除主记录
export const DeleteCRRTByID = params => {
  return http.post(urls.DeleteCRRTByID, qs.stringify(params));
};
// CRRT停止
export const AddCRRTEnd = params => {
  return http.post(urls.AddCRRTEnd, params);
};
