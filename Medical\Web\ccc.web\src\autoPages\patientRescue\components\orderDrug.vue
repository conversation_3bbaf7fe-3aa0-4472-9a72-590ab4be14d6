<!--
 * FilePath     : \src\autoPages\patientRescue\components\orderDrug.vue
 * Author       : 马超
 * Date         : 2022-05-13 08:42
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-12-21 17:28
 * Description  : 抢救给药--医嘱
 * CodeIterationRecord:
-->
<template>
  <div class="order-drug">
    <el-table
      border
      :show-header="showHeader"
      :data="saveData"
      :header-cell-style="{
        'background-color': '#ffffff',
        border: '0',
      }"
    >
      <el-table-column label="组号" :width="convertPX(96)">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.orderGroupNo"
            placeholder=" 请输入组号"
            @change="setData(scope.$index, scope.row.orderGroupNo, 'groupNo')"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column label="药品">
        <template slot-scope="scope">
          <el-select
            placeholder="请选择药品"
            v-model="scope.row.orderDrug"
            class="select-rescue"
            popper-class="select-rescue-popper"
            @change="setData(scope.$index, scope.row.orderDrug, 'drug')"
          >
            <el-option
              v-for="(order, index) in orderList"
              :key="index"
              :label="order.rescueDrugContent"
              :value="order.rescueDrugContent"
            >
              <el-table :show-header="false" :data="order.rescueDrugList">
                <el-table-column prop="orderContent" label="药名" min-width="280"></el-table-column>
                <el-table-column prop="orderRule" label="途径" width="180"></el-table-column>
                <el-table-column prop="frequence" label="频次" width="100"></el-table-column>
                <el-table-column prop="orderDose" label="剂量" width="80"></el-table-column>
              </el-table>
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="速度" :width="convertPX(120)">
        <template slot-scope="scope">
          <el-input
            placeholder="请输入速度"
            v-model="scope.row.inputRescue"
            @change="changeInputRescue(scope.row)"
            class="input-rescue"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(50)" label="操作">
        <template slot-scope="scope">
          <el-tooltip content="删除" v-if="scope.$index !== 0">
            <i class="iconfont icon-del" @click="deleteRow(scope.$index)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div>
      <el-button class="add-button" icon="iconfont icon-add" @click="addNewRow"></el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    orderList: {
      type: Array,
      required: true,
    },
    showHeader: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      saveData: [],
    };
  },
  watch: {
    orderList: {
      deep: true,
      immediate: true,
      handler(newValue) {
        this.saveData = [];
        if (newValue) {
          this.init();
        }
      },
    },
    value: {
      deep: true,
      immediate: true,
      handler(newValue) {
        this.saveData = newValue;
        this.$emit("input", this.saveData);
      },
    },
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    init() {
      this.deelData();
      if (!this.saveData || this.saveData.length <= 0) {
        this.addNewRow();
      }
    },
    deelData() {
      if (!this.orderList || this.orderList.length <= 0) {
        return;
      }
      this.orderList.forEach((order) => {
        let rescueDrugContent = "";
        if (order.rescueDrugList && order.rescueDrugList.length > 0) {
          order.rescueDrugList.forEach((drug) => {
            let content = drug.orderContent + "( " + drug.orderDose + "),";
            if (rescueDrugContent) {
              rescueDrugContent += "";
            }
            rescueDrugContent += content;
          });
          order.rescueDrugContent = rescueDrugContent + order.rescueDrugList[0].orderRule;
        }
      });
    },
    /**
     * description: 在组件中添加新行
     * param {*}
     * return {*}
     */
    addNewRow() {
      this.saveData.push({
        orderGroupNo: "",
        orderDrug: "",
        inputRescue: "",
      });
    },
    /**
     * description: 改变值时同步改变表里面的数据
     * param {*}
     * return {*}
     */
    changeInputRescue() {
      this.$emit("input", this.saveData);
    },
    setData(rowIndex, value, type) {
      if (!value) {
        return;
      }
      if (type == "groupNo") {
        let order = this.orderList.find((order) => {
          return order.groupNo == value;
        });
        if (!order) {
          this.$set(this.saveData[rowIndex], "orderDrug", "");
          return;
        }
        this.$set(this.saveData[rowIndex], "orderDrug", order.rescueDrugContent);
      } else {
        let order = this.orderList.find((order) => {
          return order.rescueDrugContent == value;
        });
        if (!order) {
          return;
        }
        this.$set(this.saveData[rowIndex], "orderGroupNo", order.groupNo);
      }
      this.$emit("input", this.saveData);
    },
    /**
     * description: 删除行数据
     * param {*} index
     * return {*}
     */
    deleteRow(index) {
      if (this.saveData.length === 1) {
        this._showTip("warning", "至少需要一行使用药物数据！");
        return;
      }
      this.saveData.splice(index, 1);
      this.$emit("input", this.saveData);
    },
  },
};
</script>

<style lang="scss">
.order-drug {
  display: flex;
  margin-bottom: 18px;
  .select-rescue {
    width: 100%;
  }
}
.select-rescue-popper.el-select-dropdown {
  .el-select-dropdown__list .el-select-dropdown__item {
    height: auto;
  }
}
</style>
