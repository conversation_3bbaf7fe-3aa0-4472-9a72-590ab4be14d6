<!--
 * FilePath     : \src\autoPages\monitoringScheduler\component\cellTypes\textCell.vue
 * Author       : 杨欣欣
 * Date         : 2024-06-19 08:52
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-06-19 10:22
 * Description  : 自定义列内容（T类）
 * CodeIterationRecord: 
 -->
<template>
  <el-input
    v-if="row[column.index].style == 'T'"
    v-model="row[column.index].assessValue"
    @change="$listeners.change(row)"
    v-direction="{ x: column.index, y: rowIndex }"
  />
</template>
<script>
export default {
  name: "textCell",
  props: {
    row: {
      type: Object,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    rowIndex: {
      type: Number,
      required: true,
    },
  },
};
</script>