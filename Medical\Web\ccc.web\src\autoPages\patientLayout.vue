<!--
 * FilePath     : \src\autoPages\patientLayout.vue
 * Author       : 苏军志
 * Date         : 2020-05-05 00:15
 * LastEditors  : 张现忠
 * LastEditTime : 2023-03-04 15:17
 * Description  : 带病人头页面的父路由页面
 -->
<template>
  <base-layout header-height="auto" class="patient-layout">
    <patient-title v-if="isRouterAlive" slot="header" :disabled="disabled" :isRefresh="isRefresh"></patient-title>
    <router-view v-if="!$route.meta.keepAlive" ref="childPage" />
    <keep-alive>
      <!-- 需要缓存的路由加载位置 -->
      <router-view v-if="$route.meta.keepAlive" ref="childPage"></router-view>
    </keep-alive>
    <!-- 不需要缓存的路由加载位置 -->
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import patientTitle from "@/components/patientTitle";

export default {
  components: {
    baseLayout,
    patientTitle,
  },
  data() {
    return {
      disabled: false,
      isRefresh: "",
      // 路由是否存活，用于刷新页面
      isRouterAlive: true,
    };
  },
  watch: {
    $route(to, from) {
      this.disabled = false;
      if (from.query && from.query.isDischarge) {
        this.isRouterAlive = false;
        this.$nextTick(function () {
          this.isRouterAlive = true;
        });
      }
    },
  },
  created() {
    // 监听自定义事件，设置是否可以切换病人床号
    this._receiveBroadcast("setPatientSwitch", (disabled) => {
      this.disabled = !disabled;
    });
    //监听是否需要刷新病人信息
    this._receiveBroadcast("refreshInpatient", () => {
      this.isRefresh = Math.random() + "";
    });
  },
  methods: {
    // 系统顶部刷新按钮触发
    refreshData() {
      this.$nextTick(() => {
        if (this.$refs["childPage"] && this.$refs["childPage"].refreshData) {
          this.$refs["childPage"].refreshData();
        }
      });
    },
    /**
     * description: 调用子组件中的同名方法，获取系统顶部刷新按钮触发同步需要的请求参数
     * return {*}
     */
    getRefreshParams() {
      if (this.$refs["childPage"] && this.$refs["childPage"].refreshData) {
        return this.$refs["childPage"].getRefreshParams();
      }
    },
  },
};
</script>
<style lang="scss">
.patient-layout {
  width: 100%;
  height: 100%;
}
</style>
