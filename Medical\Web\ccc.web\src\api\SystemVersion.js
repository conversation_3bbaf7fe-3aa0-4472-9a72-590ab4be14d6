/*
 * FilePath     : \src\api\SystemVersion.js
 * Author       : 苏军志
 * Date         : 2022-05-08 11:27
 * LastEditors  : 苏军志
 * LastEditTime : 2022-09-12 15:21
 * Description  : 
 * CodeIterationRecord: 
 */
import http from '../utils/ajax';
import qs from "qs";
const baseUrl = '/SystemVersion'

export const urls = {
  GetVersionBySystemCode: baseUrl + '/GetVersionBySystemCode',
  SaveSystemVersion: baseUrl + '/SaveSystemVersion',
  DeleteVersionByID: baseUrl + '/DeleteVersionByID',
  GetLastVersionBySystemCode: baseUrl + '/GetLastVersionBySystemCode',
  SendUpdateMessage: baseUrl + '/SendUpdateMessage',
}
//查询所有版本更新信息
export const GetVersionBySystemCode = (params) => {
  return http.get(urls.GetVersionBySystemCode, params);
}
//保存版本信息
export const SaveSystemVersion = (params) => {
  return http.post(urls.SaveSystemVersion, params);
}
//根据VersionID删除版本信息
export const DeleteVersionByID = (params) => {
  return http.post(urls.DeleteVersionByID, qs.stringify(params));
}
//查询最后一次版本信息
export const GetLastVersionBySystemCode = (params) => {
  return http.get(urls.GetLastVersionBySystemCode, params);
}
// 发送系统更新消息
export const SendUpdateMessage = (params) => {
  return http.post(urls.SendUpdateMessage, params);
}