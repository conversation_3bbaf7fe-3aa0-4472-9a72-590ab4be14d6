<!--
 * FilePath     : \src\pages\patientThrombolysis\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-04-24 14:35
 * LastEditors  : 马超
 * LastEditTime : 2025-06-15 10:15
 * Description  : 专项护理溶栓
 * CodeIterationRecord: 2022-08-17 2876-专项增加带入护理记录选框 -杨欣欣
-->
<template>
  <specific-care
    v-model="showMaintainFlag"
    :showRecordArr="showRecordArr"
    :drawerTitle="thrombolysisDrawerTitle"
    :nursingRecordFlag="nursingRecordArr"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    :editFlag="showEditButton"
    :drawerSize="refillFlag ? '80%' : ''"
    @getMainFlag="getMainFlag"
    @getMaintainFlag="getMaintainFlag"
    @mainAdd="recordAdd"
    @maintainAdd="maintainAdd"
    @getHandOverFlag="getHandOverFlag"
    @getNursingRecordFlag="getNursingRecordFlag"
    @cancel="drawerClose"
    @save="saveThronbolysis"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    class="patient-thrombolysis"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <!-- 主记录  highlight-current-row -->
    <div slot="main-record">
      <el-table @row-click="getMainList" height="100%" :data="recordDataList" border stripe>
        <el-table-column
          prop="sourceContent"
          label="来源"
          width="50"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="occuredDepartmentName"
          label="发生科室"
          width="150"
          header-align="center"
          align="left"
        ></el-table-column>
        <el-table-column prop="entryTime" label="入科时间" width="150" header-align="center" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.entryTime, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="给药时间" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.administrationStart, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="thrombolysisKind"
          label="溶栓种类"
          width="120"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column prop="endTime" label="结束时间" width="150" header-align="center" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.endDateTime, type: 'dateTime' }"></span>
          </template>
        </el-table-column>

        <el-table-column
          prop="nurseName"
          label="新增人员"
          width="80"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop=""
          label="护理措施"
          min-width="180"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" fixed="right" header-align="center" align="center" width="60">
          <template slot-scope="scope">
            <el-tooltip content="删除">
              <div @click.stop="deleteRecordBtn(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <el-table height="100%" :data="mainDataList" border stripe>
        <el-table-column label="类型" width="50" header-align="center" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.recordsCode.indexOf('Start') != -1">给药开始</span>
            <span v-else-if="scope.row.recordsCode.indexOf('End') != -1">给药结束</span>
            <span v-else-if="scope.row.recordsCode.indexOf('Thrombolysis_24hMaintain') != -1">24小时</span>
            <span v-else>例行评估</span>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="评估时间" width="110" header-align="center" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessDate + ' ' + scope.row.assessTime, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="conscious"
          label="意识"
          width="80"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column label="左眼" header-align="center">
          <el-table-column
            prop="pupilLeft"
            label="瞳孔大小"
            width="80"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="lightReflectionLeft"
            label="光反射"
            width="80"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table-column>
        <el-table-column label="右眼" header-align="center">
          <el-table-column
            prop="pupilRight"
            label="瞳孔大小"
            width="80"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="lightReflectionRight"
            label="光反射"
            width="80"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table-column>
        <el-table-column label="生命体征" header-align="center">
          <el-table-column
            prop="bodyTemperature"
            label="体温"
            width="60"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column prop="pulse" label="脉搏" width="60" header-align="center" align="center"></el-table-column>
          <el-table-column
            prop="heartRhythm"
            label="心率"
            width="60"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="respire"
            label="呼吸"
            width="60"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column prop="systolicPressure" label="血压" width="70" header-align="center" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.systolicPressure || scope.row.diastolicPressure">
                {{
                  (scope.row.systolicPressure ? scope.row.systolicPressure : "") +
                  "/" +
                  (scope.row.diastolicPressure ? scope.row.diastolicPressure : "")
                }}
              </div>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="spO2" label="SPO2" width="60" herderPosition="center" align="center"></el-table-column>
        <el-table-column
          prop="watianTest"
          label="洼田饮水试验"
          width="80"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="nihss"
          label="NIHSS评分"
          width="80"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column label="肌力" header-align="center">
          <el-table-column
            prop="muscleStrength_LU"
            label="左上"
            width="60"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="muscleStrength_LD"
            label="左下"
            width="60"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="muscleStrength_RU"
            label="右上"
            width="60"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="muscleStrength_RD"
            label="右下"
            width="60"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table-column>
        <el-table-column label="肌张力" header-align="center">
          <el-table-column
            prop="muscularTension_LU"
            label="左上"
            width="70"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="muscularTension_LD"
            label="左下"
            width="70"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="muscularTension_RU"
            label="右上"
            width="70"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="muscularTension_RD"
            label="右下"
            width="70"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table-column>

        <el-table-column
          prop="healthLimbPosition"
          label="良肢位摆放"
          width="70"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="languageFeature"
          label="语言功能"
          width="100"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column prop="observe" label="观察措施" min-width="180"></el-table-column>
        <el-table-column label="操作" fixed="right" header-align="center" width="70" align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="maintainAdd(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip v-if="scope.row.recordsCode != 'ThrombolysisStart'" content="删除">
              <div @click.stop="deleteMainBtn(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <base-layout slot="drawer-content" header-height="auto">
      <div slot="header">
        <span class="label">执行日期:</span>
        <el-date-picker
          v-model="performDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          @change="fixObserveTemplate"
          class="drawer-content-date"
        ></el-date-picker>
        <el-time-picker
          v-model="performTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          @change="fixObserveTemplate"
          class="drawer-content-time"
        ></el-time-picker>
        <station-selector v-model="currentStation" label="执行病区:" width="160" />
        <dept-selector label="" width="140" v-model="currentDepartment" :stationID="currentStation" />
        <span v-if="recordsCodeInfo.recordsCode == 'ThrombolysisStart'" class="label">入科时间:</span>
        <el-date-picker
          v-model="entryDate"
          type="datetime"
          :clearable="false"
          :default-value="new Date()"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          placeholder="选择日期"
          class="drawer-content-entry-date"
          v-if="recordsCodeInfo.recordsCode == 'ThrombolysisStart'"
        ></el-date-picker>
      </div>
      <tabs-layout
        ref="tabsLayout"
        :template-list="templateDatas"
        @change-values="changeValues"
        @changeItem="changeItem"
        @checkTN="checkTN"
        @button-record-click="buttonRecordClick"
        @button-click="buttonClick"
        :checkFlag="true"
        v-loading="layoutLoading"
        :element-loading-text="layoutText"
      />
    </base-layout>
    <div slot="drawer-dialog">
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="ButtonRecordTitle"
        :visible.sync="showButtonRecordDialog"
        custom-class="no-footer"
      >
        <risk-component :params="conponentParams" @result="result"></risk-component>
      </el-dialog>
      <!--弹出按钮链接框-->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonName"
        :visible.sync="showButtonDialog"
        fullscreen
        custom-class="no-footer"
      >
        <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
      </el-dialog>
    </div>
  </specific-care>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import { mapGetters } from "vuex";
import { GetBringToShiftSetting } from "@/api/Setting.js";
import { GetEventDate } from "@/api/PatientEvent.js";
import { GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import {
  GetThrombolysisRecordList,
  GetThrombolysisAssessView,
  GetThrombolysisMainList,
  DeleteThrombolysisMainByMainID,
  DeleteThrombolysisRecordByRecordID,
  SaveThrombolysisRecord,
  SaveThrombolysisMain,
  EndThrombolysisAssessment,
  GetRecordIDByscheduleMainID,
  GetObserveTemplate,
} from "@/api/thrombolysis.js";
import { GetButtonData } from "@/api/Assess";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  components: {
    baseLayout,
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    riskComponent,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      entryAssessListID: 2872,
      //新增溶栓必选项
      notNullAssessListArr: [4741, 4742, 4743, 5486],
      //弹出框加载使用变量
      layoutLoading: false,
      layoutText: "",
      //评估组件使用变量
      assessDatas: [],
      checkTNFlag: undefined,
      recordsCodeInfo: {},
      entryDate: undefined,
      selectThrombolysisKindFlag: false,
      thrombolysisKind: undefined,
      performDate: undefined,
      performTime: undefined,
      templateDatas: [],
      loading: false,
      loading: false,
      showMaintainFlag: false,
      thrombolysisDrawerTitle: undefined,
      showRecordArr: [true, false],
      settingHandOver: false,
      handOverArr: [true, false],
      settingBringToNursingRecord: false,
      nursingRecordArr: [true, false],
      informPhysicianArr: [true, false],
      currentStation: undefined,
      currentDepartment: undefined,
      patientScheduleMainID: undefined,
      patientRecordID: undefined,
      patientMainID: undefined,
      //维护表格
      mainDataList: [],
      //记录表格
      recordDataList: [],
      //选中主记录
      currentMainRecord: undefined,
      routeData: {},
      sucRecordID: undefined,
      maintainRecordsCode: "ThrombolysisMaintain",
      observeTemplate: "",
      templateToAssessListID: {
        4741: "@溶栓药@",
        4742: "@溶栓药@",
        5486: "@溶栓药@",
        1957: "@血糖@",
        4831: "@吸氧@",
        1884: "@心律@",
        1887: "@心律@",
        3001240: "@输注方式@",
        3001250: "@输注方式@",
        time: "@系统时间@",
      },
      //观察措施模板ID
      observeTemplateAssessListID: 3001220,
      //是否能编辑删除该数据
      showEditButton: false,
      //BR类数据
      // 显示BR类弹窗
      showButtonRecordDialog: false,
      // BR类标题
      ButtonRecordTitle: "",
      // BR类弹窗所需参数
      conponentParams: {},
      // BR项目ID
      brAssessListID: undefined,
      // BR项修改序号
      buttonName: "",
      showButtonDialog: false,
      buttonAssessListID: undefined,
      refillFlag: "",
      patient: undefined,
      //开始评估明细内容
      startMainDetailAssessList: "",
    };
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = "";
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.refillFlag = "*";
          this.nursingRecordArr = [false, false];
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
    showButtonDialog(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
  },
  methods: {
    /**
     * description: 页面初始化
     * param {*}
     * return {*}
     */
    async init() {
      //获取主记录
      await this.getRecordListByInpatientID();
      //获取是否带入交班配置
      await this.getBringHandOverSetting();
      await this.getBringToNursingRecordSetting();
      this.mainDataList = [];
      this.showRecordArr = [true, false];
      if (Object.keys(this.$route.query).length && !this.$route.query.shortCutFlag) {
        this._sendBroadcast("setPatientSwitch", false);
        this.routeData = this.$route.query;
        await this.getThrombolysisRecordByScheduleMainID();
        if (!this.recordDataList.length) {
          return;
        }
        let successData = this.recordDataList.find((item) => item.patientThrombolysisRecordID == this.sucRecordID);
        if (!successData) {
          return;
        }
        this.currentMainRecord = successData;
        this.showRecordArr = [false, true];
        this.recordDataList = [successData];
        await this.getMainListByID();
        if (this.mainDataList.length) {
          this.sucMaintainData = this.mainDataList.find(
            (item) => item.patientScheduleMainID == this.routeData.patientScheduleMainID
          );
        }
        //打开维护记录评估弹窗
        this.maintainAdd(this.sucMaintainData);
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
    },
    /**
     * description: 根据路由参数获取获取溶栓主记录
     * param {*}
     * return {*}
     */
    async getThrombolysisRecordByScheduleMainID() {
      if (!this.routeData.patientScheduleMainID) {
        return;
      }
      let params = {
        scheduleMainID: this.routeData.patientScheduleMainID,
      };
      await GetRecordIDByscheduleMainID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.sucRecordID = res.data.recordID ? res.data.recordID : undefined;
          this.maintainRecordsCode = res.data.recordsCode ? res.data.recordsCode : "ThrombolysisMaintain";
        }
      });
    },
    /**
     * description: 获取评估模板
     * param {*}
     * return {*}
     */
    async getThrombolysisAssessView() {
      this.layoutLoading = true;
      this.layoutText = "加载中……";
      //获取观察措施模板
      if (this.recordsCodeInfo.recordsCode.indexOf("Maintain") == -1) {
        await this.GetTemplate();
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        departmentListID: this.patient.departmentListID,
        mappingType: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
      };
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        this.layoutLoading = false;
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo) return;
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        dateOfBirth: this.patient.dateOfBirth,
        inpatientID: this.patient.inpatientID,
      };
      if (this.patientRecordID) {
        params.patientThrombolysisRecordID = this.patientRecordID;
      }
      if (this.patientMainID && this.patientMainID.indexOf("temp") == -1) {
        params.thrombolysisCareMainID = this.patientMainID;
      }
      this.layoutLoading = true;
      await GetThrombolysisAssessView(params).then((result) => {
        this.layoutLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
          //更改标题
          if (this.templateDatas.length) {
            this.openDownDialag(true, this.templateDatas[0].title);
          }
        }
      });
    },
    /**
     * description: 获取主记录
     * param {*} row
     * return {*}
     */
    getMainList(row) {
      this.currentMainRecord = row;
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.recordDataList = [row];
        this.getMainListByID();
      }
    },
    /**
     * description: 主记录新增或修改
     * param {*} item
     * return {*}
     */
    async recordAdd(item) {
      if (item) {
        //判断是否可修改或删除该数据
        let ret = await this._common.getEditAuthority(
          item.patientThrombolysisCareMainID,
          "PatientThrombolysisCareMain",
          !!this.refillFlag
        );
        if (ret) {
          this.showEditButton = false;
          this._showTip("warning", ret);
        } else {
          this.showEditButton = true;
        }
      } else {
        this.showEditButton = true;
      }
      this.openDownDialag(true, "溶栓主记录");
      this.recordsCodeInfo.recordsCode = "ThrombolysisStart";
      this.templateDatas = [];
      this.performDate = item
        ? this._datetimeUtil.formatDate(item.startDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.entryDate = item ? this._datetimeUtil.formatDate(item.entryTime, "yyyy-MM-dd hh:mm") : undefined;
      this.performTime = item
        ? this._datetimeUtil.formatDate(item.startTime, "hh:mm")
        : this._datetimeUtil.getNowTime("hh:mm");
      this.currentStation = item ? item.occuredStationID : this.patient.stationID;
      this.currentDepartment = item ? item.occuredDepartmentID : this.patient.departmentListID;
      this.patientRecordID = item ? item.patientThrombolysisRecordID : undefined;
      this.patientMainID = "temp_" + this._common.guid();
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      //新增时获取该病人入科时间
      if (!item) {
        this.getEntityDate();
      }
      this.getThrombolysisAssessView();
    },
    /**
     * description: 维护记录新增或修改
     * param {*} item
     * return {*}
     */
    async maintainAdd(item) {
      if (item) {
        //判断是否可修改或删除该数据
        this.showEditButton = await this._common.checkActionAuthorization(this.user, item.addEmployeeID);
        if (this.showEditButton) {
          let ret = await this._common.getEditAuthority(
            item.patientThrombolysisRecordID,
            "PatientThrombolysisRecord",
            !!this.refillFlag
          );
          if (ret) {
            this.showEditButton = false;
            this._showTip("warning", ret);
          } else {
            this.showEditButton = true;
          }
        }
      } else {
        this.showEditButton = true;
      }
      if (this.refillFlag && this.refillFlag === "*") {
          let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,item.addEmployeeID)
          this.showEditButton = saveButtonFlag;
        }
      if (!this.currentMainRecord) {
        this._showTip("warning", "未选择需要维护的主记录");
        return;
      }
      this.openDownDialag(true, "溶栓维护记录");
      // this.maintainRecordsCode = "Thrombolysis_24hMaintain";
      this.recordsCodeInfo.recordsCode = item ? item.recordsCode : this.maintainRecordsCode;
      this.templateDatas = [];
      this.performDate = item
        ? this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = item
        ? this._datetimeUtil.formatDate(item.assessTime, "hh:mm")
        : this._datetimeUtil.getNowTime("hh:mm");
      //排程跳转过来执行时间默认为排程时间
      if (this.routeData.scheduleDate) {
        this.performDate = this._datetimeUtil.formatDate(
          this.routeData.performDate ? this.routeData.performDate : this.routeData.scheduleDate,
          "yyyy-MM-dd"
        );
        this.performTime = this._datetimeUtil.formatDate(
          this.routeData.performTime ? this.routeData.performTime : this.routeData.scheduleTime,
          "hh:mm"
        );
      }
      this.currentStation = item ? item.occuredStationID : this.patient.stationID;
      this.currentDepartment = item ? item.occuredDepartmentID : this.patient.departmentListID;
      this.patientMainID = item ? item.patientThrombolysisCareMainID : "temp_" + this._common.guid();
      this.patientRecordID = item ? undefined : this.currentMainRecord.patientThrombolysisRecordID;
      if (this.recordsCodeInfo.recordsCode == "ThrombolysisStart") {
        this.patientRecordID = this.currentMainRecord.patientThrombolysisRecordID;
        this.entryDate = this.currentMainRecord.entryTime;
      }
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      this.getThrombolysisAssessView();
    },
    /**
     * description: 抽屉打开关闭
     * param {*} flag
     * param {*} title
     * return {*}
     */
    openDownDialag(flag, title) {
      this.showMaintainFlag = flag;
      this.thrombolysisDrawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
    },
    /**
     * description: 维护记录删除
     * param {*} row
     * return {*}
     */
    async deleteMainBtn(row) {
      //是否仅本人操作
      this.showEditButton = await this._common.checkActionAuthorization(this.user, row.addEmployeeName);
      if (!this.showEditButton) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(
        row.patientThrombolysisCareMainID,
        "PatientThrombolysisCareMain",
        !!this.refillFlag
      );
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          this.deleteMain(row);
        }
      });
    },
    /**
     * description: 主记录删除
     * param {*} row
     * return {*}
     */
    async deleteRecordBtn(row) {
      //是否仅本人操作
      this.showEditButton = await this._common.checkActionAuthorization(this.user, row.nurseName);
      if (!this.showEditButton) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (this.refillFlag && this.refillFlag === "*") {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,row.addEmployeeID)
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(
        row.patientThrombolysisRecordID,
        "PatientThrombolysisRecord",
        !!this.refillFlag
      );
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          this.deleteRecord(row.patientThrombolysisRecordID);
        }
      });
    },
    /**
     * description: 维护记录删除
     * param {*} id
     * return {*}
     */
    deleteMain(row) {
      let params = {
        mainID: row.patientThrombolysisCareMainID,
        refillFlag: this.refillFlag,
      };
      DeleteThrombolysisMainByMainID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "删除成功");
          this.getRecordListByInpatientID(row.patientThrombolysisRecordID);
          this.getMainListByID();
        }
      });
    },
    /**
     * description: 删除主记录
     * param {*} id
     * return {*}
     */
    deleteRecord(id) {
      let params = {
        recordID: id,
        refillFlag: this.refillFlag,
      };
      DeleteThrombolysisRecordByRecordID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "删除成功");
          this.showRecordArr = [true, false];
          this.getMainFlag(true);
        }
      });
    },
    /**
     * description: 主记录保存
     * param {*}
     * return {*}
     */
    async saveThronbolysis() {
      this.layoutLoading = true;
      this.layoutText = "保存中……";
      //执行评估组件中初始函数 从新获取数据
      this.$refs.tabsLayout.init();
      if (this.recordsCodeInfo.recordsCode == "ThrombolysisStart") {
        await this.saveRecord();
      }
      if (this.recordsCodeInfo.recordsCode != "ThrombolysisStart") {
        await this.saveMain();
      }
      this.layoutLoading = false;
      this.layoutText = "";
    },
    async saveRecord() {
      let param = {
        NursingLevel: this.patient.nursingLevelCode,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        PatientScheduleMainID: this.patientScheduleMainID,
        bringToNursingRecord: this.nursingRecordArr[1],
        bringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        PatientThrombolysisCareMainID: this.patientMainID,
        RefillFlag: this.refillFlag,
        Record: {
          InpatientID: this.patient.inpatientID,
          ThrombolysisKind: this.thrombolysisKind,
          StartDate: this.performDate,
          StartTime: this.performTime,
          OccuredDepartmentID: this.currentDepartment,
          OccuredStationID: this.currentStation,
          EntryTime: this.entryDate,
          PatientThrombolysisRecordID: this.patientRecordID,
        },
        Details: this.getDetails(),
      };
      if (!this.saveCheck(param.Details)) {
        return;
      }
      await SaveThrombolysisRecord(param).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
          this.openDownDialag(false);
          this.showRecordArr = [true, false];
          this.getMainFlag(true);
        }
      });
    },
    /**
     * description: 维护记录保存
     * param {*}
     * return {*}
     */
    async saveMain() {
      let param = {
        NursingLevel: this.patient.nursingLevelCode,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        bringToNursingRecord: this.nursingRecordArr[1],
        bringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        RefillFlag: this.refillFlag,
        Details: this.getDetails(),
        Main: {
          PatientThrombolysisRecordID: this.currentMainRecord.patientThrombolysisRecordID,
          InpatientID: this.patient.inpatientID,
          OccuredDepartmentID: this.currentDepartment,
          OccuredStationID: this.currentStation,
          AssessDate: this.performDate,
          AssessTime: this.performTime,
          RecordsCode: this.recordsCodeInfo.recordsCode,
        },
      };
      if (this.patientMainID) {
        param.Main.PatientThrombolysisCareMainID = this.patientMainID;
      }
      if (this.routeData.patientScheduleMainID) {
        param.Main.PatientScheduleMainID = this.routeData.patientScheduleMainID;
      }
      //提醒护士24小时维护记录要填写观察措施
      if (this.recordsCodeInfo.recordsCode == "Thrombolysis_24hMaintain") {
        let sucObserve = param.Details.find((item) => item.assessListID == this.observeTemplateAssessListID);
        if (!sucObserve || !sucObserve.assessValue) {
          this._showTip("warning", "请填写给药24小时维护记录观察措施!");
          return;
        }
      }
      await SaveThrombolysisMain(param).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
          this.getRecordListByInpatientID(this.currentMainRecord.patientThrombolysisRecordID);
          this.getMainListByID();
          this.openDownDialag(false);
        }
      });
    },
    /**
     * description: 主记录停止
     * param {*} recordID
     * return {*}
     */
    async endAssess(recordID) {
      var details = [];
      this.assessDatas.forEach((element) => {
        details.push({
          AssessListID: element.assessListID,
          AssessListGroupID: element.assessListGroupID,
          AssessValue: element.assessValue,
        });
      });
      var param = {
        NursingLevel: this.patient.nursingLevelCode,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        PatientThrombolysisRecordID: this.patientRecordID,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        AssessDate: this.performDate,
        AssessTime: this.performTime,
        bringToNursingRecord: this.nursingRecordArr[1],
        Details: details,
      };
      await EndThrombolysisAssessment(param).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
          this.openDownDialag(false);
          this.getMainListByID();
        }
      });
    },
    /**
     * description: 获取维护记录
     * param {*}
     * return {*}
     */
    async getMainListByID() {
      this.loading = true;
      let params = {
        recordID: this.currentMainRecord.patientThrombolysisRecordID,
      };
      await GetThrombolysisMainList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.mainDataList = result.data;
          if (this.mainDataList.length) {
            this.mainDataList.forEach((item) => {
              if (item.assessDate) {
                item.assessDate = this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd");
              }
            });
          }
        }
      });
      this.getStartMainDetailList();
    },
    //获取主记录
    async getRecordListByInpatientID(recordID = undefined) {
      if (!this.patient) {
        return;
      }
      this.loading = true;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordID,
      };
      await GetThrombolysisRecordList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.recordDataList = result.data;
          if (this.recordDataList.length) {
            this.recordDataList.forEach((item) => {
              if (item.endDate && item.endTime) {
                item.endDateTime = this._datetimeUtil.formatDate(item.endDate, "yyyy-MM-dd") + " " + item.endTime;
              }
            });
          }
          this.currentMainRecord = recordID && this.recordDataList?.length ? this.recordDataList[0] : undefined;
        }
      });
    },
    /**
     * description: 主记录维护记录切换
     * param {*} flag
     * return {*}
     */
    getMainFlag(flag) {
      if (flag) {
        this.getRecordListByInpatientID();
        this.currentMainRecord = undefined;
        this.mainDataList = [];
      } else {
        if (!this.currentMainRecord && this.recordDataList.length) {
          this.currentMainRecord = this.recordDataList[0];
          this.recordDataList = [this.currentMainRecord];
          this.getMainListByID();
        }
      }
    },
    /**
     * description: 主记录维护记录切换
     * param {*} flag
     * return {*}
     */
    getMaintainFlag(flag) {
      if (flag) {
        if (this.currentMainRecord) {
          this.getMainListByID();
        } else {
          this.mainDataList = [];
        }
      } else {
        this.mainDataList = [];
        this.getRecordListByInpatientID();
      }
    },
    /**
     * description: 评估模板返回数据
     * param {*} datas
     * param {*} data
     * return {*}
     */
    changeValues(datas, data) {
      this.assessDatas = datas;
    },
    /**
     * description: 返回评估模板勾选项
     * param {*} data
     * return {*}
     */
    changeItem(data) {
      if (data?.assessListID == this.observeTemplateAssessListID || !data) {
        return;
      }
      this.$nextTick(() => {
        this.fixObserveTemplate();
      });
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 获取观察措施模板
     * param {*}
     * return {*}
     */
    async GetTemplate() {
      let params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
      };
      await GetObserveTemplate(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.observeTemplate = res.data;
        }
      });
    },
    /**
     * description: 调整观察措施默认模板
     * param {*}
     * return {*}
     */
    fixObserveTemplate() {
      //维护数据
      if (this.recordsCodeInfo.recordsCode.indexOf("Maintain") != -1) {
        return;
      }
      if (!this.assessDatas.length || !this.templateDatas.length) {
        return;
      }
      let template = this._common.clone(this.observeTemplate);
      if (!template) {
        return;
      }
      //更换模板内容
      Object.keys(this.templateToAssessListID).forEach((key) => {
        if (template.indexOf(this.templateToAssessListID[key]) != -1) {
          let sucAssessData = this.assessDatas.find((item) => item.assessListID == key);
          if (sucAssessData) {
            if (sucAssessData.controlerType.trim() == "C" || sucAssessData.controlerType.trim() == "R") {
              template = template.replace(this.templateToAssessListID[key], sucAssessData.itemName);
            } else {
              template = template.replace(this.templateToAssessListID[key], sucAssessData.assessValue);
            }
          }
          //系统时间处理
          if (this.templateToAssessListID[key] == "@系统时间@") {
            template = template.replace(this.templateToAssessListID[key], this.performDate + " " + this.performTime);
          }
          //结束评估模板溶栓药物处理
          if (this.recordsCodeInfo.recordsCode == "ThrombolysisEnd" && this.templateToAssessListID[key] == "@溶栓药@") {
            template = template.replace(this.templateToAssessListID[key], this.currentMainRecord.thrombolysisKind);
          }
          //输注方式处理
          if (this.startMainDetailAssessList) {
            let infusionDetail = this.startMainDetailAssessList.find((item) => item.id == key);
            if (this.templateToAssessListID[key] == "@输注方式@" && infusionDetail) {
              template = template.replace(this.templateToAssessListID[key], infusionDetail.description);
            }
          }
        }
      });
      //更新评估模板
      this.templateDatas.forEach((bookMark) => {
        if (bookMark.bookMarkID == "ThrombolysisAssess") {
          bookMark.groups.forEach((item) => {
            if (item.assessListID == this.observeTemplateAssessListID) {
              this.$set(item, "assessValue", template);
            }
          });
        }
      });
    },
    /**
     * description: 带入交班勾选框事件
     * param {*} flag
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 带入护理记录单事件
     * param {*} flag
     * return {*}
     */
    getNursingRecordFlag(flag) {
      this.nursingRecordArr[1] = flag;
    },
    /**
     * description: 通知医师标记
     * param {*} flag
     * return {*}
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    async getBringHandOverSetting() {
      let params = {
        special: "Thrombolysis",
      };
      await GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.handOverArr[0]) {
            this.settingHandOver = res.data;
          }
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    async getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "ThrombolysisAutoInterventionToRecord",
      };
      await GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingBringToNursingRecord = res.data;
          this.nursingRecordArr[1] = res.data;
        }
      });
    },
    /**
     * description: 弹窗关闭事件
     * param {*}
     * return {*}
     */
    //
    drawerClose() {
      this.openDownDialag(false);
      this.patientRecordID = undefined;
      this.patientMainID = undefined;
    },
    /**
     * description: 组装保存detail数据
     * param {*}
     * return {*}
     */
    getDetails() {
      let details = [];
      if (!this.assessDatas.length) {
        return details;
      }
      this.assessDatas.forEach((item) => {
        let detail = {
          assessListID: item.assessListID,
          assessListGroupID: item.assessListGroupID,
        };
        if (item.controlerType.trim() == "C" || item.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = item.assessValue;
        }
        details.push(detail);
      });
      return details;
    },
    /**
     * description: 保存检核
     * param {*} details
     * return {*}
     */
    saveCheck(details) {
      if (!details.length) {
        this._showTip("warning", "请填写评估内容");
        return false;
      }
      if (!this.performDate || !this.performTime) {
        this._showTip("warning", "请填写评估时间");
        return false;
      }
      if (!this.currentStation || !this.currentDepartment) {
        this._showTip("warning", "请填写评估科室");
        return false;
      }
      if (this.recordsCodeInfo.recordsCode == "ThrombolysisStart" && !this.entryDate) {
        this._showTip("warning", "请填写入科时间");
        return false;
      }
      let medical = details.filter((item) => this.notNullAssessListArr.includes(item.assessListID));
      if (this.recordsCodeInfo.recordsCode == "ThrombolysisStart" && medical.length <= 1) {
        //溶栓药与给药时间必填
        this._showTip("warning", "请选择溶栓药物和给药时间");
        return false;
      }
      return true;
    },
    /**
     * description: 获取病人入科时间
     * param {*}
     * return {*}
     */
    getEntityDate() {
      let params = {
        inpatientID: this.patient.inpatientID,
        assessListID: this.entryAssessListID,
      };
      GetEventDate(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.entryDate = res.data ? res.data : undefined;
        }
      });
    },
    /**
     * description: 初始化BR组件
     * return {*}
     * param {*} content BR跳转风险项
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      this.conponentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: this.recordsCodeInfo.recordsCode + "BR",
        sourceID: this.getCareMainID(),
        assessTime:
          this._datetimeUtil.formatDate(this.performDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.performTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    result(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brAssessListID);
      }
    },

    /**
     * description: 评估内容回显
     * param {*} assessListID
     * return {*}
     */
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    /**
     * description: 按钮数据回显
     * param {*} assessListID
     * return {*}
     */
    async getButtonValue(assessListID) {
      let item = "";
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessListID: assessListID,
        sourceID: this.getCareMainID(),
        sourceType: this.recordsCodeInfo.recordsCode + "BR",
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },

    /**
     * description: 维护记录ID组装
     * param {*}
     * return {*}
     */
    getCareMainID() {
      let mainID = "";
      if (this.patientMainID) {
        if (this.patientMainID.indexOf("temp") != -1) {
          mainID = this.patientMainID.split("_")[1];
        } else {
          mainID = this.patientMainID;
        }
      }
      return mainID;
    },
    /**
     * description: 评估模板按钮处理
     * param {*} content
     * return {*}
     */
    buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      if (url.indexOf("?") == -1) {
        url += "?";
      }
      this.showButtonDialog = true;
      url +=
        "&bedNumber=" +
        this.patient.bedNumber.replace(/\+/g, "%2B") +
        "&userID=" +
        this.user.userID +
        "&token=" +
        this.token +
        "&isDialog=true";
      this.showButtonDialog = true;
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 获取开始评估维护明细记录
     * return {*}
     */
    getStartMainDetailList() {
      let startMainData = this.mainDataList.find((main) => main.recordsCode.includes("Start"));
      if (startMainData) {
        this.startMainDetailAssessList = startMainData.thrombolysisCareDetailAssessList;
      }
    },
  },
};
</script>
<style lang="scss">
.patient-thrombolysis {
  iframe {
    height: 99%;
    width: 100%;
    border: 0;
    overflow: hidden;
  }
  .drawer-content {
    .drawer-content-date {
      width: 120px;
    }
    .drawer-content-time {
      width: 80px;
    }
    .drawer-content-entry-date {
      width: 170px;
    }
    .station-selector .label {
      margin-left: 0px;
    }
  }
}
</style>