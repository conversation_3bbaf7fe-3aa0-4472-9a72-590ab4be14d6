<!--
 * FilePath     : \src\components\tabsLayout\mtItem.vue
 * Author       : 苏军志
 * Date         : 2022-07-18 14:36
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-30 09:15
 * Description  : 多行输入框，支持双击弹出选择内容
 * CodeIterationRecord:
-->
<template>
  <div class="mt-item">
    <span v-if="label">{{ label }}：</span>
    <div
      v-show="!showInput"
      class="custom-input"
      :style="style"
      v-html="textStyleContent"
      @click="showInput = true"
    ></div>
    <el-input
      v-show="showInput"
      type="textarea"
      v-model="textContent"
      resize="none"
      :placeholder="placeholder"
      :readonly="readonly"
      :autosize="{ minRows: 3, maxRows: 20 }"
      :style="style"
      @change="mtChange"
      @dblclick.native="openSelectTemplate"
      @blur="showInput = true"
    ></el-input>
    <el-dialog
      append-to-body
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="showSelectTemplate"
      :title="title + '选择'"
      custom-class="mt-table-dialog"
    >
      <div class="template-wrap" v-if="showSelectTemplate">
        <el-table
          v-if="displayType == 'Table'"
          :data="tableRows"
          border
          stripe
          height="100%"
          @selection-change="tableSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center"></el-table-column>
          <el-table-column
            v-for="(column, index) in tableColumns"
            :key="index"
            :label="column.title"
            :prop="column.name"
            :width="column.width ? column.width : 'auto'"
            :align="column.width ? 'center' : 'left'"
            header-align="center"
          >
            <template slot-scope="scope">
              <span v-html="scope.row[column.name]"></span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button @click="showSelectTemplate = false">取消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      require: true,
    },
    // 只读
    readonly: {
      type: Boolean,
      default: false,
    },
    // 标签及弹出标题
    label: {
      type: String,
      default: "",
    },
    // 宽度
    width: {
      type: String,
      default: "200px",
    },
    // 标签及弹出标题
    title: {
      type: String,
      default: "",
    },
    // 弹窗模板，为空时为正常的多行录入框
    template: {
      type: Object,
      default: "",
    },
    assessValueStyle: {
      type: String,
      default: "",
    },
    customMethods: {
      type: Function,
      default: undefined,
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        this.textContent = newValue.replace(/<span style="color: #ff0000;">(.*?)<\/span>/g, "$1").replace("<br/>", "");
      },
    },
    assessValueStyle: {
      immediate: true,
      handler(newValue) {
        this.textStyleContent = newValue;
      },
    },
    template: {
      immediate: true,
      handler(newValue) {
        this.showInput = !!newValue?.styleSwitch;
      },
    },
  },
  data() {
    return {
      // 输入框提示信息
      placeholder: "",
      // 组件内输入框绑定数据
      textContent: "",
      // 是否显示选择模板弹窗
      showSelectTemplate: false,
      // 弹窗呈现方式，默认Table，且目前仅支持Table
      displayType: "Table",
      // 内容回显格式模板
      contentFormat: "",
      // 模板表格数据
      tableRows: [],
      // 模板表格列
      tableColumns: [],
      // 模板表格选择的数据
      selectTableData: [],
      //
      textStyleContent: "",
      showInput: true,
    };
  },
  computed: {
    style() {
      return {
        width: this._common.getHeigt(this.width),
      };
    },
  },
  created() {
    if (this.template) {
      this.placeholder = "请输入内容或双击选择内容";
    } else {
      this.placeholder = "请输入内容";
    }
  },
  methods: {
    /**
     * description: 输入框失焦事件
     * param {*} newValue
     * return {*}
     */
    mtChange(newValue) {
      this.$emit("input", newValue);
      this.$emit("change", newValue);
      if (!this.template?.styleSwitch) {
        this.showInput = false;
      }
    },
    /**
     * description: 打开选择模板数据弹窗
     * param {*}
     * return {*}
     */
    openSelectTemplate() {
      if (!this.template) {
        return;
      }
      this.displayType = "Table";
      this.contentFormat = "";
      this.tableRows = [];
      this.tableColumns = [];
      this.selectTableData = [];
      this.showSelectTemplate = true;
      if (this.template.contentFormat) {
        this.contentFormat = this.template.contentFormat || "";
      }
      if (this.template.displayType) {
        this.displayType = this.template.displayType;
      }
      if (this.template.templateData) {
        // 暂时只有表格类型， 后续有其他类型可以扩展
        if (this.displayType == "Table") {
          this.tableRows = this.template.templateData.rows || [];
          this.tableColumns = this.template.templateData.columns.filter((column) => column.name !== "TestResultStyle");
        }
      }
    },
    /**
     * description: 表格选择回调方法
     * param {*} datas 选择数据
     * return {*}
     */
    tableSelectionChange(datas) {
      this.selectTableData = datas;
    },
    /**
     * description: 确认勾选内容，组装回显信息
     * param {*}
     * return {*}
     */
    confirm() {
      if (!this.selectTableData || this.selectTableData.length <= 0) {
        this._showTip("warning", "没有勾选内容！");
        return;
      }
      let content = "";
      let styleContent = "";
      // 表格类型确认回显
      if (this.displayType == "Table") {
        let contentArry = this.getTableReturnContent();
        content = contentArry[0];
        styleContent = contentArry[1];
      }
      this.textContent = content;
      this.textStyleContent = styleContent;
      this.mtChange(this.textStyleContent);
      this.showSelectTemplate = false;
    },
    /**
     * description: 获取模板表格选择数据的回显信息
     * param {*}
     * return {*}
     */
    getTableReturnContent() {
      let returnColumns = this.tableColumns;
      let [content, styleContent] = this.customMethods
        ? this.customMethods(this.contentFormat, this.selectTableData)
        : [];
      if (content || styleContent) {
        return [content, styleContent];
      }
      styleContent ??= "";
      if (this.contentFormat) {
        returnColumns = this.tableColumns.filter((column) => {
          return this.contentFormat.indexOf(column.assessListID) != -1;
        });
      }
      this.selectTableData.forEach((data) => {
        let text = this.contentFormat;
        returnColumns.forEach((column) => {
          let value = data[column.name].replace("<br/>", "，");
          text = text.replace("[" + column.assessListID + "]", text ? value : "");
        });
        if (text) {
          content = content ? content + "；" + text : text;
        }
        if (styleContent) {
          styleContent += "；";
        }
        if (data.TestResultStyle && data.TestResultStyle.length > 0) {
          styleContent += data.TestResultStyle;
        } else {
          styleContent += text;
        }
      });

      return styleContent ? [content, styleContent] : [content, content];
    },
  },
};
</script>

<style lang="scss">
.mt-item {
  display: flex;
}

.mt-table-dialog {
  .template-wrap {
    height: 100%;
    width: 100%;
  }
}

.custom-input {
  height: 86px;
  width: 520px;
  border: 1px solid #e9e1e1;
  font-size: 16px;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}
</style>