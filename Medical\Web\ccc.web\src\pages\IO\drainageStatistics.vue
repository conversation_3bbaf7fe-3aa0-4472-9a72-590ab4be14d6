<!--
 * FilePath     : \src\pages\IO\drainageStatistics.vue
 * Author       : 苏军志
 * Date         : 2022-05-08 11:27
 * LastEditors  : 来江禹
 * LastEditTime : 2023-07-31 08:58
 * Description  : 引流液统计
 * CodeIterationRecord: 
 * 1、2690-作为护理人员，我需要出入水量查询，每个页签都可以根据自然时间查询
 * 2、3013-作为IT人员，我需要出入量的班别汇总、每天汇总单独页签呈现
-->
<template>
  <base-layout class="drainage-statistics">
    <div slot="header" class="top">
      <span class="label">{{ $t("ioStatistics.switchLabel") }}</span>
      <el-switch v-model="queryByShift" />
      <div v-show="queryByShift" class="where">
        <span class="label">{{ $t("label.date") }}</span>
        <el-date-picker
          v-model="startShiftTime"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="date"
          class="date-picker"
          :placeholder="placeholder.date"
          :picker-options="pickerOptionscreate"
        ></el-date-picker>
        <span>-</span>
        <el-date-picker
          v-model="endShiftTime"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          type="date"
          class="date-picker"
          :placeholder="placeholder.date"
          :picker-options="pickerOptionsend"
        ></el-date-picker>
        <span class="label">{{ drainageStatistics.conditionsLabel }}</span>
        <el-select v-model="choice" class="choice" style="width: 150px">
          <el-option
            v-for="item in options"
            :key="item.typeValue"
            :label="item.settingValue"
            :value="item.typeValue"
          ></el-option>
        </el-select>
      </div>
      <div class="where" v-show="!queryByShift">
        <span class="label">{{ $t("label.date") }}</span>
        <el-date-picker
          v-model="startTime"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          :placeholder="placeholder.date"
          class="datetime-picker"
        ></el-date-picker>
        <span>-</span>
        <el-date-picker
          v-model="endTime"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          :placeholder="placeholder.date"
          class="datetime-picker"
        ></el-date-picker>
      </div>
      <station-selector
        v-model="stationID"
        :label="$t('label.station')"
        :inpatientID="inpatient ? inpatient.inpatientID : ''"
        width="160"
      ></station-selector>
      <el-button class="query-button" icon="iconfont icon-search" @click="searchData()">{{ button.query }}</el-button>
    </div>
    <div class="iorecords-table">
      <el-table
        :row-style="subtotalrow"
        show-summary
        :summary-method="totalCount"
        :data="tableData"
        height="100%"
        border
        ref="multipleTable"
      >
        <el-table-column :label="drainageStatistics.date" resizable min-width="110" align="center">
          <template slot-scope="scope">
            {{ scope.row.date }}
          </template>
        </el-table-column>
        <el-table-column :label="drainageStatistics.time" resizable min-width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.time }}
          </template>
        </el-table-column>

        <el-table-column
          min-width="100"
          align="center"
          v-for="(column, index) in columns"
          :key="index"
          :label="column.title"
          resizable
        >
          <el-table-column
            resizable
            :prop="column.name + '*intakeOutputVolume'"
            :label="drainageStatistics.amount"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row[column.name]" :key="index">
                {{ item.intakeOutputVolume }}
              </div>
            </template>
          </el-table-column>
          <el-table-column resizable :label="drainageStatistics.character" min-width="100" align="center">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row[column.name]" :key="index">
                {{ item.traits }}
              </div>
            </template>
          </el-table-column>
          <el-table-column resizable :label="drainageStatistics.color" min-width="100" align="center">
            <template slot-scope="scope">
              <div v-for="(item, index) in scope.row[column.name]" :key="index" class="drainage-color">
                <div class="drainage-color-div" :style="'backgroundColor:' + item.color"></div>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="day-shift-total" v-if="queryByShift">
      <el-table show-summary :summary-method="totalCount" border :data="drainageStatisticsShiftList">
        <el-table-column :label="drainageStatistics.date" resizable width="110" align="center">
          <template slot-scope="scope">
            {{ scope.row.date.substring(0, 10) }}
          </template>
        </el-table-column>
        <el-table-column resizable :label="drainageStatistics.shift" min-width="80" align="center">
          <template slot-scope="scope">
            {{ scope.row.ShiftName }}
          </template>
        </el-table-column>
        <el-table-column resizable :label="drainageStatistics.startTime" min-width="70" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.ShiftStartTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column resizable :label="drainageStatistics.endTime" min-width="70" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.ShiftEndTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          resizable
          min-width="100"
          align="center"
          v-for="(column, index) in drainageStatisticsShiftColumns"
          :key="index"
          :prop="column.name + '*intakeOutputVolume'"
          :label="column.title"
        >
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row[column.name]" :key="index">
              {{ item.intakeOutputVolume }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="day-shift-total" v-else>
      <el-table border :data="drainageStatisticsDayList" show-summary :summary-method="totalCount">
        <el-table-column resizable :label="drainageStatistics.date" width="110" align="center">
          <template slot-scope="scope">
            {{ scope.row.date.substring(0, 10) }}
          </template>
        </el-table-column>
        <el-table-column resizable :label="drainageStatistics.startTime" min-width="70" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column resizable :label="drainageStatistics.endTime" min-width="70" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.endTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column
          resizable
          min-width="100"
          align="center"
          v-for="(column, index) in drainageStatisticsDayColumns"
          :key="index"
          :prop="column.name + '*intakeOutputVolume'"
          :label="column.title"
        >
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row[column.name]" :key="index">
              {{ item.intakeOutputVolume }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetClinicalSettingInfo } from "@/api/Assess";
import { GetOutputDocument, GetTubeOutStatistics } from "@/api/PatientCatheterDrainage";
import { mapGetters } from "vuex";
import stationSelector from "@/components/selector/stationSelector";
export default {
  components: {
    baseLayout,
    stationSelector,
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
    }),
    placeholder() {
      return this.$t("placeholder");
    },
    button() {
      return this.$t("button");
    },
    drainageStatistics() {
      return this.$t("drainageStatistics");
    },
  },
  watch: {
    inpatient(newV) {
      if (!newV) return;
      this.tableData = [];
      this.columns = [];
      this.initQueryParams();
      this.searchData();
    },
    stationID: {
      async handler(newV) {
        if (!newV) return;
        this.tableData = [];
        this.columns = [];
        this.initQueryParams();
        this.searchData();
      },
    },
    queryByShift: {
      handler() {
        this.tableData = [];
        this.columns = [];
        this.tableData = [];
        this.initQueryParams();
        this.searchData();
      },
    },
  },
  data() {
    let that = this;
    return {
      //汇总查询开始结束时间
      drainaStartTime: "",
      drainaEndTiem: "",
      // 起始班别日期
      startShiftTime: "",
      // 结束班别日期
      endShiftTime: "",
      // 起始日期
      startTime: "",
      // 结束日期
      endTime: "",
      //返回颜色
      intakeOutputVolumeColor: [],
      //每日每班总汇
      activeName: "first",
      //引流液每日汇总
      drainageStatisticsDayList: [],
      //日期导管名称
      dateName: [],
      drainageStatisticsDayColumns: [],
      drainageStatisticsShiftColumns: [],
      drainageStatisticsShiftList: [],
      pickerOptionscreate: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.endShiftTime).getTime();
        },
      },
      pickerOptionsend: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.startShiftTime).getTime() - 8.64e7;
        },
      },
      //选择加总时间
      options: [],
      //默认选择加载时间
      choice: "0",
      //列数据
      columns: [],
      //日期时间固定列
      tableData: [],
      stationID: undefined,
      queryByShift: true,
    };
  },
  //页面加载
  mounted() {
    this.stationID = this.inpatient.stationID;
    this.init();
  },
  //方法
  methods: {
    init() {
      this.initQueryParams();
      this.getIOSummaryInterval();
    },
    initQueryParams() {
      let nowDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      if (this.queryByShift) {
        this.startShiftTime = nowDate;
        this.endShiftTime = nowDate;
      } else {
        this.startTime = nowDate + " 00:00";
        this.endTime = nowDate + " 23:59";
      }
    },
    async getIOSummaryInterval() {
      let params = {
        SettingTypeCode: "IOSummaryInterval",
      };
      await GetClinicalSettingInfo(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.options = res.data;
        }
      });
    },
    //查询导管引流液数据
    getCatheterDrainage() {
      this.tableData = [];
      this.columns = [];
      let params = {
        inpatientID: this.inpatient.inpatientID,
        stationID: this.stationID,
        queryByShift: this.queryByShift,
      };
      if (this.queryByShift) {
        params.interval = this.choice;
        params.startTime = this.startShiftTime;
        params.endTime = this.endShiftTime;
      } else {
        params.startTime = this.startTime;
        params.endTime = this.endTime;
      }
      GetOutputDocument(params).then((response) => {
        if (this._common.isSuccess(response) && response.data) {
          this.tableData = response.data.rows;
          this.columns = response.data.columns;
          this.$nextTick(() => {
            this.$refs.multipleTable && this.$refs.multipleTable.doLayout();
          });
        }
      });
    },
    /**
     * description: 引流也汇总查询(日期汇总、班别汇总)
     * return {*}
     */
    getdrainageStatistics() {
      this.drainageStatisticsDayList = [];
      this.drainageStatisticsDayColumns = [];
      this.drainageStatisticsShiftColumns = [];
      this.drainageStatisticsShiftList = [];
      if (this.queryByShift) {
        this.drainaStartTime = this.startShiftTime;
        this.drainaEndTiem = this.endShiftTime;
      } else {
        this.drainaStartTime = this.startTime;
        this.drainaEndTiem = this.endTime;
      }
      let params = {
        inpatientID: this.inpatient.inpatientID,
        stationID: this.stationID,
        startTime: this.drainaStartTime,
        endTime: this.drainaEndTiem,
      };
      GetTubeOutStatistics(params).then((response) => {
        if (
          this._common.isSuccess(response) &&
          response.data &&
          response.data.tubeOStatisticsDay &&
          response.data.tubeOStatisticsShift
        ) {
          //给予每班汇总，每日汇总字段和信息
          this.drainageStatisticsDayColumns = response.data.tubeOStatisticsDay.columns;
          this.drainageStatisticsDayList = response.data.tubeOStatisticsDay.rows;
          this.drainageStatisticsShiftColumns = response.data.tubeOStatisticsShift.columns;
          this.drainageStatisticsShiftList = response.data.tubeOStatisticsShift.rows;
        }
      });
    },
    /**
     * description: 查询按钮功能
     * return {*}
     */
    searchData() {
      this.getCatheterDrainage();
      this.getdrainageStatistics();
      this.$refs.multipleTable.doLayout();
    },
    // 小计行样式
    subtotalrow(row) {
      if (row.row.date == this.drainageStatistics.subtotal) {
        return { backgroundColor: "#FFFFDF" };
      }
    },
    // 总计的计算
    totalCount(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        //首列返回加总字符串
        if (index === 0) {
          sums[index] = this.drainageStatistics.total;
          return;
        }
        //非计算列过滤
        if (!column.property) {
          return;
        }
        let dataMark = column.property.split("*");
        let attrName = dataMark[0];
        const values = data?.map((item) => {
          if (!item[attrName] || item.date == this.drainageStatistics.subtotal) {
            return 0;
          }
          let list = item[attrName];
          let total = 0;
          for (let i = 0; i < list.length; i++) {
            total += list[i][dataMark[1]];
          }
          return total;
        });
        if (values && !values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index];
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
  },
};
</script>

<style lang="scss">
.drainage-statistics {
  .top {
    .where {
      display: inline-block;
      .label {
        margin-left: 10px;
      }
      .date-picker {
        width: 110px;
      }
      .datetime-picker {
        width: 150px;
      }
    }
  }
  .iorecords-table {
    width: 100%;
    height: 60%;
    margin-bottom: 5px;
    .drainage-color {
      height: 23px;
      padding: 5px 0;
      box-sizing: border-box;
      .drainage-color-div {
        height: 100%;
      }
    }
  }
  .day-shift-total {
    width: 100%;
  }
}
</style>
