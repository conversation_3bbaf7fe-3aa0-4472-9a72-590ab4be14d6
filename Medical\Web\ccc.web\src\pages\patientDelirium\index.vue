<!--
 * FilePath     : \src\pages\patientDelirium\index.vue
 * Author       : 曹恩
 * Date         : 2021-10-30 08:53
 * LastEditors  : 马超
 * LastEditTime : 2024-10-13 11:10
 * Description  : 谵妄评估
 * CodeIterationRecord: 2022-08-18 2876-专项增加带入护理记录选框 -杨欣欣
                        2023-06-27 3587-护理评估按钮跳转专项录入的内容评估时间跟护理评估保持一致 -杨欣欣
-->
<template>
  <specific-care
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :recordTitleSlotFalg="true"
    :handOverFlag="handOverArr"
    :nursingRecordFlag="nursingRecordArr"
    :previewFlag="!checkResult"
    :informPhysicianFlag="informPhysicianArr"
    :drawerSize="supplementFlag ? '80%' : ''"
    class="patient-delirium"
    v-loading="loading"
    element-loading-text="加载中……"
    @mainAdd="recordAdd"
    @save="saveDelirium"
    @getHandOverFlag="getHandOverFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @cancel="showTemplateFlag = false"
  >
    <div slot="record-title">
      <label>日期：</label>
      <el-date-picker
        class="date-picker"
        v-model="recordDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        @change="getTableView()"
      />
    </div>
    <div slot="main-record">
      <el-table :data="recordList" height="100%" border stripe>
        <el-table-column label="日期" width="100" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="时间" width="60" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="stationName" label="评估病区" min-width="90" header-align="center"></el-table-column>
        <el-table-column prop="nursingLevel" label="护理级别" width="90" align="center"></el-table-column>
        <el-table-column prop="riskTool" label="意识水平评估工具" min-width="100" align="center"></el-table-column>
        <el-table-column prop="riskResult" label="评估分数" min-width="90" align="center"></el-table-column>
        <el-table-column prop="acuteFluctuation" label="意识波动分数" min-width="90" align="center"></el-table-column>
        <el-table-column prop="inattentior" label="注意力障碍分数" min-width="100" align="center"></el-table-column>
        <el-table-column
          prop="disorganizedThinking"
          label="思维紊乱分数"
          min-width="90"
          align="center"
        ></el-table-column>
        <el-table-column prop="assessResult" label="谵妄评估结果" min-width="90" align="center"></el-table-column>
        <el-table-column prop="measures" label="护理措施" min-width="200" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120" align="center"></el-table-column>
        <el-table-column prop="userName" label="记录人" width="80" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" width="70" align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="recordAdd(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="recordDelete(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="layoutLoading"
      :element-loading-text="layoutText"
    >
      <div slot="header">
        <span class="label">评估日期:</span>
        <el-date-picker
          class="date-picker"
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        />
        <el-time-picker
          class="time-picker"
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        />
        <station-selector v-model="stationID" label="评估病区:" width="160"></station-selector>
        <dept-selector label="" width="140" v-model="departmentListID" :stationID="stationID"></dept-selector>
      </div>
      <tabs-layout ref="tabsLayout" :template-list="templateDatas" @change-values="changeValues" />
    </base-layout>
  </specific-care>
</template>
<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
import { GetBringToShiftSetting } from "@/api/Setting.js";
import { GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import {
  GetDeliriumAssesssView,
  SaveDeliriumCare,
  GetDeliriumTableView,
  DeleteDeliriumCare,
} from "@/api/PatientDelirium";
export default {
  components: {
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    baseLayout,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      //页面加载
      loading: false,
      layoutLoading: false,
      layoutText: undefined,
      //组件变量
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      handOverArr: [true, false],
      settingHandOver: false,
      nursingRecordArr: [false, false],
      settingBringToNursingRecord: false,
      informPhysicianArr: [true, false],
      //顶部时间变量
      recordDate: this._datetimeUtil.getNowDate(),
      //评估模板变量
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      departmentListID: undefined,
      recordsCodeInfo: {},
      templateDatas: [],
      assessDatas: [],
      //表格变量
      recordList: [],
      careMainID: undefined,
      scheduleMainID: undefined,
      supplementFlag: undefined,
      patientScheduleMainID: undefined,
      checkResult: true,
      sourceID: undefined,
      sourceType: undefined,
      patient: undefined,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  watch: {
    "patientInfo.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.supplementFlag = undefined;
        }
      },
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.supplementFlag = "*";
          this.nursingRecordArr = [false, false];
        }
      },
    },
    "patient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getTableView();
        }
      },
    },
  },
  mounted() {
    this.recordDate = this._datetimeUtil.getNowDate();
    //跳转不允许切换病人  右键进入除外
    if (Object.keys(this.$route.query).length && !this.$route.query.shortCutFlag) {
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
    this.getBringHandOverSetting();
    this.getBringToNursingRecordFlag();
    this.getTableView();
    this.getRoute();
  },
  methods: {
    /**
     * description: 获取路由携带过来的参数
     * return {*}
     */
    getRoute() {
      if (this.$route.query.patientScheduleMainID && this.$route.query.patientScheduleMainID != "null") {
        this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
      }
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "PatientDelirium",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.handOverArr[0]) {
            this.settingHandOver = res.data;
          }
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "DeliriumCareAutoInterventionToRecord",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingBringToNursingRecord = res.data;
        }
      });
    },
    /**
     * description: 弹窗开关函数
     * param {*} flag 开/关
     * param {*} title 标题
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
    },
    /**
     * description: 获取组件选中值
     * param {*} details 修改后的明细
     * return {*}
     */
    changeValues(details) {
      this.assessDatas = details;
    },
    /**
     * description: 组件回传交班flag
     * param {*} flag
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.nursingRecordArr[1] = flag;
    },
    /**
     * description: 获取表格数据
     * return {*}
     */
    getTableView() {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      if (this.recordDate) {
        params.date = this.recordDate;
      }
      this.loading = true;
      GetDeliriumTableView(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.recordList = res.data;
        }
      });
    },
    /**
     * description: 记录新增或修改
     * param {*} record 主记录
     * return {*}
     */
    async recordAdd(record) {
      this.checkResult = true;
      if (record) {
        //是否仅本人操作
        this.checkResult = await this._common.checkActionAuthorization(this.user, record.nurseID);
        if(this.supplementFlag === "*"){
          let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,record.userID);
          this.checkResult = saveButtonFlag;
        }
      }
      this.openOrCloseDrawer(true, "新增");
      this.stationID = record ? record.stationID : this.patient.stationID;
      this.departmentListID = record ? record.departmentListID : this.patient.departmentListID;
      const defaultAssessDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
      const defaultAssessTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
      this.assessDate = record ? record.assessDate : defaultAssessDate;
      this.assessTime = record ? record.assessTime : defaultAssessTime;
      this.careMainID = record ? record.patientDeliriumCareMainID : undefined;
      this.$set(this.handOverArr, 1, record ? record.bringToShift : this.settingHandOver);
      this.$set(this.nursingRecordArr, 1, record ? record.bringToNursingRecord : this.settingBringToNursingRecord);
      this.$set(this.informPhysicianArr, 1, record && record.informPhysician ? true : false);
      this.getDeliriumAssessTemplate();
    },
    /**
     * description: 获取评估模板
     * return {*}
     */
    async getDeliriumAssessTemplate() {
      this.templateDatas = [];
      let params = {
        inpatientID: this.patient.inpatientID,
        departmentListID: this.patient.departmentListID,
        mappingType: "DeliriumRecord",
        age: this.patient.age,
      };
      this.layoutLoading = true;
      this.layoutText = "加载中……";
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        this.layoutLoading = false;
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo) {
        return;
      }
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        stationID: this.patient.stationID,
        dateOfBirth: this.patient.dateOfBirth,
        inpatientID: this.patient.inpatientID,
      };
      if (this.careMainID) {
        params.deliriumCareMainID = this.careMainID;
      }
      await GetDeliriumAssesssView(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.templateDatas = res.data;
        }
      });
      this.layoutLoading = false;
      this.layoutText = "";
    },
    /**
     * description: 记录保存
     * return {*}
     */
    saveDelirium() {
      if (!this.patient || Object.keys(this.recordsCodeInfo).length == 0) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        stationID: this.stationID,
        departmentListID: this.departmentListID,
        assessDate: this.assessDate,
        assessTime: this.assessTime,
        recordsCode: this.recordsCodeInfo.recordsCode,
        interventionID: this.recordsCodeInfo.interventionMainID,
        bringToShift: this.handOverArr[1],
        bringToNursingRecord: this.nursingRecordArr[1],
        details: this.getDetails(),
        supplementFlag: this.supplementFlag,
        patientScheduleMainID: this.patientScheduleMainID,
        informPhysician: this.informPhysicianArr[1],
        SourceID: this.sourceID,
        SourceType: this.sourceType,
      };
      if (params.details.length == 0) {
        return;
      }
      if (this.careMainID) {
        params.patientDeliriumCareMainID = this.careMainID;
      }
      this.layoutLoading = true;
      this.layoutText = "保存中……";
      SaveDeliriumCare(params).then((res) => {
        this.layoutLoading = false;
        this.layoutText = "";
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.openOrCloseDrawer(false);
          this.getTableView();
        }
      });
    },
    /**
     * description: 组装保存detail数据
     * return {*}
     */
    getDetails() {
      let details = [];
      if (!this.assessDatas.length) {
        return details;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return details;
      }
      this.assessDatas.forEach((item) => {
        let detail = {
          assessListID: item.assessListID,
          assessListGroupID: item.assessListGroupID,
        };
        if (item.controlerType.trim() == "C" || item.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = item.assessValue;
        }
        details.push(detail);
      });
      return details;
    },
    /**
     * description: 记录删除
     * param {*} record 主记录
     * return {*}
     */
    async recordDelete(record) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, record.nurseID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (this.supplementFlag === "*") {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,record.userID)
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      if (!record || !record.patientDeliriumCareMainID) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            patientDeliriumCareMainID: record.patientDeliriumCareMainID,
            supplementFlag: this.supplementFlag,
          };
          this.loading = true;
          DeleteDeliriumCare(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this.loading = false;
              this._showTip("success", "删除成功");
              this.getTableView();
            }
          });
        }
      });
    },
    /**
     * description: 通知医师标记
     * param {*} flag
     * return {*}
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
  },
};
</script>
<style lang="scss" >
.patient-delirium {
  .date-picker {
    width: 120px;
  }
  .time-picker {
    width: 80px;
  }
  .station-selector .label {
    margin-left: 0px;
  }
}
</style>