/*
 * FilePath     : \ccc.web\src\utils\message.js
 * Author       : LX
 * Date         : 2020-05-21 09:33
 * LastEditors  : LX
 * LastEditTime : 2021-05-02 10:10
 * Description  : 
 */
import Vue from "vue";

/**
 * 删除时提醒对话框，callback回调方法
 */
const deleteConfirm = (text, callback) => {
  if (!text) {
    text = "确定要删除此记录吗?";
  }
  Vue.prototype
    .$confirm(text, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      dangerouslyUseHTMLString: true, // 支持html标签
    })
    .then(() => {
      callback(true);
    })
    .catch(() => {
      callback(false);
    });
};
const confirm = (text = '确定要删除此记录吗?', title = '删除确认', callback) => {
  Vue.prototype
    .$confirm(text, title, {
      cancelButtonText: "取消",
      confirmButtonText: "确定",
      type: "warning",
      dangerouslyUseHTMLString: true, // 支持html标签
    })
    .then(() => {
      callback(true);
    })
    .catch(() => {
      callback(false);
    });
};
const alert = (text, title) => {
  Vue.prototype
    .$alert(text, title, {     
      dangerouslyUseHTMLString: true, // 支持html标签
    }).catch(() => {
    });    
};
Vue.prototype._deleteConfirm = deleteConfirm;
Vue.prototype._confirm = confirm;
Vue.prototype._alert = alert;

export default {
  deleteConfirm,
  confirm,
  alert
};
