/*
 * FilePath     : \src\api\DocumentChange.js
 * Author       : 郭鹏超
 * Date         : 2021-06-27 16:26
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:44
 * Description  :病例转换API()
 */
import http from "../utils/ajax";
const baseUrl = "/DocumentChange";

export const urls = {
  DocumentChangeStart: baseUrl + "/DocumentChangeStart",
  GetDocumentChangePatientList: baseUrl + "/GetDocumentChangePatientList"
};
//病历转换
export const DocumentChangeStart = params => {
  return http.post(urls.DocumentChangeStart, params);
};
//获取转换病人列表
export const GetDocumentChangePatientList = params => {
  return http.get(urls.GetDocumentChangePatientList, params);
};
