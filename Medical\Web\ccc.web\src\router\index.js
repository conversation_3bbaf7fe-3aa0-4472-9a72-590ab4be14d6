import Vue from "vue";
import Router from "vue-router";
import autoPages from "./autoPages";
import pages from "./pages";
// 引入单病人执行画面的路由
import singlePatientBatchExecution from "./modules/singlePatientBatchExecution";

// 版本3.0后的 避免控制台会报[NavigationDuplicated {_name: "NavigationDuplicated", name: "NavigationDuplicated"}]
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  let ret = originalPush.call(this, location);
  if (ret) {
    return ret.catch((err) => err);
  }
};
const originalReplace = Router.prototype.replace;
Router.prototype.replace = function replace(location) {
  let ret = originalReplace.call(this, location);
  if (ret) {
    return ret.catch((err) => err);
  }
};

Vue.use(Router);

/*
 * mete自定义属性：
 *   auth：是否需要登录认证
 *   keepAlive：是否缓存页面
 *   如果页面需要顶部的病人信息，必须定义为patientLayout下的子路由
 */
export default new Router({
  mode: "history",
  routes: [
    {
      path: "/",
      redirect: "/login",
    },
    {
      path: "/componentDemo",
      name: "componentDemo",
      component: pages.componentDemo,
    },
    {
      path: "/login",
      name: "login",
      component: autoPages.login,
    },
    {
      path: "/caAuthorize",
      name: "caAuthorize",
      component: pages.caAuthorize,
      meta: {
        auth: true,
      },
    },
    {
      path: "/externalTransfer",
      name: "externalTransfer",
      component: pages.externalTransfer,
    },
    {
      path: "/help",
      name: "help",
      component: pages.help,
    },
    {
      path: "/transferManagement",
      name: "transferManagement",
      component: pages.transferManagement,
    },
    {
      path: "/main",
      name: "main",
      component: autoPages.main,
      meta: {
        auth: true,
        isParent: true,
      },
      children: [
        //病人清单
        {
          path: "/patientList",
          name: "patientList",
          component: autoPages.patientList,
          meta: {
            auth: true,
            isParent: true,
            syncFlag: true,
          },
        },
        {
          path: "/medicineScheduleRate",
          name: "medicineScheduleRate",
          component: autoPages.medicineScheduleRate,
        },
        {
          path: "/transferNursingManagement",
          name: "transferNursingManagement",
          component: pages.transferNursingManagement,
        },
        {
          path: "/nurseConsultQuery",
          name: "nurseConsultQuery",
          component: autoPages.nurseConsultQuery,
          meta: {
            auth: true,
          },
        },
        {
          path: "/patientLayout",
          name: "patientLayout",
          component: autoPages.patientLayout,
          meta: {
            auth: true,
            // keepAlive: true,
            isParent: true,
          },
          children: [
            // 插入d单病人执行的路由
            singlePatientBatchExecution,
            {
              path: "/assess",
              name: "assess",
              component: autoPages.assess,
              meta: {
                auth: true,
              },
            },
            {
              path: "/assessDetail",
              name: "assessDetail",
              component: autoPages.assessDetail,
              meta: {
                auth: true,
                parentPath: "/assess",
              },
            },
            {
              path: "/nursingPlan",
              name: "nursingPlan",
              component: pages.nursingPlan,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/wound",
              name: "wound",
              component: autoPages.wound,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //转运交接
            {
              path: "/transferHandover",
              name: "transferHandover",
              component: autoPages.transferHandover,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //出院小结
            {
              path: "/dischargeHandover",
              name: "dischargeHandover",
              component: autoPages.dischargeHandover,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //手术交接
            {
              path: "/operationHandover",
              name: "operationHandover",
              component: autoPages.operationHandover,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //病人接班
            {
              path: "/patientHandon",
              name: "patientHandon",
              component: autoPages.patientHandon,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //班别班内交班编辑
            {
              path: "/shiftHandover",
              name: "shiftHandover",
              component: autoPages.shiftHandover,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //造口
            {
              path: "/patientStomaRecord",
              name: "patientStomaRecord",
              component: autoPages.patientStomaRecord,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },

            {
              path: "/bloodTransfusionRecord",
              name: "bloodTransfusionRecord",
              component: pages.bloodTransfusionRecord,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/patientThrombolysis",
              name: "patientThrombolysis",
              component: pages.patientThrombolysis,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/babyFeedingRecord",
              name: "babyFeedingRecord",
              component: pages.patientBabyFeeding,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //饮食记录
            {
              path: "/patientDietIntake",
              name: "patientDietIntake",
              component: pages.patientDietIntake,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //谵妄评估
            {
              path: "/patientDelirium",
              name: "patientDelirium",
              component: pages.patientDelirium,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //镇静评估
            {
              path: "/patientSedation",
              name: "patientSedation",
              component: autoPages.patientSedation,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //无呕专项
            {
              path: "/patientCINV",
              name: "patientCINV",
              component: autoPages.patientCINV,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/tube",
              name: "tube",
              component: pages.tube,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/peripheralCirculation",
              name: "peripheralCirculation",
              component: autoPages.peripheralCirculation,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/patientDelivery",
              name: "patientDelivery",
              component: autoPages.patientDelivery,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/patientDeliveryRecord",
              name: "patientDeliveryRecord",
              component: autoPages.patientDeliveryRecord,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //神经血管评估
            {
              path: "/neurovascularAssess",
              name: "neurovascularAssess",
              component: pages.patientNeurovascular,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            //CRRT
            {
              path: "/cRRTRecord",
              name: "cRRTRecord",
              component: autoPages.cRRTRecord,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/restraint",
              name: "restraint",
              component: autoPages.restraint,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/arteriovenousFistula",
              name: "arteriovenousFistula",
              component: pages.arteriovenousFistula,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },

            {
              path: "/patientProfile",
              name: "patientProfile",
              component: autoPages.patientHomePage,
              meta: {
                auth: true,
              },
            },
            {
              path: "/handoverSBAR",
              name: "handoverSBAR",
              component: pages.handoverSBAR,
              meta: {
                auth: true,
              },
            },
            {
              path: "/handoverPerioperative",
              name: "handoverPerioperative",
              component: pages.handoverPerioperative,
              meta: {
                auth: true,
              },
            },
            {
              path: "/glucose",
              name: "glucose",
              component: pages.glucose,
              meta: {
                auth: true,
              },
            },
            {
              path: "/glucoseChart",
              name: "glucoseChart",
              component: pages.glucoseChart,
              meta: {
                auth: true,
              },
            },
            //医嘱审核
            {
              path: "/orderCheck",
              name: "orderCheck",
              component: pages.orderCheck,
              meta: {
                auth: true,
                syncFlag: true,
              },
            },
            //医嘱执行
            {
              path: "/patientMedicineSchedule",
              name: "patientMedicineSchedule",
              component: pages.patientMedicineSchedule,
              meta: {
                auth: true,
              },
            },
            //医嘱查询
            {
              path: "/patientOrders",
              name: "patientOrders",
              component: pages.patientOrders,
              meta: {
                auth: true,
              },
            },
            //生命体征
            {
              path: "/vitalSign",
              name: "vitalSign",
              component: pages.vitalSign,
              // 妇幼his需要直接跳转，去掉Token验证
              // meta: {
              //   auth: true
              // }
            },
            //观察措施
            {
              path: "/patientObserve",
              name: "patientObserve",
              component: pages.patientObserve,
              meta: {
                auth: true,
                keepAlive: true,
                refreshFlag: false,
              },
            },
            //观察措施
            {
              path: "/bedsideObservation",
              name: "bedsideObservation",
              component: pages.bedsideObservation,
              meta: {
                auth: true,
                parentPath: "/patientObserve",
              },
            },
            //观察措施
            {
              path: "/observeEvalutaion",
              name: "observeEvalutaion",
              component: pages.observeEvalutaion,
              meta: {
                auth: true,
                parentPath: "/patientObserve",
              },
            },
            {
              path: "/bloodTransfusionRecord",
              name: "patientTransfusion",
              component: pages.patientTransfusion,
              meta: {
                auth: true,
              },
            },
            {
              path: "/patientPain",
              name: "patientPain",
              component: autoPages.patientPain,
              meta: {
                auth: true,
              },
            },
            {
              path: "/rescueRecord",
              name: "rescueRecord",
              component: autoPages.rescueRecord,
              meta: {
                auth: true,
              },
            },
            {
              path: "/rescueRecordDetail",
              name: "rescueRecordDetail",
              component: pages.rescueRecordDetail,
              meta: {
                auth: true,
                parentPath: "/rescueRecord",
              },
            },
            {
              path: "/recordRiskRating",
              name: "recordRiskRating",
              component: pages.recordRiskRating,
              meta: {
                auth: true,
              },
            },
            {
              path: "/patientConsult",
              name: "patientConsult",
              component: pages.patientConsult,
              meta: {
                auth: true,
              },
            },
            {
              path: "/inHospital",
              name: "inHospital",
              component: pages.inHospital,
              meta: {
                auth: true,
              },
            },
            {
              path: "/patientEvent",
              name: "patientEvent",
              component: pages.patientEvent,
              meta: {
                auth: true,
              },
            },
            {
              path: "/patientPerioperative",
              name: "patientPerioperative",
              component: pages.patientPerioperative,
              meta: {
                auth: true,
              },
            },
            //批量评估引流液
            {
              path: "/batchRecordDrainage",
              name: "batchRecordDrainage",
              component: pages.batchRecordDrainage,
              meta: {
                auth: true,
              },
            },
            // 过敏药物
            {
              path: "/drugAllergy",
              name: "drugAllergy",
              component: pages.drugAllergy,
              meta: {
                auth: true,
              },
            },
            //重症设备绑定
            {
              path: "/equipmentBinding",
              name: "equipmentBinding",
              component: pages.equipmentBinding,
              meta: {
                auth: true,
              },
            },
            {
              path: "/ioRecordMaintain",
              name: "ioRecordMaintain",
              component: pages.ioRecordMaintenance,
              meta: {
                auth: true,
                parentPath: "/io",
              },
            },
            {
              path: "/io",
              name: "io",
              component: pages.io,
              meta: {
                auth: true,
                isParent: true,
              },
              children: [
                {
                  path: "/ioRecord",
                  name: "ioRecord",
                  component: pages.ioRecord,
                  meta: {
                    auth: true,
                    parentPath: "/io",
                  },
                },
                {
                  path: "/ioRecordMaintenance",
                  name: "ioRecordMaintenance",
                  component: pages.ioRecordMaintenance,
                  meta: {
                    auth: true,
                    parentPath: "/io",
                  },
                },
                {
                  path: "/drainageStatistics",
                  name: "drainageStatistics",
                  component: pages.drainageStatistics,
                  meta: {
                    auth: true,
                    parentPath: "/io",
                  },
                },
                {
                  path: "/ioStatisticalChart",
                  name: "ioStatisticalChart",
                  component: pages.ioStatisticalChart,
                  meta: {
                    auth: true,
                    parentPath: "/io",
                  },
                },
                {
                  path: "/ioBalanceStatistics",
                  name: "ioBalanceStatistics",
                  component: pages.ioBalanceStatistics,
                  meta: {
                    auth: true,
                    parentPath: "/io",
                  },
                },
                {
                  path: "/ioDrainageChart",
                  name: "ioDrainageChart",
                  component: pages.ioDrainageChart,
                  meta: {
                    auth: true,
                    parentPath: "/io",
                  },
                },
              ],
            },

            {
              path: "/handover",
              name: "handover",
              component: pages.handover,
              meta: {
                auth: true,
              },
            },
            // 病人护理时间轴
            {
              path: "/patientNursingTimeLine",
              name: "patientNursingTimeLine",
              component: pages.patientNursingTimeLine,
              meta: {
                auth: true,
              },
            },

            //泵入药物
            {
              path: "/pumping",
              name: "pumping",
              component: pages.pumping,
              meta: {
                auth: true,
              },
            },
            //同意书
            {
              path: "/agreement",
              name: "agreement",
              component: pages.agreement,
              meta: {
                auth: true,
              },
            },
            {
              path: "/tprChart",
              name: "tprChart",
              component: autoPages.tprChart,
              meta: {
                auth: true,
              },
            },
            {
              path: "/testExamine",
              name: "testExamine",
              component: pages.testExamine,
              meta: {
                auth: true,
              },
            },
            {
              path: "/handoverViewPeriOP",
              name: "handoverViewPeriOP",
              component: pages.operationRecord,
              meta: {
                auth: false,
              },
            },
            {
              path: "/patientCosts",
              name: "patientCosts",
              component: pages.patientCosts,
              meta: {
                auth: true,
              },
            },
            {
              path: "/skinTest",
              name: "skinTest",
              component: pages.skinTest,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/allergyAndSkinTest",
              name: "allergyAndSkinTest",
              component: pages.allergyAndSkinTest,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            {
              path: "/formRecord",
              name: "formRecord",
              component: pages.formRecord,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
            // 皮瓣护理
            {
              path: "/flapCare",
              name: "flapCare",
              component: autoPages.flap,
              meta: {
                auth: true,
                keepAlive: false,
              },
            },
          ],
        },
        {
          path: "/nurseHandover",
          name: "nurseHandover",
          component: pages.nurseHandover,
          meta: {
            auth: true,
          },
        },
        {
          path: "/dailyLog",
          name: "dailyLog",
          component: pages.dailyLog,
          meta: {
            auth: true,
          },
        },
        {
          path: "/dailyHandover",
          name: "dailyHandover",
          component: autoPages.dailyHandover,
          meta: {
            auth: true,
          },
        },
        {
          path: "/consultExhibition",
          name: "consultExhibition",
          component: pages.consultExhibition,
          meta: {
            auth: true,
          },
        },
        {
          path: "/replyConsult",
          name: "replyConsult",
          component: pages.replyConsult,
          meta: {
            auth: true,
          },
        },
        {
          path: "/consultAssign",
          name: "consultAssign",
          component: pages.consultAssign,
          meta: {
            auth: true,
          },
        },
        {
          path: "/questionAndAnaswer",
          name: "questionAndAnaswer",
          component: pages.questionAndAnaswer,
          meta: {
            auth: true,
          },
        },
        {
          path: "/drugListConvertedVolumeMaintain",
          name: "drugListConvertedVolumeMaintain",
          component: pages.drugListConvertedVolumeMaintain,
          meta: {
            auth: true,
          },
        },
        {
          path: "/assessTemplate",
          name: "assessTemplate",
          component: pages.assessTemplate,
          meta: {
            auth: true,
          },
        },
        {
          path: "/documentChange",
          name: "documentChange",
          component: pages.documentChange,
          meta: {
            auth: true,
          },
        },
        //简拼字典表维护页面
        {
          path: "/pinyinListMaintain",
          name: "pinyinListMaintain",
          component: pages.pinyinListMaintain,
          meta: {
            auth: true,
          },
        },
        //appConfigSetting典表维护页面
        {
          path: "/appConfigSetting",
          name: "appConfigSetting",
          component: pages.appConfigSetting,
          meta: {
            auth: true,
          },
        },
        //楼层位置维护主画面
        {
          path: "/hospitalBuilding",
          name: "hospitalBuilding",
          component: pages.hospitalBuilding,
          meta: {
            auth: true,
          },
        },
        //楼栋位置维护页面
        {
          path: "/building",
          name: "building",
          component: pages.building,
          meta: {
            auth: true,
          },
        },
        //楼层维护页面
        {
          path: "/floor",
          name: "floor",
          component: pages.floor,
          meta: {
            auth: true,
          },
        },
        //楼层位置维护页面
        {
          path: "/location",
          name: "location",
          component: pages.location,
          meta: {
            auth: true,
          },
        },
        //房间维护页面
        {
          path: "/room",
          name: "room",
          component: pages.room,
          meta: {
            auth: true,
          },
        },
        {
          path: "/systemVersionMaintain",
          name: "systemVersionMaintain",
          component: pages.systemVersionMaintain,
          meta: {
            auth: true,
          },
        },
        {
          path: "/schedule",
          name: "schedule",
          component: pages.schedule,
          meta: {
            auth: true,
          },
        },
        {
          path: "/scheduleByBatch",
          name: "scheduleByBatch",
          component: pages.scheduleByBatch,
          meta: {
            auth: true,
            keepAlive: true,
            refreshFlag: false,
          },
        },
        {
          path: "/multipleHandoffByNurse",
          name: "multipleHandoffByNurse",
          component: pages.multipleHandoffByNurse,
          meta: {
            auth: true,
          },
        },
        {
          path: "/multipleHandonByNurse",
          name: "multipleHandonByNurse",
          component: pages.multipleHandonByNurse,
          meta: {
            auth: true,
          },
        },
        //批量交班
        {
          path: "/multipleShiftHandoff",
          name: "multipleShiftHandoff",
          component: autoPages.multipleShiftHandoff,
          meta: {
            auth: true,
            keepAlive: false,
          },
        },
        //批量接班
        {
          path: "/multipleShiftHandon",
          name: "multipleShiftHandon",
          component: autoPages.multipleShiftHandon,
          meta: {
            auth: true,
            keepAlive: false,
          },
        },
        {
          path: "/handoverQueryByStation",
          name: "handoverQueryByStation",
          component: pages.handoverQueryByStation,
          meta: {
            auth: true,
          },
        },
        {
          path: "/stationHandoverReport",
          name: "stationHandoverReport",
          component: autoPages.stationHandoverReport,
          meta: {
            auth: true,
          },
        },
        {
          path: "/stationMedicineSchedule",
          name: "stationMedicineSchedule",
          component: pages.stationMedicineSchedule,
          meta: {
            auth: true,
          },
        },
        {
          path: "/patientsMedicationClosedLoopManagement",
          name: "patientsMedicationClosedLoopManagement",
          component: autoPages.medicationClosedLoop,
          meta: {
            auth: true,
          },
        },
        {
          path: "/morningShiftReport",
          name: "morningShiftReport",
          component: autoPages.morningShiftReport,
          meta: {
            auth: true,
          },
        },
        {
          path: "/morningShiftHandoverReport",
          name: "morningShiftHandoverReport",
          component: autoPages.morningShiftHandoverReport,
          meta: {
            auth: true,
          },
        },
        {
          path: "/patientTestDemo",
          name: "patientTestDemo",
          component: pages.patientTestDemo,
        },
        //批量监测
        {
          path: "/batchMonitoring",
          name: "batchMonitoring",
          component: autoPages.monitoringScheduler,
          meta: {
            auth: false,
          },
        },
        //批量血糖
        {
          path: "/batchRecordGlucoseKetone",
          name: "batchRecordGlucoseKetone",
          component: autoPages.batchRecordGlucoseKetone,
          meta: {
            auth: true,
          },
        },
        //生命体征
        {
          path: "/nursingEvaluation",
          name: "nursingEvaluation",
          component: pages.nursingEvaluation,
          meta: {
            auth: true,
          },
        },

        {
          path: "/transfer",
          name: "transfer",
          component: pages.transfer,
          meta: {
            auth: true,
          },
        },
        //措施汇总
        {
          path: "/measuresTheSummary",
          name: "measuresTheSummary",
          component: pages.measuresTheSummary,
          meta: {
            auth: true,
          },
        },

        {
          path: "/discharge",
          name: "discharge",
          component: pages.discharge,
          meta: {
            auth: true,
            keepAlive: true,
          },
        },
        {
          path: "/dischargeSummary",
          name: "dischargeSummary",
          component: pages.dischargeSummary,
          meta: {
            auth: true,
          },
        },

        //维护评估对应护理问题权重
        {
          path: "/assessToProblem",
          name: "assessToProblem",
          component: pages.assessToProblem,
          meta: {
            auth: true,
          },
        },

        //批量执行措施
        {
          path: "/schedulesExecution",
          name: "schedulesExecution",
          component: pages.schedulesExecution,
          meta: {
            auth: true,
            parentPath: "/scheduleByBatch",
          },
        },
        //病案查询
        {
          path: "/document",
          name: "document",
          component: pages.document,
          meta: {
            auth: false,
          },
        },
        //引流液体多笔输入
        {
          path: "/outputMultiPerform",
          name: "outputMultiPerform",
          component: pages.outputMultiPerform,
          meta: {
            auth: true,
          },
        },
        //缓存维护
        {
          path: "/maintain/cacheList",
          name: "cacheList",
          component: pages.cacheList,
          meta: {
            auth: true,
          },
        },
        //岗床配置
        {
          path: "/bedToJob",
          name: "bedToJob",
          component: pages.bedToJob,
          meta: {
            auth: true,
          },
        }, //岗位配置
        {
          path: "/departmentJob",
          name: "departmentJob",
          component: pages.departmentJob,
          meta: {
            auth: true,
          },
        }, //人员岗位配置
        {
          path: "/employeeToJob",
          name: "employeeToJob",
          component: pages.employeeToJob,
          meta: {
            auth: true,
          },
        },
        //岗位分组
        {
          path: "/careGroupList",
          name: "careGroupList",
          component: pages.careGroupList,
          meta: {
            auth: true,
          },
        },
        {
          path: "/batchRelieveOneself",
          name: "batchRelieveOneself",
          component: pages.batchRelieveOneself,
          meta: {
            auth: true,
          },
        },
        {
          path: "/attendance",
          name: "attendance",
          component: pages.attendance,
          meta: {
            auth: true,
          },
        },
        {
          path: "/assessRecordSupplement",
          name: "assessRecordSupplement",
          component: pages.assessRecordSupplement,
          meta: {
            auth: true,
          },
        },
        {
          path: "/assessContent",
          name: "assessContent",
          component: pages.assessContent,
          meta: {
            auth: true,
            parentPath: "/assessRecordSupplement",
          },
        },
        {
          path: "/dataTableEditList",
          name: "dataTableEditList",
          component: pages.dataTableEditList,
        },
        {
          path: "/nursingCodeMaintain",
          name: "nursingCodeMaintain",
          component: pages.nursingCodeMaintain,
          meta: {
            auth: true,
          },
        },
        {
          path: "/interventionToRecordSettingMaintain",
          name: "interventionToRecordSettingMaintain",
          component: autoPages.interventionToRecordSettingMaintain,
          meta: {
            auth: true,
          },
        },
        {
          path: "/patientIconMaintain",
          name: "patientIconMaintain",
          component: autoPages.patientIconMaintain,
          meta: {
            auth: true,
          },
        },
        {
          path: "/statisticDischargeTube",
          name: "statisticDischargeTube",
          component: autoPages.statisticDischargeTube,
          meta: {
            auth: true,
          },
        },
        //措施配置查询
        {
          path: "/queryNursingIntervention",
          name: "queryNursingIntervention",
          component: autoPages.queryNursingIntervention,
          meta: {
            auth: true,
          },
        },
        //触发措施配置维护
        {
          path: "/interventionTriggerIntervention",
          name: "interventionTriggerIntervention",
          component: autoPages.interventionTriggerIntervention,
          meta: {
            auth: true,
          },
        },
        //检验标签打印
        {
          path: "/testLabelPrint",
          name: "testLabelPrint",
          component: pages.testLabelPrint,
          meta: {
            auth: true,
          },
        },
        {
          path: "/examinationRequisitionLabelPrint",
          name: "examinationRequisitionLabelPrint",
          component: pages.examinationRequisitionLabelPrint,
          meta: {
            auth: true,
          },
        },
        {
          path: "/specimenCollection",
          name: "specimenCollection",
          component: pages.specimenCollection,
          meta: {
            auth: true,
          },
        },
        {
          path: "/specimenTransfer",
          name: "specimenTransfer",
          component: pages.specimenTransfer,
          meta: {
            auth: true,
          },
        },
        //检验闭环日志查询
        {
          path: "/queryClosingControlLog",
          name: "queryClosingControlLog",
          component: pages.queryClosingControlLog,
          meta: {
            auth: true,
          },
        },
        {
          path: "/continuousCare",
          name: "continuousCare",
          component: pages.continuousCare,
          meta: {
            auth: true,
          },
        },
        {
          path: "/patinetStomaDetail",
          name: "patinetStomaDetail",
          component: autoPages.patinetStomaDetail,
          meta: {
            auth: true,
            keepAlive: false,
            parentPath: "/patientStomaRecord",
          },
        },
        {
          path: "/statistics",
          name: "statistics",
          component: pages.statistics,
          meta: {
            auth: true,
          },
        },
        {
          path: "/statisticsV3",
          name: "statisticsV3",
          component: pages.statisticsV3,
          meta: {
            auth: true,
          },
        },
        {
          path: "/platonicStatistics",
          name: "platonicStatistics",
          component: pages.platonicStatistics,
          meta: {
            auth: true,
          },
        },
        {
          path: "/fishboneStatistics",
          name: "fishboneStatistics",
          component: pages.fishboneStatistics,
          meta: {
            auth: true,
          },
        },
        {
          path: "/sensitiveIndicator",
          name: "sensitiveIndicator",
          component: pages.sensitiveIndicator,
          meta: {
            auth: true,
          },
        },
        {
          path: "/workLoad",
          name: "workLoad",
          component: pages.workLoad,
          meta: {
            auth: true,
          },
        },
        // {
        //   path: "/dutyDoctorsMaintain",
        //   name: "dutyDoctorsMaintain",
        //   component: pages.dutyDoctorsMaintain,
        //   meta: {
        //     auth: false,
        //   },
        // },
        // 串nursing或html的中转页面=======================================================
        //三级质控维护页面
        {
          path: "/qcContentMaintain",
          name: "qcContentMaintain",
          component: pages.qcContentMaintain,
          meta: {
            auth: true,
          },
        },
        //三级质控主页面
        {
          path: "/qcPerformCheck",
          name: "qcPerformCheck",
          component: pages.qcPerformCheck,
          meta: {
            auth: true,
          },
        },
        //三级质控统计页面
        {
          path: "/qcPerformStatistics",
          name: "qcPerformStatistics",
          component: pages.qcPerformStatistics,
          meta: {
            auth: true,
          },
        },
        {
          path: "/datasetUserEntry",
          name: "datasetUserEntry",
          component: pages.datasetUserEntry,
          meta: {
            auth: true,
          },
        },
        {
          path: "/indicatorFactorNurse",
          name: "indicatorFactorNurse",
          component: pages.indicatorFactorNurse,
          meta: {
            auth: true,
          },
        },
        {
          path: "/eventTimeData",
          name: "eventTimeData",
          component: pages.eventTimeData,
          meta: {
            auth: true,
          },
        },
        {
          path: "/stationUseTube",
          name: "stationUseTube",
          component: pages.stationUseTube,
          meta: {
            auth: true,
          },
        },
        {
          path: "/tubeToBodyPart",
          name: "tubeToBodyPart",
          component: pages.tubeToBodyPart,
          meta: {
            auth: true,
          },
        },
        {
          path: "/patientEMR",
          name: "patientEMR",
          component: pages.patientEMR,
          meta: {
            auth: true,
          },
        },
        {
          path: "/orderLabel",
          name: "orderLabel",
          component: pages.orderLabel,
          meta: {
            auth: true,
          },
        },
        {
          path: "/dutyDoctors",
          name: "dutyDoctors",
          component: pages.dutyDoctors,
          meta: {
            auth: true,
          },
        },
        {
          path: "/roleAuthority",
          name: "roleAuthority",
          component: pages.roleAuthority,
          meta: {
            auth: true,
          },
        },
        {
          path: "/userRole",
          name: "userRole",
          component: pages.userRole,
          meta: {
            auth: true,
          },
        },
        {
          path: "/suggestProblem",
          name: "suggestProblem",
          component: pages.suggestProblem,
          meta: {
            auth: true,
          },
        },
        {
          path: "/virtualStation",
          name: "virtualStation",
          component: pages.virtualStation,
          meta: {
            auth: true,
          },
        },
        {
          path: "/riskScreenRecordList",
          name: "riskScreenRecordList",
          component: pages.riskScreenRecordList,
          meta: {
            auth: true,
          },
        },
        {
          path: "/consultGoalToEmployee",
          name: "consultGoalToEmployee",
          component: pages.consultGoalToEmployee,
          meta: {
            auth: true,
          },
        },
        {
          path: "/medicineToRecord",
          name: "medicineToRecord",
          component: pages.medicineToRecord,
          meta: {
            auth: true,
          },
        },
        {
          path: "/annuaPlanMaintain",
          name: "annuaPlanMaintain",
          component: pages.annuaPlanMaintain,
        },
        {
          path: "/verifyRecords",
          name: "verifyRecords",
          component: pages.verifyRecords,
          meta: {
            auth: true,
          },
        },
        {
          path: "/qcFaultStatistics",
          name: "qcFaultStatistics",
          component: pages.qcFaultStatistics,
          meta: {
            auth: true,
          },
        },
        {
          path: "/operationLog",
          name: "operationLog",
          component: pages.operationLog,
          meta: {
            auth: true,
            keepAlive: false,
          },
        },
        //出院病人评估和专项护理记录查看
        {
          path: "/assessRecordLook",
          name: "/assessRecordLook",
          component: pages.assessRecordLook,
          meta: {
            auth: true,
            keepAlive: false,
            readOnly: true,
          },
          children: [
            {
              path: "/woundLook",
              name: "woundLook",
              component: autoPages.wound,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/assessLook",
              name: "assessLook",
              component: autoPages.assess,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/ioLook",
              name: "ioLook",
              component: pages.ioLook,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/tubeLook",
              name: "tubeLook",
              component: pages.tubeLook,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/glucoseLook",
              name: "glucoseLook",
              component: pages.glucoseLook,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/rescueRecordLook",
              name: "rescueRecordLook",
              component: pages.rescueRecordLook,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/restraintLook",
              name: "restraintLook",
              component: autoPages.restraintLook,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/patientPainLook",
              name: "patientPainLook",
              component: autoPages.patientPainLook,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/peripheralCirculationLook",
              name: "peripheralCirculationLook",
              component: pages.peripheralCirculationLook,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/patientStomaRecordLook",
              name: "patientStomaRecordLook",
              component: autoPages.patientStomaRecordLook,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/pumpingLook",
              name: "pumpingLook",
              component: pages.pumping,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/patientThrombolysisLook",
              name: "patientThrombolysisLook",
              component: pages.patientThrombolysis,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/bloodTransfusionRecordLook",
              name: "bloodTransfusionRecordLook",
              component: pages.patientTransfusion,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/patientDietIntakeLook",
              name: "patientDietIntakeLook",
              component: pages.patientDietIntake,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/patientDeliriumLook",
              name: "patientDeliriumLook",
              component: pages.patientDelirium,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/deliveryRecordLook",
              name: "deliveryRecordLook",
              component: pages.deliveryRecord,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/babyFeedingRecordLook",
              name: "babyFeedingRecordLook",
              component: pages.patientBabyFeeding,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/recordRiskRatingLook",
              name: "recordRiskRatingLook",
              component: pages.recordRiskRating,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/nursingRecordLook",
              name: "nursingRecordLook",
              component: pages.nursingRecord,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/handoverLook",
              name: "handoverLook",
              component: pages.handover,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/handoverSBARLook",
              name: "handoverSBARLook",
              component: pages.handoverSBAR,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/cRRTRecordLook",
              name: "cRRTRecordLook",
              component: autoPages.cRRTRecord,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/patientSedationLook",
              name: "patientSedationLook",
              component: autoPages.patientSedation,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/patientCINVLook",
              name: "patientCINVLook",
              component: autoPages.patientCINV,
              meta: {
                auth: true,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/assessDetailLook",
              name: "assessDetailLook",
              component: autoPages.assessDetail,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
            {
              path: "/patientDeliveryRecordLook",
              name: "patientDeliveryRecordLook",
              component: autoPages.patientDeliveryRecord,
              meta: {
                auth: true,
                keepAlive: false,
                readOnly: true,
                parentPath: "/assessRecordLook",
              },
            },
          ],
        },

        {
          path: "/stockInventory",
          name: "stockInventory",
          component: pages.stockInventory,
          meta: {
            auth: true,
          },
        },
        {
          path: "/documentPigeonhole",
          name: "documentPigeonhole",
          component: pages.documentPigeonhole,
          meta: {
            auth: true,
          },
        },
        {
          path: "/summaryIntakeOutput",
          name: "summaryIntakeOutput",
          component: pages.summaryIntakeOutput,
          meta: {
            auth: true,
          },
        },
        {
          path: "/observeTemplate",
          name: "observeTemplate",
          component: pages.observeTemplate,
          meta: {
            auth: true,
          },
        },
        {
          path: "/settingDescription",
          name: "settingDescription",
          component: pages.settingDescription,
          meta: {
            auth: true,
          },
        },
        {
          path: "/adverseEvent",
          name: "adverseEvent",
          component: pages.adverseEvent,
          meta: {
            auth: true,
          },
        },
        {
          path: "/stationSwitch",
          name: "stationSwitch",
          component: pages.stationSwitch,
        },
        {
          path: "/transfusionRecordsCheckForm",
          name: "transfusionRecordsCheckForm",
          component: pages.transfusionRecordsCheckForm,
        },
        {
          path: "/AuthorityManagement",
          name: "export-roleAuthority",
          component: pages.roleAuthority,
        },
        {
          path: "/InterventionFileUpload",
          name: "InterventionFileUpload",
          component: pages.interventionFileUpload,
        },
        {
          path: "/addBed",
          name: "addBed",
          component: pages.addBed,
        },
        {
          path: "/systemOperationStatistics",
          name: "admissionStatistics",
          component: pages.AdmissionStatistics,
        },
        {
          path: "/physicianRelatedBed",
          name: "physicianRelatedBed",
          component: pages.physicianRelatedBed,
        },
        {
          path: "/physicianGroup",
          name: "physicianGroup",
          component: pages.physicianGroup,
        },
        {
          path: "/hospitalDutyMaintain",
          name: "hospitalDutyMaintain",
          component: pages.hospitalDutyMaintain,
        },
        {
          path: "/hospitalDutyToStationMaintain",
          name: "hospitalDutyToStationMaintain",
          component: pages.hospitalDutyToStationMaintain,
        },
        {
          path: "/doctorDutyBatchMaintain",
          name: "doctorDutyBatchMaintain",
          component: pages.doctorDutyBatchMaintain,
        },
        {
          path: "/customBulletinMaintain",
          name: "customBulletinMaintain",
          component: pages.customBulletinMaintain,
        },
        {
          path: "/treatmentMaintain",
          name: "treatmentMaintain",
          component: pages.treatmentMaintain,
        },
        {
          path: "/orderOverviewMaintain",
          name: "orderOverviewMaintain",
          component: pages.orderOverviewMaintain,
        },
        {
          path: "/jumpOtherSystemCommon",
          name: "jumpOtherSystemCommon",
          component: pages.jumpOtherSystemCommon,
          meta: {
            auth: true,
          },
        },
        {
          path: "/jumpCaseSystem",
          name: "jumpCaseSystem",
          component: pages.jumpCaseSystem,
          meta: {
            auth: true,
          },
        },
        {
          path: "/filePreview",
          name: "filePreview",
          component: pages.filePreview,
        },
        //病历审核
        {
          path: "/recordsverifierSign",
          name: "recordReview",
          component: autoPages.recordReview,
          meta: {
            auth: true,
            keepAlive: false,
          },
        },
        //操作日志分析
        {
          path: "/operationLogSum",
          name: "operationLogAnalytics",
          component: autoPages.operationLogAnalytics,
          meta: {
            auth: true,
          },
        },
        {
          path: "/stationToJobTip",
          name: "stationToJobTip",
          component: autoPages.stationToJobTip,
          meta: {
            auth: true,
          },
        },
        //批量评估引流液
        {
          path: "/multyPatientRecordDrainage",
          name: "multyPatientRecordDrainage",
          component: pages.multyPatientRecordDrainage,
          meta: {
            auth: true,
          },
        },
        //护理级别巡视执行情况
        {
          path: "/nursingLevelCarePerformQuery",
          name: "nursingLevelCarePerformQuery",
          component: autoPages.nursingLevelCarePerformQuery,
          meta: {
            auth: false,
          },
        },
        //危重患者访视页面
        {
          path: "/visitCriticalPatient",
          name: "visitCriticalPatient",
          component: autoPages.visitCriticalPatient,
          meta: {
            auth: false,
          },
        },
        //记录补录
        {
          path: "/recordSupplement",
          name: "recordSupplement",
          component: autoPages.recordSupplement,
          meta: {
            auth: true,
          },
        },
        //给药记录补录测试
        {
          path: "/giveMedicineRecordTest",
          name: "giveMedicineRecordTest",
          component: autoPages.giveMedicineRecordTest,
          meta: {
            auth: true,
          },
        },
        {
          path: "/bloodVerificationRecycle",
          name: "bloodVerificationRecycle",
          component: autoPages.bloodVerificationRecycle,
          meta: {
            auth: false,
          },
        },
      ],
    },
    /* 找不到路由跳转到登录页面 */
    {
      path: "/*",
      redirect: "/login",
    },
  ],
});
