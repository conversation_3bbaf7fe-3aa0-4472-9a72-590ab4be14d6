<!--
 * FilePath     : \src\pages\document\index.vue
 * Author       : 苏军志
 * Date         : 2020-09-06 16:44
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-23 16:36
 * Description  : 病案查询  其他页面直接跳转路径：http://localhost:8088/document?hospitalID=1&language=1&isDialog=true&caseNumber=10640653.1
 * CodeIterationRecord: 2799-作为护理人员，我需要跳转HIS病历关闭后，可以返回系统之前页面。 2022-07-24 杨欣欣
-->
<template>
  <div class="document" v-loading="loading" element-loading-text="加载中……">
    <el-container>
      <el-aside class="document-tree-wrap" v-if="showLeftPanel">
        <div class="document-input">
          <query-field-input
            ref="queryFieldInput"
            :queryField="queryField"
            @data="handleData"
            :defaultValue="defaultValue"
          />
        </div>
        <div class="document-information">
          <p class="information-label">姓名：</p>
          <p class="information name">{{ patientModel.name }}</p>
          <p class="information-label">性别：</p>
          <p class="information">{{ patientModel.sex }}</p>
          <p class="information-label">生日：</p>
          <p class="information">{{ patientModel.birthday }}</p>
        </div>
        <div class="emr-button-group">
          <el-button
            type="primary"
            class="refresh-emr-button"
            :style="{ width: 100 / buttonWidth + '%' }"
            @click="createDocument()"
          >
            {{ buttonText }}
          </el-button>
          <el-button
            v-if="hisEmrButtonFlag"
            type="primary"
            class="his-emr-button"
            :style="{ width: 100 / buttonWidth + '%' }"
            @click="getHisDocument()"
          >
            {{ hisEmrButton }}
          </el-button>
          <el-button
            v-if="holoEmrButtonFlag"
            type="primary"
            class="holo-emr-button"
            :style="{ width: 100 / buttonWidth + '%' }"
            @click="getHisDocument('GetHoloDocumentUrl')"
          >
            {{ holoEmrButton }}
          </el-button>
        </div>
        <el-tree
          node-key="id"
          icon-class="iconfont icon-minus"
          :data="datas"
          :default-expanded-keys="treeIDArr"
          :show-checkbox="isShowCheckbox"
          :props="defaultProps"
          :render-content="renderContent"
          :highlight-current="true"
          @node-click="handleNodeClick"
          ref="tree"
        ></el-tree>
      </el-aside>
      <i
        :class="['toggle', showLeftPanel ? 'show' : 'hide']"
        :title="`${showLeftPanel ? '隐藏' : '打开'}病历清单`"
        @click="showLeftPanel = !showLeftPanel"
      ></i>
      <el-main class="document-pdf-wrap">
        <el-tooltip v-if="imageButtonFlag" class="document-pdf-icon" content="伤口图片预览">
          <div @click="openImgPreview()" class="iconfont icon-img-preview"></div>
        </el-tooltip>
        <iframe
          v-if="isComponent && showHisEmrByComponent"
          ref="hisEmrByComponent"
          frameborder="0"
          width="100%"
          height="99%"
        />
        <div v-else :class="['document-pdf', { 'not-pc': !isPC }]">
          <iframe
            v-if="isPC"
            :src="urlPDF"
            type="application/x-google-chrome-pdf"
            width="100%"
            height="100%"
            frameborder="0"
            scrolling="auto"
          />
          <pdf v-else v-for="i in numPages" :key="i" :src="urlPDF" :page="i"></pdf>
        </div>
      </el-main>
    </el-container>
    <!--弹出his病历页面-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="HIS病历"
      :visible.sync="showHis"
      fullscreen
      custom-class="no-footer his-document"
    >
      <iframe v-if="showHis" ref="hisDialog"></iframe>
    </el-dialog>
    <!-- 伤口图片弹窗 -->
    <el-dialog :title="imgPreviewTitle" :visible.sync="imgPreviewFlag" fullscreen>
      <img-preview :imgPreviewData="imgPreviewData"></img-preview>
    </el-dialog>
  </div>
</template>

<script>
import { GetShowDataByChartNo, CreateNurseEMRMain, GetPatientDocumentByID, GetHisDocumentUrl } from "@/api/Document";
import { mapGetters } from "vuex";
import pdf from "vue-pdf";
import { GetOneSettingByTypeAndCode, GetClinicSettingByTypeCode } from "@/api/Setting";
import { GetDocumentAutoRefreshFlag } from "@/api/SettingDescription";
import imgPreview from "@/components/imgPreview";
import { GetImgPreviewData } from "@/api/WoundRecord";
import { GetInpatientDataViewByCaseNumber } from "@/api/Inpatient";
import queryFieldInput from "@/components/queryFieldInput";
export default {
  components: {
    pdf,
    imgPreview,
    queryFieldInput,
  },
  props: {
    isComponent: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      hisEmrButton: "HIS病历",
      urlPDF: undefined,
      loading: false,
      //床号
      bedNumber: "",
      //住院号
      caseNumberOrPatientID: undefined,
      chartNo: "",
      caseNumber: "",
      inpatientIDs: [],
      //病历清单
      datas: [],
      //病历树形结构
      defaultProps: {
        children: "children",
        label: "label",
      },
      //病人基本信息数据
      patientModel: { name: undefined, sex: undefined, birthday: undefined },
      clearPatientModel: { name: "", sex: "", birthday: "" },
      isShowCheckbox: false,
      numPages: undefined,
      isPC: true,
      //默认展开ID数组,
      treeIDArr: [],
      language: undefined,
      //重新生成按钮内容
      buttonText: "刷新病历",
      clickPDfFileClass: 0,
      clickFileID: "",
      //是否病历数组为空
      PDFFlag: false,
      createPdfButtonFlag: true,
      cloneInpatientIDs: [],
      level: 1,
      numberOfAdmissions: 1,
      hisEmrButtonFlag: false,
      showHis: false,
      imgPreviewFlag: false,
      imgPreviewData: {},
      imageButtonFlag: false,
      imgPreviewTitle: "",
      // 组件模式下 显示his病历
      showHisEmrByComponent: false,
      queryField: [],
      defaultValue: undefined,
      holoEmrButton: "360视图",
      holoEmrButtonFlag: false,
      showLeftPanel: true,
      patientData: undefined,
    };
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      currentPatient: "getCurrentPatient",
    }),
    buttonWidth() {
      let count = 1; // 默认有一个按钮
      if (this.hisEmrButtonFlag) count++;
      if (this.holoEmrButtonFlag) count++;
      return count;
    },
  },
  watch: {
    bedNumber(newValue) {
      if (newValue) {
        this.caseNumberOrPatientID = "";
      }
    },
    caseNumberOrPatientID(newValue) {
      if (newValue) {
        this.bedNumber = "";
      }
    },
  },
  async created() {
    this.isPC = this._common.isPC();
    this.isShowCheckbox = true;
    await this.getPatientQueryField();
    await this.getHisDocumentFlag();
    await this.getHoloDocumentFlag();
    await this.getHisEmrTitle();
    await this.getHoloEmrTitle();
    //病历跳转
    if (this.$route.query.caseNumber) {
      await this.getInpatientDataViewByCaseNumber(this.$route.query.caseNumber);
      let attribute = this.queryField[0].typeValue;
      let attributeValue = this.patientData[attribute.charAt(0).toLowerCase() + attribute.slice(1)];
      this.defaultValue = {
        attribute: attribute,
        attributeValue: attributeValue,
      };
      await this.getDocumentRefreshFlag();
      return;
    }
    //按钮进入
    if (this.currentPatient && this.currentPatient.localCaseNumber) {
      this.caseNumberOrPatientID = this.currentPatient.localCaseNumber;
      let settingTypeCode = this.queryField[0].typeValue;
      this.defaultValue = {
        attribute: this.queryField[0].typeValue,
        attributeValue: this.currentPatient[settingTypeCode.charAt(0).toLowerCase() + settingTypeCode.slice(1)],
      };
      await this.getDocumentRefreshFlag();
      return;
    }
  },
  methods: {
    renderContent(h, { node, data, store }) {
      return <span class={"el-tree-node__label " + (data.boldFlag ? "bold" : "normal")}>{node.label}</span>;
    },
    /**
     * description: 获取HIS病历
     * param {*}
     * return {*}
     */
    getHisDocument(buttonCode) {
      let params = {
        chartNo: this.chartNo,
        numberOfAdmissions: this.numberOfAdmissions,
        caseNumber: this.caseNumber,
        buttonCode: buttonCode,
      };
      GetHisDocumentUrl(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data?.url) {
            let url = "";
            if (result.data.type == "file") {
              // 跳转文件预览画面 传参数
              let fileList = [result.data.url];
              url = "/filePreview?isDialog=true&fileList=" + JSON.stringify(fileList);
            } else {
              // 是网址 直接跳转
              url = result.data.url;
            }
            if (result.data.target == "dialog") {
              this.showHis = true;
              this.$nextTick(() => {
                this.$refs.hisDialog.contentWindow.location.replace(url);
              });
            } else if (this.isComponent) {
              this.showHisEmrByComponent = true;
              this.$nextTick(() => {
                this.$refs.hisEmrByComponent.contentWindow.location.replace(url);
              });
            } else {
              let newWindow = window.open(url);
              newWindow.onload = () => {
                newWindow.document.title = "HIS病历";
              };
            }
          }
        }
      });
    },
    //加载患者信息
    async loadPatients(value = this.caseNumberOrPatientID) {
      this.buttonText = "刷新病历";
      this.clickPDfFileClass = 0;
      if (value.length == 0) {
        this._showTip("warning", "病人住院号不能为空!");
        return;
      }
      this.loading = true;
      let params = {
        chartNo: value,
      };
      await GetShowDataByChartNo(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          if (!result.data.length) {
            return;
          }
          this.getTreeIdData(result.data[0].children);
          this.datas = result.data;
          this.patientModel.name = this.datas[0].patientName;
          this.patientModel.sex = this.datas[0].sex;
          this.patientModel.birthday = this.datas[0].birthDate;
          //重置生成病历按钮状态
          this.createPdfButtonFlag = true;
        }
      });
    },
    //点击节点
    async handleNodeClick(data) {
      this.showHisEmrByComponent = false;
      this.level = data.level;
      if (data.level != 1) {
        var tempInpatientID = [];
        tempInpatientID.push(data.inpatientId);
        this.inpatientIDs = tempInpatientID;
      } else {
        this.inpatientIDs = this.cloneInpatientIDs;
      }
      if (data.children) {
        //#bugfix 当data.children=【】的时候,里边内容是null，直接return；因为getTreeList方法中使用了data.children[0]
        if (data.children instanceof Array && data.children.length <= 0) {
          return;
        }
        this.getTreeList(data);
        return;
      }
      if (data.isEnd) {
        this.loading = true;
        this.clickFileID = data.id;
        await this.getImgPreviewData(data);
        this.imgPreviewTitle = `${data.label}单-伤口预览-${this.patientModel.name}-${this.currentPatient.bedNumber}床`;
        await this.loadFile(data.emrFileListID).then((flag) => {});
        this.loading = false;
      }
    },
    async loadFile(id) {
      let params = {
        ID: id,
      };
      await GetPatientDocumentByID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.urlPDF = result.data.fileUrl;
          // 移动端显示
          if (!this.isPC) {
            this.getNumPages(this.urlPDF);
          }
        }
      });
    },
    getNumPages(url) {
      this.loading = true;
      let loadingTask = pdf.createLoadingTask(url);
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf._pdfInfo.numPages;
          this.loading = false;
        })
        .catch((err) => {
          this._showTip("warning", "pdf加载失败！");
        });
    },
    //获取默认展开的ID数据
    getTreeIdData(value) {
      if (!value) {
        return;
      }
      this.treeIDArr = [];
      value.forEach((item) => {
        item.children.forEach((itemOne) => {
          this.treeIDArr.push(itemOne.id);
        });
      });
    },
    //异动体温单
    async createDocument() {
      //检核fileClass是否为空
      if (this.clickPDfFileClass === "" || this.loading) {
        return;
      }
      this.loading = true;
      for (let index = 0; index < this.inpatientIDs.length; index++) {
        let inPatientId = this.inpatientIDs[index];
        let params = {
          inPatientId: inPatientId,
          fileClass: this.clickPDfFileClass,
          index: Math.random(),
        };
        await CreateNurseEMRMain(params).then((res) => {
          if (res != undefined && !res.code) {
            this._showTip("error", "生成病历失败");
          }
        });
      }
      await this.loadPatients(this.chartNo);
      this.loading = false;
    },
    //获取病历生成按钮数据
    getTreeList(value) {
      this.buttonText = "刷新" + value.label;
      if (value.level > 1) {
        this.buttonText += "病历";
      }
      this.clickPDfFileClass = value.children[0].fileClass;
      this.createPdfButtonFlag = false;
    },
    //检核病人是否有病历
    checkPDf() {
      this.PDFFlag = false;
      this.datas[0].children.map((item) => {
        item.children.map((itemOne) => {
          if (itemOne.children.length == 0) {
            this.PDFFlag = true;
            this.createPdfButtonFlag = false;
          }
        });
      });
      return this.PDFFlag;
    },
    /**
     * description: 获取HIS病历是否开启
     * param {*}
     * return {*}
     */
    async getHisDocumentFlag() {
      let params = {
        settingType: 163,
        settingCode: "GetHisDocument",
      };
      await GetOneSettingByTypeAndCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data.typeValue == "True") {
            this.hisEmrButtonFlag = true;
          }
        }
      });
    },
    /**
     * description: 获取360全息视图是否开启
     * param {*}
     * return {*}
     */
    async getHoloDocumentFlag() {
      let params = {
        settingType: 163,
        settingCode: "GetHoloDocument",
      };
      await GetOneSettingByTypeAndCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data.typeValue == "True") {
            this.holoEmrButtonFlag = true;
          }
        }
      });
    },
    /**
     * description: HIS病历按钮标题配置
     * param {*}
     * return {*}
     */
    async getHisEmrTitle() {
      let parameter = {
        settingType: 163,
        settingCode: "HISButtonTitle",
      };
      GetOneSettingByTypeAndCode(parameter).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.typeValue) {
            this.hisEmrButton = result.data.typeValue;
          }
        }
      });
    },
    /**
     * description: 360全息视图按钮标题配置
     * param {*}
     * return {*}
     */
    async getHoloEmrTitle() {
      let parameter = {
        settingType: 163,
        settingCode: "HoloButtonTitle",
      };
      GetOneSettingByTypeAndCode(parameter).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.typeValue) {
            this.holoEmrButton = result.data.typeValue;
          }
        }
      });
    },
    /**
     * description: 获取是否首次刷新去全部异动病历标记
     * param {*}
     * return {*}
     */
    async getDocumentRefreshFlag() {
      if (!this.inpatientIDs || this.inpatientIDs.length == 0) {
        return;
      }
      let params = {
        inpatientIDs: this.inpatientIDs,
      };
      let flag = false;
      await GetDocumentAutoRefreshFlag(params).then((result) => {
        if (this._common.isSuccess(result)) {
          flag = result.data;
        }
      });
      if (flag) {
        this.clickPDfFileClass = 0;
        await this.createDocument();
      }
    },
    /**
     * description: 获取图片预览组件数据
     * param {*} emrData
     * return {*}
     */
    async getImgPreviewData(emrData) {
      let params = {
        inpatientID: emrData.inpatientID,
        sourceType: "RecordPdf",
        fileId: emrData.id,
        fileClassID: emrData.fileClass,
      };
      await GetImgPreviewData(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.imgPreviewData = result.data;
          if (this.imgPreviewData.imageList && this.imgPreviewData.imageList.length) {
            this.imageButtonFlag = true;
          } else {
            this.imageButtonFlag = false;
          }
        }
      });
    },
    /**
     * description: 打开图片预览组件
     * return {*}
     */
    openImgPreview() {
      this.imgPreviewFlag = true;
    },
    /**
     * description: 获取查询配置
     * param {*}
     * return {*}
     */
    async getPatientQueryField() {
      let params = {
        settingTypeCode: "PatientQueryField",
      };
      await GetClinicSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.queryField = res.data;
        }
      });
    },
    /**
     * description: 根据相关参数获取动态获取患者信息
     * param {*} data 返回数据
     * return {*}
     */
    async handleData(data) {
      if (data.length > 0) {
        data.forEach((item) => {
          this.inpatientIDs.push(item.inpatientID);
        });
        this.cloneInpatientIDs = this._common.clone(this.inpatientIDs);
        this.chartNo = data[0].chartNo;
        this.caseNumber = data[0].caseNumber;
        this.numberOfAdmissions = data[0].numberOfAdmissions;
      } else {
        this.datas = [];
        Object.assign(this.patientModel, this.clearPatientModel);
      }
      if (this.inpatientIDs.length > 0) {
        await this.loadPatients(data[0].chartNo);
      }
    },
    async getInpatientDataViewByCaseNumber(caseNumber) {
      let params = {
        caseNumber: caseNumber,
      };
      await GetInpatientDataViewByCaseNumber(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.patientData = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.document {
  * {
    margin: 0;
    padding: 0;
  }
  height: 100%;
  padding: 5px;
  box-sizing: border-box;
  .el-container {
    background-color: #fff;
    border: 1px solid #ccc;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    .el-aside.document-tree-wrap {
      width: 260px !important;
      height: 100%;
      border-right: 1px solid #ccc;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      box-shadow: 0px 3px 12px 1px rgba(0, 0, 0, 0.2);
      .document-input {
        width: 84%;
        margin: 0 auto;
        text-align: right;
      }
      .document-information {
        width: 84%;
        height: 120px;
        border: 1px solid #ccc;
        margin: 6px auto;
        padding: 0 5px;
        border-radius: 3px;
        p {
          float: left;
          min-height: 30px;
          max-height: 60px;
          line-height: 30px;
          font-size: 14px;
          &.information-label {
            width: 41%;
            text-align: center;
          }
          &.information {
            width: 59%;
            &.name {
              word-break: break-all;
              line-height: 22px;
            }
          }
        }
      }
      .emr-button-group {
        margin: 0 auto;
        width: 88%;
        margin-bottom: 6px;
        display: flex;
        .el-button {
          padding: 9px 15px;
          font-size: 14px;
          box-sizing: border-box;
        }
        .refresh-emr-button {
          border-color: #e9b95b;
          background-color: #e9b95b;
        }
        .his-emr-button {
          border-color: #ef946c;
          background-color: #ef946c;
        }
        .holo-emr-button {
          border-color: #7b7de3;
          background-color: #7b7de3;
        }
      }
      .el-tree {
        flex: auto;
        width: 100%;
        height: 100%;
        overflow: auto;
        &.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
          background-color: #b7f6ed;
        }
        & > .el-tree-node {
          min-width: 100%;
          display: inline-block;
          font-size: 14px !important;
          & > .el-tree-node__content {
            background-color: $base-color !important;
            padding: 7px 0;
            & > .el-tree-node__label {
              font-size: 18px;
              color: #fff;
              &.bold {
                font-weight: bold;
              }
            }
          }
        }
        .el-tree-node__label {
          &.bold {
            font-weight: bold;
            color: #000;
          }
        }
        .is-checked.el-checkbox__inner {
          background-color: $base-color;
          border-color: $base-color;
        }
        .expanded.el-tree-node__expand-icon.iconfont.icon-minus::before {
          content: "\e724";
        }
        span.el-tree-node__expand-icon.iconfont.icon-minus::before {
          content: "\e71f";
          color: #605f5f;
        }
        span.is-leaf.el-tree-node__expand-icon.iconfont.icon-minus::before {
          color: transparent;
        }
        .el-tree-node__expand-icon.expanded {
          transform: rotate(180deg);
        }
      }
    }
    .toggle {
      display: block !important;
      position: absolute;
      top: calc(50% - 30px);
      left: 260px;
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
      width: 16px;
      height: 60px;
      border: solid 1px;
      border-color: #cccccc;
      background-color: #ffffff;
      z-index: 100;
      box-shadow: 0px 3px 12px 1px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      &:hover {
        background-color: #1cc6a3;
        &::before {
          border-right: 6px solid #ffffff !important;
        }
      }
      &::before {
        content: "";
        position: absolute;
        top: 24px;
        width: 0;
        height: 0;
        border-bottom: 6px solid transparent !important;
        border-top: 6px solid transparent !important;
        left: 5px;
        border-right: 6px solid #1cc6a3;
        border-left: none;
      }
      &:hover::after {
        border-right: none !important;
      }
      &.hide {
        left: 0;
        &:hover::before {
          border-left: 6px solid #ffffff !important;
        }
        &::before {
          top: 24px;
          left: 6px;
          border-bottom: 6px solid transparent !important;
          border-top: 6px solid transparent !important;
          border-left: 6px solid #1cc6a3;
          border-right: none !important;
        }
      }
    }
    .el-main.document-pdf-wrap {
      position: relative;
      height: 100%;
      overflow: hidden;
      .document-pdf-icon {
        position: absolute;
        top: 26px;
        right: 210px;
        color: #fff;
      }
      .document-pdf {
        height: 100%;
        &.not-pc {
          overflow: auto;
        }
      }
    }
  }
  .his-document.el-dialog {
    .el-dialog__body {
      padding: 0;
      iframe {
        height: 99%;
        width: 100%;
        border: 0;
      }
    }
  }
}
</style>