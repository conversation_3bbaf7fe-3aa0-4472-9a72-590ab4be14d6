/*
 * FilePath     : \src\autoPages\monitoringScheduler\component\cellTypes\hiddenItem.js
 * Author       : 杨欣欣
 * Date         : 2024-08-27 15:07
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-08-27 18:10
 * Description  : 隐藏项组件
 * CodeIterationRecord:
 */
import { formulaExec } from "../../mixins/formulaExec";
export default {
  name: "hiddenItem",
  inject: ["validateInputValueAbnormal"],
  props: {
    row: {
      type: Object,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
  },
  created() {
    this.hiddenItem = this.row[this.column.relationHiddenItemIndex];
    this.cell = this.row[this.column.index];
  },
  data() {
    return {
      hiddenItem: undefined,
      cell: undefined,
    };
  },
  render(h) {
    const { expression, newValue } = formulaExec(
      this.row,
      this.columns,
      this.hiddenItem
    );
    if (this.hiddenItem) {
      if (this.hiddenItem.assessValue != newValue) {
        this.hiddenItem.assessValue = newValue;
        this.validateInputValueAbnormal(this.cell, this.hiddenItem);
      }
      this.hiddenItem.assessValue = newValue;
    }
    return h(
      "span",
      {
        directives: [
          {
            name: "show",
            value: false,
          },
        ],
      },
      this.hiddenItem ? `${expression} = ${this.hiddenItem.assessValue}` : ""
    );
  },
};
