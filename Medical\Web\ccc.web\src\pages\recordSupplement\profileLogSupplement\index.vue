<template>
  <base-layout class="profile-log" v-if="showList">
    <div slot="header">
      <label>执行日期:</label>
      <el-date-picker
        class="data-select"
        v-model="profileLogRecordDate"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        @change="getPatientProfileLog()"
      ></el-date-picker>
      <div class="btn">
        <el-button class="add-button" @click="addPatientprofileLogshowDialog()" icon="iconfont icon-add">
          新增
        </el-button>
      </div>
    </div>

    <el-table :data="supplyDatas" border stripe height="100%" v-loading="loading" element-loading-text="加载中……">
      <el-table-column prop="profileDate" label="执行日期" min-width="85" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.profileDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="profileTime" label="执行时间" min-width="85" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.profileTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="profileLogName" label="描述" min-width="85" align="center"></el-table-column>
      <el-table-column prop="profileLogValue" label="值" min-width="85" align="center"></el-table-column>

      <el-table-column label="操作" width="80" align="center" prop="patientProfileLogId">
        <template slot-scope="monitor">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="echoPatientprofileLog(monitor.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <div class="iconfont icon-del" @click="deletePatientProfileLog(monitor.row)"></div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 修改 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="upDialogTitle"
      :visible.sync="showDialog"
      custom-class="dialog-style"
      v-loading="dialogLoading"
      element-loading-text="保存中……"
    >
      <el-row>
        <el-col class="col-label" :span="4">日期：</el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="profileLogRecordDate"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 120px"
          ></el-date-picker>
        </el-col>
        <el-col class="col-label" :span="4">时间：</el-col>
        <el-col :span="8">
          <el-time-picker
            v-model="profileTime"
            placeholder="选择时间"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 120px"
          ></el-time-picker>
        </el-col>
      </el-row>
      <hr />
      <el-row>
        <el-col class="col-label" :span="3.5">{{ supplyData.profileLogName }}：</el-col>
        <el-col :span="10" class="col-select">
          <el-input v-model="supplyData.profileLogValue" />
        </el-col>
      </el-row>
      <el-row>
        <el-col class="col-nurse" :span="18">
          <!-- <nurse-selector v-model="userID" :stationID="this.user.stationID" width="154px"></nurse-selector> -->
          <user-selector
            v-model="userID"
            :stationID="this.user.stationID"
            label="护士："
            clearable
            filterable
            remoteSearch
            :disabled="disabledFlag"
            width="154px"
          ></user-selector>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="showDialog = false">取消</el-button>
        <el-button v-if="checkResult" type="primary" @click="updatePatientprofileLog()">确定</el-button>
      </div>
    </el-dialog>

    <!-- 新增 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="addDialogTitle"
      :visible.sync="addShowDialog"
      custom-class="dialog-style"
      v-loading="dialogLoading"
      element-loading-text="保存中……"
    >
      <el-row>
        <el-col class="col-label" :span="4">日期：</el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="profileLogRecordDate"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 120px"
          ></el-date-picker>
        </el-col>
        <el-col class="col-label" :span="4">时间：</el-col>
        <el-col :span="8">
          <el-time-picker
            v-model="profileNowTime"
            placeholder="选择时间"
            format="HH:mm"
            value-format="HH:mm"
            style="width: 120px"
          ></el-time-picker>
        </el-col>
      </el-row>
      <hr />
      <el-row>
        <el-col class="col-label" :span="3.5">项目：</el-col>
        <el-col :span="8" class="col-select">
          <el-select v-model="selectTestItemValue" placeholder="请选择项目" style="width: 180px">
            <el-option v-for="(item, key) in testItems" :key="key" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row>
        <el-col class="col-label" :span="3.5" style="padding-left: 25px">值：</el-col>
        <el-col :span="16" class="col-select">
          <el-input style="width: 180px" v-model="supplyData.profileLogValue" />
        </el-col>
      </el-row>
      <el-row>
        <el-col class="col-nurse" :span="18">
          <user-selector
            v-model="userID"
            :stationID="this.user.stationID"
            label="护士："
            clearable
            :disabled="disabledFlag"
            filterable
            remoteSearch
            width="180px"
          ></user-selector>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="addShowDialog = false">取消</el-button>
        <el-button v-if="checkResult" type="primary" @click="addPatientProfileLog">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import {
  GetPatientProfileLog,
  DeletePatientprofileLog,
  UpdatePatientprofileLog,
  SavePatientprofileLog,
} from "@/api/PatientProfileLogRecord";
import baseLayout from "@/components/BaseLayout.vue";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData.vue";
import userSelector from "@/components/selector/userSelector";
import { mapGetters } from "vuex";
import { GetOneSettingByTypeAndCode, GetTPRsettingByCode } from "@/api/Setting";
export default {
  components: {
    baseLayout,
    searchPatientData,
    userSelector,
  },
  data() {
    return {
      selectUserFlag: false,
      loading: false,
      //表格头部开关
      showList: false,
      //筛选记录日期
      profileLogRecordDate: "",
      //筛选记录时间
      profileTime: "",
      //获取当前日期
      profileLogRecordNowDate: "",
      //获取当前时间
      profileNowTime: "",
      //补资料历史数据
      supplyDatas: [],
      //输入框中的对象
      supplyData: {
        profileLogValue: undefined,
        profileLogName: undefined,
        profileTime: undefined,
        profileDate: undefined,
      },
      chartNo: "",
      patient: {},
      //修改显示弹出框flag
      showDialog: false,
      //新增显示弹出框flag
      addShowDialog: false,
      //护士id
      userID: "",
      //弹窗输入内容
      profileLogRecordform: {},
      //弹出框加载旋转样式
      dialogLoading: false,
      //修改标题
      upDialogTitle: "修改身高-体重",
      //新增标题
      addDialogTitle: "新增身高-体重",
      //默认选择的检验项目
      selectTestItemValue: "",
      //检验项目
      testItems: [],
      checkResult: true,
      //是否禁用  这个页面的禁用默认打开
      disabledFlag: false,
      //是否显示保存按钮
      saveButtonFlag: true,
    };
  },
  props: {
    patientinfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    model: {
      type: String,
      default: "page",
    },
    index: {
      type: Number,
      default: 1,
    },
  },
  watch: {
    selectTestItemValue(newVal) {
      if (newVal) {
        this.supplyData.profileLogValue = "";
      }
    },
    index: {
      handler(newIndex) {
        if (newIndex == 1) {
          this.change();
        } else {
          this.selectPatientData(this.patientinfo);
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  mounted() {
    this.getSetting();
    this.getTprSetting();
  },
  methods: {
    /**
     * description: 获取下拉框配置
     * return {*}
     */
    async getTprSetting() {
      let param = {
        settingTypeCode: "SupplementItems",
      };
      await GetTPRsettingByCode(param).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data) {
            res.data.forEach((element) => {
              let params = {
                value: element.typeValue,
                label: element.settingValue,
              };
              this.testItems.push(params);
            });
          }
        }
      });
    },
    getSetting() {
      let param = {
        settingType: 169,
        settingCode: "SwitchForMaintainer",
      };
      GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.typeValue == "False") {
            this.selectUserFlag = false;
          } else {
            this.selectUserFlag = true;
          }
        }
      });
    },

    change() {
      this.formats = [];
      this.optColumn = {};
      this.supplyDatas = [];
      //重新搜索隐藏表格头部
      this.showList = false;
    },
    //查询病人
    selectPatientData(val) {
      if (val == null) {
        this.change();
      } else {
        this.patient = val;
        if (!this.showList) {
          //获取病人筛选记录日期
          this.getProfileLogRecordDate();
        }
        this.showList = true;

        this.getPatientProfileLog();
      }
    },
    getPatientProfileLog() {
      this.dialogLoading = true;
      let params = {
        inpatientID: this.patient.inpatientID,
        systemID: "EMR",
        departmentListID: this.patient.departmentListID,
        profileLogRecordDate: this.profileLogRecordDate,
      };
      GetPatientProfileLog(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.dialogLoading = false;
          if (result.data) {
            if (result.data.formats) {
              this.optColumn = result.data.formats[result.data.formats.length - 1];
            }
            this.supplyDatas = result.data;
          }
        }
      });
    },
    //填入筛选记录时间
    getProfileLogRecordDate() {
      //判断病人出院与否
      this.profileLogRecordDate = this.patient.dischargeDate
        ? this._datetimeUtil.formatDate(this.patient.dischargeDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate();
    },
    //删除PatientProfileLog
    async deletePatientProfileLog(row) {
      //是否仅本人操作
      let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
        this.user.userID,
        false,
        true,
        row.performEmployeeID
      );
      if (!saveButtonFlag) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            patientProfileLogId: row.patientProfileLogId,
          };
          DeletePatientprofileLog(params).then((result) => {
            if (_this._common.isSuccess(result)) {
              _this._showTip("success", "删除成功！");
              _this.selectPatientData(_this.patient);
            }
          });
        }
      });
    },
    //回显
    async echoPatientprofileLog(monitor) {
      ({ disabledFlag: this.disabledFlag, saveButtonFlag: this.checkResult } = await this._common.userSelectorDisabled(
        this.user.userID,
        false,
        true,
        monitor.performEmployeeID
      ));
      this.userID = monitor.performEmployeeID;
      this.profileTime = monitor.profileTime;
      this.showDialog = true;
      Object.assign(this.supplyData, monitor);
    },
    //修改
    updatePatientprofileLog() {
      this.dialogLoading = true;
      let params = {
        ID: this.supplyData.patientProfileLogId,
        ProfileDate: this.profileLogRecordDate,
        ProfileTime: this.profileTime,
        AssessValue: this.supplyData.profileLogValue,
        modifyPersonID: this.userID,
        InpatientID: this.supplyData.inpatientID,
      };
      return UpdatePatientprofileLog(params).then((result) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.showDialog = false;
          this.profileLogRecordform = {};
          this.selectPatientData(this.patient);
        }
      });
    },
    //新增回显弹窗
    async addPatientprofileLogshowDialog() {
      ({ disabledFlag: this.disabledFlag, saveButtonFlag: this.checkResult } = await this._common.userSelectorDisabled(
        this.user.userID,
        true,
        true,
        ""
      ));
      this.addShowDialog = true;
      this.selectTestItemValue = "";
      this.userID = this.user.userID;
      this.profileLogRecordNowDate = this._datetimeUtil.getNowDate();
      this.profileNowTime = this._datetimeUtil.getNowTime();
      this.supplyData.profileLogValue = undefined;
    },
    //新增方法
    addPatientProfileLog() {
      if (this.dialogLoading) {
        return;
      }
      this.dialogLoading = true;
      let params = {
        InpatientID: this.patient.inpatientID,
        CaseNumber: this.patient.caseNumber,
        PatientID: this.patient.patientID,
        ProfileDate: this.profileLogRecordDate,
        ProfileTime: this.profileNowTime,
        AssessListID: this.selectTestItemValue,
        AssessValue: this.supplyData.profileLogValue,
        ModifyPersonID: this.userID,
        ChartNo: this.patient.chartNo,
      };
      return SavePatientprofileLog(params).then((result) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.addShowDialog = false;
          this.profileLogRecordform = {};
          this.selectPatientData(this.patient);
        }
      });
    },
  },
};
</script>
<style lang="scss">
.profile-log {
  height: 100%;
  hr {
    margin: 0 17px 15px 17px;
  }
  .base-header {
    .data-select {
      width: 120px;
    }
    .btn {
      float: right;
    }
  }

  .dialog-style {
    width: 400px;
    height: 45% !important;
    .el-row {
      height: 40px;
      line-height: 40px;
      margin-bottom: 5px;
      .col-label {
        text-align: left;
        padding-left: 12px;
      }
      .col-nurse {
        margin-left: 8px;
      }
      .col-select {
        padding-left: 5px;
      }
    }
  }
}
</style>
