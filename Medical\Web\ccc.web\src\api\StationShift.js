/*
 * FilePath     : \ccc.web\src\api\StationShift.js
 * Author       : 孟昭永
 * Date         : 2020-05-17 15:44
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-09-09 08:22
 * Description  :
 */
import qs from "qs";
import http from "../utils/ajax";
const baseUrl = "/StationShift";

export const urls = {
  GetByStationID: baseUrl + "/GetByStationID",
  GetStationShiftListAll: baseUrl + "/GetStationShiftListAll",
  GetStationShiftByID: baseUrl + "/GetStationShiftByID",
  SaveStationShift: baseUrl + "/SaveStationShift",
  UpdateStationShiftByID: baseUrl + "/UpdateStationShiftByID",
  DeleteStationShiftById: baseUrl + "/DeleteStationShiftById",
  GetNowStationShiftData: baseUrl + "/GetNowStationShiftData",
  GetNowShiftDate: baseUrl + "/GetNowShiftDate",
  GetShifDayStartAndEndDateTime: baseUrl + "/GetShifDayStartAndEndDateTime",
  GetShiftByNurseID: baseUrl + "/GetShiftByNurseID"
};
export const GetByStationID = params => {
  return http.get(urls.GetByStationID, params);
};
export const GetStationShiftListAll = () => {
  return http.get(urls.GetStationShiftListAll);
};
export const GetStationShiftByID = params => {
  return http.get(urls.GetStationShiftByID, params);
};
export const SaveStationShift = params => {
  return http.post(urls.SaveStationShift, params);
};
export const UpdateStationShiftByID = params => {
  return http.post(urls.UpdateStationShiftByID, params);
};
export const DeleteStationShiftById = params => {
  return http.post(urls.DeleteStationShiftById, qs.stringify(params));
};
//取得当前班别
export const GetNowStationShiftData = params => {
  return http.post(urls.GetNowStationShiftData, qs.stringify(params));
};
//获取服务器时间
export const GetNowShiftDate = params => {
  return http.get(urls.GetNowShiftDate, params);
};
// 获取班别一天的开始时间和结束时间
export const GetShifDayStartAndEndDateTime = params => {
  return http.get(urls.GetShifDayStartAndEndDateTime, params);
};
//根据护士ID获取班别
export const GetShiftByNurseID = params => {
  return http.get(urls.GetShiftByNurseID, params);
};
