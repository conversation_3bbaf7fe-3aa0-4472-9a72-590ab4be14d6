/*
 * FilePath     : \src\api\VerifyRecord.js
 * Author       : 李正元
 * Date         : 2021-04-07 09:14
 * LastEditors  : 孟昭永
 * LastEditTime : 2023-10-07 15:03
 * Description  :
 */
import qs from "qs";
import http from "../utils/ajax";
const baseUrl = "/Verify";

export const urls = {
  getVerifyList: baseUrl + "/GetVerifyList",
  verifyRecord: baseUrl + "/VerifyRecord",
  verifyRecords: baseUrl + "/VerifyRecords",
  getEMRList: baseUrl + "/GetEMRList",
  getEMRScoreList: baseUrl + "/GetEMRScoreList",
  getRecordDetail: baseUrl + "/GetRecordDetail",
  getEMRCategory: baseUrl + "/GetEMRCategory",
  getCommonEMRListView: baseUrl + "/GetCommonEMRListView",
  getVerifyLog: baseUrl + "/GetVerifyLog",
  getRecordReviewList: baseUrl + "/GetRecordReviewList",
  getEmrFileList: baseUrl + "/GetEmrFileList",
  reviewFileApproved: baseUrl + "/ReviewFileApproved",
  reviewFileRefuse: baseUrl + "/ReviewFileRefuse"
};

//取得病历清单
export const GetVerifyList = params => {
  return http.get(urls.getVerifyList, params);
};

//审核病历
export const VerifyRecord = params => {
  return http.post(urls.verifyRecord, qs.stringify(params));
};

//批量审核病历
export const VerifyRecords = params => {
  return http.post(urls.verifyRecords, params);
};

//取得电子病历清单
export const GetEMRList = () => {
  return http.get(urls.getEMRList);
};

//取得风险表病历审核清单
export const GetEMRScoreList = params => {
  return http.get(urls.getEMRScoreList, params);
};

//取得病历明细内容
export const GetRecordDetail = params => {
  return http.post(urls.getRecordDetail, params);
};

//取得病历分类
export const GetEMRCategory = params => {
  return http.get(urls.getEMRCategory, params);
};

//取得通用的病历配置
export const GetCommonEMRListView = params => {
  return http.get(urls.getCommonEMRListView, params);
};

//取得审核过内容
export const GetVerifyLog = params => {
  return http.get(urls.getVerifyLog, params);
};

//获取审核患者清单
export const GetRecordReviewList = params => {
  return http.post(urls.getRecordReviewList, params);
};

//获取电子病历文件列表
export const GetEmrFileList = params => {
  return http.get(urls.getEmrFileList, params);
};

//审核病历文件通过
export const ReviewFileApproved = params => {
  return http.post(urls.reviewFileApproved, qs.stringify(params));
};

//审核病历文件驳回
export const ReviewFileRefuse = params => {
  return http.post(urls.reviewFileRefuse, qs.stringify(params));
};
