<!--
 * FilePath     : \src\pages\nursingPlan\index.vue
 * Author       : 李青原
 * Date         : 2020-05-04 10:39
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-27 10:00
 * Description  : 护理计划主页
 -->
<template>
  <div class="nursing-plan-index">
    <el-tabs class="tabs" v-model="activeName">
      <el-tab-pane
        v-for="(component, index) in components"
        :key="index"
        :label="component.label"
        :name="component.name"
      >
        <!-- 动态加载组件 -->
        <component
          v-if="activeName == component.name && Boolean(checkAssessFlag)"
          :is="component.name"
          :inpatientinfo="inpatientInfo"
          :lastAssessDateTime="lastAssessDateTime"
          :admissionDateTime="admissionDateTime"
          @jump-page="jumpPage(index)"
        ></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { GetLastMain } from "@/api/Assess";
//  引入组件
import RiskAssess from "./components/riskScore.vue";
import NursingDiagnosis from "./components/nursingDiagnosis.vue";
import NursingProblems from "./components/nursingProblems.vue";
import NursingPlan from "./components/nursingPlan.vue";
import ClusterCare from "./components/clusterCare.vue";
import PlanOverview from "./components/planOverview.vue";
import { PatientIsAssess } from "@/api/Assess";
import { mapGetters } from "vuex";
export default {
  components: {
    RiskAssess,
    NursingDiagnosis,
    NursingProblems,
    NursingPlan,
    ClusterCare,
    PlanOverview,
  },
  computed: {
    ...mapGetters({
      inpatientInfo: "getPatientInfo",
    }),
  },
  data() {
    return {
      checkAssessFlag: undefined,
      //页签流程控制对象
      activeName: undefined,
      // 页签组件列表
      components: [
        {
          index: 0,
          label: "风险评分",
          name: "riskAssess",
        },
        {
          index: 1,
          label: "护理诊断",
          name: "nursingDiagnosis",
        },
        {
          index: 2,
          label: "护理问题",
          name: "nursingProblems",
        },
        {
          index: 3,
          label: "护理计划",
          name: "nursingPlan",
        },
        {
          index: 4,
          label: "集束护理",
          name: "clusterCare",
        },
        {
          index: 5,
          label: "计划总览",
          name: "planOverview",
        },
      ],
      lastAssessDateTime: undefined,
      admissionDateTime: undefined,
    };
  },
  watch: {
    inpatientInfo: {
      async handler(newValue) {
        if (!newValue) return;
        if (!this.checkAssessFlag) {
          this.checkAssess();
        }
        this.admissionDateTime = this._datetimeUtil.formatDate(newValue.admissionDateTimeView, "yyyy-MM-dd hh:mm");
        await this.getLastAssessDataTime();
      },
      immediate: true,
    },
    activeName: {
      async handler(newValue) {
        if (!newValue) return;
        let routeNames = ["nursingDiagnosis", "nursingProblems"];
        // 临时解决中山刷新护理级别问题，不能长期这样，后续需要迭代掉
        // 只有切换护理诊断或护理问题时才通知病人头组件刷新患者数据
        if (routeNames.indexOf(newValue) != -1) {
          this._sendBroadcast("refreshInpatient");
        }
        if (!this.inpatientInfo) return;
        var temp = this.components.find((component) => {
          return component.name == newValue;
        });
        // 为了切换菜单
        if (temp) {
          this.$router.push({ path: "/nursingPlan?index=" + temp.index });
        }
        this.checkAssess();
      },
      immediate: true,
    },
    // 单页面多路由
    $route: function (to, from) {
      if (to.path == from.path && to.name == "nursingPlan") {
        if (to.path == from.path) {
          let len = to.fullPath.length;
          let index = to.fullPath.substring(len - 1, len);
          this.activeName = this.components[index].name;
        }
      }
    },
  },
  mounted() {
    this._sendBroadcast("setPatientSwitch", true);
    let index = this.$route.query.index;
    if (!index) {
      index = 0;
    }
    this.activeName = this.components[index].name;
  },
  methods: {
    async checkAssess() {
      this.checkAssessFlag = false;
      let component = this.components.find((component) => {
        return component.name == this.activeName;
      });
      if (component && component.index >= 0 && component.index < 5) {
        let params = {
          inPatientID: this.inpatientInfo.inpatientID,
        };
        await PatientIsAssess(params).then((response) => {
          if (this._common.isSuccess(response)) {
            this.checkAssessFlag = true;
          } else {
            this.checkAssessFlag = false;
          }
        });
      } else {
        this.checkAssessFlag = true;
      }
    },
    jumpPage(index) {
      if (index > 0 && index < 5) {
        this.activeName = this.components[index + 1].name;
      }
    },
    async getLastAssessDataTime() {
      //获取最近护理评估事件
      let params = {
        inpatientID: this.inpatientInfo.inpatientID,
      };
      await GetLastMain(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data && response.data.assessDate && response.data.assessTime) {
            this.lastAssessDateTime =
              this._datetimeUtil.formatDate(response.data.assessDate, "yyyy-MM-dd") +
              " " +
              this._datetimeUtil.formatDate(response.data.assessTime, "hh:mm");
          } else {
            this.lastAssessDateTime = undefined;
          }
        }
      });
    },
  },
};
</script>
<style lang="scss">
.nursing-plan-index {
  height: 100%;
  .tabs {
    height: 100%;
    box-sizing: border-box;
    .el-tabs__content {
      height: calc(100% - 25px);
      box-sizing: border-box;
      .el-tab-pane {
        box-sizing: border-box;
        height: 100%;
        background-color: #ffffff;
      }
    }
  }
}
</style>
