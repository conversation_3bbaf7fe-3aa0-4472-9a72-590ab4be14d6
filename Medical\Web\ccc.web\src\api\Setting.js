/*
 * FilePath     : \src\api\Setting.js
 * Author       : 李青原
 * Date         : 2020-03-30 08:19
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-23 16:42
 * Description  :
 */
import qs from "qs";
import http from "../utils/ajax";
const baseUrl = "/setting";

export const urls = {
  GetBySettingTypeCode: baseUrl + "/GetScheduleTop",
  GetIOKind: baseUrl + "/GetIOKind",
  GetIOItem: baseUrl + "/GetIOItem",
  GetServerDateTime: baseUrl + "/GetServerDateTime",
  GetColorByAssessListID: baseUrl + "/GetColorByAssessListID",
  GetColorByAssessListIDArr: baseUrl + "/GetColorByAssessListIDArr",
  GetRemoveTubeReason: baseUrl + "/GetRemoveTubeReason",
  GetWoundImageNum: baseUrl + "/GetWoundImageNum",
  GetWoundTypes: baseUrl + "/GetWoundTypes",
  GetStomaTypes: baseUrl + "/GetStomaTypes",
  GetNowShiftByStationID: baseUrl + "/GetNowShiftByStationIDAsync",
  GetClinicalBySettingTypeCode: baseUrl + "/GetClinicalBySettingTypeCode",
  GetShiftTimeLine: baseUrl + "/GetShiftTimeLine",
  GetBySettingTypeCodeByArray: baseUrl + "/GetBySettingTypeCodeByArray",
  GetClinicalBySettingTypeCodeAndValue:
    baseUrl + "/GetClinicalBySettingTypeCodeAndValue",
  GetAllAPISetting: baseUrl + "/GetAllAPISetting",
  GetPatterns: baseUrl + "/GetPatterns",
  GetPaternElements: baseUrl + "/GetPaternElements",
  GetConfigSettingWebProxyURL: baseUrl + "/GetConfigSettingWebProxyURL",
  GetByListByPinyin: baseUrl + "/GetByListByPinyin",
  GetClinicalSettingByTypeCode: baseUrl + "/GetClinicalSettingByTypeCode",
  GetOneSettingByTypeAndCode: baseUrl + "/GetOneSettingByTypeAndCode",
  GetScheduleTop: baseUrl + "/GetScheduleTop",
  GetCacheList: baseUrl + "/GetCacheList",
  GetVitalSignPoint: baseUrl + "/GetVitalSignPoint",
  GetHospitalInfo: baseUrl + "/GetHospitalInfo",
  GetLanguage: baseUrl + "/GetLanguage",
  GetHospitalList: baseUrl + "/GetHospitalList",
  GetVersionByHospitalID: baseUrl + "/GetVersionByHospitalID",
  GetTransfusionType: baseUrl + "/GetTransfusionType",
  // 获取最新版本号
  CheckVersion: "/MobileVersion/CheckVersion",
  GetBringToShiftSetting: baseUrl + "/GetBringToShiftSetting",
  //前端是否呈现
  GetViewShow: baseUrl + "/GetViewShow",
  //前端是否呈现
  GetViewShows: baseUrl + "/GetViewShows",
  GetAdverseEventTypes: baseUrl + "/GetAdverseEventTypes",
  //根据输血血液制品类型数据
  GetBloodProductCategorySetting: baseUrl + "/GetBloodProductCategorySetting",
  //获取是否进行自动组装评估内容开关
  GetObserveJiontSetting: baseUrl + "/GetObserveJiontSetting",
  //获取交班类型
  GetHandOverType: baseUrl + "/GetHandOverType",
  //获取下拉框类配置
  GetSelectSetting: baseUrl + "/GetSelectSetting",
  //根据SettingTypeCode获取配置类别
  GetClinicSettingByTypeCode: baseUrl + "/GetClinicSettingByTypeCode",
  //根据settingTypeCode取到TPRsetting配置
  GetTPRsettingByCode: baseUrl + "/GetTPRsettingByCode",
  GetSettingGroupByTypeCodes: baseUrl + "/GetSettingGroupByTypeCodes",
  GetSettingValuesByTypeCodeAndValue:
    baseUrl + "/GetSettingValuesByTypeCodeAndValue",
  GetSwitchByTypeCode: baseUrl + "/GetSwitchByTypeCode",
  GetInterventionToRecordSetting: baseUrl + "/GetInterventionToRecordSetting",
  UpdateInterventionToRecordSetting:
    baseUrl + "/UpdateInterventionToRecordSetting",
  GetSettingSwitchsByTypeCodeAndValue:
    baseUrl + "/GetSettingSwitchsByTypeCodeAndValue",
  GetSettingOptionsByTypeCode: baseUrl + "/GetSettingOptionsByTypeCode",
  GetClinicShow: baseUrl + "/GetClinicShow",
  GetIOSetting: baseUrl + "/GetIOSetting",
  GetStationIconList: baseUrl + "/GetStationIconList",
  UpdateStationIconSetting: baseUrl + "/UpdateStationIconSetting",
  GetClinicalSettingValuesBySettingTypeCode:
    baseUrl + "/GetClinicalSettingValuesBySettingTypeCode",
  GetEMRExists: baseUrl + "/GetEMRExists",
  GetNursingInterventionMainList: baseUrl + "/GetNursingInterventionMainList",
  GetIconCategoryDict: baseUrl + "/GetIconCategoryDict",
  GetTypeValueAndDescriptions: baseUrl + "/GetTypeValueAndDescriptions",
  GetAppConfigSetting: baseUrl + "/GetAppConfigSetting"
};
//根据settingTypeCode取到TPRsetting配置
export const GetTPRsettingByCode = (params) => {
  return http.get(urls.GetTPRsettingByCode, params);
};
//透过类别码取得配置数据
export const GetBySettingTypeCode = (params) => {
  return http.get(urls.GetBySettingTypeCode, params);
};

export const GetIOKind = (params) => {
  return http.get(urls.GetIOKind, params);
};

export const GetIOItem = (params) => {
  return http.get(urls.GetIOItem, params);
};

export const GetServerDateTime = (params) => {
  return http.get(urls.GetServerDateTime, params);
};

// 获取io色块
export const GetColorByAssessListID = (params) => {
  return http.get(urls.GetColorByAssessListID, params);
};
// 获取io色块根据ID数据
export const GetColorByAssessListIDArr = (params) => {
  return http.post(urls.GetColorByAssessListIDArr, qs.stringify(params));
};

// 获取拔管原因信息
export const GetRemoveTubeReason = (params) => {
  return http.get(urls.GetRemoveTubeReason, params);
};

// 获取伤口图片数量
export const GetWoundImageNum = (params) => {
  return http.get(urls.GetWoundImageNum, params);
};

// 压力性损伤伤口类别
export const GetWoundTypes = () => {
  return http.get(urls.GetWoundTypes);
};

//造口类别
export const GetStomaTypes = () => {
  return http.get(urls.GetStomaTypes);
};

// 获取当前科室当前时间所属班别
export const GetNowShiftByStationID = () => {
  return http.get(urls.GetNowShiftByStationID);
};

// 透过临床配置码取得配置
export const GetClinicalBySettingTypeCode = (params) => {
  return http.get(urls.GetClinicalBySettingTypeCode, params);
};
// 获取班次时间轴
export const GetShiftTimeLine = (params) => {
  return http.get(urls.GetShiftTimeLine, params);
};
// 根绝SettingTypeCode数组获取配置
export const GetBySettingTypeCodeByArray = (params) => {
  return http.get(urls.GetBySettingTypeCodeByArray, params);
};

export const GetClinicalBySettingTypeCodeAndValue = (params) => {
  return http.get(urls.GetClinicalBySettingTypeCodeAndValue, params);
};
//获取API
export const GetAllAPISetting = () => {
  return http.get(urls.GetAllAPISetting);
};

//获取API
export const GetPatterns = (params) => {
  return http.get(urls.GetPatterns, params);
};
//获取API
export const GetPaternElements = (params) => {
  return http.get(urls.GetPaternElements, params);
};
//获取WebProxyURL
export const GetConfigSettingWebProxyURL = (params) => {
  return http.get(urls.GetConfigSettingWebProxyURL, params);
};
//根据数据表名称获取简拼项目
export const GetByListByPinyin = (params) => {
  return http.get(urls.GetByListByPinyin, params);
};

//2020-05-19通过settingType，settingCode取得措施码interventionID
export const GetClinicalSettingByTypeCode = (params) => {
  return http.get(urls.GetClinicalSettingByTypeCode, params);
};

// 根据Type和Code获取一条数据
export const GetOneSettingByTypeAndCode = (params) => {
  return http.get(urls.GetOneSettingByTypeAndCode, params);
};

//取得药品类别
export const GetScheduleTop = (param) => {
  return http.get(urls.GetScheduleTop, param);
};

//因应缓存维护加上取得缓存清单
export const GetCacheList = () => {
  return http.get(urls.GetCacheList);
};
// 获取体温单上时间点集合
export const GetVitalSignPoint = (param) => {
  return http.get(urls.GetVitalSignPoint, param);
};

//取得医院信息
export const GetHospitalInfo = (param) => {
  return http.get(urls.GetHospitalInfo, param);
};
//取得语言
export const GetLanguage = (param) => {
  return http.get(urls.GetLanguage, param);
};
// 获取医院集合
export const GetHospitalList = () => {
  return http.get(urls.GetHospitalList);
};
// 获取医院对应系统版本号
export const GetVersionByHospitalID = (param) => {
  return http.get(urls.GetVersionByHospitalID, param);
};

//获取输血方式名称字典
export const GetTransfusionType = () => {
  return http.get(urls.GetTransfusionType);
};

// 获取最新版本号
export const CheckVersion = (param) => {
  return http.get(urls.CheckVersion, param);
};
//获取专项护理是否带入交班配置
export const GetBringToShiftSetting = (params) => {
  return http.get(urls.GetBringToShiftSetting, params);
};
//前端是否呈现
export const GetViewShow = (params) => {
  return http.get(urls.GetViewShow, params);
};
//前端是否呈现
export const GetViewShows = (params) => {
  return http.post(urls.GetViewShows, qs.stringify(params));
};
// 获取不良事件类型
export const GetAdverseEventTypes = (params) => {
  return http.get(urls.GetAdverseEventTypes, params);
};
//获取输血血液制品类型
export const GetBloodProductCategorySetting = (params) => {
  return http.get(urls.GetBloodProductCategorySetting, params);
};
//获取是否进行自动组装评估内容开关
export const GetObserveJiontSetting = (params) => {
  return http.get(urls.GetObserveJiontSetting, params);
};
//获取交班类型
export const GetHandOverType = (params) => {
  return http.get(urls.GetHandOverType, params);
};
//获取下拉框类配置
export const GetSelectSetting = (params) => {
  return http.get(urls.GetSelectSetting, params);
};
//根据SettingTypeCode获取配置类别
export const GetClinicSettingByTypeCode = (params) => {
  return http.get(urls.GetClinicSettingByTypeCode, params);
};
// 根据一组配置码获取一组配置
export const GetSettingGroupByTypeCodes = (params) => {
  return http.post(urls.GetSettingGroupByTypeCodes, qs.stringify(params));
};
//查询配置
export const GetSettingValuesByTypeCodeAndValue = (params) => {
  return http.get(urls.GetSettingValuesByTypeCodeAndValue, params);
};
//获取配置开关
export const GetSwitchByTypeCode = (params) => {
  return http.get(urls.GetSwitchByTypeCode, params);
};
//获取interventionToRecordSetting配置
export const GetInterventionToRecordSetting = (params) => {
  return http.get(urls.GetInterventionToRecordSetting, params);
};
//更新interventionToRecordSetting配置
export const UpdateInterventionToRecordSetting = (params) => {
  return http.post(
    urls.UpdateInterventionToRecordSetting,
    qs.stringify(params)
  );
};
//获取开关配置：ClinicalSettingInfo {SettingTypeCode,TypeValue}
export const GetSettingSwitchsByTypeCodeAndValue = (params) => {
  return http.get(urls.GetSettingSwitchsByTypeCodeAndValue, params);
};
//获取列表配置
export const GetSettingOptionsByTypeCode = (params) => {
  return http.get(urls.GetSettingOptionsByTypeCode, params);
};

//是否显示仪器数据按钮
export const GetClinicShow = (params) => {
  return http.get(urls.GetClinicShow, params);
};
//获取出入量配置
export const GetIOSetting = (params) => {
  return http.get(urls.GetIOSetting, params);
};
//获取患者标识
export const GetStationIconList = (params) => {
  return http.get(urls.GetStationIconList, params);
};
//更新患者标识
export const UpdateStationIconSetting = (params) => {
  return http.post(urls.UpdateStationIconSetting, qs.stringify(params));
};
// 获取SettingValue集合
export const GetClinicalSettingValuesBySettingTypeCode = (params) => {
  return http.get(urls.GetClinicalSettingValuesBySettingTypeCode, params);
};
//获取病历是否存在
export const GetEMRExists = (params) => {
  return http.get(urls.GetEMRExists, params);
};
//查询获取护理措施主表数据
export const GetNursingInterventionMainList = (params) => {
  return http.get(urls.GetNursingInterventionMainList, params);
};
//获取标记分类字典
export const GetIconCategoryDict = (params) => {
  return http.get(urls.GetIconCategoryDict, params);
};
//获取类型值和描述
export const GetTypeValueAndDescriptions = (params) => {
  return http.get(urls.GetTypeValueAndDescriptions, params);
};
//获取AppConfigSetting配置
export const GetAppConfigSetting = (params) => {
  return http.get(urls.GetAppConfigSetting, params);
};
