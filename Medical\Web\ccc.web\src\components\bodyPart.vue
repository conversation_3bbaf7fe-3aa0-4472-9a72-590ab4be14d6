<template>
  <div class="select-body-part" :style="style">
    <span v-if="label">{{ label }}</span>
    <div :class="['body-part-name', { disabled: disabled }]" @click="showBodyPart">
      <span v-if="showName">{{ showName }}</span>
      <span v-else class="no-value">选择身体部位</span>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="选择部位"
      :visible.sync="showFlag"
      :append-to-body="true"
      custom-class="body-part-dialog"
    >
      <iframe :src="url" frameborder="0" class="body-html"></iframe>
      <div slot="footer">
        <el-button @click="close(false)">取消</el-button>
        <el-button type="primary" @click="close(true)">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showFlag: false,
      bodyPart: {},
      showName: undefined,
    };
  },
  props: {
    value: {
      required: true,
    },
    gender: {
      type: String,
      required: true,
    },
    tubeID: {
      type: Number,
    },
    width: {
      type: String,
      default: "100px",
    },
    label: {
      type: String,
      default: "部位：",
    },
    type: {
      type: String,
      default: "Common",
    },
    //2020-09-24加上表单对应身体部位
    recordsCode: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        this.bodyPart = newValue;
        this.updateName();
      },
    },
  },
  computed: {
    style() {
      let widthSize = this._common.getHeigt(this.width);
      if (this.type === "CommonMulti") {
        return {
          "min-width": widthSize,
        };
      } else {
        return {
          width: widthSize,
        };
      }
    },
    url() {
      let link =
        "../../static/body/mobileBody.html?type=" + this.type + "&gender=" + this.gender + "&t=" + Math.random();
      if (this.tubeID) {
        link += "&tubeID=" + this.tubeID;
      }
      if (this.recordsCode) {
        link += "&recordsCode=" + this.recordsCode;
      }
      return link;
    },
  },
  methods: {
    showBodyPart() {
      if (this.disabled) {
        return;
      }
      this._common.storage("selectPart", this.value);
      this._common.storage("bodyPart", this.value);
      this.showFlag = true;
    },
    close(flag) {
      if (flag) {
        this.bodyPart = this._common.storage("bodyPart");
        this.$emit("input", this.bodyPart);
        this.updateName();
        this.$emit("change", this.bodyPart);
      }
      this.showFlag = false;
    },
    updateName() {
      this.showName = "";
      // 多选返回
      if (this.bodyPart instanceof Array) {
        this.bodyPart.forEach((part) => {
          if (this.showName) {
            this.showName += "、" + part.bodyPartName;
          } else {
            this.showName = part.bodyPartName;
          }
        });
      } else {
        // 单选返回
        this.showName = this.bodyPart.bodyPartName;
      }
    },
  },
};
</script>

<style lang="scss">
.select-body-part {
  float: left;
  display: flex;
  white-space: nowrap;
  width: 100%;
  height: 30px;
  line-height: 30px;
  margin-right: 5px;
  box-sizing: border-box;
  .body-part-name {
    flex: auto;
    width: 100%;
    background-color: #ffffff;
    color: #606266;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    cursor: pointer;
    padding: 0 10px;
    margin-left: 5px;
  }
  .disabled {
    background-color: #eee;
    cursor: not-allowed;
  }
  .no-value {
    color: #c8c9cc;
  }
}
.body-part-dialog.el-dialog {
  margin-top: 14vh !important;
  width: 550px;
  height: 600px;
  .body-html {
    height: 97%;
    width: 98%;
    border: 0;
    box-sizing: border-box;
  }
}
</style>
