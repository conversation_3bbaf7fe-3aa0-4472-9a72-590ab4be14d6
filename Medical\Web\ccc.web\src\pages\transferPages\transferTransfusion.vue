<!--
 * FilePath     : \src\pages\transferPages\transferTransfusion.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 苏军志
 * LastEditTime : 2020-07-16 16:55
 * Description  : 串输血记录
--> 
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
// 代码需要迁移，暂时串到nursing
import { getOldNursingUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: ""
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken"
    })
  },
  created() {
    this.url = getOldNursingUrl() + "export/transfusion?token=" + this.token;
  }
};
</script>
