/*
 * FilePath     : \src\utils\setting.js
 * Author       : 苏军志
 * Date         : 2020-05-27 21:26
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-04-26 11:18
 * Description  :
 */

import ajax from "@/utils/ajax";
import showTip from "@/utils/toast";

export const keys = {
  token: "medical-token",
  isLogin: "medical-login",
};
var bedNumberWidth = undefined;
var medicalApiUrl = undefined;
var externalApiUrl = undefined;
var webProxyApiUrl = undefined;
var timeLineServer = undefined;
var managementUrl = undefined;
var hisCommonUrl = undefined;
var oldNursingUrl = undefined;
var statisticsUrl = undefined;
var mqSetting = undefined;
var educationEdition = undefined;
var ENV = undefined;
var huiMei = undefined;
var nursingBoard = undefined;
let statisticsV3Url = undefined;
let nursingManagementUrl = undefined;
let cellReportUrl = undefined;

export const initConfig = () => {
  // index.html引入js配置文件
  if (apiConfig) {
    // 获取床位组件宽度
    bedNumberWidth = apiConfig.bedNumberWidth;
    ENV = apiConfig.RunEnv;
    if (ENV) {
      console.log(
        "%c当前环境：" +
          (ENV == "pro"
            ? "生产环境（pro）"
            : ENV == "test"
            ? "测试环境（test）"
            : "本机测试（local）"),
        "color:#1cc6a3;font-size:1.6em"
      );
      switch (ENV) {
        case "local":
          // 本机测试环境
          medicalApiUrl = apiConfig.localConfig.medicalApiUrl;
          externalApiUrl = apiConfig.localConfig.externalApiUrl;
          webProxyApiUrl = apiConfig.localConfig.webProxyApiUrl;
          timeLineServer = apiConfig.localConfig.timeLineServer;
          managementUrl = apiConfig.localConfig.managementUrl;
          hisCommonUrl = apiConfig.localConfig.hisCommonUrl;
          oldNursingUrl = apiConfig.localConfig.oldNursingUrl;
          statisticsUrl = apiConfig.localConfig.statisticsUrl;
          mqSetting = apiConfig.localConfig.mqSetting;
          educationEdition = apiConfig.localConfig.educationEdition;
          huiMei = apiConfig.localConfig.huiMei;
          nursingBoard = apiConfig.localConfig.nursingBoard;
          statisticsV3Url = apiConfig.localConfig.statisticsV3Url;
          nursingManagementUrl = apiConfig.localConfig.nursingManagementUrl;
          cellReportUrl = apiConfig.localConfig.cellReportUrl;
          break;
        case "test":
          // 测试环境
          medicalApiUrl = apiConfig.testConfig.medicalApiUrl;
          externalApiUrl = apiConfig.testConfig.externalApiUrl;
          timeLineServer = apiConfig.testConfig.timeLineServer;
          managementUrl = apiConfig.testConfig.managementUrl;
          hisCommonUrl = apiConfig.testConfig.hisCommonUrl;
          oldNursingUrl = apiConfig.testConfig.oldNursingUrl;
          statisticsUrl = apiConfig.testConfig.statisticsUrl;
          mqSetting = apiConfig.testConfig.mqSetting;
          educationEdition = apiConfig.testConfig.educationEdition;
          huiMei = apiConfig.testConfig.huiMei;
          statisticsV3Url = apiConfig.testConfig.statisticsV3Url;
          nursingManagementUrl = apiConfig.localConfig.nursingManagementUrl;
          break;
        case "pro":
          // 生产环境
          medicalApiUrl = apiConfig.config.medicalApiUrl;
          externalApiUrl = apiConfig.config.externalApiUrl;
          timeLineServer = apiConfig.config.timeLineServer;
          managementUrl = apiConfig.config.managementUrl;
          hisCommonUrl = apiConfig.config.hisCommonUrl;
          oldNursingUrl = apiConfig.config.oldNursingUrl;
          statisticsUrl = apiConfig.config.statisticsUrl;
          mqSetting = apiConfig.config.mqSetting;
          educationEdition = apiConfig.config.educationEdition;
          huiMei = apiConfig.config.huiMei;
          statisticsV3Url = apiConfig.config.statisticsV3Url;
          nursingManagementUrl = apiConfig.localConfig.nursingManagementUrl;
          break;
      }
      setMedicalApiUrl();
    }
  } else {
    showTip.showTip("warning", "找不到配置文件！");
  }
};
export const setMedicalApiUrl = () => {
  ajax.setMedicalApiUrl(medicalApiUrl);
  localStorage.setItem("apiUrl", medicalApiUrl);
};
export const getExternalApiUrl = () => {
  return externalApiUrl;
};
export const getMessageApiUrl = () => {
  return mqSetting.serviceUrl;
};
export const getWebProxyApiUrl = () => {
  return webProxyApiUrl;
};
export const getBedNumberWidth = () => {
  return bedNumberWidth;
};
export const getTimeLineServer = () => {
  return timeLineServer;
};
export const getManagementUrl = () => {
  return managementUrl;
};
export const hisHisCommonUrl = () => {
  return hisCommonUrl;
};
export const getOldNursingUrl = () => {
  return oldNursingUrl;
};
export const getStatisticsUrl = () => {
  return statisticsUrl;
};
export const getMQSetting = () => {
  return mqSetting;
};
export const getEducationEdition = () => {
  return educationEdition;
};
export const getHuiMei = () => {
  return huiMei;
};
export const getNursingBoard = () => {
  return nursingBoard;
};
export const getStatisticsV3Url = () => {
  return statisticsV3Url;
};
export const getNursingManagementUrl = () => {
  return nursingManagementUrl;
};
export const getCellReportUrl = () => {
  return cellReportUrl;
};
