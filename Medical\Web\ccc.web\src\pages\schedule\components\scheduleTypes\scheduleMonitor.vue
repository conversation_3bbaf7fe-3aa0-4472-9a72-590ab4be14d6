<!--
 * FilePath     : \src\pages\schedule\components\scheduleTypes\scheduleMonitor.vue
 * Author       : 苏军志
 * Date         : 2022-05-08 11:27
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-15 19:47
 * Description  : 排程执行画面，Monitor类型页面
 * CodeIterationRecord: 2744-作为IT人员，我需要排程执行疼痛及批量执行，可以跳转专项护理 2022-07-07 杨欣欣
                        2855-作为IT人员，我需要依据提供的资料调整嘉会产科及新生儿科交接内容，以利于护理交班内容完整。 2022-08-24 杨欣欣
                        2869-IO弹窗改为专项跳转 2022-11-28 杨欣欣
-->
<template>
  <base-layout
    class="schedule-monitor"
    v-loading="loading"
    :element-loading-text="loadingText"
    :showFooter="!hideOperate"
    headerHeight="auto"
  >
    <div slot="header">
      <div class="header-item">
        <span class="label">执行日期：</span>
        <el-date-picker
          v-model="scheduleDate"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          class="assess-date"
          :clearable="false"
          placeholder="日期"
        ></el-date-picker>
      </div>
      <div class="header-item">
        <span class="label">执行时间：</span>
        <el-time-picker
          v-model="scheduleTime"
          value-format="HH:mm"
          format="HH:mm"
          :clearable="false"
          class="assess-time"
        ></el-time-picker>
      </div>
      <el-button
        v-if="clinicFlag"
        class="clinic-btn"
        type="primary"
        icon="iconfont icon-clinic1"
        @click="showClinicDialog()"
      >
        仪器数据
      </el-button>
    </div>
    <div ref="scheduleMonitor" class="monitor-wrap" v-enter="{ function: 'savePatientSchedule' }">
      <div v-if="normalItem || params.interventionType == 'T'">
        <el-radio-group
          class="normal-item-wrap"
          v-model="normalItem.scheduleData"
          v-if="normalItem && normalItem.childs"
        >
          <el-radio-button
            v-for="(setting, index) in normalItem.childs"
            :key="index"
            :label="index + 1 + ''"
            class="normal-item"
            @click.native.prevent="clickNormalItem(normalItem, index + 1 + '')"
          >
            {{ setting.description }}
          </el-radio-button>
        </el-radio-group>
        <el-button
          v-if="params.interventionType == 'T' && params.performUrl"
          class="print-button"
          @click="showTeachingData"
        >
          宣教材料
        </el-button>
      </div>
      <div
        v-if="
          templateDatas &&
          templateDatas.length > 0 &&
          (!normalItem || (normalItem && (normalItem.scheduleData == '2' || normalItem.scheduleData == '')))
        "
      >
        <template v-for="(item, index) in templateDatas">
          <div v-if="item.children" :key="index">
            <span class="group">{{ getText(item.showName) }}</span>
            <div class="child-item">
              <monitor-view
                v-for="(child, index) in item.children"
                :key="index"
                :item="child"
                :items="item.children"
                @changeValue="changeValue($event, item.children)"
                @checkTN="checkTN"
                @inputEnter="enterEvent"
                @button-click="buttonClick"
              ></monitor-view>
            </div>
          </div>
          <monitor-view
            :key="index + 1"
            v-else
            :item="item"
            :items="templateDatas"
            @changeValue="changeValue($event, templateDatas)"
            @checkTN="checkTN"
            @inputEnter="enterEvent"
            @button-click="buttonClick"
          ></monitor-view>
        </template>
      </div>
      <div v-if="notDialog" class="not-dialog">
        <el-checkbox class="bring-checkbox" v-model="informPhysician">通知医师</el-checkbox>
        <el-checkbox class="bring-checkbox" v-model="bringToShift">带入交班</el-checkbox>
        <el-checkbox class="bring-checkbox" v-model="bringToNursingRecords">带入护理记录单</el-checkbox>
      </div>
      <div class="perform-comment">
        <span>执行说明：</span>
        <el-input
          v-model="performComment"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 4 }"
          placeholder="请输入内容"
          resize="none"
        ></el-input>
      </div>
    </div>
    <div slot="footer">
      <el-checkbox class="bring-checkbox" v-model="informPhysician">通知医师</el-checkbox>
      <el-checkbox class="bring-checkbox" v-model="bringToShift">带入交班</el-checkbox>
      <el-checkbox class="bring-checkbox" v-model="bringToNursingRecords">带入护理记录单</el-checkbox>
      <el-button @click="close(false)">取消</el-button>
      <el-button v-if="checkResult" type="primary" @click="savePatientSchedule()">确定</el-button>
    </div>
    <!-- 显示宣教资料 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="params.interventionName + '-宣教资料'"
      :visible.sync="showTeaching"
      :append-to-body="true"
      custom-class="no-footer teaching"
    >
      <file-preview v-if="showTeaching" :datas="teachingData"></file-preview>
    </el-dialog>
    <!-- 按钮弹出框 -->
    <el-dialog
      v-dialogDrag
      :title="buttonName"
      :close-on-click-modal="false"
      :visible.sync="showButtonDialog"
      fullscreen
      custom-class="no-footer specific-care"
      append-to-body
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
    </el-dialog>
    <!--仪器数据-->
    <el-drawer
      title="仪器数据"
      :modal-append-to-body="false"
      :visible.sync="showClinicFlag"
      :destroy-on-close="true"
      direction="btt"
      size="50%"
      custom-class="clinic-drawer"
      :wrapperClosable="false"
    >
      <clinic-view
        height="190px"
        v-model="timeRange"
        :clinicPrams="clinicPrams"
        @getSelectClinicData="postClinicData"
      ></clinic-view>
    </el-drawer>
  </base-layout>
</template>
<script>
import monitorView from "./monitorView";
import filePreview from "@/components/FilePreview";
import baseLayout from "@/components/BaseLayout";
import clinicView from "@/pages/schedule/components/scheduleTypes/clinicView";
import { GetPatientScheduleSingle, SavePatientScheduleSingle, GetButtonData } from "@/api/PatientSchedule";
import { PerformMedicine } from "@/api/MedicineSchedule";
import { mapGetters } from "vuex";
export default {
  components: {
    monitorView,
    filePreview,
    baseLayout,
    clinicView,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getCurrentPatient",
      token: "getToken",
    }),
  },
  props: {
    params: {
      type: Object,
      require: true,
    },
    hideOperate: {
      type: Boolean,
      default: false,
    },
    notDialog: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    params: {
      immediate: true,
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.init();
        }
      },
    },
    showButtonDialog(newVal) {
      if (!newVal) {
        this.updateButton();
      }
    },
  },
  data() {
    return {
      loading: false,
      loadingText: "加载中……",
      scheduleDate: "",
      scheduleTime: "",
      normalItem: undefined,
      templateDatas: undefined,
      bringToNursingRecords: "1",
      bringToShift: "0",
      performComment: "",
      saveDatas: [],
      checkTNFlag: true,
      //是否显示仪器数据
      clinicFlag: false,
      // 是否显示宣教资料
      showTeaching: false,
      teachingData: [],
      checkResult: true,
      informPhysician: undefined,
      painScoreThreshold: undefined,
      inputs: [],
      showButtonDialog: false,
      buttonName: "",
      // 子组件回传父组件的细项的ID
      buttonInterventionDetailID: undefined,
      showClinicFlag: false,
      //获取仪器数据所需params
      clinicPrams: {},
      //默许取得仪器时间范围
      timeRange: 30,
    };
  },
  methods: {
    showTeachingData() {
      this.teachingData = this.params.performUrl.split("||");
      this.showTeaching = true;
    },
    close(flag, triggerParams) {
      this.$emit("close", flag, triggerParams);
    },
    async init() {
      this.normalItem = undefined;
      this.scheduleDate = this.params.performDate;
      this.scheduleTime = this.params.performTime;
      this.bringToNursingRecords = this.params.bringToNursingRecord;
      this.bringToShift = this.params.bringToShift;
      this.performComment = this.params.performComment;
      this.painScoreThreshold = this.params.painScoreThreshold;
      this.saveDatas = [];
      this.loading = true;
      this.loadingText = "加载中……";
      this.templateDatas = undefined;
      this.informPhysician = this.params.informPhysician;
      let params = {
        inpatientID: this.params.inpatientID,
        patientScheduleMainID: this.params.patientScheduleMainID,
      };
      await GetPatientScheduleSingle(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          if (result.data) {
            this.templateDatas = result.data;
            for (let i = this.templateDatas.length - 1; i >= 0; i--) {
              if (this.templateDatas[i].isNormal && this.templateDatas[i].isNormal == "1") {
                this.normalItem = this._common.clone(this.templateDatas[i]);
                if (!this.normalItem.scheduleData) {
                  if (this.normalItem.childs && this.normalItem.childs.length == 1) {
                    this.normalItem.scheduleData = "";
                  } else {
                    this.normalItem.scheduleData = "2";
                  }
                }
                this.changeValue(this.normalItem);
                this.templateDatas.splice(i, 1);
                break;
              }
            }
          }
        }
      });
      if (this.params.interventionID) {
        this.clinicFlag = this.params.instrumentTimeRange != 0;
      }
      this.loading = false;
      //获取所有TN输入框
      this.$nextTick(() => {
        this.getAllInputs();
      });
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, this.params.performEmployeeID);
    },
    getAllInputs() {
      this.inputs = this.$refs.scheduleMonitor.getElementsByTagName("input");
      let sucInputs = [];
      for (let index = 0; index < this.inputs.length; index++) {
        let input = this.inputs[index];
        if (input.name && input.name == "TN") {
          sucInputs.push(input);
        }
      }
      this.inputs = sucInputs;
    },
    //上下键键切换TN输入框
    enterEvent(nowInput, flag) {
      for (let index = 0; index < this.inputs.length; index++) {
        let input = this.inputs[index];
        if (input == nowInput.target) {
          if (flag && index == this.inputs.length - 1) {
            this.inputs[0].focus();
            break;
          } else if (!flag && index == 0) {
            this.inputs[this.inputs.length - 1].focus();
            break;
          } else {
            input.blur();
            this.inputs[flag ? index + 1 : index - 1].focus();
          }
        }
      }
    },
    getText(text) {
      if (!text) return "";
      if (text.indexOf(":") != -1 || text.indexOf("：") != -1) {
        return text;
      }
      return (text += "：");
    },
    clickNormalItem(normalItem, index) {
      this.clearSelected();
      if (normalItem.childs && normalItem.childs.length == 1 && normalItem.scheduleData == index) {
        normalItem.scheduleData = "";
        this.changeValue(normalItem, "", true);
      } else {
        normalItem.scheduleData = index;
        this.changeValue(normalItem);
      }
    },
    clearSelected() {
      for (let i = 0; i < this.templateDatas.length; i++) {
        if (this.templateDatas[i].children && this.templateDatas[i].children.length > 0) {
          for (let k = 0; k < this.templateDatas[i].children.length; k++) {
            for (let j = this.saveDatas.length - 1; j >= 0; j--) {
              if (this.saveDatas[j].interventionDetailID == this.templateDatas[i].children[k].interventionDetailID) {
                this.$set(this.templateDatas[i].children[k], "scheduleData", "");
                this.saveDatas.splice(j, 1);
              }
            }
          }
        } else {
          for (let j = this.saveDatas.length - 1; j >= 0; j--) {
            if (this.saveDatas[j].interventionDetailID == this.templateDatas[i].interventionDetailID) {
              this.$set(this.templateDatas[i], "scheduleData", "");
              this.saveDatas.splice(j, 1);
            }
          }
        }
      }
    },
    changeValue(item, items, flag) {
      // 单选互斥
      if (item.style.trim() == "R" && items) {
        if (item.scheduleData) {
          items.forEach((temp) => {
            if (temp.style.trim() == "R" && item.interventionDetailID != temp.interventionDetailID) {
              this.$set(temp, "scheduleData", "");
              if (this.saveDatas.length > 0) {
                for (let i = this.saveDatas.length - 1; i >= 0; i--) {
                  if (this.saveDatas[i].interventionDetailID == temp.interventionDetailID) {
                    this.saveDatas.splice(i, 1);
                    break;
                  }
                }
              }
            }
          });
        }
      }
      // 先去掉原来的
      if (this.saveDatas.length > 0) {
        for (let i = this.saveDatas.length - 1; i >= 0; i--) {
          if (this.saveDatas[i].interventionDetailID == item.interventionDetailID) {
            this.saveDatas.splice(i, 1);
            break;
          }
        }
      }
      if (flag) {
        return;
      }
      if (item.scheduleData && item.scheduleData.length > 0) {
        this.saveDatas.push(item);
      }
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    savePatientSchedule() {
      if (this.loading) {
        return;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      //拦截执行时间为空问题
      if (!this.scheduleDate || !this.scheduleTime) {
        this._showTip("warning", "请填写执行时间");
        return;
      }
      this.loading = true;
      this.loadingText = "保存中……";
      // 执行医嘱给药
      if (this.params.actionType == 5) {
        let params = this.getSaveData();
        PerformMedicine(params).then((result) => {
          this.loading = false;
          if (this._common.isSuccess(result)) {
            this._showTip("success", "执行成功！");
            this.close(true);
          }
        });
      } else {
        // 执行D类排程
        this.savePatientScheduleSingle();
      }
    },
    // 保存排程
    savePatientScheduleSingle() {
      let params = this.getSaveData();
      SavePatientScheduleSingle(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          if (result.data) {
            let triggerParams = {
              triggerDate: this._datetimeUtil.formatDate(result.data.triggerDate, "yyyy-MM-dd"),
              triggerTime: result.data.triggerTime,
              triggerDatas: result.data.attachedIntervention,
              index: Math.random(),
            };
            this.close(false, triggerParams);
          } else {
            this._showTip("success", "执行成功！");
            this.close(true);
          }
          if (params.InterventionID == 6437) {
            this.$store.commit("session/setPatientInfo", undefined);
          }
        }
      });
    },
    /**
     * description: 获取保存参数
     * 单病人批量监测画面调用，不可改名，不可删除
     * param {*}
     * return {*}
     */
    getSaveData() {
      let params = undefined;
      if (this.params.actionType == 5) {
        params = {
          groupID: this.params.patientScheduleMainID,
          performDate: this.scheduleDate,
          performTime: this.scheduleTime,
          bringToNursingRecords: this.bringToNursingRecords ? "1" : "0",
          bringToShift: this.bringToShift ? "1" : "0",
          performComment: this.performComment,
          orderCode: this.params.orderCode,
        };
      } else {
        let datas = [];
        for (let i = 0; i < this.saveDatas.length; i++) {
          let data = {
            assessListID: this.saveDatas[i].assessListID,
            interventionDetailID: this.saveDatas[i].interventionDetailID,
            scheduleData: this.saveDatas[i].scheduleData,
          };
          datas.push(data);
        }
        params = {
          inPatientID: this.params.inpatientID,
          patientAssessMainID: this.params.patientAssessMainID,
          patientInterventionID: this.params.patientInterventionID,
          bringToNursingRecords: this.bringToNursingRecords,
          bringToShift: this.bringToShift,
          patientScheduleMainID: this.params.patientScheduleMainID,
          performComment: this.performComment,
          saveData: datas,
          scheduleDate: this.scheduleDate,
          scheduleTime: this.scheduleTime,
          interventionID: this.params.interventionID,
          informPhysician: this.informPhysician,
        };
      }
      return params;
    },
    /**
     * description: 按钮点击回调事件，组装url并跳转
     * param {*} content data更新后的细项
     * return {*}
     */
    buttonClick(content) {
      this.buttonName = content.showName;
      this.buttonInterventionDetailID = content.interventionDetailID;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      this.showButtonDialog = true;
      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.patientInfo.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        "&isDialog=true" +
        `&patientScheduleMainID=${this.params.patientScheduleMainID}` +
        `&sourceID=${this.params.patientScheduleMainID}` +
        "&sourceType=Schedule";
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 对评估细项的值进行更新
     * param {*} assessListID 细项ID
     * return {*}
     */
    async updateButton() {
      let item = await this.getButtonValue();
      if (!item) {
        return;
      }
      let templateData = this.templateDatas.find(
        (templateData) =>
          templateData.interventionDetailID == this.buttonInterventionDetailID && templateData.style === "B"
      );
      /* 如果为空，尝试去子项里寻找
         此处不能使用foreach,因为无法跳出循环
         也不可使用双层find,内层find需要return才可将需要的对象返回，但这会导致新建一个实例而不是改变指针指向
      */
      if (!templateData) {
        for (let item of this.templateDatas) {
          if (!item.children) {
            continue;
          }
          templateData = item.children.find(
            (child) => child.interventionDetailID == this.buttonInterventionDetailID && child.style === "B"
          );
          if (templateData) {
            break;
          }
        }
        // 子项中也找不到，则直接返回
        if (!templateData) {
          return;
        }
      }
      if (templateData.children?.length && templateData.children.length > 0) {
        templateData.children.forEach((element) => {
          if (element.interventionDetailID == this.buttonInterventionDetailID && element.style === "B") {
            this.$set(element, "scheduleData", item.assessValue);
            this.$set(element, "linkForm", item.linkForm);
          }
        });
      }
      this.$set(templateData, "scheduleData", item.assessValue);
      this.$set(templateData, "linkForm", item.linkForm);
    },
    /**
     * description: 更新按钮回显数据
     * params {*}
     * return {*}
     * param {*} assessListID
     */
    async getButtonValue() {
      let item = undefined;
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        nursingInterventionDetailID: this.buttonInterventionDetailID,
        sourceID: this.params.patientScheduleMainID,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: 打开仪器数据弹窗
     */
    showClinicDialog() {
      //组装获取仪器数据所需params
      this.clinicPrams = {
        inpatientID: this.patientInfo.inpatientID,
        scheduleDate: this._datetimeUtil.formatDate(this.scheduleDate, "yyyy-MM-dd"),
        scheduleTime: this._datetimeUtil.formatDate(this.scheduleTime, "hh:mm:ss"),
        interventionID: this.params.interventionID,
      };
      this.showClinicFlag = true;
    },
    /**
     * description: 勾选仪器数据事件,回传仪器数据到排程画面
     * param {*} value
     */
    postClinicData(value) {
      for (let i = 0; i < this.templateDatas.length; i++) {
        this.templateDatas[i].scheduleData = value[this.templateDatas[i].assessListID]?.toString() ?? "";
      }
      this.showClinicFlag = false;
    },
  },
};
</script>
<style lang="scss">
.schedule-monitor {
  height: 100%;
  padding: 0 10px 5px 10px;
  box-sizing: border-box;
  .base-header {
    margin-bottom: 0;
    .header-item {
      display: inline-block;
      .label {
        margin-left: 10px;
      }
      .assess-date {
        width: 110px;
      }
      .assess-time {
        width: 75px;
      }
      .clinic-btn {
        float: right;
        margin-top: 8px;
      }
    }
  }
  .monitor-wrap {
    position: relative;
    padding: 10px;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    flex: auto;
    box-sizing: border-box;
    .el-table__header-wrapper {
      display: none;
    }
    .normal-item-wrap {
      width: auto;
      margin: 0 20px 10px 0;
      .normal-item.el-radio-button:focus:not(.is-focus):not(:active):not(.is-disabled) {
        box-shadow: none;
      }
    }
    .group {
      margin-left: 12px;
      color: $base-color;
    }
    .child-item {
      display: block;
    }
    .not-dialog .bring-checkbox {
      float: left;
      margin-top: 10px;
      margin-left: 5px;
    }
    .perform-comment {
      display: flex;
      width: 100%;
      padding: 5px;
      box-sizing: border-box;
      > span {
        white-space: nowrap;
      }
    }
  }
  .bring-checkbox {
    margin-right: 10px;
  }
}
.teaching.el-dialog .el-dialog__body {
  padding: 0;
}
.specific-care.el-dialog {
  background-color: #f3f3f3;
  iframe {
    height: 99%;
    border: none;
  }
}
</style>
