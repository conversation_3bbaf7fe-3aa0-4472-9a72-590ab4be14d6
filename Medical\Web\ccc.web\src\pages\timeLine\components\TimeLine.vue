<!--
 * FilePath     : \src\pages\timeLine\components\TimeLine.vue
 * Author       : 苏军志
 * Date         : 2020-05-25 16:05
 * LastEditors  : 苏军志
 * LastEditTime : 2020-08-28 16:27
 * Description  : 时间轴
-->
<template>
  <div class="time-line" ref="timeLine">
    <div
      class="temperature-control"
      ref="temperatureControl"
      documentbuffername="DCTemperatureDocument"
      :servicepageurl="timeLineServer"
      pixelpagespacing="20"
      width="100%"
      pageshadow="True"
      align="center"
      style="position: relative;"
    ></div>
  </div>
</template>

<script>
import { getTimeLineServer } from "@/utils/setting";
import BindingDCTemperatureControl from "./timeLine";
export default {
  props: {
    templateName: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      timeLineServer: "",
      temperatureControl: undefined,
      height: 750,
    };
  },
  created() {
    this.timeLineServer = getTimeLineServer();
  },
  mounted() {
    this.$emit("callback", this);
  },
  methods: {
    async init() {
      // 最小高度
      // 获取ref="timeLine"div的高度
      let tempHeight = this.$refs.timeLine.offsetHeight;
      if (tempHeight && tempHeight > 180) {
        this.height = this.$refs.timeLine.offsetHeight - 10;
      }
      if (this.height < 750) {
        this.height = 750;
      }
      // 获取时间轴组件实例
      this.temperatureControl = this.$refs.temperatureControl;
      if (this.temperatureControl != null) {
        // 设置时间轴高度
        this.temperatureControl.setAttribute("dc_height", this.height);
        // 减去时间轴组件横向滚动条的高度
        this.temperatureControl.setAttribute("contentpixelheight", this.height - 25);
        BindingDCTemperatureControl(this.temperatureControl);
        this.temperatureControl.Init();
        this.temperatureControl.FileNew();
        // Link单击事件
        this.temperatureControl.EventLinkClick = (linkStr) => {
          this.$emit("linkClick", linkStr);
        };
        this.temperatureControl.LoadDocument(this.templateName, false);
        // 设置注册码
        this.temperatureControl.SetRegisterCode(
          "04916679E4D8654042115693532071448947595BC128047A8E58634D7D88DE94CB00B6D44AE9932D248CD853813A3A76EC2274577B6859A7B769D44FF531A51BCF030F36F65129BF18688F082EECE31FB728053826B0D28CD5",
          false
        );
        // 设置为时间轴模式
        this.temperatureControl.SetDocumentViewMode("Timeline");
      }
    },
    refreshView() {
      // 刷新组件UI，注意: 对组件做任何操作都需要调用RefreshView后页面才会重新渲染
      this.temperatureControl.RefreshView();
    },
    // 动态添加Y轴图例
    // yAxislist:要添加的Y轴集合；Type:Array
    addYAxis(yAxislist) {
      if (!yAxislist || yAxislist.length <= 0) {
        return;
      }
      this.temperatureControl.LoadDocument(this.templateName, false);
      // 获取XML模板内容
      let templateStr = this.temperatureControl.SaveDocumentToFrontEndString();
      // 循环拼接要加入的节点
      let addAxisStr = "<YAxisInfos>";
      yAxislist.forEach((yAxis) => {
        let width = 100;
        if (this.height > 750) {
          if (yAxis.title.length > 10) {
            width = 130;
          }
        } else {
          if (yAxis.title.length > 11) {
            width = 170;
          } else if (yAxis.title.length > 8) {
            width = 130;
          }
        }
        addAxisStr +=
          "<YAxis Name='" +
          yAxis.name +
          "' SymbolSize='" +
          yAxis.symbolSize +
          "' SpecifyTitleWidth='" +
          width +
          "' AllowOutofRange='true' Title='" +
          yAxis.title +
          "' MaxValue='" +
          yAxis.maxValue +
          "' SymbolStyle='" +
          yAxis.symbolStyle +
          "' SymbolColorValue='" +
          yAxis.symbolColorValue +
          "' CharacterForCharSymbolStyle='82' CharacterForLanternSymbolStyle='82'> <DataSource />";
        if (yAxis.scales) {
          addAxisStr += yAxis.scales;
        } else {
          addAxisStr += "<Scales />";
        }
        addAxisStr += "</YAxis>";
      });
      templateStr = templateStr.replace("<YAxisInfos>", addAxisStr);
      // 重新设置时间轴组件的模板内容
      this.temperatureControl.LoadDocumentFromFrontEndString(templateStr);
      // 设置为时间轴模式
      this.temperatureControl.SetDocumentViewMode("Timeline");
    },
    // 设置属性类型的值
    setParameterValue(parameterDatas, parameter, value) {
      if (!parameterDatas || parameterDatas.length <= 0) {
        return;
      }
      let datas = [];
      parameterDatas.forEach((parameterData) => {
        datas.push({
          parameter: parameterData[parameter],
          value: parameterData[value],
        });
      });
      this.temperatureControl.SetParameterValue(datas);
    },
    // 设置折线点类型的值
    setPointValue(nodeName, time, value, link) {
      // nodeName, time, value必须要传
      if (!nodeName || !time || !value) {
        return;
      }
      let newTime = this._datetimeUtil.formatDate(time, "yyyy-MM-ddThh:mm:ss");
      let valuepoint = "<ValuePoint Time='" + newTime + "' Value='" + value + "'";
      if (link) {
        valuepoint += " Link='" + link + "'";
      }
      valuepoint += "></ValuePoint>";
      this.temperatureControl.AddValuePointByXml(nodeName, valuepoint, false);
    },
    // 设置文本类型的值
    setTextValue(nodeName, time, text, title, endTime, color, link) {
      // nodeName, time, text必须要传
      if (!nodeName || !time || !text) {
        return;
      }
      let newTime = this._datetimeUtil.formatDate(time, "yyyy-MM-ddThh:mm:ss");
      let valuepoint = "<ValuePoint Time='" + newTime + "' Text='" + text + "' Title='" + title + "'";
      if (endTime) {
        let newEndTime = this._datetimeUtil.formatDate(endTime, "yyyy-MM-ddThh:mm:ss");
        valuepoint += " EndTime='" + newEndTime + "'";
      }
      if (color) {
        valuepoint += " ColorValue='" + color + "'";
      }
      if (link) {
        valuepoint += " Link='" + link + "'";
      }
      valuepoint += "></ValuePoint>";
      this.temperatureControl.AddValuePointByXml(nodeName, valuepoint, false);
    },
    addValuePoints(points) {
      // points必须要传
      if (!points || points.length <= 0) {
        return;
      }
      this.temperatureControl.AddValuePoints(points, false);
    },
  },
};
</script>

<style lang="scss">
.time-line {
  height: 100%;
  box-sizing: border-box;
}
</style>
