<!--
 * FilePath     : \src\autoPages\recordSupplement\components\handover.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-17 15:08
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-17 15:08
 * Description  : 交班补录新旧功能切换页面
-->
<template>
  <div class="handover-supplement">
    <component :is="componentName" :patientInfo="patient" />
  </div>
</template>
<script>
import handoverSupplementBak from "@/pages/recordSupplement/handoverSupplement/handoverSupplementBak";
import handoverCommon from "@/autoPages/handover/components/handoverCommon";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { mapGetters } from "vuex";
export default {
  props: {
    patient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  components: {
    handoverSupplementBak,
    handoverCommon,
  },
  data() {
    return {
      componentName: "",
    };
  },
  async created() {
    const param = {
      settingTypeCode: "NewHandoverFlag",
    };
    const res = await GetSettingSwitchByTypeCode(param);
    this.componentName = res.data ? "handoverCommon" : "handoverSupplementBak"
  },
};
</script>
<style lang="scss">
.handover-supplement {
  height: 100%;
}
</style>