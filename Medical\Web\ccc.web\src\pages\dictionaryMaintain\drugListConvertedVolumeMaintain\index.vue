<!--
 * FilePath     : \ccc.web\src\pages\dictionaryMaintain\drugListConvertedVolumeMaintain\index.vue
 * Author       : 陈超然
 * Date         : 2025-03-03 16:25
 * LastEditors  : 陈超然
 * LastEditTime : 2025-03-24 17:38
 * Description  : 药品体积转换维护
 -->
 <template>
  <base-layout v-loading="loading" :element-loading-text="loadingText" class="question-main">
    <div slot="header">
      <el-input
        class="question-input"
        v-model="inputContext"
        placeholder="请输入药品简拼"
        @keyup.enter.native="search()"
      ></el-input>
      <el-button @click="search()" class="query-button" icon="iconfont icon-search">查询</el-button>
    </div>
    <el-table :data="drugList" border height="100%" ref="drugTable">
      <el-table-column
        prop="itemName"
        label="药品名称"
        header-align="center"
        min-width="130"
        align="left"
      ></el-table-column>
      <el-table-column prop="drugCode" label="药品编码" width="160" align="left"></el-table-column>
      <el-table-column
        prop="drugSpec"
        label="规格"
        header-align="center"
        min-width="80"
        align="center"
        :cell-style="{ textAlign: 'center' }"
      ></el-table-column>
      <el-table-column
        prop="units"
        label="单位"
        header-align="center"
        min-width="35"
        align="center"
        :cell-style="{ textAlign: 'center' }"
      ></el-table-column>
      <el-table-column
        prop="drugType"
        label="药品类型"
        header-align="center"
        min-width="50"
        align="center"
        :cell-style="{ textAlign: 'center' }"
      ></el-table-column>
      <el-table-column
        prop="drugForm"
        label="药品格式"
        header-align="center"
        min-width="50"
        align="center"
        :cell-style="{ textAlign: 'center' }"
      ></el-table-column>
      <el-table-column prop="doseUnits" label="剂量单位" width="80" align="center"></el-table-column>
      <el-table-column prop="dosePreUnit" label="对应的剂量(ml)" width="100" align="center"></el-table-column>
      <!-- 自定义渲染药品转换量列 -->
      <el-table-column label="药品转换量" width="160" align="center">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.convertedVolume"
            placeholder="请输入药品转换量"
            size="mini"
            class="center-input"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="modifyPerson" label="修改人" width="100" align="center"></el-table-column>
      <el-table-column prop="modifyDateTime" label="修改时间" width="150" align="center"></el-table-column>
      <el-table-column label="操作" width="70" header-align="center" align="center">
        <template slot-scope="scope">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="saveDrug(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>

<script>
import { GetDrugListView, UpdateDrug } from "@/api/DrugList";
import baseLayout from "@/components/BaseLayout";

export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      cloneDrugList: [],
      loading: false,
      loadingText: "",
      savaData: {
        id: "",
      },
      drugList: [], // 药品列表
      isAdd: false,
      // 搜索框输入内容
      inputContext: "",
    };
  },
  watch: {
    drugList(inputContext) {
      this.$nextTick(() => {
        this.scrollToRow();
      });
    },
  },
  mounted() {
    this.getAllDrugList();
  },
  methods: {
    // 查询所有的问题记录
    getAllDrugList() {
      this.loading = true;
      this.loadingText = "加载中……";
      GetDrugListView().then((res) => {
        if (this._common.isSuccess(res)) {
          this.loading = false;
          this.drugList = res.data;
          this.totalRecords = res.data.total; // 更新总记录数
        }
      });
    },
    // 模糊查询
    search() {
      this.drugList = this.drugList.filter((element) => {
        const regex = new RegExp(this.inputContext, "i"); // 'i' 表示不区分大小写
        if (regex.test(element.pinYinCode)) {
          return true;
        }
        return false;
      });
    },
    // 保存记录
    saveDrug(data) {
      this.loading = true;
      UpdateDrug(data).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "更新成功！");
          this.getAllDrugList();
        } else {
          this._showTip("warning", "更新失败！");
        }
      });
    },
    // 分页事件处理
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1;
      this.getAllDrugList();
    },
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.getAllDrugList();
    },
  },
};
</script>

<style lang="scss">
.question-main {
  .question-input {
    width: 200px;
  }
  .right-btn {
    float: right;
  }
  .el-table__body-wrapper {
    p {
      margin-block-start: 1px;
      margin-block-end: 1px;
    }
  }
  .el-dialog {
    height: auto;
  }
  .center-input .el-input__inner {
    text-align: center;
  }
}
</style>

