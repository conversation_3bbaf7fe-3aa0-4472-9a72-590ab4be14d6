<!--
 * FilePath     : \ccc.web\src\pages\dictionaryMaintain\tube\stationUseTube.vue
 * Author       : 李艳奇
 * Date         : 2020-10-15 15:29
 * LastEditors  : 陈超然
 * LastEditTime : 2025-04-13 14:26
 * Description  : 
-->
<template>
  <base-layout class="station-use-tube-main" v-loading="loading" element-loading-text="加载中……">
    <div slot="header">
      <el-select v-model="stationID" placeholder="请选择病区">
        <el-option
          v-for="station in stationList"
          :key="station.stationID"
          :label="station.stationName"
          :value="station.stationID"
        ></el-option>
      </el-select>
      <div class="top-btn">
        <el-button icon="iconfont icon-add" class="add-button" @click="getAllStationUnUseTube">新增</el-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="updateStationUseTube">保存</el-button>
      </div>
    </div>
    <!-- 指定病区内的导管列表 -->
    <el-table ref="tubeUseTable" :data="tubeList" class="tube-table-list" border stripe row-key="sort">
      <el-table-column type="index" label="序号" width="50" align="center">
        <template slot-scope="{ $index }">
          <div>
            {{ $index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="tubeShortName" label="导管名称" min-width="60" align="left"></el-table-column>
      <el-table-column prop="addDate" label="添加时间" min-width="60" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.addDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="50" align="left">
        <template slot-scope="scope">
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteStationUseTube(scope.row.stationUseTubeID)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 点击添加导管时，显示的导管列表 -->
    <el-dialog
      title="添加导管"
      :visible.sync="dialogFormVisible"
      v-dialogDrag
      :close-on-click-modal="false"
      @close="toCancel"
      custom-class="tube-list"
      width="750px"
      v-loading="loading"
      :element-loading-text="loadingText"
    >
      <el-checkbox-group class="check-box-list" v-model="checkList">
        <el-checkbox v-for="(tube, index) in stationUnUseTube" :key="index" class="tube-item" :label="tube.id">
          {{ tube.tubeName }}
        </el-checkbox>
      </el-checkbox-group>
      <div slot="footer">
        <el-button @click="toCancel">取消</el-button>
        <el-button type="primary" @click="saveStationUseTube">保 存</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import {
  GetTubeList, //根据指定病区所有的导管
  GetUnUseTubeList, //获取指定病区没有的导管
  DeleteStationUseTube, //删除指定的病区
  SaveStationUseTube, //维护指定病区对导管
  UpdateStationUseTube,
} from "@/api/StationUseTube";

import baseLayout from "@/components/BaseLayout";
import { GetEmployeeSwitchStationList } from "@/api/User";
import { mapGetters } from "vuex";

export default {
  components: { baseLayout },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      loading: false,
      loadingText: "",
      //导管列表
      tubeList: [],
      //病区ID
      stationID: undefined,
      //所有的病区
      stationList: [],
      //新增dialog弹出框
      dialogFormVisible: false,
      //复选框选中的内容
      checkList: [],
      //制定病区没有的导管集合
      stationUnUseTube: [],
    };
  },
  watch: {
    stationID: {
      handler(newVal) {
        if (newVal) {
          this.getAllStationUseTube(newVal);
        }
      },
      immediate: true,
    },
  },

  mounted() {
    this.getStationList();
    this.rowDrop();
  },

  methods: {
    /**
     * @description: 当点击取消的时候，关闭弹框清除勾选
     * @return
     */
    toCancel() {
      this.checkList = [];
      this.dialogFormVisible = false;
    },

    /**
     * @description: 更新病区导管排序
     * @return
     * @param index
     */
    updateStationUseTube() {
      if (this.loading) return;
      let sort = 0;
      this.tubeList.forEach((tube) => {
        sort++;
        tube.sort = sort;
      });
      this.loading = true;
      this.loadingText = "保存中……";
      UpdateStationUseTube(this.tubeList).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", result.message);
          this.getAllStationUseTube(this.stationID);
        }
      });
    },

    /**
     * @description: 添加病区对导管
     * @return
     */
    saveStationUseTube() {
      if (this.loading) return;

      //添加判断条件
      if (!this.stationID) {
        this.dialogFormVisible = false;
        this._showTip("warning", "操作失败！请选择病区");
        return;
      }
      if (this.checkList.length <= 0) {
        this._showTip("warning", "操作失败！请选择导管");
        return;
      }
      this.loading = true;
      this.loadingText = "保存中……";
      let params = {
        StationID: this.stationID,
        TubeListID: this.checkList,
      };
      SaveStationUseTube(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.checkList = [];
          this._showTip("success", result.message);
          this.dialogFormVisible = false;
          this.getAllStationUseTube(this.stationID);
        }
      });
    },

    /**
     * @description: 获取用户有权限的所有病区
     * @return
     */
    getStationList() {
      // 为避免和主框架里的请求相同被拦截到
      let params = {
        index: Math.random(),
      };
      GetEmployeeSwitchStationList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.stationList = result.data;
          this.stationID = this.user.stationID;
        }
      });
    },
    /**
     * @description: 获取指定病区目前还没有的导管
     * @return
     */
    getAllStationUnUseTube() {
      //判断用户是否已经选择了病区
      if (!this.stationID) {
        this.loading = false;
        this._showTip("warning", "请先选择病区！");
        return;
      }
      this.loading = true;
      this.loadingText = "加载中……";
      this.dialogFormVisible = true;
      let params = {
        stationID: this.stationID,
      };
      GetUnUseTubeList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.stationUnUseTube = result.data;
        }
      });
    },

    /**
     * @description: 获取指定病区的所有导管
     * @return
     * @param stationID
     */
    getAllStationUseTube(stationID) {
      // this.tubeList.length = [];
      this.loading = true;
      this.loadingText = "加载中……";
      let params = {
        stationID: stationID,
      };
      GetTubeList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.tubeList = result.data;
        }
      });
    },

    /**
     * @description: 删除指定的病区对导管记录
     * @return
     * @param stationUseTubeID
     */
    deleteStationUseTube(stationUseTubeID) {
      this._deleteConfirm("确认要删除此记录吗？", (flag) => {
        if (flag) {
          let params = {
            StationUseTubeID: stationUseTubeID,
          };
          DeleteStationUseTube(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this.checkList = [];
              this.getAllStationUseTube(this.stationID);
              this._showTip("success", result.message);
            }
          });
        }
      });
    },
    /**
     * description: 拖拽调用
     * param {*}
     * return {*}
     */
    rowDrop() {
      let bodyDom = this.$refs.tubeUseTable.$el.querySelector(".el-table__body-wrapper tbody");
      this._tableRowDrop(bodyDom, this, "tubeList");
    },
  },
};
</script>
<style lang="scss">
.station-use-tube-main {
  .top-btn {
    float: right;
  }

  .tube-list {
    .tube-item {
      width: 200px;
      margin-bottom: 10px;
    }
  }

  .tube-table-list {
    .el-table__row {
      cursor: pointer;
    }
  }

  .check-box-list {
    height: 100%;
    overflow-y: scroll;
  }
}
</style>