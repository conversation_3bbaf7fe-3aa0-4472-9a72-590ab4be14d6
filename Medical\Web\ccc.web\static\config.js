/*
 * FilePath     : \static\config.js
 * Author       : 苏军志
 * Date         : 2020-05-28 02:48
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-04-26 11:19
 * Description  :
 */

var apiConfig = {
  // 床位组件宽度，不同医院设置不同的宽度
  bedNumberWidth: 70,
  // 运行环境
  // RunEnv: "pro",
  // RunEnv: "test",
  RunEnv: "local",

  // pro环境
  config: {
    medicalApiUrl: "http://*************:82/api",
    externalApiUrl: "http://*************:81/api",
    webProxyApiUrl: "http://127.0.0.1:8655/api",
    timeLineServer: "http://localhost:52909/ServicePage.aspx",
    managementUrl: "http://localhost:8086/",
    hisCommonUrl: "http://localhost:8080/",
    oldNursingUrl: "http://localhost:8888/",
    statisticsUrl: "http://localhost:8886/",
    statisticsV3Url: "http://localhost:8086/",
    nursingManagementUrl: "http://localhost:6066",
    MQSetting: {
      isOpen: false,
      url: "ws://www.honlivit.com:15674/ws",
      user: "ccc",
      password: "hlyy",
      serviceUrl: "http://*************:5000/api",
    },
    // 是否教育版本
    educationEdition: false,
    //加载惠每jdn,默认false
    huiMei: {
      isOpen: false,
      url: "http://************/cdss/jssdk?v=4.0&ak=95895F1F60E20206D900B24097620093",
      hospitalGuid: "01",
      hospitalName: "复旦大学附属中山医院",
      autherKey: "95895F1F60E20206D900B24097620093",
    },
  },
  // test环境
  testConfig: {
    medicalApiUrl: "http://**************:8090/api",
    externalApiUrl: "http://**************:8091/api",
    webProxyApiUrl: "http://127.0.0.1:8655/api",
    timeLineServer: "http://localhost:52909/ServicePage.aspx",
    managementUrl: "http://localhost:8086/",
    hisCommonUrl: "http://localhost:8080/",
    oldNursingUrl: "http://localhost:8888/",
    statisticsUrl: "http://localhost:8886/",
    statisticsV3Url: "http://localhost:8086/",
    nursingManagementUrl: "http://localhost:6066",
    MQSetting: {
      isOpen: false,
      url: "ws://www.honlivit.com:15674/ws",
      user: "ccc",
      password: "hlyy",
      serviceUrl: "http://localhost:5000/api",
    },
    // 是否教育版本
    educationEdition: false,
    //加载惠每jdn,默认false
    huiMei: {
      isOpen: false,
      url: "http://************/cdss/jssdk?v=4.0&ak=95895F1F60E20206D900B24097620093",
      hospitalGuid: "01",
      hospitalName: "复旦大学附属中山医院",
      autherKey: "95895F1F60E20206D900B24097620093",
    },
  },
  // local环境
  localConfig: {
    medicalApiUrl: "http://localhost:56194/api",
    externalApiUrl: "http://localhost:62860/api",
    webProxyApiUrl: "http://127.0.0.1:8655/api",
    timeLineServer: "http://localhost:52909/ServicePage.aspx",
    managementUrl: "http://localhost:8086/",
    hisCommonUrl: "http://localhost:8888/",
    oldNursingUrl: "http://localhost:8086/",
    statisticsUrl: "http://localhost:8886/",
    nursingBoard: "http://localhost:8068/#/",
    statisticsV3Url: "http://localhost:8086/",
    nursingManagementUrl: "http://localhost:6066",
    // TODO：暂时使用182冲刺删除
    cellReportUrl: "http://localhost:5000/",
    mqSetting: {
      isOpen: false,
      url: "ws://**************:15674/ws",
      user: "ccc",
      password: "hlyy",
      serviceUrl: "http://localhost:5000/api",
    },
    // 是否教学版本
    educationEdition: false,
    //加载惠每jdn,默认false
    huiMei: {
      isOpen: false,
      hospitalGuid: "01",
      url: "http://************/cdss/jssdk?v=4.0&ak=95895F1F60E20206D900B24097620093",
      hospitalName: "复旦大学附属中山医院",
      autherKey: "95895F1F60E20206D900B24097620093",
      // 宏力部分
      // "http://************/cdss/jssdk?v=4.0&ak=DA12FB821147938CA09641D3B51365C5",
      // hospitalName: "河南宏力医院",
      // autherKey: "DA12FB821147938CA09641D3B51365C5"
    },
  },
};
