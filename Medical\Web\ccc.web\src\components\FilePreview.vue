<!--
 * FilePath     : \src\components\FilePreview.vue
 * Author       : 苏军志
 * Date         : 2022-04-15 10:08
 * LastEditors  : 苏军志
 * LastEditTime : 2022-09-08 19:33
 * Description  : 文件预览组件，目前支持pdf、mp3、mp4
 * CodeIterationRecord: 
-->
<template>
  <div class="file-preview">
    <div class="file-directory" v-if="!isSingleFile">
      <div class="title">文件列表</div>
      <div
        v-for="(file, index) in fileDatas"
        :key="index"
        @click="showFile(file, index)"
        :class="['file-item', { selected: selectFileIndex == index }]"
      >
        {{ file.fileName }}
      </div>
    </div>
    <div class="preview-wrap">
      <div v-if="fileType == 'pdf'" :class="['pdf', 'preview', { mobile: !isPC }]" ref="preview">
        <iframe
          v-if="isPC"
          :src="src"
          type="application/x-google-chrome-pdf"
          width="100%"
          height="100%"
          frameborder="0"
          scrolling="auto"
        />
        <pdf
          v-else
          :src="src"
          :page="currentPage"
          @num-pages="pageCount = $event"
          @page-loaded="currentPage = $event"
        ></pdf>
      </div>
      <div v-else-if="fileType == 'mp3'" class="mp3 preview">
        <audio :src="src" autoplay="autoplay" controls="controls">浏览器不支持播放mp3</audio>
      </div>
      <div v-else-if="fileType == 'mp4'" class="mp4 preview">
        <video width="100%" height="100%" :src="src" autoplay="autoplay" controls="controls">浏览器不支持播放mp4</video>
      </div>
      <div v-else class="other preview">
        <div v-if="src" class="tip">系统不支持的文件类型，目前仅支持MP3、MP4、PDF格式的文件</div>
        <div v-else class="tip">文件加载中……</div>
      </div>
      <el-pagination
        class="pdf-pagination"
        v-if="fileType == 'pdf' && !isPC"
        background
        layout="prev, pager, next"
        :page-size="1"
        :total="pageCount"
        @current-change="changePage"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import pdf from "vue-pdf";
export default {
  components: { pdf },
  props: {
    datas: {
      type: Array,
      require: true,
    },
    defaultFileIndex: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    datas: {
      immediate: true,
      handler(newValue) {
        if (newValue && newValue.length > 0) {
          this.init();
        }
      },
    },
    defaultFileIndex: {
      immediate: true,
      handler(newValue) {
        this.init();
      },
    },
  },
  data() {
    return {
      // 文件类型
      fileType: "",
      // 当前运行环境是否PC端
      isPC: true,
      // 是否为单文件，单文件不显示文件列表
      isSingleFile: true,
      // 当前选中文件的地址
      src: undefined,
      fileDatas: undefined,
      // 当前选中文件序号
      selectFileIndex: 0,
      // 移动端显示PDF时计算页数
      pageCount: undefined,
      // 移动端显示PDF时当前页
      currentPage: undefined,
    };
  },
  methods: {
    init() {
      this.isPC = this._common.isPC();
      // 处理数据
      this.setFileDatas();
      if (this.fileDatas) {
        this.showFile(this.fileDatas[this.selectFileIndex], this.defaultFileIndex);
        if (this.fileDatas.length == 1) {
          this.isSingleFile = true;
        } else {
          this.isSingleFile = false;
        }
      }
    },
    setFileDatas() {
      this.fileDatas = [];
      this.datas.forEach((src) => {
        let fileType = "";
        //获取文件后缀，取得文件类型
        let endPos = src.lastIndexOf(".");
        if (endPos != -1) {
          fileType = src.substring(endPos + 1);
          let types = ["pdf", "mp3", "mp4"];
          if (types.indexOf(fileType) == -1) {
            fileType = "other";
          }
        }
        let startPos = src.lastIndexOf("/");
        let fileName = src.substring(startPos + 1, endPos);
        if (fileName.indexOf("_") != -1) {
          fileName = fileName.substring(fileName.indexOf("_") + 1);
        }
        let file = {
          fileName: fileName,
          fileType: fileType,
          fileSrc: src,
        };
        this.fileDatas.push(file);
      });
    },
    showFile(row, index) {
      this.selectFileIndex = index;
      this.src = row.fileSrc;
      this.fileType = row.fileType;
    },
    changePage(page) {
      this.currentPage = page;
      this.$nextTick(() => {
        if (this.$refs.preview) {
          this.$refs.preview.scrollTop = 0;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.file-preview {
  height: 100%;
  width: 100%;
  min-height: 100px;
  min-width: 100px;
  display: flex;
  .file-directory {
    width: 180px;
    .title {
      width: 100%;
      font-size: 15px;
      font-weight: bold;
      text-align: center;
      padding: 5px 0;
      border-bottom: 1px solid #cccccc;
    }
    .file-item {
      font-size: 14px;
      padding: 5px;
      border-bottom: 1px solid #cccccc;
      cursor: pointer;
      &.selected {
        background-color: #ffe0ac;
        font-weight: bold;
        font-size: 13px;
      }
    }
  }
  .preview-wrap {
    flex: auto;
    height: 100%;
    width: 100%;
    background-color: #525659;
    text-align: center;
    .preview {
      overflow: auto;
      height: 100%;
      width: 100%;
      text-align: center;
      padding: 10px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      &.pdf.mobile {
        display: block;
        height: calc(100% - 95px);
      }
      &.other {
        .tip {
          font-size: 40px;
          color: #ffffff;
        }
      }
    }
    .pdf-pagination {
      .el-icon {
        font-size: 36px;
      }
      button,
      .number,
      .el-icon-more {
        font-size: 40px;
        min-width: 60px;
        height: 60px;
        line-height: 60px;
        margin: 15px;
      }
    }
  }
}
</style>