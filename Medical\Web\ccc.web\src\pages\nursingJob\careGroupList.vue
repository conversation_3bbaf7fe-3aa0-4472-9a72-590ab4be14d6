<!--
 * FilePath     : \src\pages\nursingJob\careGroupList.vue
 * Author       : xml
 * Date         : 2020-10-15 15:29
 * LastEditors  : xml
 * LastEditTime : 2021-08-05 21:46
 * Description  : 
-->
<template>
  <base-layout class="caregroup-list-main">
    <div slot="header">
      <div class="top-btn">
        <el-button icon="iconfont icon-add" class="add-button" @click="insertCareGroupDialog()">新增</el-button>
      </div>
    </div>
    <!-- 指定病区的分组列表 -->
    <div v-loading="loading" element-loading-text="加载中……" class="caregroup-list">
      <el-table border stripe height="100%" :data="tableDataList" highlight-current-row>
        <el-table-column prop="careGroupID" label="分组编码" v-if="false"></el-table-column>
        <el-table-column prop="careGroupName" label="分组名称"></el-table-column>
        <el-table-column prop="modifyDateTime" label="维护时间">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="employeeName" label="新增人员"></el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <div>
              <el-tooltip content="修改">
                <i @click="modifyCareGroupDialog(scope.row)" class="iconfont icon-edit"></i>
              </el-tooltip>
              <el-tooltip content="删除">
                <i class="iconfont icon-del" @click="deleteCareGroupListInfo(scope.row.careGroupID)"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      class="new-caregroup-dialog"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
    >
      <div class="dialog-content">
        <label>分组名称:</label>
        <el-input class="dialog-form" v-model="careGroupName" placeholder="请输入名称"></el-input>
      </div>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveButtonEvent">保存</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import {
  GetCareGroupList, //根据指定病区所有的分组
  SaveCareGroupListInfo, //保存分组
} from "@/api/DepartmentJob";

import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";

export default {
  components: { baseLayout },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      loading: false,
      careGroupID: 0,
      dialogTitle: undefined,
      dialogVisible: false, //新增dialog弹出框
      loadingText: undefined,
      stationID: undefined, //病区ID
      careGroupName: undefined, //分组名称
      tableDataList: [],
    };
  },
  watch: {},

  created() {},
  mounted() {
    this.getCareGroupListData();
  },
  methods: {
    //查询
    getCareGroupListData() {
      let params = {
        stationID: this.user.stationID,
      };
      GetCareGroupList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tableDataList = res.data;
        }
      });
    },
    // 新增弹出窗口
    insertCareGroupDialog() {
      this.dialogTitle = "新增";
      this.dialogVisible = true;
    },
    modifyCareGroupDialog(row) {
      this.careGroupID = row.careGroupID;
      this.careGroupName = row.careGroupName;
      this.dialogTitle = "修改";
      this.dialogVisible = true;
    },
    //保存按钮事件
    saveButtonEvent() {
      if (this.dialogTitle == "新增") {
        this.addOneData();
      } else {
        this.MondifyOneData();
      }
    },

    //新增参数
    addOneData() {
      let params = {
        careGroupName: this.careGroupName,
        careGroupID: 0,
        deleteFlag: "",
        stationID: this.user.stationID,
        employeeID: this.user.userID,
      };
      this.saveCareGroupListInfo(params);
    },
    //修改参数
    MondifyOneData() {
      let params = {
        careGroupName: this.careGroupName,
        careGroupID: this.careGroupID,
        deleteFlag: "",
        stationID: this.user.stationID,
        employeeID: this.user.userID,
      };
      this.saveCareGroupListInfo(params);
    },
    //删除参数
    deleteCareGroupListInfo(delCareGroupID) {
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          let params = {
            careGroupID: delCareGroupID,
            deleteFlag: "*",
            stationID: this.user.stationID,
          };
          this.saveCareGroupListInfo(params);
        }
      });
    },
    //提交数据库
    saveCareGroupListInfo(params) {
      return SaveCareGroupListInfo(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.careGroupName = "";
          this.careGroupID = 0;
          this.dialogVisible = false;
          this.getCareGroupListData();
        }
      });
    },
  },
};
</script>
<style lang="scss">
.caregroup-list-main {
  .top-btn {
    float: right;
  }
  .caregroup-list {
    height: 100%;
    font-size: 13px;
    .el-table .cell {
      font-size: 14px;
    }
  }
  .new-caregroup-dialog {
    .el-dialog {
      width: 550px;
      height: 200px;
      .dialog-content {
        text-align: center;
        .el-input {
          width: 280px;
        }
      }
    }
  }
}
</style>