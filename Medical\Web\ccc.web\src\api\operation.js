/*
 * FilePath     : \projectManagement.webe:\CCC3.1\Medical\Web\ccc.web\src\api\operation.js
 * Author       : 马超
 * Date         : 2023-04-05 16:05
 * LastEditors  : 马超
 * LastEditTime : 2023-04-05 16:07
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
const baseUrl = "/operation";

export const urls = {
  GetVitalSignByInpatientID: baseUrl + "/GetVitalSignByInpatientID"
};
// 根据患者ID获取生命体征数据
export const GetVitalSignByInpatientID = params => {
  return http.get(urls.GetVitalSignByInpatientID, params);
};
