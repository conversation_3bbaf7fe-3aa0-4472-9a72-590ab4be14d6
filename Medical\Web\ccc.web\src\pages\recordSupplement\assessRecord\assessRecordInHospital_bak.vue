<!--
 * FilePath     : \src\pages\recordSupplement\assessRecord\assessRecordInHospital_bak.vue
 * Author       : 李青原
 * Date         : 2020-07-16 17:21
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-16 08:59
 * Description  : 
 * CodeIterationRecord: 
-->
<template>
  <base-layout
    class="assess-supplement"
    v-loading="loading"
    :element-loading-text="loadingText"
    header-height="auto"
    :showHeader="false"
  >
    <div class="assess-supplement-content-top" v-if="tableTopBoole">
      <el-button class="add-button" @click="addOrUpdate('add')" icon="iconfont icon-add">新增</el-button>
    </div>
    <div class="assess-supplement-content-table">
      <el-table v-if="assessRecordList.length > 0" :data="assessRecordList" height="100%" border>
        <el-table-column prop="deptName" label="科室" min-width="180" header-align="center"></el-table-column>
        <el-table-column prop="typeDes" label="名称" min-width="200" header-align="center">
          <template slot-scope="scope">
            <el-tooltip content="补录">
              <i :class="['iconfont', 'icon-info', { 'assess-show': !scope.row.additional }]"></i>
            </el-tooltip>
            {{ scope.row.typeDes }}
          </template>
        </el-table-column>
        <el-table-column prop="nursingLevelDescription" label="护理级别" width="80" align="center"></el-table-column>
        <el-table-column prop="sort" label="次数" width="50" align="center"></el-table-column>
        <el-table-column prop="addEmployeeName" label="评估人" min-width="70" align="center"></el-table-column>
        <el-table-column label="时间" min-width="155" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessDate, type: 'date' }"></span>
            <span v-formatTime="{ value: scope.row.assessTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" header-align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改明细">
              <i v-if="scope.row.additional" class="iconfont icon-edit" @click="addOrUpdate('update', scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i v-if="scope.row.additional" class="iconfont icon-del" @click="deleteAssess(scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      class="dialog"
      v-dialogDrag
      :close-on-click-modal="false"
      :title="titleText"
      :visible.sync="templateVisible"
      v-if="templateVisible"
    >
      <div class="dialog-top">
        <station-department-bed
          class="assess-patient-view"
          @selectStationDepartmentBed="selectStationDepartmentBed"
          :stDeptBed="stationDepartmentBed"
        ></station-department-bed>
        <div class="form-bottom">
          <label>日期</label>
          <el-date-picker
            v-model="formData.addDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="评估日期"
            style="width: 120px"
          ></el-date-picker>
          <label>时间</label>
          <el-time-picker
            type="date"
            value-format="HH:mm:ss"
            v-model="formData.addTime"
            placeholder="评估时间"
            style="width: 100px"
          ></el-time-picker>
          <label>护理级别</label>
          <el-select style="width: 100px" v-model="formData.nursingLevel" placeholder="请选择">
            <el-option
              v-for="item in nursingLevelList"
              :key="item.id"
              :label="item.description"
              :value="item.typeValue"
            ></el-option>
          </el-select>
          <label>评估次数</label>
          <el-input
            style="width: 78px"
            class="picker"
            v-model.number="formData.sort"
            @keyup.enter.native="getAssessDetailView(inpatientInfo, 'add')"
            @blur="getAssessDetailView(inpatientInfo, 'add')"
            :disabled="sortFlag"
          ></el-input>
        </div>
      </div>
      <div class="assessView" v-loading="assessViewFlag" :element-loading-text="assessViewText">
        <tabs-layout
          :template-list="templateDatas"
          @button-click="buttonClick"
          @change-values="changeValues"
          @checkTN="checkTN"
        />
      </div>
      <div slot="footer">
        <el-button @click="templateVisible = false">取消</el-button>
        <el-button type="primary" @click="assessSave">保存</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import shiftSelector from "@/components/selector/shiftSelector";
import tabsLayout from "@/components/tabsLayout/index.vue";
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import stationDepartmentBed from "@/pages/recordSupplement/components/stationDepartmentBed.vue";
import { GetScheduleTop } from "@/api/Setting";
import { mapGetters } from "vuex";
import {
  GetAssessRecordsCodeByDeptID,
  GetAssessView,
  SaveAssessSupplement,
  DelRecordItemByID,
  GetAdditionalAssessMainInfo,
  GetPatientIsTransOut,
} from "@/api/Assess";
export default {
  components: { tabsLayout, baseLayout, stationSelector, shiftSelector, stationDepartmentBed },
  data() {
    return {
      chartNo: undefined,
      //表格头部开关
      tableTopBoole: true,
      //暂存病区科室及床位
      stationDepartmentBed: {
        stationID: "",
        departmentListID: "",
        bedNumber: "",
        bedId: "",
      },
      //选中评估信息
      currentAssess: {},
      //评估列表信息
      assessRecordList: [],
      //评估模板数据
      templateDatas: [],
      //新增或修改弹窗数据
      templateVisible: false,
      titleText: "评估详细",
      loading: false,
      loadingText: "加载中……",
      //评估明细数据 修改评估时使用
      assessDatas: [],
      checkTNFlag: true,
      recordsInfo: {},
      nursingLevelList: [],
      formData: {
        addDate: null,
        addTime: null,
        sort: undefined,
        nursingLevel: "",
      },
      //评估序号范围
      sortMin: 0,
      sortMax: "",
      maxSort: "",
      //评估模板loading
      assessViewFlag: false,
      assessViewText: "",
      //评估次数禁用flag
      sortFlag: false,
      //是否转科 是否有入院评估
      transOutData: undefined,
      //不符合补录条件提示内容
      supplementaryShow: "",
    };
  },
  computed: {
    ...mapGetters({
      inpatientInfo: "getPatientInfo",
      user: "getUser",
    }),
  },
  created() {
    this.init();
    this.getList();
  },
  watch: {
    "inpatientInfo.inpatientID": {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        this.init();
        this.getList();
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 获取护理等级列表
     * return {*}
     */
    init() {
      if (this.inpatientInfo) {
        this.chartNo = this.inpatientInfo.chartNo;
      }
      this.getNursingLevelList();
    },
    //获取评估列表
    initData(patient) {
      if (!patient) {
        return;
      }
      this.tableTopBoole = true;
      this.getList(patient);
    },
    /**
     * description: 获取评估列表
     * return {*}
     */
    getList() {
      let param = {
        inpatientID: this.inpatientInfo.inpatientID,
      };
      GetAdditionalAssessMainInfo(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.length == 0) {
            this._showTip("warning", "未查到护理评估补录记录。");
          }
          this.assessRecordList = response.data;
          //获取次数补录范围
        }
      });
      //获取列表是 获取病人是否有转科
      this.getPatientIsTransOut();
    },
    /**
     * description: 重新搜索病人 质控页面数据
     * return {*}
     */
    change() {
      this.assessRecordList = [];
      this.tableTopBoole = false;
    },
    //添加或修改病人评估
    async addOrUpdate(flag, inpatient = this.inpatientInfo) {
      //置空模板数据
      this.templateDatas = [];
      this.recordsInfo = {};
      this.sortFlag = false;
      //顶部数据获取
      this.stationDepartmentBed.bedNumber = inpatient.bedNumber;
      this.stationDepartmentBed.departmentListID = inpatient.departmentListID;
      this.stationDepartmentBed.stationID = Number(inpatient.stationID);
      this.stationDepartmentBed.bedId = inpatient.bedID;
      this.formData.sort = "";
      //检核是否符合补录条件
      if (!this.getSortMinAndMax()) {
        this._showTip("warning", this.supplementaryShow + "!不符合评估补录条件");
        return;
      }
      //评估新增
      if (flag == "add") {
        this.titleText = "新增评估补录";
        this.formData.addDate = this._datetimeUtil.getNowDate();
        this.formData.addTime = this._datetimeUtil.getNowTime();
        this.formData.nursingLevel = this.inpatientInfo.nursingLevel;
      }
      //评估修改
      if (flag == "update") {
        //修改评估禁止修改次数
        this.sortFlag = true;
        let assessType = inpatient.additional ? "补录" : "正常";
        this.titleText = assessType + "评估修改";
        this.formData.addDate = inpatient.assessDate;
        this.formData.addTime = inpatient.assessTime;
        this.formData.nursingLevel = inpatient.nursingLevel;
        this.formData.sort = inpatient.sort;
      }
      //保存选中评估信息 如果为新增则为currentPatient病人信息
      this.currentAssess = inpatient;
      this.templateVisible = true;
      this.getAssessDetailView(inpatient, flag);
    },
    /**
     * description: 获取评估模板
     * param {*} value
     * param {*} flag
     * return {*}
     */
    async getAssessDetailView(value, flag) {
      //新增评估时提醒输入评估次数
      if (!this.formData.sort) {
        this.$nextTick(() => {
          if (this.formData.sort != "") {
            this._showTip("warning", "请输入【评估次数】获取评估模板数据");
          }
        });
        return;
      }
      //检核是否符合补录条件
      if (!this.getSortMinAndMax()) {
        this._showTip("warning", this.supplementaryShow + "!不符合评估补录条件");
        return;
      }
      //检核评估次数是否符合
      if (this.formData.sort < this.sortMin || (this.sortMax && this.formData.sort > this.sortMax)) {
        this._showTip("warning", "评估次数不符合补录条件");
        return;
      }
      this.assessViewFlag = true;
      this.assessViewText = "评估模板加载中...";
      let param = {
        age: this.inpatientInfo.age,
        inpatientID: value.inpatientID,
        gender: this.inpatientInfo.genderCode,
        departmentListID: this.stationDepartmentBed.departmentListID,
        stationID: this.stationDepartmentBed.stationID,
        dateOfBirth: this.inpatientInfo.dateOfBirth,
      };
      //新增评估获取模板数据传送数据整理
      if (flag == "add") {
        let params = {
          age: this.inpatientInfo.age,
          mappingType: this.formData.sort == 1 ? "AdmissionAssess" : "PhysicalAssess",
          inpatientID: value.inpatientID,
          departmentListID: this.stationDepartmentBed.departmentListID,
        };
        await GetAssessRecordsCodeByDeptID(params).then((response) => {
          if (this._common.isSuccess(response)) {
            this.recordsInfo = response.data;
          }
        });
        param.recordsCode = this.recordsInfo.recordsCode;
      }
      //修改评估获取模板传送数据处理
      if (flag == "update") {
        param.recordsCode = value.recordsCode;
        param.mainID = value.id;
      }
      await GetAssessView(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.templateDatas = response.data;
        }
      });
      this.assessViewFlag = false;
    },
    /**
     * description: 新增或修改保存评估
     * return {*}
     */
    assessSave() {
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      //组装保存数据
      let assessDetail = this.getAssessSaveData();
      if (!assessDetail || assessDetail.length == 0) {
        return;
      }
      this.assessViewFlag = true;
      this.assessViewText = "保存中...";
      let assessData = {
        interventionMainID: this.recordsInfo.interventionMainID,
        details: assessDetail,
      };
      let mainData = this.currentAssess;
      let main = {
        id: mainData.id,
        inpatientID: mainData.inpatientID,
        patientID: mainData.patientID,
        caseNumber: mainData.caseNumber,
        chartNo: mainData.chartNo,
        sort: this.formData.sort,
        stationID: this.stationDepartmentBed.stationID,
        departmentListID: this.stationDepartmentBed.departmentListID,
        nursingLevel: this.formData.nursingLevel,
        recordsCode: this.recordsInfo.recordsCode || mainData.recordsCode,
        assessDate: this.formData.addDate,
        assessTime: this.formData.addTime,
        bedID: this.stationDepartmentBed.bedId,
        bedNumber: this.stationDepartmentBed.bedNumber,
        tempSaveMark: "S",
        deleteFlag: "",
        emrFlag: null,
      };
      //获取numberOfAssessment
      main.numberOfAssessment = this.assessRecordList.length ? this.getNumberOfAssessment(mainData) : 1;
      let mainArr = [];
      mainArr.push(main);
      assessData.mains = mainArr;
      SaveAssessSupplement(assessData).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data) {
            this._showTip("success", "保存成功！");
            this.initData(this.inpatientInfo);
          } else {
            this._showTip("error", "保存失败！");
          }
        }
        this.assessViewFlag = false;
        this.templateVisible = false;
      });
    },
    /**
     * description: 删除评估数据
     * param {*} row
     * return {*}
     */
    deleteAssess(row) {
      this._deleteConfirm("", (flag) => {
        let _this = this;
        if (flag) {
          this.loading = true;
          this.loadingText = "删除中...";
          let params = {
            ID: row.id,
          };
          DelRecordItemByID(params).then((response) => {
            if (this._common.isSuccess(response)) {
              this._showTip("success", "删除成功！");
              this.getList(this.curr);
            }
            this.loading = false;
          });
        }
      });
    },
    /**
     * description: 获取护理级别列表
     * return {*}
     */
    getNursingLevelList() {
      let param = { settingTypeCode: "NursingLevel" };
      GetScheduleTop(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.nursingLevelList = response.data;
        }
      });
    },
    /**
     * description: 弹窗顶部组件数据
     * param {*} val
     * return {*}
     */
    selectStationDepartmentBed(val) {
      this.stationDepartmentBed.bedNumber = val.bedNumber;
      this.stationDepartmentBed.departmentListID = val.departmentListID;
      this.stationDepartmentBed.stationID = Number(val.stationID);
      this.stationDepartmentBed.bedId = val.bedId;
    },
    /**
     * description: 评估明细的中按钮点击
     * param {*} content
     * return {*}
     */
    buttonClick(content) {
      this._showTip("warning", "评估补录机制无法维护专项护理。");
    },
    /**
     * description:评估组件返回评估明细数据
     * param {*} datas
     * return {*}
     */
    changeValues(datas) {
      this.assessDatas = datas;
    },
    /**
     * description: 检核传参
     * param {*} flag
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 获取评估补录次数范围
     * return {*}
     */
    getSortMinAndMax() {
      this.sortFlag = false;
      this.sortMin = undefined;
      this.sortMax = undefined;
      let successFlag = true;
      //本科室评估的次数
      let assessListLength = 0;
      if (this.inpatientInfo.refillFlag) {
        if (!this.transOutData.admissionFlag) {
          this.formData.sort = 1;
          this.sortFlag = true;
        } else {
          this.sortMin = 2;
        }
      } else {
        if (this.transOutData.transOutFlag) {
          if (this.user.stationID == this.transOutData.transOutData.stationID) {
            assessListLength = this.assessRecordList.filter(
              (item) => item.departmentListID == this.transOutData.transOutDepartmentListID
            ).length;
            if (!this.transOutData.admissionFlag) {
              this.formData.sort = 1;
              this.sortFlag = true;
            } else {
              this.sortMin = 2;
              this.sortMax = assessListLength + 1;
            }
          } else {
            successFlag = false;
            this.supplementaryShow = "您所属病区不是患者转出病区";
          }
        } else {
          successFlag = false;
          this.supplementaryShow = "该病人没有发生转出患者事件";
        }
      }
      return successFlag;
    },
    /**
     * description: 获取弹窗标题
     * param {*} patientInfo
     * param {*} detail
     * return {*}
     */
    getDialogTitle(patientInfo, detail) {
      return `${patientInfo.patientName}【${patientInfo.gender}-${patientInfo.age}】--${detail}`;
    },
    /**
     * description: 获取NumberOfAssessment
     * param {*} value
     * return {*}
     */
    getNumberOfAssessment(value) {
      let number =
        Math.max(
          ...this.assessRecordList.map((item) => {
            return item.numberOfAssessment;
          })
        ) + 1;
      return (number = value.id ? value.numberOfAssessment : number);
    },
    /**
     * description: 获取需要保存的明细
     * return {*}
     */
    getAssessSaveData() {
      // 如果不是暂存
      for (let i = 0; i < this.templateDatas.length; i++) {
        let title = this.templateDatas[i];
        for (let j = 0; j < title.groups.length; j++) {
          let group = title.groups[j];
          let selectItem = this.assessDatas.find((data) => {
            return data.groupID.trim() == group.groupID.trim();
          });
          // 没查到，则不让保存
          if (!selectItem) {
            // 如果已经隐藏 不检核
            if (group.hidden) {
              continue;
            }
            // 如果没有明细，直接跳过
            if (group.contents.length <= 0) {
              continue;
            }
            // 判断是否只有按钮
            let buttons = group.contents.filter((content) => {
              return content.controlerType.trim() == "B";
            });
            if (buttons && buttons.length > 0) {
              // 该分组只有按钮，不提醒
              if (buttons.length == group.contents.length) {
                continue;
              }
              buttons = buttons.filter((content) => {
                return content.assessValue && content.assessValue.length > 0;
              });
              if (buttons && buttons.length > 0) {
                continue;
              }
            }
            this._showTip("warning", "【" + title.title + "-" + group.itemName.trim() + "】项目未评估！");
            this.titleIndex = i;
            this.loading = false;
            return undefined;
          }
        }
      }
      let details = [];
      let flag = true;
      this.assessDatas.forEach((content) => {
        let result = this._common.checkAssessTN(content);
        if (!result.flag) {
          flag = false;
        }
        let detail = {
          assessListID: content.assessListID,
          groupID: content.groupID,
        };
        if (content.assessListID == this.allergicAssessListID) {
          this._sendBroadcast("refreshInpatient", this._common.guid());
        }
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        // // 按钮不处理
        if (content.controlerType.trim() != "B") {
          details.push(detail);
        }
      });
      if (!flag) {
        return [];
      }
      return details;
    },
    /**
     * description: 获取病人那是否转科 是否有入院评估
     * return {*}
     */
    getPatientIsTransOut() {
      let params = {
        inpatientID: this.inpatientInfo.inpatientID,
      };
      GetPatientIsTransOut(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.transOutData = res.data;
        }
      });
    },
  },
};
</script>

<style lang='scss'>
.base-layout.assess-supplement {
  height: 100%;
  .base-header {
    padding: 0 0 10px 0;
  }
  .assess-supplement-content-top {
    height: 40px;
    line-height: 40px;
    background-color: #fff;
    padding-left: 10px;
    .dataSelect {
      width: 120px;
    }
    .el-button {
      float: right;
      margin-right: 10px;
      margin-top: 5px;
    }
  }
  .assess-supplement-content-table {
    height: calc(100% - 40px) !important;
    span {
      &.is-bring-tpr {
        color: #ff0000;
      }
      &.is-retest {
        color: #0000ff;
      }
    }
  }
  .dialog {
    height: 100%;
    width: 100%;
    .dialog-top {
      margin-bottom: 10px;
      .form-bottom {
        height: 35px;
        line-height: 35px;
        padding-left: 60px;
        label {
          padding: 0 6px;
        }
      }
      .assess-patient-view {
        .dialog-select {
          width: 165px;
        }
      }
    }
  }
  .assessView {
    height: calc(100% - 110px);
  }
  .picker {
    width: 150px;
  }
  .assess-show {
    visibility: hidden;
  }
  .div-span {
    margin-top: 10px;
    margin-bottom: 18px;
    .el-divider {
      margin: 0 8px;
      width: 5px;
      height: 1em;
      position: relative;
      vertical-align: middle;
      display: inline-block;
      background-color: #8cc63e;
    }
  }
  //限制el-from 的行间距 默认22px
  .el-form-item--small.el-form-item {
    margin-bottom: 8px;
  }

  //ElementUI未提供不显示提示的接口，使用CSS隐藏
  .el-form-item__error {
    display: none;
  }
  label.el-form-item__label::before {
    content: "" !important;
  }
}
</style>