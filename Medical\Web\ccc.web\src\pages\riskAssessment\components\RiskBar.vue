<!--
 * FilePath     : \ccc.web\src\pages\riskAssessment\components\RiskBar.vue
 * Author       : 郭鹏超
 * Date         : 2020-06-01 19:11
 * LastEditors  : 张现忠
 * LastEditTime : 2024-11-13 09:19
 * Description  : 风险等级条组件
 * CodeIterationRecord: 2022-09-08 新增参数showPointFlag，判断是否展示端点的分数 -杨欣欣
-->
/*
 组件使用说明:
 调用该组件时可传入三个参数:
 riskRange:为数组类型 必填 为整理后的风险条刻度、文字、风险条颜色数据
 value：Number类型 选填 默认值为0 为风险条的分值 需动态绑定
 riskBalWidth：string类型 选填 默认值为100%  为风险条的长度 支持auto px 数字
 showPointFlag:bool类型，选填，默认为true，是否显示端点分数
 参考案例：pages>riskAssessment>components>RiskRatingScale.vue
 <RiskBar :riskRange="[]" v-model="totalNumber" riskBalWidth="800"/>
*/
<template>
  <div class="risk-bar">
    <div class="riskBar-div">
      <div :width="fixedBalWidth" ref="barWrap" class="bar">
        <!-- 每个风险等级的长条 -->
        <div
          v-show="stripBoole"
          :style="{
            width: item.stripWidth + 'px',
            backgroundColor: item.color,
          }"
          class="group"
          v-for="(item, index) in riskRange"
          :key="index + '_0'"
        ></div>
        <!-- 风险等级文字 -->
        <span
          :style="{
            left: item.textLeft + 'px',
          }"
          :ref="'text_' + index"
          class="text"
          v-for="(item, index) in riskRange"
          :key="index + '_3'"
        >
          {{ item.name }}
        </span>
        <!-- 端点竖线 -->
        <span
          :ref="'tick_' + item"
          :style="{
            left: (reverseOrderFlag ? (index + 1):index) * tickWidth + 'px',
          }"
          :class="['tick', 'tick_' + item]"
          v-for="(item, index) in riskRangeData"
          :key="index + '_1'"
        ></span>
        <!-- 端点分值 -->
        <!-- 前边空出一个tick宽度，所以从index+1开始 -->
        <span
          :style="{
            left:(reverseOrderFlag ? (index + 1):index) * tickWidth - 3 + 'px',
          }"
          class="value"
          v-for="(item, index) in riskRangeData"
          :key="index + '_2'"
          v-if="showPointFlag"
        >
          {{ item }}
        </span>
        <!-- 表示当前所选分值所处位置的浮动条 -->
        <div :style="{ left: scoreLeft + 'px' }" class="arrow"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    riskRange: {
      type: Array,
      required: true,
    },
    value: {
      type: Number,
      default: 0,
    },
    riskBalWidth: {
      type: String,
      default: "100%",
    },
    showPointFlag: {
      type: Boolean,
      default: true,
    },
    reverseOrderFlag:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      stripBoole: false,
      tickWidth: "",
      riskRangeData: [], //刻度数据数组
      scoreLeft: 0,
      fixedBalWidth: "",
      firstTextBoole: false,
      dotFlag: false,
    };
  },
  watch: {
    riskBalWidth: {
      handler(newValue) {
        this.fixBal();
      },
      immediate: true,
    },
    value: {
      handler(newValue) {
        //数据发生改变调用
        this.getScale();
      },
      // immediate: true
    },
    riskRange: {
      handler(newValue) {
        //判断是否为点模式
        this.getDotFlag();
        //数据发生改变调用
        this.getScale();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    //调整风险条宽度数据
    fixBal() {
      this.fixedBalWidth = this._common.getHeigt(this.riskBalWidth);
    },
    //获取刻度数据数组和长度条宽度
    getScale() {
      //空数据判断
      if (this.riskRange.length == 0) {
        return;
      }
      // 数据改变将boole置为false
      this.firstTextBoole = false;
      this.stripBoole = false;
      //解决标题重叠bug
      if (this.riskRange[0].values.length == 1 && this.riskRange[0].values[0] == 0) {
        this.firstTextBoole = true;
      }
      //非计划性拔管样式重叠
      if (this.riskRange[0].values[this.riskRange[0].values.length - 1] - this.riskRange[0].values[0] <= 1) {
        this.firstTextBoole = true;
      }
      let fixArr = [];
      for (let i = 0; i < this.riskRange.length; i++) {
        //得出所有刻度
        fixArr = this.combine_arr(fixArr, this.riskRange[i].values);
      }
      //获取刻度值
      this.riskRangeData = fixArr;
      this.$nextTick(() => {
        //得到刻度的宽度
        this.tickWidth = Math.floor((this.$refs.barWrap.offsetWidth - 50) / (this.riskRangeData.length - 1));
        this.tickWidth = Math.floor(
          (this.$refs.barWrap.offsetWidth - 50 - this.tickWidth) / (this.riskRangeData.length - 1)
        );
        this.$nextTick(() => {
          //获取长度条的宽度和文字描述的位置
          this.riskRange.map((item, index) => {
            if (index == 0) {
              item.stripWidth =
                this.$refs["tick_" + (item.values[item.values.length - 1] + 1)][0].offsetLeft -
                this.$refs["tick_" + item.values[0]][0].offsetLeft;
            } else {
              item.stripWidth =
                this.$refs["tick_" + item.values[item.values.length - 1]][0].offsetLeft -
                this.$refs["tick_" + item.values[0]][0].offsetLeft;
            }
            // item.textLeft = this.$refs["tick_" + item.values[0]][0].offsetLeft;
            //获取标题位置
            item.textLeft = this.getTextLeft(item.values, index);
          });
          if (this.firstTextBoole && !this.dotFlag) {
            this.$refs.text_0[0].style.top = "68px";
          }
          //调用获取分数位置函数
          this.getArrowLeft();
          //符合风险标题标红
          this.successTick();
          //解决数据长度条数据还未出现页面就开始渲染的BUG
          this.stripBoole = true;
        });
      });
    },
    //获取分数arrow位置
    getArrowLeft() {
      let scoreLeft = 0;
      let sortArr = this.riskRangeData;
      if (this.riskRangeData.indexOf(this.value) != -1) {
        //分数与某个刻度相等
        scoreLeft = this.$refs["tick_" + this.riskRangeData[this.riskRangeData.indexOf(this.value)]][0].offsetLeft;
      } else {
        //分数与所有刻度不等
        sortArr[sortArr.length] = this.value;
        sortArr = this.sort(sortArr);
        scoreLeft =
          this.$refs["tick_" + sortArr[sortArr.indexOf(this.value) - 1]][0].offsetLeft +
          (this.value - sortArr[sortArr.indexOf(this.value) - 1]) *
            (this.tickWidth / (sortArr[sortArr.indexOf(this.value) + 1] - sortArr[sortArr.indexOf(this.value) - 1]));
        sortArr.splice(sortArr.indexOf(this.value), 1);
      }

      this.scoreLeft = this.reverseOrderFlag ? scoreLeft - 8 : scoreLeft;
    },
    //合并数组函数
    combine_arr(arr1, arr2) {
      return [...new Set([...arr1, ...arr2])];
    },
    //数组排序
    sort(arr) {
      return arr.sort(function (a, b) {
        return a - b;
      });
    },
    //判断是否为分段的末尾数值
    ifLast(value) {
      let ifLastBoole = false;
      this.riskRange.some((item) => {
        item.values.some((valueItem, index) => {
          if (valueItem == value && index == item.values.length - 1) {
            ifLastBoole = true;
            return true;
          }
        });
      });
      return ifLastBoole;
    },
    //符合风险字段标红
    successTick() {
      //获取标红标题下表
      let textIndex = this.riskRange.findIndex(
        (item) => this.value >= item.values[0] && this.value <= item.values[item.values.length - 1]
      );
      //初始化字体为黑色
      this.riskRange.forEach((item, index) => (this.$refs["text_" + index][0].style.color = "#333"));
      if(!textIndex.length){
        return;
      }
      //符合标题标红
      this.$refs["text_" + textIndex][0].style.color = "#FF0000";
    },
    //获取标题位置
    getTextLeft(values, index) {
      if (!values.length) {
        return 0;
      }
      let textWidth = this.$refs["text_" + index][0].offsetWidth;
      let length = values.length;
      if (this.dotFlag) {
        return (
          this.$refs["tick_" + values[values.length > 1 ? 1 : 0]][0].offsetLeft -
          (values.length > 1 ? textWidth / 2 : 0)
        );
      } else {
        if(this.reverseOrderFlag){
          return index == 0
            ? this.$refs["tick_" + values[0]][0].offsetLeft - this.tickWidth
            : this.$refs["tick_" + values[0]][0].offsetLeft ;
        }
        return index == 0
          ? this.$refs["tick_" + values[0]][0].offsetLeft
          : this.$refs["tick_" + values[0]][0].offsetLeft + this.tickWidth; 

      }
    },
    //判断是否为点模式
    getDotFlag() {
      if (this.riskRange.length == 0) {
        return;
      }
      // 不存在级距范围大于1的为 点模式
      let sucData = this.riskRange.find((item) => {
        return item.values.length > 1 && item.values[item.values.length - 1] - item.values[0] > 1;
      });
      this.dotFlag = sucData ? false : true;
    },
  },
};
</script>

<style lang="scss">
.risk-bar {
  width: 100%;
  .riskBar-div {
    width: 100%;
  }
  .bar {
    position: relative;
    height: 83px;
    margin: 5px 10px;
  }
  .bar .group {
    height: 20px;
    position: relative;
    display: inline-block;
    border: 0;
    top: 30px;
  }
  .bar .text {
    position: absolute;
    font-size: 13px;
    color: #333;
  }
  .bar .tick {
    position: absolute;
    top: 50px;
    height: 7px;
    width: 1px;
    background-color: #888;
  }
  .bar .value {
    position: absolute;
    top: 55px;
    font-size: 9px;
  }

  .bar .arrow {
    position: absolute;
    background-color: #00ff00 !important;
    background-image: none !important;
    width: 8px;
    height: 25px;
    top: 20px;
    left: -7px;
    cursor: pointer;
  }
}
</style>
