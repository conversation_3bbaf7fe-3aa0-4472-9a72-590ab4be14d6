<!--
 * FilePath     : \src\pages\schedule\scheduleExecution.vue
 * Author       : 李青原
 * Date         : 2020-05-20 14:40
 * LastEditors  : 来江禹
 * LastEditTime : 2024-12-10 14:13
 * Description  :  措施批量执行
* CodeIterationRecord: 2022-05-22 2642-作为IT人员，我需要排程执行时，排程时间默认排程计划时间 En
-->
<template>
  <base-layout class="schedule-execution" headerHeight="auto" v-loading="loading" :element-loading-text="loadingText">
    <div slot="header">
      全科：
      <el-switch v-model="isAll" @change="interventionChange" />
      班别日期：
      <el-date-picker
        type="date"
        placeholder="选择班别日期"
        v-model="shiftDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        style="width: 120px"
        @change="getSchedule"
      ></el-date-picker>
      <shift-select
        v-model="shiftID"
        label="班别:"
        width="65"
        :stationID="user.stationID"
        @select-item="selectShiftItem"
      />
      <shift-times-selector
        ref="timeRange"
        label="措施时间"
        width="110"
        v-model="shiftTimes"
        :shiftID="shiftID"
        @select="changeShiftTimes"
      ></shift-times-selector>
      <span>执行措施:</span>
      <el-select v-model="scheduleIndex" @change="interventionChange">
        <el-option
          v-for="(item, index) in scheduleItemList"
          :key="index"
          :label="item.value"
          :value="index"
        ></el-option>
      </el-select>
      <!-- 新增加的控制全局的时间插件 -->
      <label>执行日期:</label>
      <el-date-picker
        v-model="allExecuteDate"
        type="date"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        style="width: 80px"
        @change="changeAllExecuteTime()"
      ></el-date-picker>
      <label>执行时间:</label>
      <el-time-picker
        v-model="allExecuteTime"
        type="time"
        value-format="HH:mm"
        format="HH:mm"
        style="width: 80px"
        @change="changeAllExecuteTime()"
      ></el-time-picker>
      <div class="top-btn">
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
        <el-button class="print-button" icon="iconfont icon-back" @click="$router.go(-1)">返回</el-button>
      </div>
    </div>
    <div class="schedule-data">
      <!-- 病人列表携带措施执行内容 -->
      <el-table
        :header-cell-class-name="checkAll ? null : disabledSelection"
        @selection-change="
          (selection) => {
            commitArray = selection;
          }
        "
        border
        stripe
        height="100%"
        :row-key="getRowKey"
        @expand-change="expandChange"
        :expand-row-keys="expandArray"
        :data="showScheduleList"
        ref="executionTable"
      >
        <el-table-column type="selection" align="center" :width="convertPX(40)"></el-table-column>
        <el-table-column type="expand">
          <template slot-scope="scope" v-if="Boolean(scope.row.detailList)">
            <div class="schedule-name">
              {{ scheduleItemList[scheduleIndex].value }}
            </div>
            <div class="content">
              <template v-for="(item, index) in scope.row.detailList">
                <div v-if="item.children" :key="index" class="group-block">
                  <div class="group_exec">{{ getText(item.showName) }}</div>
                  <div class="group-exec-div" v-for="(child, index) in item.children" :key="index">
                    <monitor-view
                      :item="child"
                      :items="item.children"
                      @changeValue="changeValue(scope.row, $event, item.children, undefined)"
                    ></monitor-view>
                  </div>
                </div>
                <monitor-view
                  :key="index"
                  v-else
                  :item="item"
                  :items="scope.row.detailList"
                  @changeValue="changeValue(scope.row, $event, scope.row.detailList)"
                ></monitor-view>
              </template>
              <div class="tip">
                <span class="font">备注：</span>
                <el-input
                  v-model="scope.row.performComment"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 2 }"
                  placeholder="请输入内容"
                  resize="none"
                ></el-input>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="排程时间" min-width="110px" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.scheduleDate, type: 'date' }"></span>
            <span v-formatTime="{ value: scope.row.scheduleTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="执行时间" width="270" align="center">
          <template slot-scope="scope">
            <div class="line-time">
              <el-date-picker
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="日期"
                class="time"
                v-model="scope.row.date"
              ></el-date-picker>
              <el-time-picker
                format="HH:mm"
                value-format="HH:mm"
                placeholder="时间"
                class="time"
                v-model="scope.row.time"
              ></el-time-picker>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="床位" prop="bedNumber" width="100px" align="center"></el-table-column>
        <el-table-column label="护理级别" align="center">
          <template slot-scope="scope">
            {{ scope.row.nursingLevel }}
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center" prop="patientName"></el-table-column>
        <el-table-column label="性别" align="center">
          <template slot-scope="scope">
            {{ scope.row.gender }}
          </template>
        </el-table-column>
        <el-table-column label="带入记录" align="center">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.record"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="带入交班" align="center">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.handover"></el-checkbox>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import {
  GetMultiPatientPerformList,
  GetPatientScheduleSingle,
  GetScheduleIntervention,
  PerformCheck,
  SavePatientScheduleSingle,
} from "@/api/PatientSchedule";
import { GetOneSettingByTypeAndCode, GetScheduleTop } from "@/api/Setting";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { GetNowStationShiftData } from "@/api/StationShift";
import BaseLayout from "@/components/BaseLayout";
import ShiftSelect from "@/components/selector/shiftSelector.vue";
import shiftTimesSelector from "@/components/selector/shiftTimesSelector";
import MonitorView from "@/pages/schedule/components/scheduleTypes/monitorView.vue";
import { mapGetters } from "vuex";
export default {
  components: {
    BaseLayout,
    ShiftSelect,
    MonitorView,
    shiftTimesSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },

  data() {
    return {
      allExecuteDate: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      allExecuteTime: this._datetimeUtil.getNowDate("hh:mm"),
      //措施执行细项
      scheduleDetail: {},
      // 执行措施项目
      scheduleItemList: [],
      shiftDate: "",
      shiftID: undefined,
      shiftTimes: "",
      // 排程开始时间
      startTime: "",
      //排程结束时间
      endTime: "",
      // 班别日期
      shiftInfo: undefined,
      scheduleIndex: undefined,
      //总执行措施
      scheduleList: [],
      //过滤后展示的措施执行对象
      showScheduleList: [],
      expandArray: [],
      // 最大时间
      minTime: undefined,
      // 最小时间
      maxTime: undefined,
      // 保存数组
      commitArray: [],
      loading: false,
      loadingText: "",
      expandedRows: [],
      // 默认展开按钮
      expandFlag: false,
      //宏力默认开启全选
      checkAll: true,
      nursingLevelList: [],
      shift: "",
      nowDataTime: "",
      isAll: false,
      scheduleTimeFlag: false,
    };
  },

  mounted() {
    // 获取是否默认展开
    let params = this.$route.params;
    this.getNursingLevelList();
    this.getBatchPerformSwich();
    this.getNowDateTime();
    this.getScheduleTimeFlag();
    if (JSON.stringify(params) == "{}") {
      this.getNowStationShiftData();
    } else {
      this.isAll = params.showAll;
      this.shiftInfo = params.shiftInfo;
      this.shift = params.shiftInfo.shift;
      this.shiftID = params.shiftInfo.id;
      this.shiftDate = params.shiftDate;
      this.startTime = params.startTime;
      this.endTime = params.endTime;
      this.$nextTick(() => {
        this.shiftTimes = params.startTime + "-" + params.endTime;
      });
    }
  },
  methods: {
    changeAllExecuteTime() {
      this.showScheduleList.forEach((item) => {
        item.time = this.allExecuteTime;
        item.date = this.allExecuteDate;
      });
    },
    changeShiftTimes(shiftTimes) {
      var timeSpan = shiftTimes.split("-");
      if (timeSpan && timeSpan.length == 2) {
        this.startTime = timeSpan[0];
        this.endTime = timeSpan[1];
      }
      this.$nextTick(this.getSchedule());
    },
    getSchedule() {
      this.scheduleItemList = [];
      this.scheduleIndex = "";
      this.showScheduleList = [];
      if (!this.shiftInfo) {
        return;
      }
      let param = {
        shiftDate: this.shiftDate,
        shift: this.shift,
        startTime: this.startTime,
        endTime: this.endTime,
        nurseID: this.isAll ? undefined : this.user.userID,
      };
      GetScheduleIntervention(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data && response.data.length) {
            this.scheduleItemList = response.data;
            if (this.$route.params.interventionID) {
              let index = this.scheduleItemList.findIndex((element) => {
                return this.$route.params.interventionID == element.id;
              });
              this.scheduleIndex = index == -1 ? 0 : index;
            }
            this.interventionChange();
          }
        }
      });
    },
    async expandChange(row, expandedRows) {
      this.loading = true;
      this.expandedRows = expandedRows;
      //是否可执行
      let flag = await this.showExecute(row);
      // 判断收起还是展开
      if (expandedRows.indexOf(row) == -1) {
        this.loading = false;
        return;
      }
      if (flag) {
        //判断是否重新获取执行时间
        if (this.commitArray.indexOf(row) < 0) {
          await this.getExecutTime(row);
        }
        //减少重复请求
        if (row.detailList) {
          this.loading = false;
          return;
        }
        let param = {
          inpatientID: row.inpatientID,
          patientScheduleMainID: row.patientScheduleMainID,
        };
        await GetPatientScheduleSingle(param).then((response) => {
          if (this._common.isSuccess(response)) {
            if (!response.data) {
              return;
            }
            // 抛出元素数据处理逻辑
            let data = response.data;
            for (let i = data.length - 1; i >= 0; i--) {
              if (data[i].isNormal && data[i].isNormal == "1") {
                let normalArray = this._common.clone(data[i]);
                if (!normalArray.scheduleData) {
                  if (normalArray.childs && normalArray.childs.length == 1) {
                    normalArray.scheduleData = "";
                  } else {
                    normalArray.scheduleData = "2";
                  }
                }
                row.saveDatas = [];
                this.changeValue(row, normalArray);
                data.splice(i, 1);
                //存入normalArray
                row.normalArray = normalArray;
                break;
              }
            }
            this.$set(row, "detailList", data);
          }
          this.loading = false;
        });
      } else {
        this.loading = false;
        expandedRows.splice(expandedRows.indexOf(row), 1);
      }
    },
    // 判断是否可以执行此排程
    async showExecute(row) {
      //已经可执行则直接返回
      if (row.checkFlag) {
        return row.checkFlag;
      }
      let flag = false;
      let params = {
        scheduleDate: row.scheduleDate,
        scheduleTime: row.scheduleTime,
        //用于解决请求拦截
        mathNumber: this._common.guid(),
      };
      await PerformCheck(params).then((result) => {
        // 可以执行
        if (this._common.isSuccess(result)) {
          // 检核类型
          flag = true;
          row.checkFlag = flag;
        }
      });
      return flag;
    },
    async save() {
      if (this.loading) return;
      this.loading = true;
      this.loadingText = "保存中……";
      const array = this.commitArray;
      if (array.length == 0) {
        this._showTip("warning", "未勾选任何需要执行的排程！");
        this.loading = false;
        return;
      }
      //检核排程执行时间
      if (!this.checkSaveDataPerformDate(array)) {
        this.loading = false;
        return;
      }
      let commit = [];
      let failArray = [];
      let successArray = [];
      for (let i = 0; i < array.length; i++) {
        if (commit.indexOf(array[i].bedNumber) == -1) {
          commit.push(array[i].bedNumber);
        }
        this.loadingText = "已提交" + commit.toString() + "床排程。";
        let param = await this.getParam(array[i]);
        let flag = await this.saveSingle(param, array[i].bedNumber);
        if (flag) {
          //放进loadingText数组
          if (successArray.indexOf(array[i].bedNumber + "床") == -1) {
            successArray.push(array[i].bedNumber + "床");
          }
        } else {
          if (failArray.indexOf(array[i].bedNumber + "床") == -1) {
            failArray.push(array[i].bedNumber + "床");
          }
        }
      }
      let message = '提交成功：<strong  style="color:red">' + successArray.toString() + "</strong><br/>";
      message += '提交失败：<strong   style="color:red">' + failArray.toString() + "</strong>";
      this.$msgbox({
        title: "提示",
        message: message,
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        confirmButtonText: "确定",
        customClass: "multihandoff-msgbox-class",
      })
        .then(() => {})
        .catch(() => {});
      // 更新措施列表;
      this.loading = false;
      this.loadingText = "";
    },
    // 单笔保存
    async saveSingle(param) {
      let flag = true;
      await SavePatientScheduleSingle(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data) {
            //执行异常
            flag = false;
          }
        } else {
          //执行异常
          flag = false;
        }
      });
      return flag;
    },
    getText(text) {
      if (!text) return "";
      if (text.indexOf(":") != -1 || text.indexOf("：") != -1) {
        return text;
      }
      return (text += "：");
    },
    async getBatchPerformSwich() {
      // 默认为true
      let param = { settingType: 111, settingCode: "BatchPerformSwich" };
      await GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          // 运算 将'true' 转化 true
          let typeValueFlag = response.data.typeValue.toLowerCase();
          let flag;
          if (typeValueFlag == "true") {
            flag = true;
          }
          if (typeValueFlag == "false") {
            flag = false;
          }
          this.expandFlag = flag;
          this.checkAll = !flag;
        }
      });
    },
    getParam(row) {
      let normalArray = row.normalArray;
      let datas = row.saveDatas;
      let index = this.expandedRows.indexOf(row);
      if (normalArray && normalArray.length > 0) {
        if (datas) {
          datas[datas.indexOf(normalArray)] = {
            assessListID: 0,
            interventionDetailID: normalArray.interventionDetailID,
            scheduleData: 2,
          };
        } else {
          datas = [];
          datas.push({
            assessListID: 0,
            interventionDetailID: row.interventionDetailID,
            scheduleData: 1,
          });
        }
      }
      if (!datas) {
        datas = [];
        datas.push({
          assessListID: 0,
          interventionDetailID: row.interventionDetailID,
          scheduleData: 1,
        });
      }
      datas.forEach((element, i) => {
        if (i != index) {
          datas[i] = {
            assessListID: element.assessListID,
            interventionDetailID: element.interventionDetailID,
            scheduleData: element.scheduleData,
          };
        }
      });
      return {
        InterventionID: row.interventionID,
        bringToNursingRecords: row.record ? "1" : "0",
        bringToShift: row.handover ? "1" : "0",
        inPatientID: row.inpatientID,
        patientAssessMainID: row.patientAssessMainID,
        patientInterventionID: row.patientInterventionID,
        patientScheduleMainID: row.patientScheduleMainID,
        performComment: row.performComment,
        saveData: datas,
        scheduleDate: row.date,
        scheduleTime: row.time,
      };
    },
    //措施改变
    async interventionChange() {
      if (!this.scheduleIndex && this.scheduleIndex != 0) {
        return;
      }
      let interventionID = this.scheduleItemList[this.scheduleIndex].id;
      this.scheduleList = [];
      this.showScheduleList = [];
      let param = {
        shiftDate: this.shiftDate,
        shift: this.shiftInfo.shift,
        interventionID: interventionID,
        startTime: this.startTime,
        endTime: this.endTime,
        isAll: this.isAll,
      };
      await GetMultiPatientPerformList(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.expandArray = [];
          this.scheduleList = response.data;
          this.showScheduleList = this.scheduleList;
        }
      });
      if (this.expandFlag) {
        // 默认展开
        this.expandArray = [];
        this.scheduleList.forEach((element) => {
          // 将bedNumber 存入数组后即可将展开默认行
          this.expandArray.push(element.patientScheduleMainID);
          // 手动请求;
          this.expandChange(element, this.scheduleList);
        });
      }
    },
    /**
     * description: 获取排程执行时间
     * return {*}
     */
    async getExecutTime(row) {
      if (this.scheduleTimeFlag) {
        this.$set(row, "date", this._datetimeUtil.formatDate(row.scheduleDate, "yyyy-MM-dd"));
        this.$set(row, "time", this._datetimeUtil.formatDate(row.scheduleTime, "hh:mm"));
      } else {
        this.$set(row, "date", this._datetimeUtil.formatDate(this.nowDataTime, "yyyy-MM-dd"));
        this.$set(row, "time", this._datetimeUtil.formatDate(this.nowDataTime, "hh:mm"));
      }
    },
    getNowDateTime() {
      this.nowDataTime = this._datetimeUtil.getNow();
    },
    //选项数据操纵
    changeValue(row, item, items, flag) {
      if (!row.saveDatas) {
        row.saveDatas = [];
      }
      // 单选互斥
      if (item.style.trim() == "R" && items) {
        items.forEach((temp) => {
          if (temp.style.trim() == "R" && item.interventionDetailID != temp.interventionDetailID) {
            this.$set(temp, "scheduleData", "");
            if (row.saveDatas && row.saveDatas.length > 0) {
              for (let i = row.saveDatas.length - 1; i >= 0; i--) {
                if (row.saveDatas[i].interventionDetailID == temp.interventionDetailID) {
                  row.saveDatas.splice(i, 1);
                  break;
                }
              }
            }
          }
        });
      }
      // // 先去掉原来的
      if (row.saveDatas.length > 0) {
        for (let i = row.saveDatas.length - 1; i >= 0; i--) {
          if (row.saveDatas[i].interventionDetailID == item.interventionDetailID) {
            row.saveDatas.splice(i, 1);
            break;
          }
        }
      }
      if (flag) {
        return;
      }
      if (item.scheduleData && item.scheduleData.length > 0) {
        row.saveDatas.push(item);
      }
      if (items) {
        this.$refs.executionTable.toggleRowSelection(row, true);
      }
    },
    //配合默认展开 存入bedNumber
    getRowKey(row) {
      // 将病人行
      return row.patientScheduleMainID;
    },
    //班别改变
    selectShiftItem(data) {
      this.shiftInfo = data;
      this.shiftID = data.id;
    },
    // 获取当前班次
    async getNowStationShiftData() {
      await GetNowStationShiftData().then((result) => {
        if (this._common.isSuccess(result)) {
          this.shiftID = result.data.nowShift.id;
          this.shiftInfo = result.data.nowShift;
          this.shiftDate = result.data.shiftDate;
        }
      });
    },
    // 全选按钮显示
    disabledSelection(row) {
      if (row.columnIndex === 0) {
        return "DisableSelection";
      }
    },
    getNursingLevelList() {
      let param = { settingTypeCode: "NursingLevel" };
      GetScheduleTop(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.nursingLevelList = response.data;
        }
      });
    },
    //检核是否有排程执行时间
    checkSaveDataPerformDate(dataArr) {
      let flag = true;
      let fail = dataArr.find((item) => !item.date || !item.time);
      if (fail) {
        this._showTip("warning", "请填写" + fail.bedNumber + "床" + fail.patientName + "排程执行时间");
        flag = false;
      }
      return flag;
    },
    getScheduleTimeFlag() {
      GetSettingSwitchByTypeCode({ SettingTypeCode: "UseScheduleDateTime" }).then((res) => {
        if (this._common.isSuccess(res)) {
          this.scheduleTimeFlag = res.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.schedule-execution {
  .top-btn {
    float: right;
  }
  .line-time {
    display: inline-block;
    .time {
      width: 120px;
      display: inline-block;
    }
  }
  .schedule-data {
    height: 100%;
    .schedule-name {
      display: inline-block;
      font-size: 16px;
      font-weight: 600;
    }
    .content {
      margin-top: 10px;
      display: inline-block;
      overflow-y: auto;
      width: 100%;
      .group-exec-div {
        display: inline-block;
      }
      .group_exec {
        height: 100%;
        margin-left: 10px;
        color: $base-color;
        font-size: 14px;
      }
      .tip {
        display: flex;
        margin: 20px 0 0 10px;
        .font {
          width: 50px;
          vertical-align: top;
          font-size: 14px;
        }
      }
    }
    .el-table .DisableSelection .cell .el-checkbox__inner {
      display: none;
      position: relative;
    }
    .el-table .DisableSelection .cell:before {
      content: "";
      position: absolute;
    }
    .monitor-view .div_C .el-checkbox {
      max-width: none;
    }
  }
}
</style>
