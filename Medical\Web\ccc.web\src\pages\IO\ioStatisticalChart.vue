<!--
 * FilePath     : \src\pages\IO\ioStatisticalChart.vue
 * Author       : 郭自飞
 * Date         : 2020-03-22 08:19
 * LastEditors  : 来江禹
 * LastEditTime : 2023-11-11 09:46
 * Description  : io统计图
 * CodeIterationRecord:
 * 1、2690-作为护理人员，我需要出入水量查询，每个页签都可以根据自然时间查询
 -->
<template>
  <base-layout class="io-chart">
    <div slot="header">
      <span class="label">{{ ioStatistics.switchLabel }}</span>
      <el-switch v-model="queryByShift" />
      <div class="where" v-show="queryByShift">
        <span class="label">{{ ioStatistics.startDate }}</span>
        <el-date-picker
          v-model="startShiftTime"
          value-format="yyyy-MM-dd"
          type="date"
          :placeholder="placeholderDate"
          class="date-picker"
        ></el-date-picker>
        <shift-selector
          :stationID="stationID"
          @select-item="changeShift($event, 'startShift')"
          v-model="startShift"
          label="班别："
          width="70"
        ></shift-selector>
        <span class="label">{{ ioStatistics.endDate }}</span>
        <el-date-picker
          v-model="endShiftTime"
          value-format="yyyy-MM-dd"
          type="date"
          :placeholder="placeholderDate"
          class="date-picker"
        ></el-date-picker>
        <shift-selector
          :stationID="stationID"
          @select-item="changeShift($event, 'endShift')"
          v-model="endShift"
          label="班别："
          width="70"
        ></shift-selector>
      </div>
      <div class="where" v-show="!queryByShift">
        <span class="label">{{ ioRecord.startTime }}</span>
        <el-date-picker
          v-model="startTime"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          :placeholder="placeholderDate"
          class="datetime-picker"
        ></el-date-picker>
        <span class="label">{{ ioRecord.endTime }}</span>
        <el-date-picker
          v-model="endTime"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          :placeholder="placeholderDate"
          class="datetime-picker"
        ></el-date-picker>
      </div>
      <el-button class="query-button" icon="iconfont icon-search" @click="statisticsIOLineList">
        {{ queryButton }}
      </el-button>
    </div>
    <ve-histogram
      v-loading="loading"
      :element-loading-text="loadingText"
      :data="chartData"
      :settings="chartSettings"
      :extend="chartExtend"
      :tooltip="tooltip"
      :colors="colors"
      height="100%"
    ></ve-histogram>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout.vue";
import shiftSelector from "@/components/selector/shiftSelector";
import { GetIoInpatient } from "@/api/IO";
import { GetNowStationShiftData } from "@/api/StationShift";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    shiftSelector,
  },
  data() {
    this.colors = [];
    return {
      chartExtend: {
        series: {
          type: "bar",
          //改变统计图宽度
          barWidth: 30,
          label: {
            normal: {
              show: true,
              formatter: function (params) {
                if (params.value > 0) {
                  return params.value;
                } else {
                  return "";
                }
              },
            },
          },
        },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
        // 将 tooltip 框限制在图表的区域内
        confine: true,
        enterable: true,
      },
      intoStack: [],
      outStack: [],
      startTime: "",
      startShift: "",
      startShiftTime: "",
      endShiftTime: "",
      endTime: "",
      endShift: "",
      stationID: undefined,
      chartData: {
        columns: [],
        rows: [],
      },
      loading: false,
      queryByShift: true,
      shiftInfo: undefined,
    };
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
    }),
    placeholderDate() {
      return this.$t("placeholder.date");
    },
    queryButton() {
      return this.$t("button.query");
    },
    loadingText() {
      return this.$t("loadingText.load");
    },
    ioStatistics() {
      return this.$t("ioStatistics");
    },
    ioRecord() {
      return this.$t("ioRecord");
    },
    chartSettings() {
      return {
        stack: {
          outputTotal: [],
          intakeTotal: [],
        },
        legendName: {
          出平衡量: "平衡量",
          入平衡量: "平衡量",
        },
        dataOrder: {
          label: "输入总量",
          order: "desc",
        },
      };
    },
  },
  watch: {
    inpatient(newVal) {
      if (!newVal) return;
      this.init();
    },
    queryByShift: {
      handler() {
        this.init();
      },
    },
  },
  created() {
    this.init();
  },
  //执行方法
  methods: {
    init() {
      this.intoStack = [];
      this.outStack = [];
      this.chartData = {
        columns: [],
        rows: [],
      };
      if (this.inpatient) {
        this.stationID = this.inpatient.stationID;
      }
      if (this.queryByShift) {
        this.getShiftDate();
      } else {
        let nowDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.startTime = nowDate + " 00:00";
        this.endTime = nowDate + " 23:59";
      }
      this.statisticsIOLineList();
    },
    getShiftDate() {
      let flag = false;
      if (this.shiftInfo) {
        let date = this.shiftInfo.shiftDate;
        this.startShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
        this.endShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
      } else {
        flag = true;
      }
      if (flag) {
        let params = {
          stationID: this.stationID,
        };
        GetNowStationShiftData(params).then((res) => {
          if (this._common.isSuccess(res) && res.data) {
            this.shiftInfo = res.data;
            let date = this.shiftInfo.shiftDate;
            this.startShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
            this.endShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
          }
        });
      }
    },
    //获取病人数据
    statisticsIOLineList() {
      if (this.loading) {
        return;
      }
      if (!this.inpatient) {
        this._showTip("warning", this.ioStatistics.queryTipPatient);
        return;
      }
      if (this.queryByShift && (!this.startShift || !this.endShift)) {
        return;
      }
      if (!this.queryByShift && (!this.startTime || !this.endTime)) {
        return;
      }
      this.chartData = {
        columns: [],
        rows: [],
      };
      let params = {
        InpatientID: this.inpatient.inpatientID,
        queryByShift: this.queryByShift,
        index: Math.random(),
      };
      if (this.queryByShift) {
        params.startDate = this.startShiftTime;
        params.endDate = this.endShiftTime;
        params.startShift = this.startShift;
        params.endShift = this.endShift;
      } else {
        params.startDate = this.startTime;
        params.endDate = this.endTime;
      }
      this.loading = true;
      GetIoInpatient(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res) && res.data) {
          this.chartSettings.stack = {
            outputTotal: res.data.intoStack,
            intakeTotal: res.data.outStack,
          };
          this.colors = res.data.colors;
          this.chartData = {
            columns: res.data.columnsList,
            rows: res.data.veLineList,
          };
        }
      });
    },
    async changeShift(shift, typeName) {
      this[typeName] = shift?.id;
      this.statisticsIOLineList();
    },
  },
};
</script>
<style lang='scss'>
.io-chart {
  height: 100%;
  .where {
    display: inline-block;
    .label {
      margin-left: 10px;
    }
    .date-picker {
      width: 110px;
    }
    .datetime-picker {
      width: 150px;
    }
  }
}
</style>
