<!--
 * FilePath     : \src\pages\transferPages\transferNursingCodeMaintain.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 苏军志
 * LastEditTime : 2020-07-16 11:17
 * Description  : 串护理评估状态维护
--> 
<template>
  <div class="nursing-code-maintain">
    <el-row :gutter="10">
      <el-col :span="12" class="left">患者病历号（PatientID）:</el-col>
      <el-col :span="12" class="right">
        <el-input v-model="patientID" style="width: 200px"></el-input>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12" class="left">患者住院次数（VisitID）:</el-col>
      <el-col :span="12" class="right">
        <el-input v-model="visitID" style="width: 200px"></el-input>
      </el-col>
    </el-row>
    <br />
    <el-button type="primary" icon="iconfont icon-save-button" @click="saveNursingProcedureCode">保存</el-button>
  </div>
</template>
<script>
import { SuppleNursingProcedureCode } from "@/api/Assess";
export default {
  data() {
    return {
      patientID: "",
      visitID: "",
    };
  },
  methods: {
    saveNursingProcedureCode() {
      if (!this.patientID) {
        this._showTip("warning", "请输入患者病历号");
        return;
      }
      if (!this.visitID) {
        this._showTip("warning", "请输入患者住院次数");
        return;
      }
      let params = {
        patientID: this.patientID,
        visitID: this.visitID,
      };
      return SuppleNursingProcedureCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
        }
      });
    },
  },
};
</script>
<style lang="scss">
.nursing-code-maintain {
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  padding-top: 30px;
  box-sizing: border-box;
  text-align: center;
  .el-row {
    margin-bottom: 20px;
    .left {
      text-align: right;
    }
    .right {
      text-align: left;
    }
  }
}
</style>