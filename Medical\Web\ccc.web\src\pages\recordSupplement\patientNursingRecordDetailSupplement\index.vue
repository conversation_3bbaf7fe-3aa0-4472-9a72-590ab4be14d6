<template>
  <base-layout class="nursingRecord-Detail" v-if="showList">
    <div slot="header">
      <label>执行日期:</label>
      <el-date-picker
        class="data-select"
        v-model="nursingRecordDetailDate"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        @change="getPatientNursingRecordDetail()"
      ></el-date-picker>
      <div class="btn">
        <el-button class="add-button" @click="addOrModifyNursingRecordDetail()" icon="iconfont icon-add">
          新增
        </el-button>
      </div>
    </div>

    <el-table :data="supplyDatas" border stripe height="100%" v-loading="loading" element-loading-text="加载中……">
      <el-table-column prop="performDateTime" label="执行日期" min-width="85" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.performDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="itemName" label="项目" min-width="85" align="center"></el-table-column>
      <el-table-column prop="dataValue" label="值" min-width="85" align="center"></el-table-column>

      <el-table-column label="操作" width="80" align="center">
        <template slot-scope="monitor">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="addOrModifyNursingRecordDetail(monitor.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <div class="iconfont icon-del" @click="deleteNursingRecordDetail(monitor.row)"></div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="dalogTitle"
      :visible.sync="showDialog"
      custom-class="dialog-style"
      v-loading="dialogLoading"
      element-loading-text="保存中……"
    >
      <station-department-bed
        v-if="showDialog"
        :stDeptBed="stationDepartmentBed"
        @selectStationDepartmentBed="selectStationDepartmentBed"
      ></station-department-bed>
      <el-row>
        <el-col class="col-label" :span="4">监测时间：</el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="performDateTime"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            style="width: 180px"
          ></el-date-picker>
        </el-col>
      </el-row>

      <el-row>
        <el-col class="col-label" :span="4">执行人：</el-col>
        <el-col :span="8">
          <user-selector
            v-model="performUserID"
            :stationID="this.user.stationID"
            label=""
            clearable
            filterable
            remoteSearch
            :disabled="selectUserDisableFlag"
            width="180px"
          ></user-selector>
        </el-col>
      </el-row>
      <el-row v-if="!this.selectedData">
        <el-col class="col-label" :span="4">监测项目：</el-col>
        <el-col :span="8" class="col-select">
          <el-select v-model="selectItemValue" placeholder="请选择项目" style="width: 180px">
            <el-option v-for="(item, key) in items" :key="key" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row>
        <el-col class="col-label" :span="4">
          {{ this.selectedData ? this.selectedData.itemName + "：" : "监测结果：" }}
        </el-col>
        <el-col :span="8" class="col-select">
          <el-input style="width: 180px" v-model="detailValue" />
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="showDialog = false">取消</el-button>
        <el-button v-if="checkResult" type="primary" @click="saveData()">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import {
  GetPatientNursingRecordDetail,
  DeletePatientNursingRecordDetail,
  UpdatePatientNursingRecordDetail,
  AddPatientNursingRecordDetail,
} from "@/api/PatientNursingRecordDetail";
import baseLayout from "@/components/BaseLayout.vue";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData.vue";
import userSelector from "@/components/selector/userSelector";
import { mapGetters } from "vuex";
import { GetOneSettingByTypeAndCode, GetTPRsettingByCode } from "@/api/Setting";
import stationDepartmentBed from "@/pages/recordSupplement/components/stationDepartmentBed.vue";

export default {
  components: {
    baseLayout,
    searchPatientData,
    userSelector,
    stationDepartmentBed,
  },
  data() {
    return {
      //标记：表示执行人是否可以修改 true：表示下拉框不可选，只读
      selectUserFlag: false,
      loading: false,
      //表格头部开关
      showList: false,
      //筛选记录日期
      nursingRecordDetailDate: "",
      //筛选记录时间
      performDateTime: "",
      //获取当前时间
      currentTime: "",
      //补资料历史数据
      supplyDatas: [],
      //输入框中的对象
      selectedData: undefined,
      chartNo: "",
      patient: {},
      //修改显示弹出框flag
      showDialog: false,
      //护士id
      performUserID: "",
      //弹出框加载旋转样式
      dialogLoading: false,
      dalogTitle: "",
      //默认选择的项目
      selectItemValue: "",
      //项目列表
      items: [],
      checkResult: true,
      //是否禁用  这个页面的禁用默认打开
      selectUserDisableFlag: false,
      //是否显示保存按钮
      saveButtonFlag: true,
      //暂存病区科室及床位
      stationDepartmentBed: {
        stationID: 0,
        departmentListID: 0,
        bedNumber: "",
        bedId: 0,
      },
      //执行护士
      performNurse: undefined,
      detailValue: "",
    };
  },
  props: {
    patientinfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  watch: {
    index: {
      handler(newIndex) {
        if (newIndex == 1) {
          this.change();
        } else {
          this.selectPatientData(this.patientinfo);
        }
      },
      immediate: true,
    },
    patientinfo: {
      handler(newVal) {
        if (newVal) {
          this.selectPatientData(newVal);
        }
      },
      immediate: true,
    }
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  mounted() {
    this.getSetting();
    this.getTprSetting();
  },
  methods: {
    /**
     * description: 获取下拉框配置
     * return {*}
     */
    async getTprSetting() {
      let param = {
        settingTypeCode: "SupplementEmrSourceItems",
      };
      await GetTPRsettingByCode(param).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data) {
            res.data.forEach((element) => {
              let params = {
                value: element.typeValue,
                label: element.settingValue,
              };
              this.items.push(params);
            });
          }
        }
      });
    },
    getSetting() {
      let param = {
        settingType: 169,
        settingCode: "SwitchForMaintainer",
      };
      GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.typeValue == "False") {
            this.selectUserFlag = false;
          } else {
            this.selectUserFlag = true;
          }
        }
      });
    },

    change() {
      this.formats = [];
      this.supplyDatas = [];
      //重新搜索隐藏表格头部
      this.showList = false;
    },
    //查询病人
    selectPatientData(val) {
      if (val == null) {
        this.change();
      } else {
        this.patient = val;
        if (!this.showList) {
          //获取筛选记录日期
          this.getNursingRecordDetailDate();
        }
        this.showList = true;

        this.getPatientNursingRecordDetail();
      }
    },
    getPatientNursingRecordDetail() {
      this.dialogLoading = true;
      let params = {
        inpatientID: this.patient.inpatientID,
        date: this.nursingRecordDetailDate,
      };
      GetPatientNursingRecordDetail(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.dialogLoading = false;
          if (result.data) {
            this.supplyDatas = result.data;
          }
        }
      });
    },
    //获取记录筛选日期
    getNursingRecordDetailDate() {
      //判断病人出院与否
      this.nursingRecordDetailDate = this.patient.dischargeDate
        ? this._datetimeUtil.formatDate(this.patient.dischargeDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate();
    },
    async addOrModifyNursingRecordDetail(detail) {
      if (detail) {
        if (this.user && this.user.stationID != detail.stationID) {
          this._showTip("warning", "非本病区添加，不能修改");
          return;
        }
        this.selectStationDepartmentBed(detail);
        this.selectedData = detail;
        this.performDateTime = detail.performDateTime;
        this.performUserID = detail.performPersonID;
        this.detailValue = detail.dataValue;
        this.selectItemValue = detail.emrFilesID;
        this.dalogTitle = "修改";
        ({ disabledFlag: this.userSelectorDisabled, saveButtonFlag: this.checkResult } =
          await this._common.userSelectorDisabled(this.user.userID, false, this.supplementFlag, detail.addEmployeeID));
      } else {
        this.selectedData = undefined;
        this.selectItemValue = "";
        this.detailValue = "";
        this.performUserID = this.user.userID;
        this.performDateTime = this._datetimeUtil.getNowDate("yyyy-MM-dd hh:mm");
        this.dalogTitle = "新增";
        this.selectStationDepartmentBed(this.patient);
        this.patientEventGuid = this._common.guid();
        ({ disabledFlag: this.userSelectorDisabled, saveButtonFlag: this.checkResult } =
          await this._common.userSelectorDisabled(this.user.userID, true, this.supplementFlag, ""));
      }
      this.showDialog = true;
    },
    //新增方法
    addPatientNursingRecordDetail() {
      if (this.dialogLoading) {
        return;
      }
      this.dialogLoading = true;
      let params = {
        InpatientID: this.patient.inpatientID,
        PatientID: this.patient.patientID,
        DepartmentListID: this.patient.departmentListID,
        StationID: this.patient.stationID,
        BedID: this.patient.bedID,
        BedNumber: this.patient.bedNumber,
        PerformDateTime: this.performDateTime,
        EmrFilesID: this.selectItemValue,
        DataValue: this.detailValue,
        PerformPersonID: this.performUserID,
        RefillFlag: "*",
      };
      return AddPatientNursingRecordDetail(params).then((result) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.showDialog = false;
          this.selectPatientData(this.patient);
        }
      });
    },
    //修改
    updatePatientNursingRecordDetail() {
      this.dialogLoading = true;
      let params = {
        PerformDateTime: this.performDateTime,
        EmrFilesID: this.selectItemValue,
        DataValue: this.detailValue,
        PerformPersonID: this.performUserID,
        NursingRecordDetailID: this.selectedData.nursingRecordDetailID,
      };

      return UpdatePatientNursingRecordDetail(params).then((result) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.showDialog = false;
          this.selectPatientData(this.patient);
        }
      });
    },
    saveData() {
      if (!this.selectItemValue) {
        this._showTip("warning", "请选择项目");
        return;
      }
      if (!this.performUserID) {
        this._showTip("warning", "请选择护士！");
        return;
      }
      if (this.selectedData) {
        this.updatePatientNursingRecordDetail();
      } else {
        this.addPatientNursingRecordDetail();
      }
      this.selectItemValue = "";
      this.detailValue = "";
    },
    //删除数据
    async deleteNursingRecordDetail(row) {
      //是否仅本人操作
      let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
        this.user.userID,
        false,
        true,
        row.performPersonID
      );
      if (!saveButtonFlag) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            id: row.nursingRecordDetailID,
          };
          DeletePatientNursingRecordDetail(params).then((result) => {
            if (_this._common.isSuccess(result)) {
              _this._showTip("success", "删除成功！");
              _this.selectPatientData(_this.patient);
            }
          });
        }
      });
    },

    /**
     * @description: 补录选择对应的病区科室以及床位信息赋值
     * @param {*} val -组件中的返回的值
     * @return {*}void
     */
    selectStationDepartmentBed(val) {
      this.stationDepartmentBed.departmentListID = val.departmentListID;
      this.stationDepartmentBed.stationID = val.stationID;
      this.stationDepartmentBed.bedNumber = val.bedNumber;
      this.stationDepartmentBed.bedId = val?.bedId ?? val?.bedID;
    },
  },
};
</script>
<style lang="scss">
.nursingRecord-Detail {
  height: 100%;
  hr {
    margin: 0 17px 15px 17px;
  }
  .base-header {
    .data-select {
      width: 120px;
    }
    .btn {
      float: right;
    }
  }

  .dialog-style {
    width: 650px;
    height: 45% !important;
    .el-row {
      height: 40px;
      line-height: 40px;
      margin-bottom: 5px;
      text-align: right;
      .col-label {
        padding-left: 12px;
      }
      .col-select {
        padding-left: 5px;
      }
    }
  }
}
</style>
