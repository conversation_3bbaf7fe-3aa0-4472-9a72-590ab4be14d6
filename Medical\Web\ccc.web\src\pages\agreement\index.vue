<!--
 * FilePath     : \src\pages\agreement\index.vue
 * Author       : LX
 * Date         : 2020-09-12 08:06
 * LastEditors  : 张现忠
 * LastEditTime : 2023-05-07 11:07
 * Description  : 
-->
<template>
  <div class="agree-ment">
    <el-table stripe :data="tableData" style="width: 100%" border>
      <el-table-column prop="documentCategory" label="类别" width="180"></el-table-column>
      <el-table-column prop="documentName" label="项目" min-width="180"></el-table-column>
      <el-table-column label="操作" width="70" align="center">
        <template slot-scope="scope">
          <el-tooltip content="打印">
            <i class="iconfont icon-print" @click="getDocument(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="noticeVisible"
      custom-class="no-footer"
      title="入院告知单"
      fullscreen
    >
      <document-sign :iframeType="'application/x-google-chrome-pdf'" :signParams="notificationParams"></document-sign>
    </el-dialog>
  </div>
</template>

<script>
import { GetEmrDocumentList } from "@/api/Consent";
import { mapGetters } from "vuex";
import documentSign from "@/components/DocumentSign";
export default {
  components: {
    documentSign,
  },
  computed: {
    ...mapGetters({
      inpatientInfo: "getPatientInfo",
    }),
  },
  watch: {
    inpatientInfo(newPatient) {
      if (newPatient) {
        this.getTableData();
      } else {
        this.tableData = [];
      }
    },
  },
  data() {
    return {
      tableData: [],
      noticeVisible: false,
      //告知书显示和签名的参数
      notificationParams: undefined,
    };
  },
  mounted() {
    // 设置可切换病人
    this._sendBroadcast("setPatientSwitch", true);
    this.getTableData();
  },
  methods: {
    getTableData() {
      GetEmrDocumentList().then((res) => {
        if (this._common.isSuccess(res)) {
          this.tableData = res.data;
        }
      });
    },
    getDocument(value) {
      this.noticeVisible = true;
      this.notificationParams = {
        inpatientID: this.inpatientInfo.inpatientID,
        emrDocumentID: value.emrDocumentID,
      };
    },
  },
};
</script>
<style lang="scss">
.icon-print {
  color: #ff6700;
}
</style>