<!--
 * FilePath     : \src\pages\transferHisCommon\transferTestLabelPrint.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 石高阳
 * LastEditTime : 2020-10-20 14:12
 * Description  : 串检验条码打印
--> 
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { hisHisCommonUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
    }),
  },
  created() {
    this.url = hisHisCommonUrl() + "testLabelPrint?token=" + this.token;
  },
};
</script>
