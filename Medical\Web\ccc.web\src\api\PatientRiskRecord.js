/*
 * FilePath     : \src\api\PatientRiskRecord.js
 * Author       : 苏军志
 * Date         : 2022-01-10 10:44
 * LastEditors  : 曹恩
 * LastEditTime : 2025-06-17 08:22
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientRiskRecord";

export const urls = {
  GetSourceRiskRecord: baseUrl + "/GetSourceRiskRecord",
  GetRiskAssessView: baseUrl + "/GetRiskAssessView",
  GetPatientRiskDetailSource: baseUrl + "/GetPatientRiskDetailSource",
};
// 获取风险数据
export const GetSourceRiskRecord = (params) => {
  return http.post(urls.GetSourceRiskRecord, params);
};
// 获取风险评估模板
export const GetRiskAssessView = (params) => {
  return http.get(urls.GetRiskAssessView, params);
};
// 获取患者风险来源信息
export const GetPatientRiskDetailSource = (params) => {
  return http.post(urls.GetPatientRiskDetailSource, qs.stringify(params));
};
