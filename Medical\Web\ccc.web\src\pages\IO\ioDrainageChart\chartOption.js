/*
 * FilePath     : \src\pages\IO\ioDrainageChart\chartOption.js
 * Author       : 来江禹
 * Date         : 2023-06-12 09:08
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-08-05 16:23
 * Description  :
 * CodeIterationRecord:
 */
export const grid = {
  height: "auto",
  top: 60,
  bottom: 5,
  right: 10,
  left: 10,
};
export const extend = {
  series: {
    type: "line",
    connectNulls: true,
    smooth: false,
    symbolSize: 10
  }
};
export const tooltip = {
  trigger: "axis",
  confine: true,
  axisPointer: {
    animation: false
  },
  formatter: function (params) {
    var result = "";
    for (var i = 0; i < params.length; i++) {
      // 判断当前 series 中数据是否为空
      if (!params[i].data[1]) {
        continue;
      }
      // 自定义 tooltip 内容
      result +=
        params[i].marker +
        params[i].seriesName +
        ": " +
        params[i].value[1] +
        "<br>";
    }
    return result;
  },
};
export const setIODrainageChartOption = (chart, _this, data) => {
  let yTitleFontSize = _this.convertPX(12);
  let yAxisLine = {
    show: true,
    linesStyle: {
      width: _this.convertPX(1)
    }
  };
  let yAxisTick = {
    show: true,
    linesStyle: {
      width: _this.convertPX(1)
    }
  };
  let yAxisLabel = {
    show: true,
    fontWeight: "bold",
    fontSize: _this.convertPX(13),
    margin: _this.convertPX(8),
    padding: [0, 0, 0, 4]
  };
  let symbolSize = _this.convertPX(8);
  let fontSize = _this.convertPX(15);
  let seriesLabel = {
    show: false,
    position: "top",
    fontWeight: "bold",
    fontSize: fontSize,
  };
  let seriesOptions = {
    type: "line",
    connectNulls: true,
    smooth: false,
    showAllSymbol: true,
    label: seriesLabel,
    symbolSize: symbolSize,
  };
  let colors = [
    "#1f77b4", "#ff7f0e", "#2ca02c", "#9467bd", "#8c564b", "#e377c2", "#bcbd22", "#17becf", "#9edae5",
    "#aec7e8", "#ffbb78", "#98df8a", "#ff9896", "#c5b0d5", "#c49c94", "#f7b6d2", "#c7c7c7", "#dbdb8d"
  ]
  let series = [];
  if (data && data.series) {
    data.series.forEach((element, index) => {
      if (element === 0) {
        series.push({ yAxisIndex: element, ...seriesOptions, color: colors[index] });
      } else {
        series.push({ yAxisIndex: element, ...seriesOptions, color: "#FF0000" });
      }
    });
  }
  chart.setOption({
    xAxis: {
      axisLabel: {
        formatter: val => {
          return val.replace("(", "\n(");
        },
        textStyle: {
          fontWeight: "bold",
          fontSize: _this.convertPX(14)
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: "#000000",
          width: _this.convertPX(1)
        }
      }
    },
    yAxis: [
      {
        position: "left",
        type: "value",
        name: data.showCatheterStatisticsFlag ? "引流液量(ml)" : "ml",
        min: 0,
        max: data?.maxCount ?? 200,
        interval: data?.interval ?? 20,
        nameTextStyle: {
          padding: [_this.convertPX(5), data.showCatheterStatisticsFlag ? _this.convertPX(-10) : 0, _this.convertPX(5), data.showCatheterStatisticsFlag ? _this.convertPX(-30) : _this.convertPX(-45)],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        axisLine: yAxisLine,
        axisLabel: yAxisLabel,
        axisTick: yAxisTick,
      },
      {
        show: data.showCatheterStatisticsFlag,
        position: "right",
        type: "value",
        name: "尿量(ml)",
        min: 0,
        max: data && data.catheterMaxCount ? data.catheterMaxCount : 500,
        interval: data && data.catheterInterval ? data.catheterInterval : 50,
        nameTextStyle: {
          padding: [_this.convertPX(5), 0, _this.convertPX(5), _this.convertPX(5)],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        axisLine: yAxisLine,
        axisLabel: yAxisLabel,
        axisTick: yAxisTick,
      }
    ],
    series: series
  });
};