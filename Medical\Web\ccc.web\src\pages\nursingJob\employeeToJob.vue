<!--
 * FilePath     : \src\pages\nursingJob\employeeToJob.vue
 * Author       : 郭鹏超
 * Date         : 2020-07-02 16:13
 * LastEditors  : xml
 * LastEditTime : 2021-08-05 20:19
 * Description  : 人员岗位配置
--> 
<template>
  <base-layout class="employee-job">
    <div class="employee-job-top" slot="header">
      <label>班次日期：</label>
      <el-date-picker
        v-model="shiftDate"
        :clearable="false"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        @change="getDeptJobView"
        class="top-picker"
      ></el-date-picker>
      <label>班别：</label>
      <el-select class="top-select" placeholder="班别" v-model="currentShift" @change="getDeptJobView">
        <el-option v-for="item in shiftList" :key="item.shift" :label="item.shiftName" :value="item.shift"></el-option>
      </el-select>
      <label>岗位检索:</label>
      <!-- <el-input class="top-input" v-model="searchInfo" placeholder="岗位名称" @input="searchJob"></el-input> -->
      <el-input class="top-input" @keyup.enter.native="searchJob" v-model="searchInfo" placeholder="请输入岗位名称">
        <i @click="searchJob" slot="append" class="iconfont icon-search"></i>
      </el-input>
      <el-button
        class="edit-button"
        icon="iconfont icon-create"
        @click="createAttendanceByJob()"
        :disabled="createDisable"
      >
        生成派班
      </el-button>
      <el-button
        type="primary"
        icon="iconfont icon-bring-into"
        class="top-button"
        @click="bringYesterday()"
        :disabled="bringDisable"
      >
        带入前日
      </el-button>
    </div>
    <el-table
      v-loading="loadingData"
      element-loading-text="加载中……"
      :data="empJobs"
      stripe
      border
      class="bed-table"
      height="100%"
    >
      <el-table-column
        prop="jobGroupName"
        label="岗位分组"
        header-align="center"
        width="140px"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="jobName"
        label="岗位名称"
        header-align="center"
        width="140px"
        align="center"
      ></el-table-column>
      <el-table-column label="床位" header-align="center" align="left">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.beds" :key="index">
            <bed :type="'jobBed'" :bed="item"></bed>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="主责护士" header-align="center" align="center" width="140px">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.firstCarePriority" :key="index" style="margin: 0 15px">
            <bed :type="'people'" :bed="item" @getBedIDForDel="deleteEmpJob(scope.row.deptmentJobID, item, 1)"></bed>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="次责护士" header-align="center" align="center" width="140px">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.secondCarePriority" :key="index" style="margin: 0 15px">
            <bed :type="'people'" :bed="item" @getBedIDForDel="deleteEmpJob(scope.row.deptmentJobID, item, 2)"></bed>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" header-align="center" width="100px" align="center">
        <template slot-scope="scope">
          <el-tooltip content="主责">
            <i class="iconfont icon-first-attendance" @click="modifyFirst(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="次责">
            <i class="iconfont icon-second-attendance" @click="modifySecond(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      v-loading="dialogLoading"
      element-loading-text="加载中……"
      :title="name"
      :visible.sync="showDialog"
      custom-class="enployee-job-dialog"
      v-if="showDialog"
    >
      <div class="bed-dialog">
        <bed
          v-for="(item, index) in dicts"
          :bed="item"
          :key="index"
          @getBedNumberAndBedIDForSel="changeJobStatue(index)"
          :type="'people'"
        ></bed>
      </div>
      <div slot="footer">
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="saveEmployeeJob">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import {
  DeleteEmployeeJob,
  SaveEmployeeJob,
  CreateAttendanceByJob,
  GetDeptJobView,
  SaveSingleEmployeeJob,
  DeleteEmployeeJobByParam,
  BringYesterDay,
} from "@/api/EmployeeJob";
import { GetNurse } from "@/api/User";
import { GetAttendanceByNurseShift } from "@/api/Attendance.js";
import { GetNowStationShiftData } from "@/api/StationShift";
import bed from "@/components/bed/index.vue";
import BaseLayout from "@/components/BaseLayout";
export default {
  data() {
    return {
      empJobs: [],
      empJobsData: [],
      nurseList: [],
      dicts: [],
      searchInfo: "",
      showDialog: false,
      dialogLoading: false,
      dialogLoadingText: "",
      shiftList: [],
      shiftDate: undefined,
      currentShift: undefined,
      carePriority: 0,
      currentDeptJobView: undefined,
      selectedUser: 0,
      createDisable: false,
      bringDisable: false,
      loadingData: false,
      loadingText: "数据加载中",
    };
  },
  components: {
    bed,
    BaseLayout,
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.stationID = this.$route.query.stationID;
      this.loadingData = true;
      await this.getStationShifts();
      await this.getDeptJobView();
      await this.getNurses();
      this.loadingData = false;
    },
    //获取班别日期
    async getStationShifts() {
      await GetNowStationShiftData().then((result) => {
        if (this._common.isSuccess(result)) {
          this.shiftList = result.data.stationShifts;
          this.shiftDate = this._datetimeUtil.formatDate(result.data.shiftDate, "yyyy-MM-dd");
          this.currentShift = result.data.nowShift.shift;
        }
      });
    },
    //获取科室人员岗位
    async getDeptJobView() {
      let params = {
        stationID: this.stationID,
        shiftDate: this.shiftDate,
        shift: this.currentShift,
      };
      await GetDeptJobView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.empJobs = result.data;
          this.dataTransfer();
          this.empJobsData = result.data;
        }
      });
    },
    //获取科室护士列表
    async getNurses() {
      this.nurseList = [];
      let params = {
        attendaceDate: this.shiftDate,
        shiftID: this.shiftList.find((item) => item.shift == this.currentShift).id,
      };
      await GetAttendanceByNurseShift(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let datainfo = result.data;
          datainfo.forEach((item) => {
            const RowItem = {
              id: item.employeeBasicID,
              name: item.employeeName,
            };
            this.nurseList.push(RowItem);
          });
        }
      });
    },
    //岗位检索
    searchJob() {
      if (!this.searchInfo) {
        this.empJobs = this.empJobsData;
        return;
      }
      this.empJobs = [];
      this.empJobsData.forEach((element) => {
        if (element.jobName.indexOf(this.searchInfo) > -1) {
          this.empJobs.push(element);
        }
      });
    },
    //表格主责护士按钮
    modifyFirst(value) {
      this.dicts = [];
      this.carePriority = 1;
      for (let i = 0; i < this.nurseList.length; i++) {
        let job = {
          isDel: false,
          bedNumber: this.nurseList[i].name,
          bedID: this.nurseList[i].id,
        };
        let isSel = false;
        let isAdd = true;
        for (let j = 0; j < value.secondCarePriority.length; j++) {
          if (value.secondCarePriority[j].bedID == this.nurseList[i].id) {
            isAdd = false;
            job.isDel = true;
            job.showDelete = false;
            this.dicts.push(job);
            break;
          }
        }
        if (isAdd) {
          for (let j = 0; j < value.firstCarePriority.length; j++) {
            if (value.firstCarePriority[j].bedID == this.nurseList[i].id) {
              isSel = true;
              break;
            }
          }
          job.isSel = isSel;
          this.dicts.push(job);
        }
      }
      this.showConfirm = "";
      this.hasBedJob = [];
      this.name = "岗位-" + value.jobName;
      this.showDialog = true;
      this.currentDeptJobView = value;
    },
    //删除人员岗位
    deleteEmpJob(jobID, user, carePriority) {
      let userID = user.bedID;
      let params = {
        shiftDate: this.shiftDate,
        shift: this.currentShift,
        carePriority: carePriority,
        empID: userID,
        jobID: jobID,
      };
      this._confirm("确认要删除此岗位吗？", undefined, (flag) => {
        if (flag) {
          DeleteEmployeeJobByParam(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              this.getDeptJobView();
            }
          });
        }
      });
    },
    //表格次责护士按钮
    modifySecond(value) {
      this.dicts = [];
      this.carePriority = 2;
      for (let i = 0; i < this.nurseList.length; i++) {
        let job = {
          isDel: false,
          bedNumber: this.nurseList[i].name,
          bedID: this.nurseList[i].id,
        };
        let isSel = false;
        let isAdd = true;
        for (let j = 0; j < value.firstCarePriority.length; j++) {
          if (value.firstCarePriority[j].bedID == this.nurseList[i].id) {
            isAdd = false;
            job.isDel = true;
            job.showDelete = false;
            this.dicts.push(job);
            break;
          }
        }
        if (isAdd) {
          for (let j = 0; j < value.secondCarePriority.length; j++) {
            if (value.secondCarePriority[j].bedID == this.nurseList[i].id) {
              isSel = true;
              break;
            }
          }
          job.isSel = isSel;
          this.dicts.push(job);
        }
      }
      this.showConfirm = "";
      this.hasBedJob = [];
      this.name = "岗位-" + value.jobName;
      this.showDialog = true;
      this.currentDeptJobView = value;
    },
    //使用床位组件显示岗位，进行数据转换
    dataTransfer() {
      for (let i = 0; i < this.empJobs.length; i++) {
        const item = this.empJobs[i];
        let firstList = [];
        for (let j = 0; j < item.firstCarePriority.length; j++) {
          const first = item.firstCarePriority[j];
          firstList.push({
            isDel: true,
            bedNumber: first.userName,
            bedID: first.employeeID,
          });
        }
        item.firstCarePriority = firstList;

        let secondList = [];
        for (let j = 0; j < item.secondCarePriority.length; j++) {
          const second = item.secondCarePriority[j];
          secondList.push({
            isDel: true,
            bedNumber: second.userName,
            bedID: second.employeeID,
          });
        }
        item.secondCarePriority = secondList;
      }
      for (let i = 0; i < this.empJobs.length; i++) {
        let empJob = this.empJobs[i];
        if (empJob.employeeToJobs && empJob.employeeToJobs.length > 0) {
          empJob.JobList = [];
          for (let j = 0; j < empJob.employeeToJobs.length; j++) {
            let job = empJob.employeeToJobs[j];
            empJob.JobList.push({
              isDel: true,
              bedNumber: job.jobName,
              bedID: job.employeeJobID,
            });
          }
        }
      }
    },
    //修改岗位状态
    changeJobStatue(index) {
      for (let i = 0; i < this.dicts.length; i++) {
        let item = this._common.clone(this.dicts[i]);
        if (index == i) {
          this.$set(item, "isSel", true);
          this.$set(this.dicts, i, item);
          this.selectedUser = item.bedID;
        } else {
          this.$set(item, "isSel", false);
          this.$set(this.dicts, i, item);
        }
      }
    },
    //保存人员岗位
    saveEmployeeJob() {
      let params = {
        shiftDate: this.shiftDate,
        shift: this.currentShift,
        carePriority: this.carePriority,
        empID: this.selectedUser,
        jobID: this.currentDeptJobView.deptmentJobID,
      };
      return SaveSingleEmployeeJob(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.showDialog = false;
          this.getDeptJobView();
        }
      });
    },

    //生成派班
    createAttendanceByJob() {
      let res = this.shiftList.filter((m) => m.shift == this.currentShift);
      if (!res || res.length == 0) {
        return;
      }
      this._confirm("确认要生成【" + this.shiftDate + res[0].shiftName + "】的派班吗？", "派班确认", (flag) => {
        if (flag) {
          this.loadingData = true;
          this.createDisable = true;
          let params = {
            shiftDate: this.shiftDate,
            shift: this.currentShift,
          };
          CreateAttendanceByJob(params).then((result) => {
            if (result.code === 1) {
              this.loadingData = false;
              this._showTip("success", "保存成功！");
              this.showDialog = false;
              this.createDisable = false;
            } else {
              this.loadingData = false;
              this._showTip("warning", "保存失败！");
              this.createDisable = false;
            }
          });
        }
      });
    },
    //带入前日派班
    bringYesterday() {
      this._confirm("确认要带入前日的人员岗位配置吗", "带入前日确认", (flag) => {
        if (flag) {
          this.loadingData = true;
          this.bringDisable = true;
          let params = {
            shiftDate: this.shiftDate,
            shift: this.currentShift,
          };
          BringYesterDay(params)
            .then((result) => {
              if (result.code === 1) {
                this.loadingData = false;
                this._showTip("success", "保存成功！");
                this.showDialog = false;
                this.bringDisable = false;
              } else {
                this.loadingData = false;
                this._showTip("warning", "保存失败！");
                this.bringDisable = false;
              }
            })
            .then(() => this.getDeptJobView());
        }
      });
    },
  },
};
</script>
<style lang="scss" >
.employee-job {
  height: 100%;
  .employee-job-top {
    line-height: 50px;
    background-color: #fff;
    padding: 0;
    label {
      margin-left: 20px;
    }
    .top-picker {
      width: 140px;
    }
    .top-select {
      width: 100px;
    }
    .top-input {
      width: 170px;
      .el-input-group__append {
        padding: 0 10px;
      }
      i {
        color: #8cc63e;
      }
    }
    .top-button,
    .edit-button {
      float: right;
      margin-top: 10px;
      margin-right: 15px;
      span {
        vertical-align: text-top;
      }
    }
  }
  .enployee-job-dialog {
    width: 785px;
    height: 50%;
    .bed-dialog {
      width: 100%;
      height: 100%;
    }
  }
}
</style>