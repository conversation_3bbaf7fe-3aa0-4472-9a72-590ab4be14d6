<!--
 * FilePath     : \src\autoPages\flap\index.vue
 * Author       : 杨欣欣
 * Date         : 2023-06-07 17:27
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-01-06 09:56
 * Description  : 皮瓣护理
 * CodeIterationRecord: 3527-作为护理人员，我需要增加皮瓣护理及电子病历，以便提供皮瓣观察专项护理 -杨欣欣
-->
<template>
  <specific-care
    class="flap-record"
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    :editFlag="showEditButton"
    :careMainAddFlag="careMainAddFlag"
    :drawerSize="refillFlag ? '80%' : ''"
    :previewFlag="previewFlag"
    @mainAdd="recordAdd"
    @maintainAdd="careMainAdd"
    @save="saveFlap"
    @cancel="drawerClose"
    @getHandOverFlag="receiveEmit($event, 'handOverArr', 1)"
    @getInformPhysicianFlag="receiveEmit($event, 'informPhysicianArr', 1)"
    @getNursingRecordFlag="receiveEmit($event, 'bringToNursingRecordArr', 1)"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <!-- 主记录 -->
    <div slot="main-record">
      <packaging-table
        ref="recordTable"
        v-model="recordTableData"
        :headerList="recordTableHeaderList"
        @rowClick="recordClick"
      >
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope">
          <el-tooltip content="修改">
            <div @click.stop="recordAdd(scope.row)" class="iconfont icon-edit"></div>
          </el-tooltip>
          <!-- <el-tooltip content="停止">
            <div @click.stop="recordEnd(scope.row)" :class="['iconfont', { 'icon-stop': !scope.row.endDate }]"></div>
          </el-tooltip> -->
          <el-tooltip content="删除">
            <div @click.stop="deleteRecord(scope.row)" class="iconfont icon-del"></div>
          </el-tooltip>
          <el-tooltip content="皮瓣护理图片预览" v-if="scope.row.imgPreviewButtonFlag">
            <div @click.stop="openImgPreview(scope.row)" class="iconfont icon-img-preview"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <packaging-table ref="maintainTable" v-model="careMainTableData" :headerList="careMainTableHeaderList">
        <!-- 评估类型 插槽 -->
        <div slot="assessType" slot-scope="row">
          <span v-if="row.row.recordsCode.includes('Start')">开始评估</span>
          <span v-else-if="row.row.recordsCode.includes('End')">结束评估</span>
          <span v-else>例行评估</span>
        </div>
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope">
          <el-tooltip content="修改" placement="top" v-if="!scope.row.recordsCode.includes('Start')">
            <div class="iconfont icon-edit" @click="careMainAdd(scope.row)"></div>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" v-if="!scope.row.recordsCode.includes('Start')">
            <div class="iconfont icon-del" @click="deleteCareMain(scope.row)"></div>
          </el-tooltip>
          <el-tooltip content="皮瓣护理图片预览" v-if="scope.row.imgPreviewButtonFlag">
            <div class="iconfont icon-img-preview" @click="openImgPreview(scope.row)"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 抽屉 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
    >
      <div slot="header">
        <span class="label">日期：</span>
        <el-date-picker
          class="date-picker"
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
        />
        <el-time-picker
          class="time-picker"
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
        />
        <station-selector v-model="stationID" label="病区:" :width="convertPX(420) + 'px'" />
        <dept-selector label="" :width="convertPX(350) + 'px'" v-model="departmentListID" :stationID="stationID" />
      </div>
      <body-and-assess-layout :link="link" :bodyShowFlag="recordsCode && recordsCode.includes('Start')">
        <tabs-layout
          ref="tabsLayout"
          :template-list="templateData"
          @button-click="buttonClick"
          @change-values="receiveEmit($event, 'assessData')"
          @checkTN="receiveEmit($event, 'checkTNFlag')"
          @upload-img="receiveEmit($event, 'flapImages')"
        />
      </body-and-assess-layout>
    </base-layout>
    <!-- 弹窗 -->
    <div class="drawer-dialog" slot="drawer-dialog">
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonName"
        :visible.sync="showButtonDialog"
        fullscreen
        custom-class="no-footer specific-care-view"
        :append-to-body="true"
      >
        <iframe v-if="showButtonDialog" ref="buttonDialog" scrolling="no" frameborder="0" width="100%" height="99%" />
      </el-dialog>
    </div>
    <div slot="drawer-dialog">
      <el-dialog :title="imgPreviewTitle" :visible.sync="showImgPreview" fullscreen>
        <img-preview :imgPreviewData="imgPreviewData" />
      </el-dialog>
    </div>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import { mapGetters } from "vuex";
import imgPreview from "@/components/imgPreview";
import bodyAndAssessLayout from "@/components/bodyAndAssessLayout";
import {
  GetFlapRecordList,
  GetFlapCareMainListByRecordID,
  GetFlapRecordsCodeInfo,
  GetFlapAssessView,
  GetImgPreviewData,
  AddFlapRecord,
  AddFlapCare,
  AddFlapEnd,
  UpdateFlapRecord,
  UpdateFlapCare,
  DeleteFlapByID,
  DeleteFlapCare,
} from "@/api/flap";
import { GetBringToShiftSetting } from "@/api/Setting";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { GetBringToNursingRecordFlagSetting } from "@/api/SettingDescription";
export default {
  components: {
    specificCare,
    tabsLayout,
    baseLayout,
    packagingTable,
    imgPreview,
    stationSelector,
    deptSelector,
    bodyAndAssessLayout,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      loading: false,
      patient: undefined,
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      //主记录变量
      recordTableData: [],
      recordID: undefined,
      currentRecord: undefined,
      //维护记录变量
      careMainTableData: [],
      careMainID: undefined,
      // 新增修改标记
      isAdd: true,
      //弹窗变量
      drawerLoading: false,
      drawerLoadingText: undefined,
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      userID: undefined,
      departmentListID: undefined,
      templateData: [],
      recordsCodeInfo: {},
      assessData: [],
      checkTNFlag: true,
      recordsCode: undefined,
      handOverArr: [true, false],
      informPhysicianArr: [true, false],
      bringToNursingRecordArr: [true, false],
      settingHandOver: false,
      settingNursingRecord: false,
      //路由变量
      patientScheduleMainID: undefined,
      assessMainID: undefined,
      sourceID: undefined,
      sourceType: undefined,
      refillFlag: "",
      showEditButton: true,
      // 动态表头
      recordTableHeaderList: [],
      careMainTableHeaderList: [],
      // 维护记录新增按钮开关
      careMainAddFlag: true,
      // 专项按钮跳转相关参数
      showButtonDialog: false,
      buttonName: "",
      // 人体图
      link: "",
      // 组件回传图片数组
      flapImages: [],
      showImgPreview: false,
      imgPreviewData: {},
      imgPreviewTitle: "",
      previewFlag: false,
    };
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = "";
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.refillFlag = "*";
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    async init() {
      localStorage.setItem("selectPart", JSON.stringify({}));
      localStorage.setItem("bodyPart", JSON.stringify({}));
      this.patientScheduleMainID = this.$route.query?.patientScheduleMainID;
      this.assessMainID = this.$route.query.num;
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
      const isLink = this.patientScheduleMainID || this.assessMainID || this.sourceID || this.sourceType;
      // 跳转页面不允许切换病人
      this._sendBroadcast("setPatientSwitch", !isLink);
      await this.getRecordTableData();
      this.fixTable();
      await this.getTableHeaderList("Record", "recordTableHeaderList");
      await this.getTableHeaderList("Maintain", "careMainTableHeaderList");
      this.getBringHandOverSetting();
      this.getBringToNursingRecordSetting();
    },
    /**
     * description: 获取维护记录动态列
     * param {*} classSub 副类别
     * param {*} headerList 要赋值的变量
     * return {*}
     */
    async getTableHeaderList(classSub, headerList) {
      let params = {
        fileClassID: 114,
        fileClassSub: classSub,
        useDescription: "1||Table",
        newSourceFlag: true,
      };
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this[headerList] = res.data;
        }
      });
    },
    /**
     * description: 弹窗保存按钮点击
     * param {*}
     * return {*}
     */
    async saveFlap() {
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      if (this.recordsCodeInfo.recordsCode.includes("Start")) {
        await this.saveRecord();
      }
      if (this.recordsCodeInfo.recordsCode.includes("Maintain")) {
        await this.saveCareMain();
      }
      if (this.recordsCodeInfo.recordsCode.includes("End")) {
        await this.saveEnd();
      }
      this.drawerLoading = false;
      this.drawerLoadingText = "";
    },

    /*-------------主记录CRUD-------------*/

    /**
     * description: 获取主记录数据
     * param {*}
     * return {*}
     */
    async getRecordTableData() {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      this.loading = true;
      //获取病人主记录列表
      await GetFlapRecordList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.recordTableData = result.data;
          this.$nextTick(() => this.$refs.recordTable?.doLayout());
        }
      });
    },

    /**
     * description: 主记录新增修改
     * param {*} item 要保存的数据
     * return {*}
     */
    async recordAdd(item) {
      this.recordsCode = "FlapStart";
      this.openOrCloseDrawer(true, "皮瓣护理主记录");
      if (item) {
        //权限检核
        await this.checkAuthor(item.patientFlapRecordID, "PatientFlapRecord", item.userID);
        if (this.refillFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            item.userID
          );
          this.previewFlag = !saveButtonFlag;
        }
        this.isAdd = false;
        this.assessDate = this._datetimeUtil.formatDate(item.startDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.startTime, "hh:mm");
        this.stationID = item.stationID;
        this.departmentListID = item.departmentListID;
        this.recordID = item.patientFlapRecordID;
        this.userID = item.userID;
        localStorage.setItem("selectPart", item.bodyPartJson);
      } else {
        this.showEditButton = true;
        this.isAdd = true;
        localStorage.setItem("selectPart", "{}");
        localStorage.setItem("bodyPart", "{}");
        this.careMainID = "temp_" + this._common.guid();
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.userID = this.user.userID;
        this.recordID = undefined;
      }
      this.link = `../../static/body/mobileBody.html?type=CommonMulti&recordsCode=${this.recordsCode}&gender=${this.patient.genderCode}`;
      this.$set(this.handOverArr, 1, item?.bringToShift ?? this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, !!item?.informPhysician);
      this.$set(this.bringToNursingRecordArr, 1, item?.bringToNursingRecord ?? this.settingNursingRecord);
      await this.getAssessTemplate();
    },

    /**
     * description: 主记录保存
     * param {*}
     * return {*}
     */
    async saveRecord() {
      // 数据验证
      if (!this.saveCheck()) {
        return;
      }
      let saveDataView = this.createRecordSaveView();
      let req = this.isAdd ? AddFlapRecord(saveDataView) : UpdateFlapRecord(saveDataView);

      await req.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.isAdd ? "新增" : "修改"}成功！`);
        }
      });
      this.openOrCloseDrawer(false);
      this.fixTable();
      this.getRecordTableData();
    },
    /**
     * description: 创建主记录保存请求View
     * param {*}
     * return {*}
     */
    createRecordSaveView() {
      let saveData = {
        PatientFlapCareMainID: this.getCareMainID(),
        Details: this.getDetails(),
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        NursingLevel: this.patient.nursingLevelCode,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        PatientScheduleMainID: this.patientScheduleMainID,
        NumberOfAssessment: 1,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        Images: this.getImages(),
        Record: {
          PatientFlapRecordID: this.recordID,
          InpatientID: this.patient.inpatientID,
          StartDate: this.assessDate,
          StartTime: this.assessTime,
          StationID: this.stationID,
          DepartmentListID: this.departmentListID,
          AddEmployeeID: this.userID,
          BodyPartJson: localStorage.getItem("bodyPart"),
          BodyPartName: this.getBodyPartName(localStorage.getItem("bodyPart")),
          SameBodyPartSort: this.isAdd
            ? this.recordTableData.filter(({ bodyPartJson }) => bodyPartJson == localStorage.getItem("bodyPart"))
                .length + 1
            : undefined,
          RefillFlag: this.refillFlag,
        },
      };
      return saveData;
    },
    /**
     * description: 保存检核
     * param {*}
     * return {*}
     */
    saveCheck() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return false;
      }
      if (!this.departmentListID) {
        this._showTip("warning", "请选择科室");
        return false;
      }
      if (this.recordsCodeInfo.recordsCode.includes("Start")) {
        const selectPart = localStorage.getItem("bodyPart");
        if (selectPart == "{}" || selectPart == "[]") {
          this._showTip("warning", "请选择部位！");
          return false;
        }
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      return true;
    },

    /**
     * description: 主记录停止
     * param {*} item 当前主记录行数据
     * return {*}
     */
    async recordEnd(recordRow) {
      //是否仅本人操作
      this.showEditButton = true;

      this.isAdd = true;
      this.currentRecord = recordRow;
      this.recordsCode = "FlapEnd";
      this.recordID = recordRow.patientFlapRecordID;
      this.careMainID = "temp_" + this._common.guid();
      this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
      this.stationID = this.patient.stationID;
      this.departmentListID = this.patient.departmentListID;
      this.bedID = this.patient.bedID;
      this.bedNumber = this.patient.bedNumber;

      this.$set(this.handOverArr, 1, this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, this.settingNursingRecord);
      this.openOrCloseDrawer(true, "皮瓣护理停止");
      await this.getAssessTemplate();
    },

    /**
     * description: 主记录停止保存
     * param {*}
     * return {*}
     */
    async saveEnd() {
      if (!this.saveCheck()) {
        return;
      }
      let saveData = {
        RecordID: this.recordID,
        CareMainID: this.getCareMainID(),
        PatientScheduleMainID: this.patientScheduleMainID,
        NursingLevel: this.patient.nursingLevelCode,
        StationID: this.stationID,
        DepartmentListID: this.departmentListID,
        BedID: this.bedID,
        BedNumber: this.bedNumber,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        RefillFlag: this.refillFlag,
        Details: this.getDetails(),
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        Images: this.getImages(),
      };

      let req = this.isAdd ? AddFlapEnd(saveData) : UpdateFlapCare(saveData);
      await req.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.isAdd ? "停止" : "修改"}成功！`);
          this.fixTable();
          this.getRecordTableData();
        }
        this.openOrCloseDrawer(false);
      });
    },
    /**
     * description: 主记录删除
     * param {*} row 当前行数据
     * return {*}
     */
    async deleteRecord(row) {
      //权限检核
      await this.checkAuthor(row.patientFlapRecordID, "PatientFlapRecord", row.userID);
      if (!this.showEditButton) {
        return;
      }
      if (this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.userID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            RecordID: row.patientFlapRecordID,
          };
          this.loading = true;
          DeleteFlapByID(params).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              this.fixTable();
              this._showTip("success", "删除成功！");
              this.getRecordTableData();
            }
          });
        }
      });
    },

    /*-------------维护记录CRUD-------------*/
    /**
     * description: 点击选中主记录，展示维护记录
     * param {*} row 当前行数据
     * return {*}
     */
    async recordClick(row) {
      this.currentRecord = row;
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.recordTableData = [row];
        this.careMainAddFlag = !row.endDate;
        this.getCareMainTableData();
      } else {
        this.getRecordTableData();
      }
    },
    /**
     * description: 根据RecordID获取维护记录
     * param {*}
     * return {*}
     */
    async getCareMainTableData() {
      let params = {
        recordID: this.currentRecord.patientFlapRecordID,
      };
      this.loading = true;
      await GetFlapCareMainListByRecordID(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.careMainTableData = result.data;
          this.$nextTick(() => this.$refs.maintainTable?.doLayout());
        }
      });
    },

    /**
     * description: 维护记录新增修改
     * param {*} item 当前行数据
     * return {*}
     */
    async careMainAdd(item) {
      this.openOrCloseDrawer(true, "皮瓣护理维护记录");
      this.recordID = this.currentRecord.patientFlapRecordID;
      if (item) {
        //权限检核
        await this.checkAuthor(item.patientFlapCareMainID, "PatientFlapCareMain", item.userID);
        if (this.refillFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            item.userID
          );
          this.previewFlag = !saveButtonFlag;
        }
        this.isAdd = false;
        this.careMainID = item.patientFlapCareMainID;
        this.assessDate = this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.assessTime, "hh:mm");
        this.stationID = item.stationID;
        this.departmentListID = item.departmentListID;
        this.recordsCode = item.recordsCode;
        this.userID = item.userID;
      } else {
        this.showEditButton = true;
        this.isAdd = true;
        this.careMainID = "temp_" + this._common.guid();
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowDate("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.bedID = this.patient.bedID;
        this.bedNumber = this.patient.bedNumber;
        this.recordsCode = "FlapMaintain";
      }

      this.$set(this.informPhysicianArr, 1, !!item?.informPhysician);
      this.$set(this.handOverArr, 1, item?.bringToShift ?? this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, item?.bringToNursingRecord ?? this.settingNursingRecord);
      await this.getAssessTemplate();
    },

    /**
     * description: 维护记录保存
     * param {*}
     * return {*}
     */
    async saveCareMain() {
      if (!this.saveCheck()) {
        return;
      }
      let saveData = this.createCareMainSaveView();
      let req = this.isAdd ? AddFlapCare(saveData) : UpdateFlapCare(saveData);

      await req.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.isAdd ? "新增" : "修改"}成功！`);
          this.getCareMainTableData();
        }
        this.openOrCloseDrawer(false);
      });
    },
    /**
     * description: 维护记录保存model
     * param {*}
     * return {*}
     */
    createCareMainSaveView() {
      let saveData = {
        RecordID: this.currentRecord.patientFlapRecordID,
        CareMainID: this.getCareMainID(),
        PatientScheduleMainID: this.patientScheduleMainID,
        BedID: this.bedID,
        BedNumber: this.bedNumber,
        StationID: this.stationID,
        DepartmentListID: this.departmentListID,
        NursingLevel: this.patient.nursingLevelCode,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        RefillFlag: this.refillFlag,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        Details: this.getDetails(),
        Images: this.getImages(),
      };
      return saveData;
    },

    /**
     * description: 维护记录删除
     * param {*} row 删除行
     * return {*}
     */
    async deleteCareMain(row) {
      // 是否仅本人操作
      await this.checkAuthor(row.patientFlapCareMainID, "PatientFlapCareMain", row.userID);
      if (!this.showEditButton) {
        return;
      }
      if (this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.userID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this._deleteConfirm("", (flag) => {
        if (!flag) {
          return;
        }
        let param = {
          CareMainID: row.patientFlapCareMainID,
        };
        this.loading = true;
        DeleteFlapCare(param).then((result) => {
          this.loading = false;
          if (this._common.isSuccess(result)) {
            this._showTip("success", "删除成功！");
            if (row.recordsCode.includes("End")) {
              this.fixTable();
              this.getRecordTableData();
            } else {
              this.getCareMainTableData();
            }
          }
        });
      });
    },
    /**
     * description: 重置选中状态
     * param {*}
     * return {*}
     */
    fixTable() {
      this.showRecordArr = [true, false];
      this.currentRecord = undefined;
      this.careMainTableData = [];
    },

    /**
     * description: 获取评估模板
     * param {*}
     * return {*}
     */
    async getAssessTemplate() {
      this.drawerLoadingText = "加载中……";
      this.drawerLoading = true;
      let params = {
        recordsCode: this.recordsCode,
        departmentListID: this.patient.departmentListID,
      };
      await GetFlapRecordsCodeInfo(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo?.recordsCode) {
        this.openOrCloseDrawer(false);
        this._showTip("warning", "找不到评估模板！");
        return;
      }
      params = {
        recordID: this.recordID,
        careMainID: this.careMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        inpatientID: this.patient.inpatientID,
        dateOfBirth: this.patient.dateOfBirth,
        isAdd: this.isAdd,
      };
      this.templateData = [];
      await GetFlapAssessView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.templateData = result.data;
        }
      });
      this.drawerLoading = false;
    },
    /**
     * description: 评估组件按钮事件
     * return {*}
     * param {*} content 跳转明细项
     */
    buttonClick(content) {
      this.showButtonDialog = true;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        `&sourceID=${this.getCareMainID()}` +
        "&sourceType=flap" +
        "&isDialog=true";
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => this.$refs.buttonDialog.contentWindow.location.replace(url));
    },
    /**
     * description: 获取维护记录CareMainID，若为新增则截取掉头部
     * return {*} 维护记录CareMainID
     * param {*}
     */
    getCareMainID() {
      return this.careMainID?.split("_")?.[1] ?? this.careMainID;
    },
    /*-------------获取页面配置-------------*/
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "flap",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingHandOver = res.data;
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "flapAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },

    /*-------------配合保存方法-------------*/
    /**
     * description: 获取保存明细
     * param {*}
     * return {*}
     */
    getDetails() {
      let details = [];
      this.assessData.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        if (content.disableGroup != -1) {
          details.push(detail);
        }
      });
      return details;
    },
    /**
     * description: 权限检核
     * param {*} id 主记录/主表ID
     * param {*} tableName
     * return {*}
     */
    async checkAuthor(id, tableName, userID) {
      this.showEditButton = await this._common.checkActionAuthorization(this.user, userID);
      if (!this.showEditButton) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, tableName, !!this.refillFlag);
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
    },
    /*-------------专项护理组件逻辑-------------*/
    /**
     * description: 弹窗关闭
     * param {*}
     * return {*}
     */
    drawerClose() {
      this.recordID = undefined;
      this.careMainID = undefined;
      this.templateData = [];
      this.assessData = [];
      this.showTemplateFlag = false;
      this.flapImages = [];
    },
    /**
     * description: 弹窗开关
     * param {*} flag 开关动作
     * param {*} title 弹窗标题
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 回显部位名称
     * param {*} bodyPartJson 身体部位对象
     * return {*}
     */
    getBodyPartName(bodyPart) {
      const selectPart = JSON.parse(bodyPart);
      let name = "";
      if (selectPart instanceof Array) {
        // 多选
        if (!selectPart?.length) {
          return name;
        }
        selectPart.forEach((part) => {
          if (name) {
            name += "，" + part.bodyPartName;
          } else {
            name = part.bodyPartName;
          }
        });
      } else {
        // 单选
        name = selectPart.bodyPartName;
      }
      return name;
    },
    /**
     * description: 获取图片
     * return {*}
     */
    getImages() {
      const imgs = [];
      // 组装图片表数据
      for (let i = 0; i < this.flapImages.length; i++) {
        const image = this.flapImages[i].split(",");
        const type = image[0].match(/:(.*?);/)[1].split("/")[1];
        const imageInfo = {
          InpatientID: this.patient.inpatientID,
          PatientID: this.patient.patientID,
          SpecialListImageNumber: i + 1,
          SpecialListImage: image[1],
          ImageType: type,
        };
        imgs.push(imageInfo);
      }
      return imgs;
    },
    /**
     * description: 打开图片预览组件
     * param {*} patientFlapRecordID 主记录ID
     * param {*} patientFlapCareMainID 维护记录ID
     * return {*}
     */
    async openImgPreview({ patientFlapRecordID, patientFlapCareMainID }) {
      this.imgPreviewTitle = `皮瓣护理图片-${this.patient.patientName}-${this.patient.bedNumber}床`;
      this.imgPreviewData = {};
      let params = {
        inpatientID: this.patient.inpatientID,
        sourceType: "Special",
        recordID: patientFlapRecordID,
        fileClassID: 114,
        fileClassSub: "Maintain",
        careMainID: patientFlapCareMainID ?? "",
      };
      await GetImgPreviewData(params).then((result) => {
        this.showImgPreview = true;
        if (this._common.isSuccess(result)) {
          this.imgPreviewData = result.data;
        }
      });
    },
    /**
     * description: 接收子组件回传的变量，更新当前组件变量值
     * param {*} propValue 回传的变量值
     * param {*} propName 待改变成员名
     * param {*} arrayIndex 待接收变量是数组，指定数组下标，默认不传则直接赋值
     * return {*}
     */
    receiveEmit(propValue, propName, arrayIndex = undefined) {
      if (arrayIndex) {
        this[propName][arrayIndex] = propValue;
      } else {
        this[propName] = propValue;
      }
    },
  },
};
</script>

<style lang="scss" >
.flap-record {
  .drawer-content {
    .base-header {
      .date-picker {
        width: 160px;
      }
      .time-picker {
        width: 100px;
      }
    }
  }
  .icon-img-preview:before {
    color: $base-color;
  }
}
</style>