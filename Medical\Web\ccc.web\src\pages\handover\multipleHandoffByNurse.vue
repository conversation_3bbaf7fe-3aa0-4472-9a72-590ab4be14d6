<!--
 * FilePath     : \src\pages\handover\multipleHandoffByNurse.vue
 * Author       : 苏军志
 * Date         : 2021-3-12 08:07
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-09-12 09:56
 * Description  : 批量交班
 * CodeIterationRecord: 2022-11-24 3126 交班标识从弹窗移到表格列 -杨欣欣
-->

<template>
  <base-layout v-loading="loading" class="handover" headerHeight="auto" :element-loading-text="loadingText">
    <div slot="header">
      <div class="handover-header">
        <div class="level-one-1">
          <div class="level-two">
            <span>班别日期：</span>
            <el-date-picker
              @change="handoverDateChange"
              v-model="handoverDate"
              :clearable="false"
              value-format="yyyy-MM-dd 00:00:00"
              format="yyyy-MM-dd"
              type="date"
              class="date-picker"
              laceholder="选择日期"
            ></el-date-picker>
          </div>
          <div class="level-two" v-if="!normalFlag">
            <span>起始时间：</span>
            <el-date-picker
              class="date-picker"
              v-model="startDateTime"
              type="datetime"
              :clearable="false"
              placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
            ></el-date-picker>
          </div>
        </div>
        <div class="level-one-2">
          <div class="level-two-shift-select">
            <shift-selector
              width="163px"
              :stationID="stationID"
              v-model="handoverShiftID"
              @select-item="selectShiftItem"
            ></shift-selector>
          </div>
          <div class="level-two" v-if="!normalFlag">
            <span>结束时间：</span>
            <el-date-picker
              class="date-picker"
              v-model="endDateTime"
              type="datetime"
              :clearable="false"
              placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
            />
          </div>
        </div>
        <div class="level-one-3">
          <span>责任护士：</span>
          <el-select class="nurse-select" @change="getAttendancePatient" v-model="userID" placeholder="请选择">
            <el-option
              v-for="(item, index) in attendance"
              :key="index"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </div>
      </div>
    </div>
    <base-layout class="handover-wrap">
      <div slot="header">
        <el-radio-group @change="typeChange()" v-model="normalFlag">
          <el-radio :label="true">正常交班</el-radio>
          <el-radio :label="false">班内交班</el-radio>
        </el-radio-group>
        <div class="top-btn">
          <el-button type="primary" class="query-button" icon="iconfont icon-search" @click="getAttendancePatient">
            交班查询
          </el-button>
          <el-button type="primary" @click="summaryCheck" icon="iconfont icon-summation">交班汇总</el-button>
        </div>
      </div>
      <div class="handover-content" ref="handoverContent">
        <u-table
          :data="handoverData"
          border
          stripe
          :height="tableHeight"
          highlight-current-row
          :row-height="30"
          :big-data-checkbox="true"
          ref="handoverTable"
          use-virtual
          :excess-rows="90"
        >
          <u-table-column
            :selectable="selectable"
            type="selection"
            key="operate"
            width="50px"
            align="center"
          ></u-table-column>
          <u-table-column v-if="normalFlag" width="100px" label="交班标识" header-align="center">
            <template slot-scope="scope">
              <div v-for="(keySign, index) in scope.row.keySigns" :key="index">
                <el-checkbox
                  v-model="keySign.isCheck"
                  :disabled="keySign.readOnly"
                  @change="saveKeySign(keySign, scope.row.handoverID)"
                >
                  {{ keySign.signName }}
                </el-checkbox>
              </div>
            </template>
          </u-table-column>
          <u-table-column width="110" label="病人" key="bedNumber" align="center">
            <template slot-scope="scope">
              <div>{{ scope.row.bedNum + "床" }}</div>
              <div>{{ scope.row.patientName }}</div>
            </template>
          </u-table-column>
          <u-table-column v-if="normalFlag" width="80px" key="CompletionDegree" label="完成度" align="center">
            <template slot-scope="scope">
              {{ scope.row.completedSchedule + "/" + scope.row.totalSchedule }}
            </template>
          </u-table-column>
          <u-table-column key="situation" label="S-现状">
            <template slot-scope="scope" v-if="scope.row.situation">
              <div v-html="scope.row.situation"></div>
            </template>
          </u-table-column>
          <u-table-column key="background" label="B-背景">
            <template slot-scope="scope">
              <div v-html="scope.row.background"></div>
            </template>
          </u-table-column>
          <u-table-column key="assess" label="A-评估">
            <template slot-scope="scope">
              <div v-html="scope.row.assement"></div>
            </template>
          </u-table-column>
          <u-table-column key="recommendation" label="R-建议">
            <template slot-scope="scope">
              <div v-html="scope.row.recommendation"></div>
            </template>
          </u-table-column>
          <u-table-column label="人体图" width="70px" align="center" v-if="showBodyImage">
            <template slot-scope="scope">
              <body-image
                v-if="scope.row.handoverID"
                :type="'button'"
                :handoverID="scope.row.handoverID"
                :patientName="scope.row.patientName"
              ></body-image>
            </template>
          </u-table-column>
          <u-table-column prop="edit" width="100px" key="handonNurse" label="接班护士">
            <template slot-scope="scope">
              <div>{{ scope.row.attendanceNurseName }}</div>
            </template>
          </u-table-column>
          <u-table-column prop="edit" width="110" align="center" label="编辑">
            <template slot-scope="scope">
              <template v-if="scope.row.handoverID != null">
                <el-tooltip content="修改" v-if="!scope.row.handonNurse">
                  <i class="iconfont icon-edit" @click="editInClass(scope.row)"></i>
                </el-tooltip>
                <el-tooltip content="删除">
                  <i class="iconfont icon-del" v-if="!normalFlag" @click="deleteInClass(scope.row)"></i>
                </el-tooltip>
                <el-tooltip content="还原删除">
                  <i
                    class="iconfont icon-refresh"
                    v-if="!normalFlag && scope.row.restoreDeleteFlag == '*'"
                    @click="restoreDelete(scope.row)"
                  ></i>
                </el-tooltip>
              </template>
            </template>
          </u-table-column>
        </u-table>
        <!-- 修改班内交班记录 -->
        <el-dialog
          v-dialogDrag
          :close-on-click-modal="false"
          title="修改"
          fullscreen
          top="0"
          v-loading="editloading"
          v-if="openEditDialog"
          :visible.sync="openEditDialog"
          custom-class="no-footer"
        >
          <base-layout>
            <div slot="header">
              <div class="button-area">
                <el-button type="primary" icon="iconfont icon-save-button" @click="editCommit">保 存</el-button>
                <el-button class="edit-button" icon="iconfont icon-refresh" @click="back">还 原</el-button>
                <el-button class="print-button" icon="iconfont icon-back" @click="openEditDialog = false">
                  返 回
                </el-button>
              </div>
            </div>
            <handover ref="refhandover" :componentData="componentData" />
          </base-layout>
        </el-dialog>
        <!-- 正常交班 dialog-->
        <el-dialog
          v-dialogDrag
          :close-on-click-modal="false"
          v-if="normaldialog"
          fullscreen
          top="0"
          v-loading="editloading"
          title="修改"
          :visible.sync="normaldialog"
          custom-class="no-footer"
        >
          <base-layout>
            <div slot="header">
              <div class="button-area">
                <el-button type="primary" icon="iconfont icon-save-button" @click="editCommit">保 存</el-button>
                <el-button class="edit-button" icon=" iconfont icon-refresh" @click="back">还 原</el-button>
                <el-button class="print-button" icon="iconfont icon-back" @click="normaldialog = false">
                  返 回
                </el-button>
              </div>
            </div>
            <handover ref="normalhandover" :componentData="normaldialogData" />
          </base-layout>
        </el-dialog>
      </div>
    </base-layout>
  </base-layout>
</template>
<script>
import { mapGetters } from "vuex";
import {
  ReSummaryInClassHandoff,
  DeleteByID,
  UpdateInClassShiftHandoff,
  InClassHandoff,
  GetInclassHandoff,
  RestoreDelete,
} from "@/api/InClassHandover";
import { UpdateMultiShiftHandoff, GetMultiHandOff, MultiHandOffCheck, MultiHandOff } from "@/api/MultiHandover";
import { SaveKeySign } from "@/api/HandoverSign";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetHandoffAttendanceShiftNurse } from "@/api/Attendance";
import { GetNurse } from "@/api/User";
import { CheckHandoverTime } from "@/api/HandoffCheck";
import shiftSelector from "@/components/selector/shiftSelector";
import baseLayout from "@/components/BaseLayout";
import Handover from "@/components/handoverSBAR";
import bodyImage from "@/components/bodyImage";
import { GetBodyImg } from "@/api/Handover";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  components: {
    baseLayout,
    shiftSelector,
    Handover,
    bodyImage,
  },
  watch: {
    async normalFlag(newV) {
      //重置时间
      this.handoverData = [];
      this.userID = undefined;
      this.timeManage();
      this.keySign = [];
      await this.selectShift();
    },
  },
  data() {
    return {
      //使用人员
      userID: undefined,
      //病区
      stationID: undefined,
      //派班
      attendance: [],
      //交班班别序号
      handoverShiftID: "",
      //交班班别代码
      handoverShift: undefined,
      //病区班别清单
      stationShifts: [],
      //交班日期
      handoverDate: undefined,
      //交班数据
      handoverData: [],
      //加载控制变量
      loading: false,
      //打开编辑
      openEditDialog: false,
      //交班修改內容
      handoverInfo: Object,
      //交班汇整开始日期时间
      startDateTime: this._datetimeUtil.formatDate(new Date()),
      //交班汇整结束日期时间
      endDateTime: this._datetimeUtil.formatDate(new Date()),
      //保存确认信息
      saveCheckMessage: "",
      // 交班类别(true:正常交班/false:班内交班)
      normalFlag: true,
      // 正常交班dialog
      normaldialog: false,
      //正常交班弹窗数据
      normaldialogData: {},
      // 正常交班loading
      //当前修改中的病人
      onInpatient: {},
      //获取班次列表
      timeList: {},
      // 选中护士
      checkedNurse: "",
      // 护士列表
      nurseList: [],
      //交班组件数据
      componentData: {},
      // 修改还原备份
      bakeUpUpdate: {},
      //
      loadingText: "加载中……",
      // 当前病人KeySign
      keySign: [],
      editloading: false,
      //解决连续点击
      stopClickFlag: false,
      //表格高度
      tableHeight: 0,
      showBodyImage: true,
    };
  },
  created() {
    //病区
    this.stationID = this.user.stationID;
    //取得交班数据
    this.getNowStationShift();
    this.getNurse();
    this.typeChange();
    this.getShowBodyImage();
  },
  methods: {
    getShowBodyImage() {
      let params = {
        settingTypeCode: "ShiftHandoverShowBodyImage",
      };
      GetSettingSwitchByTypeCode(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.showBodyImage = response.data;
        }
      });
    },
    /**
     * description: 获取当前班别，并获取交班数据
     * return {*}
     */
    getNowStationShift() {
      GetNowStationShiftData().then((data) => {
        if (this._common.isSuccess(data)) {
          let result = data.data;
          this.timeList = data.data;
          this.handoverDate = result.shiftDate;
          //当前班别(默许班别)
          this.handoverShift = result.nowShift.shift;
        }
        this.selectShift();
      });
    },
    /**
     * description: 获取交班护士列表
     * return {*}
     */
    async selectShift() {
      //派班人员初始化
      this.handoverData = [];
      this.attendance = [];
      let params = {
        shiftDate: this.handoverDate,
        stationID: this.stationID,
        shift: this.handoverShift,
        _id: this._common.guid(),
      };
      await GetHandoffAttendanceShiftNurse(params).then((data) => {
        if (this._common.isSuccess(data)) {
          if (data.data == null || data.data.length == 0) {
            this.userID = undefined;
            return;
          }
          //派班数据
          let tempAttendance = data.data;
          // 默许列表第一位护士
          this.userID = tempAttendance[0].userID;
          tempAttendance.forEach((item) => {
            const rowItem = {
              value: item.userID,
              label: item.name,
            };
            //默许登入人员
            if (item.userID == this.user.userID) {
              this.userID = this.user.userID;
            }
            if (!this.attendance.find((data) => data.value == rowItem.value)) {
              this.attendance.push(rowItem);
            }
          });
        }
        this.getAttendancePatient();
      });
    },
    /**
     * description: 取得被派班病人
     * return {*}
     */
    async getAttendancePatient() {
      this.loading = true;
      //数据清空
      this.handoverData = [];
      if (this.normalFlag) {
        await this.handoffCheck();
      } else {
        await this.getInClassData();
      }
    },
    /**
     * description: 将病人信息组件信息中
     * return {*}
     * param {*} rowdata
     */
    async getHandoverDetail(rowdata) {
      let detailData = {
        handoverInfo: {
          situation: rowdata.situation,
          background: rowdata.background,
          recommendation: rowdata.recommendation,
          bodyPartImage: rowdata.bodyPartImage,
          assement: rowdata.assement,
        },
        riskAssessResult: [],
      };
      this.checkedNurse = rowdata.attendanceNurseID;
      return detailData;
    },
    /**
     * description: 正常批量交班查询，获取交班数据
     * return {*}
     */
    async getMultiHandOffData() {
      let params = {
        shiftDate: this.handoverDate,
        shiftID: this.handoverShiftID,
        nurserID: this.userID,
        index: Math.random(),
      };
      await GetMultiHandOff(params).then((response) => {
        if (!this.normalFlag) return;
        if (this._common.isSuccess(response)) {
          this.handoverData = response.data;
        }
      });
    },
    /**
     * description: 班内交班查询
     * return {*}
     */
    async getInClassData() {
      let params = {
        shiftDate: this.handoverDate,
        stationID: this.stationID,
        nurseID: this.userID,
        shiftID: this.handoverShiftID,
      };
      await GetInclassHandoff(params).then((data) => {
        this.loading = false;
        if (this.normalFlag) return;
        if (this._common.isSuccess(data)) {
          this.handoverData = data.data;
          let rows = this.handoverData.map((checkedRow) => ({
            row: checkedRow,
            selected: !checkedRow.handoverID ?? true,
          }));
          this.$nextTick(() => {
            this.$refs.handoverTable?.toggleRowSelection(rows);
          });
        }
      });
    },
    /**
     * description: 交班汇总检核
     * return {*}
     */
    async summaryCheck() {
      let data = this.$refs.handoverTable.getCheckboxRecords();
      if (!data || data.length === 0) {
        this._showTip("warning", "请先勾选汇总患者！");
        return;
      }
      if (this.normalFlag) {
        this.multiHandOffCheck();
      } else {
        if (this.endDateTime < this.startDateTime) {
          this._showTip("warning", "结束时间不得小于开始时间");
          return;
        }
        if (this.handoverData == undefined) {
          this._showTip("warning", "没有派班数据,无法进行交班汇整");
          return;
        }
        if (this.userID == undefined) {
          this._showTip("warning", "尚未选择主责护士");
          return;
        }
        this.saveCheckMessage = "";
        data.forEach((item) => {
          if (item.handoverID != null) {
            this.saveCheckMessage +=
              "<b class='messagefontcolor'>床位:" +
              item.bedNum +
              "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
              "病人:" +
              item.patientName +
              "</b></br>";
          }
        });
        if (this.saveCheckMessage != "") {
          this.saveCheckMessage += "已有班内交班记录,请确认是否要重新生成</br>";
          this.saveCheckMessage += "1.已修改内容会被还原为系统默许值</br>";
          this.saveCheckMessage += "2.起迄时间内无数据内容会被删除</br>";
          this.saveCheckMessage += "3.生成未产生记录</br>";
          this.$msgbox({
            title: "提示",
            message: this.saveCheckMessage,
            dangerouslyUseHTMLString: true,
            showCancelButton: false,
            confirmButtonText: "确定",
            customClass: "multihandoff-msgbox-class",
          })
            .then(() => {
              this.summary();
            })
            .catch(() => {
              this.reSummary();
            });
        } else {
          this.summary();
        }
      }
    },
    /**
     * description: 交班汇总
     * return {*}
     */
    async summary() {
      this.loadingText = "汇总中……";
      this.loading = true;
      let data = this.$refs.handoverTable.getCheckboxRecords();
      for (let i = 0; i < data.length; i++) {
        let params = {
          inpatientID: data[i].inpatientID,
          startDateTime: this.startDateTime,
          endDateTime: this.endDateTime,
          shiftID: this.handoverShiftID,
          nurseID: this.userID,
        };
        await InClassHandoff(params).then((data) => {
          this.loadingText = "加载中……";
          if (this._common.isSuccess(data)) {
            this.handoverData = data.data;
          }
        });
      }
      this.loading = false;
    },
    /**
     * description: //数据清空，重新汇整
     * return {*}
     */
    reSummary() {
      //数据清空
      this.handoverData = undefined;
      let params = {
        startDateTime: this.startDateTime,
        endDateTime: this.endDateTime,
        shiftID: this.handoverShiftID,
        nurseID: this.userID,
      };
      ReSummaryInClassHandoff(params).then((data) => {
        if (this._common.isSuccess(data)) {
          this.handoverData = data.data;
        }
      });
    },
    /**
     * description: 正常交班交班汇总
     * return {*}
     */
    async multihandoff() {
      this.loading = true;
      // let data = this.handoverData;
      let data = this.$refs.handoverTable.getCheckboxRecords();
      let bedArray = [];
      let failArray = [];
      for (let i = 0; i < data.length; i++) {
        //有接班不护士不得重新汇总
        if (data[i].handonNurse) {
          continue;
        }
        let params = {
          handoverID: data[i].handoverID,
          shiftDate: this.handoverDate,
          shiftID: this.handoverShiftID,
          nurserID: this.userID,
          inpatientID: data[i].inpatientID,
        };
        let flag = await this.singlePatientSummary(params, data[i].inpatientID);
        if (!flag) {
          failArray.push(data[i].bedNum + "床-" + data[i].patientName);
        }
        bedArray.push(data[i].bedNum);
        this.loadingText = "已完成 " + bedArray.toString() + " 床";
      }
      //修复交班汇总后内容过长时不显示滚动条的问题
      this.$refs.handoverTable.doLayout();
      let message = null;

      if (data && data.length == 0) {
        message = "无汇总人员！<br/> (已接班内容不可重复汇总。)";
      } else {
        message = "汇总完成</strong><br/>";
        message =
          message +
          (failArray.length == 0 ? "" : '交班失败：<strong  style="color:red">' + failArray.toString() + "</strong>");
      }
      this.$msgbox({
        title: "提示",
        message: message,
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        confirmButtonText: "确定",
        customClass: "multihandoff-msgbox-class",
      })
        .then(() => {})
        .catch(() => {});
      this.loading = false;
      this.loadingText = "加载中……";
    },
    /**
     * description: 编辑时传入交班数据
     * return {*}
     * param {*} rowData
     */
    async editInClass(rowData) {
      let params = {
        handOverID: rowData.handoverID,
      };
      this.loading = true;
      await GetBodyImg(params).then((response) => {
        this.loading = false;
        if (response && response.data != null) {
          rowData.bodyPartImage = response.data;
        }
      });
      this.onInpatient = rowData;
      let response = await this.getHandoverDetail(rowData);
      //普通交班
      if (this.normalFlag) {
        this.editloading = true;
        this.loading = true;
        this.normaldialogData = new Object();
        this.normaldialogData = response;
        this.asyncImportant();
        this.loading = false;
        this.normaldialog = true;
        this.editloading = false;
      } else {
        this.componentData = new Object();
        this.componentData = response;
        this.openEditDialog = true;
      }
      this.bakeUpUpdate = this._common.clone(response);
    },
    /**
     * description: 编辑结束回传修改数据
     * return {*}
     */
    editCommit() {
      //取得组件回传数据
      let tempData;
      //获取不同Tempalte 分开请求
      this.editloading = true;
      if (this.normalFlag) {
        tempData = this.$refs.normalhandover.getValue();
        if (!tempData.wordNumberFlag) {
          this._showTip("warning", "请精简内容！");
          this.editloading = false;
          return;
        }
        let params = {
          handoverID: this.onInpatient.handoverID,
          situation: tempData.situation,
          background: tempData.background,
          assement: tempData.assement,
          recommendation: tempData.recommendation,
          handonNurse: this.checkedNurse,
          shiftID: this.handoverShiftID,
        };
        return UpdateMultiShiftHandoff(params).then((response) => {
          if (this._common.isSuccess(response)) {
            this._showTip("success", "修改成功！");
            this.getAttendancePatient();
            this.normaldialog = false;
          }
          this.editloading = false;
        });
      } else {
        tempData = this.$refs.refhandover.getValue();
        if (!tempData.wordNumberFlag) {
          this._showTip("warning", "请精简内容！");
          this.editloading = false;
          return;
        }
        let params = {
          handoverID: this.onInpatient.handoverID,
          situation: tempData.situation,
          background: tempData.background,
          recommendation: tempData.recommendation,
          assement: tempData.assement,
          handonNurse: this.checkedNurse,
          shiftID: this.handoverShiftID,
        };
        return UpdateInClassShiftHandoff(params).then((response) => {
          if (this._common.isSuccess(response)) {
            this._showTip("success", "修改成功！");
            this.openEditDialog = false;
            this.getAttendancePatient();
          }
          this.editloading = false;
        });
      }
    },
    /**
     * description: 删除班内交班
     * return {*}
     * param {*} rowData
     */
    deleteInClass(rowData) {
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            handoverID: rowData.handoverID,
          };
          DeleteByID(params).then((response) => {
            if (this._common.isSuccess(response)) {
              if (response.data) {
                this._showTip("success", "删除成功");
                this.$set(rowData, "restoreDeleteFlag", "*");
                this.getAttendancePatient();
              } else {
                this._showTip("warning", "删除失败");
              }
            }
          });
        }
      });
    },
    /**
     * description: 交班汇总检核
     * return {*}
     */
    async multiHandOffCheck() {
      //防止连续点击 --GPC
      if (this.stopClickFlag) {
        return;
      }
      let params = {
        shiftDate: this.handoverDate,
        shiftID: this.handoverShiftID,
      };
      let flag = false;
      await CheckHandoverTime(params).then((response) => {
        if (this._common.isSuccess(response)) {
          flag = true;
        }
      });
      if (!flag) {
        return;
      }
      //TODO 禁止再次点击交班汇总
      this.stopClickFlag = true;
      let string = '<div >交班日期：<span style="color:red"> ' + this.handoverDate.substring(0, 10) + "</span> </div>";
      string += '<div >交班班别：<span  style="color:red" >' + this.getshiftContent() + "</span></div> ";
      string += "<div  >确定要执行吗？</div>";
      let check = await this.checkAndFilterHandoverData();
      if (check.message == null && !check.severity) {
        this.$confirm(string, "提示", {
          cancelButtonText: "取消",
          confirmButtonText: "确定",
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
          customClass: "multihandoff-msgbox-class",
        })
          .then(() => {
            //汇总逐个替换
            this.multihandoff();
          })
          .catch(() => {});
      } else if (check.severity) {
        this.$msgbox({
          title: "提示",
          message: check.message,
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
          confirmButtonText: "确定",
          customClass: "multihandoff-msgbox-class",
        })
          .then(() => {
            //汇总逐个替换
            this.multihandoff();
          })
          .catch(() => {});
      } else {
        this.$msgbox({
          title: "提示",
          message: check.message,
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
          confirmButtonText: "确定",
          customClass: "multihandoff-msgbox-class",
        })
          .then(() => {})
          .catch(() => {});
      }
    },
    /**
     * description: 单病人同步修改
     * return {*}
     */
    //
    async singlePatientSummary(params, inpatientID) {
      let flag = true;
      await MultiHandOff(params).then((response) => {
        if (this._common.isSuccess(response)) {
          //逐一替换
          // 对关键信息替换
          let handover = this.handoverData.find((handover) => {
            return handover.inpatientID == inpatientID;
          });
          if (handover) {
            this.$set(handover, "situation", response.data.situation);
            this.$set(handover, "background", response.data.background);
            this.$set(handover, "recommendation", response.data.recommendation);
            this.$set(handover, "handoverID", response.data.handoverID);
            this.$set(handover, "assement", response.data.assement);
            this.$set(handover, "bodyPartImage", response.data.bodyPartImage);
            this.$set(handover, "keySigns", response.data.keySigns);
          }
        } else {
          flag = false;
        }
      });
      return flag;
    },
    /**
     * description: 初始检核并提示信息
     * return {*}
     */
    async handoffCheck() {
      this.loading = true;
      await this.getMultiHandOffData();
      let check = await this.checkAndFilterHandoverData();
      if (check.message) {
        this.$msgbox({
          title: "提示",
          message: "<div style='color:red; max-height: 500px; overflow: auto;'>" + check.message + "</div>",
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
          confirmButtonText: "确定",
          customClass: "multihandoff-msgbox-class",
        })
          .then(() => {})
          .catch(() => {});
      }
      this.loading = false;
    },
    /**
     * description: 检核交班数据，对符合条件的数据进行勾选
     * return {*} 提示信息
     */
    async checkAndFilterHandoverData() {
      this.loading = true;
      let message = null;
      let severity = false;
      //需要默认勾选的数据
      let handoverTable = this.$refs.handoverTable;
      let checkedReocrds = handoverTable?.getCheckboxRecords();
      let params = {
        inpatientIDs: checkedReocrds.map(({ inpatientID }) => inpatientID),
        shiftDate: this.handoverDate,
        shiftID: this.handoverShiftID,
        nurserID: this.userID,
        index: Math.random(),
      };
      await MultiHandOffCheck(params).then((response) => {
        if (response && response.data != null) {
          let list = response.data;
          message = "";
          for (let index = 0; index < this.handoverData.length; index++) {
            let sucCheck = list.find((check) => check.bedNum == this.handoverData[index].bedNum);
            this.$set(this.handoverData[index], "disabled", false);
            if (sucCheck) {
              message += "<div>" + sucCheck.bedNum + "床:" + sucCheck.result + "</div>";
              if (sucCheck.severity == true) {
                handoverTable?.toggleRowSelection([{ row: this.handoverData[index], selected: true }]);
                severity = true;
              } else {
                this.$set(this.handoverData[index], "disabled", true);
              }
            } else {
              handoverTable?.toggleRowSelection([{ row: this.handoverData[index], selected: true }]);
            }
          }
        } else {
          if (response && !checkedReocrds.length) {
            //全部符合检核条件，默认全部勾选
            let rows = this.handoverData.map((checkedRow) => ({
              row: checkedRow,
              selected: !checkedRow.handoverID ?? true,
            }));
            handoverTable?.toggleRowSelection(rows);
          }
        }
        this.stopClickFlag = false;
        this.loading = false;
      });
      let result = {
        message: message,
        severity: severity,
      };
      return result;
    },
    async selectShiftItem(shiftInfo) {
      this.handoverShift = shiftInfo.shift;
      this.handoverShiftID = shiftInfo.id;
      this.timeManage();
      await this.selectShift();
    },

    /**
     * description: 还原删除成功
     * return {*}
     * param {*} row
     */
    restoreDelete(row) {
      let params = { handoverID: row.handoverID };
      RestoreDelete(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this._showTip("success", "还原删除成功！");
          this.getInClassData();
        }
      });
    },
    /**
     * description: 还原
     * return {*}
     * param {*} row
     */
    back(row) {
      if (this.normalFlag) {
        //正常交班重点同步
        this.asyncImportant();
        //刷新风险
        this.checkedNurse = this.onInpatient.attendanceNurseID;
        this.normaldialogData = this._common.clone(this.bakeUpUpdate);
      } else {
        this.componentData = this._common.clone(this.bakeUpUpdate);
      }
    },
    /**
     * description: 获取护士列表
     * return {*}
     */
    getNurse() {
      let params = { stationID: this.stationID };
      GetNurse(params).then((response) => {
        this.nurseList = response.data;
      });
    },
    /**
     * description: 正常交班重点同步
     * return {*}
     */
    asyncImportant() {
      let keySignArray = this.onInpatient.keySigns;
      if (keySignArray == null || keySignArray.length == 0) return;
      this.keySign = [];
      for (let j = 0; j < keySignArray.length; j++) {
        if (keySignArray[j].isCheck) {
          this.keySign.push(keySignArray[j].signName);
        }
      }
    },
    /**
     * description: 检验交班日期班次
     * return {*}
     */
    shiftCheck() {
      // 检验交班日期 交班班次
      let nowShift = this.timeList.nowShift;
      let crossDay = false;
      let list = [];
      //取出可以交班的班次
      for (let i = 0; i < this.timeList.stationShifts.length; i++) {
        let shift = this.timeList.stationShifts[i];
        if (nowShift.id == shift.id) {
          list.push(shift);
          i--;
          if (i < 0) {
            crossDay = true;
            i = this.timeList.stationShifts.length - 1;
          }
          list.push(this.timeList.stationShifts[i]);
          break;
        }
      }
      if (!crossDay) {
        if (
          (this.handoverShiftID == list[0].id || this.handoverShiftID == list[1].id) &&
          this.handoverDate == this.timeList.shiftDate
        ) {
          return true;
        } else return false;
      } else {
        let date = new Date(this.timeList.shiftDate);
        let beforeDate = date.getTime() - 24 * 60 * 60 * 1000;
        if (this.handoverShiftID == list[1].id && new Date(this.handoverDate).getTime() == beforeDate) {
          return true;
        } else if (this.handoverShiftID == list[0].id && this.handoverDate == this.timeList.shiftDate) {
          return true;
        } else return false;
      }
    },
    /**
     * description: 切换交班类型时间控制
     * return {*}
     */
    timeManage() {
      if (this.normalFlag) {
        // 重置当前班次时间
        this.startDateTime = null;
        this.endDateTime = "";
      } else {
        // 重置班内交班时间
        for (let i = 0; i < this.timeList.stationShifts.length; i++) {
          if (this.handoverShiftID == this.timeList.stationShifts[i].id) {
            this.startDateTime =
              this.handoverDate.substring(0, 11) + " " + this.timeList.stationShifts[i].shiftStartTime;
          }
        }
        this.endDateTime = this._datetimeUtil.getNowTime("yyyy-MM-dd hh:mm");
      }
    },
    /**
     * description: 获取当前班别名称
     * return {*}
     */
    getshiftContent() {
      for (let i = 0; i < this.timeList.stationShifts.length; i++) {
        if (this.handoverShiftID == this.timeList.stationShifts[i].id) {
          return this.timeList.stationShifts[i].shiftName;
        }
      }
    },
    /**
     * description: 班别日期改变，重新获取记录
     * return {*}
     */
    async handoverDateChange() {
      if (!this.handoverShiftID) return;
      this.userID = "";
      this.handoverData = [];
      if (!this.normalFlag) {
        this.timeManage();
      }
      await this.selectShift();
    },
    /**
     * description: 保存交班注记
     * return {*}
     */
    saveKeySign(keySign, handoverID) {
      Object.assign(keySign, { handoverID: handoverID });

      this.loading = true;
      SaveKeySign(keySign).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "修改成功!");
        }
      });
    },
    /**
     * description:不符合条件的患者禁止勾选交班
     * return {*}
     * param {*} row
     */
    selectable(row) {
      return !row.disabled;
    },
    /**
     * @description: 交班类型切换重算表格高度
     * @return
     */
    typeChange() {
      this.$nextTick(() => {
        this.tableHeight = this.$refs.handoverContent.offsetHeight;
      });
    },
  },
};
</script>
<style lang="scss">
.handover {
  .handover-header {
    line-height: 35px;
    padding-bottom: 3px;
    box-sizing: border-box;
    .level-one-1 {
      vertical-align: top;
      width: 260px;
      display: inline-block;
      .level-two {
        .date-picker {
          width: 160px;
        }
      }
    }
    .level-one-2 {
      vertical-align: top;
      display: inline-block;
      width: 270px;
      .level-two-shift-select {
        margin-left: 28px;
      }
      .level-two {
        .date-picker {
          width: 160px;
        }
      }
    }
    .level-one-3 {
      display: inline-block;
      width: 200px;
      vertical-align: top;
      .el-select {
        width: 100px;
      }
    }
    .level-one-4 {
      display: inline-block;
      width: 200px;
      .level-two {
        .el-radio {
          margin-right: 10px;
        }
      }
    }
  }
  .handover-wrap {
    .top-btn {
      float: right;
    }
    .handover-content {
      height: 100%;
    }
    .el-table {
      td {
        vertical-align: top;
        padding-top: 5px;
      }
      .el-checkbox.is-disabled {
        .el-checkbox__label {
          color: #606266;
        }
      }
    }
  }

  .normal-handover {
    display: block;
    border: 1px solid #c4c4c4;
    height: 90%;
    box-sizing: border-box;
    margin-top: 10px;
  }
  .el-dialog {
    .body-pic {
      position: relative;
      top: 20%;
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 100%;
    }
    .el-checkbox-group {
      float: left;
    }
    .button-area {
      float: right;
    }
  }
}
.multihandoff-msgbox-class {
  border: 0;
  .el-message-box__header {
    background-color: $base-color;
    color: #ffffff;
  }
  .el-message-box__title,
  .el-icon-close:before {
    color: #ffffff;
  }
}
</style>
