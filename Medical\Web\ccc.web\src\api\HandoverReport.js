/*
 * FilePath     : \src\api\HandoverReport.js
 * Author       : 李青原
 * Date         : 2020-03-30 08:19
 * LastEditors  : 马超
 * LastEditTime : 2025-05-07 18:02
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/HandoverReport";

export const urls = {
  GetHandoverReportTitle: baseUrl + "/GetHandoverReportTitle",
  GetHandoverReportSBAR: baseUrl + "/GetHandoverReportSBAR",
  GetSingleHandoverReportSBAR: baseUrl + "/GetSingleHandoverReportSBAR",
  GetDailyHandoverTitle: baseUrl + "/GetDailyHandoverTitle",
  GetDailyHandoverSBAR: baseUrl + "/GetDailyHandoverSBAR",
  GetMorningHandover: baseUrl + "/GetMoringHandover",
  SummaryMorningHandover: baseUrl + "/SummaryMorningHandover",
  GetMorningHandoverReportTitle: baseUrl + "/GetMorningHandoverReportTitle",
  GetMorningHandoverReportSBAR: baseUrl + "/GetMorningHandoverReportSBAR",
  UpdateMorningHandoverReport: baseUrl + "/UpdateMorningHandoverReport",
  GetMorningHandoverBeds: baseUrl + "/GetMorningHandoverBeds",
};
// 获取交班报告顶部初始化内容
export const GetHandoverReportTitle = (params) => {
  return http.get(urls.GetHandoverReportTitle, params);
};

// 获取多病人交班报告内容
export const GetHandoverReportSBAR = (params) => {
  return http.post(urls.GetHandoverReportSBAR, qs.stringify(params));
};
// 获取单病人交班报告内容
export const GetSingleHandoverReportSBAR = (params) => {
  return http.get(urls.GetSingleHandoverReportSBAR, params);
};
// 获取每日交班统计表头数据
export const GetDailyHandoverTitle = (params) => {
  return http.get(urls.GetDailyHandoverTitle, params);
};
// 获取每日交班内容
export const GetDailyHandoverSBAR = (params) => {
  return http.post(urls.GetDailyHandoverSBAR, qs.stringify(params));
};
// 获取晨交班内容
export const GetMorningHandover = (params) => {
  return http.post(urls.GetMorningHandover, qs.stringify(params));
};
// 汇总晨交班内容
export const SummaryMorningHandover = (params) => {
  return http.post(urls.SummaryMorningHandover, qs.stringify(params));
};
// 获取晨交班报表表头
export const GetMorningHandoverReportTitle = (params) => {
  return http.get(urls.GetMorningHandoverReportTitle, params);
};
export const GetMorningHandoverReportSBAR = (params) => {
  return http.post(urls.GetMorningHandoverReportSBAR, params);
};
export const UpdateMorningHandoverReport = (params) => {
  return http.post(urls.UpdateMorningHandoverReport, qs.stringify(params));
};
// 获取晨交班报告床位
export const GetMorningHandoverBeds = (params) => {
  return http.get(urls.GetMorningHandoverBeds, params);
};
