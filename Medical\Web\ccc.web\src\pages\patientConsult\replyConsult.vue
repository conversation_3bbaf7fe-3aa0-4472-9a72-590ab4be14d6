<!--
 * FilePath     : \src\pages\patientConsult\replyConsult.vue
 * Author       : 郭自飞
 * Date         : 2020-01-14 14:16
 * LastEditors  : 马超
 * LastEditTime : 2025-06-05 10:08
 * Description  : 会诊回复
 * CodeIterationRecord: 2022-05-03 2572 新增会诊回复评价
 -->
<template>
  <base-layout show-header header-height="auto" class="reply-consult">
    <div slot="header">
      <div class="consult-select">
        过期会诊
        <el-switch v-model="beOverdue" @change="dataHandle"></el-switch>
        <span>开始日期：</span>
        <el-date-picker
          v-model="queryConsult.startTime"
          type="date"
          placeholder="选择日期"
          style="width: 120px"
          :picker-options="pickerOptionsCreate"
          @change="getByCondition"
        ></el-date-picker>
        <span>结束日期：</span>
        <el-date-picker
          v-model="queryConsult.endTime"
          type="date"
          placeholder="选择日期"
          style="width: 120px"
          :picker-options="pickerOptionsEnd"
          @change="getByCondition"
        ></el-date-picker>
        <span class="label">会诊人员：</span>
        <el-select
          v-model="queryConsult.consultedEmployeeID"
          style="width: 120px"
          placeholder="请选择"
          clearable
          filterable
          :filter-method="filterConsultedEmployee"
          @change="getByCondition"
        >
          <el-option
            v-for="item in consultedEmployeeIDlsit"
            :key="item.employeeID"
            :label="item.employeeName"
            :value="item.employeeID"
          ></el-option>
        </el-select>
        <span>状态：</span>
        <el-select v-model="consultState" style="width: 140px" placeholder="请选择" @change="getByCondition" clearable>
          <el-option v-for="item in consult" :key="item.key" :label="item.key" :value="item.value"></el-option>
        </el-select>
        <span>会诊分类：</span>
        <el-select
          style="width: 140px"
          v-model="queryConsult.emergencyFlag"
          placeholder="请选择"
          clearable
          @change="getByCondition"
        >
          <el-option v-for="item in categoryList" :key="item.value" :label="item.key" :value="item.value"></el-option>
        </el-select>
      </div>
    </div>
    <el-table
      ref="consultTableRef"
      :data="patientConsultList"
      v-loading="loading"
      v-loading.fullscreen.lock="loading"
      row-key="patientConsultID"
      :expand-row-keys="expands"
      @expand-change="loadData"
      height="100%"
      border
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <el-table :data="scope.row.children" stripe border class="nesting-table" height="100%">
            <el-table-column show-overflow-tooltip label="目的" min-width="100" align="left">
              <template slot-scope="scope">
                <span>{{ scope.row.mainContent + "-" + scope.row.detailContent }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="replyStationNmae"
              label="会诊回复病区"
              min-width="85"
              align="center"
            ></el-table-column>
            <el-table-column label="回复日期" width="160" align="center">
              <template slot-scope="scope">
                <span
                  v-formatTime="{
                    value: scope.row.replyDate,
                    type: 'dateTime',
                  }"
                ></span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="consultAssignFlag"
              prop="assignEmployeeName"
              label="指派人员"
              min-width="80"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="consultedEmployeeName"
              label="会诊人"
              min-width="80"
              align="center"
            ></el-table-column>
            <el-table-column label="状态" min-width="60" align="center">
              <template slot-scope="scope">
                <span
                  v-if="scope.row.replyStationNmae || consultAssignFlag"
                  :class="{
                    'no-reply': scope.row.consultState == '未指派' || scope.row.consultState == '已申请,待会诊',
                  }"
                >
                  {{ scope.row.consultState }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="consultOperation">
                <el-tooltip content="回复">
                  <i class="iconfont icon-reply" @click="supplementaryDate(consultOperation.row)"></i>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="stationName"
        label="病区"
        width="120"
        align="left"
        header-align="center"
      ></el-table-column>
      <el-table-column prop="bedNumber" label="床号" width="50" align="center"></el-table-column>
      <el-table-column prop="localCaseNumber" label="住院号" width="120" align="center"></el-table-column>
      <el-table-column prop="patientName" label="姓名" width="100" align="center"></el-table-column>
      <el-table-column prop="gender" label="性别" width="50" align="center"></el-table-column>
      <el-table-column prop="ageDetail" label="年龄" width="50" align="center"></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="consultStationNmae"
        label="发起病区"
        width="120"
        align="left"
        header-align="center"
      ></el-table-column>
      <el-table-column show-overflow-tooltip label="目的" min-width="150" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.mainContent + "-" + scope.row.detailContent }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="emergencyName" label="类别" min-width="60" align="center"></el-table-column>

      <el-table-column width="90" align="center">
        <template slot-scope="scope" slot="header">
          <span>出诊人</span>
          <i class="iconfont icon-info" @click="showMessage('实际会诊回复人', scope)"></i>
        </template>
        <template slot-scope="scope">
          <div>{{ scope.row.replyEmployeeName }}</div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip width="140" align="left" header-align="center">
        <template slot-scope="scope" slot="header">
          <span>出诊病区</span>
          <i class="iconfont icon-info" @click="showMessage('实际会诊回复病区', scope)"></i>
        </template>
        <template slot-scope="scope">
          <div>{{ scope.row.replyStationNmae }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="consultAssignFlag"
        prop="assignEmployeeName"
        label="指派人员"
        width="90"
        align="center"
      ></el-table-column>
      <el-table-column v-if="!consultAssignFlag" prop="consultedEmployeeName" label="会诊人" width="70" align="center">
        <template slot-scope="scope" slot="header">
          <span>会诊人</span>
          <i class="iconfont icon-info" @click="showMessage('会诊发起邀请的会诊人员', scope)"></i>
        </template>
        <template slot-scope="scope">
          <div>{{ scope.row.consultedEmployeeName }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="consultDate" label="申请时间" width="160" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.consultDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="consultDate" label="回复时间" width="160" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.replyDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80" align="center">
        <template slot-scope="consultOperation">
          <span class="span">{{ consultOperation.row.consultState }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" align="center" fixed="right">
        <template slot-scope="consultOperation">
          <el-tooltip v-if="!consultOperation.row.hasChildren" content="回复">
            <i class="iconfont icon-reply" @click="supplementaryDate(consultOperation.row)"></i>
          </el-tooltip>
          <el-tooltip content="评价">
            <i class="iconfont icon-record-evaluate" @click="getConsultAssessView(consultOperation.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="reply-content">
      <el-dialog
        title="回复内容"
        v-dialogDrag
        :visible.sync="dialogTableVisible"
        width="800px"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        v-loading="dialogLoading"
        :before-close="formateReplyDate"
        custom-class="dialog"
      >
        <div class="date-pick-div">
          <span>回复时间：</span>
          <el-date-picker
            v-model="replyDate"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            type="datetime"
            placeholder="选择日期时间"
            :picker-options="pickerLimit"
          ></el-date-picker>
        </div>
        <el-form :model="replyData" class="date-pick-div">
          <el-form-item label="会诊目的:" label-width="75px">
            <el-input v-model="replyData.detailContent" :readonly="true"></el-input>
          </el-form-item>
          <el-form-item label="发起内容:" label-width="75px">
            <el-input
              type="textarea"
              :readonly="true"
              v-model="replyData.consultContent"
              :autosize="{ minRows: 3, maxRows: 6 }"
              resize="none"
            ></el-input>
          </el-form-item>
          <el-form-item class="reply-contect" label="回复内容:" label-width="75px">
            <limit-text-box
              class="consult-input"
              v-model="replyContent"
              :maxLength="666"
              :rows="10"
              width="680"
            ></limit-text-box>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-checkbox v-model="bringToNursingRecord">带入护理记录单</el-checkbox>
          <el-button type="primary" @click="reply">回 复</el-button>
        </div>
      </el-dialog>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="会诊评价"
      :visible.sync="dialogVisible"
      v-loading="dialogLoading"
      element-loading-text="加载中……"
    >
      <div class="date-pick-div">
        <span>评价时间：</span>
        <el-date-picker
          v-model="evaluateDate"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="选择日期时间"
        ></el-date-picker>
      </div>
      <tabs-layout ref="tabsLayout" :template-list="consultAssessTemplate" @change-values="changeValues"></tabs-layout>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConsultEvaluate">确 定</el-button>
      </span>
    </el-dialog>
  </base-layout>
</template>

<script>
import {
  GetByCondition,
  ReplyConsult,
  GetGroupPersonnelList,
  GetConsultSetting,
  GetConsultAssign,
  GetConsultAssessView,
  SaveConsultDetail,
  GetConsultRecord,
} from "@/api/PatientConsult";
import stationSelector from "@/components/selector/stationSelector";
import baseLayout from "@/components/BaseLayout";
import tabsLayout from "@/components/tabsLayout/index";
import LimitTextBox from "@/components/LimitTextBox";
import { GetOneSettingByTypeAndCode } from "@/api/Setting";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    tabsLayout,
    stationSelector,
    LimitTextBox,
  },
  props: {
    refillFlag: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const _this = this;
    return {
      bringToNursingRecord: false,
      pickerDate: undefined,
      evaluateDate: this._datetimeUtil.getNow(),
      patientConsultList: [],
      stationID: undefined,
      loading: false,
      consultState: "2",
      dialogTableVisible: false,
      replyData: {},
      replyDate: this._datetimeUtil.getNow(),
      dialogLoading: false,
      consultedEmployeeIDlsit: [],
      cloneConsultedEmployeeIDlsit: [],
      beOverdue: false,
      consultAssignFlag: true,
      consultOverdue: {
        emergencyDate: undefined,
        UsualDate: undefined,
      },
      cloneConsult: [],
      replyContent: undefined,
      expands: [],
      queryConsult: {
        startTime: this._datetimeUtil.getNowDate(),
        endTime: this._datetimeUtil.getNowDate(),
        consultedEmployeeID: undefined,
        emergencyFlag: "",
      },
      consult: [
        {
          key: "已回复",
          value: "1",
        },
        {
          key: "已申请,待会诊",
          value: "2",
        },
        {
          key: "已取消",
          value: "3",
        },
      ],
      categoryList: [
        {
          key: "平会诊",
          value: "0",
        },
        {
          key: "急会诊",
          value: "1",
        },
        {
          key: "多学科会诊",
          value: "2",
        },
        {
          key: "全部",
          value: "",
        },
      ],
      pickerOptionsCreate: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(_this.queryConsult.endTime).getTime();
        },
      },
      pickerOptionsEnd: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(_this.queryConsult.startTime).getTime() - 8.64e7;
        },
      },
      pickerLimit: {
        disabledDate(time) {
          return time.getTime() < new Date(_this.pickerDate).getTime();
        },
      },
      //评价模版内容
      consultAssessTemplate: [],
      //会诊评价模版Code
      recordsCode: undefined,
      //弹窗
      dialogVisible: false,
      //组件返回数据
      tabsListData: [],
      //会诊ID
      patientConsultID: undefined,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  async mounted() {
    this.stationID = this.user.stationID;
    await this.init();
    this.getByCondition();
  },
  methods: {
    /**
     * @description: 初始加载
     * @return
     */
    async init() {
      await GetGroupPersonnelList().then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultedEmployeeIDlsit = res.data;
          this.cloneConsultedEmployeeIDlsit = this._common.clone(this.consultedEmployeeIDlsit);

          if (this.user && this.consultedEmployeeIDlsit && this.consultedEmployeeIDlsit.length > 0) {
            let employee = this.consultedEmployeeIDlsit.find((m) => m.employeeID == this.user.userID);
            if (employee) {
              this.queryConsult.consultedEmployeeID = this.user.userID;
            }
          }
        }
      });
      GetConsultSetting().then((res) => {
        if (this._common.isSuccess(res)) {
          let list = res.data.filter((item) => {
            return item.settingTypeCode == "ConsultDeadline_ST";
          });
          let list2 = res.data.filter((item) => {
            return item.settingTypeCode == "ConsultDeadline_General";
          });
          this.consultOverdue.emergencyDate = list[0].typeValue;
          this.consultOverdue.UsualDate = list2[0].typeValue;
        }
      });
      GetConsultAssign().then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultAssignFlag = res.data.ConsultAssignFlag;
          if (this.consultAssignFlag && this.consult.length < 4) {
            this.consult.push({
              key: "未指派",
              value: "4",
            });
          }
        }
      });
      this.getOneSetting();
    },
    formateReplyDate() {
      this.dialogTableVisible = false;
    },

    /**
     * @description: 根据条件查询
     * @return
     */
    getByCondition() {
      if (!this.queryConsult.consultedEmployeeID) {
        this.consultedEmployeeIDlsit = this._common.clone(this.cloneConsultedEmployeeIDlsit);
      }
      let params = {
        startTime: this._datetimeUtil.formatDate(this.queryConsult.startTime, "yyyy-MM-dd"),
        endTime: this._datetimeUtil.formatDate(this.queryConsult.endTime, "yyyy-MM-dd"),
        consultState: this.consultState,
        consultedEmployeeID: this.queryConsult.consultedEmployeeID,
        emergencyFlag: this.queryConsult.emergencyFlag,
      };
      this.loading = true;
      GetByCondition(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.patientConsultList = res.data;
          this.cloneConsult = this._common.clone(this.patientConsultList);
          this.$nextTick(() => {
            this.$refs.consultTableRef.doLayout();
          });
        }
      });
      this.dataHandle();
    },
    /**
     * @description: 组合数据
     * @return
     * @param data
     */
    supplementaryDate(data) {
      if (this.consultAssignFlag && !data.assignEmployeeID) {
        this._showTip("warning", "尚未指定会诊人员");
        return;
      }
      if (data.consultGroupID && !data.signOffMainID) {
        this._showTip("warning", "尚未审批通过");
        return;
      }
      this.dialogTableVisible = true;
      this.replyDate = this._datetimeUtil.getNow();
      this.replyData = this._common.clone(data);
      if (!data.replyContent) {
        this.replyContent = "";
        this.pickerDate = this._datetimeUtil.formatDate(this.replyData.consultDate, "yyyy-MM-dd");
        if (this.replyData.replyDate) {
          this.replyDate = this._datetimeUtil.formatDate(this.replyData.replyDate, "yyyy-MM-dd hh:mm");
        }
      } else {
        this.replyContent = data.replyContent;
        this.pickerDate = this._datetimeUtil.formatDate(this.replyData.consultDate, "yyyy-MM-dd");
        if (this.replyData.replyDate) {
          this.replyDate = this._datetimeUtil.formatDate(this.replyData.replyDate, "yyyy-MM-dd hh:mm");
        }
      }
    },
    /**
     * @description: 确认回复
     * @return
     */
    reply() {
      this.dialogLoading = true;
      if (this.dialogLoading) {
        this.replyData.replyContent = this.replyContent;
        if (this.replyDate) {
          this.replyData.replyDate = this._datetimeUtil.formatDate(this.replyDate, "yyyy-MM-dd hh:mm");
        }
        this.replyData.bringToNursingRecord = this.bringToNursingRecord;
        this.replyData.modifyPersonID = this.replyData.assignEmployeeID;
        this.$set(this.replyData, "refillFlag", this.refillFlag);
        return ReplyConsult(this.replyData).then((res) => {
          this.dialogLoading = false;
          if (this._common.isSuccess(res)) {
            this.dialogTableVisible = false;
            this._showTip("success", res.message);
            this.getByCondition();
            this.replyDate = undefined;
          }
        });
      }
    },
    /**
     * @description: 展开关闭子表格方法
     * @param row
     * @return
     */
    loadData(row) {
      if (row.hasChildren) {
        var index = this.expands.indexOf(row.patientConsultID);
        if (index >= 0) {
          this.expands.splice(index, 1);
          return;
        }
        this.expands.push(row.patientConsultID);
      } else {
        this.expands = [];
      }
    },
    /**
     * @description: 查看过期会诊记录
     * @return
     */
    dataHandle() {
      let newDate = new Date();
      let template = [];
      if (this.beOverdue) {
        this.cloneConsult.forEach((item) => {
          if (item.emergencyFlag == "平会诊") {
            if (
              (newDate.getTime() - new Date(item.consultDate).getTime()) / 60000 >= this.consultOverdue.UsualDate &&
              !item.replyEmployeeID &&
              !item.cancelEmployeeID
            ) {
              template.push(item);
            }
          }
          if (item.emergencyFlag == "急会诊") {
            if (
              (newDate.getTime() - new Date(item.consultDate).getTime()) / 60000 >= this.consultOverdue.emergencyDate &&
              !item.replyEmployeeID &&
              !item.cancelEmployeeID
            ) {
              template.push(item);
            }
          }
        });
        this.patientConsultList = template;
      } else {
        this.patientConsultList = this.cloneConsult;
      }
    },
    changeValues(data) {
      //模板数据
      this.tabsListData = data;
    },
    /**
     * description: 获取会诊评价模版
     * param {row} 当前操作会诊
     * return {*}
     */
    getConsultAssessView(row) {
      if (!row.replyEmployeeID) {
        this._showTip("warning", "尚未回复会诊");
        return;
      }
      this.dialogVisible = true;
      this.dialogLoading = true;
      this.consultAssessTemplate = [];
      this.patientConsultID = row.patientConsultID;
      this.recordsCode = row.recordsCode;
      let params = {
        PatientConsultID: row.patientConsultID,
        recordsCode: "Reply" + row.recordsCode,
        inpatientID: row.inpatientID,
      };
      let patientConsultParams = {
        patientConsultID: row.patientConsultID,
      };
      GetConsultRecord(patientConsultParams).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.replyConsultEvaluateDate) {
            this.evaluateDate = res.data.replyConsultEvaluateDate;
          }
        }
      });
      GetConsultAssessView(params).then((res) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(res)) {
          this.consultAssessTemplate = res.data;
        }
      });
    },
    /**
     * description: 会诊评价保存
     * param {*}
     * return {*}
     */
    saveConsultEvaluate() {
      this.dialogLoading = true;
      let list = [];
      if (!this.tabsListData.length) {
        this.dialogVisible = false;
        return list;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        this.dialogVisible = false;
        return list;
      }
      this.tabsListData.forEach((element) => {
        let item = {
          assessListID: element.assessListID,
          assessValue: element.assessValue,
          assessListGroupID: element.assessListGroupID,
          assessValue: element.assessValue,
          controlerType: element.controlerType,
        };
        if (element.controlerType.trim() == "C" || element.controlerType.trim() == "R") {
          item.assessValue = "";
        } else {
          item.assessValue = element.assessValue;
        }
        list.push(item);
      });
      let params = {
        recordsCode: "Reply" + this.recordsCode,
        patientConsultID: this.patientConsultID,
        PatientConsultDetailList: list,
      };
      if (this.evaluateDate) {
        params.evaluateDate = this._datetimeUtil.formatDate(this.evaluateDate, "yyyy-MM-dd hh:mm");
      } else {
        params.evaluateDate = this._datetimeUtil.getNow();
      }
      SaveConsultDetail(params).then((res) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "会诊评价保存成功");
          this.dialogVisible = false;
        }
      });
    },
    /**
     * @description: 获取配置，会诊回复是否带入护理记录单
     * @return
     */
    getOneSetting() {
      let params = {
        settingType: 249,
        settingCode: "ConsultToRecordSwitch",
      };
      GetOneSettingByTypeAndCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.bringToNursingRecord = res.data.typeValue == "True" ? true : false;
        }
      });
    },
    /**
     * @description: 输入工号筛选会诊人员
     * @param value
     * @return
     */
    filterConsultedEmployee(value) {
      if (!value) {
        this.consultedEmployeeIDlsit = this._common.clone(this.cloneConsultedEmployeeIDlsit);
        return;
      }
      this.consultedEmployeeIDlsit = this.cloneConsultedEmployeeIDlsit.filter(
        (consulted) => consulted.employeeID == value
      );
    },
    /**
     * @description: 表头列提示内容
     * @param messageText
     * @return
     */
    showMessage(messageText) {
      this._showMessage({
        message: messageText,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
  },
};
</script>
<style lang="scss">
.reply-consult {
  .el-table__expanded-cell {
    .nesting-table {
      width: calc(100% - 10px);
    }
  }
  .el-form-item__content {
    height: 100%;
    .el-textarea {
      height: 100%;
      .el-textarea__inner {
        height: 100%;
      }
    }
  }
  .reply-content {
    .el-dialog {
      height: auto;
      margin-top: 20vh !important;
    }
    .el-dialog__body {
      padding: 5px;
    }
  }
  .date-pick-div {
    margin-left: 2%;
    margin-bottom: 15px;
    margin-top: 10px;
    .el-input__inner {
      width: 168px;
    }
  }
}
</style>
