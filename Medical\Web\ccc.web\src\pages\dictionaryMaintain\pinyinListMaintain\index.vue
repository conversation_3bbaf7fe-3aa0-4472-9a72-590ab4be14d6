<!--
 * FilePath     : \src\pages\dictionaryMaintain\pinyinListMaintain\index.vue
 * Author       : 郭鹏超
 * Date         : 2020-06-11 11:53
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-16 08:24
 * Description  : 简拼字典表维护
--> 
<template>
  <base-layout class="pinyin-list-maintain">
    <div class="pinyin-list-maintain-top" slot="header">
      <label>简拼类型:</label>
      <el-select class="table-name-select" v-model="tableName" placeholder="请选择">
        <el-option
          v-for="(item, index) in tableNameList"
          :key="index"
          :value="item.typeValue"
          :label="item.description"
        ></el-option>
      </el-select>
      <label>名称查询:</label>
      <el-input
        @keyup.enter.native="filterTableData(1)"
        v-model="inputData"
        class="pinyin-input"
        placeholder="请输入要搜索的简称"
      >
        <i slot="append" class="iconfont icon-search" @click="filterTableData(1)"></i>
      </el-input>
    </div>
    <div v-loading="loading" element-loading-text="加载中……" class="pinyin-list-maintain-content">
      <el-table height="100%" border :data="fixPinyinTableData" stripe highlight-current-row>
        <el-table-column min-width="150" label="全名" prop="name"></el-table-column>
        <el-table-column min-width="150" label="简称" prop="shortName"></el-table-column>
        <el-table-column min-width="100" label="字母简拼" prop="chinesePinyin"></el-table-column>
        <el-table-column width="80" label="更改人" prop="modifyPersonID"></el-table-column>
        <el-table-column width="140" align="center" label="维护时间" prop="modifyDate">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.modifyDate, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column width="110" label="操作" align="center" prop="deleteFlag">
          <template v-if="scope.row.deleteFlag != '*'" slot-scope="scope">
            <el-tooltip content="新增">
              <i @click="addButtonEvent(scope.row)" class="iconfont icon-add"></i>
            </el-tooltip>
            <el-tooltip content="修改">
              <i @click="fixIconEvent(scope.row)" class="iconfont icon-edit"></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i @click="deletePinyinData(scope.row.pinyinIndexID)" class="iconfont icon-del"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      class="pinyin-list-maintain-dialog"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
    >
      <div class="dialog-content">
        <label>别名简称:</label>
        <el-input
          @blur="fixName()"
          class="dialog-form"
          v-model="dialogData.shortName"
          placeholder="请输入简称"
        ></el-input>
        <label>简拼:</label>
        <el-input class="dialog-form" v-model="dialogData.chinesePinyin" placeholder="请输入简拼"></el-input>
      </div>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveButtonEvent">保存</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import BaseLayout from "@/components/BaseLayout";
import {
  GetPinyinIndexs,
  SaveOnePinyinIndexInfo,
  UpDateOnePinyinIndexInfo,
  DeleteOnePinyinIndexInfo,
} from "@/api/PinyinListMaintain";
import { GetClinicalSettingInfo } from "@/api/Assess";
export default {
  components: {
    BaseLayout,
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      dialogTitle: "",
      inputData: "",
      tableName: "",
      tableNameList: [],
      dialogtableNameList: [],
      pinyinTableData: [],
      fixPinyinTableData: [],
      dialogData: {
        tableName: "",
        indexField: "",
        indexID: "",
        chinesePinyin: "",
        shortName: "",
        name: "",
      },
    };
  },
  watch: {
    tableName: {
      handler(newValue) {
        this.filterTableData(0);
      },
    },
  },
  mounted() {
    this.getTableNameSelectData();
    this.getPinyinIndexData();
  },
  methods: {
    //获取顶部下拉框数据
    getTableNameSelectData() {
      let params = {
        SettingTypeCode: "PinyinTable",
      };
      GetClinicalSettingInfo(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tableNameList = res.data;
          this.tableName = this.tableNameList[0].typeValue;
        }
      });
    },
    //获取弹窗中下拉框数据
    getDialogSelectData() {
      this.dialogtableNameList = [];
      for (let i = 0; i < this.tableNameList.length; i++) {
        if (i > 0) {
          this.dialogtableNameList.push(this.tableNameList[i]);
        }
      }
    },
    //获取pinyinIndex数据表所有数据
    getPinyinIndexData() {
      this.loading = true;
      GetPinyinIndexs().then((res) => {
        if (this._common.isSuccess(res)) {
          this.pinyinTableData = res.data;
          this.$set(this, "fixPinyinTableData", res.data);
          this.filterTableData(0);
          this.loading = false;
        }
      });
    },
    //新增按钮操作函数
    addButtonEvent(value) {
      this.dialogTitle = "新增";
      this.dialogVisible = true;
      let clearDialogData = {
        chinesePinyin: "",
        shortName: "",
      };
      //在不改变原变量的基础上删除不需要字段
      let { chinesePinyin, shortName, pinyinIndexID, modifyPersonID, modifyDate, deleteFlag, ...params } = value;
      Object.assign(clearDialogData, params);
      Object.assign(this.dialogData, clearDialogData);
    },
    // 数据修改图标操作函数
    fixIconEvent(value) {
      this.dialogTitle = "修改";
      this.dialogVisible = true;
      Object.assign(this.dialogData, value);
    },
    //保存按钮事件
    saveButtonEvent() {
      if (this.dialogTitle == "新增") {
        this.addOneData();
      } else {
        this.fixOneData();
      }
    },
    //新增一条数据
    addOneData() {
      if (!this.dialogData.shortName) {
        this._showTip("warning", "请输入简称……");
        return;
      }
      if (!this.dialogData.chinesePinyin) {
        this._showTip("warning", "请输入简拼……");
        return;
      }
      return SaveOnePinyinIndexInfo(this.dialogData).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "新增成功");
          this.dialogVisible = false;
          this.getPinyinIndexData();
        }
      });
    },
    //修改一条数据
    fixOneData() {
      return UpDateOnePinyinIndexInfo(this.dialogData).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "修改成功");
          this.dialogVisible = false;
          this.getPinyinIndexData();
        }
      });
    },
    //删除一条数据
    deletePinyinData(value) {
      let params = {
        pinyinIndexID: value,
      };
      let _this = this;
      this._deleteConfirm("确定要删除此条数据", (flag) => {
        if (flag) {
          DeleteOnePinyinIndexInfo(params).then((res) => {
            if (_this._common.isSuccess(res)) {
              _this._showTip("success", "删除成功");
              this.getPinyinIndexData();
            }
          });
        }
      });
    },
    //筛选表格数据
    filterTableData(value) {
      if (!value) {
        this.inputData = "";
      }
      this.fixPinyinTableData = this.pinyinTableData;
      if (this.tableName != "全部") {
        this.fixPinyinTableData = this.search(this.pinyinTableData.filter((item) => item.tableName == this.tableName));
      } else {
        this.fixPinyinTableData = this.search(this.fixPinyinTableData);
      }
    },
    //输入框模糊查询
    search(value) {
      return value.filter((item) => {
        let str = item.shortName.replace(/<[^>]+>/g, ""); //简称
        let str1 = item.name.replace(/<[^>]+>/g, ""); //全名
        if (str.indexOf(this.inputData) != -1 || str1.indexOf(this.inputData) != -1) {
          return item.shortName;
        }
      });
    },
    //获取汉字简拼
    fixName() {
      this.dialogData.chinesePinyin = this._common.initial(this.dialogData.shortName);
    },
  },
};
</script>

<style lang="scss">
.pinyin-list-maintain {
  .pinyin-list-maintain-top {
    .table-name-select {
      width: 150px;
      margin-right: 30px;
    }
    .pinyin-input {
      width: 200px;
      .el-input-group__append {
        padding: 0 5px;
      }
      i {
        color: #8cc63e;
      }
    }
    .add-button {
      float: right;
      margin-top: 10px;
    }
  }
  .pinyin-list-maintain-content {
    height: 100%;
    font-size: 13px;
    .el-table .cell {
      font-size: 14px;
    }
  }
  .pinyin-list-maintain-dialog {
    .el-dialog {
      width: 550px;
      height: 200px;
      .dialog-content {
        text-align: center;
        .el-input {
          width: 180px;
        }
      }
    }
  }
}
</style>