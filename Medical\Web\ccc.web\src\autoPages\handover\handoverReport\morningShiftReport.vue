<!--
 * FilePath     : \src\autoPages\handover\handoverReport\morningShiftReport.vue
 * Author       : 马超
 * Date         : 2023-09-28 09:45
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-23 16:45
 * Description  :
 * CodeIterationRecord:
-->
<template>
  <base-layout class="morning-shift-report" v-loading="loading" headerHeight="auto" :element-loading-text="loadingText">
    <div slot="header">
      <station-selector v-model="stationID"></station-selector>
      <span>日期:</span>
      <el-date-picker
        class="picker-date"
        v-model="handoverDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        :picker-options="checkDate"
        @change="getHandoverTitleView()"
      ></el-date-picker>
      <div class="top-btn">
        <el-button type="primary" class="query-button" icon="iconfont icon-search" @click="summaryMorningHandover()">
          {{ buttonName }}
        </el-button>
      </div>
      <label>床号:</label>
      <el-input
        @keyup.enter.native="getDataByBedNumber"
        v-model="bedNumber"
        class="search-input"
        placeholder="患者床号"
      >
        <i slot="append" class="iconfont icon-search" @click="getDataByBedNumber"></i>
      </el-input>
    </div>
    <div class="morning-report">
      <!-- 头部内容 -->
      <el-table
        v-for="(viewItem, index) in handoverReportTitleView"
        :key="index"
        :data="viewItem[1]"
        :show-header="false"
        border
        v-show="viewItem[1] && viewItem[1].length > 0"
        :class="'table-' + viewItem[0]"
      >
        <template v-if="viewItem[1] && viewItem[1].length > 0">
          <el-table-column
            v-for="(item, itemIndex) in Object.keys(viewItem[1][0])"
            :key="itemIndex"
            :prop="item"
            align="center"
            :min-width="convertPX(25)"
          >
            <template slot-scope="scope">
              <div v-if="viewItem[0] != 'signData'">{{ scope.row[item] }}</div>
              <div v-else>
                <div
                  v-if="scope.$index > 0"
                  @click="getMorningHandover(item, scope.row[item])"
                  :class="{ 'is-select': checkedProp == item }"
                >
                  {{ scope.row[item].numberOrLabel }}
                </div>
                <el-tooltip
                  v-else
                  placement="top"
                  :disabled="!scope.row[item].description"
                  :content="scope.row[item].description"
                >
                  <div>{{ scope.row[item].numberOrLabel }}</div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>

      <!-- 详细内容 -->
      <div class="morning-report-sbar">
        <el-table
          :data="handoverList"
          height="100%"
          border
          stripe
          class="table-sbar"
          :row-class-name="getRowClass"
          ref="handoverDetailTable"
        >
          <el-table-column
            label="I-介绍"
            v-if="handoverShowType.indexOf('I') != -1"
            :width="handoverShowType.length < 5 ? convertPX(500) : convertPX(234)"
          >
            <template slot-scope="scope">
              <div v-html="scope.row.inturduction" class="inturduction"></div>
            </template>
          </el-table-column>
          <el-table-column label="S-现状" v-if="handoverShowType.indexOf('S') != -1" :min-width="convertPX(555)">
            <template slot-scope="scope">
              <div v-html="scope.row.situation"></div>
            </template>
          </el-table-column>
          <el-table-column label="B-背景" v-if="handoverShowType.indexOf('B') != -1" :width="convertPX(230)">
            <template slot-scope="scope">
              <div v-html="scope.row.background"></div>
            </template>
          </el-table-column>
          <el-table-column label="A-评估" v-if="handoverShowType.indexOf('A') != -1" :width="convertPX(345)">
            <template slot-scope="scope">
              <div v-html="scope.row.assement"></div>
            </template>
          </el-table-column>
          <el-table-column label="R-建议" v-if="handoverShowType.indexOf('R') != -1" :width="convertPX(345)">
            <template slot-scope="scope">
              <div v-html="scope.row.recommendation"></div>
            </template>
          </el-table-column>
          <el-table-column :width="convertPX(98)" align="center" label="操作">
            <template slot-scope="scope">
              <el-tooltip content="修改" v-if="!scope.row.handonDate">
                <i class="iconfont icon-edit" @click.stop="showMorningSBAR(scope.row)"></i>
              </el-tooltip>
              <body-image
                v-if="scope.row.handoverID"
                :type="'tooltip'"
                :handoverID="scope.row.handoverID"
                :patientName="scope.row.patientName"
              ></body-image>
            </template>
          </el-table-column>
        </el-table>
        <el-dialog
          v-dialogDrag
          :close-on-click-modal="false"
          :title="dialogTitle"
          fullscreen
          v-if="showSbarFlag"
          :visible.sync="showSbarFlag"
        >
          <base-layout>
            <div slot="header">
              <div class="button-area">
                <el-button class="edit-button" icon="iconfont icon-refresh" @click="restoreSBAR">还 原</el-button>
                <el-button type="primary" icon="iconfont icon-save-button" @click="saveSBAR">保 存</el-button>
              </div>
            </div>
            <handover-rich-text v-model="sbarData" />
          </base-layout>
        </el-dialog>
      </div>
    </div>
  </base-layout>
</template>
<script>
import { GetShitHandoverSBAR, UpdateShiftSBAR } from "@/api/Handover/MultipleShifHandover";
import { GetMorningHandover, GetMorningHandoverReportTitle, SummaryMorningHandover } from "@/api/HandoverReport.js";
import { GetSettingValueByTypeCodeAsync } from "@/api/SettingDescription";
import HandoverRichText from "@/autoPages/handover/components/HandoverRichText";
import baseLayout from "@/components/BaseLayout";
import bodyImage from "@/components/bodyImage";
import stationSelector from "@/components/selector/stationSelector";
import { mapGetters } from "vuex";
import handoverReport from "../components/handoverReport";
import common from "@/utils/common";
export default {
  components: {
    baseLayout,
    stationSelector,
    handoverReport,
    HandoverRichText,
    bodyImage,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  mounted() {
    this.handoverDate = this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -1, "yyyy-MM-dd");
    this.stationID = this.user.stationID;
    this.getHandoverTitleView();
    this.getSettingValueByTypeCode();
  },
  data() {
    return {
      stationID: 0,
      handoverDate: undefined,
      handoverReportTitleView: {},
      // 日期不得大于当前日期
      checkDate: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        },
      },
      handoverList: [],
      updateHandover: {},
      sbarData: {},
      cloneSbarData: [],
      showSbarFlag: false,
      dialogTitle: "",
      currentInpatientArr: [],
      buttonName: undefined,
      checkedProp: "",
      loadingText: "查询中……",
      loading: false,
      handoverShowType: "",
      bedNumber: "", //床号作为查询条件
    };
  },
  methods: {
    //设置行唯一样式
    getRowClass({ row }) {
      return "bedNumber_" + row.bedNumber;
    },
    getDataByBedNumber() {
      const queryBedNumber = this.$refs.handoverDetailTable.$el.querySelector(".bedNumber_" + this.bedNumber);
      if (queryBedNumber) {
        queryBedNumber.scrollIntoView({ block: "start" });
      } else {
        this._showTip("warning", "未找到该床位患者选中分类的交班数据");
      }
    },
    /**
     * description: 获取按钮名称
     * return {*}
     */
    async getSettingValueByTypeCode() {
      let param = {
        SettingTypeCode: "MoringHandoverButtonName",
      };
      await GetSettingValueByTypeCodeAsync(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.buttonName = response?.data ?? "汇总晨交班";
        }
      });
      param = {
        SettingTypeCode: "MorningHandoverShowType",
      };
      await GetSettingValueByTypeCodeAsync(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.handoverShowType = response?.data ?? "ISBAR";
        }
      });
    },
    /**
     * description: 查询交班数据
     * return {*}
     */
    getHandoverTitleView() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return;
      }
      this.handoverReportTitleView = {};
      this.handoverList = [];
      let params = {
        stationID: this.stationID,
        startDate: this.handoverDate,
        endDate: this.handoverDate,
        showSignFlag: true,
      };
      GetMorningHandoverReportTitle(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverReportTitleView = res.data;
          this.handoverReportTitleView = Object.entries(res.data ?? {});
        }
      });
    },
    /**
     * description: 获取交班内容
     * param {*}
     * param {*}
     * return {*}
     */
    async getMorningHandover(prop, item) {
      this.checkedProp = prop;
      let params = {
        inpatientIDs: this.getInpatientIDArr(item.inpatientIDArr),
        handoverDate: this.handoverDate,
      };
      this.cloneRowData = this._common.clone(item);
      await GetMorningHandover(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.handoverList = res.data;
        }
      });
      //修复el-table表头错位
      this.$nextTick(() => {
        this.$refs.handoverDetailTable.doLayout();
      });
    },
    /**
     * description: 汇总晨交班内容
     * param {*}
     * return {*}
     */
    summaryMorningHandover() {
      let params = this.createHandoverParam();
      if (!params) {
        this._showTip("warning", "没有需要汇总的患者");
        return;
      }
      this.loading = true;
      SummaryMorningHandover(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data) {
            this.getHandoverTitleView();
            this.handoverList = [];
          }
        }
      });
    },
    /**
     * description: 创建汇总晨交班参数
     * param {*}
     * return {*}
     */
    createHandoverParam() {
      let handoverReportList = this.handoverReportTitleView.find((m) => m[0] === "signData")?.[1]?.[1];
      if (!handoverReportList) {
        return undefined;
      }

      let handoverReportArr = Object.values(handoverReportList);
      let inpatientIDArr = [];

      handoverReportArr.forEach((h) => {
        let childArr = this.getInpatientIDArr(h.inpatientIDArr);
        if (childArr) {
          inpatientIDArr.push(...childArr);
        }
      });

      if (inpatientIDArr.length === 0) {
        return undefined;
      }

      let result = {
        inpatientIDs: [...new Set(inpatientIDArr)],
        handoverDate: this.handoverDate,
      };
      return result;
    },
    /**
     * description: 获取患者主键 ID 字符串
     * param {*}
     * return {*}
     */
    getInpatientIDArr(inpatientArrString) {
      return inpatientArrString.length === 0 ? [] : inpatientArrString.split("||");
    },
    /**
     * description:展示数据
     * param {*} handover
     * return {*}
     */
    showMorningSBAR(handover) {
      this.updateHandover = handover;
      this.dialogTitle = handover.bedNumber + "床--" + handover.patientName;
      let params = {
        handoverID: handover.handoverID,
        handoverClass: "HandOff",
      };
      GetShitHandoverSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.sbarData = res.data?.sbarData ?? {};
          this.cloneSbarData = this._common.clone(this.sbarData);
          this.showSbarFlag = true;
        }
      });
    },
    /**
     * description: 还原数据
     * param {*}
     * return {*}
     */
    restoreSBAR() {
      let tempData = this._common.clone(this.cloneSbarData);
      this.sbarData = this.cloneSbarData;
      this.cloneSbarData = tempData;
    },
    /**
     * description: SBAR修改保存
     * return {*}
     */
    saveSBAR() {
      let params = {
        handoverCommonSaveView: {
          handoverID: this.updateHandover.handoverID,
          // 去除空的html标签
          situation: common.removeEmptyHtmlTags(this.sbarData?.situation?.value ?? ""),
          background: common.removeEmptyHtmlTags(this.sbarData?.background?.value ?? ""),
          assement: common.removeEmptyHtmlTags(this.sbarData?.assement?.value ?? ""),
          recommendation: common.removeEmptyHtmlTags(this.sbarData?.recommendation?.value ?? ""),
          inturduction: common.removeEmptyHtmlTags(this.sbarData?.inturduction?.value ?? ""),
          bodyPartImage: this.sbarData?.bodyPartImage?.value,
        },
      };
      UpdateShiftSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.showSbarFlag = false;
          this.getMorningHandover(this.checkedProp, this.cloneRowData);
        }
      });
    },
  },
};
</script>
<style lang="scss">
.morning-shift-report {
  .switch {
    margin-right: 5px;
    display: inline-block;
  }
  .top-btn {
    float: right;
  }
  .is-select {
    background-image: url("../../../../static/images/text-select.png");
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 40px;
  }
  .search-input {
    width: 160px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
}
.morning-report {
  height: 100%;
  .table-signData {
    .cell {
      height: 38px;
      line-height: 38px;
      cursor: pointer;
      & > div {
        height: 100%;
      }
    }
  }
  .morning-report-sbar {
    height: calc(100% - 180px);
    overflow: hidden;
    overflow-y: auto;
  }
  .show-sign {
    height: calc(100% - 80px);
  }
  .table-sbar {
    .cell {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .title {
      text-align: center;
    }
    td {
      border-bottom-color: #8f8f8f !important;
    }
    .inturduction {
      font-size: 15px;
    }
  }
  .el-table td div {
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
  .button-area {
    float: right;
  }
  .icon-body {
    color: $base-color;
    font-size: 20px;
    font-weight: 600;
  }
}
</style>
