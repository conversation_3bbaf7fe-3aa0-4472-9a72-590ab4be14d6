<!--
 * FilePath     : \src\pages\help\index.vue
 * Author       : 苏军志
 * Date         : 2022-09-06 19:03
 * LastEditors  : 苏军志
 * LastEditTime : 2022-09-08 15:32
 * Description  : 
 * CodeIterationRecord: 
-->

<template>
  <div class="help-index">
    <el-tabs v-model="componentName">
      <el-tab-pane label="版本更新" name="systemVersion"></el-tab-pane>
      <el-tab-pane label="常见问题" name="commonProblem"></el-tab-pane>
      <el-tab-pane label="插件下载" name="pluginDownload"></el-tab-pane>
    </el-tabs>
    <div class="component-wrap">
      <component :is="componentName"></component>
    </div>
  </div>
</template>
<script>
import systemVersion from "./components/systemVersion.vue";
import commonProblem from "./components/commonProblem.vue";
import pluginDownload from "./components/pluginDownload.vue";
export default {
  components: {
    systemVersion,
    commonProblem,
    pluginDownload,
  },
  data() {
    return {
      componentName: "systemVersion",
    };
  },
};
</script>
<style lang="scss">
.help-index {
  height: 100%;
  .el-tabs {
    height: 45px;
    background-color: #ffffff;
    .el-tabs__nav {
      margin-left: 20px;
    }
  }
  .component-wrap {
    height: calc(100% - 45px);
  }
}
</style>
