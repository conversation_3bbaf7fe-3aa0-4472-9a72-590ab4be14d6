<!--
 * FilePath     : \src\components\tabsLayout\bdItem.vue
 * Author       : 苏军志
 * Date         : 2021-06-21 15:11
 * LastEditors  : 苏军志
 * LastEditTime : 2022-12-06 17:51
 * Description  : 评估模板中BD类型
-->

<template>
  <div class="bd-item">
    <div class="record-wrap">
      <div
        class="bd-item-name"
        v-longpress="{ function: 'showMessage', params: item.description }"
        :title="item.description"
      >
        {{ item.itemName ? item.itemName.trim() + "：" : "" }}
      </div>
      <ux-grid
        v-if="bdRecords && bdRecords.length > 0"
        :data="bdRecords"
        stripe
        :widthResize="true"
        :edit-config="{ trigger: 'click', mode: 'cell' }"
      >
        <ux-table-column
          v-for="(item, index) in bdTemplate"
          :key="index"
          :title="item.title"
          :field="item.assessListID"
          header-align="center"
          edit-render
          :width="getItemWidth(item)"
          :align="getItemAlign(item)"
        >
          <template slot="edit" slot-scope="scope">
            <template v-if="item.controlerType === 'T'">
              <el-input v-model="scope.row[item.assessListID]" @change="setResult">
                <template slot="append" v-if="item.unit != null && item.unit">{{ item.unit.trim() }}</template>
              </el-input>
            </template>
            <template v-if="item.controlerType === 'TN'">
              <el-input
                v-model="scope.row[item.assessListID]"
                @blur="setResult"
                @click.native="showKeyBoard($event)"
                @input="updateValue($event, scope.row, item.assessListID)"
                :readonly="isReadOnly"
              >
                <template slot="append" v-if="item.unit != null && item.unit">{{ item.unit.trim() }}</template>
              </el-input>
            </template>
            <template v-if="item.controlerType === 'D'">
              <el-date-picker
                v-model="scope.row[item.assessListID]"
                type="date"
                :clearable="false"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                style="width: 120px"
                @change="setResult"
              ></el-date-picker>
            </template>
            <template v-if="item.controlerType === 'DT'">
              <el-date-picker
                v-model="scope.row[item.assessListID]"
                type="datetime"
                :clearable="false"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                style="width: 150px"
                @change="setResult"
              ></el-date-picker>
            </template>
            <template v-if="item.controlerType === 'TM'">
              <el-time-picker
                v-model="scope.row[item.assessListID]"
                placeholder="选择时间"
                :clearable="false"
                value-format="HH:mm"
                format="HH:mm"
                style="width: 80px"
                @change="setResult"
              ></el-time-picker>
            </template>
            <template v-if="item.controlerType === 'DL'">
              <el-select
                v-model="scope.row[item.assessListID]"
                :multiple="item.isMultiSelect"
                placeholder="请选择"
                @change="setResult"
                :style="{ width: getItemWidth(item) - 10 + 'px' }"
              >
                <el-option
                  v-for="(option, index) in item.dlOptions"
                  :key="index"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
            </template>
            <template v-if="item.controlerType === 'CS'">
              <el-cascader
                :props="{ emitPath: false, expandTrigger: 'hover' }"
                :options="item.selectOptionList"
                :show-all-levels="false"
                v-model="scope.row[item.assessListID]"
                @change="setResult"
                filterable
                :style="{ width: getItemWidth(item) - 10 + 'px' }"
              ></el-cascader>
            </template>
          </template>
          <template slot-scope="{ row }">
            <span v-if="item.controlerType === 'DL'" class="bd-value">
              {{ getLabel(row[item.assessListID], item.dlOptions, "value", "label", false, false) }}
            </span>
            <span v-else-if="item.controlerType === 'CS'" class="bd-value">
              {{ getLabel(row[item.assessListID], item.selectOptionList, "value", "label", false, true) }}
            </span>
            <template v-else>
              <span v-if="row[item.assessListID]" class="bd-value">
                {{ row[item.assessListID] }}
                <span v-if="row[item.assessListID] && item.unit != null && item.unit">{{ item.unit.trim() }}</span>
              </span>
            </template>
          </template>
        </ux-table-column>
        <ux-table-column title="操作" width="50" align="center">
          <template slot-scope="scope">
            <el-tooltip content="删除">
              <div class="iconfont icon-del" @click="deleteRecord(scope.row)"></div>
            </el-tooltip>
          </template>
        </ux-table-column>
      </ux-grid>
      <el-button type="primary" class="add" @click="addRecord">+</el-button>
    </div>
    <key-board
      v-tobody="{ id: 'key-board' }"
      :show="isShowKeyBoard"
      :output="el"
      typeName="TN"
      @hide="hideKeyBoard"
    ></key-board>
  </div>
</template>

<script>
import keyBoard from "@/components/KeyBoard/KeyBoard";
import { GetBDTemplate } from "@/api/Assess";
export default {
  components: {
    keyBoard,
  },
  props: {
    item: { require: true },
    assessTime: {},
  },
  watch: {
    item: {
      immediate: true,
      deep: true,
      handler(newValue) {
        if (!this.bdTemplate || this.bdTemplate.length <= 0) {
          this.getBDTemplate();
        } else {
          this.getData();
        }
      },
    },
  },
  data() {
    return {
      // 表格模板
      bdTemplate: [],
      // 维护中的数据
      bdRecords: [],
      isReadOnly: false,
      isShowKeyBoard: false,
      el: undefined,
    };
  },
  methods: {
    getBDTemplate() {
      this.bdTemplate = [];
      let params = {
        recordsCode: this.item.linkForm,
        // 参数添加随机数，防止同页面多BD时请求被拦截
        index: Math.random(),
      };
      GetBDTemplate(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.bdTemplate = result.data;
          this.getData();
        }
      });
    },

    getData() {
      this.bdRecords = [];
      // 修改，获取历史数据
      if (this.item.assessValue) {
        this.bdRecords = JSON.parse(this.item.assessValue);
        if (!this.bdRecords || this.bdRecords.length <= 0) {
          this.addRecord();
        }
      } else {
        this.addRecord();
      }
      this.setResult();
    },
    addRecord() {
      // 新增一条数据
      let record = {};
      this.bdTemplate.forEach((template) => {
        record[template.assessListID] = "";
        // 如果配置的可以拷贝，取最后一条记录的值
        if (template.canCopy && this.bdRecords && this.bdRecords.length > 0) {
          record[template.assessListID] = this.bdRecords[this.bdRecords.length - 1][template.assessListID];
        }
      });
      this.bdRecords.push(record);
    },
    deleteRecord(record) {
      let index = -1;
      for (let i = 0; i < this.bdRecords.length; i++) {
        if (this.bdRecords[i] == record) {
          index = i;
          break;
        }
      }
      this.bdRecords.splice(index, 1);
      this.setResult();
    },
    setResult() {
      let savaRecords = [];
      if (this.bdRecords && this.bdRecords.length > 0) {
        this.bdRecords.forEach((record) => {
          let tempRecord = this._common.clone(record);
          // 去除ux-grid特有的属性
          if (tempRecord._XID) {
            delete tempRecord._XID;
          }
          let hasValue = false;
          for (var key in tempRecord) {
            if (tempRecord[key]) {
              hasValue = true;
              break;
            }
          }
          if (hasValue) {
            savaRecords.push(tempRecord);
          }
        });
      }
      if (savaRecords.length <= 0) {
        this.item.assessValue = "";
      } else {
        this.item.assessValue = JSON.stringify(savaRecords);
      }
      this.$emit("change", this.item);
    },
    getItemAlign(item) {
      let align = "left";
      if (item.controlerType == "TN") {
        align = "center";
      } else if (item.controlerType == "D") {
        align = "center";
      } else if (item.controlerType == "DT") {
        align = "center";
      } else if (item.controlerType == "TM") {
        align = "center";
      } else if (item.controlerType == "DL") {
        align = "center";
      }
      return align;
    },
    getItemWidth(item) {
      let width = Number(item.width);
      if (item.controlerType == "D") {
        width = 130;
      } else if (item.controlerType == "DT") {
        width = 160;
      } else if (item.controlerType == "TM") {
        width = 90;
      } else {
        if (!width) {
          if (item.controlerType == "T") {
            width = 100;
          } else if (item.controlerType == "TN") {
            width = 100;
          } else if (item.controlerType == "DL") {
            width = 140;
          } else if (item.controlerType == "CS") {
            width = 180;
          } else {
            width = 100;
          }
        }
      }
      item.unit && (width += 40);
      return width;
    },
    showMessage(messageContent) {
      this._showMessage({
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
    updateValue(value, item, key) {
      item[key] = value;
    },
    showKeyBoard(e) {
      // 判断浏览器类型，PC返回true，移动端返回false
      if (this._common.isPC()) {
        this.isReadOnly = false;
        return;
      }
      this.isReadOnly = true;
      this.el = e.target;
      this.isShowKeyBoard = true;
    },
    hideKeyBoard() {
      this.el = undefined;
      this.isShowKeyBoard = false;
    },
    // 获取下拉框对应的名称
    getLabel(value, items, key, name, isNumber, hasChild) {
      if (!value) {
        return "";
      }
      let values = [];
      if (typeof value == "object") {
        values = value;
      } else {
        values = this.stringToArray(value, isNumber);
      }
      let label = "";
      values.forEach((val) => {
        let text = this.getText(val, items, key, name, hasChild);
        if (text) {
          if (label) {
            label += "," + text;
          } else {
            label = text;
          }
        }
      });
      return label;
    },
    getText(val, items, key, name, hasChild) {
      let text = "";
      items.forEach((item) => {
        if (text) {
          return text;
        }
        if (item[key] == val) {
          text = item[name];
          return text;
        }
        if (hasChild && item.children && item.children != null && item.children != "null" && item.children.length > 0) {
          text = this.getText(val, item.children, key, name, hasChild);
          return text;
        }
      });
      return text;
    },
    stringToArray(str, isNumber) {
      let array = [];
      if (typeof str == "string" && str.indexOf(",") != -1) {
        let strs = str.split(",");
        if (!isNumber) {
          array = strs;
        } else {
          strs.forEach((id) => {
            array.push(Number(id));
          });
        }
      } else {
        if (isNumber) {
          array = [Number(str)];
        } else {
          array = [str];
        }
      }
      return array;
    },
  },
};
</script>

<style lang="scss">
.bd-item {
  .record-wrap {
    display: flex;
    .bd-item-name {
      white-space: nowrap;
    }
    .el-button.add {
      height: 32px;
      line-height: 15px;
      font-size: 24px;
    }
    td.col--actived .elx-cell {
      padding: 0 5px 0 2px;
    }
    .elx-table .elx-body--column.col--ellipsis > .elx-cell,
    .elx-table .elx-footer--column.col--ellipsis > .elx-cell,
    .elx-table .elx-header--column.col--ellipsis > .elx-cell {
      max-height: 30px;
    }
    .elx-table--body-wrapper.body--wrapper::-webkit-scrollbar {
      display: none;
    }
    .singleTable.elx-grid {
      width: calc(100% + 1px) !important;
    }
    .elx-body--y-space {
      display: none;
    }
    .el-input__inner {
      height: 26px;
      line-height: 26px;
    }
    .el-input-group__append {
      padding: 0 5px;
      color: $base-color;
    }
    .bd-value {
      display: block;
      text-align: left;
    }
  }
}
</style>