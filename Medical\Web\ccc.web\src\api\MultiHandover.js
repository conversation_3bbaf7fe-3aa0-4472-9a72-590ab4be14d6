/*
 * FilePath     : \projectManagement.webe:\CCC3.1\Medical\Web\ccc.web\src\api\MultiHandover.js
 * Author       : 李正元
 * Date         : 2020-06-23 16:42
 * LastEditors  : 马超
 * LastEditTime : 2023-03-28 08:41
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/MultiHandover";

export const urls = {
  MultiHandOff: baseUrl + "/MultiHandOff",
  GetMultiHandOff: baseUrl + "/GetMultiHandOff",
  MultiHandOffCheck: baseUrl + "/MultiHandOffCheck",
  UpdateMultiShiftHandoff: baseUrl + "/UpdateMultiShiftHandoff",
  MultiHandOn: baseUrl + "/MultiHandOn",
  GetPreviousMultiHandOff: baseUrl + "/GetPreviousMultiHandOff",
  SaveKeySign: baseUrl + "/SaveKeySign"
};

//交班汇总
export const MultiHandOff = params => {
  return http.post(urls.MultiHandOff, qs.stringify(params));
};

//正常批量交班
export const GetMultiHandOff = params => {
  return http.get(urls.GetMultiHandOff, params);
};

//交班汇总检查
export const MultiHandOffCheck = params => {
  return http.post(urls.MultiHandOffCheck, qs.stringify(params));
};

//修改正常交班内容
export const UpdateMultiShiftHandoff = params => {
  return http.post(urls.UpdateMultiShiftHandoff, qs.stringify(params));
};

// 提交批量接班内容
export const MultiHandOn = params => {
  return http.post(urls.MultiHandOn, qs.stringify(params));
};

//批量接班查询
export const GetPreviousMultiHandOff = params => {
  return http.get(urls.GetPreviousMultiHandOff, params);
};

// 保存交班注记
export const SaveKeySign = params => {
  return http.post(urls.SaveKeySign, qs.stringify(params));
};
