<!--
 * FilePath     : \src\autoPages\handover\dischargeHandover.vue
 * Author       : 郭鹏超
 * Date         : 2023-01-09 14:41
 * LastEditors  : 曹恩
 * LastEditTime : 2025-03-26 15:04
 * Description  : 出院小结
 * CodeIterationRecord:
-->
<template>
  <specific-care
    class="discharge-handover"
    v-model="showTemplateFlag"
    drawerSize="80%"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :previewFlag="saveButtonShowFlag"
    :recordTitleSlotFalg="true"
    @mainAdd="handoverAdd"
    @save="dischargeHandoverSave"
    @cancel="drawerClose"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <div slot="main-record">
      <el-table ref="dischargeHandoverTable" :data="handoverRecordList" height="100%" border stripe>
        <el-table-column prop="recordsName" label="交班类型" :width="convertPX(100)" align="center"></el-table-column>
        <el-table-column prop="stationName" label="病区" :width="convertPX(100)" align="center"></el-table-column>
        <el-table-column prop="handoverDate" label="日期" :width="convertPX(170)" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.handoverDate, type: 'date' }"></span>
            <span v-formatTime="{ value: scope.row.handoverTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="S-状况" :min-width="convertPX(60)">
          <template slot-scope="scope">
            <span v-html="scope.row.situation"></span>
          </template>
        </el-table-column>
        <el-table-column label="B-背景" :min-width="convertPX(60)">
          <template slot-scope="scope">
            <span v-html="scope.row.background"></span>
          </template>
        </el-table-column>
        <el-table-column label="A-评估" :min-width="convertPX(60)">
          <template slot-scope="scope">
            <span v-html="scope.row.assement"></span>
          </template>
        </el-table-column>
        <el-table-column label="R-建议" :min-width="convertPX(60)">
          <template slot-scope="scope">
            <span v-html="scope.row.recommendation"></span>
          </template>
        </el-table-column>
        <el-table-column
          prop="handoverNurseName"
          label="交接护士"
          :width="convertPX(120)"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" width="70" align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div class="iconfont icon-edit" @click="handoverAdd(scope.row)"></div>
            </el-tooltip>
            <el-tooltip :class="{ visibility: !getDeleteShowFlag(scope.row) }" content="删除">
              <div class="iconfont icon-del" @click="deleteHandoverDischarge(scope.row)"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading.fullscreen.lock="layoutLoading"
      :element-loading-text="layoutLoadingText"
    >
      <div slot="header">
        <span>日期:</span>
        <el-date-picker
          v-model="handoverDate"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          class="date-picker"
          :clearable="false"
        ></el-date-picker>
        <span>时间:</span>
        <el-time-picker
          v-model="handoverTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          class="time-picker"
        ></el-time-picker>
        <template v-if="!!refillFlag">
          <station-selector v-model="handoverStationID" :inpatientID="patient.inpatientID" />
          <dept-selector v-model="handoverDepartmentID" :stationID="handoverStationID" />
        </template>
        <span>类型:</span>
        <el-select
          :disabled="!handoverIDArr[0]"
          v-model="recordsCode"
          class="handover-type-select"
          placeholder="请选择"
          @change="getHandoverPaneList"
        >
          <el-option
            v-for="item in handoverTypeArr"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <div class="drawer-content">
        <el-tabs class="handover-tabs" v-model="paneKey" @tab-click="paneChange($event.index)">
          <el-tab-pane
            v-for="(item, index) in handoverPaneList"
            :key="index"
            :label="item.label"
            :name="item.value"
            class="tab-pane"
          ></el-tab-pane>
        </el-tabs>
        <div class="pane-content">
          <el-table v-if="paneKey == 'Risk'" :data="riskList" style="width: 100%" height="100%" border stripe>
            <el-table-column prop="recordName" label="风险评估量表" :min-width="convertPX(100)">
              <template slot-scope="scope">
                <span :class="{ 'unconfirm-risk': !scope.row.confirm }">{{ scope.row.recordName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="scoreRangeContent"
              align="center"
              label="风险等级"
              :min-width="convertPX(60)"
            ></el-table-column>
            <el-table-column prop="point" align="center" label="分值" :width="convertPX(60)"></el-table-column>
            <el-table-column prop="recordName" align="center" label="状态" :width="convertPX(100)">
              <template slot-scope="scope">
                <span :class="{ 'unconfirm-risk': !scope.row.confirm }">
                  {{ scope.row.confirm ? "已确认" : "未确认" }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="recordName" align="center" label="操作" :width="convertPX(60)">
              <template slot-scope="scope">
                <el-tooltip content="修改">
                  <div class="iconfont icon-edit" @click="getPaneRiskParams(scope.row)"></div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <tabs-layout
            v-if="paneKey == 'Assessment'"
            ref="tabsLayout"
            :template-list="templateDatas"
            :disabled="disabled"
            @button-click="buttonClick"
            @change-values="changeValues"
            @button-record-click="getBrRiskParams"
            @checkTN="checkTN"
            :checkFlag="true"
          />
          <handover-rich-text ref="handover" v-if="paneKey == 'Handover'" v-model="sbarData" />
          <patient-evaluation
            v-if="paneKey == 'Evaluate'"
            ref="evaluate"
            :inpatientid="patient ? patient.inpatientID : ''"
            :stationid="patient && patient.stationID ? patient.stationID : 0"
            :showcommit="false"
            :all-Flag="false"
            :assessDate="handoverDate"
            :assessTime="handoverTime"
          ></patient-evaluation>
        </div>
      </div>
    </base-layout>
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      v-if="showButtonDialog"
      fullscreen
      custom-class="no-footer"
      slot="drawer-dialog"
      :modal-append-to-body="false"
      @close="updateButton"
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
    </el-dialog>
    <el-dialog
      v-if="showButtonRecordDialog"
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonRecordTitle"
      :visible.sync="showButtonRecordDialog"
      slot="drawer-dialog"
      custom-class="no-footer"
      :modal-append-to-body="false"
    >
      <risk-component :params="componentParams" @result="getRiskResult"></risk-component>
    </el-dialog>
  </specific-care>
</template>

<script>
import { mapGetters } from "vuex";
import specificCare from "@/components/specificCare";
import baseLayout from "@/components/BaseLayout";
import tabsLayout from "@/components/tabsLayout/index";
import handoverRichText from "@/autoPages/handover/components/HandoverRichText";
import patientEvaluation from "@/pages/nursingEvaluation/patientEvaluation";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import {
  GetHandOverType,
  GetHandoverPaneList,
  GetHandoverRisk,
  DeleteHandoverRisk,
  UpdateSBARContent,
} from "@/api/Handover/HandoverCommonUse";
import { GetButtonData } from "@/api/Assess";
import {
  GetHandOverDischargeAssessTemplate,
  GetHandoverDischargeSBAR,
  SaveDischargeHandoverSBAR,
  GetHandoverDischargeList,
  DeleteHandoverDischarge,
  SaveDischargeHandoverAssess,
} from "@/api/Handover/HandoverDischarge";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";

export default {
  components: {
    specificCare,
    baseLayout,
    tabsLayout,
    handoverRichText,
    patientEvaluation,
    riskComponent,
    stationSelector,
    deptSelector,
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      user: "getUser",
    }),
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    handoverData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      patient: undefined,
      showTemplateFlag: false,
      saveFlag: false,
      drawerTitle: "出院小结",
      showRecordArr: [true, false],
      loading: false,
      searchHandoverType: undefined,
      handoverDate: undefined,
      handoverTime: undefined,
      handoverStationID: undefined,
      handoverDepartmentID: undefined,
      handoverType: "DischargeAssess",
      handoverClass: "HandOff",
      recordsCode: undefined,
      handoverTypeArr: [],
      handoverRecordList: [],
      layoutLoading: false,
      layoutLoadingText: undefined,
      handoverPaneList: [],
      copyHandoverPaneList: [],
      paneKey: undefined,
      paneIndex: undefined,
      riskList: [],
      templateDatas: [],
      disabled: false,
      checkTNFlag: true,
      showButtonDialog: false,
      buttonName: undefined,
      sbarData: {},
      //[0]为true 表示新增 false表示修改
      handoverIDArr: [true, this._common.guid()],
      currentHandover: undefined,
      assessValues: [],
      //BR
      componentParams: undefined,
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR项
      brFlag: false,
      brItem: undefined,
      //补录标记
      refillFlag: undefined,
      //死亡
      deathSwitch: false,
      deathFlag: false,
      deathAssessListID: 3578,
      daethNotAssessBookMarkID: ["20", "40"],
      daethNotAssessBookMarkViews: [],
      showRisk: true,
      saveButtonShowFlag: true,
    };
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = undefined;
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.refillFlag = "*";
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        newVal && this.init();
      },
      immediate: true,
    },
    /**
     * description: 死亡处理
     * return {*}
     */
    deathFlag: {
      handler(val) {
        this.deathDealWith(val);
      },
    },
  },
  methods: {
    /**
     * description: 页面初始化
     * return {*}
     */
    async init() {
      //获取页面配置
      await this.getPageSetting();
      await this.getHandoverRecordList();
      //页面自动化处理
      this.pageAuto();
    },
    /**
     * description: 页面自动化处理  无数据自动弹窗  有传入HandoverID自动筛选弹窗
     * return {*}
     */
    async pageAuto() {
      //补录不自动化
      if (this.refillFlag == "*") {
        return;
      }
      //无记录自动弹窗
      if (!this.handoverRecordList?.length) {
        await this.handoverAdd();
        return;
      }
      //有跳转ID 直接找到弹窗
      let routeHandoverID = this.$route.query?.handoverID ?? this.handoverData?.handoverID ?? undefined;
      if (!routeHandoverID) {
        return;
      }
      let sucHandover = this.handoverRecordList.find((handover) => handover.handoverID == routeHandoverID);
      sucHandover && (await this.handoverAdd(sucHandover));
    },
    /**
     * description: 获取出院小结列表
     * return {*}
     */
    async getHandoverRecordList() {
      let params = {
        inpatientID: this.patient.inpatientID,
        handoverType: this.handoverType,
      };
      this.loading = true;
      await GetHandoverDischargeList(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data?.length) {
            res.data.forEach((item) => {
              Object.assign(item, item?.handoverCommonTableView ?? {});
            });
          }
          this.handoverRecordList = res.data;
          this.$nextTick(() => {
            this.$refs.dischargeHandoverTable.doLayout();
          });
        }
      });
    },
    /**
     * description: 记录新增修改
     * return {*}
     */
    async handoverAdd(item) {
      if (!item && this.handoverRecordList?.length >= 1) {
        this._showTip("warning", "已有出院小结记录");
        return;
      }
      if (this.refillFlag && this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = item
          ? await this._common.userSelectorDisabled(this.user.userID, false, true, item.handoverNurse)
          : await this._common.userSelectorDisabled(this.user.userID, true, true, "");
        this.saveButtonShowFlag = !saveButtonFlag;
      } else {
        this.saveButtonShowFlag = this.handoverData.handonFlag;
      }
      this.saveFlag = false;
      this.currentHandover = item ?? undefined;
      this.handoverDate = item?.handoverDate ?? this._datetimeUtil.getNowDate();
      this.handoverTime = item?.handoverTime ?? this._datetimeUtil.getNowTime("hh:mm");
      this.handoverStationID = item?.stationID ?? this.patient.stationID;
      this.handoverDepartmentID = item?.departmentListID ?? this.patient.departmentListID;
      this.recordsCode = item?.recordsCode ?? this.searchHandoverType;
      this.handoverIDArr = [!item, item?.handoverID ?? this._common.guid()];
      this.handoverID = this.handoverIDArr[1];
      this.handoverClass = "HandOff";
      this.drawerTitle = "出院小结";
      //页签初始化
      if (this.handoverPaneList?.length) {
        this.paneChange(0);
      }
      this.openOrCloseDrawer(true, this.drawerTitle);
    },

    /**
     * description: 页签切换初始化
     * param {*} index
     * return {*}
     */
    async paneChange(index) {
      this.paneIndex = index;
      this.paneKey = this.handoverPaneList[index]?.value;
      //评价无需初始化 或者 初始化方法没有直接返回
      if (this.paneKey == "Evaluate" || !this.handoverPaneList[index]?.initMethod) {
        return;
      }
      this.layoutLoading = true;
      this.layoutLoadingText = "加载中……";
      await this.handoverPaneList[index]?.initMethod();
      this.layoutLoading = false;
    },
    /**
     * description: 风险页签初始化
     * return {*}
     */
    initRisk() {
      if (!this.patient?.inpatientID) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        handoverID: this.handoverIDArr[1],
      };
      GetHandoverRisk(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.riskList = res.data ?? [];
        }
      });
    },
    /**
     * description:页签风险修改按钮
     * param {*} row
     * return {*}
     */
    getPaneRiskParams(row) {
      this.brFlag = false;
      this.buttonRecordTitle = row.recordName;
      this.componentParams = {
        patientInfo: this.patient,
        showPoint: row.showPointFlag,
        showTime: true,
        showStyle: row.showStyle,
        showBar: true,
        recordListID: row.recordID,
        recordsCode: row.recordsCode,
        sourceID: this.handoverIDArr[1],
        sourceType: "DischargeHandoverBR",
        readOnly: this.disabled,
        assessTime:
          this._datetimeUtil.formatDate(this.handoverDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.handoverTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 评估内容初始化
     * return {*}
     */
    async initAssessment() {
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCode,
        sourceType: this.handoverType,
        dischargeHandoverID: this.handoverIDArr[1],
        handoverClass: this.handoverClass,
      };
      await GetHandOverDischargeAssessTemplate(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.templateDatas = res.data;
          //初始化处理死亡勾选
          this.deathSwitch && this.deathDealWith(this.deathFlag);
        }
      });
    },
    /**
     * description: SBAR初始化
     * return {*}
     */
    async initHandover() {
      let params = {
        handoverID: this.handoverIDArr[1],
        handoverClass: this.handoverClass,
        inpatientID: this.patient.inpatientID,
        stationID: this.handoverStationID,
        recordsCode: this.recordsCode,
        handoverDate: this.handoverDate,
        handoverTime: this.handoverTime,
        handoverNurse: this.user.userID,
      };
      await GetHandoverDischargeSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.sbarData = res.data?.sbarData ?? {};
        }
      });
    },

    /**
     * description: 出院小结保存
     * return {*}
     */
    async dischargeHandoverSave() {
      this.layoutLoading = true;
      this.layoutLoadingText = "保存中……";
      await this.handoverPaneList[this.paneIndex].saveMethod();
      this.layoutLoading = false;
      if (Number(this.paneIndex) == this.handoverPaneList.length) {
        this.openOrCloseDrawer(false);
      }
      //保存后重新刷新交班表格数据 没有保存 不需要刷新
      this.saveFlag = true;
      this.saveFlag && this.getHandoverRecordList();
    },
    /**
     * description: 风险保存
     * return {*}
     */
    saveRisk() {
      if (this.saveRiskCheck()) {
        return;
      }
      this.paneChange(Number(this.paneIndex) + 1);
    },
    /**
     * description: 评估内容保存
     * return {*}
     */
    async saveAssessment() {
      if (!this.checkTNFlag || (!this.deathSwitch && this.saveRiskCheck())) {
        this.checkTNFlag = true;
        return false;
      }
      let params = this.getSBARSaveView();
      if (!params.handoverCommonSaveView?.assessContentList?.length) {
        return;
      }
      await SaveDischargeHandoverAssess(params).then((res) => {
        if (this._common.isSuccess(res)) {
          res?.data && (this.handoverIDArr = [false, res?.data]);
          this._showTip("success", "保存成功！");
          this.paneChange(Number(this.paneIndex) + 1);
        }
      });
    },
    /**
     * description: SBAR内容保存
     * return {*}
     */
    async saveHandover() {
      if (this.saveRiskCheck()) {
        return false;
      }
      let params = this.getSBARSaveView();
      await SaveDischargeHandoverSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          res?.data && (this.handoverIDArr = [false, res?.data]);
          this._showTip("success", "保存成功！");
          this.paneChange(Number(this.paneIndex) + 1);
        }
      });
    },
    /**
     * description: 评价保存
     * return {*}
     */
    saveEvaluate() {
      this.$refs?.evaluate?.commit();
      this.getHandoverRecordList();
    },

    /**
     * description: 交接班删除
     * param {*} item
     * return {*}
     */
    async deleteHandoverDischarge(item) {
      if (this.refillFlag && this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          item.handoverNurse
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this.currentHandover = item;
      this.handoverIDArr = [!item, item?.handoverID];
      this.handoverClass = item?.handoverClass;
      let params = {
        handoverID: this.handoverIDArr[1],
        handoverClass: this.handoverClass,
      };
      this._deleteConfirm("", (flag) => {
        flag &&
          DeleteHandoverDischarge(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getHandoverRecordList();
            }
          });
      });
    },
    /**
     * description: 风险删除
     * return {*}
     */
    deleteRisk() {
      let params = {
        inpatientID: this.patient.inpatientID,
        handoverID: this.handoverIDArr[1],
        sourceType: "DischargeHandover",
      };
      DeleteHandoverRisk(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "风险删除成功");
        }
      });
    },
    /**
     * description: 风险保存按钮检核
     * return {*}
     */
    saveRiskCheck() {
      if (!this.riskList?.length || !this.showRisk) {
        return false;
      }
      let notConfirmRisk = this.riskList.find((m) => !m.confirm);
      if (notConfirmRisk) {
        this._showTip("warning", "您有未确认风险评分");
        return true;
      }
      return false;
    },

    /**
     * description: 弹窗关闭 没有保存记录无需重新拉去数据
     * return {*}
     */
    drawerClose() {
      if (this.paneKey == "Risk") {
        this.drawerCloseRiskSaveCheck();
      } else {
        this.openOrCloseDrawer(false);
      }

      this.saveFlag && this.getHandoverRecordList();
    },
    /**
     * description: 弹窗只保存风险内容 关闭弹窗删除保存风险
     * return {*}
     */
    drawerCloseRiskSaveCheck() {
      let confirmRisk = this.riskList.find((m) => m.confirm);
      if (confirmRisk && this.handoverIDArr[0]) {
        let message = "您只保存了风险内容,如果关闭当前弹窗,会将您保存的风险内容删除,您确定要关闭么?";
        this._confirm(message, "弹窗关闭确认", (flag) => {
          if (flag) {
            this.deleteRisk();
            this.openOrCloseDrawer(false);
          }
        });
      } else {
        this.openOrCloseDrawer(false);
      }
    },
    /**
     * description: 组装保存参数
     * return {*}
     */
    getSBARSaveView() {
      let view = {
        handoverID: this.handoverIDArr[1],
        handoverClass: this.handoverClass,
        handoverType: this.handoverType,
        recordsCode: this.recordsCode,
        handoverDate: this.handoverDate,
        handoverTime: this.handoverTime,
        handoverNurse: this.user.userID,
      };
      Object.assign(view, this.patient);
      view.stationID = this.handoverStationID;
      view.departmentListID = this.handoverDepartmentID;
      //评估获取明细
      if (this.paneKey == "Assessment") {
        view.assessContentList = this.getAssessContentValue();
      }
      //SBAR获取组件内容
      if (this.paneKey == "Handover") {
        view.situation = this.sbarData?.situation?.value;
        view.background = this.sbarData?.background?.value;
        view.assement = this.sbarData?.assement?.value;
        view.recommendation = this.sbarData?.recommendation?.value;
        view.bodyPartImage = this.sbarData?.bodyPartImage?.value;
      }
      view.refillFlag = this.refillFlag;
      let allView = {
        handoverCommonSaveView: view,
      };
      return allView;
    },
    /**
     * description: 组装保存模板明细
     * return {*}
     */
    getAssessContentValue() {
      if (!this.assessValues?.length) {
        return [];
      }
      //检核必选项
      if (!this.$refs?.tabsLayout.checkRequire()) {
        return undefined;
      }
      let details = [];
      let flag = true;
      for (let i = 0; i < this.assessValues.length; i++) {
        let content = this.assessValues[i];
        // 按钮不处理
        if (content.controlerType.trim() == "B") {
          continue;
        }
        //检核TN内容
        let result = this._common.checkAssessTN(content);
        if (!result.flag) {
          flag = false;
          break;
        }
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          controlerType: content.controlerType,
        };
        if (["C", "R"].includes(content.controlerType.trim())) {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        details.push(detail);
      }
      if (!flag) {
        return [];
      }
      return details;
    },

    /**
     * description: 删除按钮权限控制  只有已保存数据和本病区数据才可删除
     * param {*} handover
     * return {*}
     */
    getDeleteShowFlag(handover) {
      return handover.stationID == this.user?.stationID;
    },
    /**
     * description: 风险组件保存回调  br保存完回显按钮数据  页签风险保存完重新获取数据
     * param {*} resultFlag
     * return {*}
     */
    getRiskResult(resultFlag) {
      this.showButtonRecordDialog = false;
      if (!resultFlag) {
        return;
      }
      const riskResultMethod = {
        assessRisk: () => {
          this.initRisk();
          this.updateHandoverContent();
        },
        brRisk: () => {
          if (resultFlag) {
            // 保存成功，回显数据
            this.updateButton(this.brItem.assessListID);
          }
        },
      };
      this.brFlag ? riskResultMethod.brRisk() : riskResultMethod.assessRisk();
    },
    /**
     * @description: 更新SBAR内容
     * @return
     */
    updateHandoverContent() {
      const params = {
        inpatientID: this.patient.inpatientID,
        stationID: this.handoverStationID,
        handoverID: this.handoverIDArr[1],
        handoverDate: this.handoverDate,
        handoverTime: this.handoverTime,
        updateTypes: ["A"],
        handoverClass: this.handoverClass,
        recordsCode: this.recordsCode,
      };
      UpdateSBARContent(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.getHandoverRecordList();
        }
      });
    },
    /**
     * description: 专项跳转
     * param {*} content
     * return {*}
     */
    buttonClick(content) {
      if (!this.deathSwitch && this.saveRiskCheck()) {
        return;
      }
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url =
        url +
        (url.includes("?") ? "&" : "?") +
        "handoverID=" +
        this.handoverID +
        "&userID=" +
        this.user.userID +
        "&isDialog=true" +
        "&bedNumber=" +
        this.patient.bedNumber.replace(/\+/g, "%2B") +
        "&isDischarge=" +
        !!this.patient.dischargeDate +
        "&sourceID=" +
        this.handoverID;
      this.showButtonDialog = true;
      this.$nextTick(() => {
        this.$refs?.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 获取BR及专项按钮按钮数据
     * param {*} assessListID
     * return {*}
     */
    async getButtonValue(assessListID) {
      let item = "";
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCode,
        assessListID: assessListID,
        sourceID: this.handoverID,
        sourceType: "DischargeHandoverBR",
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: BR 风险保存回显
     * return {*}
     */
    async updateButton() {
      let item = await this.getButtonValue(this.buttonAssessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
      //添加过敏药物后返回刷新病人头 显示新增过敏药物
      if (this.buttonAssessListID == 185 || this.buttonAssessListID == 4570) {
        this.initFlag = false;
        this._sendBroadcast("refreshInpatient");
      }
    },
    /**
     * description: BR按钮
     * param {*} content
     * return {*}
     */
    async getBrRiskParams(content) {
      if (!this.deathSwitch && this.saveRiskCheck()) {
        return;
      }
      this.brFlag = true;
      let record = content.brParams || {};
      this.brItem = content;
      this.buttonRecordTitle = content.itemName;
      this.componentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: "DischargeHandoverBR",
        sourceID: this.handoverIDArr[1],
        assessTime:
          this._datetimeUtil.formatDate(this.handoverDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.handoverTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 评估木模板勾选内容
     * param {*} values
     * return {*}
     */
    changeValues(values) {
      this.assessValues = values;
      this.deathFlag =
        this.deathSwitch && !!this.assessValues.find((item) => item.assessListID == this.deathAssessListID);
    },
    /**
     * description: TN检核
     * param {*} flag
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    //弹窗开关函数
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 获取页面配置
     * return {*}
     */
    async getPageSetting() {
      const methodArr = [this.getHandoverType, this.getHandoverPaneList, this.getDeathSwitch];
      for await (const method of methodArr) {
        await method();
      }
    },
    /**
     * description: 获取交班类型
     * return {*}
     */
    async getHandoverType() {
      let params = {
        typeValue: this.handoverType,
        inpatientID: this.patient.inpatientID,
      };
      await GetHandOverType(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data?.childrenItem?.length) {
            this.handoverTypeArr = res.data.childrenItem;
            this.searchHandoverType = this.handoverTypeArr[0].value;
            this.recordsCode = this.searchHandoverType;
          }
        }
      });
    },
    /**
     * description: 获取页签配置
     * return {*}
     */
    async getHandoverPaneList() {
      let params = {
        settingCode: "HandoverFunctionShift",
        handoverCode: this.recordsCode,
      };
      await GetHandoverPaneList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.handoverPaneList = res.data;
          this.getPaneMethods();
          this.copyHandoverPaneList = this._common.clone(this.handoverPaneList);
          this.initAssessment();
          this.paneChange(0);
        }
      });
    },
    /**
     * description: pane对象对应函数填充
     * return {*}
     */
    getPaneMethods() {
      if (!this.handoverPaneList?.length) {
        return;
      }
      this.handoverPaneList.forEach((pane, index) => {
        // 添加各Pane初始化事件 评价无需初始化方法
        pane.initMethod = pane?.value == "Evaluate" || this["init" + pane.value];
        // 添加各Pane保存事件
        pane.saveMethod = this["save" + pane.value];
      });
    },
    /**
     * description: 死亡处理
     * param {*} flag
     * return {*}
     */
    deathDealWith(flag) {
      if (!this.templateDatas || !this.templateDatas.length) {
        return;
      }
      let dealWithObj = {
        notAssessBookMartViews: [],
        //评估模板是否有既往史和出院宣教
        findNotAssessBookMartViews: () => {
          let notAssessBookMartViews = this.templateDatas.filter((view) =>
            this.daethNotAssessBookMarkID.includes(view.bookMarkID)
          );
          //隐藏内容保留 已被死亡取消勾选回显
          notAssessBookMartViews.length && (this.daethNotAssessBookMarkViews = notAssessBookMartViews);
          return notAssessBookMartViews;
        },
        //死亡勾选
        deathIsTrueMethod: () => {
          //数组取差集
          this.$set(this, "templateDatas", [
            ...new Set(this.templateDatas.filter((x) => !new Set(this.daethNotAssessBookMarkViews).has(x))),
          ]);
          let riskIndex = this.handoverPaneList.findIndex((m) => m.value == "Risk");
          riskIndex != -1 && this.handoverPaneList.splice(riskIndex, 1);
          this.showRisk = false;
        },
        //死亡取消勾选 回显评估 风险
        deathIsFalseMethod: () => {
          //找不到需要回显的内容 并且可以拿到上次已隐藏内容
          if (!dealWithObj.notAssessBookMartViews.length && this.daethNotAssessBookMarkViews.length) {
            this.$set(this, "templateDatas", [...this.templateDatas, ...this.daethNotAssessBookMarkViews]);
          }
          this.handoverPaneList = this._common.clone(this.copyHandoverPaneList);

          this.showRisk = true;
        },
      };
      dealWithObj.notAssessBookMartViews = dealWithObj.findNotAssessBookMartViews();
      this.$nextTick(() => {
        flag ? dealWithObj.deathIsTrueMethod() : dealWithObj.deathIsFalseMethod();
      });
    },
    /**
     * description: 勾选死亡是否特殊处理开关
     * return {*}
     */
    async getDeathSwitch() {
      let params = {
        settingTypeCode: "DischangeAssessDeathDealWithFlag",
      };
      await GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.deathSwitch = res.data && this.handoverType == "DischargeAssess";
        }
      });
    },
  },
};
</script>

<style lang="scss">
.discharge-handover {
  height: 100%;

  .drawer-content {
    .handover-tabs {
      height: 35px;
      margin-bottom: 5px;
    }

    .pane-content {
      height: calc(100% - 45px);
    }

    .unconfirm-risk {
      color: #ff0000;
    }
  }

  .date-picker {
    width: 160px;
  }

  .time-picker {
    width: 100px;
  }

  .handover-type-select {
    width: 240px;
  }

  .visibility {
    visibility: hidden;
  }
}
</style>