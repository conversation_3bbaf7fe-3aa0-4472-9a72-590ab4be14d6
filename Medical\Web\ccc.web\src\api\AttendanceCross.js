/*
 * FilePath     : \src\api\AttendanceCross.js
 * Author       : 孟昭永
 * Date         : 2020-07-26 10:51
 * LastEditors  : 孟昭永
 * LastEditTime : 2020-07-26 10:53
 * Description  :
 */

import http from "../utils/ajax";
const baseUrl = "/AttendanceCross";
export const urls = {
  GetAttendanceCrossAsync: baseUrl + "/GetAttendanceCrossAsync"
};

//获取可跨病区的信息
export const GetAttendanceCrossAsync = params => {
  return http.get(urls.GetAttendanceCrossAsync, params);
};
