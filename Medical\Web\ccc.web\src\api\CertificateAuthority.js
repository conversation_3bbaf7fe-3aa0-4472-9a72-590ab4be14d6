/*
 * FilePath     : \src\api\CertificateAuthority.js
 * Author       : 李青原
 * Date         : 2020-05-28 16:40
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:43
 * Description  : 电子签名相关API
 */
import http from "../utils/ajax";
const baseUrl = "/CertificateAuthority";

export const urls = {
  GetAuthorize: baseUrl + "/GetAuthorize",
  GetSignResult: baseUrl + "/GetSignResult",
  ApplyPDFSign: baseUrl + "/ApplyPDFSign",
  GetPDFSign: baseUrl + "/GetPDFSign",
};
// 获取电子签名授权
export const GetAuthorize = (params) => {
  return http.get(urls.GetAuthorize, params);
};
// 获取电子签名结果
export const GetSignResult = (params) => {
  return http.get(urls.GetSignResult, params);
};
// 向AnySign无线手写信息数字签名系统申请签名
export const ApplyPDFSign = (params) => {
  return http.get(urls.ApplyPDFSign, params);
};
// 根据病历文档ID获取AnySign无线手写信息数字签名系统签名后的PDF文件
export const GetPDFSign = (params) => {
  return http.get(urls.GetPDFSign, params);
};
