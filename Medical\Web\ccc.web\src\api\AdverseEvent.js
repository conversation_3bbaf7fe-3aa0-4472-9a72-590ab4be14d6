/*
 * FilePath     : \src\api\AdverseEvent.js
 * Author       : 苏军志
 * Date         : 2021-09-20 15:54
 * LastEditors  : 苏军志
 * LastEditTime : 2021-09-20 15:57
 * Description  : AdverseEvent
 */

import http from "../utils/ajax";
const baseUrl = "/AdverseEvent";

export const urls = {
  StaticAdverseEvent: baseUrl + "/StaticAdverseEvent"
}

export const StaticAdverseEvent = (params) => {
  return http.get(urls.StaticAdverseEvent, params)
}