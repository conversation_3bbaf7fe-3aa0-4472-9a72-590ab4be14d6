<!--
 * FilePath     : \CareDirect\Medical\Web\ccc.web\src\pages\patientObserve\observeEvalutaion.vue
 * Author       : 李正元
 * Date         : 2022-01-09 08:09
 * LastEditors  : 曹恩
 * LastEditTime : 2024-08-28 11:22
 * Description  : 2022-01-19 2224 病情观察评价新增页面 -正元
-->
<template>
  <base-layout class="evalution" v-loading="loading" :element-loading-text="loadingText">
    <div slot="header" class="evalution-header">
      <span>评价时间:</span>
      <el-date-picker
        v-model="evalutionDate"
        :clearable="false"
        value-format="yyyy-MM-dd HH:mm"
        format="yyyy-MM-dd HH:mm"
        type="datetime"
        placeholder="选择日期"
        style="width: 160px"
        @change="dateCheck"
      ></el-date-picker>
      <div class="btn">
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
        <el-button v-if="!scheduleFlag && !dialogFlag" class="print-button" icon="iconfont icon-back" @click="goBack">
          返回
        </el-button>
      </div>
    </div>
    <div class="evalution-body">
      <tabs-layout
        ref="tabsLayout"
        class="assess-layout"
        :template-list="templateDatas"
        check-flag
        :gender="patient ? patient.genderCode : ''"
        @checkTN="checkTN"
        @change-values="changeValues"
        @button-click="buttonClick"
        @button-record-click="buttonRecordClick"
      />
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonRecordTitle"
      :visible.sync="showButtonRecordDialog"
      custom-class="no-footer"
      append-to-body
    >
      <risk-component :params="conponentParams" @result="result"></risk-component>
    </el-dialog>
    <!-- 措施触发措施 -->
    <el-dialog
      v-dialogDrag
      @closed="closeOrTransfer()"
      :close-on-click-modal="false"
      title="措施触发措施"
      :visible.sync="showTrigger"
    >
      <trigger-schedule v-if="showTrigger" :triggerParams="triggerParams" @close="closeOrTransfer()"></trigger-schedule>
    </el-dialog>
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      fullscreen
      custom-class="link no-footer"
      append-to-body
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
    </el-dialog>
  </base-layout>
</template>
<script >
import baseLayout from "@/components/BaseLayout";
import tabsLayout from "@/components/tabsLayout/index";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import triggerSchedule from "@/pages/schedule/components/scheduleTypes/triggerSchedule.vue";
import {
  ReadPatientObservation,
  CreateObserveEvalution,
  UpdateObserveEvalution,
  DeleteDataByObserveSourceID,
  GetPatientObservationByScheduleMainID,
} from "@/api/PatientObservation";
import { GetButtonData } from "@/api/Assess";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    tabsLayout,
    riskComponent,
    triggerSchedule,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patient: "getPatientInfo",
      token: "getToken",
    }),
  },
  props: {
    params: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  watch: {
    patient: {
      handler(newValue) {
        if (!newValue) return;
        this.init();
      },
    },
    showButtonDialog(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
    showTrigger(newValue) {
      if (!newValue) {
        this.$emit("refresh");
      }
    },
  },
  data() {
    return {
      //加载中
      loading: false,
      //加载呈现内容
      loadingText: "",
      //评价结果 1好转:2稳定 默许好转
      evalutionResult: 1,
      //评价日期
      evalutionDate: undefined,
      //评估模版
      templateDatas: [],
      //观察评估内容
      assessDatas: [],
      evalutionArr: [30014530, 30014540, 30014550],
      checkTNFlag: true,
      buttonAssessListID: "",
      buttonName: "",
      showButtonDialog: false,
      // 显示BR类弹窗
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR项目ID
      brAssessListID: undefined,
      //BR组件参数
      conponentParams: undefined,
      // 病情观察串BR类型
      sourceType: "ObserveEvalutaion",
      buttonRecordLoading: false,
      buttonRecordLoadingText: "加载中……",
      obervationID: undefined,
      oldEvalutionDate: undefined,
      evalutionScheduleMainID: undefined,
      recordsCode: undefined,
      oberserDate: undefined,
      sourceID: undefined,
      scheduleFlag: false,
      dialogFlag: false,
      showTrigger: false,
      triggerParams: {},
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      // 设置不可切换病人
      this._sendBroadcast("setPatientSwitch", false);
      if (!this.patient) {
        return;
      }
      let params = this.$route.query;
      // 组件形式，用父组件传的参数
      if (this.params) {
        params = this.params;
      }
      if (params) {
        this.dialogFlag = params.isDialog;
        //观察措施点击修改
        if (params.flag == "G") {
          this.getTemplateData(params.data);
        } else {
          //排程跳转修改
          if (params.patientScheduleMainID) {
            this.scheduleFlag = true;
            this.getObserveBySchedule(params.patientScheduleMainID);
          }
        }
      }
    },
    //排程跳转对接
    getObserveBySchedule(scheduleMainID) {
      let params = {
        scheduleMainID,
      };
      GetPatientObservationByScheduleMainID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          res.data.evalutionScheduleMainID = scheduleMainID;
          this.getTemplateData(res.data);
        }
      });
    },
    //修改时变量初始化
    getTemplateData(row) {
      this.obervationID = row.id;
      this.oldEvalutionDate = row.evalutionDate;
      this.evalutionScheduleMainID = row.evalutionScheduleMainID;
      this.recordsCode = row.evalutaionRecordsCode;
      this.oberserDate = row.performDate;
      this.sourceID = row.sourceID;
      this.evalutionDate = row.evalutionDate ? row.evalutionDate : row.expectEvalutionDate;
      this.getRecordTemplate();
    },
    getRecordTemplate() {
      this.assessDatas = [];
      let params = {
        inpatientID: this.patient.inpatientID,
        chartNo: this.patient.chartNo,
        recordsCode: this.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentID: this.patient.departmentListID,
        dateOfBirth: this.patient.dateOfBirth,
        stationID: this.patient.stationID,
        sourceType: this.sourceType,
        sourceID: this.sourceID,
      };
      this.loading = true;
      this.loadingText = "加载中……";
      //取得历史内容
      this.templateDatas = [];
      ReadPatientObservation(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    //保存检核
    saveCheck(details) {
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      if (!details || !details.length) {
        this._showTip("warning", "请填写评价内容!");
        return false;
      }
      let outComeData = details.find((detail) => this.evalutionArr.includes(detail.AssessListID));
      if (!outComeData || !outComeData.AssessListID) {
        this._showTip("warning", "请填写评价结果!");
        return false;
      }
      return true;
    },
    getDetails() {
      let details = [];
      this.assessDatas.forEach((item) => {
        if (item.disableGroup.indexOf(-1) == -1) {
          let detail = {
            AssessListID: item.assessListID,
            AssessValue: item.assessValue,
            AssessListGroupID: item.assessListGroupID,
            AssessValueJson: item.type === "BD" ? item.assessValue : undefined,
            ControlerType: item.controlerType ? item.controlerType : undefined,
          };
          details.push(detail);
        }
      });
      return details;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    //评估模版内容变更事件
    changeValues(datas) {
      this.assessDatas = datas;
    },
    keydownSave() {
      this.$refs.record.save();
    },
    buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }

      url += (url.includes("?") ? "&" : "?") + "isDialog=true";
      url +=
        `&sourceID=${this.sourceID}` +
        `&sourceType=${this.sourceType}` +
        `&patientScheduleMainID=${this.patientScheduleMainID}` +
        `&bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}` +
        `&userID=${this.user.userID}` +
        `&token=${this.token}`;
      this.showButtonDialog = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 初始化BR组件
     * return {*}
     * param {*} content BR跳转风险项
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      this.conponentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: this.sourceType,
        sourceID: this.sourceID,
        assessTime:
          this._datetimeUtil.formatDate(this.evalutionDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.evalutionDate, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    result(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brAssessListID);
      }
    },

    // 添加完更新按钮数据
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    //更新按钮回显数据
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCode,
        assessListID: assessListID,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    dateCheck() {
      if (this.oberserDate > this.evalutionDate) {
        this._showTip("warning", "评价时间不得早于观察时间");
        this.evalutionDate = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      }
    },
    //保存
    async save() {
      let params = {
        id: this.obervationID,
        inpatientID: this.patient.inpatientID,
        EvalutionScheduleMainID: this.evalutionScheduleMainID,
        evalutionDate: this.evalutionDate,
        recordsCode: this.recordsCode,
      };
      params.details = this.getDetails();
      let check = this.saveCheck(params.details);
      if (!check) {
        return;
      }
      params.outComeID = params.details.find((detail) => this.evalutionArr.includes(detail.AssessListID)).AssessListID;
      this.loading = true;
      this.loadingText = "保存中……";
      let ret = this.oldEvalutionDate ? UpdateObserveEvalution(params) : CreateObserveEvalution(params);
      await ret.then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data) {
            this.triggerParams = {
              triggerDate: this._datetimeUtil.formatDate(res.data.triggerDate, "yyyy-MM-dd"),
              triggerTime: res.data.triggerTime,
              triggerDatas: res.data.attachedIntervention,
              index: Math.random(),
            };
            this.showTrigger = true;
          } else {
            this.closeOrTransfer();
          }
        }
      });
    },
    //返回上一层
    async goBack() {
      if (!this.oldEvalutionDate) {
        // 新增未保存，点击返回时需要把模板中的BR、B串出的数据删除
        await this.deleteDataByObserveSourceID();
      }
      this.$router.push({
        name: "patientObserve",
      });
    },
    // 删除模板中的BR、B串出的数据
    async deleteDataByObserveSourceID() {
      let params = {
        sourceID: this.sourceID,
        sourceType: this.sourceType,
      };
      this.loading = true;
      this.loadingText = "处理中……";
      // 有则删除，没有返回true
      await DeleteDataByObserveSourceID(params).then((ret) => {
        this.loading = false;
      });
    },
    closeOrTransfer() {
      if (this.dialogFlag) {
        // 如果是组件，通知父组件关闭
        this.$emit("close");
      } else {
        this.$router.push({
          name: "patientObserve",
        });
      }
    },
  },
};
</script>
<style lang="scss">
.evalution {
  height: 100%;

  .evalution-header {
    .btn {
      float: right;
    }

    .evalution-body {
      height: 100%;

      .assess-layout {
        height: 100%;
        width: calc(100% - 5px);
      }
    }
  }
}

.el-dialog.no-footer.link .el-dialog__body {
  background-color: #f3f3f3;

  iframe {
    height: 99%;
    border: none;
  }
}
</style>