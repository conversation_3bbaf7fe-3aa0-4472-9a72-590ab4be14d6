/*
 * FilePath     : \src\api\NewNursingRecord.js
 * Author       : 郭鹏超
 * Date         : 2022-05-25 17:34
 * LastEditors  : 马超
 * LastEditTime : 2025-07-01 11:01
 * Description  :新记录编辑APi
 * CodeIterationRecord:
 */

import http from "../utils/ajax";
const baseUrl = "/NewNursingRecord";

//生成API
export const urls = {
  //获取护理记录单表格列
  GetNursingRecordTableColumns: baseUrl + "/GetNursingRecordTableColumns",
  //获取护理记录单编辑表格数据
  GetNursingRecordTableData: baseUrl + "/GetNursingRecordTableData",
  //护理记录新增修改
  NewNursingRecordSave: baseUrl + "/NewNursingRecordSave",
  //获取有记录的所有科室
  GetHaveRecordAllDepartments: baseUrl + "/GetHaveRecordAllDepartments",
  //记录删除
  NursingRecordDelete: baseUrl + "/NursingRecordDelete",
  //获取复测记录
  GetRetestNursingRecordDetailData:
    baseUrl + "/GetRetestNursingRecordDetailData",
  //获取当前病区的护理记录单类别
  GetAllNursingRecordFileClass: baseUrl + "/GetAllNursingRecordFileClass",
  //获取新增护理记录默认带入护理记录单开关是否打开
  GetAddNursingRecordFlagSwitch: baseUrl + "/GetAddNursingRecordFlagSwitch",
  //获取经历科室
  GetDepartmentsByPerformDate: baseUrl + "/GetDepartmentsByPerformDate",
};

// 获取护理记录单表格列
export const GetNursingRecordTableColumns = (params) => {
  return http.get(urls.GetNursingRecordTableColumns, params);
};
//获取护理记录单编辑表格数据
export const GetNursingRecordTableData = (params) => {
  return http.get(urls.GetNursingRecordTableData, params);
};

//护理记录新增修改
export const NewNursingRecordSave = (params) => {
  return http.post(urls.NewNursingRecordSave, params);
};

//获取有记录的所有科室
export const GetHaveRecordAllDepartments = (params) => {
  return http.get(urls.GetHaveRecordAllDepartments, params);
};
//记录删除
export const NursingRecordDelete = (params) => {
  return http.post(urls.NursingRecordDelete, params);
};
//获取复测记录
export const GetRetestNursingRecordDetailData = (params) => {
  return http.post(urls.GetRetestNursingRecordDetailData, params);
};
//获取当前病区的护理记录单类别
export const GetAllNursingRecordFileClass = (params) => {
  return http.get(urls.GetAllNursingRecordFileClass, params);
};
//获取新增护理记录默认带入护理记录单开关是否打开
export const GetAddNursingRecordFlagSwitch = (params) => {
  return http.get(urls.GetAddNursingRecordFlagSwitch, params);
};
//获取经历科室
export const GetDepartmentsByPerformDate = (params) => {
  return http.get(urls.GetDepartmentsByPerformDate, params);
};
