/*
 * FilePath     : \ccc.web\src\api\TestClosingControl.js
 * Author       : 孟昭永
 * Date         : 2020-04-16 15:06
 * LastEditors  : 孟昭永
 * LastEditTime : 2020-04-16 15:24
 * Description  : 检验标签打印
 */
import http from "@/utils/ajax";
const baseUrl = "/TestClosingControl";

export const urls = {
  // 获取检验打印任务列表
  GetTestsTaskListByDate: baseUrl + "/GetTestsTaskListByDate",
  // 打印后回写注记
  UpdatePrintStatusCode: baseUrl + "/UpdatePrintStatusCode",
  // 获取检验预览
  GetViewTestsLabel: "/print/ViewTestsLabel"
};
// 获取检验打印任务列表
export const GetTestsTaskListByDate = param => {
  return http.get(urls.GetTestsTaskListByDate, param);
};
// 获取检验预览
export const GetViewTestsLabel = param => {
  return http.get(urls.GetViewTestsLabel, param);
};
// 打印后回写注记
export const UpdatePrintStatusCode = param => {
  return http.post(urls.UpdatePrintStatusCode, param);
};
