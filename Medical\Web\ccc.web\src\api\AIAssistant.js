/*
 * FilePath     : \src\api\AIAssistant.js
 * Author       : 苏军志
 * Date         : 2025-04-10 19:56
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-10 19:58
 * Description  : AI助手相关接口
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
var baseUrl = "/AIAssistant";

export const urls = {
  getPatientAllergy: baseUrl + "/GetAIProofreadOriginalText",
};

/**
 * @description: 获取润色文本的原始文本
 * @param params
 * @return
 */
export const GetAIProofreadOriginalText = (params) => {
  return http.get(urls.getPatientAllergy, params);
};
