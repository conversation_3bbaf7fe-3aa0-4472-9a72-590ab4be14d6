<!--
 * FilePath     : \ccc.web\src\pages\discharge\index.vue
 * Author       : 李正元
 * Date         : 2020-05-05 08:58
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-12-28 16:05
 * Description  : 出院作业
 -->
<template>
  <base-layout class="discharge-index" v-loading="loading" element-loading-text="加载中……">
    <div slot="header">
      <span>日期:</span>
      <el-date-picker
        v-model="query.startTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 120px"
        placeholder="开始日期"
        @change="selectDischargePatient"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        v-model="query.endTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 120px"
        placeholder="结束日期"
        @change="selectDischargePatient"
      ></el-date-picker>
      <el-radio-group v-model="query.isEvaluate" @change="selectDischargePatient" class="switch-evaluate">
        <el-radio-button label="false">未评价</el-radio-button>
        <el-radio-button label="true">已评价</el-radio-button>
      </el-radio-group>
      <span class="title">住院号：</span>
      <el-input
        v-model="query.localCaseNumber"
        placeholder="住院号"
        class="search-input"
        @keyup.enter.native="selectDischargePatient"
        clearable
      >
        <i slot="append" class="iconfont icon-search" @click="selectDischargePatient"></i>
      </el-input>
    </div>
    <el-table :data="dischargePatient" border stripe height="100%">
      <el-table-column prop="bedNumber" label="床号" align="center" min-width="100"></el-table-column>
      <el-table-column prop="patientName" label="姓名" align="center" min-width="120"></el-table-column>
      <el-table-column prop="localCaseNumber" label="住院号" align="center" min-width="120"></el-table-column>
      <el-table-column prop="gender" label="性别" align="center" min-width="55"></el-table-column>
      <el-table-column prop="age" label="年龄" align="center" min-width="55"></el-table-column>
      <el-table-column prop="diagnosis" label="诊断" align="center" min-width="180"></el-table-column>
      <el-table-column label="入院日期" align="center" width="110">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.admissionDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="出院日期" align="center" width="110">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.dischargeTime, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="edit" align="center" label="出院作业" width="80">
        <template slot-scope="scope">
          <el-tooltip content="执行出院作业">
            <i class="iconfont icon-edit" @click="performDischarge(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="护理小结">
            <i class="iconfont icon-preview" @click="showSummary(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="dialogTitle"
      fullscreen
      v-if="showSbarFlag"
      :visible.sync="showSbarFlag"
      custom-class="discharge-dialog"
      :modal="false"
      @close="selectDischargePatient"
    >
      <div class="dialog-content">
        <component :is="activeName" :supplemnentPatient="supplemnentPatient"></component>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import dischargeHandover from "@/autoPages/handover/dischargeHandover.vue";
import { GetDischargeEvaluatePatients } from "@/api/Inpatient";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { GetPatientBasicDataByInpatientID } from "@/api/Inpatient";
import { mapGetters } from "vuex";
export default {
  components: { baseLayout, dischargeHandover },
  computed: {
    ...mapGetters({
      hospitalInfo: "getHospitalInfo",
    }),
  },
  data() {
    return {
      //出院病人清单
      dischargePatient: [],
      //查询条件
      query: {
        //开始日期
        startTime: this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -7, "yyyy-MM-dd"),
        //结束日期
        endTime: this._datetimeUtil.getNowDate(),
        isEvaluate: false,
        localCaseNumber: "",
      },
      //显示病人计划摘要
      showSummaryDialog: false,
      loading: false,
      newHandoverFlag: false,
      showSbarFlag: false,
      activeName: undefined,
      supplemnentPatient: undefined,
      dialogTitle: "",
    };
  },
  activated() {
    this.getNewHandoverFlag();
    this.selectDischargePatient();
    this.query.localCaseNumber = "";
  },
  methods: {
    async selectDischargePatient() {
      if (this.query.endTime > this._datetimeUtil.getNowDate()) {
        this._showTip("warning", "结束时间不得大于当日");
        return;
      }
      if (this.query.startTime > this.query.endTime) {
        this._showTip("warning", "开始时间不得大于结束时间");
        return;
      }
      this.loading = true;
      await GetDischargeEvaluatePatients(this.query).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          this.dischargePatient = response.data;
        }
      });
    },
    showSummary(rowData) {
      this.$router.push({
        name: "dischargeSummary",
        query: {
          inpatientID: rowData.inpatientID,
        },
      });
    },
    async performDischarge(rowData) {
      // 处理中山单独上转运交接的问题 后续新版交班全部上线--统一删除
      if (this.newHandoverFlag && this.hospitalInfo.hospitalID == 1) {
        await this.getPatientData(rowData.inpatientID);
        if (!this.supplemnentPatient) {
          return;
        }
        this.dialogTitle = rowData.patientName + " -- 出院小结";
        this.showSbarFlag = true;
        this.activeName = "dischargeHandover";
      } else {
        this.$router.push({
          name: "handoverSBAR",
          query: {
            inpatientID: rowData.inpatientID,
            handoverID: rowData.handoverID,
            handoverType: "DischargeAssess",
            disabled: false,
            handoffDate: rowData.shiftDate,
            handoffTime: rowData.handoffTime,
            handoverShift: rowData.handoverShift,
            isDischarge: true,
          },
        });
      }
    },
    /**
     * description: 获取新版交班开关
     * return {*}
     */
    async getNewHandoverFlag() {
      let param = {
        settingTypeCode: "NewHandoverFlag",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.newHandoverFlag = response.data;
        }
      });
    },
    async getPatientData(inpatientID = undefined) {
      let params = {
        inpatientID: inpatientID,
      };
      //根据病案号查找病人数据
      await GetPatientBasicDataByInpatientID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (!result.data) {
            this._showTip("warning", "无法获取病人基本信息");
          }
          this.supplemnentPatient = result.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.discharge-index {
  .switch-evaluate {
    margin-left: 10px;
  }
  .search-input {
    width: 160px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .discharge-dialog {
    .el-dialog__body {
      height: calc(100% - 25px);
      .dialog-content {
        height: calc(100% - 25px);
      }
    }
  }
}
</style>
