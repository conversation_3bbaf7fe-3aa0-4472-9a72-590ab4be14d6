<!--
 * FilePath     : \src\autoPages\dictionaryMaintain\patientIconMaintain.vue
 * Author       : 马超
 * Date         : 2024-03-03 16:11
 * LastEditors  : 马超
 * LastEditTime : 2024-03-03 16:11
 * Description  : 
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="patient-icon-maintain">
    <div slot="header">
      <station-selector
        label="病区："
        :width="convertPX(180) + ''"
        v-model="stationID"
        :userID="user.userID"
        @change="getData"
      />
    </div>
    <div class="data-table">
      <el-table
        v-loading="loading"
        element-loading-text="加载中……"
        border
        stripe
        :data="showIconList"
        row-key="patientListIconID"
        :default-expand-all="false"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        >
        <el-table-column prop="iconText" label="标识" :width="convertPX(120) + ''" align="center">
          <template slot-scope="scope">
            <div class="mark-list">
              <div :style="{ backgroundColor: scope.row.backGroundColor }" class="mark-text">
                {{ scope.row.iconText }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="标识释义"
          :min-width="convertPX(300) + ''"
          head-align="center"
        ></el-table-column>
        <el-table-column
          prop="modifyPerson"
          label="修改人"
          :min-width="convertPX(30) + ''"
          head-align="center"
        ></el-table-column>
        <el-table-column
          prop="modifyDate"
          label="修改时间"
          :min-width="convertPX(50) + ''"
          head-align="center"
        ></el-table-column>
        <el-table-column label="本病区是否显示" :width="convertPX(200) + ''" head-align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.showMark"
              @change="changeRow(scope.row.patientListIconID, scope.row.showMark)"
            ></el-switch>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout.vue";
import stationSelector from "@/components/selector/stationSelector";
import { GetStationIconList, UpdateStationIconSetting } from "@/api/Setting";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    stationSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      loading: false,
      stationID: undefined,
      showIconList: [],
    };
  },
  created() {
    this.stationID = this.user.stationID;
    this.getData();
  },
  methods: {
    /**
     * description: 获取数据
     * return {*}
     */
    getData() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return;
      }
      let params = {
        stationID: this.stationID,
        index: Math.random(),
      };
      this.loading = true;
      GetStationIconList(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.showIconList = res.data;
        }
      });
    },
    /**
     * description:更新Setting
     * param {*}
     * return {*}
     */
    changeRow(patientListIconID, showMark) {
      let params = {
        patientListIconID: patientListIconID,
        showMark: showMark,
        stationID: this.stationID,
      };
      this.loading = true;
      UpdateStationIconSetting(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "修改成功！");
          this.getData();
        } else {
          this._showTip("warning", "修改失败！");
          return;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.patient-icon-maintain {
  .mark-list {
    width: 100%;
    line-height: 24px;
    padding: 0 2px 5px 2px;
    box-sizing: border-box;
    border-top: 1px solid #f0f0f0;

    .mark {
      display: inline-block;
      width: 20px;
      height: 20px;
      line-height: 20px;
      margin: 1px 2px;
      font-size: 14px;
      padding: 1px 3px;
      color: #f6f6f6;
      text-align: center;
      cursor: pointer;
    }

    .mark-text {
      color: #fff;
    }
  }
}
</style>