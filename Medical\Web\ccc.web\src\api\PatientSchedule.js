import http from "../utils/ajax";
import qs, { stringify } from "qs";
const baseUrl = "/PatientSchedule";

export const urls = {
  GetPatientScheduleSingle: baseUrl + "/GetPatientScheduleSingle",
  SavePatientScheduleSingle: baseUrl + "/SavePatientScheduleSingle",
  SaveTriggerIntervention: baseUrl + "/SaveTriggerIntervention",
  GetScheduleAssessView: baseUrl + "/GetScheduleAssessView",
  SaveScheduleAssess: baseUrl + "/SaveScheduleAssess",
  GetStationPatientSchedule: baseUrl + "/GetStationPatientSchedule",
  GetStationPatientScheduleByCareNurse:
    baseUrl + "/GetStationPatientScheduleByCareNurse",
  GetPatientScheduleByBedNum: baseUrl + "/GetPatientScheduleByBedNum",
  GetPatientSchedule: baseUrl + "/GetPatientSchedule",
  PerformCheck: baseUrl + "/PerformCheck",
  GetNursingInterventionMainInfoByID:
    baseUrl + "/GetNursingInterventionMainInfoByID",
  DeletePatientSchedule: baseUrl + "/DeletePatientSchedule",
  SaveDelayORNotPerformReason: baseUrl + "/SaveDelayORNotPerformReason",
  nurseAddSchedule: baseUrl + "/nurseAddSchedule",
  CheckAddScheduleTime: baseUrl + "/CheckAddScheduleTime",
  GetTimeSchedule: baseUrl + "/GetTimeSchedule",
  GetSimpleShow: baseUrl + "/GetSimpleShow",
  GetBatchMonitorByPatientID: baseUrl + "/GetBatchMonitorByPatientID",
  SaveMultiSchedule: baseUrl + "/SaveMultiSchedule",
  GetGlucoseScheduleByDate: baseUrl + "/GetGlucoseScheduleByDate",
  AddScheduleNow: baseUrl + "/AddScheduleNow",
  GetNoExecutionGlucoseScheduleByDate:
    baseUrl + "/GetNoExecutionGlucoseScheduleByDate",
  GetScheduleIntervention: baseUrl + "/GetScheduleIntervention",
  GetMultiPatientPerformList: baseUrl + "/GetMultiPatientPerformList",
  GetBatchMonitorByTime: baseUrl + "/GetBatchMonitorByTime",
  GetBatchRelieveOneselfByTime: baseUrl + "/GetBatchRelieveOneselfByTime",
  GetClinicDataByTime: baseUrl + "/GetClinicDataByTime",
  GetObserveClinicData: baseUrl + "/GetObserveClinicData",
  CopySchedule: baseUrl + "/CopySchedule",
  GetPatientScheduleListByID: baseUrl + "/GetPatientScheduleListByID",
  GetPatientScheduleForBatchExecution:
    baseUrl + "/GetPatientScheduleForBatchExecution",
  GetScheuleExcuteStatus: baseUrl + "/GetScheuleExcuteStatus",
  GetButtonData: baseUrl + "/GetButtonData",
  GetBatchGlucoseByTime: baseUrl + "/GetBatchGlucoseByTime",
  GetBloodGasChart: baseUrl + "/GetBloodGasChart",
  GetPatientNursingLevelSchedules: baseUrl + "/GetPatientNursingLevelSchedules",
  GetBatchMonitorSchedules: baseUrl + "/GetBatchMonitorSchedules",
};

// 获取单一笔排程数据
export const GetPatientScheduleSingle = params => {
  return http.post(urls.GetPatientScheduleSingle, qs.stringify(params));
};
// 护理排程提交
export const SavePatientScheduleSingle = params => {
  return http.post(urls.SavePatientScheduleSingle, params);
};
// 保存措施触发措施
export const SaveTriggerIntervention = params => {
  return http.post(urls.SaveTriggerIntervention, params);
};
//执行页面获取排程明细数据
export const GetScheduleAssessView = params => {
  return http.get(urls.GetScheduleAssessView, params);
};
//执行页面保存排程明细数据
export const SaveScheduleAssess = params => {
  return http.post(urls.SaveScheduleAssess, params);
};
// 获取护士派班病人的排程
export const GetStationPatientScheduleByCareNurse = params => {
  return http.get(urls.GetStationPatientScheduleByCareNurse, params);
};
// 获取排程
export const GetStationPatientSchedule = params => {
  return http.get(urls.GetStationPatientSchedule, params);
};
// 获取指定病人排程
export const GetPatientScheduleByBedNum = params => {
  return http.get(urls.GetPatientScheduleByBedNum, params);
};
// 获病人排程明细
export const GetPatientSchedule = params => {
  return http.get(urls.GetPatientSchedule, params);
};
// 检核是否可以执行排程
export const PerformCheck = params => {
  return http.post(urls.PerformCheck, qs.stringify(params));
};
// 获取排程详细内容
export const GetNursingInterventionMainInfoByID = params => {
  return http.get(urls.GetNursingInterventionMainInfoByID, params);
};
// 删除病人排程
export const DeletePatientSchedule = params => {
  return http.post(urls.DeletePatientSchedule, qs.stringify(params));
};
// 保存不执行排程或延迟执行排程
export const SaveDelayORNotPerformReason = params => {
  return http.post(urls.SaveDelayORNotPerformReason, params);
};
// 添加排程
export const nurseAddSchedule = params => {
  return http.post(urls.nurseAddSchedule, params);
};
//  检核新增排程时间
export const CheckAddScheduleTime = params => {
  return http.get(urls.CheckAddScheduleTime, params);
};
//  获取班次时间段内的排程
export const GetTimeSchedule = params => {
  return http.get(urls.GetTimeSchedule, params);
};
//  获取观察措施生命体征模板
export const GetSimpleShow = params => {
  return http.get(urls.GetSimpleShow, params);
};
//  获取单病人监测数据
export const GetBatchMonitorByPatientID = params => {
  return http.get(urls.GetBatchMonitorByPatientID, params);
};
//  保存病人监测数据
export const SaveMultiSchedule = params => {
  return http.post(urls.SaveMultiSchedule, params);
};
//  根据InpatientID和scheduleDate获取血糖监测的排程数据
export const GetGlucoseScheduleByDate = params => {
  return http.get(urls.GetGlucoseScheduleByDate, params);
};
//立即新增排程
export const AddScheduleNow = params => {
  return http.get(urls.AddScheduleNow, params);
};
//  根据InpatientID和scheduleDate获取未执行的血糖监测排程数据
export const GetNoExecutionGlucoseScheduleByDate = params => {
  return http.get(urls.GetNoExecutionGlucoseScheduleByDate, params);
};

//  根据班别日期时间查询当前时间的排程列表
export const GetScheduleIntervention = params => {
  return http.get(urls.GetScheduleIntervention, params);
};

//  根据班别日期时间查询当前时间的排程列表
export const GetMultiPatientPerformList = params => {
  return http.get(urls.GetMultiPatientPerformList, params);
};

//获取按时间段批量录入病人监测类项目
export const GetBatchMonitorByTime = params => {
  return http.get(urls.GetBatchMonitorByTime, params);
};

//获取按时间段批量录入病人大小便项目
export const GetBatchRelieveOneselfByTime = params => {
  return http.get(urls.GetBatchRelieveOneselfByTime, params);
};

//批量监测获取仪器数据
export const GetClinicDataByTime = params => {
  return http.get(urls.GetClinicDataByTime, params);
};

//观察措施获取仪器数据
export const GetObserveClinicData = params => {
  return http.get(urls.GetObserveClinicData, params);
};
//复制排程
export const CopySchedule = params => {
  return http.post(urls.CopySchedule, params);
};
//根据排程ID获取排程
export const GetPatientScheduleListByID = params => {
  return http.get(urls.GetPatientScheduleListByID, params);
};
//单病人批量监测画面-获取所有排程
export const GetPatientScheduleForBatchExecution = params => {
  return http.get(urls.GetPatientScheduleForBatchExecution, params);
};
//  获取排程执行状态 1已执行，0未执行
export const GetScheuleExcuteStatus = params => {
  return http.get(urls.GetScheuleExcuteStatus, params);
};
//  获取按钮数据
export const GetButtonData = params => {
  return http.get(urls.GetButtonData, params);
};
//根据排程时间段获取批量血糖作业项目
export const GetBatchGlucoseByTime = params => {
  return http.get(urls.GetBatchGlucoseByTime, params);
};
//获取血气分析统计图数据
export const GetBloodGasChart = params => {
  return http.get(urls.GetBloodGasChart, params);
};
// 获取病人护理等级排程
export const GetPatientNursingLevelSchedules = params => {
  return http.post(urls.GetPatientNursingLevelSchedules, params);
}
// 获取批量监测排程
export const GetBatchMonitorSchedules = params => {
  return http.post(urls.GetBatchMonitorSchedules, params);
}
