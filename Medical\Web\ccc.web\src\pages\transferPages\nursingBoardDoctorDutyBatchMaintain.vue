<!--
 * FilePath     : \src\pages\transferPages\nursingBoardDoctorDutyBatchMaintain.vue
 * Author       : 来江禹
 * Date         : 2023-03-09 16:07
 * LastEditors  : 来江禹
 * LastEditTime : 2023-03-11 14:12
 * Description  : 
 * CodeIterationRecord: 串护理看板医师值班批量维护画面
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getNursingBoard } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      hospitalInfo: "getHospitalInfo",
      token: "getToken",
      user: "getUser",
    }),
  },
  created() {
    this.url =
      getNursingBoard() +
      "doctorDutyBatchMaintain?hospitalID=" +
      this.hospitalInfo.hospitalID +
      "&token=" +
      this.token +
      "&stationID=" +
      this.user.stationID +
      "&userID=" +
      this.user.userID;
  },
};
</script>