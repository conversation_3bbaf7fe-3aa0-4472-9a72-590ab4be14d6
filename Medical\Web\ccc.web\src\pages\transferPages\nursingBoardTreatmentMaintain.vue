<!--
 * FilePath     : \src\pages\transferPages\nursingBoardTreatmentMaintain.vue
 * Author       : 苏军志
 * Date         : 2022-04-29 16:30
 * LastEditors  : 马超
 * LastEditTime : 2024-11-20 16:01
 * Description  :  串到护理看板检查治疗动态维护
 * CodeIterationRecord: 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getNursingBoard } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      user: "getUser",
      hospitalInfo: "getHospitalInfo"
    }),
  },
  created() {
    this.url =
      getNursingBoard() +
      "treatmentMaintain?stationID=" +
      this.user.stationID +
      "&token=" +
      this.token +
      "&userID=" +
      this.user.userID +
      "&hospitalID=" +
      this.hospitalInfo.hospitalID;
  },
};
</script>