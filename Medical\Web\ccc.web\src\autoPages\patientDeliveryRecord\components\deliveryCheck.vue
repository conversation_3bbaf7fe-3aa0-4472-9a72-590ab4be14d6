<!--
 * FilePath     : \ccc.web\src\autoPages\patientDeliveryRecord\components\deliveryCheck.vue
 * Author       : 曹恩
 * Date         : 2024-04-20 14:42
 * LastEditors  : 曹恩
 * LastEditTime : 2024-04-20 16:17
 * Description  : 准备接产核查
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="delivery-check" :showFooter="true">
    <div slot="header">
      <slot name="main-header"></slot>
    </div>
    <slot name="main-tabs-layout"></slot>
    <div slot="footer">
      <slot name="main-footer"></slot>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import { GetPatientDeliveryRecordCareMainView, DeletePatientDeliveryRecordCareMain } from "@/api/PatientDeliveryRecord";
export default {
  components: {
    baseLayout,
  },
  props: {
    mainSaveParams: {
      type: Object,
      required: true,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      careMain: undefined,
    };
  },
  computed: {
    parentInstance() {
      return this.$parent?.$parent;
    },
  },
  async activated() {
    this.$emit("pageInit", true);
    this.$emit("setInpatientInfo");
    await this.init();
    this.$emit("pageInit", false);
  },
  methods: {
    async init() {
      await this.getCareMain();
      await this.checkAuthor();
      this.emitProperties();
      if (this.parentInstance && this.parentInstance.getDeliveryAssessView) {
        await this.parentInstance.getDeliveryAssessView();
      }
    },
    /**
     * description: 获取维护记录
     * return {*}
     */
    async getCareMain() {
      this.careMain = undefined;
      const params = {
        recordID: this.mainSaveParams.recordID,
        recordsCode: this.mainSaveParams.recordsCode,
      };
      await GetPatientDeliveryRecordCareMainView(params).then(async (res) => {
        if (this._common.isSuccess(res)) {
          this.careMain = res.data;
        }
      });
    },
    /**
     * description: 权限检查
     * return {*}
     */
    async checkAuthor() {
      if (!this.careMain?.patientDeliveryCareMainID) {
        return;
      }
      await this.mainSaveParams.checkAuthor(
        this.careMain.patientDeliveryCareMainID,
        "PatientDeliveryCareMain",
        this.careMain.userID
      );
    },
    /**
     * description: 回传变量
     * return {*}
     */
    emitProperties() {
      const returnValues = {
        showDelBtn: !!this.careMain?.patientDeliveryCareMainID,
        careMainID: this.careMain?.patientDeliveryCareMainID,
        assessDate: this.careMain?.assessDate,
        assessTime: this.careMain?.assessTime,
      };
      this.$emit("pageActivated", returnValues);
    },
    /**
     * description: 删除当前记录
     * return {*}
     */
    async deleteCareMain() {
      await this.checkAuthor();
      if (!this.mainSaveParams.showEditButton) {
        return;
      }

      this._deleteConfirm("", async (flag) => {
        if (flag) {
          let params = {
            careMainID: this.careMain.patientDeliveryCareMainID,
          };
          await DeletePatientDeliveryRecordCareMain(params).then(async (res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
            }
            // 刷新主记录
            this.$emit("refreshCurrentRecord", this.mainSaveParams.recordID);
            await this.init();
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.delivery-check {
  .base-content {
    overflow: hidden;
  }
  .base-footer {
    display: flex;
    justify-content: flex-end;
    bottom: 5px;
    background-color: #fff;
    .bring-checkbox {
      margin-right: 15px;
    }
  }
}
</style>