<!--
 * FilePath     : \src\pages\schedule\components\scheduleTypes\outputMultiPerform.vue
 * Author       : 李正元
 * Date         : 2020-06-06 15:44
 * LastEditors  : 张现忠
 * LastEditTime : 2023-02-01 11:26
 * Description  : 批量输入导管引流液体
-->
<template>
  <base-layout
    class="schedule-output-multi-perform"
    v-loading.fullscreen.lock="loading"
    :element-loading-text="loadingText"
  >
    <div slot="header">
      <div class="header-left">
        <span>执行日期：</span>
        <el-date-picker
          v-model="scheduleDate"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          style="width: 150px"
          :clearable="false"
          placeholder="日期"
        ></el-date-picker>
        <span class="label">执行时间：</span>
        <el-time-picker
          v-model="scheduleTime"
          format="HH:mm"
          value-format="HH:mm"
          :clearable="false"
          style="width: 120px"
        ></el-time-picker>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="iconfont icon-save-button" @click="saveMultiTubeOutput">保存</el-button>
      </div>
    </div>
    <el-table :data="multiOutput" border stripe highlight-current-row height="100%">
      <el-table-column prop="ioKind" label="种类" width="90px" align="center"></el-table-column>
      <el-table-column prop="tubeName" label="项目" header-align="center" align="left"></el-table-column>
      <el-table-column prop="color" label="颜色" width="100px" align="center">
        <template slot-scope="scope">
          <color-picker :colorArray="scope.row.colorDict" v-model="scope.row.color16bit" width="90" />
        </template>
      </el-table-column>
      <el-table-column prop="characteristic" label="性状" width="120px" align="center">
        <template slot-scope="scope">
          <el-select v-model="scope.row.characteristicID" placeholder="请选择">
            <el-option
              v-for="item in scope.row.characteristicDict"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="Smell" label="气味" width="120px" align="center">
        <template slot-scope="scope">
          <el-select v-model="scope.row.smellID" placeholder="请选择">
            <el-option
              v-for="item in scope.row.smellDict"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="intakeOutputVolume" width="90px" label="量(ml)" align="center">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.intakeOutputVolume"
            class="input-width"
            @keyup.native="oninput(scope.row)"
          ></el-input>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetMultiOutputList, MultiSaveOutput } from "@/api/IO";
import colorPicker from "@/components/colorPicker/colorPicker";
export default {
  components: {
    baseLayout,
    colorPicker,
  },
  data() {
    return {
      //加载
      loading: false,
      //批量输入数据
      multiOutput: [],
      //排程主表序号
      patientScheduleMainID: undefined,
      //排程日期
      scheduleDate: undefined,
      //排程时间
      scheduleTime: undefined,
      //加载显示
      loadingText: "加载中……",
      //病人住院序号
      inpatientID: undefined,
      //科别序号
      departmentListID: undefined,
      //护理级别
      nursingLevel: undefined,
      //病区序号
      stationID: undefined,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.inpatientID = this.$route.query.inpatientID;
      this.scheduleDate = this.$route.query.scheduleDate;
      this.scheduleTime = this.$route.query.scheduleTime;
      this.departmentListID = this.$route.query.departmentListID;
      this.nursingLevel = this.$route.query.nursingLevel;
      this.stationID = this.$route.query.stationID;
      this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
      this.loading = true;
      let params = {
        perform: this.$route.query.completeMark,
        patientScheduleMainID: this.patientScheduleMainID,
        inpatientID: this.inpatientID,
        scheduleDate: this.scheduleDate,
        scheduleTime: this.scheduleTime,
        departmentListID: this.departmentListID,
      };
      GetMultiOutputList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.multiOutput = result.data;
        }
      });
    },
    oninput(row) {
      row.intakeOutputVolume = this._decimalUtil.decimalRound(row.intakeOutputVolume, "RoundDown", 2);
    },
    saveMultiTubeOutput() {
      let saveData = [];
      for (let i = 0; i < this.multiOutput.length; i++) {
        let item = this.multiOutput[i];
        // 新增 且 空数据不保存
        if (
          !item.patientIntakeOutputID &&
          !item.color16bit &&
          !item.characteristicID &&
          !item.smellID &&
          !item.intakeOutputVolume
        ) {
          continue;
        }
        let params = {
          characteristicID: item.characteristicID,
          color: item.color16bit,
          inpatientID: this.inpatientID,
          intakeOutputSettingID: item.intakeOutputSettingID,
          intakeOutputVolume: item.intakeOutputVolume,
          ioDate: this.scheduleDate,
          ioTime: this.scheduleTime,
          patientTubeCareMainID: this.checkNotNullStr(item.patientTubeCareMainID)
            ? item.patientTubeCareMainID
            : "temp" + this._common.guid(),
          patientTubeRecordID: item.patientTubeRecordID,
          smellID: item.smellID,
          intakeOutputKind: item.intakeOutputKind,
          nursingLevel: this.nursingLevel,
          departmentListID: this.departmentListID,
          stationID: this.stationID,
          recordsCode: item.recordsCode,
          patientScheduleMainID: this.patientScheduleMainID,
          patientIntakeOutputID: item.patientIntakeOutputID,
        };
        //取得气味
        item.smellDict.forEach((smells) => {
          if (smells.key == item.smellID) {
            params.smell = smells.value;
          }
        });
        //取得性状
        item.characteristicDict.forEach((characteristics) => {
          if (characteristics.key == item.characteristicID) {
            params.characteristic = characteristics.value;
          }
        });
        saveData.push(params);
      }
      if (saveData.length == 0) {
        return;
      }
      this.loading = true;
      //提交
      MultiSaveOutput(saveData).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
          this.close();
        }
      });
    },
    close() {
      window.parent.postMessage("closeDialog", "*");
    },
    /**
     * description: 检查字符串是否为空串
     * return {*} 非空返回true
     * param {*} val
     */
    checkNotNullStr(val) {
      if (val && val.trim() != "") {
        return true;
      }
    },
  },
};
</script>
<style lang="scss">
.schedule-output-multi-perform {
  height: 100%;
  .header-left {
    float: left;
    .label {
      margin-left: 10px;
    }
  }
  .header-right {
    float: right;
  }
}
</style>
