<!--
 * FilePath     : \src\pages\transferPages\jumpOtherSystemCommon.vue
 * Author       : 郭鹏超
 * Date         : 2022-04-15 16:37
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-09-14 09:26
 * Description  : 跳转其它系统公共页面
 * CodeIterationRecord:
-->
<template></template>

<script>
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters({
      token: "getToken",
      patient: "getCurrentPatient",
      user: "getUser",
    }),
  },
  data() {
    return {
      url: undefined,
    };
  },

  created() {
    if (this.$route && this.$route.query.URL) {
      this.url = this.$route.query.URL;
      if (this.url.indexOf("?") > -1) {
        this.url += "&isDialog=true";
      } else {
        this.url += "?isDialog=true";
      }
      this.url +=
        "&chartNo=" +
        this.patient.chartNo +
        "&bedNumber=" +
        this.patient.bedNumber +
        "&localCaseNumber=" +
        this.patient.localCaseNumber +
        "&deptCode=" +
        this.patient.departmentCode +
        //市一手术排程跳转参数
        "&sectionCode=" +
        this.patient.departmentCode +
        "&stationCode=" +
        this.user.stationCode;
      window.open(this.url);
    }
    this.$router.go(-1);
  },
};
</script>

<style>
</style>