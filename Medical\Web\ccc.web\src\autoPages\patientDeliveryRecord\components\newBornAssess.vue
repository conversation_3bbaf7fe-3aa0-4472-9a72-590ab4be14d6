<!--
 * FilePath     : \src\autoPages\patientDeliveryRecord\components\newBornAssess.vue
 * Author       : 杨欣欣
 * Date         : 2023-03-10 11:44
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-12-04 14:55
 * Description  : 新生儿评估
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="newborn-assess" :showHeader="false" v-loading="loading">
    <packaging-table
      v-model="newbornViews"
      :headerList="tableHeaders"
      @rowClick="updateCareMain"
    >
      <!-- 操作 插槽-->
      <div slot="operate" slot-scope="scope">
        <el-tooltip content="修改">
          <div
            @click.stop="updateCareMain(scope.row)"
            class="iconfont icon-edit"
          ></div>
        </el-tooltip>
        <el-tooltip content="删除">
          <div
            @click.stop="delCareMain(scope.row)"
            class="iconfont icon-del"
          ></div>
        </el-tooltip>
      </div>
    </packaging-table>
    <!-- 新增抽屉 -->
    <el-drawer
      :title="drawerSetting.title"
      :visible.sync="drawerSetting.showFlag"
      :direction="drawerSetting.direction"
      :size="drawerSize"
      custom-class="care-main-drawer"
      :wrapperClosable="false"
      :modal="false"
      :append-to-body="false"
      @closed="getCareMains"
    >
      <base-layout :showFooter="true">
        <div slot="header">
          <slot name="main-header"></slot>
        </div>
        <slot name="main-tabs-layout"></slot>
        <div slot="footer">
          <slot name="main-footer"></slot>
        </div>
      </base-layout>
    </el-drawer>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import {
  GetPatientDeliveryRecordCareMainViews,
  DeletePatientDeliveryRecordCareMain
} from "@/api/PatientDeliveryRecord";
export default {
  components: {
    baseLayout,
    packagingTable
  },
  props: {
    mainSaveParams: {
      type: Object,
      required: true,
      default: () => {
        return undefined;
      }
    },
    drawerSize: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      tableHeaders: [],
      newbornViews: [],
      drawerSetting: {
        title: "",
        showFlag: false,
        direction: this.mainSaveParams.drawerDirection
      }
    };
  },
  computed: {
    parentInstance() {
      return this.$parent?.$parent;
    }
  },
  async mounted() {
    await this.getTableHeaderList();
  },
  async activated() {
    await this.init();
  },
  methods: {
    /**
     * description: 激活页签，初始化内容
     * return {*}
     */
    async init() {
      const returnValues = {
        showRefreshBtn: true
      };
      this.$emit("pageActivated", returnValues);
      this.getCareMains();
    },
    /**
     * description: 获取动态表头
     * return {*}
     * param {*}
     */
    async getTableHeaderList() {
      let params = {
        fileClassID: 560,
        fileClassSub: "DeliveryNewbornStart",
        useDescription: "1||Table",
        newSourceFlag: true
      };
      this.tableHeaders = [];
      await GetCareMainTableHeader(params).then(res => {
        if (this._common.isSuccess(res)) {
          this.tableHeaders = res.data;
        }
      });
    },
    /**
     * description: 获取维护表格数据
     * return {*}
     * param {*}
     */
    async getCareMains() {
      if (this.mainSaveParams.recordID.includes("temp")) {
        this.newbornViews = [];
        return;
      }
      let params = {
        recordID: this.mainSaveParams.recordID,
        recordsCode: this.mainSaveParams.recordsCode
      };
      this.loading = true;
      await GetPatientDeliveryRecordCareMainViews(params).then(res => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.newbornViews = res.data;
        }
      });
    },
    /**
     * description: 更新维护记录
     * param {*} row 当前行
     * return {*}
     */
    async updateCareMain(row) {
      if (this.parentInstance && this.parentInstance.addOrModifyCareMain) {
        this.$emit("setInpatientInfo", row);
        await this.parentInstance?.addOrModifyCareMain(row);
      }
    },
    /**
     * description: 删除维护
     * param {*} row 当前行
     * return {*}
     */
    async delCareMain(row) {
      this._deleteConfirm("", flag => {
        if (flag) {
          this.loading = true;
          let params = {
            careMainID: row.patientDeliveryCareMainID
          };
          DeletePatientDeliveryRecordCareMain(params).then(res => {
            this.getCareMains();
            this.loading = false;
            if (this._common.isSuccess(res)) {
              // 刷新主记录
              this.$emit("refreshCurrentRecord", this.mainSaveParams.recordID);
              this._showTip("success", "删除成功");
            }
          });
        }
      });
    },
    /**
     * description: 专项护理弹窗开关函数
     * return {*}
     * param {*} flag 弹框开关，type 新增/修改
     */
    openOrCloseDrawer(flag, type) {
      this.drawerSetting.showFlag = flag;
      if (flag) {
        this.drawerSetting.title = `新生儿评估${type}`;
      }
    }
  }
};
</script>

<style lang="scss">
.newborn-assess {
  .care-main-drawer {
    .base-content {
      overflow: hidden;
    }
    .base-footer {
      display: flex;
      justify-content: flex-end;
      bottom: 5px;
      background-color: #fff;
      .bring-checkbox {
        margin-right: 15px;
      }
    }
  }
}
</style>
