.login-main {
  /* ------------------中山上海(hospitalID=2)登录样式------------------ */
  .hospital2 {
    /* 遮罩层 */
    &.background-mark {
      display: none;
    }
    /* 医院logo */
    &.logo {
      width: 96PX;
      height: 96PX;
      top:-10px;
      left: 10px;
      border: 0;
      opacity: 1;
      border-radius: 0;
      background-color: transparent;
      .logo-img {
        width: 70PX;
        height: 70PX;
      }
    }
    /* 登录框位置及医院名称 */
    &.login-form {
      transform: scale(0.8);
      top: calc(60% - 320px);
      left: inherit;
      right: 110px;
      .login-top .hospital-name.is-education {
        font-size: 36px;
      }
    }
    /* 免责声明 */
    &.login-claimer {
      transform: scale(0.8);
      top: calc(60% + 10px);
      left: inherit;
      right: 110px;
    }
    /* 公司logo */
    &.system-logo {
      bottom: 90PX;
      right: 16PX;
      opacity: 0.5;
    }
    /* 底部工具栏 */
    &.footer {
      opacity: 1;
      background-color: rgba(0,0,0,0.1);
      *{
        color: $base-color!important;
      }
      a:hover {
        color: #ff0000!important;
      }
    }
  }
  /* ------------------------------结束------------------------------- */
  
  /* ------------------中山厦门(hospitalID=4)登录样式------------------ */
  /* 医院名称字体大小 */
  .hospital4 {
    &.login-form {
      .login-top .hospital-name {
        font-size: 36px;
      }
    }
  }
  /* ------------------------------结束------------------------------- */

  /* ------------------宁夏妇幼(hospitalID=5)登录样式------------------ */
  .hospital5 {
    /* 遮罩层 */
    &.background-mark {
      display: none;
    }
    /* logo */
    &.logo {
      border: 0;
      left: 26px;
    }
    /* 登录框及医院名称 */
    &.login-form {
      left: calc(50% - 70px);
      .login-top .hospital-name {
        line-height: 30px;
        font-size: 26px;
      }
    }
    /* 免责声明 */
    &.login-claimer {
      left: calc(50% - 70px);
    }
  }
  /* ------------------------------结束------------------------------- */

  /* ------------------北京佑安(hospitalID=7)登录样式------------------ */
  .hospital7 {
    /* 医院图标 */
    &.logo {
      border: 0;
      width: auto;
      background-color: transparent;
      .logo-img{
        width: auto;
      }
    }
    /* 背景图片 */
    &.background-mark{
      opacity: 0.8;
      background-color:#122b48;
      height: 45%;
    }
    &.background-image {
      height: 50%;
      top: -5%;
    }
    /* 左侧图片 */
    &.login-left-img-wrap{
      display: block;
    }
    /* 登录窗口位置及医院名称 */
    &.login-form {
      box-shadow:none;
      left: calc(50% - 20px);
      .login-top .hospital-name {
        font-size: 32px;
      }
    }
     /* 免责声明 */
     &.login-claimer {
      width: 1120px;
      left: calc(50% - 560px);
      top: calc(50% + 160px);
    }
    /* 系统logo */
     &.system-logo {
      opacity: 1;
    }
    /* 底部状态栏 */
    &.footer {
      opacity: 1;
      background-color: #122b48;
      a:hover {
        color: #ff0000!important;
      }
    }
  }
  /* ------------------------------结束------------------------------- */
}