/*
 * FilePath     : \src\api\WardMaintenance.js
 * Author       : 来江禹
 * Date         : 2022-08-06 17:02
 * LastEditors  : 来江禹
 * LastEditTime : 2022-09-12 14:28
 * Description  : 
 * CodeIterationRecord: 
 */
import http from "../utils/ajax";
const baseUrl = "/WardMaintenance";
export const urls = {
  //保存楼栋等配置
  SaveBuildDatas: baseUrl +"/SaveBuildDatas",
  //获取TypeValue，Description字段
  GetSettingDescriptionOne: baseUrl +"/GetSettingDescriptionOne",
  //逻辑删除楼栋以及与楼栋有关联的楼层等数据,并清空BedList表中相关字段
  DeleteSettingDescriptionBuidDatas: baseUrl +"/DeleteSettingDescriptionBuidDatas",
  //逻辑删除楼层以及与楼层有关联的楼层位置数据，并清空BedList表中相关字段
  DeleteSettingDescriptionFloorDatas: baseUrl +"/DeleteSettingDescriptionFloorDatas",
  //逻辑删除配置表内楼层位置以及所在楼层位置的房间数据，并清空BedList表相关字段
  DeleteSettingDescriptionLocationDatas: baseUrl +"/DeleteSettingDescriptionLocationDatas",
  //逻辑删除配置表内房间数据，并清空BedList表相关字段
  DeleteSettingDescriptionRoomDatas: baseUrl +"/DeleteSettingDescriptionRoomDatas",
  //楼栋维护主页面删除数据
  DeleteBedListDatas:baseUrl +"/DeleteBedListDatas",
  //获取楼栋维护页面表格数据
  GetFloorDatas:baseUrl+"/GetFloorDatas",
  //获取楼层位置维护页面表格数据
  GetLoactionDatas:baseUrl+"/GetLoactionDatas",
  //获取房间维护页面表格数据
  GetRoomDatas:baseUrl+"/GetRoomDatas",
  //获取对应病区床位下拉框数据
  GetBedNumber: baseUrl+"/GetBedNumber",
  //通过病区ID获取楼栋维护主页面数据
  GetStationBuild: baseUrl+"/GetStationBuild",
  //保存楼栋维护主页面数据到BedList表
  SaveStationBuildDatas: baseUrl+"/SaveStationBuildDatas"
};
//通过病区ID获取楼栋维护主页面数据
export const GetStationBuild=params=>{
  return http.get(urls.GetStationBuild,params);
};
//获取对应病区床位下拉框数据
export const  GetBedNumber=params=>{
  return http.get(urls.GetBedNumber,params);
};
//保存楼栋维护主页面数据到BedList表
export const SaveStationBuildDatas=params=>{
  return http.post(urls.SaveStationBuildDatas,params);
};
//楼栋维护主页面删除数据
export const DeleteBedListDatas=params=>{
  return http.get(urls.DeleteBedListDatas,params);
};
//逻辑删除配置表内房间数据，并清空BedList表相关字段
export const  DeleteSettingDescriptionRoomDatas=params=>{
  return http.get(urls.DeleteSettingDescriptionRoomDatas,params);
};
//逻辑删除配置表内楼层位置以及所在楼层位置的房间数据，并清空BedList表相关字段
export const DeleteSettingDescriptionLocationDatas=params=>{
  return http.get(urls.DeleteSettingDescriptionLocationDatas,params);
}; 
//逻辑删除楼层以及与楼层有关联的楼层位置数据，并清空BedList表中相关字段
export const DeleteSettingDescriptionFloorDatas=params=>{
  return http.get(urls.DeleteSettingDescriptionFloorDatas,params);
};
//获取房间维护页面表格数据
export const GetRoomDatas=params=>{
  return http.get(urls.GetRoomDatas,params);
};
//获取楼层位置维护页面表格数据
export const GetLoactionDatas=params=>{
  return  http.get(urls.GetLoactionDatas,params);
};
//获取楼栋维护页面表格数据
export const GetFloorDatas=params=>{
  return http.get(urls.GetFloorDatas,params);
};
//保存楼栋等配置
export const SaveBuildDatas=params=>{
  return http.post(urls.SaveBuildDatas,params);
};
//逻辑删除楼栋以及与楼栋有关联的楼层等数据,并清空BedList表中相关字段
export const DeleteSettingDescriptionBuidDatas=params=>{
  return http.get(urls.DeleteSettingDescriptionBuidDatas,params);
};
//获取TypeValue，Description字段
export const GetSettingDescriptionOne = params =>{
  return http.get(urls.GetSettingDescriptionOne,params);
};