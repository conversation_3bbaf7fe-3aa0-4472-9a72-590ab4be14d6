/*
 * FilePath     : \src\api\DataTableEditList.js
 * Author       : 胡长攀
 * Date         : 2022-11-16 11:33
 * LastEditors  : 胡长攀
 * LastEditTime : 2022-11-28 15:53
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
const baseUrl = "/DataTableEditList";

export const urls = {
  GetDataTableEditListInfosByInpatientID:
    baseUrl + "/GetDataTableEditListInfosByInpatientID",
  AddDataTableEditListInfo: baseUrl + "/AddDataTableEditListInfo",
  DeleteDataTableEditListInfo: baseUrl + "/DeleteDataTableEditListInfo",
  UpdateDataTableEditListInfo: baseUrl + "/UpdateDataTableEditListInfo"
};
//根据患者inpatientID查询异动记录
export const GetDataTableEditListInfosByInpatientID = params => {
  return http.get(urls.GetDataTableEditListInfosByInpatientID, params);
};
//添加异动记录
export const AddDataTableEditListInfo = params => {
  return http.post(urls.AddDataTableEditListInfo, params);
};
//删除异动记录
export const DeleteDataTableEditListInfo = params => {
  return http.get(urls.DeleteDataTableEditListInfo, params);
};
//修改异动记录
export const UpdateDataTableEditListInfo = params => {
  return http.post(urls.UpdateDataTableEditListInfo, params);
};
