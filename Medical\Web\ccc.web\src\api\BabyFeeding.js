/*
 * FilePath     : \src\api\BabyFeeding.js
 * Author       : 郭鹏超
 * Date         : 2021-06-09 10:31
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:43
 * Description  :母乳喂养API
 */
import http from "../utils/ajax";
const baseUrl = "/BabyFeeding";

export const urls = {
  GetBabyFeedingRecordList: baseUrl + "/GetBabyFeedingRecordList",
  DeleteBabyFeedingRecordByID: baseUrl + "/DeleteBabyFeedingRecordByID",
  SaveBabyFeedingRecord: baseUrl + "/SaveBabyFeedingRecord",
  GetBabyFeedingCareList: baseUrl + "/GetBabyFeedingCareList",
  SaveBabyFeedingCare: baseUrl + "/SaveBabyFeedingCare",
  DeleteBabyFeedingCareByID: baseUrl + "/DeleteBabyFeedingCareByID",
  GetFeedingAssessView: baseUrl + "/GetFeedingAssessView",
  GetBabyFeedingRecordsCodeInfo: baseUrl + "/GetBabyFeedingRecordsCodeInfo",
  GetUseFlag: baseUrl + "/GetUseFlag"
};
// 获取婴儿喂养记录列表
export const GetBabyFeedingRecordList = params => {
  return http.get(urls.GetBabyFeedingRecordList, params);
};
//删除婴儿喂养记录
export const DeleteBabyFeedingRecordByID = params => {
  return http.get(urls.DeleteBabyFeedingRecordByID, params);
};
// 保存婴儿喂养记录
export const SaveBabyFeedingRecord = params => {
  return http.post(urls.SaveBabyFeedingRecord, params);
};
// 依据记录ID获取喂养维护列表
export const GetBabyFeedingCareList = params => {
  return http.get(urls.GetBabyFeedingCareList, params);
};
// 保存婴儿喂养维护记录
export const SaveBabyFeedingCare = params => {
  return http.post(urls.SaveBabyFeedingCare, params);
};
// 删除婴儿喂养维护记录
export const DeleteBabyFeedingCareByID = params => {
  return http.get(urls.DeleteBabyFeedingCareByID, params);
};
// 获取婴儿喂养维护明细模板
export const GetFeedingAssessView = params => {
  return http.get(urls.GetFeedingAssessView, params);
};
// 获取婴儿喂养记录对应的DepartmentToAssessInfo记录
export const GetBabyFeedingRecordsCodeInfo = params => {
  return http.get(urls.GetBabyFeedingRecordsCodeInfo, params);
};
//获取使用权限
export const GetUseFlag = params => {
  return http.get(urls.GetUseFlag, params);
};
