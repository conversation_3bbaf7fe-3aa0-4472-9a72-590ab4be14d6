<!--
 * FilePath     : \src\pages\schedule\components\scheduleTypes\scheduleScore.vue
 * Author       : 苏军志
 * Date         : 2022-05-08 11:27
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-15 19:48
 * Description  : Score执行页面
 * CodeIterationRecord:
-->
<template>
  <base-layout
    class="schedule-score"
    v-enter="{ function: 'savePatientScore' }"
    v-loading="loading"
    :element-loading-text="loadingText"
    :showFooter="!hideOperate"
    headerHeight="auto"
  >
    <div slot="header">
      <div class="header-item">
        <span class="label">执行日期：</span>
        <el-date-picker
          v-model="scheduleDate"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          class="schedule-assess-date"
          :clearable="false"
          placeholder="日期"
        ></el-date-picker>
      </div>
      <div class="header-item">
        <span class="label">执行时间：</span>
        <el-time-picker
          v-model="scheduleTime"
          format="HH:mm"
          value-format="HH:mm"
          :clearable="false"
          class="schedule-assess-time"
        ></el-time-picker>
      </div>
    </div>
    <el-table :data="scoreFormat" border height="100%" class="schedule-score-table">
      <el-table-column prop="recordsFormatContent" label="评估内容" width="180"></el-table-column>
      <el-table-column label="评估选项">
        <template slot-scope="scope">
          <div
            class="cell-wrap"
            @click="recordFormatClick(item, scope.row.scoreDetail)"
            v-for="(item, index) in scope.row.scoreDetail"
            :key="index"
          >
            <i class="iconfont icon-info" @click="showMessage(item.showMessage)"></i>
            <span
              v-if="item.controlerType == 'R'"
              :class="['risk-line-input iconfont', item.selectFlag ? 'icon-radio-select' : 'icon-radio-normal']"
            ></span>
            <span
              v-if="item.controlerType == 'C'"
              :class="['risk-line-input iconfont', item.selectFlag ? 'icon-checked' : 'icon-no-checked']"
            ></span>
            <span>
              {{ item.recordsFormatContent }}
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="notDialog" class="not-dialog">
      <el-checkbox v-model="informPhysician">通知医师</el-checkbox>
    </div>
    <div slot="footer">
      <el-checkbox v-model="informPhysician">通知医师</el-checkbox>
      <el-button @click="close(false)">取消</el-button>
      <el-button type="primary" @click="savePatientScore()">确定</el-button>
    </div>
  </base-layout>
</template>
<script>
import { GetPatientScheduleScore, SavePatientScore } from "@/api/PatientScore";
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
export default {
  components: {
    baseLayout,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  props: {
    params: {
      type: Object,
      require: true,
    },
    hideOperate: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    params: {
      immediate: true,
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.init();
        }
      },
    },
  },
  data() {
    return {
      loading: false,
      loadingText: "加载中……",
      scoreFormat: [],
      scheduleDate: "",
      scheduleTime: "",
      patientScoreMainID: "",
      informPhysician: undefined,
    };
  },

  methods: {
    /**
     * description: 关闭
     * param {*} flag
     * return {*}
     */
    close(flag) {
      this.$emit("close", flag);
    },
    /**
     * description: 风险选项提示
     * param {*} messageContent
     * return {*}
     */
    showMessage(messageContent) {
      this._showMessage({
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
    /**
     * description: 初始话
     * return {*}
     */
    init() {
      this.scheduleDate = this.params.performDate;
      this.scheduleTime = this.params.performTime;
      this.informPhysician = this.params.informPhysician;
      this.scoreFormat = [];
      this.loading = true;
      this.loadingText = "加载中……";
      let parms = {
        patientScheduleMainID: this.params.patientScheduleMainID,
        interventionID: this.params.interventionID,
        stationID: this.params.stationID,
        inPatientID: this.params.inpatientID,
      };
      GetPatientScheduleScore(parms).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.scoreFormat = res?.data?.recordFormat ?? [];
          this.patientScoreMainID = res?.data?.patientScoreMainID ?? undefined;
        }
      });
      //是否仅本人操作
      this.checkResult = this._common.checkActionAuthorization(this.user, this.params.performEmployeeID);
    },
    /**
     * description: 风险保存
     * return {*}
     */
    savePatientScore() {
      if (this.loading) {
        return;
      }
      this.loading = true;
      this.loadingText = "保存中……";
      let params = this.getSaveData();
      SavePatientScore(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data != null) {
            this._showTip("success", `评估结果: ${res.data[0]}${res.data[1] ? ` (${res.data[1]})` : ""}`);
            this.close(true);
          }
        }
      });
    },
    /**
     * description: 获取保存参数
     * 单病人批量监测画面调用，不可改名，不可删除
     * param {*}
     * return {*}
     */
    getSaveData() {
      let twoRecordFormatList = [];
      let saveRecordFormatList = [];
      this.scoreFormat.forEach((oneRecordFormat) => {
        if (oneRecordFormat.scoreDetail?.length) {
          twoRecordFormatList = [...twoRecordFormatList, ...oneRecordFormat.scoreDetail];
        }
      });
      if (twoRecordFormatList?.length) {
        saveRecordFormatList = twoRecordFormatList.filter((item) => item.selectFlag);
      }
      let params = {
        inPatientID: this.params.inpatientID,
        patientAssessMainID: this.params.patientAssessMainID,
        patientInterventionID: this.params.patientInterventionID,
        recordListId: this.scoreFormat[0].recordListID,
        sourceType: "schedule",
        sourceId: this.params.patientScheduleMainID,
        nursingInterventionMainId: this.params.interventionID,
        patientScoreMainID: this.patientScoreMainID,
        inpatientId: this.params.inpatientID,
        recordsFormatId: saveRecordFormatList?.length ? saveRecordFormatList.map((item) => item.recordsFormatID) : [],
        scheduleDate: this.scheduleDate,
        scheduleTime: this.scheduleTime,
        stationID: this.params.stationID,
        informPhysician: this.informPhysician,
      };
      return params;
    },
    /**
     * description: 选线勾选与取消勾选
     * param {*} item
     * param {*} details
     * return {*}
     */
    recordFormatClick(item, details) {
      if (item.controlerType == "R") {
        if (item.selectFlag) {
          this.$set(item, "selectFlag", !item.selectFlag);
          return;
        }
        details.forEach((detail) => {
          if (detail.controlerType == "R") {
            this.$set(detail, "selectFlag", detail.recordsFormatID == item.recordsFormatID);
          }
        });
      }
      if (item.controlerType == "C") {
        this.$set(item, "selectFlag", !item.selectFlag);
      }
    },
  },
};
</script>
<style lang="scss">
.schedule-score {
  background-color: #fff;
  padding: 0 10px 5px 10px;
  box-sizing: border-box;
  .header-item {
    display: inline-block;
    .label {
      margin-left: 10px;
    }
    .assess-date {
      width: 110px;
    }
    .assess-time {
      width: 75px;
    }
  }
  .schedule-score-table {
    .cell-wrap {
      display: flex;
      width: 100%;
      .icon {
        margin: -3px 5px 0 -5px;
      }
      .el-radio {
        line-height: 20px;
        white-space: normal;
        display: flex;
        .el-radio__input {
          line-height: 22px;
        }
        .el-radio__label {
          line-height: 22px;
          padding-left: 5px;
        }
      }
    }
  }
  .not-dialog .bring-checkbox {
    float: left;
    margin-top: 10px;
    margin-left: 5px;
  }
}
</style>
