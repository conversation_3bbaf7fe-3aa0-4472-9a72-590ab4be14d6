<!--
    使用者引用该组件之后，该组件为子组件，引用该组件的组件为父组件。（以下用子组件，父组件叙述）
    1.该组件封装了校验方法，病区科别床位号都不能为空的时候才能将数据传到父组件。
    2.病区对应父组件传递stationID
    3.科别对应父组件传递departmentListID
    4.床位号对应父组件传递的bedNumber
    5.如果父组件的需要向子组件传值，只需要给stDeptBed对象赋值即
    6.父组件传递过来三个值之后，该组件会返回四个值，这四个值分别为病区ID(stationID)、科别ID(departmentListID)、 
    床位号(bedNumber)和床位号对应的床位(bedId)
    7.该组件同时向父组件提供了selectStationDepartmentBed方法，该方法会将该组件中的tempStDeptBed对象传递给父组件，
    父组件可以接收该（tempStDeptBed）对象即可获得病区ID(stationID)、科别ID(departmentListID)、 床位号(bedNumber)和床位号对应的床位(bedId)    
-->
<template>
  <div class="station-departmentbed">
    <div class="top">
      <el-form :inline="true" :model="tempStDeptBed" ref="stDeptBedFrom" align="center" :rules="rules">
        <el-form-item label="病区" prop="stationID">
          <template>
            <el-select
              class="dialog-select"
              v-model.number="tempStDeptBed.stationID"
              placeholder="请选择"
              @change="getBedListByStationId"
            >
              <el-option v-for="item in stationList" :key="item.id" :label="item.stationName" :value="item.id" />
            </el-select>
          </template>
        </el-form-item>
        <el-form-item label="科别" prop="departmentListID">
          <template>
            <el-select
              class="dialog-select"
              v-model.number="tempStDeptBed.departmentListID"
              placeholder="请选择"
              @change="giveParentVal"
            >
              <el-option v-for="item in optionsOfDepartmentList" :key="item.id" :label="item.value" :value="item.id" />
            </el-select>
          </template>
        </el-form-item>
        <el-form-item label="床位" prop="bedNumber">
          <template>
            <el-select
              class="dialog-bed-number"
              filterable
              v-model="tempStDeptBed.bedNumber"
              placeholder="请选择"
              @change="giveParentVal"
            >
              <el-option v-for="item in bedlist" :key="item.id" :label="item.bedNumber" :value="item.bedNumber" />
            </el-select>
          </template>
        </el-form-item>
      </el-form>
    </div>
    <hr />
  </div>
</template>

<script>
import { GetStationList, GetDepartmentDataByStationID } from "@/api/Station";
import { GetBedListByStationId } from "@/api/BedList";
import common from "@/utils/common.js";

export default {
  data() {
    return {
      bedlist: [],
      rules: {
        departmentListID: [
          {
            type: "number",
            message: "请选择科别",
            trigger: "change",
            pattern: true,
          },
        ],
        stationID: [
          {
            type: "number",
            message: "请选择部门",
            trigger: "change",
            pattern: true,
          },
        ],
        bedNumber: [
          {
            message: "请选择床位",
            trigger: "blur",
            pattern: true,
          },
        ],
      },
      stDeptBedFrom: "stDeptBedFrom",
      stationList: [],
      optionsOfDepartmentList: [],
      tempStDeptBed: {},
    };
  },
  props: {
    stDeptBed: {},
  },
  watch: {
    stDeptBed: {
      immediate: true,
      handler(newVal, oldVal) {
        this.tempStDeptBed = common.clone(newVal);
      },
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    giveParentVal() {
      this.$refs[this.stDeptBedFrom].validate((valid) => {
        if (valid) {
          this.getBedIdByBedNumber();
          this.selectStationDepartmentBed();
        } else {
          return false;
        }
      });
    },
    selectStationDepartmentBed() {
      this.$emit("selectStationDepartmentBed", this.tempStDeptBed);
    },
    getBedIdByBedNumber() {
      for (var i = 0; i < this.bedlist.length; i++) {
        if (this.tempStDeptBed.bedNumber == this.bedlist[i].bedNumber) {
          this.tempStDeptBed.bedId = this.bedlist[i].id;
          break;
        }
      }
    },
    getBedListByStationId(StationID, initFlag) {
      if (
        this.tempStDeptBed.stationID != null &&
        this.tempStDeptBed.departmentListID != null &&
        this.tempStDeptBed.bedNumber != null &&
        this.tempStDeptBed.bedId != null
      ) {
        this.giveParentVal();
      }
      this.bedlist = {};
      let params = {
        stationID: StationID,
      };
      GetBedListByStationId(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.bedlist = result.data;
        }
      });
      if (!initFlag) {
        this.tempStDeptBed.departmentListID = undefined;
      }
      this.getDepartmentList();
    },
    init() {
      this.getStationList();
      this.getDepartmentList();
      this.getBedListByStationId(this.tempStDeptBed.stationID, true);
      if (
        this.tempStDeptBed.stationID != null &&
        this.tempStDeptBed.departmentListID != null &&
        this.tempStDeptBed.bedNumber != null &&
        this.tempStDeptBed.bedId != null
      ) {
        this.giveParentVal();
      }
    },
    //获取病区列表
    getStationList() {
      let params = {};
      GetStationList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.stationList = result.data;
        }
      });
    },
    //获取科别列表
    getDepartmentList() {
      if (!this.tempStDeptBed.stationID) {
        return;
      }
      let params = {
        ID: this.tempStDeptBed.stationID,
      };
      GetDepartmentDataByStationID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.optionsOfDepartmentList = result.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.station-departmentbed {
  hr {
    margin-left: 17px;
    margin-right: 28px;
    margin-bottom: 15px;
  }
}
.dialog-select {
  width: 0;
  max-width: 170px !important;
  min-width: 120px !important;
}
.dialog-bed-number {
  width: 157px;
}
</style>
