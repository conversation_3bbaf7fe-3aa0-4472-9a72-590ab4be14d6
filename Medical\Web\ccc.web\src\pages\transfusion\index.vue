<!--
 * FilePath     : \src\pages\transfusion\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-03-25 16:31
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-06-10 10:55
 * Description  : 输血
-->
<template>
  <specific-care
    v-model="showMaintainFlag"
    :showRecordArr="showRecordArr"
    :showRecordLabelArr="['输血主记录', '输血维护记录']"
    :drawerTitle="transfusionDrawerTitle"
    :editFlag="checkResult"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    @mainAdd="mainAddOrFix"
    @maintainAdd="maintainAddOrFix"
    @save="transfusionSave()"
    @cancel="transfusionCancel()"
    @getMainFlag="getMainFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    :recordTitleSlotFalg="false"
    :mainTableHeight="tableOneRowHeight"
    :drawerSize="supplementFlag ? '80%' : ''"
    class="patient-transfusion"
  >
    <!-- 主记录  slot="main-record" -->
    <div slot="main-record">
      <el-table
        ref="recordTable"
        @row-click="getMaintainList"
        height="100%"
        :data="mainDataList"
        border
        stripe
        row-class-name="main-record-row"
        header-row-class-name="main-record-header-row"
      >
        <el-table-column
          v-for="(item, index) in mainTableHeader"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="!item.minWidthFlag ? item.width : item.minWidthFlag"
          :min-width="item.minWidthFlag ? item.width : item.minWidthFlag"
          header-align="center"
          :align="item.position"
        ></el-table-column>
        <el-table-column label="操作" fixed="right" width="100" header-align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="mainAddOrFix(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip v-if="scope.row && !scope.row.endDateTime" content="结束">
              <div @click.stop="endTransfusion(scope.row)" class="iconfont icon-stop"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="deleteTransfusion(scope.row, 1)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <el-table height="100%" :data="maintainDataList" border stripe>
        <el-table-column
          v-for="(item, index) in maintainTableHeader"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="!item.minWidthFlag ? item.width : item.minWidthFlag"
          :min-width="item.minWidthFlag ? item.width : item.minWidthFlag"
          :header-align="item.herderPosition"
          :align="item.position"
        >
          <template slot-scope="scope">
            <div v-if="index == 0">
              <span v-if="scope.row.recordsCode.indexOf('Start') != -1">开始评估</span>
              <span v-else-if="scope.row.recordsCode.endsWith('End')">结束评估</span>
              <span v-else>例行评估</span>
            </div>
            <div v-else>{{ scope.row[item.prop] }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="70" header-align="center">
          <template v-if="scope.row.recordsCode != 'BloodTransfusionStart'" slot-scope="scope">
            <el-tooltip content="修改">
              <div @click="maintainAddOrFix(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="deleteTransfusion(scope.row, 2)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 弹窗 -->
    <base-layout
      v-loading="tabsLayoutLoading"
      :element-loading-text="tabsLayoutText"
      slot="drawer-content"
      header-height="auto"
    >
      <div slot="header">
        <span class="label">执行日期:</span>
        <el-date-picker
          v-model="performDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          style="width: 110px"
        ></el-date-picker>
        <el-time-picker
          v-model="performTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          style="width: 90px"
        ></el-time-picker>
        <span class="label">执行病区:</span>
        <station-selector v-model="currentStation" label="" width="160"></station-selector>
        <dept-selector label="" width="140" v-model="currentDepartment" :stationID="currentStation"></dept-selector>
        <span v-if="recordsCodeInfo.recordsCode == 'BloodTransfusionStart'" class="label">血单号:</span>
        <el-select
          v-if="recordsCodeInfo.recordsCode == 'BloodTransfusionStart'"
          v-model="bloodDonorBagsCode"
          placeholder="请输入血单号"
          :disabled="bloodNumberFlag"
        >
          <el-option
            v-for="(item, index) in bloodDonorBagsCodeArr"
            :key="index"
            :label="item.label"
            :value="item.label"
            @click.native="bloodBagCodeChange(item)"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="openClinic" v-if="showVitalFlag">生命体征</el-button>
      </div>
      <tabs-layout
        ref="transfusionLayout"
        :template-list="templateDatas"
        @change-values="changeValues"
        @checkTN="checkTN"
        :checkFlag="true"
      />
      <el-drawer
        title="生命体征"
        :modal-append-to-body="false"
        :visible.sync="showClinicFlag"
        :destroy-on-close="true"
        direction="btt"
        size="67%"
        custom-class="clinic-drawer"
        :wrapperClosable="false"
      >
        <clinic-view
          height="190px"
          v-model="timeRange"
          :clinicPrams="clinicPrams"
          @getSelectClinicData="postClinicData"
        ></clinic-view>
      </el-drawer>
    </base-layout>
  </specific-care>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import { mapGetters } from "vuex";
import {
  GetBloodDonorBagsCodeListByInpatientID,
  GetTransfusionRecordByInpatientID,
  GetTransfusionCareMainByTransfusionID,
  GetTransfusionRecordsCodeInfo,
  GetTransfusionAssessView,
  SavePatientTransfusionRecord,
  SavePatientTransfusionCareMain,
  StopTransfusionRecord,
  DeleteTransfusionRecord,
  DeleteTransfusionCareMain,
  GetCurrentTransfusionRecord,
} from "@/api/PatientTransfusion";
import { GetBloodProductCategorySetting } from "@/api/Setting";
import clinicView from "@/pages/schedule/components/scheduleTypes/clinicView";
export default {
  components: {
    baseLayout,
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    clinicView,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
    }),
  },
  data() {
    return {
      showMaintainFlag: false,
      transfusionDrawerTitle: "",
      showRecordArr: [true, false],
      //主表表格数据
      mainDataList: [],
      //维护表格数据
      maintainDataList: [],
      //评估单号
      recordsCodeInfo: {},
      //主记录输血ID
      transfusionID: undefined,
      //输血主键ID
      transfusionRecordID: undefined,
      //维护记录主键
      patientTransfusionCareMainID: undefined,
      //选中主记录Item
      currentTransfusionRecord: undefined,
      //维护弹窗顶部数据
      performDate: undefined,
      performTime: undefined,
      currentStation: undefined,
      currentDepartment: undefined,
      bloodDonorBagsCode: undefined,
      bloodNumber: undefined,
      outerBloodCode: undefined,
      bloodDonorBagsCodeArr: [],
      bloodNumberFlag: false,
      bloodProductCategoryArr: [],
      bloodProductCategoryGroupID: 6277060,
      //评估模板loading开关
      tabsLayoutLoading: false,
      tabsLayoutText: "加载中……",
      //维护弹窗模板数据
      templateDatas: [],
      //评估模板返回数据
      assessDatas: [],
      checkTNFlag: true,
      //主表表头数组
      mainTableHeader: [
        {
          label: "来源",
          prop: "sourceContent",
          width: 50,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "输血科室",
          prop: "occuredDepartmentName",
          width: 180,
          minWidthFlag: false,
          herderPosition: "center",
          position: "left",
        },
        {
          label: "血袋号码",
          prop: "bloodDonorBagsCode",
          width: 150,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "开始时间",
          prop: "startDateTime",
          width: 130,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "输血类型",
          prop: "transfusionType",
          width: 120,
          minWidthFlag: true,
          herderPosition: "center",
          position: "center",
          templateFlag: false,
        },
        {
          label: "血液制品",
          prop: "transfusionBloodNameAndVolume",
          width: 200,
          minWidthFlag: false,
          herderPosition: "center",
          position: "left",
          templateFlag: true,
        },
        {
          label: "血型",
          prop: "patientABO",
          width: 50,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          templateFlag: true,
        },
        {
          label: "RH",
          prop: "patientRH",
          width: 70,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          templateFlag: true,
        },
        {
          label: "输血方式",
          prop: "transfusionWay",
          width: 100,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          templateFlag: true,
        },
        {
          label: "输血速度",
          prop: "transfusionStartFlowRate",
          width: 100,
          minWidthFlag: false,
          herderPosition: "center",
          position: "left",
          templateFlag: true,
        },
        {
          label: "输血人",
          prop: "transfusionEmployeeName",
          width: 80,
          minWidthFlag: false,
          herderPosition: "center",
          position: "left",
          templateFlag: true,
        },
        {
          label: "核对人",
          prop: "transfusionCheckEmployeeName",
          width: 80,
          minWidthFlag: false,
          herderPosition: "center",
          position: "left",
          templateFlag: true,
        },
        {
          label: "实际剂量",
          prop: "bloodTransfusionVolume",
          width: 100,
          minWidthFlag: false,
          herderPosition: "center",
          position: "left",
          templateFlag: true,
        },
        {
          label: "结束时间",
          prop: "endDateTime",
          width: 130,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
      ],
      //维护表头数据
      maintainTableHeader: [
        {
          label: "类型",
          prop: "sourceContent",
          width: 50,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "排程时间",
          prop: "scheduleDateTime",
          width: 160,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "科室",
          prop: "departmentName",
          width: 180,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "体温",
          prop: "bodyTemperature",
          width: 70,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "脉搏",
          prop: "pulse",
          width: 70,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "呼吸",
          prop: "respire",
          width: 70,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "收缩压",
          prop: "systolicPressure",
          width: 70,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },

        {
          label: "舒张压",
          prop: "diastolicPressure",
          width: 70,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "滴速",
          prop: "flowRate",
          width: 100,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          templateFlag: true,
        },
        {
          label: "输血反应",
          prop: "transfusionReaction",
          width: 120,
          minWidthFlag: true,
          herderPosition: "center",
          position: "left",
        },
        {
          label: "执行时间",
          prop: "inspectionDate",
          width: 160,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
        },
        {
          label: "执行人",
          prop: "inspectionEmployeeName",
          width: 100,
          minWidthFlag: false,
          herderPosition: "center",
          position: "center",
          templateFlag: true,
        },
      ],
      //排程路由参数
      routeData: {},
      //排程对应主记录
      scheduleToRecord: undefined,
      typeFlag: false,
      //主记录表格表头加第一行的高度
      tableOneRowHeight: undefined,
      patient: undefined,
      supplementFlag: undefined,
      checkResult: true,
      //通知医师
      informPhysicianArr: [true, false],
      bringToNursingRecordArr: [true, false],
      showClinicFlag: false,
      clinicTable: [],
      showVitalFlag: false,
      //默许取得仪器时间范围
      timeRange: 30,
      //获取仪器数据所需params
      clinicPrams: {},
    };
  },
  watch: {
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.supplementFlag = undefined;
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.supplementFlag = "*";
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * @description: 初始化
     * @return
     */
    async init() {
      this.mainDataList = [];
      this.maintainDataList = [];
      this.routeData = this.$route.query;
      //获取病人输血主记录
      await this.getTransfusionRecordList();
      //获取该病人输血单号
      await this.getBloodDonorBagsCodeArr();
      //获取血液制品类型
      await this.getGetBloodProductCategoryData();
      if (Object.keys(this.routeData).length && !this.$route.query.shortCutFlag) {
        this._sendBroadcast("setPatientSwitch", false);
        await this.getRecordAndMaintain();
        this.mainDataList = [this.scheduleToRecord];
        this.currentTransfusionRecord = this.scheduleToRecord;
        await this.GetTransfusionCareMainList();
        this.$nextTick(() => {
          this.tableOneRowHeight = this._common.getTableOneRowHeight(
            this.$refs.recordTable?.$el,
            ".main-record-row",
            ".main-record-header-row"
          );
        });
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
      await this.scheduleJump();
    },
    /**
     * @description: 获取血液制品类型
     * @return
     */
    async getGetBloodProductCategoryData() {
      await GetBloodProductCategorySetting().then((res) => {
        if (this._common.isSuccess(res)) {
          this.bloodProductCategoryArr = res.data;
        }
      });
    },
    /**
     * @description: 血单号改变触发
     * @return
     * @param bagData
     */
    bloodBagCodeChange(bagData) {
      if (!bagData) {
        return;
      }
      let settingData = this.bloodProductCategoryArr.find((item) => item.typeValue == bagData.code);
      if (!settingData) {
        return;
      }
      if (this.templateDatas.length && this.templateDatas[0].groups) {
        this.templateDatas[0].groups.forEach((group) => {
          if (group.groupID == this.bloodProductCategoryGroupID && group.contents.length) {
            group.contents.forEach((content) => {
              if (content.assessListID == settingData.settingValue) {
                content.assessValue = "1";
              } else {
                content.assessValue = "";
              }
            });
          }
        });
        this.$refs.transfusionLayout.init();
      }
    },
    /**
     * @description: 主记录新增或修改
     * @return
     * @param item
     */
    async mainAddOrFix(item) {
      if (item) {
        this.checkResult = await this._common.checkActionAuthorization(this.user, item.nurseID);
        if (this.checkResult) {
          let ret = await this._common.getEditAuthority(
            item.patientWoundRecordID,
            "PatientWoundRecord",
            !!this.supplementFlag
          );
          if (ret) {
            this.checkResult = false;
            this._showTip("warning", ret);
          } else {
            this.checkResult = true;
          }
        }
      }
      this.informPhysicianArr = [false, false];
      this.bringToNursingRecordArr = [false, false];
      //打开弹窗 修改弹窗标题
      this.openOrCloseDrawer(true, item ? "修改输血主记录" : "新增输血主记录");
      //修改评估表单号
      this.recordsCodeInfo.recordsCode = "BloodTransfusionStart";
      //填充弹窗顶部数据
      this.performDate = item
        ? this._datetimeUtil.formatDate(item.startDateTime, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = item
        ? this._datetimeUtil.formatDate(item.startDateTime, "hh:mm")
        : this._datetimeUtil.getNowTime("hh:mm");
      this.currentStation = item ? item.stationID : this.patient.stationID;
      this.currentDepartment = item ? item.departmentID : this.patient.departmentListID;
      this.bloodDonorBagsCode = item ? item.bloodDonorBagsCode : undefined;
      this.bloodNumber = item ? item.bloodNumber : undefined;
      this.outerBloodCode = item ? item.outerBloodCode : undefined;
      //保存输血主记录的时候使用
      this.PatientScheduleMainID = item ? item.patientScheduleMainID : undefined;
      //获取评估模板时使用
      this.transfusionID = item ? item.transfusionID : undefined;
      this.transfusionRecordID = item ? item.patientTransfusionRecordID : undefined;
      //修改主记录不允许更改血单号
      this.bloodNumberFlag = item ? true : false;

      //获取评模板
      this.getTransfusionAssessView();
    },
    /**
     * @description: 输血结束
     * @return
     * @param item
     */
    endTransfusion(item) {
      this.openOrCloseDrawer(true, "输血结束");
      this.recordsCodeInfo.recordsCode = "BloodTransfusionEnd";
      this.performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = this._datetimeUtil.getNowTime("hh:mm");
      this.currentStation = item ? item.stationID : this.patient.stationID;
      this.currentDepartment = item ? item.departmentID : this.patient.departmentListID;
      //获得当前点击的记录的信息
      this.currentTransfusionRecord = item;
      this.patientTransfusionCareMainID = "";
      this.showVitalFlag = true;
      //通知医师
      this.informPhysicianArr = [true, false];
      this.bringToNursingRecordArr = [true, false];
      //获取评模板
      this.getTransfusionAssessView();
    },
    /**
     * description: 删除输血主记录
     * params {*} type : 1表示主记录 2 表示输血巡视记录
     * return {*}
     */
    async deleteTransfusion(item, type) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, item.nurseID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (!item) {
        this._showTip("Warn", "没有获取到删除的信息");
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (!flag) {
          return;
        }
        if (type == 1) {
          let param = {
            patientTransfusionRecordID: item.patientTransfusionRecordID,
          };
          DeleteTransfusionRecord(param).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功");
              this.$set(this.showRecordArr, 0, true);
              this.maintainDataList = [];
              this.getTransfusionRecordList();
              this.fixTable();
            }
          });
        } else {
          let param = {
            patientTransfusionCareMainID: item.patientTransfusionCareMainID,
          };
          DeleteTransfusionCareMain(param).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功");
              this.getTransfusionRecordList(this.currentTransfusionRecord?.patientTransfusionRecordID);
              this.GetTransfusionCareMainList();
            }
          });
        }
      });
    },
    /**
     * description: 维护主表新增或修改
     * param {*} item
     * return {*}
     */
    async maintainAddOrFix(item) {
      this.checkResult = true;
      if (item) {
        //是否仅本人操作
        this.checkResult = await this._common.checkActionAuthorization(this.user, item.addEmployeeID);
        if (this.checkResult) {
          let ret = await this._common.getEditAuthority(
            item.patientWoundCareMainID,
            "PatientWoundCareMain",
            !!this.supplementFlag
          );
          if (ret) {
            this.checkResult = false;
            this._showTip("warning", ret);
          } else {
            this.checkResult = true;
          }
        }
      }
      if (!this.currentTransfusionRecord) {
        this._showTip("warning", "请先选择输血主记录");
        return;
      }
      this.openOrCloseDrawer(true, "新增输血维护记录");
      // 模板类型RecordsCode
      this.recordsCodeInfo.recordsCode = item
        ? item.recordsCode
        : this.currentTransfusionRecord.endDateTime
        ? "BloodTransfusionEnd_4hMaintain"
        : "BloodTransfusionMaintain";
      this.currentStation = item ? item.stationID : this.patient.stationID;
      this.currentDepartment = item ? item.departmentID : this.patient.departmentListID;
      this.patientTransfusionCareMainID = item ? item.patientTransfusionCareMainID : "";
      this.showVitalFlag = true;
      if (!item) {
        this.performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.performTime = this._datetimeUtil.getNowTime("hh:mm");
      } else {
        this.performDate = item.inspectionDate
          ? this._datetimeUtil.formatDate(item.inspectionDate, "yyyy-MM-dd")
          : this._datetimeUtil.formatDate(item.scheduleDate, "yyyy-MM-dd");
        this.performTime = item.inspectionDate
          ? this._datetimeUtil.formatDate(item.inspectionDate, "hh:mm")
          : this._datetimeUtil.formatDate(item.scheduleTime, "hh:mm");
      }
      //通知医师
      this.$set(this.informPhysicianArr, 0, true);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      //带入护理记录单
      this.$set(this.bringToNursingRecordArr, 0, true);
      this.$set(this.bringToNursingRecordArr, 1, item && item.bringToNursingRecord ? true : false);
      //获取评模板
      this.getTransfusionAssessView();
    },
    /**
     * description: 组件保存按钮事件
     * return {*}
     */
    transfusionSave() {
      //主记录保存
      if (this.recordsCodeInfo.recordsCode == "BloodTransfusionStart") {
        this.transfusionRecordSave();
        return;
      }
      //维护记录保存
      if (["BloodTransfusionMaintain", "BloodTransfusionEnd_4hMaintain"].includes(this.recordsCodeInfo.recordsCode)) {
        this.transfusionCareMainSave();
        return;
      }
      //输血结束
      if (this.recordsCodeInfo.recordsCode == "BloodTransfusionEnd") {
        this.transfusionEndSave();
      }
    },
    /**
     * @description: 底部弹窗取消事件
     * @return
     */
    transfusionCancel() {
      this.openOrCloseDrawer(false, "");
    },
    /**
     * @description: 主记录勾选框事件
     * @return
     * @param flag
     */
    getMainFlag(flag) {
      if (flag) {
        //获取主表数据
        this.getTransfusionRecordList();
        this.maintainDataList = [];
      } else {
        if (this.mainDataList.length) {
          this.mainDataList = [this.mainDataList[0]];
          this.currentTransfusionRecord = this.mainDataList[0];
          this.GetTransfusionCareMainList();
        }
      }
    },
    /**
     * @description: 获取病人输血主记录
     * @return
     * @param recordID
     */
    getTransfusionRecordList(recordID = undefined) {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        recordID,
      };
      GetTransfusionRecordByInpatientID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.mainDataList = res.data;
          //重新获取数据置空选中主记录
          if (this.mainDataList.length > 0) {
            this.mainDataList.forEach((item) => {
              item.startDateTime = item.startDateTime
                ? this._datetimeUtil.formatDate(item.startDateTime, "yyyy-MM-dd hh:mm")
                : "";
              item.endDateTime = item.endDateTime
                ? this._datetimeUtil.formatDate(item.endDateTime, "yyyy-MM-dd hh:mm")
                : "";
            });
          }
          this.currentTransfusionRecord = recordID && this.mainDataList?.length ? this.mainDataList[0] : undefined;
        }
      });
    },
    /**
     * @description: 排程跳转获取主记录和维护记录
     * @return
     * @param recordID
     */
    async GetCurrentRecord(recordID) {
      let params = {
        recordID: recordID,
      };
      await GetCurrentTransfusionRecord(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data) {
            res.data.startDateTime = res.data.startDateTime
              ? this._datetimeUtil.formatDate(res.data.startDateTime, "yyyy-MM-dd hh:mm")
              : "";
            res.data.endDateTime = res.data.endDateTime
              ? this._datetimeUtil.formatDate(res.data.endDateTime, "yyyy-MM-dd hh:mm")
              : "";
            this.mainDataList = [res.data];
            this.currentTransfusionRecord = res.data;
          }
        }
      });
    },
    /**
     * @description: 获取血单号下拉框数据
     * @return
     */
    getBloodDonorBagsCodeArr() {
      let params = {
        inpatientID: this.patient.inpatientID,
        status: 1,
      };
      GetBloodDonorBagsCodeListByInpatientID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.bloodDonorBagsCodeArr = res.data;
        }
      });
    },
    /**
     * @description: 获取弹窗评估模板
     * @return
     */
    async getTransfusionAssessView() {
      //打开维护记录模板重新获取血单号
      if (this.recordsCodeInfo.recordsCode == "BloodTransfusionStart") {
        //获取该病人输血单号
        this.getBloodDonorBagsCodeArr();
      }
      let params = {
        departmentListID: this.patient.departmentListID,
        mappingType: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
      };
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "加载中……";
      await GetTransfusionRecordsCodeInfo(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          Object.assign(this.recordsCodeInfo, result.data);
        }
      });
      if (!this.recordsCodeInfo) return;
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        inpatientID: this.patient.inpatientID,
      };
      if (this.transfusionID) {
        params.transfusionID = this.transfusionID;
      }
      if (this.transfusionRecordID && this.recordsCodeInfo.recordsCode == "BloodTransfusionStart") {
        params.transfusionRecordID = this.transfusionRecordID;
      }
      if (this.patientTransfusionCareMainID && this.recordsCodeInfo.recordsCode != "BloodTransfusionStart") {
        params.transfusionCareMainID = this.patientTransfusionCareMainID;
      }
      await GetTransfusionAssessView(params).then((result) => {
        this.tabsLayoutLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * @description: 输血主记录保存
     * @return
     */
    transfusionRecordSave() {
      //保存检核
      if (!this.saveCheck()) {
        return;
      }
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "保存中……";
      // 新增保存数据
      let saveData = {
        Main: {},
        Details: this.getDetail(),
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: false,
        supplementFlag: this.supplementFlag,
      };
      //如果来源于措施执行则填入
      if (this.patientScheduleMainID) {
        saveData.PatientScheduleMainID = this.patientScheduleMainID;
      }
      //组装保存main数据
      saveData.Main.patientTransfusionRecordID = this.transfusionRecordID;
      saveData.Main.inpatientID = this.patient.inpatientID;
      saveData.Main.transfusionID = this.transfusionID;
      saveData.Main.transfusionStartDate = this.performDate + " " + this.performTime;
      saveData.Main.stationID = this.currentStation;
      saveData.Main.departmentID = this.currentDepartment;

      let sucBloodBagData = this.bloodDonorBagsCodeArr.find((item) => item.label == this.bloodDonorBagsCode);
      if (sucBloodBagData && sucBloodBagData.code) {
        saveData.Main.BloodNumber = sucBloodBagData.code;
        saveData.Main.OuterBloodCode = sucBloodBagData.outerCode;
        saveData.Main.bloodDonorBagsCode = sucBloodBagData.value;
      } else {
        //修改处理
        if (this.bloodDonorBagsCode) {
          saveData.Main.BloodNumber = this.bloodNumber;
          saveData.Main.OuterBloodCode = this.outerBloodCode;
          saveData.Main.bloodDonorBagsCode = this.bloodDonorBagsCode;
        }
      }

      SavePatientTransfusionRecord(saveData).then((res) => {
        this.tabsLayoutLoading = false;
        if (this._common.isSuccess(res)) {
          this.openOrCloseDrawer(false, "");
          this._showTip("success", "保存成功");
          //显示主记录
          this.$set(this.showRecordArr, 0, true);
          //不显示维护记录table框体
          this.$set(this.showRecordArr, 1, false);
          //保存成功后重新获取输血记录
          this.getTransfusionRecordList();
        }
      });
    },
    /**
     * @description: 主记录行点击事件
     * @return
     * @param item
     * @param column
     * @param event
     */
    async getMaintainList(item, column, event) {
      //切换页面形态 主记录只显示选中记录
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.mainDataList = [item];
        this.currentTransfusionRecord = item;
        await this.GetTransfusionCareMainList();
        this.$nextTick(() => {
          this.tableOneRowHeight = this._common.getTableOneRowHeight(
            this.$refs.recordTable?.$el,
            ".main-record-row",
            ".main-record-header-row"
          );
        });
      }
    },
    /**
     * @description: 获取输血维护主记录
     * @return
     */
    async GetTransfusionCareMainList() {
      if (!this.currentTransfusionRecord) {
        return;
      }
      this.maintainDataList = [];
      let params = {
        transfusionID: this.currentTransfusionRecord.transfusionID,
      };
      await GetTransfusionCareMainByTransfusionID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.maintainDataList = res.data;
          if (this.maintainDataList.length > 0) {
            this.maintainDataList.forEach((item) => {
              item.scheduleDate = this._datetimeUtil.formatDate(item.scheduleDate, "yyyy-MM-dd");
              item.scheduleTime = this._datetimeUtil.formatDate(item.scheduleTime, "hh:mm");
              item.scheduleDateTime = item.scheduleDate + " " + item.scheduleTime;
              item.inspectionDate = item.inspectionDate
                ? (item.inspectionDate = this._datetimeUtil.formatDate(item.inspectionDate, "yyyy-MM-dd hh:mm"))
                : "";
            });
            //该排序是为了让开始评估始终显示在第一行
            this.maintainDataList.sort((a, b) => {
              if (a.recordsCode == "BloodTransfusionStart") {
                return -1;
              } else if (b.recordsCode == "BloodTransfusionStart") {
                return 1;
              } else {
                return a.scheduleDateTime < b.scheduleDateTime ? -1 : 1;
              }
            });
          }
        }
      });
    },
    /**
     * @description: 维护记录保存
     * @return
     */
    transfusionCareMainSave() {
      if (!this.saveCheck()) {
        return;
      }
      let params = {
        transfusionID: this.currentTransfusionRecord?.transfusionID,
        inpatientID: this.patient.inpatientID,
        details: this.getDetail(),
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessDate: this.performDate,
        assessTime: this.performTime,
        stationID: this.currentStation,
        departmentListID: this.currentDepartment,
        patientTransfusionCareMainID: this.patientTransfusionCareMainID,
        BringToShift: false,
        supplementFlag: this.supplementFlag,
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
      };
      if (this.patientScheduleMainID) {
        params.PatientScheduleMainID = this.patientScheduleMainID;
      }
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "保存中……";
      SavePatientTransfusionCareMain(params).then((res) => {
        this.tabsLayoutLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.openOrCloseDrawer(false, "");
          this.getTransfusionRecordList(this.currentTransfusionRecord.patientTransfusionRecordID);
          this.GetTransfusionCareMainList();
        }
      });
    },
    /**
     * description: 组件保存按钮事件
     * return {*}
     */
    transfusionEndSave() {
      if (!this.saveCheck()) {
        return;
      }
      let params = {
        patientTransfusionRecord: this.currentTransfusionRecord.patientTransfusionRecordID,
        careDetails: this.getDetail(),
        recordsCode: this.recordsCodeInfo.recordsCode,
        //暂时写死,看后期如何处理
        isEnd: false,
        assessDate: this.performDate,
        assessTime: this.performTime,
        stationID: this.currentStation,
        departmentListID: this.currentDepartment,
        BringToShift: false,
        supplementFlag: this.supplementFlag,
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
      };
      if (this.patientScheduleMainID) {
        params.PatientScheduleMainID = this.patientScheduleMainID;
      }
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "保存中……";
      StopTransfusionRecord(params).then((res) => {
        this.tabsLayoutLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.openOrCloseDrawer(false, "");
          this.GetTransfusionCareMainList();
          this.GetCurrentRecord(params.patientTransfusionRecord);
          this.fixTable();
        }
      });
    },
    /**
     * @description: 评估保存检核
     * @return
     */
    saveCheck() {
      if (!this.performDate || !this.performTime) {
        this._showTip("warning", "请填写执行日期和时间");
        return false;
      }
      if (this.currentStation == "") {
        this._showTip("warning", "请选择发生病区");
        return false;
      }
      if (this.currentDepartment == "") {
        this._showTip("warning", "请选择发生科室");
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      if (!this.$refs.transfusionLayout || !this.$refs.transfusionLayout.checkRequire()) {
        return;
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        return false;
      }
      if (this.recordsCodeInfo.recordsCode == "BloodTransfusionStart") {
        if (!this.bloodDonorBagsCode) {
          this._showTip("warning", "请选择输血单号！");
          return false;
        }
        if (!this.assessDatas.find((item) => item.assessListGroupID == this.bloodProductCategoryGroupID)) {
          this._showTip("warning", "请选择血液制品！");
          return false;
        }
      }
      if (this.recordsCodeInfo.recordsCode == "BloodTransfusionEnd") {
        if (
          !this.assessDatas.find((item) => item.assessListID == "30084") ||
          !this.assessDatas.find((item) => item.assessListGroupID == "30085")
        ) {
          this._showTip("warning", "请填写输血量和输血单位");
          return false;
        }
        if (!this._regularNumber(this.assessDatas.find((item) => item.assessListID == "30084").assessValue)) {
          this._showTip("warning", "输血量请输入正数");
          return false;
        }
      }
      return true;
    },
    /**
     * @description: 评估模板返回数据
     * @return
     * @param datas
     */
    changeValues(datas) {
      this.assessDatas = datas;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * @description: 排程跳转获取主记录和维护记录
     * @return
     */
    async getRecordAndMaintain() {
      let params = {
        scheduleMainID: this.routeData.patientScheduleMainID,
      };
      await GetCurrentTransfusionRecord(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data) {
            this.mainDataList.push(res.data);
            this.scheduleToRecord = res.data;
          }
        }
      });
    },
    /**
     * @description: 排程跳转处理
     * @return
     */
    async scheduleJump() {
      if (this.maintainDataList.length == 0) {
        return;
      }
      let sucCareMain = this.maintainDataList.find(
        (item) => item.patientScheduleMainID == this.routeData.patientScheduleMainID
      );
      if (sucCareMain != null) {
        await this.maintainAddOrFix(sucCareMain);
      }
    },
    /**
     * @description: 专项护理弹窗开关函数
     * @return
     * @param flag
     * @param title
     */
    openOrCloseDrawer(flag, title) {
      this.showMaintainFlag = flag;
      this.transfusionDrawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
      if (!flag) {
        this.transfusionRecordID = "";
        this.transfusionCareMainID = "";
      }
    },
    /**
     * @description: 组装保存明细数据
     * @return
     */
    getDetail() {
      let details = [];
      if (this.assessDatas.length == 0) {
        return details;
      }
      //组装保存Detail数据
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        details.push(detail);
      });
      return details;
    },
    /**
     * @description: 通知医师标记
     * @return
     * @param flag
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * @description: 带入护理记录标记
     * @return
     * @param flag
     */
    getBringToNursingRecordFlag(flag) {
      this.bringToNursingRecordArr[1] = flag;
    },
    /**
     * description: 重置选中状态
     * param {*}
     * return {*}
     */
    fixTable() {
      this.showRecordArr = [true, false];
      this.maintainDataList = [];
    },
    /**
     * description: 加载最新生命体征数据
     * param {*}
     * return {*}
     */
    openClinic() {
      this.clinicPrams = {
        inpatientID: this.patient.inpatientID,
        scheduleDate: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
        scheduleTime: this._datetimeUtil.formatDate(new Date(), "hh:mm:ss"),
      };
      this.showClinicFlag = true;
    },
    /**
     * description:单击触发事件
     * param {*}当前行数据
     * return {*}
     */
    postClinicData(value) {
      let clinicDataArr = Object.entries(value);
      clinicDataArr.forEach((item) => {
        if (item[1]) {
          this.$refs.transfusionLayout.setItemValue(item[0], item[1]);
        }
      });
      this.showClinicFlag = false;
    },
  },
};
</script>