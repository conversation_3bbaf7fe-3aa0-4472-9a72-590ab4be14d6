<template>
  <base-layout class="assess-main" ref="layout">
    <div slot="header" class="top">
      <el-button class="add-button" icon="iconfont icon-add" @click="goAddOrModify('add')">新增</el-button>
    </div>
    <u-table
      :data="list"
      v-loading="loading"
      element-loading-text="加载中……"
      border
      stripe
      :height="$refs.layout ? $refs.layout.getMainHeight() : 0"
      highlight-current-row
      :row-height="30"
      use-virtual
    >
      <u-table-column prop="deptName" label="科室" :min-width="convertPX(180)" header-align="center"></u-table-column>
      <u-table-column label="名称" :min-width="convertPX(160)" header-align="center">
        <template slot-scope="scope">
          <el-tooltip content="补录" v-if="scope.row.additional">
            <i class="iconfont icon-info"></i>
          </el-tooltip>
          {{ scope.row.typeDes }}
        </template>
      </u-table-column>
      <u-table-column
        prop="nursingLevelDescription"
        label="护理级别"
        :min-width="convertPX(100)"
        align="center"
      ></u-table-column>
      <u-table-column prop="sort" label="次数" :width="convertPX(65)" align="center"></u-table-column>
      <u-table-column label="状态" :width="convertPX(85)" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.tempSaveMark === 'S'">已完成</span>
          <span class="status" v-else>暂存</span>
        </template>
      </u-table-column>
      <u-table-column prop="addEmployeeName" label="评估人" :min-width="convertPX(90)" align="center"></u-table-column>
      <u-table-column
        prop="modifyEmployeeName"
        label="修改人"
        :min-width="convertPX(90)"
        align="center"
      ></u-table-column>
      <u-table-column label="时间" :min-width="convertPX(140)" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.assessDate, type: 'date' }"></span>
          <span v-formatTime="{ value: scope.row.assessTime, type: 'time' }"></span>
        </template>
      </u-table-column>
      <u-table-column label="操作" :width="convertPX(145)" header-align="center">
        <template slot-scope="scope">
          <el-tooltip content="修改" v-if="!scope.row.additional">
            <i class="iconfont icon-edit" @click="goAddOrModify('modify', scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="查看" v-if="scope.row.additional || onlyRead">
            <i class="iconfont icon-more" @click="goAddOrModify('watch', scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除" v-if="scope.row.tempSaveMark === 'T'">
            <i class="iconfont icon-del" @click="deleteAssess(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="入院告知单" v-if="scope.row.tempSaveMark !== 'T' && scope.row.emrDocument != null">
            <i class="iconfont icon-pdf" @click="getAdmissionNotice(scope.row.emrDocument)"></i>
          </el-tooltip>
        </template>
      </u-table-column>
    </u-table>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="noticeVisible"
      custom-class="no-footer print-dialog"
      title="入院告知单"
      v-if="noticeVisible"
      fullscreen
    >
      <document-sign :iframeType="'application/x-google-chrome-pdf'" :signParams="notificationParams"></document-sign>
    </el-dialog>
  </base-layout>
</template>

<script>
import {
  GetAssessMainInfo,
  DeleteItemByID,
  PatientProblemAssessCheck,
  GetPatientIsTransOut,
  PatientEventAssessCheckAsync,
} from "@/api/Assess";
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
import { PatientAttendanceCheck } from "@/api/UserCheck";
import documentSign from "@/components/DocumentSign";
import { CheckPatientInDept } from "@/api/PatientCheck";

export default {
  components: { baseLayout, documentSign },
  data() {
    return {
      list: [],
      inpatientID: "",
      loading: false,
      inpatientID: "",
      noticeVisible: false,
      showMessage: undefined,
      //获取告知书的参数
      notificationParams: {
        inpatientID: undefined,
        emrDocumentID: undefined,
      },
      //操作功能是否开放，是否只读
      onlyRead: false,
    };
  },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
    }),
  },
  watch: {
    patient(newPatient) {
      if (newPatient) {
        this.getlist();
      } else {
        this.list = [];
      }
    },
  },
  mounted() {
    // 设置可切换病人
    this._sendBroadcast("setPatientSwitch", true);
    if (this.patient && this.patient.inpatientID) {
      this.getlist();
    }
    if (this.$route.query?.onlyRead) {
      this.onlyRead = true;
    }
  },
  methods: {
    // 新增或修改评估
    async goAddOrModify(type, main) {
      if (this.loading) {
        return;
      }
      if (!this.patient) {
        this._showTip("warning", "请选择病人！");
        return;
      }
      let num = 0;
      let sort = 0;
      let assessMainListLength = this.list?.length;
      let checkAssessDate = "";
      if (type == "add") {
        let params = {
          InpatientID: this.patient.inpatientID,
        };
        let attendanFlag;
        //2021-06-17 En 中山需求主次责护士才能够评估
        await PatientAttendanceCheck(params).then((result) => {
          if (this._common.isSuccess(result)) {
            attendanFlag = result.data;
          }
        });
        if (!attendanFlag) {
          this._showTip("warning", "病人尚未分配主责护士!");
          return;
        }
        let eventFlag;
        await CheckPatientInDept(params).then((result) => {
          if (this._common.isSuccess(result)) {
            eventFlag = result.data.isChecked;
          }
        });
        if (!eventFlag) {
          this._showTip("warning", "患者不在科，暂无法进行评估!");
          return;
        }
        await PatientEventAssessCheckAsync(params).then((result) => {
          this._common.isSuccess(result);
        });
        if (this.list && this.list.length > 0) {
          if (this.list[0].tempSaveMark == "T") {
            this._showTip("warning", "您有暂存的评估没有保存！");
            return;
          }
          //判断是否有护理计划未提交或有护理问题未评价
          PatientProblemAssessCheck(params).then((result) => {
            if (this._common.isSuccess(result)) {
              if (this.list.length == 0) {
                num = 1;
              } else {
                num = this.list[0].numberOfAssessment + 1;
                sort = this.list[0].sort + 1;
              }
              if (assessMainListLength) {
                let lastAssess = this.list.find((assess) => assess.sort == assessMainListLength);
                if (!lastAssess) {
                  lastAssess = this.list.find((assess) => assess.numberOfAssessment == assessMainListLength);
                }
                if (lastAssess) {
                  checkAssessDate = `${this._datetimeUtil.formatDate(
                    lastAssess.assessDate,
                    "yyyy-MM-dd"
                  )} ${this._datetimeUtil.formatDate(lastAssess.assessTime, "hh:mm")}`;
                }
              }
              this.goDetail(type, main, num, sort, checkAssessDate);
            }
          });
        } else {
          num = 1;
          sort = 1;
          this.goDetail(type, main, num, sort, checkAssessDate);
        }
      } else if (type == "watch") {
        // 补录查看
        num = main.numberOfAssessment;
        sort = main.sort;
        this.goDetail(type, main, num, sort, checkAssessDate);
      } else {
        num = main.numberOfAssessment;
        sort = main.sort;
        if (sort) {
          let lastAssess = this.list.find((assess) => assess.sort == sort - 1);
          if (lastAssess) {
            checkAssessDate = `${this._datetimeUtil.formatDate(
              lastAssess.assessDate,
              "yyyy-MM-dd"
            )} ${this._datetimeUtil.formatDate(lastAssess.assessTime, "hh:mm")}`;
          }
        }
        this.goDetail(type, main, num, sort, checkAssessDate);
      }
    },
    goDetail(type, main, num, sort, checkAssessDate) {
      let params = {
        inpatientID: this.patient.inpatientID,
        patient: this.patient,
        assessMain: main,
        type: type,
        num: num,
        sort: sort,
        checkAssessDate: checkAssessDate,
      };
      this.$router.push({
        name: this.onlyRead ? "assessDetailLook" : "assessDetail",
        params: params,
        query: { inpatientID: this.patient.inpatientID, onlyRead: this.onlyRead },
      });
    },

    async getlist() {
      if (!this.patient) return;
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      this.loading = true;
      //获取病人评估信息
      await GetAssessMainInfo(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.list = result.data;
        }
      });
    },
    /**
     * description: 获取告知书
     * return {*}
     * param {*} emrCode 告知书对应的ID,可为空：取this.emrDocumentID
     */
    getAdmissionNotice(emrCode) {
      this.notificationParams = {
        inpatientID: this.patient.inpatientID,
        emrDocumentID: emrCode,
      };
      this.noticeVisible = true;
    },

    deleteAssess(row) {
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            ID: row.id,
          };
          DeleteItemByID(params).then((response) => {
            if (_this._common.isSuccess(response)) {
              _this._showTip("success", "删除成功");
              _this.getlist();
            }
          });
        }
      });
    },
    //判断转科病人是否有入院评估
    checkTransOut() {
      let flag = false;
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      GetPatientIsTransOut(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data.transOutFlag && !result.data.admissionFlag) {
            this._showTip("warning", "请在转出科室进行入院评估补录");
          } else {
            flag = true;
          }
        }
      });
      return flag;
    },
  },
};
</script>
<style lang="scss">
.assess-main {
  .base-header {
    .top {
      text-align: right;
    }
  }
  .table-wrap {
    height: 100%;
    .status {
      color: #ea4380;
      font-weight: 600;
    }
    .source-flag {
      float: right;
      margin-left: 10px;
      // display: inline-block;
      height: 20px;
      line-height: 20px;
      width: 20px;
      border-radius: 10px;
      text-align: center;
      font-size: 12px;
      color: #fff;
      font-weight: bold;
      margin-bottom: 2px;
      background-color: #8cc63e;
    }
  }
}
</style>
