<!--
 * FilePath     : \src\autoPages\patientList\components\patientBed1.vue
 * Author       : 李青原
 * Date         : 2020-05-10 16:19
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-04 16:27
 * Description  :  病人卡片组件1(通用)
 -->

<template>
  <div :class="['patient-bed-one', { small: !whole }, { 'filter-mask': patientInfo.filterMaskFlag }]">
    <div class="patient-info" :style="bedCardStyle">
      <div class="header-info">
        <tip-marker
          v-if="jobTipList && jobTipList.length > 0"
          class="job-tip"
          :whiteColor="focusOn"
          :width="convertPX(220)"
          :tipList="jobTipList"
          @jumpPage="tipJumpPage"
        ></tip-marker>
        <div class="bed-number" :style="bedNumberStyle" :title="patientInfo.bedNumber">
          {{ patientInfo.bedNumber }}
        </div>
        <div class="gender" v-if="!emptyBed">
          {{ patientInfo.gender ? patientInfo.gender : "" }}
        </div>
        <div class="patient-name" v-if="!emptyBed">
          <div v-if="isPrintable" class="name is-print" :title="patientInfo.patientName" @click="bedsideCard">
            {{ patientInfo.patientName }}
          </div>
          <div v-else class="name" :title="patientInfo.patientName">{{ patientInfo.patientName }}</div>
          <div class="age" :title="patientInfo.ageDetail">{{ patientInfo.ageDetail }}</div>
        </div>
      </div>
      <div class="detail-info" v-if="whole && !emptyBed">
        <div class="diagnosis" :title="patientInfo.diagnose">
          {{ patientInfo.diagnose }}
        </div>
        <div class="case-number-days">
          <span>住院号:&nbsp;</span>
          <span>{{ patientInfo.localCaseNumber }}</span>
          <div class="days" :title="'入院' + patientInfo.days + '天'">{{ patientInfo.days }}</div>
        </div>
        <div>
          <span>入院日期:&nbsp;</span>
          <span v-formatTime="{ value: patientInfo.admissionDate, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
        </div>
        <div>
          <span>出生日期:&nbsp;</span>
          <span v-formatTime="{ value: patientInfo.dateOfBirth, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
        </div>
        <div>
          <div class="physicianName" :title="patientInfo.physicianName">
            <span>医:&nbsp;</span>
            {{ patientInfo.physicianName }}
          </div>
          <div class="careNurse" :title="patientInfo.careNurse">
            <span>护:&nbsp;</span>
            {{ patientInfo.careNurse }}
          </div>
        </div>
      </div>
    </div>
    <div class="mark-list" v-if="whole && !emptyBed">
      <template v-for="(item, index) in patientInfo.inPatientMarkStyleList">
        <patient-mark v-if="item.display" :key="index" :markStyle="item" type="1" :showGroup="showGroup"></patient-mark>
      </template>
    </div>
  </div>
</template>

<script>
import tipMarker from "./tipMarker";
import patientMark from "./patientMark";
export default {
  components: { tipMarker, patientMark },
  props: {
    patientInfo: {
      type: Object,
      required: true,
    },
    focusOn: { type: Boolean, default: false },
    whole: { type: Boolean, default: true },
    isPrintable: {
      type: Boolean,
      default: true,
    },
    jobTipList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    showGroup: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      nursingColor: "#ffffff",
      emptyBed: true,
    };
  },
  computed: {
    bedCardStyle() {
      let style = {};
      if (this.focusOn) {
        let color = "#ffffff";
        if (
          !this.patientInfo.inPatientNursingLevelStyle ||
          !this.patientInfo.nursingLevel ||
          this.nursingColor == "#ffffff"
        ) {
          color = "#000000";
        }
        style.color = color;
        style.backgroundColor = this.nursingColor;
        if (
          !this.patientInfo.inPatientNursingLevelStyle ||
          !this.patientInfo.nursingLevel ||
          this.nursingColor == "#ffffff"
        ) {
          style.backgroundColor = "#dedede";
        }
      } else {
        style.color = "#000000";
      }
      return style;
    },
    bedNumberStyle() {
      if (!this.patientInfo) return;
      let style = {};
      let nursingLevel = this.patientInfo.inPatientNursingLevelStyle;
      if (nursingLevel && nursingLevel.backGroundColor) {
        this.nursingColor = nursingLevel.backGroundColor;
        style.backgroundColor = nursingLevel.backGroundColor;
      } else {
        if (this.focusOn) {
          style.backgroundColor = "#dedede";
        } else {
          style.backgroundColor = "#ffffff";
        }
      }
      return style;
    },
  },
  mounted() {
    this.emptyBed = this.patientInfo.chartNo == "" && this.patientInfo.caseNumber == "";
  },
  methods: {
    //床头卡事件
    bedsideCard() {
      this.$emit("bedside-card", this.patientInfo);
    },

    tipJumpPage(router) {
      if (!router) {
        return;
      }
      this.$emit("tipJumpPage", router);
    },
  },
};
</script>

<style lang="scss">
.patient-bed-one {
  float: left;
  display: flex;
  flex-direction: column;
  width: 210px;
  height: 240px;
  border: 1px solid #e3e3e3;
  background-color: #ffffff;
  box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
  & * {
    font-size: 15px;
  }
  &.small {
    height: 70px;
  }
  &.filter-mask,
  &.filter-mask * {
    color: #ffffff !important;
    background-color: #e3e3e3 !important;
  }
  .patient-info {
    flex: auto;
    height: 100%;
    .header-info {
      position: relative;
      .job-tip .tip-marker {
        top: -5px;
        left: 5px;
        width: 24px;
        height: 24px;
        font-size: 24px !important;
      }
      .gender {
        position: absolute;
        right: 5px;
        top: 3px;
        width: 30px;
        height: 30px;
        font-weight: bold;
        line-height: 28px;
        border-radius: 30px;
        text-align: center;
        color: #000000;
        background-color: #ffffff;
      }
      .bed-number {
        height: 32px;
        line-height: 31px;
        margin: 0 45px;
        text-align: center;
        font-weight: bold;
        font-size: 24px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .patient-name {
        margin-top: 3px;
        .name {
          float: left;
          padding-left: 10px;
          max-width: calc(100% - 80px);
          font-weight: bold;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 20px;
          &.is-print:hover {
            text-decoration: underline;
            cursor: pointer;
          }
        }
        .age {
          float: right;
          width: 60px;
          padding-right: 7px;
          text-align: right;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .detail-info {
      line-height: 22px;
      padding-left: 10px;
      .diagnosis {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .case-number-days {
        .days {
          float: right;
          min-width: 15px;
          margin-right: 5px;
          padding: 0 2px;
          border-radius: 3px;
          font-size: 12px;
          color: #000000;
          background-color: #cccccc;
        }
      }
      .physicianName {
        display: inline-block;
        width: calc(50% - 5px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .careNurse {
        display: inline-block;
        width: calc(50% - 5px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .mark-list {
    width: 100%;
    line-height: 24px;
    padding: 0 2px 5px 2px;
    box-sizing: border-box;
    border-top: 1px solid #f0f0f0;
  }
}
@media screen and (max-width: 1280px) {
  .patient-bed-one {
    width: 178px;
    height: 210px;
    & * {
      font-size: 13px;
    }
    .bed-number {
      height: 28px !important;
      line-height: 28px !important;
      font-size: 20px !important;
    }
    .gender {
      height: 24px !important;
      width: 24px !important;
      line-height: 23px !important;
    }
    .name {
      font-size: 16px !important;
    }
    .detail-info {
      line-height: 20px !important;
    }
    .mark-list {
      line-height: 22px !important;
    }
  }
}
</style>
