/*
 * FilePath     : \src\autoPages\monitoringScheduler\mixins\formulaExec.js
 * Author       : 杨欣欣
 * Date         : 2024-08-27 17:35
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-09-12 08:45
 * Description  : 公式替换与表达式计算
 * CodeIterationRecord:
 */
import decimalUtil from "@/utils/decimalUtil";
export function formulaExec(row, columns, obj) {
  const formula = obj?.formula;
  if (!formula) {
    return {};
  }
  const params = formula.params;
  let expression = formula.expression;
  const cells = Object.values(row);
  for (let param of params) {
    cells.forEach((cell) => {
      let value = 0;
      //处理下拉框公式计算
      if (cell.style?.endsWith("DL")) {
        const option = columns
          .find((column) => column.index == cell.columnIndex)
          ?.childColumns?.find((child) => child.assessListID == param.id);
        if (option) {
          option.assessListID == cell.assessValue &&
            (value = option.linkForm?.trim() || 0);
          expression = expression.replace(param.key, value);
        }
      }
      if (cell.assessListID == param.id) {
        value = cell.assessValue?.trim();
        expression = expression.replace(param.key, value);
      }
    });
  }
  if (expression.indexOf("}") > 0) {
    obj.assessValue = "";
    return {};
  }
  let newValue = "";
  //解决eval(expression)中expression不规范导致报错
  try {
    newValue =
      decimalUtil.decimalRound(
        eval(expression),
        formula.decimalRule,
        formula.decimalValue
      ) + "";
  } catch (error) {
    newValue = "";
  }
  return { expression, newValue };
}
