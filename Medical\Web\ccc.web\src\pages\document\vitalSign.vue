<!--
 * FilePath     : \src\pages\document\vitalSign.vue
 * Author       : 郭鹏超
 * Date         : 2020-06-08 09:41
 * LastEditors  : 来江禹
 * LastEditTime : 2023-07-04 10:40
 * Description  : 生命体征
--> 
<template>
  <temperature-list :query="$route.query" v-model="tabsData"></temperature-list>
</template>

<script>
import temperatureList from "@/components/TemperatureList";
import { GetPatientVitalSignData } from "@/api/VitalSign";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      //体温单tabs和PDF数据
      tabsData: {},
    };
  },
  components: {
    temperatureList,
  },
  computed: {
    ...mapGetters({
      currentPatient: "getCurrentPatient",
    }),
  },
  watch: {
    "currentPatient.inpatientID": {
      handler(newValue) {
        if (newValue) {
          this.getTabsData();
        }
      },
      immediate: true,
    },
  },
  created() {
    if (this.$route.query.noToken) {
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
  },
  methods: {
    //获取tabs数据
    async getTabsData() {
      let params = {
        inPatientID: this.currentPatient.inpatientID,
        startTime: this.currentPatient.admissionDate,
        endTime: this._datetimeUtil.getNow(),
      };
      // 无Token访问
      if (this.$route.query.noToken) {
        params.hospitalID = this.$route.query.hospitalID;
        params.language = this.$route.query.language;
      }
      this.tabsData = {};
      await GetPatientVitalSignData(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.length == 0) {
            this._showTip("warning", "暂无数据");
            return;
          }
          this.tabsData = res.data;
        }
      });
    },
  },
};
</script>