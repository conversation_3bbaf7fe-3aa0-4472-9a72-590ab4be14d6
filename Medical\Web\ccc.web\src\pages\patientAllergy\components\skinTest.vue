<!--
 * FilePath     : \src\pages\patientAllergy\components\skinTest.vue
 * Author       : 孟昭永
 * Date         : 2022-02-21 11:11
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-06-05 11:21
 * Description  :
-->
<template>
  <base-layout class="skin-test" :showHeader="canEdit">
    <div slot="header">
      <div class="top-btn">
        <el-button class="add-button" icon="iconfont icon-add" @click="addRow">新增</el-button>
      </div>
    </div>
    <el-table
      class="skin-test-table"
      :data="skinTestList"
      border
      stripe
      height="100%"
      v-loading="loading"
      :element-loading-text="loadingText"
    >
      <el-table-column label="药物类型" min-width="160">
        <template slot-scope="scope">
          <el-select
            v-model="scope.row.allergyBasicID"
            filterable
            allow-create
            default-first-option
            placeholder="请选择药物类型"
            class="allergy-basic"
          >
            <el-option
              v-for="(allergyDrug, index) in allergyDrugList"
              :key="index"
              :label="allergyDrug.allergyName"
              :value="allergyDrug.allergyBasicID"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="测试结果" width="120" align="center">
        <template slot-scope="scope">
          <el-select v-model="scope.row.interventionDetailID" placeholder="请选择测试结果">
            <el-option
              v-for="(item, index) in options"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="测试日期" width="145" align="center">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.performDateTime"
            value-format="yyyy-MM-dd HH:mm"
            format="yyyy-MM-dd HH:mm"
            type="datetime"
            placeholder="请选择测试日期"
            class="perform-datetime"
          ></el-date-picker>
        </template>
      </el-table-column>
      <el-table-column label="测试人员" prop="performPersonID" width="110" align="center">
        <template slot-scope="scope">
          <el-select v-model="scope.row.performPersonID" placeholder="请选择人员">
            <el-option
              v-for="(nurse, index) in nurseList"
              :key="index"
              :label="nurse.name"
              :value="nurse.userID"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" align="center">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.patientSkinTestID ? '修改' : '保存'">
            <div
              :class="['iconfont', scope.row.patientSkinTestID ? 'icon-edit' : 'icon-save']"
              @click="save(scope.row)"
            ></div>
          </el-tooltip>
          <el-tooltip v-if="canEdit" content="删除">
            <div class="iconfont icon-del" @click="del(scope.row.patientSkinTestID)"></div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import nurseSelector from "@/components/selector/nurseSelector";
import { mapGetters } from "vuex";
import { GetNurse } from "@/api/User";
import { GetSkinTestByChartNo, SaveSkinTest, GetAllergyDrugList, DeleteSkinTestByID } from "@/api/Allergy";
import { GetClinicalBySettingTypeCodeAndValue, GetViewShow } from "@/api/Setting";
export default {
  components: {
    baseLayout,
    nurseSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  props: {
    patient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  watch: {
    "patient.inpatientID": {
      handler(newValue) {
        if (!newValue) {
          return;
        }
        this.init();
      },
      immediate: true,
    },
  },
  data() {
    return {
      //皮试记录
      skinTestList: [],
      loading: false,
      loadingText: "",
      //皮试结果选项
      options: [],
      // 是否显示新增、删除按钮
      canEdit: false,
      allergyDrugList: [],
      nurseList: [],
    };
  },
  methods: {
    /**
     * description: 初始加载皮试记录
     * param {*}
     * return {*}
     */
    async init() {
      await this.getSetting();
      await this.getSkinTestData();
    },
    /**
     * description: 获取皮试表格数据
     * param {*}
     * return {*}
     */
    async getSkinTestData() {
      if (this.options.length == 0) {
        return;
      }
      let params = {
        chartNo: this.patient.chartNo,
        index: Math.random(),
      };
      this.loading = true;
      this.loadingText = "加载中……";
      await GetSkinTestByChartNo(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.skinTestList = result.data;
          this.skinTestList.forEach((skinTest) => {
            // 如果没有allergyBasicID，但有allergyName，说明是自定类型
            if (skinTest.allergyName && !skinTest.allergyBasicID) {
              skinTest.allergyBasicID = skinTest.allergyName;
            }
          });
        }
      });
    },

    /**
     * description: 皮试新增
     * param {*}
     * return {*}
     */
    addRow() {
      if (this.skinTestList && this.skinTestList.length > 0 && !this.skinTestList[0].patientSkinTestID) {
        this._showTip("warning", "请先保存上一条皮试！");
        return;
      }
      let row = {
        caseNumber: this.patient.caseNumber,
        chartNo: this.patient.chartNo,
        inpatientID: this.patient.inpatientID,
        patientScheduleMainID: "",
        performDateTime: this._datetimeUtil.getNow(),
        performPersonID: this.user.userID,
      };
      this.skinTestList.unshift(row);
    },
    /**
     * description: 保存皮试记录
     * param {*} row
     * return {*}
     */
    async save(row) {
      let checkData = [row.performDateTime, row.interventionDetailID, row.performPersonID];
      let message = ["请选择测试日期！", "请选择测试结果！", "请选择测试人员！"];
      let index = checkData.findIndex((data) => !data);
      if (index != -1) {
        this._showTip("warning", message[index]);
        return;
      }
      let params = this._common.clone(row);
      params.allergyResultName = this.getSkinTestName(params.interventionDetailID);
      let allergyDrug = this.getAllergyDrug(params.allergyBasicID);
      if (allergyDrug) {
        params.chemicCode = allergyDrug.chemicCode;
        params.allergyName = allergyDrug.allergyName;
      } else {
        // 自定义类型
        params.allergyBasicID = "";
        params.chemicCode = "";
        params.allergyName = row.allergyBasicID;
      }
      params.RefillFlag = this.patient.supplemnentFlag;
      this.loading = true;
      this.loadingText = "保存中……";
      await SaveSkinTest(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
        }
      });
      this._sendBroadcast("refreshInpatient");
      await this.init();
    },
    /**
     * description: 删除皮试记录
     * param {*} patientSkinTestID
     * return {*}
     */
    del(patientSkinTestID) {
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          //确认删除
          let params = {
            patientSkinTestID: patientSkinTestID,
          };
          this.loading = true;
          this.loadingText = "删除中……";
          DeleteSkinTestByID(params).then((res) => {
            this.loading = false;
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功！");
              this._sendBroadcast("refreshInpatient");
              this.init();
            }
          });
        }
      });
    },
    /**
     * description: 获取皮试结果文字
     * param {*} interventionDetailID
     * return {*}
     */
    getSkinTestName(interventionDetailID) {
      let obj = this.options.find((item) => {
        return item.value == interventionDetailID;
      });
      let label = "";
      if (obj) {
        label = obj.label;
      }
      return label;
    },
    /**
     * description: 获取过敏药物药理码
     * param {*} allergyBasicID
     * return {*}
     */
    getAllergyDrug(allergyBasicID) {
      let allergyDrug = this.allergyDrugList.find((item) => {
        return item.allergyBasicID == allergyBasicID;
      });
      return allergyDrug;
    },
    /**
     * description: 获取皮试结果配置
     * param {*}
     * return {*}
     */
    async getSetting() {
      this.options = [];
      let params = {
        settingTypeCode: "InterventionCode",
        typeValue: "SkinTestResult",
      };
      await GetClinicalBySettingTypeCodeAndValue(params).then((response) => {
        if (this._common.isSuccess(response)) {
          let data = response.data;
          if (data.length == 0) return;
          for (let i = 0; i < data.length; i++) {
            let option = {
              value: parseInt(data[i].settingValue),
              label: data[i].description,
            };
            this.options.push(option);
          }
        }
      });
      // 是否显示新增、删除功能
      params = {
        settingTypeCode: "ViewDispable",
        typeValue: "SkinTestAdd",
      };
      await GetViewShow(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.canEdit = response.data;
        }
      });
      // 获取过敏药物类型集合
      await GetAllergyDrugList().then((response) => {
        if (this._common.isSuccess(response)) {
          this.allergyDrugList = response.data;
        }
      });
      params = {
        stationID: this.patient.stationID,
      };
      await GetNurse(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.nurseList = result.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.skin-test {
  height: 100%;
  width: 100%;
  .top-btn {
    float: right;
  }
  .skin-test-table {
    .allergy-basic,
    .drug-name {
      width: 100%;
    }
    .perform-datetime {
      width: 130px;
    }
  }
}
</style>