/*
 * FilePath     : \src\api\RecordQuality.js
 * Author       : 石高阳
 * Date         : 2020-10-14 11:11
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:48
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/RecordQuality";

export const urls = {
  GetSuggestProblem: baseUrl + "/GetSuggestProblem",
  ExportSuggestProblem: baseUrl + "/ExportSuggestProblem"
};

//取得建议护理问题
export const GetSuggestProblem = params => {
  return http.get(urls.GetSuggestProblem, params);
};

//导出建议护理问题
export const ExportSuggestProblem = params => {
  return http.post(urls.ExportSuggestProblem, params);
};
