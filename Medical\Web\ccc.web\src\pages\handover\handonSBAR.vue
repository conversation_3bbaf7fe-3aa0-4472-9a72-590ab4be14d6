<template>
  <base-layout class="handover" headerHeight="auto" v-loading="loading" element-loading-text="加载中……">
    <div slot="header">
      <span>班别开始日期：</span>
      <el-date-picker
        v-model="startDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        class="picker-date"
        :picker-options="pickerOptionscreate"
        laceholder="选择日期"
        style="width: 130px"
      ></el-date-picker>
      <span class="label">班别结束日期：</span>
      <el-date-picker
        v-model="endDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        :picker-options="pickerOptionsend"
        class="picker-date"
        laceholder="选择日期"
        style="width: 130px"
      ></el-date-picker>
      <span class="label">交接类别：</span>
      <el-select
        v-model="handoverType"
        @change="getHandOverType(undefined, handoverType, 'handoverTypeItems')"
        placeholder="请选择"
        class="type"
        style="width: 130px"
      >
        <el-option v-for="item in handoverTypes" :key="item.key" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <span class="label">交接子类：</span>
      <el-select v-model="handoverTypeItem" placeholder="请选择" class="type" style="width: 130px">
        <el-option
          v-for="item in handoverTypeItems"
          :key="item.key"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button class="query-button" icon="iconfont icon-search" @click="getHistory">查询</el-button>
    </div>
    <el-table :data="handoverData" border stripe height="100%" row-key="handoverID">
      <el-table-column
        :resizable="false"
        prop="handoverTypeDescription"
        label="交接类别"
        header-align="center"
      ></el-table-column>
      <el-table-column :resizable="false" prop="handoffStation" label="交班病区" align="center"></el-table-column>
      <el-table-column prop="handoffDepartment" label="交班科别" :resizable="false" align="center"></el-table-column>
      <el-table-column
        prop="handoffDate"
        label="评估日期"
        :resizable="false"
        width="110"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="handoffTime"
        label="评估时间"
        :resizable="false"
        width="100"
        align="center"
      ></el-table-column>
      <el-table-column prop="handoverShift" label="交班班别" :resizable="false" align="center"></el-table-column>
      <el-table-column prop="handoffNurse" label="交班护士" :resizable="false" header-align="center"></el-table-column>
      <el-table-column prop="handonStation" label="接班病区" :resizable="false" align="center"></el-table-column>
      <el-table-column prop="handonDepartment" label="接班科别" :resizable="false" align="center"></el-table-column>
      <el-table-column
        prop="handonDate"
        label="交接日期"
        :resizable="false"
        header-align="center"
        width="110"
      ></el-table-column>
      <el-table-column
        prop="handonTime"
        label="交接时间"
        :resizable="false"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column prop="handonNurse" label="接班护士" :resizable="false" header-align="center"></el-table-column>
      <el-table-column prop="progress" label="进度" align="center">
        <template slot-scope="scope">
          <img
            v-if="!scope.row.handonNurse"
            src="../../../static/progressHandoff.png"
            alt="已交班"
            class="handover-progress"
          />
          <img v-else src="../../../static/progressHandon.png" alt="已接班" class="handover-progress" />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-tooltip content="修改" v-if="!scope.row.handonNurse && user.userName == scope.row.handoffNurse">
            <i class="iconfont icon-edit save" @click="modify(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="预览">
            <i class="iconfont icon-more" @click="view(scope.row)"></i>
          </el-tooltip>
          <!-- 是转科交接，有交班时间且交班病区和接班人病区不一致时可显示接班符号 -->
          <el-tooltip
            v-if="
              (user.stationID != scope.row.stationID &&
                scope.row.handoffDate &&
                !scope.row.handonDate &&
                settingsTransferCategory.find((m) => m.value === scope.row.recordsCode)) ||
              (scope.row.handoffDate && !scope.row.handonDate && scope.row.recordsCode == 'WardHandover')
            "
            content="接班"
          >
            <i class="iconfont icon-handon" @click="handon(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import { GetClinicalBySettingTypeCode, GetSelectSetting } from "@/api/Setting";
import { GetPatientHandoverView, Handon } from "@/api/Handover";
import baseLayout from "@/components/BaseLayout";
//引用病人基本信息组件
import { mapGetters } from "vuex";
export default {
  props: {
    inpatientID: {
      type: String,
      required: true,
    },
    dischargedPreview: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  components: {
    baseLayout,
  },

  data() {
    let that = this;
    return {
      //交班汇整开始日期时间
      startDate: "",
      //交班汇整结束日期时间
      endDate: "",
      //交班内容
      handoverData: undefined,
      //交班类别
      handoverQuerType: [],
      //交班类别配置
      handoverTypes: [],
      handoverType: undefined,
      handoverTypeItems: [],
      handoverTypeItem: undefined,
      //科室转运交班类别明细
      settingsTransferCategory: undefined,
      //数据加载
      loading: false,
      pickerOptionscreate: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.endDate).getTime();
        },
      },
      pickerOptionsend: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.startDate).getTime() - 8.64e7;
        },
      },
    };
  },
  activated() {
    this.getHandOverType();
    this.getHistory();
    this.GetSettingTransferCategory();
  },
  watch: {
    inpatientID: {
      async handler() {
        this.init();
      },
      immediate: true,
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      //数据初始化
      this.startDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.endDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.handoverType = undefined;
      this.handoverTypeItems = [];
      //查询数据
      this.getHandOverType();
      this.getHistory();
      this.GetSettingTransferCategory();
    },
    async getHistory() {
      //查无病人数据
      // if (!this.patient) {
      //   return;
      // }
      let params = {
        inpatientID: this.inpatientID,
        startDate: this.startDate,
        endDate: this.endDate,
        handoverType: this.handoverType,
        recordsCode: this.handoverTypeItem,
      };
      this.loading = true;
      //数据清空
      this.handoverData = [];
      await GetPatientHandoverView(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result) && result.data) {
          result.data.forEach((row) => {
            row.handoffDate = this._datetimeUtil.formatDate(row.handoffDate, "yyyy-MM-dd");
            row.handoffTime = this._datetimeUtil.formatDate(row.handoffTime, "hh:mm");
            if (row.handonDate != null) {
              row.handonDate = this._datetimeUtil.formatDate(row.handonDate, "yyyy-MM-dd");
              row.handonTime = this._datetimeUtil.formatDate(row.handonTime, "hh:mm");
            }
            this.handoverData.push(row);
          });
        }
      });
    },
    /**
     * description: 获取交班类型
     * params {*}
     * return {*}
     * param {*} typeCode
     * param {*} typeValue
     * param {*} key
     */
    async getHandOverType(typeCode = "HandoverSetting", typeValue = "HandoverCategory", key = "handoverTypes") {
      this.handoverTypeItem = undefined;
      let params = {
        typeCode,
        typeValue,
        addDefaultFlag: true,
        index: Math.random(),
      };
      await GetSelectSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this[key] = res.data;
        }
      });
    },
    //取得科室转运交班类别明显
    GetSettingTransferCategory() {
      this.settingsTransferCategory = [];
      let params = {
        settingTypeCode: "TransferCategory",
        firstSpace: true,
        index: Math.random(),
      };
      this.loading = true;
      GetClinicalBySettingTypeCode(params).then((data) => {
        if (this._common.isSuccess(data)) {
          data.data.forEach((item) => {
            const rowItem = {
              key: item.clinicSettingID,
              value: item.settingValue,
              label: item.description,
            };
            this.settingsTransferCategory.push(rowItem);
          });
          this.loading = false;
        }
      });
    },
    modify(row) {
      //跳转SBAR修改画面
      this.$router.push({
        name: "handoverSBAR",
        query: {
          handoverID: row.handoverID,
          handoverType: row.handoverType ? row.handoverType : row.recordsCode,
          recordsCode: row.handoverType == "DischargeAssess" ? "DischargeAssess" : row.recordsCode,
          handoffDate: row.handoffDate,
          handoverShift: row.handoverShift,
          bedNumber: row.bedNumber,
          refresh: true,
        },
      });
    },
    view(row) {
      let url = {
        name: "handoverSBAR",
        query: {
          handoverID: row.handoverID,
          handoverType: row.handoverType ? row.handoverType : row.recordsCode,
          //出院小结默认为DischargeAssess
          recordsCode: row.handoverType == "DischargeAssess" ? "DischargeAssess" : row.recordsCode,
          handoffDate: row.handoffDate,
          handoverShift: row.handoverShift,
          disabled: true,
          bedNumber: row.bedNumber,
          refresh: true,
        },
      };

      if (this.dischargedPreview) {
        url.name = "handoverSBARLook";
      }
      this.$router.push(url);
    },
    handon(row) {
      this.loading = true;
      let params = {
        handoverID: row.handoverID,
      };
      Handon(params).then((data) => {
        if (this._common.isSuccess(data)) {
          this.loading = false;
          this.getHistory();
        }
      });
    },
  },
};
</script>
<style lang="scss">
.handover {
  // .picker-date {
  //   width: 120px;
  // }
  // .type {
  //   width: 160px;
  // }
  .type-select {
    width: 130px;
  }
  .label {
    margin-left: 10px;
  }
  .handover-progress {
    width: 55px;
    height: 16px;
  }
}
</style>
