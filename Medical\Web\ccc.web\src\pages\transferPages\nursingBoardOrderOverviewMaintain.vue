<!--
 * FilePath     : \src\pages\transferPages\nursingBoardStationOverviewMaintain.vue
 * Author       : 来江禹
 * Date         : 2024-05-03 15:20
 * LastEditors  : 来江禹
 * LastEditTime : 2024-05-08 09:09
 * Description  : 跳转看板病区一览维护页面
 * CodeIterationRecord:
 -->

<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getNursingBoard } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      language: "getLanguage",
      hospitalInfo: "getHospitalInfo",
      token: "getToken",
      user: "getUser",
    }),
  },
  created() {
    this.url =
      getNursingBoard() +
      "orderOverviewMaintain?hospitalID=" +
      this.hospitalInfo.hospitalID +
      "&language=" +
      this.language +
      "&token=" +
      this.token +
      "&stationID=" +
      this.user.stationID +
      "&userID=" +
      this.user.userID;
  },
};
</script>