/*
 * FilePath     : \src\api\PatientDelirium.js
 * Author       : 曹恩
 * Date         : 2021-10-30 15:17
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:46
 * Description  : 谵妄相关API
 */
import http from "../utils/ajax";
const baseUrl = "/PatientDelirium";

const urls = {
  GetDeliriumAssesssView: baseUrl + "/GetDeliriumAssesssView",
  GetDeliriumTableView: baseUrl + "/GetDeliriumTableView",
  SaveDeliriumCare: baseUrl + "/SaveDeliriumCare",
  DeleteDeliriumCare: baseUrl + "/DeleteDeliriumCare"
};

//获取评估模板
export const GetDeliriumAssesssView = params => {
  return http.get(urls.GetDeliriumAssesssView, params);
};
//获取谵妄评估记录
export const GetDeliriumTableView = params => {
  return http.get(urls.GetDeliriumTableView, params);
};
//保存谵妄评估
export const SaveDeliriumCare = params => {
  return http.post(urls.SaveDeliriumCare, params);
};
//删除谵妄评估
export const DeleteDeliriumCare = params => {
  return http.get(urls.DeleteDeliriumCare, params);
};
