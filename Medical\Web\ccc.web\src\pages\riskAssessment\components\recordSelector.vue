<!--
 * FilePath     : \src\pages\riskAssessment\components\recordSelector.vue
 * Author       : 苏军志
 * Date         : 2025-04-13 14:18
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-17 20:03
 * Description  : 
 * CodeIterationRecord: 
 -->

<template>
  <div class="record-selector">
    <el-radio-group class="record-type" v-model="recordType" @change="changeRecordType">
      <el-radio-button :label="0">全部</el-radio-button>
      <el-radio-button :label="1">风险</el-radio-button>
      <el-radio-button :label="2">非风险</el-radio-button>
    </el-radio-group>
    <span>表单：</span>
    <el-cascader
      class="record-list"
      popper-class="record-list-popper"
      v-model="recordListID"
      :show-all-levels="false"
      :options="recordList"
      :props="{
        value: 'recordListID',
        label: 'recordName',
        children: 'children',
        emitPath: false,
        expandTrigger: 'hover',
      }"
      clearable
      filterable
      placeholder="请选择"
      @change="change"
    ></el-cascader>
  </div>
</template>
<script>
import { GetRecordCascaderData } from "@/api/RecordsList";
export default {
  props: {
    value: {
      // 表单列表
      // type: Number,
      // default: "",
    },
    type: {},
    recordID: {},
    list: {},
  },
  data() {
    return {
      recordType: 0, // 0全部 1风险 2非风险
      recordListID: undefined, // 表单ID
      allRecordList: [], // 表单列表
      recordList: [], // 表单列表
    };
  },
  watch: {
    value: {
      handler(newVal) {
        this.recordListID = newVal;
      },
      immediate: true,
    },
    type: {
      handler(newVal) {
        this.recordType = newVal;
      },
      immediate: true,
    },
    recordID: {
      handler(newVal) {
        this.recordListID = newVal;
      },
      immediate: true,
    },
    list: {
      handler(newVal) {
        this.getRecordList();
      },
      immediate: true,
    },
  },
  created() {
    this.getRecordList(); // 获取表单列表
  },
  methods: {
    /**
     * @description: 获取表单列表
     */
    getRecordList() {
      if (this.list?.length) {
        this.recordList = this.list;
        this.allRecordList = this.list;
        this.fillterRecordList();
        return;
      }
      // 获取表单列表
      GetRecordCascaderData().then((res) => {
        if (this._common.isSuccess(res)) {
          this.recordList = res.data;
          this.allRecordList = res.data;
          this.fillterRecordList();
        }
      });
    },
    /**
     * @description: 表单类型切换
     */
    changeRecordType() {
      this.fillterRecordList();
      // 切换表单
      this.$emit("changeRecordType", this.recordType);
      this.recordListID = undefined;
    },
    /**
     * @description: 表单切换
     */
    change() {
      if (!this.recordListID) {
        this.$emit("change", undefined);
      }
      let selectRecord = undefined;
      for (const record of this.recordList) {
        if (record.children) {
          selectRecord = record.children.find((child) => child.recordListID == this.recordListID);
          if (selectRecord) {
            break;
          }
        }
      }
      this.$emit("change", selectRecord);
    },
    /**
     * @description: 过滤表单列表
     */
    fillterRecordList() {
      // 全部
      if (this.recordType == 0) {
        this.recordList = this.allRecordList;
        return;
      }
      this.recordList = this.allRecordList.reduce((result, record) => {
        if (!record.children) {
          return result;
        }
        const filteredChildren = record.children.filter((child) =>
          this.recordType == 1 ? child.recordType == "Risk" : child.recordType != "Risk"
        );
        if (filteredChildren.length) {
          result.push({
            ...record,
            children: filteredChildren,
          });
        }
        return result;
      }, []);
    },
  },
};
</script>
<style lang="scss">
.record-selector {
  display: inline-flex;
  align-items: center;
  margin: 0 10px;
  padding-top: -20px;
  box-sizing: border-box;
  .record-type {
    margin-right: 10px;
  }
  .record-list {
    width: 240px;
  }
}
.record-list-popper .el-cascader-menu__wrap {
  min-height: 280px;
}
</style>