<!--
 * FilePath     : /src/pages/recordSupplement/assessRecord/index.vue
 * Author       : 来江禹
 * Date         : 2023-08-29 11:30
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-05 16:51
 * Description  : 护理评估补录页面(在院和出院)
 * CodeIterationRecord:
-->

<template>
  <base-layout class="assess-supplement" v-loading="loading" :element-loading-text="loadingText" header-height="auto">
    <template slot="header">
      <el-button class="add-button" @click="addOrUpdate('add')" icon="iconfont icon-add">新增</el-button>
    </template>
    <el-table v-if="assessRecordList.length >= 0" :data="assessRecordList" height="100%" border>
      <el-table-column prop="deptName" label="科室" min-width="180" header-align="center"></el-table-column>
      <el-table-column prop="typeDes" label="名称" min-width="200" header-align="center">
        <template slot-scope="scope">
          <el-tooltip content="补录">
            <i :class="['iconfont', 'icon-info', { 'assess-show': !scope.row.additional }]"></i>
          </el-tooltip>
          {{ scope.row.typeDes }}
        </template>
      </el-table-column>
      <el-table-column prop="nursingLevelDescription" label="护理级别" width="80" align="center"></el-table-column>
      <el-table-column prop="sort" label="次数" width="50" align="center"></el-table-column>
      <el-table-column prop="addEmployeeName" label="评估人" min-width="70" align="center"></el-table-column>
      <el-table-column label="时间" min-width="155" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.assessDate, type: 'date' }"></span>
          <span v-formatTime="{ value: scope.row.assessTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" header-align="center">
        <template slot-scope="scope">
          <el-tooltip content="修改明细">
            <i class="iconfont icon-edit" @click="addOrUpdate('update', scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i v-if="scope.row.additional || scope.row.tempSaveMark == 'T'" class="iconfont icon-del" @click="deleteAssess(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      class="dialog"
      v-dialogDrag
      :close-on-click-modal="false"
      :title="titleText"
      :visible.sync="templateVisible"
      v-if="templateVisible"
      fullscreen
    >
      <div class="dialog-top">
        <station-department-bed-date
          class="dialog-top-components"
          @custCkick="selectStationDepartmentBed"
          :stDeptBed="stationDepartmentBed"
          :switch="componentsSwitch"
        ></station-department-bed-date>
        <label class="dialog-top-label">护理级别：</label>
        <el-select class="dialog-top-level" v-model="formData.nursingLevel" placeholder="请选择">
          <el-option
            v-for="item in nursingLevelList"
            :key="item.id"
            :label="item.description"
            :value="item.typeValue"
          ></el-option>
        </el-select>
        <label class="dialog-top-label">评估次数：</label>
        <el-input
          class="dialog-top-sort"
          v-model.number="formData.sort"
          @change="getAssessDetailView(inpatientInfo, 'add')"
          :disabled="sortFlag"
        ></el-input>
      </div>
      <div class="assess-view" v-loading="assessViewFlag" :element-loading-text="assessViewText">
        <tabs-layout
          ref="tabsLayout"
          :template-list="templateDatas"
          @button-click="buttonClick"
          @change-values="changeValues"
          @checkTN="checkTN"
        />
      </div>
      <div slot="footer">
        <el-button @click="templateVisible = false">取消</el-button>
        <el-button type="primary" @click="assessSave">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="titleText"
      :visible.sync="dischargeDialog"
      v-if="dischargeDialog"
      fullscreen
    >
      <assess-detail @saveEvent="saveEmitData" :dischargeAssessData="dischargeAssessData"></assess-detail>
    </el-dialog>
  </base-layout>
</template>
<script>
import tabsLayout from "@/components/tabsLayout/index.vue";
import baseLayout from "@/components/BaseLayout";
import stationDepartmentBedDate from "@/pages/recordSupplement/components/stationDepartmentBedDate.vue";
import assessDetail from "@/pages/recordSupplement/assessRecord/assessDetail.vue";
import { GetScheduleTop } from "@/api/Setting";
import { mapGetters } from "vuex";
import {
  GetAssessRecordsCodeByDeptID,
  GetAssessView,
  SaveAssessSupplement,
  DelRecordItemByID,
  GetAdditionalAssessMainInfo,
  GetPatientIsTransOut,
} from "@/api/Assess";
export default {
  components: { tabsLayout, baseLayout, stationDepartmentBedDate, assessDetail },
  data() {
    return {
      chartNo: undefined,
      //表格头部开关
      tableTopBoole: true,
      //暂存病区科室及床位
      stationDepartmentBed: {
        stationID: "",
        departmentListID: "",
        bedNumber: "",
        bedId: "",
      },
      //选中评估信息
      currentAssess: {},
      //评估列表信息
      assessRecordList: [],
      //评估模板数据
      templateDatas: [],
      //新增或修改弹窗数据
      templateVisible: false,
      titleText: "评估详细",
      loading: false,
      loadingText: "加载中……",
      //评估明细数据 修改评估时使用
      assessDatas: [],
      checkTNFlag: true,
      recordsInfo: {},
      nursingLevelList: [],
      formData: {
        sort: undefined,
        nursingLevel: "",
      },
      //评估模板loading
      assessViewFlag: false,
      assessViewText: "",
      //评估次数禁用flag
      sortFlag: false,
      //不符合补录条件提示内容
      supplementaryShow: "",
      inpatientInfo: "",
      //出院患者编辑弹出开关
      dischargeDialog: false,
      dischargeAssessData: {},
      // 设置病区科室床位日期组件是否显示
      componentsSwitch: {
        stationSwitch: true,
        departmentListSwitch: true,
        bedNumberSwitch: true,
        dateTimeSwitch: true,
      },
      //是否转科 是否有入院评估
      transOutData: undefined,
      addFlag: undefined,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  props: {
    patient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  watch: {
    "patient.inpatientID": {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        this.inpatientInfo = this.patient;
        this.init();
        this.getList();
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 获取护理等级列表
     * return {*}
     */
    init() {
      if (this.inpatientInfo) {
        this.chartNo = this.inpatientInfo.chartNo;
      }
      if (this.inpatientInfo?.dischargeDate) {
        this.tableTopBoole = false;
      }
      this.getNursingLevelList();
      this.getPatientIsTransOut();
    },
    //获取评估列表
    initData(patient) {
      if (!patient) {
        return;
      }
      this.tableTopBoole = true;
      this.getList(patient);
    },
    /**
     * description: 获取评估列表
     * return {*}
     */
    getList() {
      this.loading = true;
      let param = {
        inpatientID: this.inpatientInfo.inpatientID,
      };
      GetAdditionalAssessMainInfo(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.length == 0) {
            this.assessRecordList = [];
          }
          this.assessRecordList = response.data;
        }
        this.loading = false;
      });
    },
    /**
     * description: 重新搜索病人 质控页面数据
     * return {*}
     */
    change() {
      this.assessRecordList = [];
      this.tableTopBoole = false;
    },
    /**
     * description: 添加或修改病人评估
     * param {*} flag
     * param {*} inpatient
     * return {*}
     */
    async addOrUpdate(flag, inpatient = this.inpatientInfo) {
      if (this.inpatientInfo?.dischargeDate) {
        if (!this.inpatientInfo?.dischargeDateTimeView) {
          this._showTip("warning", "患者出院时间为空！");
          return false;
        }
        this.openDischargeDialog(flag, inpatient);
        return;
      }
      // 如果是修改操作，检查权限
      if (flag === "update") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          inpatient.addEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可修改");
          return;
        }
      }
      //置空模板数据
      this.templateDatas = [];
      this.recordsInfo = {};
      this.sortFlag = false;
      //顶部数据获取
      this.stationDepartmentBed.bedNumber = inpatient.bedNumber;
      this.stationDepartmentBed.departmentListID = inpatient.departmentListID;
      this.stationDepartmentBed.stationID = Number(inpatient.stationID);
      this.stationDepartmentBed.bedId = inpatient.bedID;
      this.formData.sort = undefined;
      //评估新增
      if (flag == "add") {
        this.titleText = this.getDialogTitle("评估新增");
        this.stationDepartmentBed.assessDate = this._datetimeUtil.getNowDate();
        this.stationDepartmentBed.assessTime = this._datetimeUtil.getNowTime();
        this.formData.nursingLevel = this.inpatientInfo.nursingLevel;
      }
      //评估修改
      if (flag == "update") {
        //修改评估禁止修改次数
        this.sortFlag = true;
        let assessType = inpatient.additional ? "补录" : "正常";
        this.titleText = this.getDialogTitle(`${assessType}评估修改`);
        this.stationDepartmentBed.assessTime = inpatient.assessTime;
        this.stationDepartmentBed.assessDate = inpatient.assessDate;
        this.formData.nursingLevel = inpatient.nursingLevel;
        this.formData.sort = inpatient.sort;
      }
      //保存选中评估信息 如果为新增则为currentPatient病人信息
      this.currentAssess = inpatient;
      this.templateVisible = true;
      this.getAssessDetailView(inpatient, flag);
    },
    /**
     * description: 获取评估模板
     * param {*} value
     * param {*} flag
     * return {*}
     */
    async getAssessDetailView(value, flag) {
      //新增评估时提醒输入评估次数
      if (!this.formData.sort) {
        this.$nextTick(() => {
          if (this.formData.sort != "") {
            this._showTip("warning", "请输入【评估次数】获取评估模板数据");
          }
        });
        return;
      }
      //新增第一次评估且患者有入院护评估
      if (flag == "add" && this.formData.sort == "1" && this.transOutData.admissionFlag) {
        this.formData.sort = undefined;
        this.templateDatas = [];
        this._showTip("warning", "患者已有入院评估");
        return;
      }
      // 新增检核评估次数
      if (flag == "add" && this.formData.sort > this.assessRecordList.length + 1) {
        this.formData.sort = undefined;
        this.templateDatas = [];
        this._showTip("warning", `患者评估最大次数不可以超过${this.assessRecordList.length + 1}次`);
        return;
      }
      this.assessViewFlag = true;
      this.assessViewText = "评估模板加载中……";
      let param = {
        age: this.inpatientInfo.age,
        inpatientID: value.inpatientID,
        gender: this.inpatientInfo.genderCode,
        departmentListID: this.stationDepartmentBed.departmentListID,
        stationID: this.stationDepartmentBed.stationID,
        dateOfBirth: this.inpatientInfo.dateOfBirth,
      };
      //新增评估获取模板数据传送数据整理
      if (flag == "add") {
        let params = {
          age: this.inpatientInfo.age,
          mappingType: this.formData.sort == 1 ? "AdmissionAssess" : "PhysicalAssess",
          inpatientID: value.inpatientID,
          departmentListID: this.stationDepartmentBed.departmentListID,
        };
        await GetAssessRecordsCodeByDeptID(params).then((response) => {
          if (this._common.isSuccess(response)) {
            this.recordsInfo = response.data;
          }
        });
        param.recordsCode = this.recordsInfo.recordsCode;
      }
      //修改评估获取模板传送数据处理
      if (flag == "update") {
        param.recordsCode = value.recordsCode;
        param.mainID = value.id;
      }
      await GetAssessView(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.templateDatas = response.data;
        }
      });
      this.assessViewFlag = false;
    },
    /**
     * description: 新增或修改保存评估
     * return {*}
     */
    assessSave() {
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      if (!this.checkSave()) {
        return;
      }
      //组装保存数据
      let assessDetail = this.getAssessSaveData();
      if (!assessDetail || assessDetail.length == 0) {
        return;
      }
      this.assessViewFlag = true;
      this.assessViewText = "保存中……";
      let assessData = {
        interventionMainID: this.recordsInfo.interventionMainID,
        details: assessDetail,
      };
      let mainData = this.currentAssess;
      let main = {
        id: mainData.id,
        inpatientID: mainData.inpatientID,
        patientID: mainData.patientID,
        caseNumber: mainData.caseNumber,
        chartNo: mainData.chartNo,
        sort: this.formData.sort,
        stationID: this.stationDepartmentBed.stationID,
        departmentListID: this.stationDepartmentBed.departmentListID,
        nursingLevel: this.formData.nursingLevel,
        recordsCode: this.recordsInfo.recordsCode || mainData.recordsCode,
        assessDate: this.stationDepartmentBed.assessDate,
        assessTime: this.stationDepartmentBed.assessTime,
        bedID: this.stationDepartmentBed.bedId,
        bedNumber: this.stationDepartmentBed.bedNumber,
        tempSaveMark: "S",
        deleteFlag: "",
        emrFlag: null,
      };
      //获取numberOfAssessment
      main.numberOfAssessment = this.assessRecordList.length ? this.getNumberOfAssessment(mainData) : 1;
      let mainArr = [];
      mainArr.push(main);
      assessData.mains = mainArr;
      SaveAssessSupplement(assessData).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data) {
            this.templateVisible = false;
            this._showTip("success", "保存成功！");
            this.initData(this.inpatientInfo);
          } else {
            this._showTip("error", "保存失败！");
          }
        }
        this.assessViewFlag = false;
      });
    },
    /**
     * description: 删除评估数据
     * param {*} row
     * return {*}
     */
    async deleteAssess(row) {
      let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
        this.user.userID,
        false,
        true,
        row.addEmployeeID
      );
      // 检查是否有权限删除
      if (!saveButtonFlag) {
        this._showTip("warning", "非本人或非护士长不可删除");
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          this.loading = true;
          this.loadingText = "删除中……";
          let params = {
            ID: row.id,
          };
          DelRecordItemByID(params).then((response) => {
            if (this._common.isSuccess(response)) {
              this._showTip("success", "删除成功！");
              this.getList(this.curr);
            }
            this.loading = false;
          });
        }
      });
    },
    /**
     * description: 获取护理级别列表
     * return {*}
     */
    getNursingLevelList() {
      let param = { settingTypeCode: "NursingLevel" };
      GetScheduleTop(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.nursingLevelList = response.data;
        }
      });
    },
    /**
     * description: 弹窗顶部组件数据
     * param {*} val
     * return {*}
     */
    selectStationDepartmentBed(val) {
      if (
        this.stationDepartmentBed.departmentListID != val.departmentListID ||
        this.stationDepartmentBed.stationID != Number(val.stationID)
      ) {
        this.templateDatas = [];
        this.formData.sort = undefined;
      }
      this.stationDepartmentBed.bedNumber = val.bedNumber;
      this.stationDepartmentBed.departmentListID = val.departmentListID;
      this.stationDepartmentBed.stationID = Number(val.stationID);
      this.stationDepartmentBed.bedId = val.bedId;
      this.stationDepartmentBed.assessDate = val.assessDate;
      this.stationDepartmentBed.assessTime = val.assessTime;
    },
    /**
     * description: 评估明细的中按钮点击
     * param {*} content
     * return {*}
     */
    buttonClick(content) {
      this._showTip("warning", `护理评估补录功能不支持维护【${content.itemName}】`);
    },
    /**
     * description:评估组件返回评估明细数据
     * param {*} datas
     * return {*}
     */
    changeValues(datas) {
      this.assessDatas = datas;
    },
    /**
     * description: 检核传参
     * param {*} flag
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 组装标题数据
     * param {*} detail
     * return {*}
     */
    getDialogTitle(detail) {
      return `${this.inpatientInfo.bedNumber}-${this.inpatientInfo.patientName}【${this.inpatientInfo.gender}-${this.inpatientInfo.age}】--${detail}`;
    },
    /**
     * description: 获取NumberOfAssessment
     * param {*} value
     * return {*}
     */
    getNumberOfAssessment(value) {
      let number =
        Math.max(
          ...this.assessRecordList.map((item) => {
            return item.numberOfAssessment;
          })
        ) + 1;
      return (number = value.id ? value.numberOfAssessment : number);
    },
    /**
     * description: 获取需要保存的明细
     * return {*}
     */
    getAssessSaveData() {
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return undefined;
      }
      let details = [];
      let flag = true;
      this.assessDatas.forEach((content) => {
        // 非隐藏项才进行检核
        if (content.disableGroup.indexOf("-1") === -1) {
          let result = this._common.checkAssessTN(content);
          if (!result.flag) {
            flag = false;
          }
        }
        let detail = {
          assessListID: content.assessListID,
          groupID: content.groupID,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        // 按钮不处理
        if (content.controlerType.trim() != "B") {
          details.push(detail);
        }
      });
      if (!flag) {
        return [];
      }
      return details;
    },
    /**
     * description: 检核保存日期
     * return {*}
     */
    checkSave() {
      if (!this.stationDepartmentBed.assessDate || !this.inpatientInfo.admissionDateTimeView) {
        return false;
      }
      let assessDate =
        this._datetimeUtil.formatDate(this.stationDepartmentBed.assessDate, "yyyy-MM-dd") +
        " " +
        this.stationDepartmentBed.assessTime;
      let patientAdmissionDate = this._datetimeUtil.formatDate(
        this.inpatientInfo.admissionDateTimeView,
        "yyyy-MM-dd hh:mm"
      );
      if (this._datetimeUtil.getTimeDifference(patientAdmissionDate, assessDate, undefined, "M") < 0) {
        this._showTip("warning", "护理评估时间不可以早于入院时间");
        return false;
      }
      return true;
    },
    /**
     * description: 获取病人那是否转科 是否有入院评估
     * return {*}
     */
    getPatientIsTransOut() {
      let params = {
        inpatientID: this.inpatientInfo.inpatientID,
      };
      GetPatientIsTransOut(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.transOutData = res.data;
        }
      });
    },
    /**
     * description: 出院补录子组件保存提交(关闭弹窗，重新获取数据)
     * param {*} val
     * return {*}
     */
    saveEmitData(val) {
      if (val) {
        this.dischargeDialog = false;
        this.initData(this.inpatientInfo);
      }
    },
    /**
     * description: 打开出院补录弹窗
     * return {*}
     */
    openDischargeDialog(flag, inpatient) {
      this.dischargeAssessData = {
        admissionDateTimeView: this.inpatientInfo?.admissionDateTimeView,
        dischargeDateTimeView: this.inpatientInfo?.dischargeDateTimeView,
        admissionFlag: this.transOutData?.admissionFlag ?? false,
      };
      if (flag == "update") {
        this.titleText = this.getDialogTitle("出院患者评估修改");
        this.dischargeDialog = true;
        this.dischargeAssessData.mainID = inpatient.id;
        this.dischargeAssessData.bedNumber = inpatient.bedNumber;
        this.dischargeAssessData.sort = inpatient.sort;
        this.dischargeAssessData.assessMain = inpatient;
        return;
      }
      if (flag == "add") {
        this.titleText = this.getDialogTitle("出院患者评估新增");
        this.dischargeDialog = true;
        this.dischargeAssessData.inpatientInfo = this.inpatientInfo;
        this.dischargeAssessData.numberOfAssessment = this.assessRecordList.length + 1;
        this.dischargeAssessData.userID = this.user.userID;
        return;
      }
    },
  },
};
</script>

<style lang='scss'>
.base-layout.assess-supplement {
  height: 100%;
  .base-header {
    padding: 8px 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .dialog {
    height: 100%;
    width: 100%;
    .dialog-top {
      margin-bottom: 10px;
      .form-bottom {
        height: 35px;
        line-height: 35px;
        padding-left: 60px;
        label {
          padding: 0 6px;
        }
      }
      .dialog-top-components {
        padding: 0px 5px 0px 5px;
      }
      .dialog-top-level {
        width: 110px;
      }
      .dialog-top-sort {
        width: 80px;
      }
      .dialog-top-label {
        padding: 0px 0px 0px 5px;
      }
    }
  }
  .assess-view {
    height: calc(100% - 50px);
  }
  .assess-show {
    visibility: hidden;
  }
  .div-span {
    margin-top: 10px;
    margin-bottom: 18px;
    .el-divider {
      margin: 0 8px;
      width: 5px;
      height: 1em;
      position: relative;
      vertical-align: middle;
      display: inline-block;
      background-color: #8cc63e;
    }
  }
  //限制el-from 的行间距 默认22px
  .el-form-item--small.el-form-item {
    margin-bottom: 8px;
  }

  //ElementUI未提供不显示提示的接口，使用CSS隐藏
  .el-form-item__error {
    display: none;
  }
  label.el-form-item__label::before {
    content: "" !important;
  }
}
</style>