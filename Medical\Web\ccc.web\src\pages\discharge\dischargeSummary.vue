<!--
 * FilePath     : \src\pages\discharge\dischargeSummary.vue
 * Author       : 李正元
 * Date         : 2020-05-06 16:11
 * LastEditors  : 苏军志
 * LastEditTime : 2022-10-26 17:47
 * Description  : 出院病情小结
 -->
<template>
  <base-layout class="summary">
    <div slot="header" class="top-btn">
      <el-button title="返回上一页" class="print-button" icon="iconfont icon-back" @click="$router.go(-1)">
        返回
      </el-button>
    </div>
    <div class="handover-content">
      <!-- <handover :componentData="{ handoverInfo: handoverInfo }" :risk="true" :humanPic="true" /> -->
      <handover :componentData="{ handoverInfo: handoverInfo }" :disabled="true" :risk="true" :humanPic="true" />
    </div>
    <el-table :data="summary.nursingPlanDocs" border stripe height="50%">
      <el-table-column prop="problem" label="护理问题" align="left"></el-table-column>
      <el-table-column prop="signs" label="定义特征" align="left"></el-table-column>
      <el-table-column prop="relatedFactor" label="相关因素" align="left"></el-table-column>
      <el-table-column prop="goals" label="预期目标" align="left"></el-table-column>
      <el-table-column prop="interventions" label="护理措施" align="left"></el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import Handover from "@/components/handoverSBAR";
import { GetDischargePlan } from "@/api/Document";
export default {
  components: { baseLayout, Handover },
  data() {
    return {
      //病人住院序号
      inpatientID: this.$route.query.inpatientID,
      //出院小结
      summary: [],
      //SBAR数据
      handoverInfo: {
        situation: "",
        background: "",
        recommendation: "",
        bodyPartImage: "",
      },
    };
  },
  mounted() {
    this.selectDischargePatient();
  },
  methods: {
    async selectDischargePatient() {
      let params = {
        inpatientID: this.inpatientID,
      };
      await GetDischargePlan(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (!response.data.handoverRecord) {
            this._showTip("error", "获取数据为空");
            return;
          }
          this.summary = response.data;
          this.handoverInfo.situation = this.summary.handoverRecord.situation;
          this.handoverInfo.background = this.summary.handoverRecord.background;
          this.handoverInfo.assement = this.summary.handoverRecord.assement;
          this.handoverInfo.recommendation = this.summary.handoverRecord.recommendation;
          this.handoverInfo.bodyPartImage = this.summary.handoverRecord.bodyPartImage;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.summary {
  .top-btn {
    float: right;
  }
  .handover-content {
    height: 50%;
  }
}
</style>
