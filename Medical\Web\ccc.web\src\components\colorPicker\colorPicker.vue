<!--
 * FilePath     : \src\components\colorPicker\colorPicker.vue
 * Author       : 苏军志
 * Date         : 2022-03-21 14:54
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-06-24 14:42
 * Description  : 颜色选择器组件
 * CodeIterationRecord: 2555-增加了平铺选项与样式 2022-04-19 杨欣欣
-->
<template>
  <div class="color-picker" :style="componentStyle">
    <div v-if="flat" class="flat">
      <span class="color-label" v-if="label">{{ label }}</span>
      <div>
        <div
          v-for="(color, index) in colorArray"
          :key="index"
          :class="['color', { selected: value == color.color }]"
          @click="changeColor(color, value)"
        >
          <div class="color-block" :style="{ 'background-color': color.color }"></div>
          <div class="color-name">{{ color.colorName }}</div>
        </div>
      </div>
    </div>
    <div v-else class="no-flat">
      <div class="show">
        {{ label }}
        <div class="show-color" @click="showCheck" :style="style"></div>
        <div class="show-name">
          <span>{{ colorName }}</span>
        </div>
      </div>
      <div class="container" ref="container" v-if="checkController">
        <div class="colors-area">
          <div class="item" @click="changeColor(item)" v-for="(item, index) in colorArray" :key="index">
            <el-tooltip :content="item.colorName">
              <div class="color-area" :style="{ backgroundColor: item.color }"></div>
            </el-tooltip>
            <div class="word">
              {{ item.colorName }}
            </div>
          </div>
        </div>
        <div class="container-footer">
          <span class="clear" @click="clearBGI">清除</span>
          <el-button type="primary" @click="checkController = false">关闭</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    colorArray: {
      default: () => {
        return [];
      },
      type: Array,
    },
    value: {
      default: "",
      type: String,
    },
    width: {
      type: String,
      default: "150px",
    },
    height: {
      type: String,
      default: "24px",
    },
    label: {
      type: String,
      default: "",
    },
    flat: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    colorArray: {
      handler(newV) {
        if (newV.length > 0 && this.value) {
          this.asyncColorName();
        } else if (!newV.length) {
          this.checkController = false;
          this.colorName = "";
        }
      },
      immediate: true,
    },
    value: {
      handler(newV) {
        if (!newV) {
          this.clearBGI();
        } else {
          this.onColor = newV;
          this.backgroundImage = "none";
          this.asyncColorName();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      onColor: this.value,
      colorName: "",
      //显示校验,为false表示此项目没有待选颜色
      checkController: false,
      backgroundImage:
        "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAAAAAByaaZbAAAACXBIWXMAAAsTAAALEwEAmpwYAAADGWlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjaY2BgnuDo4uTKJMDAUFBUUuQe5BgZERmlwH6egY2BmYGBgYGBITG5uMAxIMCHgYGBIS8/L5UBA3y7xsDIwMDAcFnX0cXJlYE0wJpcUFTCwMBwgIGBwSgltTiZgYHhCwMDQ3p5SUEJAwNjDAMDg0hSdkEJAwNjAQMDg0h2SJAzAwNjCwMDE09JakUJAwMDg3N+QWVRZnpGiYKhpaWlgmNKflKqQnBlcUlqbrGCZ15yflFBflFiSWoKAwMD1A4GBgYGXpf8EgX3xMw8BUNTVQYqg4jIKAX08EGIIUByaVEZhMXIwMDAIMCgxeDHUMmwiuEBozRjFOM8xqdMhkwNTJeYNZgbme+y2LDMY2VmzWa9yubEtoldhX0mhwBHJycrZzMXM1cbNzf3RB4pnqW8xryH+IL5nvFXCwgJrBZ0E3wk1CisKHxYJF2UV3SrWJw4p/hWiRRJYcmjUhXSutJPZObIhsoJyp2V71HwUeRVvKA0RTlKRUnltepWtUZ1Pw1Zjbea+7QmaqfqWOsK6b7SO6I/36DGMMrI0ljS+LfJPdPDZivM+y0qLBOtfKwtbFRtRexY7L7aP3e47XjB6ZjzXpetruvdVrov9VjkudBrgfdCn8W+y/xW+a8P2Bq4N+hY8PmQW6HPwr5EMEUKRilFG8e4xUbF5cW3JMxO3Jx0Nvl5KlOaXLpNRlRmVdas7D059/KY8tULfAqLi2YXHy55WyZR7lJRWDmv6mz131q9uvj6SQ3HGn83G7Skt85ru94h2Ond1d59uJehz76/bsK+if8nO05pnXpiOu+M4JmzZj2aozW3ZN6+BVwLwxYtXvxxqcOyCcsfrjRe1br65lrddU3rb2402NSx+cFWq21Tt3/Y6btr1R6Oven7jh9QP9h56PURv6Obj4ufqD355LT3mS3nZM+3X/h0Ke7yqasW15bdEL3ZeuvrnfS7N+/7PDjwyPTx6qeKz2a+EHzZ9Zr5Td3bn+9LP3z6VPD53de8b+9+5P/88Lv4z7d/Vf//AwAqvx2K829RWwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAU0lEQVR42uyToREAIRAD955vh/6beEcNDFXwYEAhDjSJWxGTzNoHQIgBgJqqww+HubJgefR+ABrmcdvcf7J+OPhhY//Bb5EP8kE+yAf5IB9W7gMAATVfU05rRlAAAAAASUVORK5CYII=)",
    };
  },
  computed: {
    style() {
      return {
        backgroundColor: this.onColor,
        backgroundImage: this.backgroundImage,
      };
    },
    componentStyle() {
      return {
        width: this._convertUtil.getHeigt(this.width, true),
        height: this._convertUtil.getHeigt(this.height, true),
      };
    },
  },
  methods: {
    /**
     * description: 颜色切换/选中
     * params {colorItem} 颜色集合中的颜色对象
     * params {selectedColor} 选中的颜色值
     * return {*}
     */
    changeColor(colorItem, selectedColor = undefined) {
      if (this.flat) {
        if (selectedColor && colorItem.color == selectedColor) {
          //已选中则去除选中
          this.$emit("input", "");
          this.$emit("change", undefined);
        } else {
          this.$emit("input", colorItem.color);
          this.$emit("change", colorItem);
        }
      } else {
        this.backgroundImage = "none";
        this.onColor = colorItem.color;
        this.colorName = colorItem.colorName;
        this.$emit("input", colorItem.color);
        this.$emit("change", colorItem);
        this.checkController = false;
      }
    },
    showCheck() {
      if (!this.colorArray.length) {
        this._showTip("warning", "无待选颜色！");
        return;
      }
      this.checkController = !this.checkController;
      this.adjustDomPosition();
    },
    /**
     * description: 清除背景图
     * params {*}
     * return {*}
     */
    clearBGI() {
      //设置透明
      this.backgroundImage =
        "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAAAAAByaaZbAAAACXBIWXMAAAsTAAALEwEAmpwYAAADGWlDQ1BQaG90b3Nob3AgSUNDIHByb2ZpbGUAAHjaY2BgnuDo4uTKJMDAUFBUUuQe5BgZERmlwH6egY2BmYGBgYGBITG5uMAxIMCHgYGBIS8/L5UBA3y7xsDIwMDAcFnX0cXJlYE0wJpcUFTCwMBwgIGBwSgltTiZgYHhCwMDQ3p5SUEJAwNjDAMDg0hSdkEJAwNjAQMDg0h2SJAzAwNjCwMDE09JakUJAwMDg3N+QWVRZnpGiYKhpaWlgmNKflKqQnBlcUlqbrGCZ15yflFBflFiSWoKAwMD1A4GBgYGXpf8EgX3xMw8BUNTVQYqg4jIKAX08EGIIUByaVEZhMXIwMDAIMCgxeDHUMmwiuEBozRjFOM8xqdMhkwNTJeYNZgbme+y2LDMY2VmzWa9yubEtoldhX0mhwBHJycrZzMXM1cbNzf3RB4pnqW8xryH+IL5nvFXCwgJrBZ0E3wk1CisKHxYJF2UV3SrWJw4p/hWiRRJYcmjUhXSutJPZObIhsoJyp2V71HwUeRVvKA0RTlKRUnltepWtUZ1Pw1Zjbea+7QmaqfqWOsK6b7SO6I/36DGMMrI0ljS+LfJPdPDZivM+y0qLBOtfKwtbFRtRexY7L7aP3e47XjB6ZjzXpetruvdVrov9VjkudBrgfdCn8W+y/xW+a8P2Bq4N+hY8PmQW6HPwr5EMEUKRilFG8e4xUbF5cW3JMxO3Jx0Nvl5KlOaXLpNRlRmVdas7D059/KY8tULfAqLi2YXHy55WyZR7lJRWDmv6mz131q9uvj6SQ3HGn83G7Skt85ru94h2Ond1d59uJehz76/bsK+if8nO05pnXpiOu+M4JmzZj2aozW3ZN6+BVwLwxYtXvxxqcOyCcsfrjRe1br65lrddU3rb2402NSx+cFWq21Tt3/Y6btr1R6Oven7jh9QP9h56PURv6Obj4ufqD355LT3mS3nZM+3X/h0Ke7yqasW15bdEL3ZeuvrnfS7N+/7PDjwyPTx6qeKz2a+EHzZ9Zr5Td3bn+9LP3z6VPD53de8b+9+5P/88Lv4z7d/Vf//AwAqvx2K829RWwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAU0lEQVR42uyToREAIRAD955vh/6beEcNDFXwYEAhDjSJWxGTzNoHQIgBgJqqww+HubJgefR+ABrmcdvcf7J+OPhhY//Bb5EP8kE+yAf5IB9W7gMAATVfU05rRlAAAAAASUVORK5CYII=)";
      this.color = "";
      this.colorName = "";
      this.checkController = false;
      this.$emit("input", "");
      this.$emit("change", undefined);
    },
    asyncColorName() {
      this.colorArray.forEach((item) => {
        if (item.color == this.onColor) {
          this.colorName = item.colorName;
        }
      });
    },
    /**
     * @description: 越界判断
     * @return
     */
    adjustDomPosition() {
      this.$nextTick(() => {
        const dom = this.$refs.container;
        //越界方向判断
        let adjustPosition = this._common.domAdjustPosition(dom);
        if (!adjustPosition) {
          return;
        }
        //越界处理
        const rect = dom.getBoundingClientRect();
        switch (adjustPosition) {
          case "bottom":
            dom.style.top = `${-rect.height}px`;
            break;
          case "top":
            dom.style.bottom = `${-rect.height}px`;
            break;
          case "left":
            dom.style.left = "0px";
            break;
          case "right":
            dom.style.right = "0px";
            break;
          default:
            break;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.color-picker {
  height: 25px;
  line-height: 25px;
  display: inherit;
  > div {
    height: 100%;
  }
  .flat {
    display: inherit;
    .color {
      display: inline-block;
      cursor: pointer;
      margin: 0 0 5px 10px;
      border: 1px solid #ebeef5;
      box-sizing: border-box;
    }
    .color.selected {
      border: 1px solid #ff7400;
    }
    .color .color-block {
      width: 45px;
      height: 16px;
      border-bottom: 1px solid #ebeef5;
      box-sizing: border-box;
    }
    .color .color-name {
      width: 45px;
      text-align: center;
      font-size: 13px;
      margin-top: -2px;
    }
  }
  .no-flat {
    display: inline-block;
    width: 100%;
  }
  .show {
    display: flex;
    width: 100%;
    height: 100%;
    .show-color {
      flex: auto;
      border: 1px solid #c0c4cc;
      cursor: pointer;
    }
    .show-name {
      text-align: right;
      padding-right: 5px;
      font-weight: normal !important;
    }
  }
  .container {
    z-index: 9999;
    margin-top: 2px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    background-color: #fff;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    position: absolute;
    width: 300px;
    padding: 5px;
    .colors-area {
      height: 100px;
      overflow-y: auto;
      width: 100%;
      .item {
        cursor: pointer;
        margin: 2px;
        box-sizing: border-box;
        width: 60px;
        float: left;
        text-align: center;
        .color-area {
          border: 1px solid #e4e7ed;
          height: 24px;
          width: 24px;
          border-radius: 3px;
          box-sizing: border-box;
          margin: auto;
          text-align: center;
        }
        .word {
          box-sizing: border-box;
          margin-top: -3px;
          font-size: 10px;
          text-align: center;
          width: 100%;
          font-weight: normal !important;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    .container-footer {
      position: relative;
      box-sizing: border-box;
      width: 100%;
      height: 32px;
      line-height: 32px;
      text-align: right;
      .clear {
        font-size: 12px;
        color: $base-color;
      }
      .el-button {
        box-sizing: border-box;
        padding: 5px;
      }
    }
  }
}
</style>
