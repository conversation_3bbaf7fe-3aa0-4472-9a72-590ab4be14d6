<!--
 * FilePath     : \src\autoPages\handover\handoverReport\dailyHandover.vue
 * Author       : 郭鹏超
 * Date         : 2020-05-18 16:07
 * LastEditors  : 来江禹
 * LastEditTime : 2024-06-03 10:53
 * Description  :  每日交班
-->
<template>
  <base-layout class="daily-handover">
    <div class="daily-log-top" slot="header">
      <label>班别日期:</label>
      <el-date-picker
        v-model="handoverDate"
        :clearable="false"
        value-format="yyyy-MM-dd"
        type="date"
        laceholder="选择日期"
        class="date-picker"
        :picker-options="checkDate"
      ></el-date-picker>
    </div>
    <div class="daily-log-content">
      <el-table class="top-table" border :data="topTableData">
        <el-table-column
          v-for="(prop, index) in topTableHerderArr"
          :key="index"
          :prop="prop"
          :label="topTableHerderData[prop].numberOrLabel"
          align="center"
        >
          <template slot="header" slot-scope="scope">
            <el-tooltip
              :id="scope.$index"
              :disabled="!topTableHerderData[prop].description"
              placement="top"
              :content="topTableHerderData[prop].description"
            >
              <div>{{ topTableHerderData[prop].numberOrLabel }}</div>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <div
              :class="{
                'is-select': cellIndex == index + '||' + scope.$index,
              }"
              @click="
                index > 0 &&
                  getHandOverDetailData(
                    prop,
                    scope.row[prop],
                    scope.row.prop_shift.recordsCode,
                    index + '||' + scope.$index
                  )
              "
            >
              {{ scope.row[prop].numberOrLabel }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="content-table">
        <el-table stripe highlight-current-row height="100%" :data="detailTableData" border>
          <el-table-column align="center" min-width="72%" label="交班内容">
            <template slot-scope="scope">
              <el-row type="flex" align="middle">
                <el-col :span="4">I-介绍</el-col>
                <el-col v-html="scope.row.inturduction" :span="20"></el-col>
              </el-row>
              <el-row>
                <el-col :span="4" class="col-body">
                  S-状况
                  <body-image
                    v-if="scope.row.handoverID"
                    :type="'tooltip'"
                    :handoverID="scope.row.handoverID"
                    :patientName="scope.row.patientName"
                  ></body-image>
                </el-col>
                <el-col v-html="scope.row.situation" :span="20"></el-col>
              </el-row>
              <el-row>
                <el-col :span="4">B-背景</el-col>
                <el-col v-html="scope.row.background" :span="20"></el-col>
              </el-row>
              <el-row>
                <el-col :span="4">A-评估</el-col>
                <el-col v-html="scope.row.assement" :span="20"></el-col>
              </el-row>
              <el-row>
                <el-col :span="4">R-建议</el-col>
                <el-col v-html="scope.row.recommendation" :span="20"></el-col>
              </el-row>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </base-layout>
</template>

<script>
import { mapGetters } from "vuex";
import { GetDailyHandoverTitle, GetDailyHandoverSBAR } from "@/api/HandoverReport";
import baseLayout from "@/components/BaseLayout";
import bodyImage from "@/components/bodyImage";
export default {
  components: {
    baseLayout,
    bodyImage,
  },
  data() {
    return {
      handoverDate: "",
      topTableHerderData: {},
      topTableHerderArr: [],
      topTableData: [],
      detailTableData: [],
      cellIndex: "", //控制单元格背景图片
      // 日期不得大于当前日期
      checkDate: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  watch: {
    // 监听日期
    handoverDate() {
      this.getStatisticsData();
      // 置空单元格背景颜色和下边表格数据
      this.cellIndex = "";
      this.detailTableData = [];
    },
  },
  mounted() {
    this.handoverDate = this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -1, "yyyy-MM-dd");
  },
  methods: {
    /**
     * description:  获取顶部表格数据
     * return {*}
     */
    getStatisticsData() {
      let params = {
        shiftDate: this.handoverDate,
        stationID: this.user.stationID,
      };
      GetDailyHandoverTitle(params).then((res) => {
        if (this._common.isSuccess(res) && res.data && res.data.length) {
          //表头
          this.topTableHerderData = res.data[0];
          this.topTableHerderArr = Object.keys(this.topTableHerderData);
          //表格数据
          [this.topTableHerderData, ...this.topTableData] = res.data;
        }
      });
    },
    /**
     * description: 获取下边详细内容数据
     * param {*} prop
     * param {*} item
     * param {*} shift
     * param {*} index
     * return {*}
     */
    getHandOverDetailData(prop, item, shift, index) {
      this.cellIndex = index;
      if (!item || !item.inpatientIDArr) {
        return;
      }
      let params = {
        inpatientIDArr: item.inpatientIDArr.split("||"),
        recordsCode: item.recordsCode,
        shiftDate: this.handoverDate,
        shift: shift,
        signID: prop.split("_")[1],
      };
      GetDailyHandoverSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.detailTableData = res.data;
          this.detailTableData.map((item) => {
            this.$set(item, "bodyPartImage", "data:image/png;base64," + item.bodyPartImage);
            this.$set(item, "inturduction", item.inturduction);
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.daily-handover {
  $borderColor: #ebeef5;
  $backgroundColor: #f3f3f3;
  height: 100%;
  .daily-log-top {
    label {
      margin-left: 10px;
    }
    .date-picker {
      width: 160px;
    }
    .icon-info {
      cursor: pointer;
      font-size: 18px;
      margin-left: 20px;
    }
  }
  .daily-log-content {
    height: 100%;
    .top-table .el-table__body .cell {
      height: 38px;
      line-height: 38px;
      cursor: pointer;
      div {
        height: 100%;
      }
      // 点击单元格添加背景
      .is-select {
        background-image: url("../../../../static/images/text-select.png");
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 40px;
      }
    }
    .content-table {
      height: calc(100% - 200px);

      td {
        padding-left: 0px !important;
        padding-right: 0px !important;
        padding-bottom: 0px !important;
      }
      .cell {
        padding-left: 0px !important;
        padding-right: 0px !important;
      }
      .el-col {
        height: 100%;
        text-align: center;
        padding: 6px 8px;
      }
      .col-body {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .current-row {
        .el-row {
          background-color: #e6faf5 !important;
        }
      }
      .el-row {
        font-size: 15px;
        border: 1px solid $borderColor;
        border-top: none;
        border-left: none;
        border-right: none;
        .el-col:first-child {
          color: #fc6a23;
          font-weight: 600;
        }
        &:last-child {
          border-bottom: none;
        }
        .el-col:nth-of-type(2) {
          text-align: left;
          height: auto;
          min-height: 30px;
          border-left: 0.5px solid $borderColor;
        }
      }
    }
  }
  .daily-handover-img {
    width: 100%;
  }
}
</style>
