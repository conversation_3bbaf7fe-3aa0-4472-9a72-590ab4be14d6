<!--
 * FilePath     : \ccc.web\src\pages\transferPages\qcFaultStatistics.vue
 * Author       : 李艳奇
 * Date         : 2021-04-09 09:06
 * LastEditors  : 李艳奇
 * LastEditTime : 2021-04-20 09:17
 * Description  : 
-->

<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getStatisticsUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  created() {
    this.url =
      getStatisticsUrl() + "qcFaultStatistics?token=" + this.token + "&hospitalID=" + this.hospitalInfo.hospitalID;
  },
};
</script>
<style lang="scss">
.transfer-statistics {
  height: 100%;
  width: 100%;
}
</style>