<!--
 * FilePath     : \src\pages\transferHisCommon\transferPatientAllergicDrug.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 苏军志
 * LastEditTime : 2020-09-10 15:38
 * Description  : 串药物过敏页面
--> 
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { hisHisCommonUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      user: "getUser",
      patientInfo: "getPatientInfo",
    }),
  },
  watch: {
    patientInfo(newPatient) {
      if (newPatient) {
        this.init();
      } else {
        this.url = "";
      }
    },
  },
  created() {
    // 设置可切换病人
    this._sendBroadcast("setPatientSwitch", true);
    this.init();
  },
  methods: {
    init() {
      this.url =
        hisHisCommonUrl() +
        "patientAllergicDrug?chartNo=" +
        this.patientInfo.chartNo +
        "&userID=" +
        this.user.userID +
        "&token=" +
        this.token;
    },
  },
};
</script>
