<!--
 * FilePath     : \ccc.web\src\pages\handover\handoverPerioperative.vue
 * Author       : 孟昭永
 * Date         : 2020-11-18 11:21
 * LastEditors  : 陈超然
 * LastEditTime : 2024-09-29 09:40
 * Description  : 手术转运交班,增加获取手术ID
-->
<template>
  <base-layout class="handover-perioperative">
    <div slot="header">
      <!-- <el-button class="add-button" icon="iconfont icon-add" @click="addHandover">新增</el-button> -->
    </div>
    <el-table :data="tableData" border height="100%" width="100%" :span-method="objectSpanMethod">
      <el-table-column prop="scheduleDateTime" label="预约日期" align="center" width="105">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.scheduleDateTime, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="operateName" label="手术名称" header-align="center"></el-table-column>
      <el-table-column prop="anesthesiaMethod" label="麻醉方式" header-align="center" width="115"></el-table-column>
      <el-table-column prop="handoverTypeName" label="交班类型" align="center" width="80"></el-table-column>
      <el-table-column prop="handoffDateTime" label="评估时间" align="center" width="155">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.handoffDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="handoffNurse" label="交班人" align="center" width="80"></el-table-column>
      <el-table-column prop="handonDateTime" label="交接时间" align="center" width="155">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.handonDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="handonNurse" label="接班人" align="center" width="80"></el-table-column>
      <el-table-column prop="data10" label="交接班" align="center" width="80">
        <template slot-scope="scope">
          <el-tooltip content="交班">
            <i class="iconfont icon-edit" @click="operationHandover(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteHandover(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="data11" label="操作" align="center" width="60">
        <template slot-scope="scope">
          <el-tooltip content="新增交接">
            <i class="iconfont icon-handoff" @click="addHandover(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetOperationHandoverList, AddTransferOPHandover } from "@/api/PatientPerioperative";
import { DeleteHandOverByHandOverID } from "@/api/Handover";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      tableData: [],
      handoverRadio: true,
    };
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
      user: "getUser",
    }),
  },
  watch: {
    inpatient(newVal) {
      if (!newVal) return;
      this.init();
    },
  },
  created() {
    // 设置可切换病人
    this._sendBroadcast("setPatientSwitch", true);
    if (this.inpatient) {
      this.init();
    }
  },
  methods: {
    init() {
      let params = {};
      params.inpatientID = this.inpatient.inpatientID;
      GetOperationHandoverList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.tableData = result.data;
        }
      });
    },
    operationHandover(row) {
      this.$router.push({
        path: "handoverSBAR",
        query: {
          handoverType: row.handoverType,
          recordsCode: row.recordsCode,
          handoverID: row.handoverID,
          hisOperationNo: row.hisOperationNo,
          patientOperationID: row.patientOperationID,
          //多次转运无法切换类型
          typeDisabled: row.handoverType == "PreOPHandover" && row.rowSpan == 0 ? true : false,
        },
      });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 9) {
        if (row.rowSpan) {
          return {
            rowspan: row.rowSpan,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    addHandover(row) {
      this.$confirm("此操作将新增交班数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          //取得总笔数
          let opData = [];
          this.tableData.forEach((item) => {
            if (item.hisOperationNo === row.hisOperationNo) {
              opData.push(item);
            }
          });
          // let count = opData.length - 1;
          // if (opData[count].handoffNurse != null) {
          //   this._showTip("warning", "已产生术后交接不得再新增转运交班");
          //   return;
          // }
          let params = {
            sourceID: row.hisOperationNo,
            inpatientID: row.inpatientID,
          };
          AddTransferOPHandover(params).then((result) => {
            if (this._common.isSuccess(result)) {
              if (result.message != "") {
                this._showTip("warning", result.message);
              }
              this.init();
            } else {
              this._showTip("warning", result.message);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "warning",
            message: "取消新增",
            offset: 200,
          });
        });
    },
    deleteHandover(row) {
      if (!row.handoverID) {
        this._showTip("warning", "不能删除空数据");
        return;
      }
      if (row.handonDateTime) {
        this._showTip("warning", "不能删除已接班数据，可以前往补录页面调整！");
        return;
      }
      if (row.handoffNurseID != this.user.userID) {
        this._showTip("warning", "不能删除非本人操作的数据");
        return;
      }
      let params = {
        handOverID: row.handoverID,
      };
      this._deleteConfirm("", (flag) => {
        if (flag) {
          DeleteHandOverByHandOverID(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this.init();
              this._showTip("success", "删除成功");
            }
          });
        }
      });
    },
  },
};
</script>

<style  lang="scss">
.handover-perioperative {
  .add-button {
    float: right;
    margin-right: 10px;
    margin-top: 8px;
  }
}
</style>