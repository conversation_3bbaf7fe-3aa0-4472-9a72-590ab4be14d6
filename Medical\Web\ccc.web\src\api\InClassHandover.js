/*
 * FilePath     : \src\api\InClassHandover.js
 * Author       : 李正元
 * Date         : 2020-06-22 20:35
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:45
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/InClassHandover";

export const urls = {
  InClassHandoff: baseUrl + "/InClassHandoff",
  ReSummaryInClassHandoff: baseUrl + "/ReSummaryInClassHandoff",
  GetInclassHandoff: baseUrl + "/GetInclassHandoff",
  GetInclassHandon: baseUrl + "/GetInclassHandon",
  DeleteByID: baseUrl + "/DeleteByID",
  UpdateInClassShiftHandoff: baseUrl + "/UpdateInClassShiftHandoff",
  InClassHandon: baseUrl + "/InClassHandon",
  RestoreDelete: baseUrl + "/RestoreDelete"
};

//班内交班
export const InClassHandoff = params => {
  return http.post(urls.InClassHandoff, qs.stringify(params));
};

//重新汇整班内交班内容
export const ReSummaryInClassHandoff = params => {
  return http.post(urls.ReSummaryInClassHandoff, qs.stringify(params));
};

//取得护士照顾的病人
export const GetInclassHandoff = params => {
  return http.get(urls.GetInclassHandoff, params);
};

//班内交班
export const GetInclassHandon = params => {
  return http.get(urls.GetInclassHandon, params);
};

//透过序号删除交班记录
export const DeleteByID = params => {
  return http.post(urls.DeleteByID, qs.stringify(params));
};

//更新班内交班内容
export const UpdateInClassShiftHandoff = params => {
  return http.post(urls.UpdateInClassShiftHandoff, qs.stringify(params));
};

//重新汇整班内交班内容
export const InClassHandon = params => {
  return http.post(urls.InClassHandon, params);
};

//还原删除
export const RestoreDelete = params => {
  return http.post(urls.RestoreDelete, qs.stringify(params));
};
