<!--
 * FilePath     : \src\components\RichText.vue
 * Author       : 郭鹏超
 * Date         : 2020-04-15 16:41
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-30 09:29
 * Description  : 
 -->
<template>
  <div
    :class="outlineBoole ? 'richText-body-focus' : ''"
    :style="{ height: size.height, width: size.width }"
    class="richText-body"
  >
    <span class="richText-label" v-if="label">{{ label }}</span>
    <editor
      @click.native="inputFocus()"
      @blur.native="outlineBoole = false"
      @keyup.native="getInputnumber($event)"
      :disabled="inputDisabled || disabled"
      class="richText-input"
      boder
      v-model="inputData"
      :init="tinyceInit"
    ></editor>
    <span :style="{ color: wordColorBoole ? '#f56c6c' : '' }" class="words-number">
      {{ wordsLength + (wordNumber == 0 ? "" : "/" + fixWordNumber) + "字" }}
    </span>
  </div>
</template>

<script>
import tinymce from "tinymce/tinymce";
import "tinymce/themes/modern/theme";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/plugins/image";
import "tinymce/plugins/link";
import "tinymce/plugins/code";
import "tinymce/plugins/table";
import "tinymce/plugins/lists";
import "tinymce/plugins/contextmenu";
import "tinymce/plugins/wordcount";
import "tinymce/plugins/colorpicker";
import "tinymce/plugins/textcolor";
import "tinymce/plugins/paste";
export default {
  props: {
    label: {
      type: String,
      default: "",
    },
    value: {
      type: String,
      required: true,
    },
    size: {
      type: Object,
      default: () => {
        return { height: "150px", width: "90%" };
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    wordNumber: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    fixWordNumber() {
      if (!this.wordNumber) {
        return 0;
      }
      return parseInt((this.wordNumber - 200) / 2);
    },
  },
  components: {
    Editor,
  },
  data() {
    return {
      //文本框绑定值
      inputData: "",
      //控制聚焦边框线
      outlineBoole: false,
      //文本框字数
      wordsLength: 0,
      inputDisabled: "",
      wordColorBoole: false,
      //富文本工具栏
      tinyceInit: {
        language_url: "/static/tinymce/zh_CN.js",
        language: "zh_CN",
        skin_url: "/static/tinymce/skins/lightgray",
        height: 300,
        plugins: "link lists code table colorpicker textcolor wordcount contextmenu paste",
        toolbar:
          "bold italic underline strikethrough | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist  | undo redo | removeformat",
        branding: false,
        inline: true,
        menubar: false,
        exhibition: false,
        br_in_pre: true,
        paste_data_images: true,
        paste_as_text: true,
        paste_enable_default_filters: false,
        paste_word_valid_elements:
          "table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody",
      },
    };
  },
  watch: {
    inputData: {
      handler(newValue) {
        this.$emit("input", this.inputData);
      },
      immediate: true,
    },
    value: {
      handler(newValue) {
        if (!newValue) {
          this.inputData = "";
          return;
        }
        this.inputData = newValue;
        //获取去除html标签后的字数
        this.wordsLength = this.inputData
          .replace(/<\/?.+?>/g, "")
          .replace(/ /g, "")
          .replace(/[ ]|[&nbsp;]/g, "")
          .replace(/[\r\n\s]/g, "")
          .trim().length;
        if (this.wordsLength < 0) {
          this.wordsLength = 0;
        }
      },
      immediate: true,
    },
    disabled: {
      handler(newValue) {
        this.inputDisabled = this.disabled;
      },
      immediate: true,
    },
  },
  created() {
    if (this.wordsLength > this.fixWordNumber) {
      this.wordColorBoole = true;
      this.inputDisabled = true;
      this.$emit("getNumberBoole", false);
    } else {
      this.wordColorBoole = false;
      this.$emit("getNumberBoole", true);
    }
  },
  methods: {
    getInputnumber(e) {
      //如果不需要限制字数 或者 按下键盘backspace键并且字数已超过限制字数直接return
      if (this.wordNumber == 0 || (e.keyCode == 8 && this.wordsLength > this.fixWordNumber)) {
        //没有接收到限制字数或者按下BackSpace键直接return
        return;
      }
      if (this.wordsLength > this.fixWordNumber) {
        this.wordColorBoole = true;
        //超出限制字数将文本框设置为只读
        this.inputDisabled = true;
        this.$emit("getNumberBoole", false);
      } else {
        this.wordColorBoole = false;
        this.$emit("getNumberBoole", true);
      }
    },
    //文本框点击事件
    inputFocus() {
      this.outlineBoole = true;
      if (this.wordNumber == 0) {
        return;
      }
      //将文本框设置为可编辑
      this.inputDisabled = false;
    },
  },
};
</script>
<style lang="scss">
//富文本组件body样式
.richText-body-focus {
  outline: 1px dotted #333;
}
.richText-body {
  position: relative;
  margin: 10px auto;
  border: 1px solid #dcdfe6;
  //富文本组件label样式
  .richText-label {
    margin-left: 14px;
  }
  //富文本组件 输入框样式
  .richText-input {
    width: 100%;
    height: calc(100% - 40px);
    overflow: auto;
    margin: 0 auto;
    top: 0;
    p {
      margin: 0;
      padding: 5px 10px;
    }
  }
  .words-number {
    font-size: 12px;
    color: #cccccc;
    position: absolute;
    bottom: 3px;
    right: 5px;
  }
  .mce-edit-focus {
    outline: none;
  }
}
</style>
