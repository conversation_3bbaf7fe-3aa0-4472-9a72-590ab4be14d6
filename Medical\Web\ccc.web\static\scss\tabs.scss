.el-tabs__header {
  background-color: #ffffff;
  margin: 0;
}

.el-tabs--top .el-tabs__item,
.el-tabs--top .el-tabs__item.is-bottom,
.el-tabs--bottom .el-tabs__item,
.el-tabs--bottom .el-tabs__item.is-bottom {
  padding: 0 20px !important;
}

.el-tabs {
  &.el-tabs--border-card {
    box-shadow: none
  }

  &.el-tabs--card {
    .el-tabs__header {
      background-color: $base-color;

      .el-tabs__nav-prev,
      .el-tabs__nav-next {
        color: #ffffff;
      }

      .el-tabs__item {
        border-right: 1px solid #ffffff;
        color: #ffffff;
        height: 36px;
        line-height: 34px;

        &.is-active {
          background-color: #ffffff;
        }
      }
    }
  }

  .el-tabs__header {
    margin: 0;
    border-bottom: 1px solid #E4E7ED;

    .el-tabs__nav-wrap {
      overflow: hidden;
      margin-bottom: -1px;
      position: relative;

      .el-tabs__nav-prev {
        color: #ffffff;
        margin-right: 4px;
        line-height: 36px;
      }

      .el-tabs__nav-next {
        color: #ffffff;
        margin-left: 4px;
        line-height: 36px;
      }
    }

    .el-tabs__nav {
      border: 0;
      border-bottom: none;
      border-radius: 4px 4px 0 0;
      box-sizing: border-box;
    }

    .el-tabs__item {
      margin: 2px 0 0 0;
      border-left: 0;
      border-right: 1px solid #d4d6d9;
      padding: 2px 10px;
      box-sizing: border-box;
      font-size: 16px;
      height: 36px;
      line-height: 34px;

      &:last-child {
        padding-right: 10px;
      }

      &.is-active {
        color: $base-color;
        font-weight: bold;
      }
    }
  }

  .el-tabs__content {
    padding: 5px;
  }
}