/*
 * FilePath     : \src\api\VirtualStation.js
 * Author       : 张现忠
 * Date         : 2020-10-24 11:47
 * LastEditors  : 张现忠
 * LastEditTime : 2020-10-26 19:10
 * Description  :获取虚拟病区内容
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/VirtualStationList";

export const urls = {
  // 新增虚拟病区
  SaveVirtualStation: baseUrl + "/SaveVirtualStation",
  // 修改虚拟病区
  UpdateVirtualStation: baseUrl + "/UpdateVirtualStation",
  // 删除虚拟病区
  DeleteVirtualStation: baseUrl + "/DeleteVirtualStation",
  // 获取所有虚拟病区
  GetAllVirtualStation: baseUrl + "/GetAllVirtualStation"
};
//新增虚拟病区
export const GetAllVirtualStation = () => {
  return http.get(urls.GetAllVirtualStation);
};
//删除虚拟病区
export const DeleteVirtualStation = params => {
  return http.post(urls.DeleteVirtualStation, qs.stringify(params));
};
//修改虚拟病区
export const UpdateVirtualStation = params => {
  return http.post(urls.UpdateVirtualStation, params);
};
//添加版本信息
export const SaveVirtualStation = params => {
  return http.post(urls.SaveVirtualStation, params);
};
