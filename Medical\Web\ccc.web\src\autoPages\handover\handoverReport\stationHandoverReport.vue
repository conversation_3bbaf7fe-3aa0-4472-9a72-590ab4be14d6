<!--
 * FilePath     : \src\autoPages\handover\handoverReport\stationHandoverReport.vue
 * Author       : 郭鹏超
 * Date         : 2020-07-02 22:38
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-09-20 15:19
 * Description  : 护士交班单按照病区查询
-->
<template>
  <base-layout class="station-handover-report">
    <div slot="header">
      <station-selector v-model="stationID"></station-selector>
      <span>日期:</span>
      <el-date-picker
        class="picker-date"
        v-model="handoverDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        :picker-options="checkDate"
        @change="getHandoverTitleView()"
      ></el-date-picker>
      <el-radio-group v-model="printType">
        <el-radio-button v-if="primaryNursePrintFlag || obstetricalPrintFlag" label="all">全部</el-radio-button>
        <el-radio-button v-if="primaryNursePrintFlag" label="primaryNurse">责护</el-radio-button>
        <el-radio-button v-if="obstetricalPrintFlag" label="obstetrical">产科</el-radio-button>
        <!-- todo:  待开发 -->
        <!-- <el-radio-button v-if="obstetricalPrintFlag" label="deliveryRoom">产房</el-radio-button> -->
        <el-radio-button v-if="obstetricalPrintFlag" label="newborn">产婴</el-radio-button>
      </el-radio-group>
      <div class="top-btn" v-if="printable">
        <el-button class="print-button" icon="iconfont icon-print" @click="printPDF()">打印</el-button>
      </div>
    </div>
    <handover-report
      :handoverReportTitleView="handoverReportTitleView"
      v-loading.fullscreen.lock="loading"
      :handoverReportSbarView="handoverReportSbarView"
      :stationID="stationID"
      :showBodyPartFlag="bodyPartFlag"
      @getReportParams="getReportParams"
    ></handover-report>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="交班报告单"
      fullscreen
      :visible.sync="showPrint"
      v-loading="pdfLoading"
    >
      <iframe
        id="printIframe"
        :src="ftpPath"
        type="application/x-google-chrome-pdf"
        width="99%"
        height="98%"
        frameborder="1"
        scrolling="auto"
      />
    </el-dialog>
  </base-layout>
</template>
<script>
import { PrintHandoverSummaryPDF } from "@/api/Document.js";
import { GetHandoverReportSBAR, GetHandoverReportTitle } from "@/api/HandoverReport.js";
import { GetEMRExists } from "@/api/Setting";
import { GetClinicalSettingValuesBySettingTypeCode } from "@/api/Setting.js";
import { GetSettingSwitchByTypeCode, GetSettingSwitchByTypeCodeAndTypeValue } from "@/api/SettingDescription";
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import { mapGetters } from "vuex";
import handoverReport from "../components/handoverReport";
export default {
  components: {
    baseLayout,
    stationSelector,
    handoverReport,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  async mounted() {
    this.handoverDate = this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -1, "yyyy-MM-dd");
    this.stationID = this.user.stationID;
    this.getShowBodyPartFlag();
    this.getHandoverTitleView();
    this.getPrintAllFlag();
    this.getFilteredColumns();
    this.getPrintTypeFlag();
    await this.getEMRExists();
  },
  data() {
    return {
      loading: false,
      stationID: 0,
      handoverDate: undefined,
      handoverReportTitleView: {},
      handoverReportSbarView: [],
      inpatientIDArrString: "",
      recordsCode: "",
      //PDF路径
      ftpPath: "",
      //显示打印
      showPrint: false,
      //读取PDF
      pdfLoading: false,
      bodyPartFlag: true,
      // 日期不得大于当前日期
      checkDate: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        },
      },
      printAll: false,
      filterPrintColumns: [],
      printable: false,
      primaryNursePrintFlag: false,
      obstetricalPrintFlag: false,
      printType: "all",
    };
  },
  methods: {
    /**
     * description: 查询交班数据
     * return {*}
     */
    getHandoverTitleView() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return;
      }
      this.handoverReportSbarView = [];
      this.handoverReportTitleView = {};
      let params = {
        stationID: this.stationID,
        startDate: this.handoverDate,
        endDate: this.handoverDate,
        showSignFlag: true,
      };
      this.loading = true;
      GetHandoverReportTitle(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverReportTitleView = res.data;
        }
      });
    },
    /**
     * description: 获取交班内容
     * param {*} inpatientIDArrString
     * param {*} recordsCode
     * return {*}
     */
    getReportParams(inpatientIDArrString, recordsCode) {
      this.inpatientIDArrString = inpatientIDArrString;
      this.recordsCode = recordsCode;
      let inpatientIDArr = inpatientIDArrString.split("||");
      let params = {
        inpatientIDArr: inpatientIDArr,
        stationID: this.stationID,
        startDate: this.handoverDate,
        endDate: this.handoverDate,
        recordsCode,
      };
      this.loading = true;
      GetHandoverReportSBAR(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverReportSbarView = res.data;
        }
      });
    },
    /**
     * description: 报告打印
     * param {*}
     * return {*}
     */
    printPDF() {
      const inpatientIDArrStr = this.getInpatientIDArr();
      this.ftpPath = "";
      this.showPrint = true;
      this.pdfLoading = true;
      let params = {
        handoverDate: this.handoverDate,
        stationID: this.stationID,
        inpatientIDArrString: JSON.stringify(inpatientIDArrStr),
        // 是否按班别呈现顶部统计，根据打印全部开关决定
        summaryByShift: this.printAll,
      };
      if (this.printType) {
        params.recordsCode = this.printType;
      } else {
        params.recordsCode = this.printAll ? "all" : this.recordsCode;
      }
      PrintHandoverSummaryPDF(params).then((res) => {
        this.pdfLoading = false;
        if (this._common.isSuccess(res)) {
          this.ftpPath = res.data;
        }
      });
    },
    /**
     * @description: 获取待打印的患者ID字符串集合
     * @return
     */
    getInpatientIDArr() {
      if (this.printAll) {
        const inpatientIDArr = [];
        // 需要排除的列
        const noPushKeys = this.filterPrintColumns.map((columnName) => `prop_${columnName}`);
        Object.entries(this.handoverReportTitleView.signData?.[1])?.reduce((acc, [key, value]) => {
          !noPushKeys.includes(key) && value.inpatientIDArr && acc.push(value.inpatientIDArr);
          return acc;
        }, inpatientIDArr);
        if (inpatientIDArr.length === 0) {
          return [];
        }
        // inpatientIDArr数组的元素包含||，需先拼接再拆分去重，类似于二维数组转一维数组并去重
        return [...new Set(inpatientIDArr.join("||").split("||"))];
      } else {
        return this.inpatientIDArrString.split("||");
      }
    },
    /**
     * description: 获取是否显示身体部位
     * param {*}
     * return {*}
     */
    getShowBodyPartFlag() {
      let params = {
        settingTypeCode: "HandoverReportBodyPartShowFlag",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.bodyPartFlag = res.data;
        }
      });
    },
    /**
     * @description: 获取打印全部开关
     * @return
     */
    getPrintAllFlag() {
      GetSettingSwitchByTypeCode({ settingTypeCode: "PrintAllPatientHandoverReport" }).then((res) => {
        if (this._common.isSuccess(res)) {
          this.printAll = res.data;
        }
      });
    },
    /**
     * @description: 获取过滤列
     * @return
     */
    getFilteredColumns() {
      GetClinicalSettingValuesBySettingTypeCode({ settingTypeCode: "FilteredHandoverReportPrintColumn" }).then(
        (res) => {
          if (this._common.isSuccess(res)) {
            this.filterPrintColumns = res.data;
          }
        }
      );
    },
    /**
     * @description: 获取病历是否存在，决定打印按钮是否显示
     * @return
     */
    async getEMRExists() {
      await GetEMRExists({
        fileClassID: 21,
      }).then((res) => {
        if (this._common.isSuccess(res)) {
          this.printable = res.data;
        }
      });
    },
    /**
     * @description: 获取打印类型标记
     * @return {*}
     */
    getPrintTypeFlag() {
      //责护打印
      let primaryNurseParams = {
        index: Math.random(),
        settingTypeCode: "HandoverReportPrimaryNursePrintFlag",
        typeValue: this.stationID,
      };
      GetSettingSwitchByTypeCodeAndTypeValue(primaryNurseParams).then((result) => {
        if (this._common.isSuccess(result)) {
          this.primaryNursePrintFlag = result.data;
        }
      });
      //产科打印
      let obstetricalParams = {
        index: Math.random(),
        settingTypeCode: "HandoverReportObstetricalPrintFlag",
        typeValue: this.stationID,
      };
      GetSettingSwitchByTypeCodeAndTypeValue(obstetricalParams).then((result) => {
        if (this._common.isSuccess(result)) {
          this.obstetricalPrintFlag = result.data;
        }
      });
    },
  },
};
</script>
<style lang='scss'>
.station-handover-report {
  .switch {
    margin-right: 5px;
    display: inline-block;
  }
  .top-btn {
    float: right;
  }
  .handover-station {
    .picker-date {
      width: 120px;
    }
  }
}
</style>