<!--
 * FilePath     : \ccc.web\src\pages\nursingJob\bedToJob.vue
 * Author       : 郭鹏超
 * Date         : 2020-07-02 15:53
 * LastEditors  : 杨欣欣
 * LastEditTime : 2022-04-15 11:49
 * Description  : 增加清除所有岗床配置功能
--> 
<template>
  <base-layout class="bed-job">
    <div class="bed-job-top top" slot="header">
      <span class="attendance-num">
        <span class="unfinished">未派床位：{{ attendanceCount.notNurseBedCount }}</span>
        <br />
        <span class="finished">已派床位：{{ attendanceCount.nurseBedCount }}</span>
      </span>

      <label class="top-label">床位搜索:</label>
      <el-input style="width: 170px" @keyup.enter.native="searchNursBed" v-model="bedNumber" placeholder="请输入床位号">
        <i @click="searchNursBed()" slot="append" class="iconfont icon-search"></i>
      </el-input>
      <div class="btn">
        <el-button class="clear-button" type="primary" @click="deleteAllBeds">清除所有岗床配置</el-button>
      </div>
    </div>
    <div class="bed-job-content" v-loading="mainLoading" element-loading-text="加载中……">
      <el-table :data="nurseBeds" stripe border height="100%">
        <el-table-column
          prop="jobGroupName"
          label="岗位分组"
          header-align="center"
          min-width="30px"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="岗位名称"
          header-align="center"
          min-width="30px"
          align="center"
        ></el-table-column>
        <el-table-column label="床位" header-align="center" align="left">
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row.beds" :key="index">
              <bed :type="'jobBed'" :bed="item" @getBedIDForDel="deleteBed(index, item)"></bed>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" min-width="15px" align="center">
          <template slot-scope="scope">
            <div class="opt">
              <el-tooltip content="维护">
                <i class="iconfont icon-edit" @click="modifyNurseBeds(scope.row)"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="name"
      :visible.sync="showDialog"
      custom-class="nursing-bed"
      v-if="showDialog"
    >
      <div class="bed">
        <bed
          v-for="(item, index) in dicts"
          :bed="item"
          :key="index"
          @getBedNumberAndBedIDForSel="addBedForJob(index)"
          :type="'jobBed'"
        ></bed>
      </div>
      <div slot="footer">
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="saveNurseBed">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import { GetNurseBed, SaveNurseBed, DeleteBed, GetNurseCount, DeleteAllBeds } from "@/api/NurseBed";
import { GetBedListDict } from "@/api/BedList";
import bed from "@/components/bed/index.vue";
import BaseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      mainLoading: false,
      formInline: [],
      nurseBeds: [],
      bedNumber: "",
      nurseBedsData: [],
      dicts: {},
      showDialog: false,
      dialogLoading: false,
      dialogLoadingText: "",
      departmentJobValue: {},
      submit: {},
      details: [],
      nurseBedSet: {},
      name: "",
      bedList: [],
      isSel: false,
      hasBedJob: [],
      showConfirm: "",
      stationID: "",
      deptmentJobID: "",
      attendanceCount: [],
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  components: {
    bed,
    BaseLayout,
  },
  mounted() {
    this.init();
  },
  methods: {
    //页面初始化
    async init() {
      this.stationID = this.user.stationID;
      this.getNurseBed();
      this.getDict();
      await this.getAttendanceCountNum();
    },
    //获取所岗床关系
    getNurseBed() {
      let params = { stationID: this.stationID };
      this.mainLoading = true;
      GetNurseBed(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.nurseBeds = result.data;
          this.nurseBedsData = result.data;
          this.assemblyData();
          this.mainLoading = false;
        }
      });
    },
    //获取所有床位
    getDict() {
      let params = { stationID: this.stationID };
      GetBedListDict(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.dicts = result.data;
        }
      });
    },
    //根据床号筛选
    searchNursBed() {
      this.nurseBeds = [];
      if (this.bedNumber == "") {
        this.nurseBeds = this.nurseBedsData;
        return;
      }
      this.nurseBedsData.forEach((item) => {
        if (item.beds.length != 0) {
          item.beds.forEach((list) => {
            if (list.bedNumber == this.bedNumber) {
              this.nurseBeds.push(item);
            }
          });
        }
      });
    },
    //表格删除床位
    deleteBed(index, bed) {
      let _this = this;
      let params = {
        id: bed.id,
      };
      this._deleteConfirm("确认要删除此床位吗？", (flag) => {
        if (flag) {
          this.changeBedStatue(index, true);
          DeleteBed(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              this.bedList = [];
              this.init();
            }
          });
        }
      });
    },
    //点击表格维护按钮
    modifyNurseBeds(value) {
      this.deptmentJobID = value.deptmentJobID;
      this.details = [];
      this.assemblyDistsData();
      for (var j = 0; j < this.dicts.length; j++) {
        for (var i = 0; i < value.beds.length; i++) {
          if (value.beds[i].bedID == this.dicts[j].id) {
            this.addBedForJob(j, "U");
          }
        }
      }
      this.showConfirm = "";
      this.hasBedJob = [];
      this.name = "科室岗位-" + value.name;
      this.departmentJobValue = value.deptmentJobID;
      this.showDialog = true;
      this.getPageData();
    },
    //弹窗床位点击事件
    addBedForJob(index, flag) {
      //传入index坐标,和床位状态
      //将该对象置空
      this.nurseBedSet = {};
      //如果所点击床位已存在在集合中状态改为false
      if (this.dicts[index].isSel) {
        //床位为已勾选状态 再点击改为false
        if (this.details.length != 0) {
          for (var i = 0; i < this.details.length; i++) {
            if (this.details[i].BedID == this.dicts[index].id) {
              this.details.splice(i, 1);
              this.changeBedStatue(index, false);
            }
          }
        }
      } else {
        //未被选中的床位 点击为更改状态为选中
        if (flag == null) {
          //手动更改床位状态
          let nurseBedList = this.nurseBeds.filter((item) => {
            return item.deptmentJobID == this.deptmentJobID;
          });
          if (nurseBedList.length > 0) {
            let list = nurseBedList[0].beds.filter((item) => {
              return item.bedID == this.dicts[index].id;
            });
            if (list.length > 0) {
              this.nurseBedSet.BedID = this.dicts[index].id;
              this.details.push(this.nurseBedSet);
              this.changeBedStatue(index, true);
              return;
            }
          }
        }
        if (flag == null && this.isHave(this.dicts[index].id)) {
          //选择床位已经在别的岗位选中
          this.showConfirm = "";
          for (var i = 0; i < this.hasBedJob.length; i++) {
            if (i != this.hasBedJob.length - 1) {
              this.showConfirm += this.hasBedJob[i].name + ",";
            } else {
              this.showConfirm += this.hasBedJob[i].name;
            }
          }
          this._confirm("【" + this.showConfirm + "】已配置该床位，是否继续配置？", "选择确认", (flag) => {
            if (flag) {
              this.nurseBedSet.BedID = this.dicts[index].id;
              this.details.push(this.nurseBedSet);
              this.changeBedStatue(index, true);
            }
          });
        } else {
          //点击维护 默认勾选已选床位
          this.nurseBedSet.BedID = this.dicts[index].id;
          this.details.push(this.nurseBedSet);
          this.changeBedStatue(index, true);
        }
      }
    },
    //维护弹窗保存按钮
    saveNurseBed() {
      this.submit.deptmentJobID = this.departmentJobValue;
      this.submit.details = this.details;
      let params = this.submit;
      return SaveNurseBed(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.showDialog = false;
          this.details = [];
          this.bedList = [];
          this.init();
          this._showTip("success", "保存成功！");
        }
      });
    },
    //更改床位的状态
    changeBedStatue(index, statue) {
      let item = this._common.clone(this.dicts[index]);
      this.$set(item, "isSel", statue);
      this.$set(this.dicts, index, item);
    },

    //床位集合非空的岗位将被存到bedList集合中
    assemblyData() {
      for (var i = 0; i < this.nurseBedsData.length; i++) {
        for (var j = 0; j < this.nurseBedsData[i].beds.length; j++) {
          this.nurseBedsData[i].beds[j].isDel = true;
          this.nurseBedsData[i].beds[j].isSel = false;
          this.bedList.push(this.nurseBedsData[i]);
        }
      }
    },
    //组装数据，为dialog提供数据,返回的是床位数据
    assemblyDistsData(flag) {
      for (var i = 0; i < this.dicts.length; i++) {
        this.dicts[i].isDel = false;
        this.dicts[i].isSel = false;
        ///会执行多次进行数据渲染
        if (flag != null) {
          // this.dicts[i].bedNumber = this.dicts[i].bedNumber + "床";
          this.dicts[i].bedNumber = this.dicts[i].bedNumber;
        }
      }
    },
    //获取刷新后页面数据
    getPageData() {
      this.getAttendanceCountNum();
    },
    //获取已派班未派班统计数
    async getAttendanceCountNum() {
      await GetNurseCount().then((result) => {
        if (this._common.isSuccess(result)) {
          this.attendanceCount = result.data;
        }
      });
    },
    //判断可别岗位是否包含指定的床位，如果有，将床位所对应岗位给hasBedJob集合
    //hasBedJob中有几个元素，就会被打印几次
    isHave(bedId) {
      this.hasBedJob = [];
      for (var i = 0; i < this.bedList.length; i++) {
        for (var j = 0; j < this.bedList[i].beds.length; j++) {
          if (this.bedList[i].beds[j].bedID == bedId) {
            ///外层加一个去重，是否包含数据
            if (!this.isHaveBedInJob(this.bedList[i].name)) {
              this.hasBedJob.push(this.bedList[i]);
            }
          }
        }
      }
      if (this.hasBedJob.length != 0 && this.hasBedJob != null) {
        return true;
      } else {
        return false;
      }
    },
    isHaveBedInJob(name) {
      for (var i = 0; i < this.hasBedJob.length; i++) {
        if (this.hasBedJob[i].name == name) {
          return true;
        }
      }
      return false;
    },
    deleteAllBeds() {
      let params = {
        departmentJobIDs: [],
      };
      this.nurseBeds.forEach((nurseBed) => {
        if (nurseBed.beds.length != 0) {
          params.departmentJobIDs.push(nurseBed.deptmentJobID);
        }
      });
      if (params.departmentJobIDs.length == 0) {
        this._showTip("warning", "没有要清除的岗床配置!");
        return;
      }
      this._deleteConfirm("确认要删除所有床位吗？", (flag) => {
        if (flag) {
          DeleteAllBeds(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              this.bedList = [];
              this.init();
            }
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" >
.bed-job {
  height: 100%;
  .bed-job-top {
    .el-input-group__append {
      padding: 0 10px;
    }
    i {
      color: #8cc63e;
    }
    .attendance-num {
      line-height: 20px;
      display: inline-block;
      vertical-align: middle;
      .unfinished {
        color: #ff0000;
      }
    }
    .top-label {
      margin-left: 20px;
    }
    .btn {
      float: right;
    }
  }

  .bed-job-content {
    height: 100%;
  }
  .nursing-bed {
    width: 785px;
    height: 70%;
  }
}
</style>
