<!--
 * FilePath     : \ccc.web\src\pages\transferPages\transferDatasetUserEntry.vue
 * Author       : 曹恩
 * Date         : 2021-09-26 10:38
 * LastEditors  : machao
 * LastEditTime : 2022-03-06 16:31
 * Description  : 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getStatisticsUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
      user: "getUser",
    }),
  },
  created() {
    this.url =
      getStatisticsUrl() +
      "datasetUserEntry?token=" +
      this.token +
      "&hospitalID=" +
      this.hospitalInfo.hospitalID +
      "&stationID=" +
      this.user.stationID +
      "&empID=" +
      this.user.userID;
  },
};
</script>
<style lang="scss"></style>
