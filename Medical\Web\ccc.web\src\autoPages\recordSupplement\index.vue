<!--
 * FilePath     : \src\autoPages\recordSupplement\index.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-22 15:58
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-21 15:06
 * Description  : 记录补录
 * CodeIterationRecord: 
 -->
<template>
  <base-layout class="record-supplement" headerHeight="auto" v-loading="loading">
    <template slot="header">
      <div class="record-supplement-header">
        <div class="left-wrapper">
          <query-field-input
            :queryField="queryField"
            :defaultValue="defaultValue"
            :loading.sync="loading"
            direction
            :disabled="queryDisabled"
            @data="handlePatientQueryResult"
          />
          <!-- 病人历次住院信息切换器 -->
          <el-select v-model="selectInpatientID" class="inpatient-select">
            <template slot="prefix">
              {{
                (patientInHospitalLabels.find((inHospital) => inHospital.inpatientID === selectInpatientID) || {}).label
              }}
            </template>
            <el-option
              v-for="{ inpatientID, patientName, numberOfAdmissions, admissionDateTimeView } in patientInHospitalList"
              :key="inpatientID"
              :label="patientInHospitalLabels.find((label) => label.inpatientID === inpatientID).label"
              :value="inpatientID"
            >
              <span>姓名：{{ patientName }}</span>
              <span>入院次数：{{ numberOfAdmissions }}次</span>
              <span>入院时间：{{ _datetimeUtil.formatDate(admissionDateTimeView, "yyyy-MM-dd hh:mm") }}</span>
            </el-option>
          </el-select>
        </div>
        <div class="right-wrapper">
          <el-button type="primary" icon="iconfont icon-temperature" @click="getTemperaturePdfData">体温单</el-button>
        </div>
      </div>
    </template>
    <div class="container">
      <div class="aside">
        <div class="menu-list">
          <div
            v-for="({ settingValue, description }, index) in componentList"
            :key="index"
            class="menu-item"
            :class="{ 'is-active': activeComponentName === settingValue }"
            @click="handleMenuSelect(settingValue)"
          >
            <div class="menu-item-label">{{ description }}</div>
          </div>
        </div>
      </div>
      <div class="main">
        <component
          :is="activeComponentName"
          :patient="patient"
          :supplemnentPatient="patient"
          :childComponents="childComponents"
        />
      </div>
    </div>
    <el-dialog
      v-if="patient"
      v-loading="dialogLoading"
      :visible.sync="dialogVisible"
      :title="`${patient.bedNumber} - ${patient.patientName}【${patient.gender}-${patient.age}】-- 体温单`"
      custom-class="temperature-list-dialog"
      element-loading-text="加载中……"
      fullscreen
    >
      <temperature-list v-model="temperatureTabsData"></temperature-list>
    </el-dialog>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import queryFieldInput from "@/components/queryFieldInput";
import nursingProcessSwitch from "./components/nursingProcessSwitch";
import drugAllergy from "@/pages/patientAllergy/index";
import patientEvent from "@/pages/patientEvent/index";
import handover from "./components/handover";
import specificCareSwitch from "./components/specificCareSwitch";
import consultSwitch from "./components/consultSwitch";
import nursingRecordSwitch from "./components/nursingRecordSwitch";
import otherRecordSwitch from "./components/otherRecordSwitch";
import temperatureList from "@/components/TemperatureList";
import { GetPatientVitalSignData } from "@/api/VitalSign";
import { GetClinicSettingByTypeCode, GetBySettingTypeCodeByArray } from "@/api/Setting";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    queryFieldInput,
    nursingProcessSwitch,
    drugAllergy,
    patientEvent,
    handover,
    specificCareSwitch,
    consultSwitch,
    nursingRecordSwitch,
    temperatureList,
    otherRecordSwitch,
  },
  data() {
    return {
      loading: false,
      queryField: [],
      queryDisabled: false,
      defaultValue: {},
      patientInHospitalList: [],
      patient: undefined,
      componentSettings: [],
      componentList: [],
      selectInpatientID: "",
      activeComponentName: "",
      modifyDeadlineDays: undefined,
      dialogLoading: false,
      dialogVisible: false,
      temperatureTabsData: [],
    };
  },
  computed: {
    ...mapGetters({
      patientInfo: "getCurrentPatient",
    }),
    dischargeDays() {
      if (!this.patient.dischargeDateTimeView) {
        return undefined;
      }
      return Math.floor(
        this._datetimeUtil.getTimeDifference(this.patient.dischargeDateTimeView, undefined, "date", "D", 3)
      );
    },
    patientInHospitalLabels() {
      return this.patientInHospitalList.map(
        ({ inpatientID, patientName, numberOfAdmissions, admissionDateTimeView }) => {
          return {
            inpatientID: inpatientID,
            label: `姓名：${patientName}   住院次数：${numberOfAdmissions}次   入院时间：${this._datetimeUtil.formatDate(
              admissionDateTimeView,
              "yyyy-MM-dd hh:mm"
            )}`,
          };
        }
      );
    },
    childComponents() {
      return this.componentSettings.filter((setting) => setting.typeValue === this.activeComponentName);
    },
  },
  watch: {
    selectInpatientID: {
      handler(newVal) {
        this.patient = this.patientInHospitalList.find((patientInfo) => patientInfo.inpatientID === newVal);
      },
    },
    "patient.emrArchivingFlag": {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        this._showTip("warning", "该病人已归档，记录只读无法编辑");
        this.$store.commit("session/setReadOnly", true);
      },
    },
  },
  async created() {
    this.loading = true;
    await Promise.all([this.getPatientQueryField(), this.getModifyDeadlineDays(), this.getModulesSetting()]);
    if (!this.queryField.length) {
      this.loading = false;
      return;
    }
    const attribute = this.queryField[0].typeValue;
    if (this.$route.query.chartNo) {
      this.queryDisabled = true;
      this.$store.commit("session/setReadOnly", true);
    }
    let attributeValue = this.$route.query?.chartNo;
    attributeValue ??= this.patientInfo?.[attribute.charAt(0).toLowerCase() + attribute.slice(1)];
    this.defaultValue = {
      attribute,
      attributeValue: attributeValue,
    };
  },
  methods: {
    /**
     * @description: 获取开启的模块
     * @return
     */
    async getModulesSetting() {
      const params = {
        settingTypeCode: "SupplementModule",
      };
      const res = await GetClinicSettingByTypeCode(params);
      if (this._common.isSuccess(res)) {
        this.componentSettings = res.data;
        this.componentList = res.data.filter((setting) => !setting.typeValue);
        this.activeComponentName =
          this.componentList.find((m) => m.settingValue === "nursingRecordSwitch")?.settingValue ??
          this.componentList[0].settingValue;
      }
    },
    /**
     * @description: 头部查询控件返回病人信息处理
     * @param patientInHospitalList 病人住院信息列表
     * @return
     */
    handlePatientQueryResult(patientInHospitalList) {
      if (!patientInHospitalList || !patientInHospitalList.length) {
        this.selectInpatientID = "";
        this.patientInHospitalList = [];
        this._showTip("warning", "没有获取到该病人近几天数据，请确定输入病案号的准确性");
        return;
      }
      this.selectInpatientID = patientInHospitalList[0].inpatientID;
      this.patientInHospitalList = patientInHospitalList;
      if (
        this.patientInHospitalList[0].dischargeFlag &&
        this.dischargeDays &&
        this.dischargeDays > this.modifyDeadlineDays
      ) {
        this.selectInpatientID = "";
        this.patientInHospitalList = [];
        this._showTip("warning", `出院${this.modifyDeadlineDays}天后数据无法进行补录！`);
        return;
      }
    },
    /**
     * @description: 菜单选择处理
     * @param key 选中的菜单项
     */
    handleMenuSelect(key) {
      this.activeComponentName = key;
    },
    /**
     * @description: 获取体温单数据
     * @return
     */
    async getTemperaturePdfData() {
      this.dialogVisible = true;
      this.dialogLoading = true;
      const params = {
        inPatientID: this.patient.inpatientID,
        startTime: this.patient.admissionDate,
        endTime: this.patient.dischargeDate ? this.patient.dischargeDate : this._datetimeUtil.getNow(),
      };
      const res = await GetPatientVitalSignData(params);
      if (this._common.isSuccess(res)) {
        this.dialogLoading = false;
        if (res.data.length == 0) {
          this._showTip("warning", "暂无数据");
          return;
        }
        this.temperatureTabsData = res.data;
      }
    },
    /**
     * @description: 获取顶部查询控件配置
     * @return
     */
    async getPatientQueryField() {
      let params = {
        settingTypeCode: "PatientQueryField",
      };
      const res = await GetClinicSettingByTypeCode(params);
      if (this._common.isSuccess(res)) {
        this.queryField = res.data;
      }
    },
    /**
     * @description: 获取可修改期限天数
     * @return
     */
    async getModifyDeadlineDays() {
      const result = await GetBySettingTypeCodeByArray({
        SettingTypeCode: "ModifyDeadline_HN",
      });
      if (this._common.isSuccess(result) && result.data && result.data.length > 0) {
        this.modifyDeadlineDays = result.data[0].typeValue;
      }
    },
  },
  beforeDestroy() {
    this.$store.commit("session/setReadOnly", false);
  },
};
</script>

<style lang="scss">
.record-supplement {
  .base-header {
    padding-right: 0;
  }
  .record-supplement-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 10px;
    .left-wrapper {
      display: flex;
      align-items: center;
      flex: 1;
      .query-field-input > p {
        margin: 0;
      }
      .inpatient-select {
        min-width: 320px;
        .el-input__prefix {
          position: relative;
          box-sizing: border-box;
          border: 1px solid #dcdfe6;
          padding: 0 40px 30px 16px;
          left: 0;
          display: block;
          font-size: 13px;
          line-height: 32px;
          height: 32px;
          visibility: hidden;
        }
        input {
          position: absolute;
          padding-left: 16px;
        }
        .el-input__suffix {
          display: flex;
          align-items: center;
        }
      }
    }
  }
  .container {
    height: 100%;
    box-sizing: border-box;
    display: flex;
    gap: 8px;
    .aside {
      width: 175px;
      background-color: #fff;
      border: 1px solid #e6e6e6;

      .menu-list {
        width: 100%;
        .menu-item {
          height: 40px;
          line-height: 40px;
          cursor: pointer;
          transition: all 0.3s;
          display: flex;
          flex-direction: column;
          align-items: center;
          border-bottom: 1px solid #e6e6e6;
          &:hover,
          &.is-active {
            background-color: #ebf7df;
          }

          .menu-item-label {
            font-size: 18px;
            text-align: center;
          }
        }
      }
    }
    .main {
      height: 100%;
      overflow: hidden;
      flex: 1;
    }
  }
  .temperature-list-dialog {
    & .el-dialog__body {
      height: calc(100% - 35px);
    }
  }
}
</style>
