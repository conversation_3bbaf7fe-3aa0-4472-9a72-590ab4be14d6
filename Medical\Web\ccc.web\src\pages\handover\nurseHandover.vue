<!--
 * FilePath     : \ccc.web\src\pages\handover\nurseHandover.vue
 * Author       : 李青原
 * Date         : 2020-04-29 09:25
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-09-10 15:31
 * Description  : 
 -->

<template>
  <base-layout>
    <div slot="header">
      <span>班别日期：</span>
      <el-date-picker
        v-model="handoverDate"
        :clearable="false"
        value-format="yyyy-MM-dd 00:00:00"
        format="yyyy-MM-dd"
        type="date"
        laceholder="选择日期"
        style="width: 160px"
      ></el-date-picker>
      <shift-selector
        width="165px"
        :stationID="user.stationID"
        v-model="handoverShiftID"
        @select-item="selectShiftItem"
      ></shift-selector>
      <span>责任护士：</span>
      <el-select class="nurse-select" @change="getPatientData" v-model="userID" placeholder="请选择">
        <el-option v-for="(item, index) in attendance" :key="index" :value="item.value" :label="item.label"></el-option>
      </el-select>
    </div>
    <el-table :data="tableData" height="100%">
      <el-table-column label="床号" prop="bedNumber"></el-table-column>
      <el-table-column label="姓名" prop="patientName"></el-table-column>
      <el-table-column label="年龄" prop="age"></el-table-column>
      <el-table-column label="性别" prop="gender"></el-table-column>
      <el-table-column label="护理级别" prop="nursingLevel"></el-table-column>
      <el-table-column label="责任护士" prop="careNurse"></el-table-column>
      <el-table-column label="班别" prop="shift"></el-table-column>
      <el-table-column label="进度" prop="handOverStatus"></el-table-column>
      <el-table-column label="交班" key="handover">
        <template slot-scope="scope">
          <el-button @click="handoverCheck(scope.row)">交班</el-button>
        </template>
      </el-table-column>
      <el-table-column label="修改" key="updatehandover">
        <template slot-scope="scope">
          <el-button @click="handoverDetail(scope.row.handOverID, scope.row.bedNumber, 'update')">修改</el-button>
        </template>
      </el-table-column>
      <el-table-column label="预览交班" key="readerhandover">
        <template slot-scope="scope">
          <el-button @click="handoverDetail(scope.row.handOverID, scope.row.bedNumber, 'reader')">预览</el-button>
        </template>
      </el-table-column>
      <el-table-column label="接班" key="handon">
        <template slot-scope="scope">
          <el-button @click="handon(scope.row.handOverID)">接班</el-button>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import shiftSelector from "@/components/selector/shiftSelector";
import { mapGetters } from "vuex";
import { HandoffCheck } from "@/api/HandoffCheck";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetHandoffAttendanceShiftNurse } from "@/api/Attendance";
import { GetHandOver, Handon } from "@/api/Handover";
export default {
  components: {
    baseLayout,
    shiftSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  mounted() {
    this.getNowStationShift();
    this.selectShift();
  },
  data() {
    return {
      tableData: [],
      nurseList: [],
      userID: "",
      attendance: [],
      handoverDate: new Date(),
      handoverShift: "",
      handoverShiftID: "",
    };
  },

  methods: {
    async selectShift() {
      //派班人员初始化
      this.attendance = [];
      let params = {
        shiftDate: this.handoverDate,
        stationID: this.user.stationID,
        shift: this.handoverShift,
      };
      await GetHandoffAttendanceShiftNurse(params).then((data) => {
        if (this._common.isSuccess(data)) {
          if (data.data == null || data.data.length == 0) {
            return;
          }
          //派班数据
          let tempAttendance = data.data;
          // 默许列表第一位护士
          this.userID = tempAttendance[0].userID;
          tempAttendance.forEach((item) => {
            const rowItem = {
              value: item.userID,
              label: item.name,
            };
            //默许登入人员
            if (item.userID == this.user.userID) {
              this.userID = this.user.userID;
            }
            this.attendance.push(rowItem);
          });
        }
        this.getPatientData();
      });
    },
    getPatientData() {
      let params = {
        shiftDate: this.handoverDate,
        stationID: this.user.stationID,
        shift: this.handoverShift,
        nurseID: this.userID,
      };
      GetHandOver(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.length == 0) {
            this._showTip("warning", "未查到病人信息！");
          } else {
            this.tableData = response.data;
          }
        }
      });
    },
    async selectShiftItem(shiftInfo) {
      this.handoverShift = shiftInfo.shift;
      this.handoverShiftID = shiftInfo.id;
      await this.selectShift();
    },
    handoverCheck(patientInfo) {
      let params = {
        inpatientID: patientInfo.inpatientID,
        nursingLevel: patientInfo.nursingLevel,
        shiftDate: this.handoverDate,
        shift: this.handoverShift,
      };
      HandoffCheck(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data == null) {
            //TODO:跳转
            this.$router.push({
              name: "handoverSBAR",
              query: {
                handoverType: "ShiftHandover",
                handoffDate: this.handoverDate,
                handoverShift: this.handoverShift,
                bedNumber: patientInfo.bedNumber,
              },
            });
          }
        }
      });
    },
    handoverDetail(handoverID, bedNumber, type) {
      if (type == "update") {
        this.$router.push({
          name: "handoverSBAR",
          query: {
            handoverID: handoverID,
            handoverType: "ShiftHandover",
            handoffDate: this.handoverDate,
            handoverShift: this.handoverShift,
            bedNumber: bedNumber,
          },
        });
      } else if (type == "reader") {
        this.$router.push({
          name: "handoverSBAR",
          query: {
            handoverID: handoverID,
            handoverType: "ShiftHandover",
            handoffDate: this.handoverDate,
            handoverShift: this.handoverShift,
            disabled: true,
            bedNumber: bedNumber,
          },
        });
      }
    },
    getNowStationShift() {
      GetNowStationShiftData().then((data) => {
        if (this._common.isSuccess(data)) {
          let result = data.data;
          this.handoverDate = result.shiftDate;
          //当前班别(默许班别)
          this.handoverShift = result.nowShift.shift;
        }
        this.selectShift();
      });
    },
    handon(handoverID) {
      this.loading = true;
      let params = {
        handoverID: handoverID,
      };
      Handon(params).then((data) => {
        if (this._common.isSuccess(data)) {
          this.loading = false;
        }
      });
    },
  },
};
</script>

<style  lang="scss">
.title-style {
  display: inline-block;
}
</style>