<!--
 * FilePath     : \src\pages\operationLog\index.vue
 * Author       : 马超
 * Date         : 2022-11-26 17:36
 * LastEditors  : 张现忠
 * LastEditTime : 2024-04-19 17:37
 * Description  : 
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="query-log">
    <div slot="header">
      <span>开始时间</span>
      <el-date-picker
        v-model="startDateTime"
        type="datetime"
        value-format="yyyy-MM-dd HH:mm"
        format="yyyy-MM-dd HH:mm"
        placeholder="选择日期时间"
      ></el-date-picker>
      <span>结束时间</span>
      <el-date-picker
        v-model="endDateTime"
        type="datetime"
        value-format="yyyy-MM-dd HH:mm"
        format="yyyy-MM-dd HH:mm"
        placeholder="选择日期时间"
      ></el-date-picker>
      <el-button class="query-button" icon="iconfont icon-search" @click="getEventLogByDateTime()">查询</el-button>
    </div>
    <div slot-scope="layout" :style="{ height: layout.height + 'px' }" class="query-log-content">
      <u-table
        v-loading="loading"
        element-loading-text="加载中……"
        :data="logTableData"
        border
        stripe
        use-virtual
        highlight-current-row
        :height="layout.height"
        :row-height="43"
      >
        <u-table-column width="150" label="事件类型" prop="eventType"></u-table-column>
        <u-table-column min-width="140" label="日志内容" prop="content"></u-table-column>
        <u-table-column min-width="150" label="目标主体" prop="target"></u-table-column>
        <u-table-column width="180" label="客户端地址" prop="ipAddress"></u-table-column>
        <u-table-column width="180" label="服务器地址" prop="serverIPAddress"></u-table-column>
        <u-table-column width="80" label="登录类型" align="center" prop="clientType"></u-table-column>
        <u-table-column width="160" label="记录时间" align="center" prop="createTime"></u-table-column>
        <u-table-column width="80" label="操作人" align="center" prop="employeeName"></u-table-column>
      </u-table>
    </div>
  </base-layout>
</template>

<script>
import BaseLayout from "@/components/BaseLayout";
import { GetEventLogByDateTime } from "@/api/EventLog";
export default {
  components: {
    BaseLayout,
  },
  data() {
    return {
      loading: false,
      logTableData: [],
      currrntDate: this._datetimeUtil.getNow(),
      startDateTime:  this._datetimeUtil.addHours(this._datetimeUtil.getNow(), -0.5, "yyyy-MM-dd hh:mm") ,
      endDateTime: this._datetimeUtil.getNow(),
    };
  },
  mounted() {
    this.getEventLogByDateTime();
  },
  methods: {
    /**
     * @description: 根据时间区间获取操作日志
     * @return 
     */    
    getEventLogByDateTime() {
      let params = {
        startDateTime: this.startDateTime,
        endDateTime: this.endDateTime,
      };
      this.loading = true;
      GetEventLogByDateTime(params).then((resp) => {
        this.loading = false;
        if (this._common.isSuccess(resp)) {
          this.logTableData = resp.data || [];
          return;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.query-log {
  .query-log-content {
    height: 100%;
    font-size: 13px;
    .el-table .cell {
      font-size: 14px;
    }
  }
}
</style>