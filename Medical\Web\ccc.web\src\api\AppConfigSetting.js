/*
 * FilePath     : \src\api\AppConfigSetting.js
 * Author       : 吴馥辰
 * Date         : 2022-11-07 08:57
 * LastEditors  : 来江禹
 * LastEditTime : 2025-03-03 08:54
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
const baseUrl = "/AppConfigSetting";
export const urls = {
  //获取全部的API接口
  GetAppConfigSettings: baseUrl + "/GetAppConfigSettings",
  //新增数据
  SaveAppConfigSettingInfo: baseUrl + "/SaveAppConfigSettingInfo",
  //根据code和systemType获取配置
  GetConfigSettingByCode: baseUrl + "/GetConfigSettingByCode"
};
// 获取全部查询数据
export const GetAppConfigSettings = params => {
  return http.get(urls.GetAppConfigSettings, params);
};
// 新增数据
export const SaveAppConfigSettingInfo = params => {
  return http.post(urls.SaveAppConfigSettingInfo, params);
};
//根据code和systemType获取配置
export const GetConfigSettingByCode = params => {
  return http.get(urls.GetConfigSettingByCode, params);
};
