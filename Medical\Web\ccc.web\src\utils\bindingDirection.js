/*
 * FilePath     : \src\utils\direction.js
 * Author       : 郭鹏超
 * Date         : 2021-06-07 10:25
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-06-17 18:54
 * Description  :输入框切换函数
 * 用法:将this._direction(this)
 */

import Vue from "vue";
let direction = (model, flag = "") => {
  if (!model) {
    return;
  }
  let direction = model.$getDirection(flag);
  direction.on("keyup", function(e, val) {
    if (e.keyCode == 39) {
      direction.next();
    }
    if (e.keyCode == 37) {
      direction.previous();
    }
    if (e.keyCode == 38) {
      direction.previousLine();
    }
    if (e.keyCode == 40) {
      direction.nextLine();
    }
  });
  return direction;
};

Vue.prototype._direction = direction;
export default {
  direction
};
