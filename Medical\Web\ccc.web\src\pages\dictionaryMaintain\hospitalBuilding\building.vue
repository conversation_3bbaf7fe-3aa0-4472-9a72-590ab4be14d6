<!--
 * FilePath     : \src\pages\dictionaryMaintain\hospitalBuilding\building.vue
 * Author       : 来江禹
 * Date         : 2022-07-24 16:54
 * LastEditors  : 来江禹
 * LastEditTime : 2022-09-12 14:31
 * Description  : 楼栋维护页面，对楼栋数据进行增删改查操作
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="hospital-building">
    <div class="hospital-building-header" slot="header">
      <span class="btn-list">
        <el-button type="success" icon="iconfont icon-add" @click="addFloor">新增</el-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
        <el-button class="print-button" icon="iconfont icon-back" @click="goBack">返回</el-button>
      </span>
      <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    </div>
    <div class="build-table">
      <el-table :data="buildTabList" border stripe>
        <el-table-column type="index" label="序号" width="70" align="center"></el-table-column>
        <el-table-column label="楼栋名称" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.TypeValue">{{ scope.row.Description }}</span>
            <el-input
              v-else
              class="table-build-input"
              v-model="scope.row.buildingName"
              placeholder="请输入楼栋名称"
              @change="getSelectData()"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" width="80px" align="left">
          <template slot-scope="scope">
            <!-- 删除按钮 -->
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="deletBuild(scope.row.TypeValue, scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import progressView from "@/components/progressView";
import { SaveBuildDatas, DeleteSettingDescriptionBuidDatas, GetSettingDescriptionOne } from "@/api/WardMaintenance";
export default {
  components: {
    baseLayout,
    stationSelector,
    progressView,
  },
  data() {
    return {
      // 显示的记录
      buildTabList: [],
      // 供数据克隆的对象
      buildTabView: {
        buildingName: "",
      },
      settingTypeCode: "WardBuilding",
      code: "",
      name: "",
      //进度条开关
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
    };
  },
  created() {
    this.refresh();
  },
  methods: {
    /**
     * description: 进度条关闭函数
     * return {*}
     */
    progressClose() {
      this.progressFlag = false;
      this.renewMessageData();
    },
    /**
     * description: 重置进度条
     * return {*}
     */
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    /**
     * description: 刷新页面数据
     * return {*}
     */
    async refresh() {
      this.buildTabList = [];
      this.getSetting();
    },
    /**
     * description: 获取当前页面表格数据
     * return {*}
     */
    async getSetting() {
      let params = {
        SettingTypeCode: "WardBuilding",
      };
      await GetSettingDescriptionOne(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            list.forEach((data) => {
              let param = {
                Description: data.description,
                TypeValue: data.typeValue,
              };
              this.buildTabList.push(param);
            });
          }
        }
      });
    },
    /**
     * description: 点击新增按钮，实现新增数据
     * return {*}
     */
    addFloor() {
      let row = this._common.clone(this.buildTabView);
      this.buildTabList.push(row);
    },
    /**
     * description: 删除当前前行数据
     * param {*} index
     * return {*}
     */
    deletBuild(index, row) {
      if (index == undefined) {
        let index = this.buildTabList.findIndex((build) => build == row);
        if (index >= 0) {
          this.buildTabList.splice(index, 1);
        }
      } else {
        this._deleteConfirm("确定删除数据么？", (flag) => {
          if (flag) {
            // 确认删除
            let params = {
              TypeValue: index,
              SettingTypeCode: this.settingTypeCode,
            };
            DeleteSettingDescriptionBuidDatas(params).then((res) => {
              if (this._common.isSuccess(res)) {
                this._showTip("success", "删除成功");
                this.refresh();
              }
            });
          }
        });
      }
    },
    /**
     * description: 获取输入框输入内容，以便于保存
     * return {*}
     */
    getSelectData() {
      let modifyDatas = this.buildTabList;
      if (modifyDatas.length == 0) {
        return undefined;
      }
      return modifyDatas;
    },
    /**
     * description:保存新增数据，实现批量保存
     * return {*}
     */
    async save() {
      let datas = this.getSelectData();
      let successMessage = "";
      let failMessage = "";
      for (let index = 0; index < datas.length; index++) {
        const buildData = datas[index];
        this.progressFlag = true;
        //判断输入框是否有内容，避免重复请求。有内容执行保存
        if (buildData.buildingName) {
          let params = {
            SettingTypeCode: this.settingTypeCode,
            Description: buildData.buildingName,
            SettingType: "216",
          };
          let messageItem = buildData.buildingName;
          await SaveBuildDatas(params).then((res) => {
            if (res.code == 1) {
              successMessage = index == 0 ? messageItem : successMessage + messageItem + ",";
            } else {
              failMessage = failMessage + " " + messageItem;
            }
          });
        }
        //配置进度条内容
        let progress = (((index + 1) / datas.length) * 100).toFixed(0);
        //配置进度条内容
        this.messageData[0].value = Number(progress);
        this.messageData[1].value = successMessage;
        this.messageData[2].value = failMessage;
        this.messageData[3].value = "";
      }
      await this.refresh();
    },
    /**
     * description: 点击返回按钮，跳转页面
     * return {*}
     */
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss">
.hospital-building {
  .hospital-building-header {
    .table-name-select {
      width: 150px;
      margin-right: 30px;
    }
    .btn-list {
      display: block;
      float: right;
    }
  }
  .build-table {
    .table-build-input {
      min-width: 96%;
    }
  }
}
</style>










