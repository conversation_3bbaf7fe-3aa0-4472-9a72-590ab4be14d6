<!--
 * FilePath     : \src\pages\schedule\components\ClinicDataView.vue
 * Author       : 曹恩
 * Date         : 2022-06-16 09:21
 * LastEditors  : 苏军志
 * LastEditTime : 2023-01-18 09:39
 * Description  : 病人仪器数据
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="clinic-data-view">
    <div slot="header">
      <span class="label">日期:</span>
      <el-date-picker
        v-model="queryDate"
        type="date"
        :clearable="false"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        @change="getTableData()"
        class="date-picker"
      ></el-date-picker>
    </div>
    <div class="clinic-data-table">
      <el-table height="100%" :data="monitorData" border stripe v-loading="loading" :element-loading-text="loadingText">
        <el-table-column :label="timeColumn.title" :min-width="timeColumn.width" align="center">
          <template slot-scope="clinicData">
            <div>{{ clinicData.row[timeColumn.index] }}</div>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(col, index) in dataColumns"
          :key="index"
          :label="col.title"
          :min-width="col.width"
          align="center"
        >
          <template slot="header" slot-scope="scope">
            <span v-html="scope.column.label"></span>
          </template>
          <template slot-scope="monitor">
            <el-input v-model="monitor.row[index + 1].assessValue" :readonly="true"></el-input>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetClinicDataByPatientID } from "@/api/ClinicData";
export default {
  components: {
    baseLayout,
  },
  props: {
    params: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      loadingText: "加载中……",
      loading: false,
      //查询日期
      queryDate: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      inpatientID: undefined,
      monitorData: [],
      timeColumn: [],
      dataColumns: {},
    };
  },
  watch: {
    params: {
      immediate: true,
      handler(newValue) {
        this.init();
      },
    },
  },
  methods: {
    /**
     * description: 初始化方法 接受排程页面日期及病人ID参数
     * return {*}
     */
    init() {
      if (!this.params && !this.params.inpatientID) {
        return;
      }
      this.queryDate = this.params.scheduleDate;
      this.inpatientID = this.params.inpatientID;
      this.getTableData();
    },
    /**
     * description: 获取病人仪器数据
     * return {*}
     */
    getTableData() {
      this.loading = true;
      this.timeColumn = [];
      this.dataColumns = [];
      this.monitorData = [];
      let params = {
        inpatientID: this.inpatientID,
        scheduleDate: this.queryDate,
      };
      GetClinicDataByPatientID(params).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response) && response.data) {
          if (response.data.columns && response.data.columns.length > 0) {
            this.timeColumn = response.data.columns[0];
            this.dataColumns = response.data.columns;
            // 删除时间列
            this.dataColumns.shift();
          }
          this.monitorData = response.data.rows;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.clinic-data-view {
  width: 100%;
  height: 100%;
  .date-picker {
    width: 120px;
  }
  .clinic-data-table {
    height: 100%;
    width: 100%;
    .el-input__inner {
      padding: 5px;
    }
  }
}
</style>