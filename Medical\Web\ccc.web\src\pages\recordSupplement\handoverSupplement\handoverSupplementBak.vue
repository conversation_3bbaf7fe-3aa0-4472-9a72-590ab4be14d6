<!--
 * FilePath     : \src\pages\recordSupplement\handoverSupplement\handoverSupplementBak.vue
 * Author       : 郭鹏超
 * Date         : 2021-11-25 09:18
 * LastEditors  : 马超
 * LastEditTime : 2024-09-20 11:20
 * Description  : 交班补录-旧
 * CodeIterationRecord:
-->
<template>
  <div class="handover-record-supplement">
    <!-- 补录患者信息页 -->
    <base-layout v-if="showList" headerHeight="auto" class="handover-record-wrap">
      <div slot="header">
        <span>班别日期：</span>
        <el-date-picker
          v-model="queryStart"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          class="date-select"
          placeholder="选择日期"
        ></el-date-picker>
        <span>-</span>
        <el-date-picker
          v-model="queryEnd"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          type="date"
          class="date-select"
          placeholder="选择日期"
        ></el-date-picker>
        <span class="label">交接类别：</span>
        <el-select
          v-model="handoverType"
          @change="getHandOverType(undefined, handoverType, 'handoverTypeItems')"
          placeholder="请选择"
          class="header-handover-type"
        >
          <el-option v-for="item in handoverTypes" :key="item.key" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <span class="label">交接子类：</span>
        <el-select v-model="handoverTypeItem" placeholder="请选择" class="header-handover-type">
          <el-option
            v-for="item in handoverTypeItems"
            :key="item.key"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button class="query-button" icon="iconfont icon-search" @click="queryList()">查询</el-button>
        <div class="right-wrap">
          <el-button class="add-button" icon="iconfont icon-add" @click="addOrUpdateHandover()">新增</el-button>
        </div>
      </div>
      <!-- 列表 -->
      <el-table :data="tableData" v-loading="tableLoading" border stripe height="100%">
        <el-table-column
          prop="handoverTypeName"
          label="交班类别"
          min-width="80"
          header-align="center"
        ></el-table-column>
        <el-table-column
          prop="handoffStationName"
          label="交班病区"
          min-width="80"
          header-align="center"
        ></el-table-column>
        <el-table-column prop="handoffDeptName" label="交班科别" min-width="80" header-align="center"></el-table-column>
        <el-table-column prop="handoffDay" label="评估日期" width="105" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.handoffDay, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="handoffTime" label="评估时间" width="60" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.handoffTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="handoverShiftName" label="交班班别" width="60" align="center"></el-table-column>
        <el-table-column prop="handoffNurseName" label="交班护士" width="80" align="center"></el-table-column>
        <el-table-column prop="handonStationName" label="接班病区" header-align="center"></el-table-column>
        <el-table-column prop="handonDeptName" label="接班科别" width="80" header-align="center"></el-table-column>
        <el-table-column prop="handonDay" label="交接日期" width="105" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.handonDay, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="handonTime" label="交接时间" width="60" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.handonTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="handonNurseName" label="接班护士" width="80" align="center"></el-table-column>
        <el-table-column label="操作" width="70" align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <i class="iconfont icon-edit" @click="addOrUpdateHandover(scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="deleteHandover(scope.row.handoverID)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </base-layout>
    <!-- 弹出框 -->
    <el-dialog
      v-dialogDrag
      fullscreen
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      :title="dialogTitle"
      custom-class="handover-record-dialog no-footer"
    >
      <base-layout header-height="auto" v-if="dialogVisible">
        <div slot="header">
          <!-- 第一行 -->
          <div class="row">
            <div class="column-1">
              <span class="label">交班类别：</span>
              <el-cascader
                class="handover-type"
                v-model="addHandoverType"
                :options="settings"
                :show-all-levels="false"
                :disabled="isEdit"
                :props="{ expandTrigger: 'hover', children: 'childrenItem', label: 'label', value: 'value' }"
                @change="getSetting()"
              ></el-cascader>
            </div>
            <div class="column-2">
              <shift-selector
                label="交班班别："
                v-model="handoverShift"
                :stationID="handoffStation"
                width="120"
              ></shift-selector>
            </div>
            <div class="column-3">
              <span class="label">交班床位：</span>
              <el-select class="handover-bed" v-model="bedID" placeholder="交班床位" @change="selectBed">
                <el-option v-for="item in bedlist" :key="item.id" :label="item.bedNumber" :value="item.id"></el-option>
              </el-select>
            </div>
          </div>
          <!-- 第二行 -->
          <div class="row">
            <div class="column-1">
              <station-selector
                label="交班病区："
                v-model="handoffStation"
                width="160"
                @select="selectStation"
              ></station-selector>
            </div>
            <div class="column-2">
              <dept-selector
                label="交班科室："
                v-model="handoffDept"
                :stationID="handoffStation"
                width="120"
              ></dept-selector>
            </div>
            <div class="column-3">
              <nurse-selector
                label="交班护士："
                v-model="handoffNurse"
                :stationID="handoffStation"
                width="80"
              ></nurse-selector>
            </div>
            <div class="column-4">
              <span class="label">评估日期：</span>
              <el-date-picker
                class="handover-date"
                v-model="handoffDay"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
            </div>
            <div class="column-5">
              <span class="label">评估时间：</span>
              <el-time-picker
                class="handover-time"
                v-model="handoffTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择时间"
              ></el-time-picker>
            </div>
          </div>
          <!-- 第三行 -->
          <div class="row">
            <div class="column-1">
              <station-selector label="接班病区：" v-model="handonStation" width="160"></station-selector>
            </div>
            <div class="column-2">
              <dept-selector
                label="接班科室："
                v-model="handonDept"
                :stationID="handonStation"
                width="120"
              ></dept-selector>
            </div>
            <div class="column-3">
              <nurse-selector
                label="接班护士："
                v-model="handonNurse"
                :stationID="handonStation"
                filterable
                width="80"
              ></nurse-selector>
            </div>
            <div class="column-4">
              <span class="label">交接日期：</span>
              <el-date-picker
                class="handover-date"
                v-model="handonDay"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
            </div>
            <div class="column-5">
              <span class="label">交接时间：</span>
              <el-time-picker
                class="handover-time"
                v-model="handonTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择时间"
              ></el-time-picker>
            </div>
          </div>
        </div>
        <div v-if="showTemplate" class="handover-sbar" v-loading="loading" :element-loading-text="loadingText">
          <el-button type="primary" icon="iconfont icon-save-button" class="save-btn" @click="save">保 存</el-button>
          <el-tabs class="handover-tabs" v-model="activeName" @tab-click="activeControl">
            <el-tab-pane
              :class="tabPane.value == 'Handover' ? 'handover' : ''"
              v-for="(tabPane, index) in tabPanes"
              :key="index"
              :label="tabPane.label"
              :name="tabPane.value"
            >
              <tabs-layout
                v-if="tabPane.value == 'Assessment'"
                ref="tabsLayout"
                :template-list="templateDatas"
                @button-click="buttonClick"
                @button-record-click="buttonRecordClick"
                @change-values="changeValues"
                @checkTN="checkTN"
                :checkFlag="true"
              />
              <handover
                v-if="tabPane.value == 'Handover'"
                ref="handover"
                :edit-risk="true"
                :componentData="handoverData"
              />
              <patient-evaluation
                v-if="tabPane.value == 'Evaluate'"
                ref="evaluate"
                :inpatientid="inpatinetInfo.inpatientID"
                :stationid="inpatinetInfo.stationID"
                :showcommit="false"
                :all-Flag="false"
                :assessDate="handoffDay"
                :assessTime="handoffTime"
              ></patient-evaluation>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div v-else></div>
      </base-layout>
    </el-dialog>
  </div>
</template>
<script>
import baseLayout from "@/components/BaseLayout.vue";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData.vue";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import nurseSelector from "@/components/selector/nurseSelector";
import userSelector from "@/components/selector/userSelector";
import shiftSelector from "@/components/selector/shiftSelector";
import tabsLayout from "@/components/tabsLayout/index";
import handover from "@/components/handoverSBAR";
import { mapGetters } from "vuex";
import { GetHandOverType, GetSelectSetting, GetClinicalBySettingTypeCodeAndValue } from "@/api/Setting";
import { GetBedListByStationId } from "@/api/BedList";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetPatientHandoverList, DeleteHandoverSupplement } from "@/api/HandoverSupply";
import PatientEvaluation from "@/pages/nursingEvaluation/patientEvaluation";
import {
  GetPatientSBARHandovers,
  SaveSBARHandover,
  UpdateSBARHandover,
  GetPatientAssessHandovers,
  UpdateAssessHandover,
  SaveAssessHandover,
} from "@/api/Handover";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  components: {
    searchPatientData,
    baseLayout,
    stationSelector,
    deptSelector,
    shiftSelector,
    nurseSelector,
    userSelector,
    tabsLayout,
    handover,
    PatientEvaluation,
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  watch: {
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.selectPatient();
        } else {
          this.changePatient();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      queryStart: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      queryEnd: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      //交班类别配置
      handoverTypes: [],
      handoverType: undefined,
      handoverTypeItems: [],
      handoverTypeItem: undefined,
      tableData: [],
      // 对话框变量
      dialogVisible: false,
      dialogTitle: undefined,
      addHandoverType: [],
      handoverShift: undefined,
      handoffStation: undefined,
      handoffDept: undefined,
      handoffNurse: undefined,
      handoffDay: undefined,
      handoffTime: undefined,
      handonStation: undefined,
      handonDept: undefined,
      handonNurse: undefined,
      handonDay: undefined,
      handonTime: undefined,
      //表格开关
      showList: false,
      inpatinetInfo: undefined,
      tableLoading: false,
      bedlist: [],
      bedID: undefined,
      bedNumber: undefined,
      settings: [],
      loading: false,
      loadingText: "",
      activeName: "",
      tabPanes: [],
      templateDatas: [],
      assessDatas: [],
      checkTNFlag: true,
      handoverData: {},
      isEdit: false,
      showTemplate: true,
      showEvaluate: false,
    };
  },
  async created() {
    await this.getSettingSwitchByTypeCode();
  },
  mounted() {
    this.getHandOverType();
    this.getAddHandoverSetting();
  },
  methods: {
    /**
     * description: 选择患者信息时查询数据
     * param {*}
     * return {*}
     */
    selectPatient() {
      if (!this.patientInfo) {
        return;
      }
      this.showList = true;
      this.inpatinetInfo = this.patientInfo;
      this.handoverType = undefined;
      this.queryList();
    },
    /**
     * description: 切换患者信息时清空数据
     * param {*}
     * return {*}
     */
    changePatient() {
      this.showList = false;
      this.tableData = [];
    },
    /**
     * description: 查询交班数据
     * param {*}
     * return {*}
     */
    queryList() {
      let params = {
        inpatientID: this.inpatinetInfo.inpatientID,
        startDate: this.queryStart,
        endDate: this.queryEnd,
        handoverType: this.handoverType,
        recordsCode: this.handoverTypeItem,
      };
      this.tableLoading = true;
      GetPatientHandoverList(params).then((result) => {
        this.tableLoading = false;
        if (this._common.isSuccess(result)) {
          this.tableData = result.data;
        }
      });
    },
    /**
     * description: 新增和修改交班数据
     * param {*} rowData 当前行数据
     * return {*}
     */
    async addOrUpdateHandover(rowData) {
      let patientInfo =
        this.inpatinetInfo.patientName + "【" + this.inpatinetInfo.gender + "-" + this.inpatinetInfo.ageDetail + "】";
      if (rowData) {
        this.isEdit = true;
        this.dialogTitle = patientInfo + "-- 修改交接";
        this.handoverID = rowData.handoverID;
        this.addHandoverType = [rowData.handoverType, rowData.recordsCode];
        this.handoverShift = rowData.handoverShiftID;
        this.handoffStation = rowData.handoffStation;
        this.handoffDept = rowData.handoffDept;
        this.handoffNurse = rowData.handoffNurse;
        this.handoffDay = rowData.handoffDay;
        this.handoffTime = rowData.handoffTime;
        this.handonStation = rowData.handonStation;
        this.handonDept = rowData.handonDept;
        this.handonNurse = rowData.handonNurse;
        this.handonDay = rowData.handonDay;
        this.handonTime = rowData.handonTime;
        this.bedID = rowData.bedID;
      } else {
        this.isEdit = false;
        this.dialogTitle = patientInfo + "-- 新增交接";
        this.handoverID = "handover-" + this._common.guid();
        this.addHandoverType = ["WardHandover", "WardHandover"];
        this.handoffStation = this.inpatinetInfo.stationID;
        this.handoffDept = this.inpatinetInfo.departmentListID;
        this.bedID = this.inpatinetInfo.bedID;
        this.handoffNurse = this.user.userID;
        this.handoffDay = this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd");
        this.handoffTime = this._datetimeUtil.formatDate(new Date(), "hh:mm");
        this.handonStation = undefined;
        this.handonDept = undefined;
        this.handonNurse = "";
        this.handonDay = "";
        this.handonTime = "";
        this.getNowStationShift();
      }
      this.dialogVisible = true;
      await this.getSetting();
      await this.GetBedList();
    },
    /**
     * description: 页签切换事件
     * param {*} tab 切换的页签
     * return {*}
     */
    async activeControl(tab) {
      if (this.activeName == tab.name) {
        return;
      }
      this.activeName = tab.name;
      switch (tab.name) {
        case "Assessment":
          await this.getAssess();
          break;
        case "Handover":
          this.getSBAR();
          break;
      }
    },
    /**
     * description: 获取交班评估模板
     * param {*}
     * return {*}
     */
    async getAssess() {
      if (!this.inpatinetInfo) {
        return;
      }
      // 根据handoverType获取模板
      this.loadingText = "加载中……";
      this.loading = true;
      let params = {
        handoverType: this.addHandoverType[0],
        recordsCode: this.addHandoverType[1],
        handoverID: this.handoverID,
        inpatientID: this.inpatinetInfo.inpatientID,
        sourceType: this.addHandoverType[0],
      };
      await GetPatientAssessHandovers(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result) && result.data) {
          // 出院小结只能有一个，新增时进行提醒
          if (
            !this.isEdit &&
            params.recordsCode.indexOf("Discharge") != -1 &&
            params.handoverID.indexOf(result.data.handoverID) == -1
          ) {
            let handoffDate = this._datetimeUtil.formatDate(result.data.handoffDate, "yyyy-MM-dd hh:mm");
            this._showTip("warning", `患者在${handoffDate}已存在出院小结，不允许重复添加！`);
            this.showTemplate = false;
          }
          this.handoverID = result.data.handoverID;
          this.handoverData = result.data.handoverRecord;
          this.templateDatas = result.data.layoutGroupList;
        }
      });
    },
    /**
     * description: 获取交班的SBAR数据
     * param {*}
     * return {*}
     */
    getSBAR() {
      let params = {};
      if (this.handoverID) {
        params = {
          handoverID: this.handoverID,
          recordsCode: this.addHandoverType[1],
        };
      } else {
        params = {
          recordsCode: this.addHandoverType[1],
          inpatientID: this.inpatinetInfo.inpatientID,
          shiftID: this.handoverShift,
          shiftDate: this.handoffDay,
        };
      }
      this.loadingText = "加载中……";
      this.loading = true;
      GetPatientSBARHandovers(params).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response) && response.data) {
          this.handoverData = response.data.handoverRecord;
        }
      });
    },
    /**
     * description: 获取交班类型的标签
     * param {*}
     * return {*}
     */
    async getSetting() {
      this.tabPanes = [];
      let params = {
        settingTypeCode: "HandoverFunctionShift",
        typeValue: this.addHandoverType[1],
      };
      await GetClinicalBySettingTypeCodeAndValue(params).then((response) => {
        if (this._common.isSuccess(response)) {
          let data = response.data;
          if (data && data.length > 0) {
            for (let i = 0; i < data.length; i++) {
              if (data[i].settingValue == "Evaluate" && !this.showEvaluate) {
                continue;
              }
              if (this.isEdit && data[i].settingValue == "Assessment") {
                continue;
              }
              this.tabPanes.push({
                value: data[i].settingValue,
                label: data[i].description,
              });
            }
            if (this.tabPanes && this.tabPanes.length > 0) {
              this.activeName = "";
              this.activeControl({ name: this.tabPanes[0].value });
            }
          }
        }
      });
    },
    /**
     * description: 保存按钮事件
     * param {*}
     * return {*}
     */
    save() {
      if (!this.addHandoverType || this.addHandoverType.length == 0) {
        this._showTip("warning", "请先选择交班类别！");
        return;
      }
      if (!this.bedID) {
        this._showTip("warning", "请先选择床位！");
        return;
      }
      this.selectBed();
      switch (this.activeName) {
        case "Assessment":
          this.saveAssess();
          break;
        case "Handover":
          this.saveSBAR();
          break;
        case "Evaluate":
          this.saveEvaluate();
          break;
      }
    },
    /**
     * description: 保存评估模板数据
     * param {*}
     * return {*}
     */
    async saveAssess() {
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      // 至少填写一个评估项
      if (this.assessDatas.length == 0) {
        this._showTip("warning", "评估必须填写内容！");
        return;
      }
      let list = this.getSaveDetail();
      if (!list || list.length <= 0) {
        return;
      }
      let patientHandover = {
        assessList: list,
        inpatientID: this.inpatinetInfo.inpatientID,
        bedNumber: this.bedNumber,
        id: this.handoverID,
        stationID: this.handoffStation,
        departmentListID: this.handoffDept,
        handoffNurseID: this.handoffNurse,
        handoffDay: this.handoffDay,
        handoffTime: this.handoffTime,
        handonStationID: this.handonStation,
        handonDepartmentListID: this.handonDept,
        handonNurseID: this.handonNurse,
        handonDate: this.handonDay,
        handonTime: this.handonTime,
        handoverType: this.addHandoverType[0],
        refillFlag: "*",
      };
      this.loadingText = "保存中……";
      this.loading = true;
      if (this.isEdit) {
        await UpdateAssessHandover(patientHandover).then((response) => {
          this.loading = false;
          if (this._common.isSuccess(response)) {
            this._showTip("success", "保存成功！");
            this.getAssess();
            // 自动切换到SBAR
            this.activeControl({ name: "Handover" });
          }
        });
      } else {
        patientHandover.handoverType = this.addHandoverType[0];
        let params = {
          patientHandover: patientHandover,
          recordsCode: this.addHandoverType[1],
        };
        await SaveAssessHandover(params).then((response) => {
          this.loading = false;
          if (this._common.isSuccess(response)) {
            this._showTip("success", "保存成功！");
            this.handoverID = response.data;
            this.isEdit = true;
            this.getAssess();
            // 自动切换到SBAR
            this.activeControl({ name: "Handover" });
          }
        });
      }
    },
    /**
     * description: 获取需要保存的明细
     * param {*}
     * return {*}
     */
    getSaveDetail() {
      if (this.$refs.tabsLayout && this.$refs.tabsLayout.length > 0 && !this.$refs.tabsLayout[0].checkRequire()) {
        return undefined;
      }
      let details = [];
      let flag = true;
      for (let i = 0; i < this.assessDatas.length; i++) {
        let content = this.assessDatas[i];
        // 按钮不处理
        if (content.controlerType.trim() == "B") {
          continue;
        }
        let result = this._common.checkAssessTN(content);
        if (!result.flag) {
          flag = false;
          break;
        }
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          controlerType: content.controlerType,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        details.push(detail);
      }
      if (!flag) {
        return [];
      }
      return details;
    },
    /**
     * description: 保存交班SBAR数据
     * param {*}
     * return {*}
     */
    async saveSBAR() {
      let handoverValue = undefined;
      if (this.$refs.handover && this.$refs.handover.length > 0) {
        handoverValue = this.$refs.handover[0].getValue();
      }
      // 在出院小结页面内容没有渲染完成，去点记保存按钮时，recordsCode会为空值
      if (!handoverValue) {
        this._showTip("warning", "数据加载中，请稍后保存！");
        return;
      }
      if (!handoverValue.recordsCode) {
        handoverValue.recordsCode = this.addHandoverType[1];
      }
      if (!handoverValue.wordNumberFlag) {
        this._showTip("warning", "请精简内容！");
        return;
      }
      handoverValue.inpatientID = this.inpatinetInfo.inpatientID;
      handoverValue.bedNumber = this.bedNumber;
      handoverValue.stationID = this.handoffStation;
      handoverValue.departmentListID = this.handoffDept;
      handoverValue.handoffNurseID = this.handoffNurse;
      handoverValue.handoffDay = this.handoffDay;
      handoverValue.handoffTime = this.handoffTime;
      handoverValue.handOnStationID = this.handonStation;
      handoverValue.handOnDepartmentListID = this.handonDept;
      handoverValue.handonNurseID = this.handonNurse;
      handoverValue.handonDate = this.handonDay;
      handoverValue.handonTime = this.handonTime;
      handoverValue.refillFlag = "*";
      this.loadingText = "保存中……";
      this.loading = true;
      if (this.isEdit) {
        handoverValue.id = this.handoverID;
        await UpdateSBARHandover(handoverValue).then((response) => {
          this.loading = false;
          if (this._common.isSuccess(response)) {
            this._showTip("success", "保存成功！");
            var evaluate = this.tabPanes.find((item) => item.value == "Evaluate");
            if (evaluate) {
              this.activeControl({ name: "Evaluate" });
            } else {
              this.dialogVisible = false;
            }
          }
        });
      } else {
        handoverValue.handoverType = this.addHandoverType[0];
        await SaveSBARHandover(handoverValue).then((response) => {
          this.loading = false;
          if (this._common.isSuccess(response)) {
            this._showTip("success", "保存成功！");
            var evaluate = this.tabPanes.find((item) => item.value == "Evaluate");
            if (evaluate) {
              this.activeControl({ name: "Evaluate" });
            } else {
              this.dialogVisible = false;
            }
          }
        });
      }
      this.queryList();
    },
    /**
     * description: 删除交班数据
     * param {*} handoverID 要删除的行数据记录ID
     * return {*}
     */
    deleteHandover(handoverID) {
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          //确认删除
          let params = {
            handoverID: handoverID,
          };
          DeleteHandoverSupplement(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this.queryList();
            }
          });
        }
      });
    },

    /**
     * description: 评估模板组件返回数据集合
     * param {*} datas 返回选择数据集合
     * return {*}
     */
    changeValues(datas) {
      this.assessDatas = datas;
    },
    /**
     * description: 评估组件检核TN是否通过
     * param {*} flag 通过true 否则false
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 评估组件按钮单击事件回调
     * param {*}
     * return {*}
     */
    buttonClick() {
      this._showTip("warning", "请去对应的专项护理补录数据！");
      return;
    },
    /**
     * description: 评估组件br按钮单击事件回调
     * param {*}
     * return {*}
     */
    buttonRecordClick() {
      this._showTip("warning", "请去风【记录编辑-风险评估】补录数据！");
      return;
    },
    /**
     * description: 切换病区时获取病区床位数据
     * param {*}
     * return {*}
     */
    selectStation() {
      this.GetBedList();
      this.bedID = undefined;
    },
    /**
     * description: 选择床位
     * param {*}
     * return {*}
     */
    selectBed() {
      if (!this.bedID) {
        return;
      }
      let bedInfo = this.bedlist.find((x) => x.id == this.bedID);
      if (bedInfo) {
        this.bedNumber = bedInfo.bedNumber;
      }
    },

    /**
     * description: 获取当前病区班别
     * param {*}
     * return {*}
     */
    getNowStationShift() {
      GetNowStationShiftData().then((result) => {
        if (this._common.isSuccess(result)) {
          let infos = result.data;
          this.handoverShift = infos.nowShift.id;
        }
      });
    },
    /**
     * description: 获取床位列表
     * param {*}
     * return {*}
     */
    GetBedList() {
      this.bedlist = [];
      let params = {
        stationID: this.handoffStation,
      };
      GetBedListByStationId(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.bedlist = result.data;
        }
      });
    },
    /**
     * description: 取得新增二阶交班类别配置
     * params {*}
     * return {*}
     */
    getAddHandoverSetting() {
      this.settings = [];
      GetHandOverType().then((res) => {
        if (this._common.isSuccess(res)) {
          this.settings = res.data;
        }
      });
    },
    /**
     * description: 获取交班类型
     * params {*}
     * return {*}
     * param {*} typeCode
     * param {*} typeValue
     * param {*} key
     */
    async getHandOverType(typeCode = "HandoverSetting", typeValue = "HandoverCategory", key = "handoverTypes") {
      this.handoverTypeItem = undefined;
      let params = {
        typeCode,
        typeValue,
        addDefaultFlag: true,
      };
      await GetSelectSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this[key] = res.data;
        }
      });
    },
    /**
     * description: 评价保存
     * return {*}
     */
    saveEvaluate() {
      this.$refs?.evaluate[0]?.commit();
      this.dialogVisible = false;
    },
    /**
     * description: 获取是否显示转科评价开关
     * return {*}
     */
    async getSettingSwitchByTypeCode() {
      let param = {
        SettingTypeCode: "ShowEvaluateFlag",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.showEvaluate = response.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.handover-record-supplement {
  height: 100%;
  display: flex;
  flex-direction: column;
  .handover-record-wrap {
    flex: auto;
    .date-select {
      width: 110px;
    }
    .right-wrap {
      float: right;
    }
    .label {
      margin-left: 10px;
    }
    .header-handover-type {
      width: 100px;
    }
    .type-select {
      width: 140px;
    }
  }
  .handover-record-dialog.el-dialog {
    .el-dialog__body {
      padding: 0;
      .row {
        display: flex;
        //margin: 5px 0;
        .label {
          margin-left: 10px;
        }
        .column-1 {
          display: flex;
          align-items: center;
          min-width: 240px;
          .label {
            margin-left: 0;
          }
          .handover-type {
            width: 160px;
          }
          .station-selector {
            align-items: center;
            display: flex;
          }
        }
        .column-2 {
          display: flex;
          align-items: center;
          min-width: 200px;
          .dept-selector {
            align-items: center;
            display: flex;
            .el-select {
              margin-left: 4px;
            }
          }
        }
        .column-3 {
          display: flex;
          align-items: center;
          min-width: 160px;
          .handover-bed {
            width: 80px;
          }
          .nurse-selector {
            align-items: center;
            display: flex;
          }
        }
        .column-4 {
          display: flex;
          align-items: center;
          min-width: 200px;
          .handover-date {
            width: 110px;
          }
        }
        .column-5 {
          display: flex;
          align-items: center;
          min-width: 160px;
          .handover-time {
            width: 80px;
          }
        }
      }
      .handover-sbar {
        position: relative;
        height: 100%;
        width: 100%;
        .save-btn {
          position: absolute;
          top: 8px;
          right: 5px;
          z-index: 1000;
        }
        .handover-tabs {
          height: 100%;
          width: 100%;
          border-top: 1px solid #ccc;
        }
        .el-tabs__content {
          height: calc(100% - 45px);
          width: 100%;
          padding: 0 5px;
          box-sizing: border-box;
          .tabs-layout {
            height: 600px;
          }
          .el-tab-pane.handover {
            height: 100%;
            width: 100%;
          }
        }
      }
    }
  }
}
</style>