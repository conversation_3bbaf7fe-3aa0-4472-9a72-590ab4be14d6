/*
 * FilePath     : \.postcssrc.js
 * Author       : 苏军志
 * Date         : 2022-05-08 11:27
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-14 09:33
 * Description  :
 * CodeIterationRecord:
 */
// https://github.com/mi<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>/postcss-load-config
const fs = require("fs");
const path = require("path");
// postcss-plugin-px2rem 换算基数
const rootValue = 192;
// 将换换算基数写入文件 供其他地方调用
const px2remSettingText = `// postcss-plugin-px2rem 换算基数 \n export default ${rootValue}`;
fs.writeFileSync(
  path.join(__dirname, "/static/px2remSetting.js"),
  px2remSettingText
);
module.exports = {
  plugins: {
    "postcss-import": {},
    "postcss-url": {},
    // to edit target browsers: use "browserslist" field in package.json
    autoprefixer: {},
    "postcss-plugin-px2rem": {
      rootValue: rootValue, // 换算基数，设计稿是1920*1080，此处设置为:设计稿宽度/10
      unitPrecision: 8, // 转换为rem后的小数位数
      // propWhiteList: [],  //默认值是一个空数组，这意味着禁用白名单并启用所有属性。
      // propBlackList: [], //黑名单
      // exclude: /src\\pages|static\\scss|src\\components|src\\autoPages/i, //默认false，可以（reg）利用正则表达式排除某些文件夹的方法，例如/(node_module)/ 。如果想把前端UI框架内的px也转换成rem，请把此属性设为默认值
      exclude: /src\\pages/i, //默认false，可以（reg）利用正则表达式排除某些文件夹的方法，例如/(node_module)/ 。如果想把前端UI框架内的px也转换成rem，请把此属性设为默认值
      //要忽略并保留为px的选择器
      selectorBlackList: [
        // 左侧菜单
        ".el-menu",
        // 宏力患者清单卡片组件
        ".patient-bed-one",
        // 日期组件
        ".el-date-picker",
        // 提示组件
        ".el-badge__content",
        // 评估组件
        ".tabs-layout",
        // 评估明细组件
        ".layout-item",
        // 表格单元格
        ".el-table td .cell",
        // 开关组件
        ".el-switch",
        // 对话框组件
        ".el-dialog",
        // 时间线组件
        ".el-timeline",
        ".layout-body-warp",
        // 患者清单 顶部 标签过滤组件
        ".mark-filter",
        ".patient-mark-1",
      ],
      // ignoreIdentifier: false,  //（boolean/string）忽略单个属性的方法，启用ignoreidentifier后，replace将自动设置为true。
      // replace: true, // （布尔值）替换包含REM的规则，而不是添加回退。
      mediaQuery: false, //（布尔值）允许在媒体查询中转换px。
      minPixelValue: 2, //设置要替换的最小像素值(3px会被转rem)。 默认 0
    },
  },
};
