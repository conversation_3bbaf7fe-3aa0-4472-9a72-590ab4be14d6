/* 改变主题色变量 */
$--color-primary: $base-color  !default;
$--color-success: $base-color  !default;
$--color-warning: #e6a23c !default;
$--color-danger: #f56c6c !default;
$--color-info: #909399 !default;

// 默认字体颜色
$--color-text-primary: #000000 !default;
// input框字体颜色
$--input-font-color: #000000 !default;
// table框字体颜色
$--table-font-color: #000000 !default;
// 按钮默认字体颜色
$--button-default-font-color: #000000 !default;
// 时间区间字体颜色
$--color-text-regular: #000000 !default;
$--color-text-secondary: #000000 !default;

/* 改变 icon 字体路径变量，必需 */
$--font-path: "~element-ui/lib/theme-chalk/fonts";
@import "~element-ui/packages/theme-chalk/src/index";

/* loading遮罩层颜色 */
@include b(loading-mask) {
  background-color: rgba(0, 0, 0, 0.5);
}