.el-input__prefix {
  color: $base-color;
  font-size: 20px;
}

.el-picker-panel {
  line-height: 0;
  margin: 0;
  color: #333333;
}

.el-date-editor {
  &.el-input {
    font-size: 14px;
  }

  .el-input__inner {
    padding-right: 5px;
  }

  .el-input__suffix {
    display: none;
  }
}

.el-date-picker {
  width: 240px;

  .el-picker-panel__content {
    width: auto;
    margin: 12px 0;
  }

  .el-date-picker__header {
    background-color: $base-color;
    margin: 0;
    padding: 8px 20px;
  }

  .el-date-picker__header-label,
  .el-picker-panel__icon-btn {
    color: #fff;
    font-weight: 600;
    margin-top: 6px;
    padding: 0;
  }

  .el-date-picker__header-label:hover,
  .el-picker-panel__icon-btn:hover,
  .el-year-table td .cell:hover,
  .el-year-table td.current:not(.disabled) .cell,
  .el-month-table td .cell:hover,
  .el-month-table td.current:not(.disabled) .cell {
    color: #ffc600;
  }

  .el-date-table {
    font-size: 14px;

    th {
      padding-bottom: 12px;
    }

    td {
      padding: 0;
      height: 24px;
      width: 20px;

      span {
        height: 24px;
        width: 24px;
        line-height: 24px;
      }

      &.current {
        &:not(.disabled) {
          span {
            background-color: #ffc600;
          }
        }
      }

      &.available {
        &:hover {
          color: #ffc600;
        }
      }

      &.today {
        span {
          color: $base-color;
        }
      }
    }
  }

  /* 时间 */
  .el-time-panel {
    width: 140px;
  }

  .el-time-spinner__item {
    font-size: 14px;
    color: #333333;
  }

  .el-time-spinner__item.active:not(.disabled),
  .el-time-spinner__item:hover {
    color: #ffc600;
  }

  .el-time-spinner__wrapper {
    .el-scrollbar__wrap {
      &:not(.el-scrollbar__wrap--hidden-default) {
        margin-top: -10px;
        padding-bottom: 8px;
      }
    }
  }

  /* 时间范围 */
  .el-date-editor {
    .el-range__icon {
      color: $base-color;
      font-size: 20px;
      margin-right: 5px;
    }

    .el-range__close-icon {
      display: none;
    }
  }

  .el-time-range-picker {
    .el-time-range-picker__content {
      padding: 10px 0 0 0;
    }

    .el-time-range-picker__header {
      margin-bottom: 15px;
    }
  }
}