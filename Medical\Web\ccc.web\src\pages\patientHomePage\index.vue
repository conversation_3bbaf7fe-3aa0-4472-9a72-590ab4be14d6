<template>
  <base-layout class="patient-home-page" v-loading="temLoading">
    <div slot="header" class="vital-signs-operation">
      <el-tag :type="patientState != patientHomePage.patientStatus ? 'danger' : 'success'" effect="dark">
        &nbsp;{{ patientState }}
      </el-tag>
      <div class="buttons">
        <!-- 血气分析统计 -->
        <el-button v-if="bloodGasChartFlag" type="primary" icon="iconfont icon-line-chart" @click="goBloodGasChart">
          {{ patientHomePage.bloodGasChart }}
        </el-button>
        <!-- 血糖统计 -->
        <el-button type="primary" icon="iconfont icon-line-chart" @click="goGlucoseChart">
          {{ patientHomePage.glucoseChart }}
        </el-button>
        <!-- 生命体征 -->
        <el-button type="primary" icon="iconfont icon-line-chart" @click="goTPRChart">
          {{ patientHomePage.vitalSigns }}
        </el-button>
        <!-- 时间轴 -->
        <el-button type="primary" icon="iconfont icon-time-line" v-if="showTimeLine" @click="goNursingTimeLine">
          {{ patientHomePage.timeAxis }}
        </el-button>
        <!-- 体温单 -->
        <el-button type="primary" icon="iconfont icon-temperature" @click="getTemperatureData">
          {{ patientHomePage.temperatureSheet }}
        </el-button>
        <!-- 长期医嘱 -->
        <el-button type="primary" icon="iconfont icon-advice" v-if="showRegulaOrder" @click="loadingdoctorAdvice">
          {{ patientHomePage.LDA }}
        </el-button>
        <!-- 历史问题 -->
        <el-button class="query-button" icon="iconfont icon-delay" @click="loadingHistoryNursingProblem">
          {{ patientHomePage.historicalProblems }}
        </el-button>
        <!-- 返回 -->
        <el-button class="print-button" icon="iconfont icon-back" @click="$router.go(-1)">
          {{ button.back }}
        </el-button>
      </div>
    </div>

    <div class="home-patient-table">
      <div class="risk-plan">
        <!-- 护理问题及措施 及历史护理措施问题-->
        <div class="nursing-plan" v-loading="nursingPlanLoading">
          <el-table :resizable="false" height="100%" stripe border :data="patientProblemToIntervention">
            <!-- 护理问题 -->
            <el-table-column
              :resizable="false"
              :label="patientHomePage.nursingProblems"
              prop="nursingPlan"
              :width="tableWidth.nursingProblem"
            >
              <template slot-scope="scope">
                {{ scope.row.nursingProblem }}
              </template>
            </el-table-column>
            <!-- 护理措施 -->
            <el-table-column :resizable="false" :label="patientHomePage.nursingIntervention">
              <template slot-scope="scope">
                <div v-for="(item, index) in scope.row.interventions" :key="index">
                  {{ item }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="risk-assessment">
          <el-table height="100%" :data="riskTable" stripe border>
            <el-table-column :resizable="false" width="35">
              <template slot-scope="scope">
                <i @click="getPatientTrend(scope.row)" class="iconfont icon-chart"></i>
              </template>
            </el-table-column>
            <!-- 风险评估 -->
            <el-table-column :resizable="false" prop="recordName" :label="patientHomePage.riskTable"></el-table-column>
            <!-- 分数 -->
            <el-table-column
              :resizable="false"
              prop="point"
              :label="patientHomePage.riskGrade"
              min-width="50"
            ></el-table-column>
            <!-- 风险等级 -->
            <el-table-column
              :resizable="false"
              prop="scoreRangeContent"
              min-width="80"
              :label="patientHomePage.riskLevel"
            ></el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 每日交班 -->
      <div class="handover">
        <div class="handover-info">
          <el-table :data="handOverList" height="100%" stripe border>
            <!-- SBAR -->
            <el-table-column
              :resizable="false"
              prop="title"
              :label="patientHomePage.SBAR"
              :width="tableWidth.SBAR"
            ></el-table-column>
            <!-- 交班内容 -->
            <el-table-column :resizable="false" :label="patientHomePage.shiftRecords">
              <template slot-scope="scope">
                <div class="content" v-html="scope.row.content"></div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="handover-pic">
          <!-- 人体图 -->
          <div class="pic-tittle">
            <span>{{ patientHomePage.bodyMaps }}</span>
          </div>
          <div class="pic-zone">
            <img :src="humanPicUrl" :alt="patientHomePage.bodyMaps" />
          </div>
        </div>
      </div>
    </div>
    <div style="clear: both"></div>
    <!-- 历史护理问题 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="patientHomePage.historicalProblems"
      :visible.sync="historyNuringProblemVisiableControlObj"
    >
      <el-table :resizable="false" :data="patientHistoryProblem">
        <el-table-column :resizable="false" :label="patientHomePage.nursingProblem" prop="problem"></el-table-column>
        <el-table-column :resizable="false" :label="patientHomePage.nursingOutcome" prop="outCome"></el-table-column>
        <el-table-column :resizable="false" :label="patientHomePage.startDate" prop="startDate">
          <template slot-scope="scope">{{ scope.row.startDate.substring(0, 10) }}</template>
        </el-table-column>
        <el-table-column :resizable="false" :label="patientHomePage.endDate" prop="endDate">
          <template slot-scope="scope">{{ scope.row.endDate.substring(0, 10) }}</template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 医嘱 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="patientHomePage.LDA"
      :visible.sync="doctorAdviceVisiable"
    >
      <el-table :data="patientLongTermOrder">
        <el-table-column :resizable="false" :label="patientHomePage.content">
          <template slot-scope="scope">
            <div v-for="item in scope.row.orderContents" :key="item.patientOrderDetailID">
              {{ item.content }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :resizable="false"
          :label="patientHomePage.frequency"
          prop="frequencyDescription"
        ></el-table-column>
        <el-table-column :resizable="false" :label="patientHomePage.order" prop="orderDescription"></el-table-column>
        <el-table-column :resizable="false" :label="patientHomePage.doctor" prop="doctor" width="100"></el-table-column>
        <el-table-column :resizable="false" :label="patientHomePage.startDate" prop="startDateTime">
          <template slot-scope="scope">
            {{ scope.row.startDateTime.substring(0, 10) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 体温单 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :fullscreen="true"
      width="60%"
      custom-class="temperature-list-dialog"
      :title="getTemperatureTitle(patientHomePage.temperatureSheet)"
      :visible.sync="temperatureVisiable"
      v-if="temperatureVisiable"
      v-loading="temperatureLoadingBoole"
    >
      <temperature-list v-model="temperatureData"></temperature-list>
    </el-dialog>
    <!-- 风险评估趋势 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      width="60%"
      custom-class="risk-trend"
      :title="patientHomePage.tendencyChart"
      :visible.sync="riskTrendVisiable"
    >
      <ve-line :data="chartData" :settings="chartSettings" :extend="extend"></ve-line>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :fullscreen="true"
      title="血气分析"
      :visible.sync="bloodGasChartVisiable"
      v-if="bloodGasChartVisiable"
    >
      <blood-gas-chart :patient="patient"></blood-gas-chart>
    </el-dialog>
  </base-layout>
</template>
<script>
import { mapGetters } from "vuex";
import datetimeUtil from "@/utils/datetimeUtil";
import { GetPaitentProblemToIntervention } from "@/api/NursingPlan";
import { GetLastHandOver, GetPatientVitalSign } from "@/api/Handover";
import { GetPatientCriticallyill } from "@/api/PatientProfile";
import { GetLongTermOrdersByInpatientIDAsync } from "@/api/PatientOrder";
import { GetHistoryPatientProblems } from "@/api/PatientProblem";
import { GetPatientVitalSignData } from "@/api/VitalSign";
import { GetPatientScoreMainListByInpatient } from "@/api/PatientScore";
import { GetLastPatientRisk } from "@/api/PatientScore";
import baseLayout from "@/components/BaseLayout";
import temperatureList from "@/components/TemperatureList";
import { GetViewShows } from "@/api/Setting";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import bloodGasChart from "@/autoPages/patientHomePage/bloodGasChart/index";
export default {
  components: {
    baseLayout,
    temperatureList,
    bloodGasChart,
  },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
    }),
    button() {
      return this.$t("button");
    },
    patientHomePage() {
      return this.$t("patientHomePage");
    },
    content() {
      return [
        this.patientHomePage.situation,
        this.patientHomePage.background,
        this.patientHomePage.assessment,
        this.patientHomePage.recommendation,
      ];
    },
    tableWidth() {
      return this.$t("patientHomePage.tableWidth");
    },
    //V-chart设置对象
    chartSettings() {
      return {
        labelMap: {
          scorePoint: this.patientHomePage.riskGrade,
        },
        //最大值
        max: [],
        //最小值
        min: [],
      };
    },
  },
  watch: {
    "patient.inpatientID": {
      handler(newVal) {
        if (!newVal) return;
        this.init();
      },
    },
  },
  data() {
    return {
      //体温单loading
      temperatureLoadingBoole: false,
      control: true,
      temperatureDate: [],
      // 交班数据集合
      handOverList: [],
      //体温单tabs和PDF数据
      temperatureData: {},
      //体温单弹出框控制
      temperatureVisiable: false,
      //交班集合
      //交班对象属性集合
      attributeName: ["situation", "background", "assement", "recommendation"],
      //人体图URL
      humanPicUrl: "",
      // 风险表
      riskTable: [],
      //病人问题对措施
      patientProblemToIntervention: [],
      //病人长期医嘱集合
      patientLongTermOrder: [],
      // 历史护理问题控制
      historyNuringProblemVisiableControlObj: false,
      // 长期医嘱控制
      doctorAdviceVisiable: false,
      // 风险趋势控制
      riskTrendVisiable: false,
      //历史问题集合
      patientHistoryProblem: [],
      // 病人状态
      patientState: "",

      temLoading: false,
      // V-charts数据
      chartData: {
        columns: ["addDate", "scorePoint"],
        rows: [],
      },

      //v-chart扩展配置
      extend: {
        "xAxis.0.axisLabel.rotate": 45,
        series: {
          label: {
            normal: {
              show: true,
            },
          },
        },
      },
      //病人URL数组
      temperaturePicArray: [],
      nursingPlanLoading: false,
      //显示时间轴
      showTimeLine: false,
      //显示长期医嘱
      showRegulaOrder: false,
      //血气分析按钮开关
      bloodGasChartFlag: false,
      //血气分析弹窗开关
      bloodGasChartVisiable: false,
    };
  },
  mounted() {
    //加上判断检验是否呈现
    let params = {
      settingTypeCode: "ViewDispable",
      typeValue: ["TimeLine", "RegularOrder"],
    };
    GetViewShows(params).then((response) => {
      if (this._common.isSuccess(response)) {
        response.data.forEach((element) => {
          if (element == "TimeLine") {
            this.shoshowTimeLine = true;
          }
          if (element == "RegularOrder") {
            this.showRegulaOrder = true;
          }
        });
      }
    });

    // 设置不可切换病人
    this._sendBroadcast("setPatientSwitch", false);
    if (!this.patient) return;
    this.init();
  },
  methods: {
    getTemperatureTitle(value) {
      let title = "";
      title =
        this.patient.bedNumber +
        " - " +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        this.patient.age +
        "】" +
        "-- " +
        value;
      return title;
    },
    async init() {
      this.control = true;
      //风险表
      this.riskTable = [];
      //交班数据
      this.handOverList = [];
      //历史护理问题表
      this.patientHistoryProblem = [];
      //病人病危状态
      this.patientState = "";
      //病人护理问题对措施数组
      this.patientProblemToIntervention = [];
      //病人长期医嘱
      this.patientLongTermOrder = [];
      //清空数据集合
      this.temperatureDate = [];
      //清空数据集合
      this.temperatureData = {};
      //清空体温单
      this.temperaturePicArray = [];
      //病人发生变化要处理的逻辑
      this.getPatientProblemToIntervention();
      //获得病人病危病重数据
      this.getpatientcriticallyIllState();
      //获得病人最近一次交班数据
      this.getLastHandOver();
      //获取病人最近的风险评估
      this.getLastPatientRisk();
      //获得病人体温单
      // await this.getTemperatureData();
      //按钮禁用
      this.control = false;
      //血气分析按钮开关配置
      this.getbloodGasSwitch();
    },
    getLastHandOver() {
      let params = {
        stationID: this.patient.stationID,
        inPatientID: this.patient.inpatientID,
      };
      this.handOverList = [];
      GetLastHandOver(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.humanPicUrl = "data:image/jpeg;base64," + response.data.bodyPartImage;

          for (let i = 0; i < 4; i++) {
            let handOverItem = {
              title: this.content[i],
              content: response.data[this.attributeName[i]],
            };
            this.handOverList.push(handOverItem);
          }
        } else {
          this.humanPicUrl = "";
        }
      });
    },

    getPatientProblemToIntervention() {
      this.nursingPlanLoading = true;
      let params = {
        inpatientID: this.patient.inpatientID,
        stationID: this.patient.stationID,
      };
      GetPaitentProblemToIntervention(params).then((response) => {
        this.nursingPlanLoading = false;
        if (this._common.isSuccess(response)) {
          this.patientProblemToIntervention = response.data;
        }
      });
    },

    getPatientOrder() {
      let params = { inPatientID: this.patient.inpatientID };
      GetLongTermOrdersByInpatientIDAsync(params).then((response) => {
        if (this._common.isSuccess(response)) this.patientLongTermOrder = response.data;
      });
    },
    getHistoryPatientProblem() {
      let params = {
        inpatientID: this.patient.inpatientID,
        stationID: this.patient.stationID,
      };
      GetHistoryPatientProblems(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.patientHistoryProblem = response.data;
        }
      });
    },
    goTPRChart() {
      this.$router.push({
        name: "tprChart",
      });
    },
    goNursingTimeLine() {
      this.$router.push({
        name: "patientNursingTimeLine",
      });
    },
    //获取病人体温数据，生成病人体温单，存储到集合中
    async getTemperatureData() {
      if (!this.patient) {
        this._showTip("error", "获取病人基本信息失败");
        return;
      }
      this.temperatureVisiable = true;
      this.temperatureLoadingBoole = true;
      let params = {
        inPatientID: this.patient.inpatientID,
        startTime: this.patient.admissionDate,
        endTime: datetimeUtil.formatDate(new Date()),
      };
      await GetPatientVitalSignData(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.temperatureData = response.data;
          this.temperatureLoadingBoole = false;
        }
      });
    },
    loadingdoctorAdvice() {
      this.getPatientOrder();
      this.doctorAdviceVisiable = true;
    },
    loadingHistoryNursingProblem() {
      this.getHistoryPatientProblem();
      this.historyNuringProblemVisiableControlObj = true;
    },

    //click时间触发
    async highLight(item, index) {
      this.$refs.temperatureCarousel.setActiveItem(index);
    },
    getpatientcriticallyIllState() {
      let params = { inpatientID: this.patient.inpatientID };
      //根据病人ID查询
      GetPatientCriticallyill(params).then((response) => {
        this.patientState = response.data;
      });
    },
    //获得病人风险变化趋势
    getPatientTrend(row) {
      let params = {
        recordListID: row.recordID,
        inpatientID: this.patient.inpatientID,
        orderBy: 1,
        onlyRisk: true,
      };
      GetPatientScoreMainListByInpatient(params).then((response) => {
        if (this._common.isSuccess(response)) {
          for (let i = 0; i < response.data.length; i++) {
            response.data[i].addDate = response.data[i].addDate.substring(0, 16);
          }
          //设置v-chrat数据
          this.$set(this.chartData, "rows", response.data);
          //设置风险评分的最高分
          this.$set(this.chartSettings, "max", [row.totalNumber]);
          this.$set(this.chartSettings, "min", [row.bottomValue]);
        }
      });
      this.riskTrendVisiable = true;
    },
    /**
     * description: 获取病人最近风险
     * return {*}
     */
    getLastPatientRisk() {
      let params = { inpatientID: this.patient.inpatientID };
      GetLastPatientRisk(params).then((response) => {
        if (this._common.isSuccess(response)) this.riskTable = response.data;
      });
    },
    /**
     * description: 血糖统计跳转
     * return {*}
     */
    goGlucoseChart() {
      this.$router.push({
        name: "glucoseChart",
        query: {
          patientInfo: this.patient,
        },
      });
    },
    /**
     * description: 血气分析跳转
     * return {*}
     */
    goBloodGasChart() {
      this.bloodGasChartVisiable = true;
    },
    /**
     * description: 血气分析开关按钮
     * return {*}
     */
    async getbloodGasSwitch() {
      let param = {
        SettingTypeCode: "BloodGasChartButton",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.bloodGasChartFlag = response.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.patient-home-page {
  .vital-signs-operation {
    .buttons {
      float: right;
      .el-button {
        margin: 2px;
      }
    }
  }
  .home-patient-table {
    position: absolute;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    .risk-plan {
      width: 100%;
      height: 50%;
      display: flex;
      .div {
        margin-bottom: 10px;
        box-sizing: border-box;
      }
      .nursing-plan {
        width: 70%;
        height: 100%;
      }
      .risk-assessment {
        flex: auto;
        height: 100%;
        box-sizing: border-box;
        padding-left: 10px;
        overflow: auto;
        .iconfont.icon-chart {
          color: #ff0000;
          cursor: pointer;
          font-size: 20px;
          margin-top: 5px;
        }
      }
    }
    .handover {
      width: 100%;
      height: 50%;
      display: flex;
      .handover-info {
        height: 100%;
        width: 70%;
      }
      .handover-pic {
        height: 100%;
        box-sizing: border-box;
        padding-left: 10px;

        .pic-tittle {
          height: 10%;
          width: 100%;
          text-align: center;
          background-color: #ebf7df;
        }
        .pic-zone {
          height: 90%;
          width: 100%;
          img {
            height: 98%;
            width: 100%;
          }
        }
      }
    }
  }
  //GPC修改
  .temperature-list-dialog {
    & .el-dialog__body {
      height: calc(100% - 35px);
    }
  }
  .risk-trend {
    height: 70%;
  }
}
</style>
