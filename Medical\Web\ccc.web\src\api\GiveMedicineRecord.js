/*
 * FilePath     : \src\api\GiveMedicineRecord.js
 * Author       : AI Assistant
 * Date         : 2025-07-21
 * LastEditors  : AI Assistant
 * LastEditTime : 2025-07-21
 * Description  : 给药记录补录API
 */
import http from "../utils/ajax";
const baseUrl = "/GiveMedicineRecord";
export const urls = {
  GetGiveMedicineRecordTableData: baseUrl + "/GetGiveMedicineRecordTableData",
  SaveGiveMedicineRecord: baseUrl + "/SaveGiveMedicineRecord",
  DeleteGiveMedicineRecord: baseUrl + "/DeleteGiveMedicineRecord",
};
/**
 * description: 获取给药记录表格数据
 * param {Object} params - 查询参数
 * return {Promise}
 */
export const GetGiveMedicineRecordTableData = (params) => {
  // 模拟数据，后续替换为真实API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: "1",
          inpatientID: params.inpatientID,
          patientID: "P001",
          performDate: "2025-04-29",
          performTime: "09:00",
          category: "长期",
          groupNumber: "1",
          orderContent: "维生素C注射液 1g",
          route: "ivgtt",
          dosage: "每天一次",
          frequency: "qd",
          doctor: "李医生",
          endTime: "15:05",
          performer: "张护士",
          userID: "U001",
          orderRefillFlag: false,
        },
        {
          id: "2",
          inpatientID: params.inpatientID,
          patientID: "P001",
          performDate: "2025-04-29",
          performTime: "14:00",
          category: "临时",
          groupNumber: "2",
          orderContent: "生理盐水 250ml",
          route: "iv",
          dosage: "250ml",
          frequency: "st",
          doctor: "王医生",
          endTime: "14:30",
          performer: "李护士",
          userID: "U002",
          orderRefillFlag: true,
        },
        {
          id: "3",
          inpatientID: params.inpatientID,
          patientID: "P001",
          performDate: "2025-04-29",
          performTime: "18:00",
          category: "长期",
          groupNumber: "3",
          orderContent: "头孢曲松钠 2g",
          route: "ivgtt",
          dosage: "2g bid",
          frequency: "bid",
          doctor: "赵医生",
          endTime: "18:45",
          performer: "王护士",
          userID: "U003",
          orderRefillFlag: true,
        },
      ];
      resolve({
        success: true,
        data: mockData,
        message: "获取成功",
      });
    }, 500);
  });
};
/**
 * description: 保存给药记录
 * param {Object} params - 保存参数
 * return {Promise}
 */
export const SaveGiveMedicineRecord = (params) => {
  // 模拟数据，后续替换为真实API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: {
          id: params.id || Date.now().toString(),
          ...params,
        },
        message: "保存成功",
      });
    }, 800);
  });
};
/**
 * description: 删除给药记录
 * param {Object} params - 删除参数
 * return {Promise}
 */
export const DeleteGiveMedicineRecord = (params) => {
  // 模拟数据，后续替换为真实API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: null,
        message: "删除成功",
      });
    }, 500);
  });
};
