<!--
 * FilePath     : \src\autoPages\monitoringScheduler\component\cellTypes\checkboxCell.vue
 * Author       : 杨欣欣
 * Date         : 2024-06-18 18:48
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-06-27 14:34
 * Description  : 自定义列内容（复选框类）
 * CodeIterationRecord: 
 -->
<template functional>
  <el-checkbox
    :checked="Boolean(props.row[props.column.index].assessValue)"
    @change="$options.setValue($event, props), listeners.change(props.row)"
  />
</template>
<script>
export default {
  name: "checkboxCell",
  /**
   * @description: 设置C类单元格的值
   * @param event 事件结果
   * @param props 传入的参数
   * @return
   */
  setValue(event, props) {
    props.row[props.column.index].assessValue = event ? props.row[props.column.index].showName : "";
  },
};
</script>
