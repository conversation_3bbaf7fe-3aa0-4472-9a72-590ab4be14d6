<!--
 * FilePath     : \src\pages\transfusion\transfusionCheckIn.vue
 * Author       : 杨欣欣
 * Date         : 2021-10-23 10:48
 * LastEditors  : 马超
 * LastEditTime : 2024-04-26 15:39
 * Description  : 
-->
<template>
  <base-layout class="transfusion-check-in">
    <div slot="header">
      <span class="label">病区：</span>
      <el-select :disabled="stationList.length <= 1" v-model="currentStationID" placeholder="请选择病区"
        class="select-station">
        <el-option v-for="item in stationList" :key="item.stationID" :label="item.stationName"
          :value="item.stationID"></el-option>
      </el-select>

      <span>开始日期：</span>
      <el-date-picker class="datepicker" v-model="startDate" type="date" :clearable="false" placeholder="选择日期"
        format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="getTransfusionRecords"></el-date-picker>
      <span>-</span>
      <el-date-picker class="datepicker" v-model="endDate" type="date" :clearable="false" placeholder="选择日期"
        format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="getTransfusionRecords"></el-date-picker>
    </div>
    <el-table slot-scope="data" :height="data.height" v-loading="loading" :element-loading-text="loadingText"
      :data="tableData" border stripe>
      <el-table-column prop="patientName" label="姓名" width="60" header-align="center"></el-table-column>
      <el-table-column prop="bedNumber" label="床号" width="60" header-align="center"></el-table-column>
      <el-table-column prop="age" label="年龄" width="60" header-align="center"></el-table-column>
      <el-table-column prop="gender" label="性别" align="center" width="60"></el-table-column>
      <el-table-column prop="localCaseNumber" label="住院号" width="90"></el-table-column>
      <el-table-column prop="patientABO" label="患者血型" width="50" header-align="center"></el-table-column>
      <el-table-column prop="bloodDonorBagsCode" label="血袋号" width="110" header-align="center"></el-table-column>
      <el-table-column prop="sendBloodDate" label="发血时间" width="140" header-align="center"></el-table-column>
      <el-table-column prop="transfusionStartDate" sortable label="输血开始时间" width="140" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{
            value: scope.row.transfusionStartDate,
            type: 'dateTime',
            format: 'yyyy-MM-dd hh:mm'
          }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="sendBloodInterval" label="发血间隔" width="90" header-align="center">
        <template slot-scope="scope">
          <span :class="{ 'bold-red': scope.row.sendBloodInterval > intervalValue }">
            {{ scope.row.sendBloodInterval + "分钟" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="bloodName" label="血品名称" width="180"></el-table-column>
      <el-table-column prop="bloodDonorABO" label="血袋血型" width="50" header-align="center"></el-table-column>
      <el-table-column prop="bloodTransfusionVolume" label="输血量" width="90" header-align="center">
        <template slot-scope="scope">
          {{
            scope.row.bloodTransfusionVolume + scope.row.bloodTransfusionUnit
          }}
        </template>
      </el-table-column>
      <el-table-column prop="transfusionEndDate" sortable label="输血结束时间" width="160" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{
            value: scope.row.transfusionEndDate,
            type: 'dateTime',
            format: 'yyyy-MM-dd hh:mm'
          }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="transfusionEmployee" label="输血人" width="80"></el-table-column>
      <el-table-column prop="transfusionCheckEmployee" label="核对人" width="70"></el-table-column>
      <el-table-column prop="transfusionReaction" label="输血反应">
        <template slot-scope="scope">
          {{
            scope.row.transfusionReaction == undefined
              ? "无不良反应"
              : scope.row.transfusionReaction
          }}
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>

<script>
import stationSelector from "@/components/selector/stationSelector";
import { GetEmployeeSwitchStationList } from "@/api/User";
import { GetStationByID } from "@/api/Station";
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
import { GetTransfusionRecordsCheckForm } from "@/api/PatientTransfusion";
import { GetSettingValueByTypeCodeAsync } from "@/api/SettingDescription";
export default {
  components: {
    stationSelector,
    baseLayout
  },
  data() {
    return {
      tableData: [],
      currentStationID: undefined,
      stationList: [],
      startDate: undefined,
      endDate: undefined,
      loading: true,
      loadingText: "加载中……",
      intervalValue: 30
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser"
    })
  },

  watch: {
    currentStationID: {
      handler() {
        this.getTransfusionRecords();
      }
    }
  },
  created() {
    //默认获取近一周
    this.startDate = this._datetimeUtil.addDate(
      this._datetimeUtil.getNow(),
      -7
    );
    this.endDate = this._datetimeUtil.getNow();
    // 获取病区列表
    this.getStationList();
    let params = {
      settingTypeCode: "SendBloodInterval"
    };
    GetSettingValueByTypeCodeAsync(params).then(res => {
      if (this._common.isSuccess(res)) {
        this.intervalValue = res.data;
      }
    });
  },
  methods: {
    async getTransfusionRecords() {
      this.loading = true;
      let endDate = new Date(this.endDate);
      endDate.setHours(23, 59, 59);
      let params = {
        stationID: this.currentStationID,
        //开始日期设置为当天的0点
        startDate: this._datetimeUtil.formatDate(this.startDate, "yyyy-MM-dd"),
        //结束日期设置为当天的23点59分59秒
        endDate: endDate
      };
      await GetTransfusionRecordsCheckForm(params).then(res => {
        if (this._common.isSuccess(res)) {
          this.tableData = [];
          this.tableData = res.data;
          this.loading = false;
        }
      });
    },
    //获取病区下拉框数据
    async getStationList() {
      await GetEmployeeSwitchStationList({ index: Math.random() }).then(
        async result => {
          if (this._common.isSuccess(result)) {
            if (result.data && result.data.length > 0) {
              this.currentStationID = this.user.stationID;
              this.stationList = result.data;
            } else {
              let params = {
                ID: this.user.stationID
              };
              await GetStationByID(params).then(result => {
                if (this._common.isSuccess(result)) {
                  this.stationList.push({
                    stationID: result.data.id,
                    stationName: result.data.stationName
                  });
                  this.currentStationID = this.user.stationID;
                }
              });
            }
          }
        }
      );
    }
  }
};
</script>
<style lang="scss">
.transfusion-check-in {
  .el-select.select-station {
    width: 150px;
    height: 41px;

    .el-input {
      height: 100%;

      &.is-disabled {
        .el-input__suffix {
          display: none;
        }

        .el-input__inner {
          background-color: #ffffff;
        }
      }

      .el-input__inner {
        height: 32px !important;
        padding: 0 30px 0 10px;
        border: 1px solid $base-color;
        color: #000000 !important;
      }
    }
  }
  .bold-red {
    font-weight: bold;
    color: red;
  }
}
</style>
