<!--
 * FilePath     : \src\components\tabsLayout\index.vue
 * Author       : 苏军志
 * Date         : 2020-05-05 00:15
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-15 16:34
 * Description  : 评估模板组件
 * CodeIterationRecord:
 2022-07-28 connectStr增加MT类型 -En
 * 可用参数：
 1、templateList 组装好的模板列表，必须
 2、imageMaxNum 图片最大数量，非必必须
 3、showNav 显示底部导航按钮，默认false
 4、disabled 是否为预览模式，默认false
 5、tabPosition  页签显示位置，参数选项 left top bottom right，默认top
 6、assessTime 评估时间，不传默认当前时间，组件中D、DT、TM类型的defaultValue为'A'或'I'的会取该值
         6.1、defaultValue为'A'表示assess，项目的值始终会和assessTime保持一致，
         6.2、defaultValue为'I'表示init，项目只有初始化的时候才会用assessTime赋值，若已有值则不会改变
 7、checkFlag 是否需要检核互斥，检核包含：
         7.1、textToText输入框与输入框的互斥（目前只有抢救记录在用）
         7.2、checkToRadio复选与单选的互斥检核
         7.3、radioToCheck单选与复选的互斥检核
         7.4、checkToCheck复选与复选的互斥检核
 8、gender 性别，身体部位类型需要
 9、splicedSelected 是否组装选择项目，配合splicedContent事件使用
 10、model，组件编辑模式，add新增，edit修改，默认undefined，
            当为add时，初始化时默认隐藏且为默认勾选的项目不勾选
 11、notSplicedContentIDArr和notSplicedContentGroupIDArr 不需要自动拼接的assessListID和GroupID集合
 12、showStyle，显示样式Assess：评估模式，Table：表格模式，默认Assess模式
 13、hiddenSingleTab，只有一个页签时是否隐藏，false不隐藏，true隐藏，默认false
* 事件
 1、@change-values 选择项目的回调事件，返回所有有效项目列表
 2、@button-click 按钮点击回调事件， 返回按钮信息
 3、@button-record-click BR类按钮点击回调事件， 返回按钮信息
 4、@button-display-click BD类按钮点击回调事件， 返回按钮信息
 5、@checkTN 检核TN类型录入内容是否通过检核回调事件，有一个TN项目检核不通过就返回false，否则返回true
 6、@upload-img 上传图片回调事件，返回所有图片
 7、@splicedContent 返回选择项目组装好的字符串，splicedSelected属性必须为true才可使用
 8、@changeItem 异动项目，第一个参数为异动项目，第二个参数为true表示勾选，false表示取消勾选
 * 方法
 1、提供checkRequire方法检核必选项是否勾选，父组件中可以通过ref调用此方法
 2、提供updateButtonItem方法更新B、BR类项目，父组件中可以通过ref调用此方法
-->
<template>
  <!-- ctrl + shift + S 显示永久隐藏项目（disableGroup = -1） -->
  <!-- ctrl + shift + H 隐藏永久隐藏项目（disableGroup = -1） -->
  <div
    class="tabs-layout"
    tabindex="0"
    @keydown.ctrl.shift.83="toggleHidden(true)"
    @keyup.ctrl.shift.72="toggleHidden(false)"
    ref="tabsLayout"
  >
    <el-tabs
      :class="['tabs-layout-wrap', { 'hidden-tabs': hiddenSingleTab }, { 'table-mode': showStyle == 'Table' }]"
      type="border-card"
      v-model="curIndex"
      :tab-position="position"
    >
      <el-tab-pane
        class="layout-pane"
        :label="title.title"
        v-for="(title, index) in templateList"
        :key="index"
        :name="index + 1 + ''"
      >
        <table v-loading="disabled" class="layout-wrap">
          <template v-for="(detail, dIndex) in title.groups">
            <tr
              :key="dIndex"
              :class="[
                'context',
                {
                  hidden:
                    (!showHidden && detail.disableGroup.indexOf('-1') != -1) ||
                    (detail.disableGroup == '0' && !detail.show) ||
                    (detail.disableGroup == '1' && !detail.show),
                  'show-hidden': showHidden && detail.disableGroup.indexOf('-1') != -1,
                },
                { 'show-stripe': dIndex % 2 == 0 },
              ]"
            >
              <td class="label-td">
                <div
                  v-if="detail.itemName"
                  v-longpress="{ function: 'showMessage', params: detail.description }"
                  :title="detail.description"
                  :style="{ backgroundColor: detail.backgroundColor ? detail.backgroundColor.trim() : '#ffffff' }"
                >
                  <span v-if="detail.requireFlag" class="require">*</span>
                  {{ detail.itemName.trim() }}
                  <span v-if="showStyle !== 'Table'">：</span>
                </div>
              </td>
              <td class="content-td">
                <template v-if="detail.controlerType && detail.controlerType.trim() !== 'L'">
                  <div
                    :class="[
                      'context-item',
                      {
                        'pl-item': detail.controlerType && detail.controlerType.trim() == 'PL',
                        'text-item':
                          detail.controlerType &&
                          (detail.controlerType.trim() == 'T' ||
                            detail.controlerType.trim() == 'TN' ||
                            detail.controlerType.trim() == 'E'),
                        'button-badge-string':
                          detail.controlerType &&
                          (detail.controlerType.trim() == 'B' || detail.controlerType.trim() == 'BR') &&
                          detail.assessValue &&
                          !/^\d+$/.test(detail.assessValue) &&
                          detail.assessValue.trim().toLowerCase() !== 'true',
                        'new-line': detail.newLineFlag,
                        hidden:
                          (!showHidden && detail.disableGroup.indexOf('-1') != -1) ||
                          (detail.disableGroup == '0' && !detail.show) ||
                          (detail.disableGroup == '1' && !detail.show),
                        'show-hidden': showHidden && detail.disableGroup.indexOf('-1') != -1,
                      },
                    ]"
                  >
                    <layout-item
                      :item="detail"
                      :items="[detail]"
                      :formulaParamOptions="title.formulaParamOptions"
                      :imageMaxNum="imageMaxNum"
                      :assessTime="time"
                      :gender="gender"
                      :custom-methods="customMethods"
                      @b-change-value="bChangeItem($event, [detail], title.bookMarkID)"
                      @br-click="brClick($event)"
                      @change-value="changeItem($event, [detail], title.bookMarkID)"
                      @changeImg="changeImg"
                      @inputEnter="enterEvent"
                    />
                  </div>
                </template>
                <template v-if="detail.contents">
                  <template v-for="(item, iIndex) in detail.contents">
                    <br
                      :class="'new-line-br' + item.assessListID"
                      v-if="
                        item.defaultValue != '0' &&
                        item.newLineFlag &&
                        item.show != false &&
                        iIndex > 0 &&
                        (!detail.contents[iIndex - 1].childList ||
                          (detail.contents[iIndex - 1].childList && detail.contents[iIndex - 1].show == false))
                      "
                      :key="'befor-br' + iIndex"
                    />
                    <div
                      :key="iIndex"
                      :class="[
                        'context-item',
                        {
                          'three-level': item.controlerType && item.controlerType.trim() == 'L' && item.childList,
                          'pl-item': item.controlerType && item.controlerType.trim() == 'PL',
                          'text-item':
                            item.controlerType &&
                            (item.controlerType.trim() == 'T' ||
                              item.controlerType.trim() == 'TN' ||
                              item.controlerType.trim() == 'E'),
                          'button-badge-string':
                            item.controlerType &&
                            (item.controlerType.trim() == 'B' || item.controlerType.trim() == 'BR') &&
                            item.assessValue &&
                            !/^\d+$/.test(item.assessValue) &&
                            item.assessValue.trim().toLowerCase() !== 'true',
                          'new-line': item.newLineFlag,
                          hidden:
                            (!showHidden && item.disableGroup.indexOf('-1') != -1) ||
                            (item.disableGroup == '0' && !item.show) ||
                            (item.disableGroup == '1' && !item.show),
                          'show-hidden': showHidden && item.disableGroup.indexOf('-1') != -1,
                        },
                      ]"
                    >
                      <div
                        v-if="item.controlerType && item.controlerType.trim() == 'L' && item.childList"
                        class="child-wrap"
                      >
                        <span
                          class="label-item"
                          v-if="item.defaultValue != '0'"
                          v-longpress="{ function: 'showMessage', params: item.description }"
                          :title="item.description"
                          :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : '#ffffff' }"
                        >
                          {{ item.defaultValue }}{{ item.itemName ? item.itemName.trim() : "" }}：
                        </span>
                        <template v-for="(child, cIndex) in item.childList">
                          <layout-item
                            v-if="
                              !(
                                child.disableGroup.indexOf('-1') != -1 ||
                                (child.disableGroup == '0' && !child.show) ||
                                (child.disableGroup == '1' && !child.show)
                              )
                            "
                            :key="cIndex"
                            :class="['child', { 'new-line': child.newLineFlag }]"
                            :items="item.childList"
                            :formulaParamOptions="title.formulaParamOptions"
                            :item="child"
                            :imageMaxNum="imageMaxNum"
                            :assessTime="time"
                            :gender="gender"
                            :custom-methods="customMethods"
                            @b-change-value="bChangeItem($event, item.childList, title.bookMarkID)"
                            @br-click="brClick($event)"
                            @change-value="changeItem($event, item.childList, title.bookMarkID)"
                            @changeImg="changeImg"
                            @inputEnter="enterEvent"
                          />
                        </template>
                      </div>
                      <layout-item
                        v-else
                        :items="detail.contents"
                        :formulaParamOptions="title.formulaParamOptions"
                        :item="item"
                        :imageMaxNum="imageMaxNum"
                        :assessTime="time"
                        :gender="gender"
                        :custom-methods="customMethods"
                        @b-change-value="bChangeItem($event, detail.contents, title.bookMarkID)"
                        @change-value="changeItem($event, detail.contents, title.bookMarkID)"
                        @br-click="brClick($event)"
                        @changeImg="changeImg"
                        @inputEnter="enterEvent"
                      />
                    </div>
                  </template>
                </template>
              </td>
            </tr>
          </template>
        </table>
        <!-- 模板之外的自定义内容 -->
        <slot name="customize" v-if="templateList.length == 1"></slot>
        <div v-if="showNav" class="nav-btn">
          <el-button v-if="index != 0" @click="changePage('p')" class="print-button">上一页</el-button>
          <el-button v-if="index != templateList.length - 1" @click="changePage('n')" type="primary">下一页</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import LayoutItem from "./layoutItem";
export default {
  props: {
    templateList: {
      require: true,
      default: () => {
        return [];
      },
    },
    showNav: {
      default: false,
    },
    checkFlag: {
      type: Boolean,
      default: false,
    },
    imageMaxNum: {},
    disabled: {
      type: Boolean,
      default: false,
    },
    tabPosition: {
      type: String,
      default: "top",
    },
    assessTime: {},
    gender: {},
    splicedSelected: {
      type: Boolean,
      default: false,
    },
    model: {},
    //不需要拼接AssessListID集合  数组内为Number类型
    excludeSplicedIDArr: {
      type: Array,
      default: () => {
        return [1295];
      },
    },
    //不需要拼接GroupID集合 数组内为String类型
    excludeSplicedGroupArr: {
      type: Array,
      default: () => {
        return [];
      },
    },
    showStyle: {
      type: String,
      default: "Assess",
    },
    hiddenSingleTab: {
      type: Boolean,
      default: false,
    },
    customMethods: {
      type: Function,
      default: undefined,
    },
  },
  data() {
    return {
      returnItems: [],
      curIndex: "1",
      position: "top",
      showHidden: false,
      inputs: [],
    };
  },
  components: {
    LayoutItem,
  },
  computed: {
    ...mapGetters({
      readOnly: "getReadOnly",
    }),
    time() {
      if (!this.assessTime) {
        return this._datetimeUtil.getNowDate("yyyy-MM-dd");
      }
      return this.assessTime;
    },
  },
  watch: {
    templateList(newValue) {
      this.curIndex = "1";
      this.init();
      this.getInputs();
    },
    tabPosition: {
      immediate: true,
      handler(newValue) {
        if (!newValue || (newValue != "top" && newValue != "right" && newValue != "bottom" && newValue != "left")) {
          this.position = "top";
        } else {
          this.position = newValue;
        }
      },
    },
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    init() {
      this.showHidden = false;
      this.returnItems = [];
      this.templateList.forEach((boolMark) => {
        let oneDisableGroupID = "";
        let oneGroupID = "";
        let formulaParamOptions = [];
        boolMark.groups.forEach((group) => {
          if (group.controlerType != "L") {
            formulaParamOptions.push(group);
            if (group.assessValue && group.assessValue.length > 0) {
              group.bookMarkID = boolMark.bookMarkID;
              if (this.model == "add" && group.defaultValue && group.show == false) {
                group.assessValue = "";
              } else {
                this.addItem(group);
                if (group.controlerType === "TN") {
                  this.setAuto(group);
                }
                if (this.model == "add") {
                  this.emitItem(group, true);
                }
              }
            }
            if (
              oneGroupID != `${group.groupID}|R` &&
              group.disableGroup &&
              group.disableGroup.trim() != "0" &&
              group.disableGroup.trim() != "1"
            ) {
              if (group.disableGroup.trim() != oneDisableGroupID) {
                oneDisableGroupID = group.disableGroup.trim();
                oneGroupID = `${group.groupID}|${group.controlerType}`;
                this.setHiddenItems(group, group.assessValue && group.assessValue.length > 0, true);
              } else {
                if (group.assessValue && group.assessValue.length > 0) {
                  oneGroupID = `${group.groupID}|${group.controlerType}`;
                  this.setHiddenItems(group, true, true);
                }
              }
            }
          }
          let twoDisableGroupID = "";
          let twoGroupID = "";
          group.contents.forEach((content) => {
            if (content.controlerType != "L") {
              formulaParamOptions.push(content);
              if (content.assessValue && content.assessValue.length > 0) {
                content.bookMarkID = boolMark.bookMarkID;
                if (this.model == "add" && content.defaultValue && content.show == false) {
                  content.assessValue = "";
                } else {
                  this.addItem(content);
                  if (content.controlerType === "TN") {
                    this.setAuto(content);
                  }
                  if (this.model == "add") {
                    this.emitItem(content, true);
                  }
                }
              }
              if (
                twoGroupID != `${content.groupID}|R` &&
                content.disableGroup &&
                content.disableGroup.trim() != "0" &&
                content.disableGroup.trim() != "1"
              ) {
                if (content.disableGroup.trim() != twoDisableGroupID) {
                  twoDisableGroupID = content.disableGroup.trim();
                  twoGroupID = `${content.groupID}|${content.controlerType}`;
                  this.setHiddenItems(content, content.assessValue && content.assessValue.length > 0, true);
                } else {
                  if (content.assessValue && content.assessValue.length > 0) {
                    twoGroupID = `${content.groupID}|${content.controlerType}`;
                    this.setHiddenItems(content, true, true);
                  }
                }
              }
            }
            if (content.childList && content.childList.length > 0) {
              let threeDisableGroupID = "";
              let threeGroupID = "";
              content.childList.forEach((child) => {
                if (child.controlerType != "L") {
                  formulaParamOptions.push(child);
                  if (child.assessValue && child.assessValue.length > 0) {
                    child.bookMarkID = boolMark.bookMarkID;
                    if (this.model == "add" && child.defaultValue && child.show == false) {
                      child.assessValue = "";
                    } else {
                      this.addItem(child);
                      if (child.controlerType === "TN") {
                        this.setAuto(child);
                      }
                      if (this.model == "add") {
                        this.emitItem(child, true);
                      }
                    }
                  }

                  if (
                    threeGroupID != `${child.groupID}|R` &&
                    child.disableGroup &&
                    child.disableGroup.trim() != "0" &&
                    child.disableGroup.trim() != "1"
                  ) {
                    if (child.disableGroup.trim() != threeDisableGroupID) {
                      threeDisableGroupID = child.disableGroup.trim();
                      threeGroupID = `${child.groupID}|${child.controlerType}`;
                      this.setHiddenItems(child, child.assessValue && child.assessValue.length > 0, true);
                    } else {
                      if (child.assessValue && child.assessValue.length > 0) {
                        threeGroupID = `${child.groupID}|${child.controlerType}`;
                        this.setHiddenItems(child, true, true);
                      }
                    }
                  }
                }
              });
            }
          });
        });
        boolMark.formulaParamOptions = formulaParamOptions;
      });
      this.splicedContent();
    },
    // 图片回调
    changeImg(imgs) {
      this.$emit("upload-img", imgs);
    },
    /**
     * description: 上下页且切换
     * param {*}
     * return {*}
     */
    //
    changePage(flag) {
      let index = parseInt(this.curIndex);
      if (flag === "p") {
        index -= 1;
      } else {
        index += 1;
      }
      this.curIndex = index + "";
    },
    /**
     * description: 检查T或TN类项目是否为其他T或TN类型的默认值
     * param {*} changeItem
     * param {*} item
     * return {*}
     */
    checkTextDefault(changeItem, item) {
      let controlerTypes = ["T", "TN"];
      if (
        !controlerTypes.includes(changeItem.controlerType.trim()) ||
        !controlerTypes.includes(item.controlerType.trim())
      ) {
        return false;
      }
      if (!changeItem.defaultValue || !changeItem.defaultValue.trim().includes("AssessListID_")) {
        return false;
      }
      let str = changeItem.defaultValue.trim().split("_");
      if (!str || str.length != 2) {
        return false;
      }
      if (str[1] == item.assessListID && !item.assessValue) {
        return true;
      }
      return false;
    },
    /**
     * description: 项目异动处理方法
     * param {*} item 异动项目
     * param {*} items 异动项目同组项目
     * param {*} bookMarkID 页签ID
     * return {*}
     */
    changeItem(item, items, bookMarkID) {
      let groupItems = [];
      item.bookMarkID = bookMarkID;
      this.templateList.forEach((boolMark) => {
        boolMark.groups.forEach((group) => {
          if (item.groupID.trim() == group.groupID.trim()) {
            // 把相同组的所有项目（包含三阶）作为normalCheck候选项目
            group.contents.forEach((content) => {
              groupItems.push(content);
              if (content.childList) {
                groupItems = groupItems.concat(content.childList);
              }
            });
          }
          // 处理T或TN类默认值
          if (this.checkTextDefault(item, group)) {
            group.assessValue = item.assessValue;
            this.addItem(group);
          }
          group.contents.forEach((content) => {
            if (this.checkTextDefault(item, content)) {
              content.assessValue = item.assessValue;
              this.addItem(group);
            }
            if (content.childList) {
              content.childList.forEach((child) => {
                if (this.checkTextDefault(item, child)) {
                  child.assessValue = item.assessValue;
                  this.addItem(group);
                }
              });
            }
          });
        });
      });
      if (item.controlerType.trim() == "TN") {
        if (item.assessValue || item.assessValue + "" === "0") {
          if (item.formula) {
            this.addItem(item);
          } else {
            let result = this._common.checkAssessTN(item);
            item.assessValue = result.value;
            if (result.flag) {
              this.addItem(item);
              if (this.checkFlag) {
                this.textToText(item, items);
                this.checkToRadio(item, groupItems);
              }
            } else {
              this.$emit("checkTN", false);
              this.removeItem(item, false);
              return;
            }
          }
        } else {
          this.removeItem(item);
        }
        this.$emit("checkTN", true);
        this.setAuto(item);
      } else if ((item.controlerType.trim() == "T" || item.controlerType.trim() == "E") && item.assessValue) {
        this.addItem(item);
        // 检核数据互斥
        if (this.checkFlag) {
          this.textToText(item, items);
          this.checkToRadio(item, groupItems);
        }
      } else if (item.controlerType.trim() == "R") {
        if (item.assessValue === "1") {
          // 若不要求必须选一个则可以不注释
          item.assessValue = "";
          this.removeItem(item);
          this.deelSameGroupByLinkForm(groupItems, item, false);
        } else {
          // 先处理同阶中单选和单选的互斥
          items.forEach((cont) => {
            if (cont.assessListID == item.assessListID) {
              cont.assessValue = "1";
              this.addItem(item);
              this.deelSameGroupByLinkForm(groupItems, item, true);
            } else {
              if (cont.controlerType.trim() == "R" && cont.assessValue == "1") {
                cont.assessValue = "";
                this.setAuto(cont);
                if (cont.disableGroup && item.disableGroup && cont.disableGroup.trim() == item.disableGroup.trim()) {
                  this.removeItem(cont, true);
                } else {
                  this.removeItem(cont);
                }
              }
            }
          });
          // 检核数据互斥
          if (this.checkFlag) {
            // 三阶需要和二阶的单选互斥
            if (item.contentILevel == 3) {
              this.checkToRadio(item, groupItems);
            } else {
              this.radioToCheck(item, groupItems);
            }
          }
        }
        this.setAuto(item);
      } else if (item.controlerType.trim() == "C") {
        if (item.assessValue === "1") {
          item.assessValue = "";
          let cont = items.find((cont) => {
            let flag =
              cont.controlerType.trim() == "C" &&
              cont.assessValue == "1" &&
              cont.disableGroup &&
              item.disableGroup &&
              cont.disableGroup.trim() == item.disableGroup.trim();
            return flag;
          });
          if (cont) {
            this.removeItem(item, true);
          } else {
            this.removeItem(item);
          }
          this.deelSameGroupByLinkForm(groupItems, item, false);
        } else {
          // 检核数据互斥
          if (this.checkFlag) {
            let flag = this.checkToCheck(item, items);
            if (flag) {
              this.checkToRadio(item, groupItems);
              this.deelSameGroupByLinkForm(groupItems, item, true);
            }
          } else {
            item.assessValue = "1";
            this.addItem(item);
            this.deelSameGroupByLinkForm(groupItems, item, true);
          }
        }
        this.setAuto(item);
      } else if (item.controlerType.trim() == "B") {
        // 只读画面，不触发事件
        if (this.readOnly) {
          return;
        }
        this.$emit("button-click", item);
      } else if (item.controlerType.trim() == "BR") {
        if (item.assessValue) {
          this.addItem(item);
        } else {
          this.removeItem(item);
        }
      } else if (item.controlerType.trim() == "P") {
        if (item.assessValue) {
          this.addItem(item);
        } else {
          this.removeItem(item);
        }
      } else if (item.controlerType.trim() == "BD") {
        if (item.assessValue) {
          this.addItem(item);
          // 检核数据互斥
          if (this.checkFlag) {
            this.checkToRadio(item, groupItems);
          }
        } else {
          this.removeItem(item);
        }
      } else {
        if (item.assessValue || item.assessValue + "" === "0") {
          this.addItem(item);
        } else {
          this.removeItem(item);
        }
      }
      this.splicedContent();
    },
    /**
     * description: 按钮角标变化
     * param {*} item 异动项目
     * param {*} items 异动项目同组项目
     * param {*} bookMarkID 页签ID
     * return {*}
     */
    bChangeItem(item, items, bookMarkID) {
      if (item.assessValue) {
        item.bookMarkID = bookMarkID;
        // 检核数据互斥
        if (this.checkFlag) {
          this.checkToRadio(item, items);
        }
        this.addItem(item);
        this.splicedContent();
      } else {
        let deleteItem = this.returnItems.find((m) => m.assessListID == item.assessListID);
        if (deleteItem) {
          this.removeItem(deleteItem);
        }
      }
    },
    /**
     * description: 按钮项目回调
     * param {*} item
     * return {*}
     */
    brClick(item) {
      // 只读画面，不触发事件
      if (this.readOnly) {
        return;
      }
      if (!item.linkForm) {
        this._showTip("warning", "【" + item.itemName + "】为BR类，但未配置linkForm！");
        return;
      }
      if (!item.brParams || !item.brParams.recordListID) {
        this._showTip("warning", "【" + item.itemName + "】为BR类，获取recordListID失败！");
        return;
      }
      this.$emit("button-record-click", item);
    },

    /**
     * description: 从勾选集合中去除项目
     * param {*} item 移除项目
     * param {*} flag 为true时不处理隐藏逻辑，false需要处理隐藏逻辑
     * return {*}
     */
    removeItem(item, flag) {
      let length = this.returnItems.length;
      if (length == 0) return;
      for (let i = 0; i < length; i++) {
        if (this.returnItems[i].assessListID == item.assessListID) {
          this.returnItems.splice(i, 1);
          this.emitItem(item, false);
          if (!flag) {
            this.setHiddenItems(item, false);
          }
          break;
        }
      }
      let returnItems = this._common.clone(this.returnItems);
      this._common.sortByKeys(returnItems, ["groupSort", "sort"], [1, 1]);
      this.$emit("change-values", returnItems);
    },
    /**
     * description: 将项目添加到够选集合
     * param {*} item 添加项目
     * return {*}
     */
    addItem(item) {
      let flag = true;
      this.emitItem(item, true);
      let length = this.returnItems.length;
      for (let i = 0; i < length; i++) {
        if (this.returnItems[i].assessListID == item.assessListID) {
          flag = false;
          break;
        }
      }
      if (flag) {
        this.returnItems.push(item);
        this.setHiddenItems(item, true);
      } else {
        let valueFlag = true;
        // TN类要触发隐藏机制，0也算有效值，下面代码暂时屏蔽
        if (item.controlerType.trim() === "TN" && item.assessValue === "0" && !item.show) {
          valueFlag = false;
        }
        this.setHiddenItems(item, valueFlag);
      }
      //重新排序 防止死循环，克隆一个对象
      let returnItems = this._common.clone(this.returnItems);
      this._common.sortByKeys(returnItems, ["groupSort", "sort"], [1, 1]);
      this.$emit("change-values", returnItems);
    },
    // 隐藏/显示逻辑
    // DisableGroup可以配置为0;1;-1;xx;xx,xx;0,xx,xx;1,xx,xx;-1,xx,xx;0,3_assessListID;1,3_assessListID;-1,3_assessListID
    // xx是groupID组号, 配置说明:
    // 1. null 与开关显示无关,  正常显示;
    // 2. -1 永远隐藏;
    // 3. 0或1的项目默认隐藏,只有对应的开关项目勾选/取消勾选后才能显示;
    // 4. xx标识为xx组的开关,xx,xx标识为多组的开关;
    // 5. 0,xx,xx和1,xx,xx标识既是其他开关的隐藏项,本身又是开关;
    // 6. -1,xx,xx标识永远隐藏,同时又是其他组开关;
    // 7. 0,3_assessListID和1,3_assessListI标识本身隐藏,又是其他三阶组隐藏/显示开关;
    // 8. -1,3_assessListID标识本身永久隐藏,又是其他三阶组隐藏/显示开关;
    //   3_标识关联三阶前缀,AssessListID标识三阶parent项目的AssessListID
    // 1、flag=true表示要item.disableGroup下项目disableGroup=0的要显示，disableGroup=1的要隐藏
    // 2、flag=false表示要item.disableGroup下项目disableGroup=0的要隐藏，disableGroup=1的要显示
    // isInit为初始化标记，若为初始化，则不清空值 -- sjz 2022-4-17 影响业务 暂时屏蔽
    setHiddenItems(item, flag, isInit) {
      if (!item.disableGroup) {
        return;
      }
      let groupIDs = item.disableGroup.trim().split(",");
      for (let i = 0; i < this.templateList.length; i++) {
        for (let j = 0; j < this.templateList[i].groups.length; j++) {
          if (this.templateList[i].groups.assessListID == item.assessListID) {
            continue;
          }
          this.updateItem(groupIDs, this.templateList[i].groups[j], isInit, flag);
          for (let k = 0; k < this.templateList[i].groups[j].contents.length; k++) {
            let content = this.templateList[i].groups[j].contents[k];
            // 二阶控制三阶显示/隐藏，特殊处理
            if (
              content.assessListID == item.assessListID ||
              (content.threeHideFlag && item.disableGroup.trim().indexOf(content.assessListID + "") == -1)
            ) {
              continue;
            }
            this.updateItem(groupIDs, content, isInit, flag);
            if (this.templateList[i].groups[j].contents[k].childList) {
              for (let m = 0; m < this.templateList[i].groups[j].contents[k].childList.length; m++) {
                let child = this.templateList[i].groups[j].contents[k].childList[m];
                // 二阶控制三阶显示/隐藏，特殊处理
                if (
                  child.assessListID == item.assessListID ||
                  (child.threeHideFlag && item.disableGroup.trim().indexOf(child.assessListGroupID + "") == -1)
                ) {
                  continue;
                }
                this.updateItem(groupIDs, child, isInit, flag);
              }
            }
          }
        }
      }
    },

    /**
     * description: 更新项目状态
     * param {*} groupIDs 组号集合
     * param {*} item 项目
     * param {*} isInit 是否初始化
     * param {*} flag 隐藏标记
     * return {*}
     */
    updateItem(groupIDs, item, isInit, flag) {
      groupIDs.forEach((groupID) => {
        if (groupID.trim() == "0" || groupID.trim() == "1" || groupID.trim() == "-1") {
          return;
        }
        if (item.groupID.trim() == groupID.trim()) {
          if (item.disableGroup.indexOf("0") == 0) {
            this.$set(item, "show", flag);
            if ((!isInit && !flag) || (isInit && this.model == "add" && !flag)) {
              this.$set(item, "assessValue", "");
              let single = item.disableGroup === "0";
              this.removeItem(item, single);
            }
            //显示时根据defaultValue有值 assessValue无值 默认打上
            // if (this.model == "add" && flag&&item.defaultValue&&!item.assessValue&&item.controlerType=="R") {
            //   this.$set(item, "assessValue", item.defaultValue);
            // }
          } else if (item.disableGroup.indexOf("1") == 0) {
            this.$set(item, "show", !flag);
            if ((!isInit && flag) || (isInit && this.model == "add" && flag)) {
              this.$set(item, "assessValue", "");
              let single = item.disableGroup === "1";
              this.removeItem(item, single);
            }
          }
        } else if (groupID.trim().indexOf("3_") != -1) {
          // 三阶特殊处理
          let tempStrs = groupID.trim().split("_");
          if (!tempStrs || tempStrs.length != 2) {
            return;
          }
          let assessListGroupID = tempStrs[1];
          // 处理三阶标题及内容隐藏/显示逻辑
          if (item.assessListID + "" == assessListGroupID || item.assessListGroupID + "" == assessListGroupID) {
            this.$set(item, "threeHideFlag", true);
            if (item.disableGroup.indexOf("0") == 0) {
              this.$set(item, "show", flag);
              if ((!isInit && !flag) || (isInit && this.model == "add" && !flag)) {
                this.$set(item, "assessValue", "");
                let single = item.disableGroup === "0";
                this.removeItem(item, single);
              }
            } else if (item.disableGroup.indexOf("1") == 0) {
              this.$set(item, "show", !flag);
              if ((!isInit && flag) || (isInit && this.model == "add" && flag)) {
                this.$set(item, "assessValue", "");
                let single = item.disableGroup === "1";
                this.removeItem(item, single);
              }
            }
          }
        }
      });
    },
    /**
     * description: 设置自动勾选
     * param {*} item 当前够选项目
     * return {*}
     */
    setAuto(item) {
      if (!item.autoChecklList) {
        return;
      }
      if (item.controlerType.trim() == "TN" || item.controlerType.trim() == "C" || item.controlerType.trim() == "R") {
        let autoAdd = [];
        let autoCancel = [];
        if (item.assessValue || item.assessValue + "" === "0") {
          let val = parseFloat(item.assessValue);
          item.autoChecklList.forEach((auto) => {
            if (item.controlerType.trim() == "TN") {
              if (val >= auto.value && val <= auto.upLimit) {
                autoAdd.push(auto.newAssessListID);
              } else {
                autoCancel.push(auto.newAssessListID);
              }
            } else {
              if (auto.value == "1") {
                autoAdd.push(auto.newAssessListID);
              } else {
                autoCancel.push(auto.newAssessListID);
              }
            }
          });
        } else {
          item.autoChecklList.forEach((auto) => {
            autoCancel.push(auto.newAssessListID);
          });
        }
        this.templateList.forEach((boolMark) => {
          boolMark.groups.forEach((group) => {
            if (group.controlerType.trim() !== "L") {
              // 自动勾选
              if (autoAdd.length > 0 && autoAdd.indexOf(group.assessListID) != -1) {
                group.assessValue = "1";
                this.addItem(group);
              }
              // 自动取消
              if (autoCancel.length > 0 && autoCancel.indexOf(group.assessListID) != -1) {
                group.assessValue = "";
                this.removeItem(group);
              }
            }
            group.contents.forEach((content) => {
              let autoAddItem = undefined;
              let groupItems = [];
              if (content.controlerType.trim() !== "L") {
                // 自动勾选
                if (autoAdd.length > 0 && autoAdd.indexOf(content.assessListID) != -1) {
                  content.assessValue = "1";
                  autoAddItem = content;
                  groupItems = group.contents;
                  this.addItem(content);
                }
                // 自动取消
                if (autoCancel.length > 0 && autoCancel.indexOf(content.assessListID) != -1) {
                  content.assessValue = "";
                  this.removeItem(content);
                }
              }
              let tempItems = [];
              if (autoAddItem) {
                tempItems = group.contents;
                if (autoAddItem.groupID == content.groupID && content.childList) {
                  groupItems = groupItems.concat(content.childList);
                }
              } else {
                if (content.childList) {
                  content.childList.forEach((child) => {
                    if (child.controlerType.trim() !== "L") {
                      // 自动勾选
                      if (autoAdd.length > 0 && autoAdd.indexOf(child.assessListID) != -1) {
                        child.assessValue = "1";
                        autoAddItem = child;
                        this.addItem(child);
                        groupItems = content.childList;
                        tempItems = content.childList;
                      }
                      // 自动取消
                      if (autoCancel.length > 0 && autoCancel.indexOf(child.assessListID) != -1) {
                        child.assessValue = "";
                        this.removeItem(child);
                      }
                    }
                  });
                }
              }
              if (autoAddItem) {
                if (autoAddItem.controlerType.trim() == "R") {
                  // 先处理同阶单选与单选互斥
                  tempItems.forEach((tempItem) => {
                    if (
                      tempItem.assessListID != autoAddItem.assessListID &&
                      tempItem.controlerType.trim() == "R" &&
                      tempItem.assessValue == "1"
                    ) {
                      tempItem.assessValue = "";
                      this.removeItem(tempItem);
                    }
                  });
                  // 处理单选和复选的互斥
                  this.radioToCheck(autoAddItem, groupItems);
                } else if (autoAddItem.controlerType.trim() == "C") {
                  // 处理复选和单选互斥
                  this.checkToRadio(autoAddItem, groupItems);
                }
              }
            });
          });
        });
      }
    },
    /**
     * description: 复选和复选项目互斥检核
     * param {*} item 当前勾选项目
     * param {*} items 当前勾选项目所属组所有项目
     * return {*}
     */
    checkToCheck(item, items) {
      if (item.interactionList && item.interactionList.length > 0) {
        let flag = true;
        item.interactionList.forEach((assessListID) => {
          items.forEach((cont) => {
            if (cont.assessListID == assessListID && cont.assessValue == "1") {
              this._showTip(
                "warning",
                "【" + item.itemName.trim() + "】和【" + cont.itemName.trim() + "】不能同时选择"
              );
              flag = false;
            }
          });
        });
        if (flag) {
          item.assessValue = "1";
          this.addItem(item);
          return true;
        } else {
          item.assessValue = "";
          return false;
        }
      } else {
        item.assessValue = "1";
        this.addItem(item);
        return true;
      }
    },
    /**
     * description: 文本框和复选项目互斥检核，目前只有抢救在用
     * param {*} item 当前勾选项目
     * param {*} items 当前勾选项目所属组所有项目
     * return {*}
     */
    textToText(item, items) {
      if (item.interactionList && item.interactionList.length > 0) {
        let flag = true;
        let tempItem = {};
        item.interactionList.forEach((assessListID) => {
          items.forEach((cont) => {
            if (cont.assessListID == assessListID && cont.assessValue) {
              tempItem = cont;
              flag = false;
            }
          });
        });
        if (!flag) {
          tempItem.assessValue = "";
          this.removeItem(tempItem);
          this.setAuto(tempItem);
        }
      }
    },
    /**
     * description: 单选和复选项目互斥处理
     * param {*} item 当前勾选项目
     * param {*} items 当前勾选项目所属组所有项目
     * return {*}
     */
    radioToCheck(item, items) {
      if (item.normalCheckList) {
        item.normalCheckList.forEach((normalCheck) => {
          items.forEach((cont) => {
            if (
              cont.assessListID != item.assessListID &&
              cont.assessListID == normalCheck.assessListID &&
              cont.assessValue.length > 0 &&
              cont.controlerType.trim() !== "B"
            ) {
              if (cont.assessListID == 172) {
              }
              cont.assessValue = "";
              this.removeItem(cont);
              this.setAuto(cont);
            }
          });
        });
      }
    },
    /**
     * description: 复选和单选项目互斥处理
     * param {*} item 当前勾选项目
     * param {*} items 当前勾选项目所属组所有项目
     * return {*}
     */
    checkToRadio(item, items) {
      if (item.normalCheckList) {
        item.normalCheckList.forEach((normalCheck) => {
          items.forEach((cont) => {
            // 排除自己
            if (cont.assessListID != item.assessListID && cont.controlerType.trim() == "R") {
              if (normalCheck.abnormalAssessListID == cont.assessListID) {
                cont.assessValue = "1";
                this.addItem(cont);
              }
              if (normalCheck.normalAssessListID == cont.assessListID) {
                cont.assessValue = "";
                this.removeItem(cont);
                this.setAuto(cont);
              }
            }
          });
        });
      }
    },
    /**
     * description: 检核必选项是否勾选
     * param {*}
     * return {*}
     */
    checkRequire() {
      for (let i = 0; i < this.templateList.length; i++) {
        let title = this.templateList[i];
        for (let j = 0; j < title.groups.length; j++) {
          let group = title.groups[j];
          // 非必选组
          if (!group.requireFlag) {
            continue;
          }
          let selectItem = this.returnItems.find((data) => {
            return data.groupID.trim() == group.groupID.trim() && data.disableGroup != "-1";
          });
          // 没查到，则不让保存
          if (!selectItem) {
            // 如果是隐藏项目 不检核
            if (
              (group.show != undefined && group.show != true) ||
              group.hidden ||
              (group.show === undefined &&
                (group.disableGroup === "0" ||
                  group.disableGroup === "1" ||
                  group.disableGroup.indexOf("0,") === 0 ||
                  group.disableGroup.indexOf("1,") === 0))
            ) {
              continue;
            }
            // 如果没有明细，直接跳过
            if (group.contents.length <= 0 && group.controlerType.trim() === "L") {
              continue;
            }
            // 判断是否只有按钮
            let buttons = group.contents.filter((content) => {
              return content.controlerType.trim() == "B";
            });
            if (buttons && buttons.length > 0) {
              // 该分组只有父级标题为L类型 并且子标题都是按钮才不检核
              if (buttons.length == group.contents.length && group.controlerType.trim() == "L") {
                continue;
              }
              buttons = buttons.filter((content) => {
                return content.assessValue && content.assessValue.length > 0;
              });
              if (buttons && buttons.length > 0) {
                continue;
              }
            }
            let msg = "项目为必选项，但无评估内容！";
            if (this.templateList.length > 1) {
              msg = `【${title.title}-${group.itemName.trim()}】${msg}`;
            } else {
              msg = `【${group.itemName.trim()}】${msg}`;
            }
            this._showTip("warning", msg);
            return false;
          }
        }
      }
      return true;
    },
    /**
     * description: 永久隐藏项目显示开关
     *              ctrl + shift + S 显示永久隐藏项目（disableGroup = -1）
     *              ctrl + shift + H 隐藏永久隐藏项目（disableGroup = -1）
     * param {*} flag true显示隐藏项，false不显示
     * return {*}
     */
    toggleHidden(flag) {
      if (flag) {
        this.showHidden = true;
      } else {
        this.showHidden = false;
      }
    },
    /**
     * description: 消息提示框
     * param {*} messageContent 消息
     * return {*}
     */
    showMessage(messageContent) {
      if (!messageContent) {
        return;
      }
      this._showMessage({
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
    /**
     * description: 项目异动时回调
     * param {*} item 当前项目
     * param {*} flag true表示新增勾选，false表示取消勾选
     * return {*}
     */
    emitItem(item, flag) {
      this.$emit("changeItem", item, flag);
    },
    /**
     * description: 将勾选集合中的项目组装成字符串
     * param {*}
     * return {*}
     */
    splicedContent() {
      if (!this.splicedSelected) {
        return;
      }
      //重新排序 防止死循环，克隆一个对象
      let returnItems = this._common.clone(this.returnItems);
      returnItems.sortBy("sort");
      let selectItems = this.getSplicedSelectItem(returnItems);
      let contentData = "";
      if (!selectItems || selectItems.length <= 0) {
        this.$emit("splicedContent", contentData);
        return;
      }
      for (let i = 0; i < this.templateList.length; i++) {
        let title = this.templateList[i];
        for (let j = 0; j < title.groups.length; j++) {
          let group = title.groups[j];
          let tempGroup = selectItems.find((data) => {
            return data.assessListID == group.assessListID && group.controlerType.trim() !== "L";
          });
          if (tempGroup) {
            contentData = this.connectStr(contentData, tempGroup, "；", true, true);
            continue;
          }
          if (group.contents && group.contents.length > 0) {
            let temp = selectItems.find((data) => {
              return data.groupID.trim() == group.groupID.trim();
            });
            if (temp) {
              let contentStr = "";
              for (let k = 0; k < group.contents.length; k++) {
                let content = group.contents[k];
                let tempContent = selectItems.find((data) => {
                  return data.assessListID == content.assessListID && content.controlerType.trim() !== "L";
                });
                if (tempContent) {
                  contentStr = this.connectStr(contentStr, tempContent, "，", k != 0);
                  continue;
                }
                if (content.childList && content.childList.length > 0) {
                  let tempC = selectItems.find((data) => {
                    return data.groupID.trim() == content.groupID.trim();
                  });
                  if (tempC) {
                    let childStr = "";
                    for (let m = 0; m < content.childList.length; m++) {
                      let child = content.childList[m];
                      let tempChild = selectItems.find((data) => {
                        return data.assessListID == child.assessListID && child.controlerType.trim() !== "L";
                      });
                      if (tempChild) {
                        childStr = this.connectStr(childStr, tempChild, "，", m != 0);
                      }
                    }
                    if (childStr) {
                      if (contentStr) {
                        contentStr += "，" + content.itemName + "：" + childStr;
                      } else {
                        contentStr += content.itemName + "：" + childStr;
                      }
                    }
                  }
                }
              }
              if (contentStr) {
                contentData += group.itemName + "：" + contentStr + "；";
              }
            }
          }
        }
      }
      // 组装选择的项目
      contentData = contentData.replace(/,;/g, "；");
      contentData = contentData.replace(/,,/g, "，");
      contentData = contentData.replace(/;/g, "；");
      contentData = contentData.replace(/,/g, "，");
      this.$emit("splicedContent", contentData);
    },
    /**
     * description: 筛选组装内容
     * param {*} returnItems
     * return {*}
     */
    getSplicedSelectItem(returnItems) {
      if (!returnItems || !returnItems.length) {
        return [];
      }
      //类型筛选
      let selectItems = returnItems.filter((data) => {
        return (
          data.disableGroup.indexOf("-1") == -1 && data.show !== "" && !["L", "B", "BR"].includes(data.controlerType)
        );
      });
      //GroupID筛选
      if (this.excludeSplicedGroupArr.length) {
        selectItems = selectItems.filter((data) => data.groupID && !this.excludeSplicedGroupArr.includes(data.groupID));
      }
      //assessListID筛选
      if (this.excludeSplicedIDArr.length) {
        selectItems = selectItems.filter(
          (data) => data.assessListID && !this.excludeSplicedIDArr.includes(data.assessListID)
        );
      }
      return selectItems;
    },
    /**
     * description: 拼接字符串
     * param {*} str 原字符串
     * param {*} item 项目
     * param {*} Separate 分隔符
     * param {*} flag 标记
     * param {*} isGroup 是否一阶
     * return {*}
     */
    connectStr(str, item, Separate, flag, isGroup) {
      let addStr = "";
      if (item.controlerType == "B" || item.controlerType == "BR") {
        return addStr;
      }
      if (
        item.controlerType == "T" ||
        item.controlerType == "TN" ||
        item.controlerType == "DT" ||
        item.controlerType == "MT"
      ) {
        addStr = item.itemName + "：" + item.assessValue;
        if (item.unit) {
          addStr += item.unit;
        }
      } else if (item.controlerType == "CS" && item.selectOptionList && item.selectOptionList.length > 0) {
        addStr = item.itemName + "：" + this.findValueInCascadeTree(item.selectOptionList, item.assessValue);
      } else {
        addStr = item.itemName;
      }
      if (!str) {
        return addStr;
      }
      if (flag) {
        if (isGroup) {
          str = str + addStr + Separate;
        } else {
          str = str + Separate + addStr;
        }
      } else {
        str = str + addStr;
      }

      return str;
    },
    /**
     * @description: 从级联选择器选项树中查询指定 value 值对应的选项描述
     * @param optionList 级联选项
     * @param targetValue 目标值
     * @return
     */
    findValueInCascadeTree(optionList, targetValue) {
      for (let option of optionList) {
        if (option && option.value === targetValue) {
          return option.label;
        }
        if (option.children) {
          let result = this.findValueInCascadeTree(option.children, targetValue);
          if (result) return result;
        }
      }
      return ""; // 如果没找到
    },
    /**
     * description: 获取所有TN输入框组件
     * param {*}
     * return {*}
     */
    getInputs() {
      this.$nextTick(() => {
        let tabsLayout = this.$refs.tabsLayout;
        this.inputs = tabsLayout.getElementsByTagName("input");
        let sucInputs = [];
        for (let index = 0; index < this.inputs.length; index++) {
          let input = this.inputs[index];
          if (input.name && input.name == "TN") {
            sucInputs.push(input);
          }
        }
        this.inputs = sucInputs;
      });
    },
    /**
     * description: 左右键切换TN输入框
     * param {*} nowInput
     * param {*} flag
     * return {*}
     */
    enterEvent(nowInput, flag) {
      for (let index = 0; index < this.inputs.length; index++) {
        let input = this.inputs[index];
        if (input == nowInput.target) {
          if (flag && index == this.inputs.length - 1) {
            this.inputs[0].focus();
            break;
          } else if (!flag && index == 0) {
            this.inputs[this.inputs.length - 1].focus();
            break;
          } else {
            input.blur();
            this.inputs[flag ? index + 1 : index - 1].focus();
          }
        }
      }
    },
    /**
     * description: 依据项目linkForm 处理同组项目
     * param {*} groupItems 项目所属组的所有项目
     * param {*} item 当前项目
     * param {*} flag true勾选，false取消勾选
     * return {*}
     */
    deelSameGroupByLinkForm(groupItems, item, flag) {
      if (!item.linkForm || item.linkForm.indexOf("||") == -1) {
        return;
      }
      // 拆分LinkForm参数
      var linkForms = item.linkForm.split("||");
      // 同组排程 后续有其他需求可以扩展else if
      if (linkForms[0] == "Intervention") {
        if (linkForms.length < 3) {
          return;
        }
        if (linkForms[2] == "0") {
          return;
        }
        let assessListIDs = [];
        // 频次
        if (linkForms.length > 3) {
          assessListIDs.push(linkForms[3]);
        }
        // 次数
        if (linkForms.length > 4) {
          assessListIDs.push(linkForms[4]);
        }
        let regularFlag = false;
        // 是否常规触发排程
        if (linkForms.length > 5 && linkForms[5] == "1") {
          regularFlag = true;
        }
        if (assessListIDs.length > 0) {
          groupItems.forEach((groupItem) => {
            if (
              groupItem.assessListID != item.assessListID &&
              assessListIDs.indexOf(groupItem.assessListID + "") != -1
            ) {
              if (flag) {
                if (!groupItem.assessValue) {
                  groupItem.assessValue = groupItem.defaultValue;
                }
                groupItem.disabled = regularFlag;
                this.addItem(groupItem);
              } else {
                groupItem.assessValue = "";
                groupItem.disabled = false;
                this.removeItem(groupItem);
              }
            }
          });
        }
      }
    },
    /**
     * description: 更新B或BR类项目
     * param {*} item 按钮项目
     * return {*} 相同类型的项目
     */
    updateButtonItem(item) {
      if (!item || !item.assessListID) {
        return;
      }
      // 根据正则从linkForm中取得专项类型
      var strs = item.linkForm.match(/\/(\S*)(\?|;)/);
      let type = "";
      if (strs != null && strs.length > 2) {
        type = strs[1];
        type = "/" + type;
      }
      let sameTypeAssessListIDs = [];
      for (let i = 0; i < this.templateList.length; i++) {
        for (let j = 0; j < this.templateList[i].groups.length; j++) {
          let group = this.templateList[i].groups[j];
          if (group.controlerType.trim() == "B" || group.controlerType.trim() == "BR") {
            if (group.assessListID == item.assessListID) {
              this.$set(group, "assessValue", item.assessValue);
              this.$set(group, "linkForm", item.linkForm);
              return;
            } else {
              if (
                type &&
                group.linkForm.indexOf(type) != -1 &&
                sameTypeAssessListIDs.indexOf(group.assessListID) != -1
              ) {
                sameTypeAssessListIDs.push(group.assessListID);
              }
            }
          }
          for (let k = 0; k < group.contents.length; k++) {
            let content = group.contents[k];
            if (content.controlerType.trim() == "B" || content.controlerType.trim() == "BR") {
              if (content.assessListID == item.assessListID) {
                if (item) {
                  this.$set(content, "assessValue", item.assessValue);
                  this.$set(content, "linkForm", item.linkForm);
                }
              } else {
                if (
                  type &&
                  content.linkForm.indexOf(type) != -1 &&
                  !sameTypeAssessListIDs.indexOf(content.assessListID) != -1
                ) {
                  sameTypeAssessListIDs.push(content.assessListID);
                }
              }
            }
            // 判断三阶里是否有按钮
            if (content.childList) {
              for (let m = 0; m < content.childList.length; m++) {
                let child = content.childList[m];
                if (child.controlerType.trim() == "B" || child.controlerType.trim() == "BR") {
                  if (child.assessListID == item.assessListID) {
                    if (item) {
                      this.$set(child, "assessValue", item.assessValue);
                      this.$set(child, "linkForm", item.linkForm);
                    }
                  } else {
                    if (
                      type &&
                      child.linkForm.indexOf(type) != -1 &&
                      !sameTypeAssessListIDs.indexOf(child.assessListID) != -1
                    ) {
                      sameTypeAssessListIDs.push(child.assessListID);
                    }
                  }
                }
              }
            }
          }
        }
      }
      return sameTypeAssessListIDs;
    },

    /**
     * @description: 检核assessListIDs是否存在
     * @param assessListIDs
     * @return
     */
    checkAssessListIDExist(assessListIDs) {
      for (let i = 0; i < this.templateList.length; i++) {
        for (let j = 0; j < this.templateList[i].groups.length; j++) {
          let group = this.templateList[i].groups[j];
          if (assessListIDs.includes(group.assessListID)) {
            return true;
          }
          for (let k = 0; k < group.contents.length; k++) {
            let content = group.contents[k];
            if (assessListIDs.includes(content.assessListID)) {
              return true;
            }
            // 判断三阶里是否有按钮
            if (content.childList) {
              for (let m = 0; m < content.childList.length; m++) {
                let child = content.childList[m];
                if (assessListIDs.includes(child.assessListID)) {
                  return true;
                }
              }
            }
          }
        }
      }
      return false;
    },
    /**
     * @description: 设置项目的值
     * @param assessListID
     * @param value
     * @return
     */
    setItemValue(assessListID, value) {
      this.templateList.forEach((bookMark) => {
        this.loopSetValue(assessListID, value, bookMark.groups);
      });
    },
    /**
     * @description: 循环设置值
     * @param assessListID
     * @param value
     * @param item
     * @return
     */
    loopSetValue(assessListID, value, item) {
      for (let index = 0; index < item.length; index++) {
        let detail = item[index];
        if (detail.assessListID == assessListID) {
          this.$set(detail, "assessValue", value);
          if (value) {
            this.addItem(detail);
          } else {
            this.removeItem(detail);
          }
          continue;
        }
        if (detail.contents && detail.contents.length) {
          this.loopSetValue(assessListID, value, detail.contents);
        }
        if (detail.childList && detail.childList.length) {
          this.loopSetValue(assessListID, value, detail.childList);
        }
      }
    },
  },
};
</script>
<style lang="scss">
.tabs-layout {
  height: 100%;
  color: #000;
  outline: none;
  .tabs-layout-wrap {
    box-shadow: none;
    height: 100%;
    background-color: #fffff9;
    box-sizing: border-box;
    /* 隐藏tabs模式样式 */
    &.hidden-tabs {
      .el-tabs__header {
        display: none;
      }
      .el-tabs__content {
        height: 100%;
      }
    }
    /* table模式样式 */
    &.table-mode {
      border: 0;
      .el-tabs__content {
        overflow-y: auto;
        overflow-x: hidden;
        .layout-pane .layout-wrap {
          border-top: 1px solid #cccccc;
          width: 100%;
          .show-stripe {
            background-color: #f4f1f1;
          }
          tr:hover {
            background-color: #ffe0ac;
          }
          .layout-item .check-item i::before {
            border-radius: 10px;
          }
          tr td {
            border-left: 1px solid #cccccc;
            border-bottom: 1px solid #cccccc;
            &:last-child {
              border-right: 1px solid #cccccc;
            }
            &.label-td {
              width: 160px;
              white-space: initial;
              text-align: left;
              vertical-align: middle;
              div {
                margin-left: 10px;
              }
            }
            &.content-td {
              width: calc(100% - 120px);
              .context-item {
                margin-bottom: 0;
                &.new-line {
                  width: 100%;
                  padding-left: 10px;
                  box-sizing: border-box;
                  border-bottom: 1px solid #cccccc;
                  &:last-child {
                    border: 0;
                  }
                }
                /* 单选、多选加粗 */
                .check-item {
                  &.rb-checked,
                  &.cb-checked {
                    font-weight: bold;
                  }
                }
              }
            }
          }
        }
      }
    }
    .el-tabs__header {
      background-color: #ebf7df;
      .el-tabs__item {
        &.is-active {
          color: $base-color;
        }
      }
    }
    .el-tabs__content {
      height: calc(100% - 40px);
      overflow: auto;
      .layout-pane {
        .layout-wrap {
          .hidden {
            display: none !important;
          }
          .show-hidden {
            background-color: #ff0000;
          }
          .context {
            width: 100%;
            .label-td {
              white-space: nowrap;
              text-align: right;
              vertical-align: baseline;
              font-weight: 600;
              font-size: 14px;
              height: 24px;
              div {
                margin-top: -1px;
                line-height: 24px;
              }
              .require {
                color: #ff0000;
                font-weight: bold;
              }
            }
            .context-item {
              display: inline-block;
              margin-right: 20px;
              vertical-align: baseline;
              margin-bottom: 4px;
              &.button-badge-string {
                margin-top: 12px;
              }
              &.pl-item {
                display: block;
              }
              &.three-level {
                display: block;
                margin-bottom: 8px;
                .child-wrap {
                  display: inline-block;
                  margin-right: 15px;
                  .child {
                    display: inline-block;
                    margin-right: 15px;
                    line-height: 24px;
                    &.new-line {
                      display: block;
                    }
                    &:last-child {
                      margin-right: 0;
                    }
                  }
                }
              }
              .label-item {
                height: 24px;
                font-size: 14px;
                background-color: #dad6d6;
                padding-left: 5px;
                border-radius: 2px;
                margin-right: 5px;
              }
            }
            .text-item {
              vertical-align: top;
            }
          }
        }
        .nav-btn {
          margin-bottom: 20px;
        }
      }
    }
  }
}
</style>
