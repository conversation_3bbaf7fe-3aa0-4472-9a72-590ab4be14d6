<!--
 * FilePath     : \src\pages\patientOrder\orderCheck.vue
 * Author       : xml
 * Date         : 2019-10-13 10:00
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-06-01 11:14
 * Description  : 医嘱审核
 -->
<template>
  <base-layout class="order-check" v-loading="loading" :element-loading-text="loadingText">
    <div slot="header" class="header">
      <el-radio-group class="header-radio" v-model="checkedValue" @change="radioChange">
        <el-radio label="1">全部</el-radio>
        <el-radio label="2">临时</el-radio>
        <el-radio label="3">长期</el-radio>
      </el-radio-group>
      <div class="container-switch">
        <span class="switch-text">未审核医嘱：</span>
        <el-switch v-model="switchAll" @change="orderSwitch" />
      </div>
      <div class="header-button">
        <el-button type="primary" icon="iconfont icon-check" @click="CheckOrderMain">审核</el-button>
      </div>
    </div>
    <el-table
      class="order-table"
      border
      height="100%"
      :data="list"
      highlight-current-row
      :row-class-name="tableRowClassName"
      :span-method="mergeCells"
    >
      <el-table-column fixed label="长/临" prop="orderType" width="55" align="center"></el-table-column>
      <el-table-column label="类别" prop="orderPatternName" width="55" align="center"></el-table-column>

      <el-table-column label="开始时间" width="160" align="center">
        <template slot-scope="scope">
          {{ scope.row.startDateTime ? scope.row.startDateTime.substring(0, 16) : "" }}
        </template>
      </el-table-column>
      <el-table-column label="警示" prop="orderAlertFlag" width="80" align="center"></el-table-column>
      <el-table-column label="医嘱码" prop="orderCode" width="120" align="center"></el-table-column>
      <el-table-column
        label="医嘱内容"
        prop="orderContent"
        width="260"
        header-align="center"
        align="left"
      ></el-table-column>
      <el-table-column label="剂量" prop="orderDose" width="60" header-align="center"></el-table-column>
      <el-table-column label="单位" prop="unit" width="50" align="center"></el-table-column>
      <el-table-column label="首日执行时间" width="160" align="center">
        <template slot-scope="scope">
          {{ scope.row.firstDayStartTime ? scope.row.firstDayStartTime.substring(0, 16) : "" }}
        </template>
      </el-table-column>
      <el-table-column label="执行时间" prop="hisFrequencySchedule" width="160" header-align="center"></el-table-column>
      <el-table-column label="修改" width="50" align="center">
        <template slot-scope="scope" v-if="scope.row.modifyFlag == true">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="modifyOrder(scope.$index, scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="途径" prop="orderRule" width="120" header-align="center"></el-table-column>
      <el-table-column label="次数" prop="hisFrequency" width="80" align="center"></el-table-column>
      <el-table-column label="医生说明" prop="orderDescription" width="200" header-align="center"></el-table-column>
      <el-table-column label="结束时间" width="160" align="center">
        <template slot-scope="scope">
          {{ scope.row.endDateTime ? scope.row.endDateTime.substring(0, 16) : "" }}
        </template>
      </el-table-column>
      <el-table-column label="摆药截至时间" width="160" align="center">
        <template slot-scope="scope">
          {{ scope.row.lastPerformTime ? scope.row.lastPerformTime.substring(0, 16) : "" }}
        </template>
      </el-table-column>
      <el-table-column label="审核人" prop="checkEmployeeId" width="120" align="center"></el-table-column>
      <el-table-column label="审核时间" width="160" align="center">
        <template slot-scope="scope">
          {{ scope.row.checkDateTime ? scope.row.checkDateTime.substring(0, 16) : "" }}
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="执行时间修改"
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      width="600px"
      v-if="dialogFormVisible"
      element-loading-text="保存中……"
    >
      <frequency
        v-if="dialogFormVisible"
        :frequency="frequency"
        @frequencySelect="frequencySelect"
        @close="close"
        ref="selectFrequency"
      ></frequency>
    </el-dialog>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetPatientOrderDetail, CheckOrder } from "@/api/PatientOrder";
import { GetServerDateTime, GetOneSettingByTypeAndCode } from "@/api/Setting";
import { mapGetters } from "vuex";
import frequency from "./components/OrdersFrequency";
export default {
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
    }),
  },
  components: {
    baseLayout,
    frequency,
  },
  watch: {
    "patient.inpatientID": {
      handler(newValue) {
        this.list = [];
        if (!newValue) return;
        this.init();
      },
      immediate: true,
    },
  },
  data() {
    return {
      list: [],
      dataList: [],
      cloneData: [],
      checkedValue: "1",
      orderSchedule: undefined,
      dialogFormVisible: false,
      listResult: [],
      switchAll: true,
      frequency: {
        row: undefined,
        schedule: undefined,
        startTimeOfFirstDay: undefined, //首日开始时间
        timesOfFistDay: undefined, //首日几次
      },
      serverDateTime: undefined, //服务器时间
      loading: false,
      loadingText: "保存中……",
      OrderFrequencySwitch: false, //医嘱频次确认标记
      orderFrequencySetting: undefined, //医嘱频次检核Setting
      orderCheckBoole: false,
    };
  },
  methods: {
    async init() {
      this.switchAll = true;
      await this.getList();
      await this.getOrderFrequencySwitch();
      //默认显示未审核 -- GPC
      this.orderSwitch();
      this.orderCheckShowTip();
    },
    async getList() {
      //查无病人数据
      if (!this.patient) {
        return;
      }
      //数据清空
      this.list = [];
      this.orderCheckBoole = false;
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      // 获取病人医嘱信息;
      this.loadingText = "加载中……";
      this.loading = true;
      await GetPatientOrderDetail(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          let datas = result.data;
          this.cloneData = this._common.clone(datas);
          this.dataList = datas;
          this.list = datas;
          this.checkedValue = "1";
        }
      });
    },

    //开关配置：医嘱频次内容完整才能审核
    getOrderFrequencySwitch() {
      let params = {
        settingType: 109,
        settingCode: "OrderFrequencyCheck",
      };
      GetOneSettingByTypeAndCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.orderFrequencySetting = result.data;
          if (this.orderFrequencySetting.typeValue == "True") {
            this.OrderFrequencySwitch = true;
          } else if (this.orderFrequencySetting.typeValue == "False") {
            this.OrderFrequencySwitch = false;
          } else {
            this.OrderFrequencySwitch = undefined;
          }
        }
      });
    },

    //切换医嘱类型
    radioChange() {
      var value = "全部";
      var i = 0;
      this.list = [];
      if (this.checkedValue == "1") {
        value = "全部";
      }
      if (this.checkedValue == "2") {
        value = "临时";
      }
      if (this.checkedValue == "3") {
        value = "长期";
      }
      if (value == "全部") {
        this.list = this.dataList;
      } else {
        for (i = 0; i < this.dataList.length; i++) {
          if (this.dataList[i].orderType == value) {
            this.list.push(this.dataList[i]);
          }
        }
      }
    },
    close() {
      this.dialogFormVisible = false;
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.modifyMastFlag == true) {
        return "modify-row";
      } else {
        return "";
      }
    },

    //选择频次
    async frequencySelect(Schedule, startTimeOfFirstDay, row, selectedFrequency) {
      if (row.hisFrequencySchedule != Schedule) {
        row.hisFrequencySchedule = Schedule;
        row.dataModifyFlag = true;
      }
      row.frequencyID = selectedFrequency[1];

      var firstDayTime = this._datetimeUtil.formatDate(row.startDate, "yyyy-MM-dd") + " " + startTimeOfFirstDay;
      if (row.firstDayStartTime != firstDayTime && startTimeOfFirstDay != null) {
        await this.getServerDateTime();
        var startDate = this._datetimeUtil.formatDate(row.startDate, "yyyy-MM-dd");
        var myDate = this._datetimeUtil.formatDate(this.serverDateTime, "yyyy-MM-dd"); //获取当前时间
        //只能修改当天的首日
        if ((myDate = startDate)) {
          row.firstDayStartTime =
            this._datetimeUtil.formatDate(row.startDate, "yyyy-MM-dd") + " " + startTimeOfFirstDay;
          row.dataModifyFlag = true;
        }
        if (startTimeOfFirstDay == "") {
          row.firstDayStartTime = "";
          row.dataModifyFlag = true;
        }
      }
    },

    //审核
    CheckOrderMain() {
      if (this.list.length <= 0) {
        return;
      }
      if (this.OrderFrequencySwitch) {
        if (!this.judgeOrder()) {
          return;
        }
      }
      let _this = this;
      _this
        .$confirm("是否要审核吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          //审核
          this.loading = true;
          this.loadingText = "保存中……";
          CheckOrder(this.list).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              this._showTip("success", "审核成功");
              _this.init();
            }
          });
        })
        .catch(() => {});
    },
    //判断医嘱频次是否有
    judgeOrder() {
      let judge = true;
      for (let index = 0; index < this.list.length; index++) {
        if (
          this.list[index].frequencyID == null &&
          this.list[index].printFlag != "*" &&
          !this.list[index].hisFrequencySchedule &&
          this.list[index].modifyFlag &&
          this.list[index].recordFlag
        ) {
          this._showTip("warning", "请确认医嘱：【" + this.list[index].orderContent + "】的频次");
          judge = false;
          break;
        }
      }
      return judge;
    },

    //修改执行时间
    modifyOrder(index, row) {
      this.dialogFormVisible = true;
      this.frequency.row = row;
      this.frequency.schedule = "";
      this.frequency.startTimeOfFirstDay = "";
      if (row.hisFrequencySchedule != null) {
        if (row.hisFrequencySchedule.includes(":")) {
          var place = row.hisFrequencySchedule.lastIndexOf(":");
          if (!place) {
            this.frequency.schedule = row.hisFrequencySchedule;
          } else {
            this.frequency.schedule = row.hisFrequencySchedule.substring(0, place);
          }
        } else {
          this.frequency.schedule = row.hisFrequencySchedule;
        }
      }

      if (row.firstDayStartTime != null) {
        this.frequency.startTimeOfFirstDay = this._datetimeUtil.formatDate(row.firstDayStartTime, "hh:mm");
      }
      if (this.$refs.selectFrequency) {
        this.$refs.selectFrequency.showView();
      }
    },

    mergeCells({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 3 || columnIndex > 6) {
        if (row.spanNum > 0) {
          return {
            rowspan: row.spanNum,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    orderSwitch() {
      if (this.switchAll) {
        let orderList = this.cloneData.filter((item) => {
          return (
            item.checkEmployeeId == null &&
            item.printFlag != "*" &&
            item.OrderPattern != "A" &&
            item.OrderPattern != "B"
          );
        });
        this.dataList = orderList;
        this.list = orderList;
        //判断是否有医嘱需要审核
        if (orderList.length) {
          this.orderCheckBoole = true;
        }
      } else {
        this.dataList = this.cloneData;
        this.list = this.cloneData;
      }
      this.radioChange();
    },
    //获取服务器时间
    async getServerDateTime() {
      await GetServerDateTime().then((result) => {
        if (this._common.isSuccess(result)) {
          this.serverDateTime = result.data;
        }
      });
    },
    //有无医嘱需要审核进行提示
    orderCheckShowTip() {
      if (this.orderCheckBoole) {
        this._showTip("warning", "有新医嘱需要审核");
      } else {
        this._showTip("warning", "无新医嘱需要审核");
      }
    },
    /**
     * description: 银川市一医嘱刷新
     * return {*}
     */
    async refreshData() {
      this.loading = true;
      this.loadingText = "加载中……";
      //页面数据刷新
      await this.init();
      this.loading = false;
    },
    /**
     * description: 获得同步刷新的请求参数 -RouterList.RefreshAPI = RefreshOrder
     * return {*}
     */
    getRefreshParams() {
      this.loading = true;
      return { caseNumber: this.patient.caseNumber };
    },
  },
};
</script>
<style lang="scss">
.order-check {
  .header {
    .container-switch,
    .header-radio {
      display: inline-block;
      width: auto;
    }
    .container-switch {
      margin-left: 30px;
      .switch-text {
        position: relative;
        top: 2px;
      }
    }
    .header-button {
      float: right;
      margin-bottom: 5px;
    }
  }
  .order-table .modify-row {
    background-color: #fce8d8;
  }
}
</style>
