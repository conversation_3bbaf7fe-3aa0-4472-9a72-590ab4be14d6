<!--
 * FilePath     : \src\pages\dictionaryMaintain\hospitalBuilding\floor.vue
 * Author       : 来江禹
 * Date         : 2022-07-24 14:28
 * LastEditors  : 来江禹
 * LastEditTime : 2022-09-12 14:31
 * Description  : 楼层维护页面，对楼栋对应楼层进行维护管理
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="hospital-floor">
    <div class="hospital-floor-header" slot="header">
      <span class="btn-list">
        <el-button type="success" icon="iconfont icon-add" @click="addFloor">新增</el-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
        <el-button class="print-button" icon="iconfont icon-back" @click="goBack">返回</el-button>
      </span>
      <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    </div>
    <div class="floor-table">
      <el-table :data="floorTabList" border stripe>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="楼栋" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.buildID">{{ scope.row.buildName }}</span>
            <el-select
              v-else
              class="table-build-select"
              v-model="scope.row.buildListId"
              placeholder="请选择楼栋"
              @change="getTypeValue(scope.row)"
            >
              <el-option
                v-for="(item, index) in buildList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="楼层名称" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.floorID">{{ scope.row.floorName }}</span>
            <el-input
              v-else
              calss="table-floor-input"
              v-model="scope.row.floorListName"
              placeholder="请输入楼层名称"
              @change="getFloorName()"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" width="80px" align="left">
          <template slot-scope="scope">
            <!-- 删除按钮 -->
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="deletFloor(scope.row.floorID, scope.row.floorCode, scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import progressView from "@/components/progressView";
import {
  GetFloorDatas,
  SaveBuildDatas,
  DeleteSettingDescriptionFloorDatas,
  GetSettingDescriptionOne,
} from "@/api/WardMaintenance";
export default {
  components: {
    baseLayout,
    stationSelector,
    progressView,
  },
  data() {
    return {
      buildList: [],
      floorTabList: [],
      settingTypeCode: "WardFloor",
      buildIndex: "",
      code: undefined,
      successCode: "",
      //进度条开关
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
    };
  },
  created() {
    this.refresh();
  },
  methods: {
    /**
     * description: 进度条关闭函数
     * return {*}
     */
    progressClose() {
      this.progressFlag = false;
      this.renewMessageData();
    },
    /**
     * description: 重置进度条
     * return {*}
     */
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    /**
     * description: 刷新页面数据
     * return {*}
     */
    async refresh() {
      this.floorTabList = [];
      this.buildList = [];
      this.getSettingfloor("WardBuilding");
      this.getSettingSelect();
    },
    /**
     * description: 新增一行数据
     * return {*}
     */
    addFloor() {
      let row = {
        buildingName: "",
        buildingID: "",
        floorName: "",
        floorID: "",
        floorCode: "",
      };
      this.floorTabList.push(row);
    },
    /**
     * description: 删除当前行数据
     * param {*} index
     * param {*} settingTypeCode
     * return {*}
     */
    deletFloor(index, settingTypeCode, row) {
      if (index == "" && settingTypeCode == "") {
        let index = this.floorTabList.findIndex((floor) => floor == row);
        if (index >= 0) {
          this.floorTabList.splice(index, 1);
        }
      } else {
        this._deleteConfirm("确定删除数据么？", (flag) => {
          if (flag) {
            // 确认删除
            let params = {
              TypeValue: index,
              SettingTypeCode: settingTypeCode,
            };
            DeleteSettingDescriptionFloorDatas(params).then((res) => {
              if (this._common.isSuccess(res)) {
                this._showTip("success", "删除成功");
                this.refresh();
              }
            });
          }
        });
      }
    },
    /**
     * description: 获取楼栋列下拉框数据
     * return {*}
     */
    async getSettingSelect() {
      let params = {
        SettingTypeCode: "WardBuilding",
      };
      await GetSettingDescriptionOne(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            list.forEach((build) => {
              let params = {
                Description: build.description,
                TypeValue: build.typeValue,
              };
              this.buildList.push(params);
            });
          }
        }
      });
    },
    /**
     * description: 获取当前页面已经维护好的表格数据
     * param {*} SettingTypeCode
     * return {*}
     */
    async getSettingfloor(SettingTypeCode) {
      let params = {
        SettingTypeCode: SettingTypeCode,
      };
      await GetFloorDatas(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            list.forEach((floor) => {
              let param = {
                buildName: floor.buildName,
                buildID: floor.buildID,
                floorName: floor.floorName,
                floorID: floor.floorID,
                floorCode: floor.floorCode,
              };
              this.floorTabList.push(param);
            });
          }
        }
      });
    },
    /**
     * description:获取新增列输入框内输入的数据，以便于保存数据
     * return {*}
     */
    getFloorName() {
      let modifyDatas = this.floorTabList;
      if (modifyDatas.length == 0) {
        return undefined;
      }
      return modifyDatas;
    },
    /**
     * description: 获取下拉框选中的数据，以便于数据写入settingTypeCode字段，实现保存
     * param {*} index
     * return {*}
     */
    getTypeValue(row) {
      if (!row.buildListId) {
        return;
      }
      this.buildIndex = row.buildListId;
    },
    /**
     * description: 保存新增列的数据，实现批量保存
     * return {*}
     */
    async save() {
      let datas = this.getFloorName();
      let successMessage = "";
      let failMessage = "";
      for (let index = 0; index < datas.length; index++) {
        const floorData = datas[index];
        this.progressFlag = true;
        //判断下拉框输入框是否有数据没有输入，全部输入执行保存
        if (floorData.floorListName && floorData.buildListId) {
          let params = {
            SettingType: "216",
            SettingTypeCode: this.settingTypeCode + "_" + floorData.buildListId,
            Description: floorData.floorListName,
          };
          let messageItem = floorData.floorListName;
          await SaveBuildDatas(params).then((res) => {
            if (res.code == 1) {
              successMessage = index == 0 ? messageItem : successMessage + "," + messageItem;
            } else {
              failMessage = failMessage + " " + messageItem;
            }
          });
        }
        //配置进度条内容
        let progress = (((index + 1) / datas.length) * 100).toFixed(0);
        //配置进度条内容
        this.messageData[0].value = Number(progress);
        this.messageData[1].value = successMessage;
        this.messageData[2].value = failMessage;
        this.messageData[3].value = "";
      }
      await this.refresh();
    },
    /**
     * description: 返回按钮跳转页面
     * return {*}
     */
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss">
.hospital-floor {
  .hospital-floor-header {
    .btn-list {
      display: block;
      float: right;
    }
  }
  .floor-table {
    .table-build-select,
    .table-floor-input {
      min-width: 96%;
    }
  }
}
</style>
