<!--
 * FilePath     : \src\autoPages\recordSupplement\components\turningRecord.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-22 15:58
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-22 16:08
 * Description  : 翻身记录单补录
 * CodeIterationRecord: 
 -->
<template>
  <nursing-record
    :patientInfo="patient"
    :supplementPatient="supplemnentPatient"
    data-source="turningRecord"
  ></nursing-record>
</template>

<script>
import nursingRecord from "./nursingRecord";
export default {
  components: {
    nursingRecord,
  },
  props: {
    patient: {
      type: Object,
      default: () => {},
    },
    supplemnentPatient: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style>
</style>