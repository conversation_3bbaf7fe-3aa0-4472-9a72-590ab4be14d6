<!--
 * FilePath     : \src\pages\handover\multipleHandonByNurse.vue
 * Author       : 苏军志
 * Date         : 2021-3-12 08:07
 * Description  : 批量接班
-->
<template>
  <base-layout class="handover" v-loading="loading" headerHeight="auto" :element-loading-text="loadingText">
    <div slot="header">
      <div class="handover-header">
        <div class="level-one-1">
          <div class="level-two">
            <span>班别日期：</span>
            <el-date-picker
              v-model="handoverDate"
              :clearable="false"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              class="date-picker"
              placeholder="选择日期"
              @change="dayChange"
            ></el-date-picker>
          </div>
          <div class="level-two" v-if="!normalFlag">
            <span>起始时间：</span>
            <el-date-picker
              class="date-picker"
              v-model="startDateTime"
              type="datetime"
              :clearable="false"
              placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              @change="selectShift"
            ></el-date-picker>
          </div>
        </div>
        <div class="level-one-2">
          <div class="level-two-shift-select">
            <shift-selector
              width="163px"
              :stationID="stationID"
              v-model="handoverShiftID"
              @select-item="selectShiftItem"
            ></shift-selector>
          </div>
          <div class="level-two" v-if="!normalFlag">
            <span>结束时间：</span>
            <el-date-picker
              @change="selectShift"
              class="date-picker"
              v-model="endDateTime"
              type="datetime"
              :clearable="false"
              placeholder="选择日期时间"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
            />
          </div>
        </div>
        <div class="level-one-3">
          <span>责任护士：</span>
          <el-select class="nurse-select" @change="getAttendancePatient" v-model="userID" placeholder="请选择">
            <el-option v-for="item in attendance" :key="item.shift" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </div>
      </div>
    </div>
    <base-layout class="handover-wrap">
      <div slot="header">
        <el-radio-group v-model="normalFlag" @change="typeChange">
          <el-radio :label="true" size="mini">正常接班</el-radio>
          <el-radio :label="false" size="mini">班内接班</el-radio>
        </el-radio-group>
        <div class="top-btn">
          <el-button type="primary" class="query-button" icon="iconfont icon-search" @click="getAttendancePatient">
            查询
          </el-button>
          <el-button type="primary" @click="submitConfirm" icon="iconfont icon-handon">接班</el-button>
        </div>
      </div>
      <div slot-scope="layout" :style="{ height: layout.height - (normalFlag ? 0 : 36) + 'px' }">
        <u-table
          :data="handoverData ? handoverData : []"
          border
          stripe
          :height="layout.height - (normalFlag ? 0 : 36)"
          highlight-current-row
          :row-height="30"
          use-virtual
          @selection-change="handleSelectionChange"
          row-key="handoverID"
        >
          <u-table-column type="selection" width="55" align="center" :selectable="selectable"></u-table-column>
          <u-table-column v-if="normalFlag" key="important" width="80px" label="重点" align="center">
            <template slot-scope="scope">
              <div :label="item" v-for="(item, index) in scope.row.keySigns" :key="index">
                {{ getKeySign(item) }}
              </div>
            </template>
          </u-table-column>

          <u-table-column width="100" prop="bedNum" label="患者" key="bedNumber" align="center">
            <template slot-scope="scope">
              <div>{{ scope.row.bedNum + "床" }}</div>
              <div>{{ scope.row.patientName }}</div>
            </template>
          </u-table-column>
          <u-table-column
            v-if="normalFlag"
            width="80px"
            key="handoffNurseName"
            prop="handoffNurseName"
            label="交班人"
          ></u-table-column>
          <u-table-column v-if="normalFlag" key="CompletionDegree" width="80px" label="完成度">
            <template slot-scope="scope">
              {{ scope.row.completedSchedule + "/" + scope.row.totalSchedule }}
            </template>
          </u-table-column>
          <u-table-column label="S-现状" class-name="cell-style" key="situation">
            <template slot-scope="scope" v-if="scope.row.situation">
              <div v-html="scope.row.situation"></div>
            </template>
          </u-table-column>
          <u-table-column label="B-背景" class-name="cell-style" key="background">
            <template slot-scope="scope">
              <div v-html="scope.row.background"></div>
            </template>
          </u-table-column>
          <u-table-column class-name="cell-style" key="assess" label="A-评估">
            <template slot-scope="scope">
              <div v-html="scope.row.assement"></div>
            </template>
          </u-table-column>
          <u-table-column label="R-建议" class-name="cell-style" key="recommendation">
            <template slot-scope="scope">
              <div v-html="scope.row.recommendation"></div>
            </template>
          </u-table-column>
          <u-table-column key="plan" label="接班状态" width="80px" align="center">
            <template slot-scope="scope">
              <div v-if="!scope.row.handoverID">未交班</div>
              <div v-else>
                {{ scope.row.handonNurse ? "已接班" : "未接班" }}
              </div>
              <div v-if="scope.row.schedule">已排程</div>
              <div v-if="!scope.row.schedule">未排程</div>
            </template>
          </u-table-column>
          <u-table-column label="详情" key="humPic" width="100px" align="center">
            <template slot-scope="scope">
              <body-image
                v-if="scope.row.handoverID"
                :type="'button'"
                :handoverID="scope.row.handoverID"
                :patientName="scope.row.patientName"
              ></body-image>
              <el-button v-if="scope.row.handoverID != null" type="text" @click="generalPlan(scope.row.inpatientID)">
                计划总览
              </el-button>
            </template>
          </u-table-column>
        </u-table>
        <el-dialog
          v-dialogDrag
          :close-on-click-modal="false"
          title="计划总览"
          :visible.sync="planVisiable"
          custom-class="no-footer"
        >
          <el-table
            :row-class-name="tableRowClassName"
            class="dialogPlanTable"
            @row-click="addStyle"
            :span-method="cellMerge"
            border
            :data="planList"
            height="100%"
            row-key="handoverID"
          >
            <el-table-column prop="problem" label="护理问题/集束护理" min-width="160"></el-table-column>
            <el-table-column prop="nursingGoal" label="护理目标"></el-table-column>
            <el-table-column prop="intervention" label="措施"></el-table-column>
            <el-table-column prop="frequency" label="频次" width="80"></el-table-column>
            <el-table-column prop="frequenyDescription" label="频次说明"></el-table-column>
            <el-table-column label="开始日期" align="center" width="120">
              <template slot-scope="scope">{{ scope.row.startDate.substring(0, 10) }}</template>
            </el-table-column>
            <el-table-column label="结束日期" align="center" width="120">
              <template>
                <template slot-scope="scope">
                  {{ scope.row.endDate == null ? "" : scope.row.endDate.substring(0, 10) }}
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-dialog>
        <!-- 修改交班记录 -->
        <el-dialog v-dialogDrag :close-on-click-modal="false" title="修改" :visible.sync="openEditDialog">
          <handover :componentData="{ handoverInfo: handoverInfo }" :risk="false" :humanPic="false" />
          <div slot="footer">
            <el-button type="primary" @click="openEditDialog = false">确 认</el-button>
          </div>
        </el-dialog>
        <el-dialog
          v-dialogDrag
          :close-on-click-modal="false"
          title="批量接班汇总"
          width="60%"
          @close="summaryClose"
          :visible.sync="summarytip"
          class="all-item"
        >
          <el-table border :data="responseData" row-key="handoverID">
            <el-table-column align="center" width="70" prop="bedNum" label="床号"></el-table-column>
            <el-table-column prop="patientName" width="100" label="姓名" header-align="center"></el-table-column>
            <el-table-column align="center" :label="item.description" :key="index" v-for="(item, index) in checkArray">
              <template slot-scope="scope">
                <i v-if="!scope.row[fixProp(item.typeValue)]" class="iconfont icon-check-mark"></i>
              </template>
            </el-table-column>
            <el-table-column prop="isSuccess" width="100" align="center" label="接班结果" header-align="center">
              <template slot-scope="scope">
                <i v-if="scope.row.isSuccess" class="iconfont icon-check-mark"></i>
              </template>
            </el-table-column>
            <el-table-column prop="checkResultStr" min-width="100" label="状态" header-align="center"></el-table-column>
          </el-table>
          <div slot="footer">
            <el-button type="primary" @click="summarytip = false">确 认</el-button>
          </div>
        </el-dialog>
      </div>
    </base-layout>
  </base-layout>
</template>
<script>
import { mapGetters } from "vuex";
import { MultiHandOn, GetPreviousMultiHandOff } from "@/api/MultiHandover";
import { InClassHandon, GetInclassHandon } from "@/api/InClassHandover";
import { GetBySettingTypeCodeByArray } from "@/api/Setting";
import { GetPatientInterventionList } from "@/api/Intervention";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetAttendanceShiftNurse } from "@/api/Attendance";
import shiftSelector from "@/components/selector/shiftSelector";
import Handover from "@/components/handoverSBAR";
import BaseLayout from "@/components/BaseLayout";
import bodyImage from "@/components/bodyImage";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  components: {
    BaseLayout,
    shiftSelector,
    Handover,
    bodyImage,
  },
  watch: {
    normalFlag: {
      async handler(newV) {
        this.handoverData = [];
        this.attendance = [];
        this.userID = undefined;
        this.timeManage();
        await this.selectShift();
      },
    },
  },
  created() {
    //病区
    this.stationID = this.user.stationID;
    //取得交班数据
    this.getNowStationShift();
  },
  data() {
    return {
      //使用人员
      userID: undefined,
      //病区
      stationID: undefined,
      //派班
      attendance: [],
      //交班班别序號
      handoverShiftID: undefined,
      //交班班别代碼
      handoverShift: undefined,
      //病区班别清单
      stationShifts: [],
      //交班日期
      handoverDate: undefined,
      //交班数据
      handoverData: [],
      //加载
      loading: false,
      //loading显示文字
      loadingText: "正在加载...",
      //打开编辑
      openEditDialog: false,
      //交班修改內容
      handoverInfo: Object,
      //正常交班为True班内交班为False
      normalFlag: true,
      //班次开始时间
      startDateTime: this._datetimeUtil.formatDate(new Date()),
      //交班汇整结束日期时间
      endDateTime: this._datetimeUtil.formatDate(new Date()),
      //dialog计划总览 表头名称
      planHeader: "",
      //计划总览集合
      planList: [],
      //批量接班汇总dialogControl
      summarytip: false,
      // 班次时间集合
      timeList: {},
      //护理计划dialog
      planVisiable: false,

      submitArray: [],
      //提交反馈
      responseData: [],
      //接班检查项
      checkArray: [],

      handonResult: "",
      //数组合并数据
      spanArr: [],
      pos: 0,
    };
  },
  methods: {
    //调整接班结果字段与后端不一致问题  --GPC
    fixProp(prop) {
      if (prop) {
        return prop.split("_")[0];
      }
    },
    //日期改变
    async dayChange() {
      this.userID = undefined;
      this.handoverData = [];
      this.submitArray = [];
      this.selectShift();
    },
    //取得被派班病人
    getAttendancePatient() {
      if (this.userID == undefined) {
        this._showTip("warning", "请选择护士!");
        this.loading = false;
        return;
      }
      //数据清空
      this.handoverData = [];
      this.submitArray = [];
      if (this.normalFlag) {
        let params = {
          nurserID: this.userID,
          shiftID: this.handoverShiftID,
          shiftDate: this.handoverDate,
        };
        GetPreviousMultiHandOff(params).then((response) => {
          if (this._common.isSuccess(response)) {
            this.handoverData = response.data;
            this.loading = false;
          }
        });
      } else {
        let params = {
          shiftDate: this.handoverDate,
          stationID: this.stationID,
          nurseID: this.userID,
          shiftID: this.handoverShiftID,
        };
        GetInclassHandon(params).then((data) => {
          this.loading = false;
          if (this._common.isSuccess(data)) {
            this.handoverData = data.data;
          }
        });
      }
    },
    async submitConfirm() {
      // 遍历
      if (!this.submitArray.length) {
        this._showTip("warning", "无接班病人！");
        this.loading = false;
        return;
      }
      //只加载一次
      if (this.checkArray.length < 1) {
        await this.getCheckArray();
      }
      let repeatCommit = [];
      let commitArray = [];
      // TODO: 重复接班病人提醒  接班病人提醒
      for (let i = 0; i < this.submitArray.length; i++) {
        let item = this.submitArray[i];
        commitArray.push(item.bedNum + "床");
        if (item.handonNurse) {
          //已存在 是重复接班
          repeatCommit.push(item.bedNum + "床");
        }
      }

      let message =
        "<span>您要接班的床位是:</span><strong style='color:red;'>" + commitArray.toString() + "</strong><br/>";
      if (repeatCommit.length > 0) {
        message += "<span>重复接班床位:</span><strong style='color:red;'>" + repeatCommit.toString() + "</strong><br>";
        message += "<strong style='color:red;' >重复接班会覆盖上一次接班记录</strong>";
      }
      // this.$confirm('确认要接班吗？', '接班确认', {
      //   cancelButtonText: '取消',
      //   confirmButtonText: '确定',
      //   type: 'warning'
      // }).then(() => {
      //   this.submit();
      // });

      this.$msgbox({
        title: "提示",
        message: message,
        dangerouslyUseHTMLString: true,
        showCancelButton: true,
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        customClass: "multihandoff-msgbox-class",
      })
        .then(() => {
          this.submit();
        })
        .catch((error) => error);
    },
    // 提交
    async submit() {
      this.loading = true;
      let completeArray = [];
      this.responseData = [];
      //班内交班
      if (!this.normalFlag) {
        this.inClassHandon();
        return;
      }
      for (let i = 0; i < this.submitArray.length; i++) {
        let params = {
          handoverID: this.submitArray[i].handoverID,
          shiftID: this.handoverShiftID,
          shiftDate: this.handoverDate,
        };
        await this.singleMultihandon(params).then((data) => {
          data.bedNum = this.submitArray[i].bedNum;
          data.patientName = this.submitArray[i].patientName;
          this.responseData.push(data);
          completeArray.push(data.bedNum);
          this.loadingText = "已完成" + completeArray.toString() + "床。";
        });
      }
      this.getAttendancePatient();
      this.loading = false;
      this.summarytip = true;
    },
    async singleMultihandon(params) {
      let data;
      await MultiHandOn(params).then((response) => {
        if (this._common.isSuccess(response)) {
          data = response.data;
        }
      });
      return data;
    },
    getCheckArray() {
      let params = {
        settingTypeCode: "HandOffPatientCheck,HandoffNurseCheck",
      };
      GetBySettingTypeCodeByArray(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.length < 1) return;
          for (let i = 0; i < response.data.length; i++) {
            //typeValue首字母小写
            let str = response.data[i].typeValue;
            response.data[i].typeValue = response.data[i].typeValue.replace(str[0], str[0].toLowerCase());
          }
        }
        this.checkArray = response.data.filter((m) => m.typeValue != "deathPatient_E");
      });
    },
    typeChange() {
      this.handoverData = [];
      this.submitArray = [];
      this.timeManage();
    },
    //取得班别数据
    getNowStationShift() {
      GetNowStationShiftData().then((data) => {
        if (this._common.isSuccess(data)) {
          let result = data.data;
          //timeList用以重置班次开始时间
          this.timeList = data.data;
          this.handoverDate = result.shiftDate;
          //当前班别(默许班别)
          this.handoverShift = result.nowShift.shift;
          //取得派班数据
          this.selectShift();
        }
      });
    },
    //取得已派班护士
    selectShift() {
      //派班人员初始化
      this.attendance = [];
      this.handoverData = [];
      this.userID = undefined;
      let params = {
        shiftDate: this.handoverDate,
        stationID: this.stationID,
        shift: this.handoverShift,
        _id: this._common.guid(),
      };
      GetAttendanceShiftNurse(params).then((data) => {
        if (this._common.isSuccess(data)) {
          if (!data.data || !data.data.length) {
            return;
          }
          //派班数据
          let tempAttendance = data.data;
          this.userID = tempAttendance[0].userID;
          tempAttendance.forEach((item) => {
            const rowItem = {
              value: item.userID,
              label: item.name,
            };
            //默许登入人员
            if (item.userID == this.user.userID) {
              this.userID = this.user.userID;
            }
            if (!this.attendance.find((data) => data.value == rowItem.value)) {
              this.attendance.push(rowItem);
            }
          });
          this.getAttendancePatient();
        }
      });
    },
    generalPlan(inpatientID) {
      let params = {
        inpatientID: inpatientID,
      };
      GetPatientInterventionList(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.length == 0) {
            this._showTip("warning", "未查到病人计划！");
          } else {
            this.planList = response.data;
            this.getSpanArr(this.planList);
            this.addClass(this.planList);
            this.planVisiable = true;
          }
        }
      });
    },
    //日期改变
    handleSelectionChange(val) {
      this.submitArray = val;
    },
    //取得交班班别代码
    async selectShiftItem(shiftInfo) {
      this.handoverShift = shiftInfo.shift;
      this.handoverShiftID = shiftInfo.id;
      this.timeManage();
      await this.selectShift();
    },
    //班内交班提交
    inClassHandon() {
      this.loading = false;
      let params = [];
      this.submitArray.forEach((item) => {
        params.push(item.handoverID);
      });
      InClassHandon(params).then((data) => {
        if (this._common.isSuccess(data)) {
          if (data.data) {
            this._showTip("success", "接班完成");
            this.handoverData = [];
            this.getAttendancePatient();
          } else {
            this._showTip("warning", "接班失败");
          }
        }
        this.loading = false;
      });
    },
    getKeySign(item) {
      if (item.isCheck) return item.signName;
    },
    summaryClose() {
      this.loadingText = "正在加载...";
    },
    //控制可选
    selectable(row, index) {
      if (!row.handoverID) return false;
      return true;
    },
    timeManage() {
      if (this.normalFlag) {
        // 重置当前班次时间
        this.startDateTime = "";
        this.endDateTime = "";
      } else {
        // 重置班内交班时间
        for (let i = 0; i < this.timeList.stationShifts.length; i++) {
          if (this.handoverShiftID == this.timeList.stationShifts[i].id) {
            let time = this.handoverDate.substring(0, 11) + " " + this.timeList.stationShifts[i].shiftStartTime;
            this.startDateTime = new Date(time);
          }
        }
        this.endDateTime = new Date();
      }
    },
    //合并护理计划单元格
    //获取合并数组
    getSpanArr(data) {
      this.spanArr = [];
      this.spanTwoArr = [];
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].problem == data[i - 1].problem) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    //相同项合并
    cellMerge({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        //合并第一列和第二列
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    //同一问题添加相同class
    addClass(value) {
      var oldI;
      for (let i = 0; i < value.length; i++) {
        value[i].classValue = "click";
        if (i == 0) {
          oldI = i;
          value[i].classClickValue = "click_" + oldI;
        } else {
          if (value[i].problemID == value[i - 1].problemID) {
            value[i].classClickValue = "click_" + oldI;
          } else {
            oldI = i;
            value[i].classClickValue = "click_" + oldI;
          }
        }
      }
    },
    tableRowClassName(row) {
      return row.row.classValue + " " + row.row.classClickValue;
    },
    addStyle(row) {
      let tableView = document.getElementsByClassName("dialogPlanTable")[0];
      let trViewList = tableView.getElementsByClassName(row.classValue);
      let clickTrViewList = tableView.getElementsByClassName(row.classClickValue);
      for (let i = 0; i < trViewList.length; i++) {
        trViewList[i].classList.remove("current-row");
      }
      for (let i = 0; i < clickTrViewList.length; i++) {
        clickTrViewList[i].classList.add("current-row");
      }
    },
  },
};
</script>
<style lang="scss">
.handover {
  height: 100%;
  .handover-header {
    line-height: 35px;
    padding-bottom: 3px;
    box-sizing: border-box;
    .level-one-1 {
      vertical-align: top;
      width: 260px;
      display: inline-block;
      .level-two {
        .date-picker {
          width: 160px;
        }
      }
    }
    .level-one-2 {
      vertical-align: top;
      display: inline-block;
      width: 270px;
      .level-two-shift-select {
        margin-left: 28px;
      }
      .level-two {
        margin-left: 5px;
        .date-picker {
          width: 160px;
        }
      }
    }
    .level-one-3 {
      display: inline-block;
      width: 200px;
      vertical-align: top;
      .el-select {
        width: 100px;
      }
    }
    .level-one-4 {
      display: inline-block;
      width: 200px;
      .level-two {
        .el-radio {
          margin-right: 10px;
        }
      }
    }
  }

  .handover-wrap {
    .top-btn {
      float: right;
    }
    .handover-table {
      width: 100%;
      height: 100%;
      .cell-style {
        vertical-align: top;
      }
    }
  }
  .el-dialog {
    &.all-item {
      line-height: 27px;
      .item-name {
        text-align: right;
        display: inline-block;
      }
    }
  }
}
</style>
