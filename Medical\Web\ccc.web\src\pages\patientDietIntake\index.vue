<!--
 * FilePath     : \ccc.web\src\pages\patientDietIntake\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-07-29 14:42
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-06-03 08:50
 * Description  : 饮食记录
 * CodeIterationRecord: 2022-08-18 2876-专项增加带入护理记录选框 -杨欣欣
-->
<template>
  <specific-care
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :recordTitleSlotFalg="true"
    :handOverFlag="handOverArr"
    :nursingRecordFlag="nursingRecordArr"
    :previewFlag="!checkResult"
    :informPhysicianFlag="informPhysicianArr"
    @mainAdd="recordAdd"
    @save="saveDietIntake"
    @getHandOverFlag="getHandOverFlag"
    @getNursingRecordFlag="getNursingRecordFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @cancel="showTemplateFlag = false"
    class="patient-dietIntake"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <div slot="record-title">
      <label>日期：</label>
      <el-date-picker
        class="date-picker"
        v-model="recordDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        @change="getTableView()"
      ></el-date-picker>
    </div>
    <div slot="main-record">
      <el-table :data="recordList" height="100%" border stripe :span-method="cellMerge">
        <el-table-column label="日期" width="100" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="时间" width="60" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="mealPeriod" label="餐段" width="50" align="center"></el-table-column>
        <el-table-column prop="soup" label="汤/粥" width="60" align="center"></el-table-column>
        <el-table-column prop="mainCourse" label="主菜" width="50" align="center"></el-table-column>
        <el-table-column prop="sideDish" label="配菜" width="50" align="center"></el-table-column>
        <el-table-column prop="vegetables" label="蔬菜" width="50" align="center"></el-table-column>
        <el-table-column prop="stapleFood" label="主食" width="50" align="center"></el-table-column>
        <el-table-column prop="fruits" label="水果/果汁" width="60" align="center"></el-table-column>
        <el-table-column prop="beverage" label="饮品" width="50" align="center"></el-table-column>
        <el-table-column prop="dessert" label="点心" width="100" align="center"></el-table-column>
        <el-table-column prop="otherFood" label="其他" width="100" align="center"></el-table-column>
        <el-table-column label="估算营养摄入" width="50" align="center">
          <el-table-column prop="grossEnergy" label="总能量(Kcal)" width="70" align="center"></el-table-column>
          <el-table-column prop="protein" label="蛋白质(g)" width="60" align="center"></el-table-column>
          <el-table-column prop="axunge" label="脂肪(g)" width="60" align="center"></el-table-column>
          <el-table-column prop="carbohydrate" label="碳水化合物(g)" width="60" align="center"></el-table-column>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="100" align="center"></el-table-column>
        <el-table-column prop="nurseName" label="记录人" width="80" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" width="70" align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="recordAdd(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip v-if="scope.row.recordsCode != 'ThrombolysisStart'" content="删除">
              <div @click.stop="recordDelete(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="layoutLoading"
      :element-loading-text="layoutText"
    >
      <div slot="header">
        <span class="label">执行日期:</span>
        <el-date-picker
          class="date-picker"
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        />
        <el-time-picker
          class="time-picker"
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        />
        <station-selector v-model="stationID" label="执行病区:" width="160" />
        <dept-selector label="" width="140" v-model="departmentListID" :stationID="stationID" />
      </div>
      <tabs-layout ref="tabsLayout" :template-list="templateDatas" @change-values="changeValues" @checkTN="checkTN" />
    </base-layout>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import { mapGetters } from "vuex";
import { GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import { GetBringToShiftSetting } from "@/api/Setting.js";
import { GetDietIntakeAssesssView, DietIntakeSave, GetDietIntakeTableView, DeleteDietIntake } from "@/api/DietIntake";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import baseLayout from "@/components/BaseLayout";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
      patient: "getPatientInfo",
    }),
  },
  components: {
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    baseLayout,
  },
  data() {
    return {
      //页面加载
      loading: false,
      layoutLoading: false,
      layoutText: undefined,
      //组件变量
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      handOverArr: [true, false],
      settingHandOver: false,
      nursingRecordArr: [false, false],
      settingBringToNursingRecord: false,
      informPhysicianArr: [true, false],
      //顶部时间变量
      recordDate: undefined,
      //评估模板变量
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      departmentListID: undefined,
      recordsCodeInfo: {},
      templateDatas: [],
      assessDatas: [],
      checkTNFlag: undefined,
      patientScheduleMainID: undefined,
      specificAssessList: [6452670, 6452680, 6452690, 6452700],
      //表格变量
      recordList: [],
      careMainID: undefined,
      scheduleMainID: undefined,
      //表格合并变量
      spanArr: [],
      pos: 0,
      checkResult: true,
    };
  },
  watch: {
    "patient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getTableView();
        }
      },
    },
  },
  mounted() {
    //获取交班配置
    this.getBringHandOverSetting();
    this.getBringToNursingRecordSetting();
    this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
  },

  methods: {
    //获取表格数据
    getTableView() {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        date: this.recordDate,
      };
      this.loading = true;
      GetDietIntakeTableView(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.recordList = res.data;
          if (this.recordList.length > 0) {
            this.getSpanArr(this.recordList);
          }
        }
      });
    },

    //记录新增或修改
    async recordAdd(record) {
      this.checkResult = true;
      if (record) {
        //是否仅本人操作
        this.checkResult = await this._common.checkActionAuthorization(this.user, record.nurseID);
      }
      this.openOrCloseDrawer(true, "新增");
      this.stationID = record ? record.stationID : this.patient.stationID;
      this.departmentListID = record ? record.departmentListID : this.patient.departmentListID;
      this.assessDate = record ? record.assessDate : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = record ? record.assessTime : this._datetimeUtil.getNowTime("hh:mm");
      this.careMainID = record ? record.patientDietIntakeCareMainID : undefined;
      this.$set(this.handOverArr, 1, record ? record.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, record && record.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, record ? record.bringToNursingRecord : this.settingBringToNursingRecord);
      this.getDietIntakeAssessTemplate();
    },

    //记录保存
    saveDietIntake() {
      if (!this.patient || Object.keys(this.recordsCodeInfo).length == 0) {
        return;
      }
      let params = {
        InpatientID: this.patient.inpatientID,
        StationID: this.stationID,
        DepartmentListID: this.departmentListID,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        InterventionID: this.recordsCodeInfo.interventionMainID,
        BringToShift: this.handOverArr[1],
        BringToNursingRecord: this.nursingRecordArr[1],
        informPhysician: this.informPhysicianArr[1],
        Details: this.getDetails(),
      };
      if (this.patientScheduleMainID) {
        params.PatientScheduleMainID = this.patientScheduleMainID;
      }
      if (params.Details.length == 0) {
        // this._showTip("error", "请勾选内容后保存!");
        return;
      }
      if (this.careMainID) {
        params.PatientDietIntakeCareMainID = this.careMainID;
      }
      this.layoutLoading = true;
      this.layoutText = "保存中……";
      DietIntakeSave(params).then((res) => {
        this.layoutLoading = false;
        this.layoutText = "";
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.openOrCloseDrawer(false);
          this.getTableView();
        }
      });
    },

    //记录删除
    async recordDelete(record) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, record.nurseID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (!record || !record.patientDietIntakeCareMainID) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            careMainID: record.patientDietIntakeCareMainID,
          };
          DeleteDietIntake(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getTableView();
            }
          });
        }
      });
    },

    //获取评估模板
    async getDietIntakeAssessTemplate() {
      let params = {
        inpatientID: this.patient.inpatientID,
        departmentListID: this.patient.departmentListID,
        mappingType: "DietIntakeRecord",
        age: this.patient.age,
      };
      this.layoutLoading = true;
      this.layoutText = "加载中……";
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        this.layoutLoading = false;
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo) {
        return;
      }
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        stationID: this.patient.stationID,
        dateOfBirth: this.patient.dateOfBirth,
        inpatientID: this.patient.inpatientID,
      };
      if (this.careMainID) {
        params.dietIntakeCareMainID = this.careMainID;
      }
      this.layoutLoading = true;
      await GetDietIntakeAssesssView(params).then((res) => {
        this.layoutLoading = false;
        if (this._common.isSuccess(res)) {
          this.templateDatas = res.data;
        }
      });
      this.layoutLoading = false;
      this.layoutText = "";
    },
    //获取组件选中值
    changeValues(details) {
      this.assessDatas = details;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    //弹窗开关函数
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      // this.drawerTitle = title;
      this.drawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
    },
    //组装保存detail数据
    getDetails() {
      let details = [];
      if (!this.assessDatas.length) {
        return details;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return details;
      }
      this.assessDatas.forEach((item) => {
        let detail = {
          assessListID: item.assessListID,
          assessListGroupID: item.assessListGroupID,
        };
        if (item.controlerType.trim() == "C" || item.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = item.assessValue;
        }

        details.push(detail);
        let sucAseessListID = this.specificAssessList.find((item) => item != item.assessListID);
        if (sucAseessListID && item.assessValue == "0") {
          details.pop();
        }
      });
      return details;
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "DietIntake",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.handOverArr[0]) {
            this.settingHandOver = res.data;
          }
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "DietIntakeAutoInterventionToRecord",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingBringToNursingRecord = res.data;
        }
      });
    },
    /**
     * description: 组件回传交班flag
     * param {*} flag
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 组件回传带入护理记录单flag
     * param {*} flag
     * return {*}
     */
    getNursingRecordFlag(flag) {
      this.nursingRecordArr[1] = flag;
    },
    //通知医师标记
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },

    //表格合并
    getSpanArr(data) {
      this.spanArr = [];
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].assessDate == data[i - 1].assessDate) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    cellMerge({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        //合并第一列和第二列
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
  },
};
</script>
<style lang="scss">
.patient-dietIntake {
  .date-picker {
    width: 120px;
  }
  .time-picker {
    width: 80px;
  }
  .station-selector .label {
    margin-left: 0px;
  }
}
</style>