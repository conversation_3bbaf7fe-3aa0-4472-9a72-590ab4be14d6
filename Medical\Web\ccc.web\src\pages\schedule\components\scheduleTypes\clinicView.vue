<!--
 * FilePath     : \src\pages\schedule\components\scheduleTypes\clinicView.vue
 * Author       : 郭鹏超
 * Date         : 2020-11-01 17:45
 * LastEditors  : 来江禹
 * LastEditTime : 2025-06-24 16:00
 * Description  : 仪器数据表格组件 用于单病人 批量监测页面 和观察措施页面
-->
<template>
  <div class="clinic-view" :style="{ height: height }">
    <div class="clinic-view-top">
      <i class="iconfont icon-info" @click="showMessage()"></i>
      <label>仪器数据范围：</label>
      <el-select @change="getClinitData()" class="timeRangeSelect" v-model="timeRange" placeholder="数据范围">
        <el-option v-for="(item, index) in timeRangeArr" :key="index" :label="item" :value="item"></el-option>
      </el-select>
    </div>
    <div class="clinic-table">
    <el-table
      height="100%"
      ref="multipleTable"
      border
      :data="clinicData"
      @select="getSelectClinicData"
      style="width: 100%"
      :select-on-indeterminate="false"
      v-if="clinicData.length"
    >
      <el-table-column
        v-if="clinicData.length"
        type="selection"
        align="center"
        :width="convertPX(40)"
      ></el-table-column>
      <el-table-column v-if="clinicData.length" prop="performTime" align="center" label="时间" width="80">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.performTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in tableheaderData"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        align="left"
        min-width="70"
      ></el-table-column>
    </el-table>
  </div>
  </div>
</template>

<script>
import { GetClinicDataByTime } from "@/api/PatientSchedule";
import { GetSettingValuesByTypeCodeAndValue } from "@/api/Setting";
export default {
  props: {
    clinicPrams: {
      type: Object,
      redirect: true,
    },
    value: {
      type: Number,
      default: 30,
    },
    height: {
      type: String,
      default: "100%",
    },
  },
  watch: {
    value: {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        this.timeRange = newVal;
      },
      immediate: true,
    },
    clinicPrams: {
      async handler(newVal) {
        if (!newVal || !newVal.inpatientID) {
          return;
        }
        await this.getSettingTimeRange();
        this.getClinitData();
      },
      deep: true,
      immediate: true,
    },
    timeRange: {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        this.$emit("input", this.timeRange);
      },
      immediate: true,
    },
  },
  data() {
    return {
      tableheaderData: [],
      clinicData: [],
      //仪器数据时间范围
      timeRangeArr: [10, 20, 30, 40, 50, 60],
      timeRange: undefined,
    };
  },
  methods: {
    // 获取仪器数据
    getClinitData() {
      this.tableheaderData = [];
      this.clinicData = [];
      let params = this._common.clone(this.clinicPrams);
      params.timeRange = this.timeRange;
      GetClinicDataByTime(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tableheaderData = res.data.columns;
          this.clinicData = res.data.rows;
        }
      });
    },
    //回传勾选数据
    getSelectClinicData(selection, row) {
      this.selectClinicData = {};
      //实现单选
      this.$refs.multipleTable.clearSelection();
      this.$refs.multipleTable.toggleRowSelection(row);
      this.$emit("getSelectClinicData", row);
    },
    //显示措施提示信息
    showMessage() {
      this._showMessage({
        message: "选择当前排程时间前后多少分钟之内的仪器数据",
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
    /**
     * description: 获取仪器数据范围配置
     * return {*}
     */
    async getSettingTimeRange() {
      let params = {
        settingTypeCode: "ClinicDataTimeRange",
        typeValue: "TimeRange",
      };
      await GetSettingValuesByTypeCodeAndValue(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.timeRange = Number(result?.data ?? 30);
        }
      });
    },
  },
};
</script>

<style lang="scss">
.clinic-view {
  height: 120px;
  .clinic-view-top {
    height: 50px;
    line-height: 50px;
    .timeRangeSelect {
      width: 80px;
    }
  }
  .clinic-table {
    height: calc(100% - 80px);
    .has-gutter {
      .el-table-column--selection {
        .el-checkbox {
          display: none;
        }
      }
    }
  }
}
</style>