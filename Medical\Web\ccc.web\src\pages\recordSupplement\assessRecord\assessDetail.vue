<!--
 * FilePath     : \src\pages\recordSupplement\assessRecord\assessDetail.vue
 * Author       : 李青原
 * Date         : 2020-06-30 19:13
 * LastEditors  : 来江禹
 * LastEditTime : 2023-10-27 09:06
 * Description  :
-->
<template>
  <base-layout
    class="assess-detail"
    v-loading="loading"
    :element-loading-text="loadingText"
    headerHeight="auto"
    :showHeader="true"
  >
    <div class="detail-header" slot="header">
      <station-department-bed-date
        class="header-float"
        @custCkick="selectStationDepartmentBed"
        :stDeptBed="stationDepartmentBed"
        :switch="componentsSwitch"
      ></station-department-bed-date>
      <label class="header-station-label header-float">护理级别：</label>
      <el-select class="header-station-level header-float" v-model="formData.nursingLevel" placeholder="请选择">
        <el-option
          v-for="item in nursingLevelList"
          :key="item.id"
          :label="item.description"
          :value="item.typeValue"
        ></el-option>
      </el-select>
      <label class="header-station-label header-float">评估次数：</label>
      <el-input
        class="header-station-sort header-float"
        v-model.number="formData.sort"
        :disabled="sortFlag"
        @change="getPatientAssessMain()"
      ></el-input>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="save" v-if="isSaveButton">保 存</el-button>
    </div>
    <div class="detail-main">
      <el-table height="100%" :data="data">
        <el-table-column prop="title" label="名称" width="200px" resizable align="center"></el-table-column>
        <el-table-column label="评估内容">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.groupContent"
              type="textarea"
              :autosize="{ minRows: 3 }"
              placeholder="请输入内容"
              resize="none"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import stationDepartmentBedDate from "@/pages/recordSupplement/components/stationDepartmentBedDate.vue";
import { UserSaveCheck } from "@/api/UserCheck";
import {
  GetAssessContentInHospital,
  GetAssessMainRow,
  SaveAssessHistory,
  GetAssessRecordsCodeByDeptID,
} from "@/api/Assess";
import { GetScheduleTop } from "@/api/Setting";
export default {
  components: {
    baseLayout,
    stationDepartmentBedDate,
  },
  props: {
    dischargeAssessData: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      data: [],
      mainID: "",
      bedNumber: "",
      taskCount: 0,
      loadingText: "",
      loading: false,
      assessInfo: undefined,
      // 是否显示保存和暂存
      isSaveButton: true,
      assessMain: {},
      //暂存病区科室及床位
      stationDepartmentBed: {
        stationID: "",
        departmentListID: "",
        bedNumber: "",
        bedId: "",
      },
      // 设置病区科室床位日期组件是否显示
      componentsSwitch: {
        stationSwitch: true,
        departmentListSwitch: true,
        bedNumberSwitch: true,
        dateTimeSwitch: true,
      },
      formData: {
        sort: undefined,
        nursingLevel: "",
      },
      nursingLevelList: [],
      sortFlag: true,
      inpatientData: {},
      recordInfo: {},
    };
  },
  created() {
    this.assessMain = this.dischargeAssessData.assessMain ?? this.$route.query.assessMain;
    this.inpatientData = this.dischargeAssessData.inpatientInfo ?? "";
    this.mainID = this.dischargeAssessData.mainID ?? this.$route.query.mainID;
    this.formData.sort = this.dischargeAssessData.sort ?? this.$route.query.sort;
    this.bedNumber = this.dischargeAssessData.bedNumber ?? this.$route.query.bedNumber;
    this.getPatientAssessMain();
    this.initButton();
    this.getNursingLevelList();
    this.initComponentsData();
  },
  methods: {
    /**
     * description: 检核保存按钮权限
     * return {*}
     */
    async initButton() {
      if (!this.assessMain) {
        return;
      }
      let params = {
        patientAssessMainID: this.assessMain.id,
      };
      await UserSaveCheck(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.isSaveButton = true;
          return;
        } else {
          this.isSaveButton = false;
        }
      });
    },
    /**
     * description: 初始化头部组件数据
     * return {*}
     */
    initComponentsData() {
      if (this.inpatientData) {
        this.sortFlag = false;
      }
      this.stationDepartmentBed.bedNumber = this.assessMain?.bedNumber ?? this.inpatientData.bedNumber;
      this.stationDepartmentBed.departmentListID =
        this.assessMain?.departmentListID ?? this.inpatientData.departmentListID;
      this.stationDepartmentBed.stationID = this.assessMain?.stationID ?? Number(this.inpatientData.stationID);
      this.stationDepartmentBed.bedId = this.assessMain?.bedID ?? this.inpatientData.bedID;
      this.stationDepartmentBed.assessDate = this.assessMain
        ? this._datetimeUtil.formatDate(this.assessMain?.assessDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNow("yyyy-MM-dd");
      this.stationDepartmentBed.assessTime = this.assessMain
        ? this._datetimeUtil.formatDate(this.assessMain?.assessTime, "hh:mm")
        : this._datetimeUtil.getNow("hh:mm");
      this.formData.nursingLevel = this.assessMain?.nursingLevel ?? this.inpatientData.nursingLevelCode;
    },
    /**
     * description: 获取评估模板数据
     * return {*}
     */
    async getPatientAssessMain() {
      if (!this.assessMain && !this.formData.sort) {
        this.$nextTick(() => {
          if (this.formData.sort != "") {
            this._showTip("warning", "请输入【评估次数】获取评估模板数据");
          }
        });
        return;
      }
      // 已有入院护理评估不可以新增入院护理评估
      if (!this.assessMain && this.formData.sort == 1 && this.dischargeAssessData.admissionFlag) {
        this.formData.sort = undefined;
        this._showTip("warning", "患者已有入院护理评估");
        return;
      }
      // 新增检核评估次数
      if (!this.assessMain && this.formData.sort > this.dischargeAssessData.numberOfAssessment) {
        this.formData.sort = undefined;
        this.data = [];
        this._showTip("warning", `患者评估最大次数不可以超过${this.dischargeAssessData.numberOfAssessment}次`);
        return;
      }
      this.loading = true;
      this.loadingText = "加载中……";
      this.data = [];
      await this.getRecordData();
      let param = { assessMainID: this.mainID, num: this.formData.sort, recordCode: this.recordInfo?.recordsCode };
      GetAssessContentInHospital(param).then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          let data = response.data;
          for (let index = 0; index < data.length; index++) {
            const element = {
              typeID: data[index].typeID,
              title: data[index].title,
              model: data[index],
              groupContent: data[index].content,
            };
            this.$set(this.data, index, element);
          }
        }
      });
    },
    /**
     * description: 保存补录评估数据
     * return {*}
     */
    async save() {
      //防止连续点击
      if (this.loading) {
        return;
      }
      this.loading = true;
      this.loadingText = "保存中……";
      if (!this.checkSave()) {
        this.loading = false;
        return;
      }
      if (!this.assessInfo) {
        await this.getInfo();
      }
      this.getSaveParam().then((param) => {
        if (param.length == 0) return;
        SaveAssessHistory(param).then((response) => {
          this.loading = false;
          if (this._common.isSuccess(response)) {
            this.cancel();
            this._showTip("success", "保存成功！");
          }
        });
      });
    },
    /**
     * description: 获取补录评估参数
     * return {*}
     */
    async getSaveParam() {
      return this.data.map((element) => {
        if (!element) return;
        return {
          patientAssessMainID: this.mainID,
          inpatientID: this.assessInfo?.inpatientID ?? this.inpatientData.inpatientID,
          patientID: this.assessInfo?.patientID ?? this.inpatientData.patientID,
          stationID: this.stationDepartmentBed.stationID,
          bedID: this.stationDepartmentBed.bedId,
          caseNumber: this.assessInfo?.caseNumber ?? this.inpatientData.caseNumber,
          chartNo: this.assessInfo?.chartNo ?? this.inpatientData.chartNo,
          bedNumber: this.stationDepartmentBed.bedNumber,
          numberOfAssessment: this.dischargeAssessData.numberOfAssessment ?? this.assessInfo?.numberOfAssessment,
          addDate: this._datetimeUtil.getNow("yyyy-MM-dd hh:mm"),
          groupID: element.typeID,
          groupContent: element.groupContent,
          modifyPersonID: this.assessInfo?.modifyPersonID ?? this.dischargeAssessData.userID,
          modifyDate: this._datetimeUtil.getNow("yyyy-MM-dd hh:mm"),
          deleteFlag: "",
          editFlag: true,
          addFlag: !!this.inpatientData,
          assessDate: this.stationDepartmentBed.assessDate,
          assessTime: this.stationDepartmentBed.assessTime,
          sort: this.formData.sort,
          nursingLevel: this.formData.nursingLevel,
          departmentListID: this.stationDepartmentBed.departmentListID,
          recordCode: this.recordInfo?.recordsCode,
        };
      });
    },
    /**
     * description: 获取评估人员评估数据
     * return {*}
     */
    async getInfo() {
      let param = { mainID: this.mainID };
      await GetAssessMainRow(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.assessInfo = response.data;
        }
      });
    },
    /**
     * description: 弹窗顶部组件数据
     * param {*} val
     * return {*}
     */
    selectStationDepartmentBed(val) {
      this.stationDepartmentBed.bedNumber = val.bedNumber;
      this.stationDepartmentBed.departmentListID = val.departmentListID;
      this.stationDepartmentBed.stationID = Number(val.stationID);
      this.stationDepartmentBed.bedId = val.bedId;
      this.stationDepartmentBed.assessDate = val.assessDate;
      this.stationDepartmentBed.assessTime = val.assessTime;
    },
    /**
     * description: 获取护理级别列表
     * return {*}
     */
    getNursingLevelList() {
      let param = { settingTypeCode: "NursingLevel" };
      GetScheduleTop(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.nursingLevelList = response.data;
        }
      });
    },
    /**
     * description: 获取评估模板recordCod数据
     * return {*}
     */
    async getRecordData() {
      if (!this.inpatientData) {
        return null;
      }
      let params = {
        age: this.inpatientData.age,
        mappingType: this.formData.sort == 1 ? "AdmissionAssess" : "PhysicalAssess",
        inpatientID: this.inpatientData.inpatientID,
        departmentListID: this.stationDepartmentBed.departmentListID,
      };
      await GetAssessRecordsCodeByDeptID(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.recordInfo = response.data;
        }
      });
    },
    /**
     * description: 检核保存日期
     * return {*}
     */
    checkSave() {
      if (!this.stationDepartmentBed.assessDate) {
        this._showTip("warning", "护理评估日期时间为空，请选择日期时间！");
        return false;
      }
      if (!this.dischargeAssessData.admissionDateTimeView || !this.dischargeAssessData.dischargeDateTimeView) {
        this._showTip("warning", "患者出院日期时间为空！");
        return false;
      }
      let assessDate =
        this._datetimeUtil.formatDate(this.stationDepartmentBed.assessDate, "yyyy-MM-dd") +
        " " +
        this.stationDepartmentBed.assessTime;
      let patientAdmissionDate = this._datetimeUtil.formatDate(
        this.dischargeAssessData.admissionDateTimeView,
        "yyyy-MM-dd hh:mm"
      );
      let patientDischargeDate = this._datetimeUtil.formatDate(
        this.dischargeAssessData.dischargeDateTimeView,
        "yyyy-MM-dd hh:mm"
      );
      if (this._datetimeUtil.getTimeDifference(patientAdmissionDate, assessDate, undefined, "M") < 0) {
        this._showTip("warning", "护理评估时间不可以早于入院时间");
        return false;
      }
      if (this._datetimeUtil.getTimeDifference(patientDischargeDate, assessDate, undefined, "M") > 0) {
        this._showTip("warning", "护理评估时间不可以晚于出院院时间");
        return false;
      }
      return true;
    },
    /**
     * description: 关闭当前组件
     * return {*}
     */
    cancel() {
      this.$emit("saveEvent", true);
    },
  },
};
</script>

<style  lang='scss' >
.assess-detail {
  height: 100%;
  width: 100%;
  .detail-header {
    text-align: right;
    margin: 0px 0px 10px 0px;
    .header-float {
      float: left;
    }
    .header-station-label {
      padding: 5px;
    }
    .header-station-level {
      width: 110px;
    }
    .header-station-sort {
      width: 80px;
    }
  }
  .detail-main {
    height: calc(100% - 30px);
  }
  .detail-footer {
    margin: 5px;
    float: right;
  }
}
</style>