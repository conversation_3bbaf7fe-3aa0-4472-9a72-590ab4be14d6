<!--
 * FilePath     : \src\pages\transferPages\transferNursingManagement.vue
 * Author       : 张现忠
 * Date         : 2024-06-22 16:44
 * LastEditors  : 张现忠
 * LastEditTime : 2024-07-02 16:04
 * Description  : CCC跳转护理管理系统页面
 * CodeIterationRecord: 
 -->

<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getNursingManagementUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
import { encryption } from "@/utils/encryptionUtils";
export default {
  data() {
    return {
      url: "",
      transferPage: "externalTransfer"
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
      loginParams: "getLoginParams",
      user: "getUser"
    })
  },
  watch: {
    // 路由不同重新刷新页面
    $route: {
      handler(val, oldval) {
        if (val.fullPath != oldval.fullPath) {
          this.$router.go(0);
        }
      },
      // 深度观察监听
      deep: true
    }
  },
  async created() {
    let queryParams = this.getUrlParams();
    // 拼接url参数
    let queryString = Object.entries(queryParams)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join("&");
    // 拼接url
    this.url = getNursingManagementUrl() + `/${this.transferPage}?${queryString}`;
  },
  methods: {
    /**
     * @description: 获取url参数
     * @returns {}
     */
    getUrlParams() {
      return {
        menuID:this.$route.query.menuID,
        userId: this.loginParams.userId,
        password: this.loginParams.password.trim(),
        hospitalID: this.hospitalInfo.hospitalID,
        departmentID: this.user.stationID,
      };
    }
  }
};
</script>
