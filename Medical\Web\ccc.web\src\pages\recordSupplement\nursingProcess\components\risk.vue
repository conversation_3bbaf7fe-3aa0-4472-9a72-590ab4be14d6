<!--
 * FilePath     : \src\pages\recordSupplement\nursingProcess\components\risk.vue
 * Author       : 郭鹏超
 * Date         : 2021-08-10 09:58
 * LastEditors  : 马超
 * LastEditTime : 2025-06-15 15:56
 * Description  : 风险补录
-->
<template>
  <base-layout class="risk-supplement">
    <div slot="header">
      <span>日期:</span>
      <el-date-picker
        v-model="queryCriteria.startTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="startPickerOption"
        @change="GetScoreMainList()"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        v-model="queryCriteria.endTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="endPickerOption"
        @change="GetScoreMainList()"
      ></el-date-picker>
      <el-radio-group class="record-type" v-model="recordType" @change="changeRecordType">
        <el-radio-button :label="0">全部</el-radio-button>
        <el-radio-button :label="1">风险</el-radio-button>
        <el-radio-button :label="2">非风险</el-radio-button>
      </el-radio-group>
      <span>表单:</span>
      <el-select v-model="nowRecordListID" clearable placeholder="请选择" style="width: 180px">
        <el-option
          v-for="item in recordList"
          :key="item.recordListID"
          :label="item.recordName"
          :value="item.recordListID"
        ></el-option>
      </el-select>
      <div class="btn">
        <el-button class="add-button" icon="iconfont icon-add" @click="addOrModifyRiskRecord()">新增</el-button>
      </div>
    </div>
    <el-table height="100%" border :data="patientScoreMain" class="table-class" stripe>
      <el-table-column prop="assessDate" label="评估日期" align="center" sortable width="110"></el-table-column>
      <el-table-column prop="assessTime" label="评估时间" align="center" width="90"></el-table-column>
      <el-table-column
        prop="recordListName"
        label="名称"
        width="300"
        align="left"
        header-align="center"
        :filters="filters"
        :filter-method="filterTag"
        filter-placement="bottom-end"
      ></el-table-column>
      <el-table-column label="分数" align="center" width="60">
        <template slot-scope="scope">
          <span v-if="!scope.row.scoreContent">{{ scope.row.scorePoint }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="rangeName" label="级距" align="left"></el-table-column>
      <el-table-column label="操作" header-align="center" width="90">
        <template slot-scope="scope">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="addOrModifyRiskRecord(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="修改预防措施" v-if="scope.row.nursingClusterFlag">
            <i class="iconfont icon-preventiveMeasure" @click="showRiskInterventions(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteRiskAssessment(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      fullscreen
      :title="dialogTitle"
      :visible.sync="updataVisible"
      custom-class="no-footer"
      v-if="updataVisible"
    >
      <risk-component :params="componentParams" @result="result"></risk-component>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="true"
      title="风险预防措施"
      :visible.sync="showInterventions"
      v-if="showInterventions"
      custom-class="no-footer"
    >
      <el-table
        class="nursing-intervention-table"
        ref="intervention"
        :data="interventionList"
        height="calc(100% - 40px);"
        highlight-current-row
        border
        stripe
      >
        <el-table-column type="selection" :width="convertPX(40)" align="center" class-name="select"></el-table-column>
        <el-table-column
          prop="interventionName"
          min-width="170"
          label="护理措施"
          header-align="center"
        ></el-table-column>
      </el-table>
      <div class="footer">
        <el-button @click="close()">取消</el-button>
        <el-button type="primary" @click="onSave">确 定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import { GetRecords } from "@/api/RecordsList";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import baseLayout from "@/components/BaseLayout";
import {
  GetPatientScoreMainListByInpatient,
  GetInterventionsByScoreMainID,
  SaveScoreInterventions,
} from "@/api/PatientScore";
import { ScoreSupplementDelete } from "@/api/RiskSupplement";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    riskComponent,
  },
  props: {
    patient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    let that = this;
    return {
      //日期控制对象
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > this._datetimeUtil.getNow();
        },
      },
      //初始化为当前日期
      queryCriteria: {
        startTime: this._datetimeUtil.getNowDate(),
        endTime: this._datetimeUtil.getNowDate(),
      },
      //时间的禁用
      startPickerOption: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.queryCriteria.endTime).getTime();
        },
      },
      endPickerOption: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.queryCriteria.startTime).getTime() - 8.64e7;
        },
      },
      //当前表格名
      nowRecordListID: "",
      //修改/新增窗口控制
      updataVisible: false,
      //病人所有表集合
      patientScoreMain: [],
      // 所有的表单
      allRecordList: [],
      //评估表列表
      recordList: [],
      //弹出框表头
      dialogTitle: "",
      //过滤集合
      filters: [],
      // 评量表类别：0全部、1风险、2非风险
      recordType: 1,
      riskAssessDateTime: undefined,
      admissionDateTime: undefined,
      dischargeDateTime: undefined,
      checkData: undefined,
      clickRow: {},
      record: undefined,
      componentParams: undefined,
      interventionList: [],
      showInterventions: false,
      selectedInterventions: [],
    };
  },
  created() {
    this.getRecordsList();
  },
  watch: {
    "patient.inpatientID": {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        this.nowRecordListID = "";
        this.queryCriteria.startTime = this.patient.admissionDate
          ? this.patient.admissionDate
          : this._datetimeUtil.getNowDate();
        this.queryCriteria.endTime = this.patient.dischargeDate ? this.patient.dischargeDate : undefined;
        this.GetScoreMainList();
        this.admissionDateTime = this._datetimeUtil.formatDate(this.patient.admissionDateTimeView, "yyyy-MM-dd hh:mm");
        this.dischargeDateTime = this.patient.dischargeDateTimeView
          ? this._datetimeUtil.formatDate(this.patient.dischargeDateTimeView, "yyyy-MM-dd hh:mm")
          : undefined;
      },
      immediate: true,
    },
    nowRecordListID(newVal) {
      this.record = this.recordList.find((record) => {
        return record.recordListID == newVal;
      });
      this.GetScoreMainList();
    },
  },
  methods: {
    //获得下拉选框集合
    getRecordsList() {
      GetRecords().then((result) => {
        if (this._common.isSuccess(result)) {
          this.allRecordList = result.data;
          this.fillterRecordList();
        }
      });
    },
    changeRecordType() {
      this.nowRecordListID = "";
      this.fillterRecordList();
      this.GetScoreMainList();
    },
    fillterRecordList() {
      // 全部
      if (this.recordType == 0) {
        this.recordList = this.allRecordList;
      } else {
        // 风险
        if (this.recordType == 1) {
          this.recordList = this.allRecordList.filter((record) => {
            return record.recordType == "Risk";
          });
        } else {
          // 非风险
          this.recordList = this.allRecordList.filter((record) => {
            return record.recordType != "Risk";
          });
        }
      }
    },
    GetScoreMainList(flag) {
      let params = {
        InpatientID: this.patient.inpatientID,
        stationID: this.patient.stationID,
      };
      if (this.recordType == 1) {
        // 风险类
        params.onlyRisk = true;
      } else if (this.recordType == 2) {
        // 非风险类
        params.onlyRisk = false;
      }
      if (!flag) {
        params.RecordListID = this.nowRecordListID;
        params.startDate = this.queryCriteria.startTime;
        params.endDate = this.queryCriteria.endTime;
      }
      GetPatientScoreMainListByInpatient(params).then((response) => {
        let recordListIDs = [];
        this.filters = [];
        if (response == null) {
          return;
        }
        for (var i = 0; i < response.data.length; i++) {
          response.data[i].assessDate = this._datetimeUtil.formatDate(response.data[i].assessDate, "yyyy-MM-dd");
          response.data[i].assessTime = this._datetimeUtil.formatDate(response.data[i].assessTime, "hh:mm");
          if (recordListIDs.indexOf(response.data[i].recordListID) < 0) {
            recordListIDs.push(response.data[i].recordListID);
            this.filters.push({
              text: response.data[i].recordListName,
              value: response.data[i].recordListID,
            });
          }
        }
        this.patientScoreMain = response.data;
      });
    },
    //表格筛选方法
    filterTag(value, row) {
      return row.recordListID == value;
    },

    //删除评量表方法
    async deleteRiskAssessment(row) {
      let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
        this.user.userID,
        false,
        true,
        row.addEmployeeID
      );
      if (!saveButtonFlag) {
        this._showTip("warning", "非本人不可删除");
        return;
      }
      this._deleteConfirm("确定删除此评量表吗?", (flag) => {
        if (flag) {
          let params = {
            scoreMainID: row.patientScoreMainID,
          };
          ScoreSupplementDelete(params).then((response) => {
            if (this._common.isSuccess(response)) {
              this.GetScoreMainList();
              this._showTip("success", "删除成功！");
            }
          });
        }
      });
    },
    /**
     * description: 组件关闭回调函数
     * param {*} resultFlag 返回状态，true保存成功；false保存失败
     * param {*} resultData 保存成功后的返回数据
     * return {*}
     */
    result(resultFlag, resultData) {
      this.updataVisible = false;
      if (resultFlag) {
        //同组风险保存提示
        if (resultData && resultData.recordListID && resultData.recordName) {
          this.deelWithHaveSameTypeRisk(resultData);
        } else {
          this._showTip("success", "保存成功！");
          this.GetScoreMainList();
        }
      }
    },
    //新增
    async addOrModifyRiskRecord(row) {
      this.clickRow = undefined;
      if (row) {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.addEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可修改");
          return;
        }
        this.clickRow = row;
        this.record = this.recordList.find((record) => {
          return record.recordListID == this.clickRow.recordListID;
        });
        //初始化风险时间
        this.riskAssessDateTime =
          this._datetimeUtil.formatDate(this.clickRow.assessDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.clickRow.assessTime, "hh:mm");
      } else {
        if (!this.nowRecordListID) {
          this._showTip("warning", "请选择评估表！");
          return;
        }
        //初始化评估时间
        this.riskAssessDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      }
      this.dialogTitle = this.getDialogTitle(this.patient);
      this.updataVisible = true;
      this.componentParams = {
        supplement: true,
        patientInfo: this.patient,
        showPoint: this.record.showPointFlag,
        showTime: true,
        showStyle: this.record.showStyle,
        showBar: this.record.recordType == "Risk",
        recordListID: this.record.recordListID,
        recordList: this.recordList,
        recordsCode: this.record.recordsCode,
        patientScoreMainID: this.clickRow?.patientScoreMainID,
        assessTime: this.riskAssessDateTime,
        assessStationID: row?.stationID ?? undefined,
        assessDepartmentListID: row?.departmentListID ?? undefined,
        assessBedNumber: row?.bedNumber ?? undefined,
        assessBedID: row?.bedID ?? undefined,
        sourceType: "RiskSupplement",
        sourceID: "",
      };
    },

    getDialogTitle(patientInfo, detail) {
      let str = `${patientInfo.bedNumber}-${patientInfo.patientName}【${patientInfo.gender}-${patientInfo.age}】`;
      str += detail ? `-- ${detail}` : "";
      return str;
    },
    showRiskInterventions(row) {
      this.selectedScoreMain = row;
      this.showInterventions = true;
      let params = {
        scoreMainID: row.patientScoreMainID,
        recordListID: row.recordListID,
      };
      GetInterventionsByScoreMainID(params).then((response) => {
        if (this._common.isSuccess(response) && response.data) {
          this.interventionList = response.data;
          this.interventionList.forEach((element) => {
            if (element.selected) {
              this.$nextTick(() => {
                this.$refs.intervention.toggleRowSelection(element);
              });
            }
          });
        } else {
          this._showTip("warning", "获取风险预防措施失败");
        }
      });
    },
    close() {
      this.showInterventions = false;
    },
    onSave() {
      let param = {
        inpatientID: this.patient.inpatientID,
        patientScoreMainID: this.selectedScoreMain.patientScoreMainID,
        recordListID: this.selectedScoreMain.recordListID,
        scoreInterventions: this.$refs.intervention.selection,
      };
      SaveScoreInterventions(param).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result) && result.data) {
          this._showTip("success", "保存成功！");
        } else {
          this._showTip("warning", "保存失败");
        }
        this.close();
      });
    },
  },
};
</script>

<style lang="scss">
.risk-supplement {
  height: 100%;
  .el-radio-group.record-type {
    margin: 0 5px 0 5px;
    .el-radio-button__inner {
      padding: 10px 8px;
    }
  }
  .btn {
    float: right;
  }
  .nursing-intervention-table {
    flex: auto;
    height: calc(100% - 40px);
    .select {
      padding: 3px;
    }
  }
  .footer {
    text-align: right;
    width: 100%;
    padding-right: 10px;
    padding-top: 10px;
    box-sizing: border-box;
  }
}
</style>
