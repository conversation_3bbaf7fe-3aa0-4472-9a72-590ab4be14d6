<!--
 * relative     : \src\components\bodyAndAssessLayout.vue
 * Author       : 郭鹏超
 * Date         : 2025-01-04 17:06
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-01-06 16:26
 * Description  : 专项人体图与评估内容布局组件
 * CodeIterationRecord: 
 -->
<template>
  <div class="body-and-assess-layout">
    <div class="layout-body-warp" v-if="bodyShowFlag">
      <iframe :src="link" frameborder="0" class="body-html"></iframe>
    </div>
    <div class="layout-assess-warp">
      <slot name="default"></slot>
    </div>
  </div>
</template>

<script >
export default {
  props: {
    link: {
      type: String,
      default: "",
    },
    bodyShowFlag: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="scss">
.body-and-assess-layout {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: #fff;
  .layout-body-warp {
    width: 500px;
    height: 98%;
    padding: 0 5px;
    box-sizing: border-box;
    .body-html {
      height: 97%;
      width: 100%;
      border: 0;
      box-sizing: border-box;
    }
  }
  .layout-assess-warp {
    flex: 1;
    height: 98%;
    box-sizing: border-box;
  }
}
</style>