/*
 * FilePath     : \ccc.web\src\api\Handover\HandoverCommonUse.js
 * Author       : 郭鹏超
 * Date         : 2023-02-24 16:00
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-05-05 17:01
 * Description  :交班公用API
 * CodeIterationRecord:
 */
import http from "../../utils/ajax";
import qs from "qs";
const baseUrl = "/HandoverCommonUse";
const urls = {
  GetHandOverType: baseUrl + "/GetHandOverType",
  GetHandoverPaneList: baseUrl + "/GetHandoverPaneList",
  GetHandoverRisk: baseUrl + "/GetHandoverRisk",
  DeleteHandoverRisk: baseUrl + "/DeleteHandoverRisk",
  GetAttendanceNurse: baseUrl + "/GetAttendanceNurse",
  UpdateSBARContent: baseUrl + "/UpdateSBARContent"
};
//获取交班类型
export const GetHandOverType = params => {
  return http.get(urls.GetHandOverType, params);
};
//获取交班页签
export const GetHandoverPaneList = params => {
  return http.get(urls.GetHandoverPaneList, params);
};
//获取交班待确认风险
export const GetHandoverRisk = params => {
  return http.get(urls.GetHandoverRisk, params);
};
//删除交班风险数据
export const DeleteHandoverRisk = params => {
  return http.get(urls.DeleteHandoverRisk, params);
};
//获取派班护士
export const GetAttendanceNurse = params => {
  return http.get(urls.GetAttendanceNurse, params);
};
//更新SBAR内容
export const UpdateSBARContent = params => {
  return http.post(urls.UpdateSBARContent, qs.stringify(params));
};
