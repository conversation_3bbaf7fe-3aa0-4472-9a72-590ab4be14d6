<!--
 * FilePath     : \src\autoPages\handover\multipleShiftHandover\components\inShiftHandon.vue
 * Author       : 郭鹏超
 * Date         : 2023-05-05 11:20
 * LastEditors  : 来江禹
 * LastEditTime : 2024-06-05 10:44
 * Description  : 批量班内接班
 * CodeIterationRecord:
-->
<template>
  <base-layout v-loading="loading" :element-loading-text="loadingText" class="in-shift-handon">
    <div slot="header" class="multiple-in-shift-handon-header">
      <span>班别日期：</span>
      <el-date-picker
        @change="handoverDateChange"
        v-model="handoverDate"
        :clearable="false"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        class="handon-date"
      ></el-date-picker>
      <shift-selector
        :width="convertPX(163) + ''"
        :stationID="user.stationID"
        v-model="handoverShiftID"
        @select-item="shiftChange"
      ></shift-selector>
      <span>责任护士：</span>
      <el-select class="handon-nurse" @change="nurseChange" v-model="nurse">
        <el-option v-for="(item, index) in nurseList" :key="index" :value="item.userID" :label="item.name"></el-option>
      </el-select>
      <span>时间：</span>
      <el-date-picker
        class="handon-date-time"
        v-model="startDateTime"
        type="datetime"
        :clearable="false"
        format="yyyy-MM-dd HH:mm"
        value-format="yyyy-MM-dd HH:mm"
      ></el-date-picker>
      <span>--</span>
      <el-date-picker
        class="handon-date-time"
        v-model="endDateTime"
        type="datetime"
        :clearable="false"
        format="yyyy-MM-dd HH:mm"
        value-format="yyyy-MM-dd HH:mm"
      />
      <el-button class="handon-button" type="primary" icon="iconfont icon-handon" @click="handoverAllSave">
        接班
      </el-button>
    </div>
    <div class="multiple-in-shift-handon-content">
      <el-table :data="handoverList" height="100%" @selection-change="selectionHandover" border stripe>
        <el-table-column type="selection" :width="convertPX(40)" align="center" class-name="select"></el-table-column>
        <el-table-column :width="convertPX(110)" label="病人" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.bedNumber + "床" }}</div>
            <div>{{ scope.row.patientName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="S-现状">
          <template slot-scope="scope" v-if="scope.row.situation">
            <div v-html="scope.row.situation"></div>
          </template>
        </el-table-column>
        <el-table-column label="B-背景">
          <template slot-scope="scope">
            <div v-html="scope.row.background"></div>
          </template>
        </el-table-column>
        <el-table-column label="A-评估">
          <template slot-scope="scope">
            <div v-html="scope.row.assement"></div>
          </template>
        </el-table-column>
        <el-table-column label="R-建议">
          <template slot-scope="scope">
            <div v-html="scope.row.recommendation"></div>
          </template>
        </el-table-column>
        <el-table-column label="接班状态" :width="convertPX(120)" align="center">
          <template slot-scope="scope">
            <div v-if="!scope.row.handoverID">未交班</div>
            <div v-else>
              {{ scope.row.handonNurse ? "已接班" : "未接班" }}
            </div>
            <div>{{ scope.row.planScheduleFlag ? "已排程" : "未排程" }}</div>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(120)" align="center" label="详情">
          <template slot-scope="scope">
            <body-image
              v-if="scope.row.handoverID"
              :type="'button'"
              :handoverID="scope.row.handoverID"
              :patientName="scope.row.patientName"
            ></body-image>
            <el-button @click="getShiftPlan(scope.row.inpatientID)" v-if="scope.row.handoverID" type="text">
              计划总览
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog v-dialogDrag :close-on-click-modal="false" title="人体图" width="37%" :visible.sync="showBodyImageFlag">
      <img :src="bodyImageStr" class="body-pic" />
      <div slot="footer">
        <el-button type="primary" @click="showBodyImageFlag = false">确 定</el-button>
      </div>
    </el-dialog>
    <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="计划总览"
      :visible.sync="planShowFlag"
      custom-class="no-footer"
    >
      <el-table class="dialogPlanTable" :span-method="cellMerge" border :data="planList" height="100%">
        <el-table-column prop="problem" label="护理问题/集束护理" :min-width="convertPX(160)"></el-table-column>
        <el-table-column prop="nursingGoal" :width="convertPX(120)" label="护理目标"></el-table-column>
        <el-table-column prop="intervention" label="措施"></el-table-column>
        <el-table-column prop="frequency" label="频次" :width="convertPX(120)"></el-table-column>
        <el-table-column prop="frequenyDescription" label="频次说明"></el-table-column>
        <el-table-column label="开始日期" align="center" :width="convertPX(180)">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="结束日期" align="center" :width="convertPX(180)">
          <template>
            <template slot-scope="scope">
              <span v-formatTime="{ value: scope.row.endDate, type: 'date' }"></span>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </base-layout>
</template>

<script>
import shiftSelector from "@/components/selector/shiftSelector";
import baseLayout from "@/components/BaseLayout";
import progressView from "@/components/progressView";
import { mapGetters } from "vuex";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetAttendanceNurse } from "@/api/Handover/HandoverCommonUse";
import { GetPatientInterventionList } from "@/api/Intervention";
import { GetShiftHandonSBARTableList, InShiftHandon } from "@/api/Handover/MultipleShifHandover";
import bodyImage from "@/components/bodyImage";
export default {
  components: {
    baseLayout,
    shiftSelector,
    progressView,
    bodyImage,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      loading: false,
      loadingText: "",
      handoverDate: undefined,
      handoverShiftID: undefined,
      stationShifts: [],
      nurseList: [],
      nurse: undefined,
      startDateTime: undefined,
      endDateTime: undefined,
      handoverList: [],
      saveHandoverList: [],
      //进度条开关
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      planShowFlag: false,
      planList: [],
      spanArr: [],
    };
  },
  async beforeMount() {
    this.startDateTime = this.endDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
    await this.getPageSetting();
  },
  methods: {
    /**
     * description: 获取接班数据
     * return {*}
     */
    getHandoverList() {
      if (!this.handoverDate || !this.handoverShiftID || !this.nurse) {
        return;
      }
      let params = {
        recordsCode: "TurnHandover",
        handoverClass: "HandOn",
        shiftDate: this.handoverDate,
        shiftID: this.handoverShiftID,
        nurserID: this.nurse,
        stationID: this.user.stationID,
      };
      this.loading = true;
      this.loadingText = "加载中……";
      GetShiftHandonSBARTableList(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverList = res.data;
        }
      });
    },
    /**
     * description: 接班信息确认
     * return {*}
     */
    handoverAllSave() {
      if (!this.saveCheck()) {
        return;
      }
      //交班信息确认
      let string = this.getHandoverInfo();
      this.$confirm(string, "提示", {
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        customClass: "multi-handon-msgbox-class",
      }).then(() => {
        this.multipleSaveHandover();
      });
    },
    /**
     * description: 批量班内接班
     * return {*}
     */
    async multipleSaveHandover() {
      this.progressFlag = true;
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      for (let i = 0; i < this.saveHandoverList.length; i++) {
        const handover = this.saveHandoverList[i];
        let saveFlag = await this.inShiftHandon(handover);
        let progress = (((i + 1) / this.saveHandoverList.length) * 100).toFixed(0);
        this.messageData[0].value = Number(progress);
        this.messageData[saveFlag ? 1 : 2].value += " " + handover.bedNumber + "床-" + handover.patientName;
      }
    },
    /**
     * description: 班内接班
     * param {*} handover
     * return {*}
     */
    async inShiftHandon(handover) {
      let params = {
        shiftDate: this.handoverDate,
        shiftID: this.handoverShiftID,
        inShiftStartDate: this.startDateTime,
        inShiftEndDate: this.endDateTime,
        handoverCommonSaveView: {
          handoverID: handover.handoverID,
          inpatientID: handover.inpatientID,
          handoverNurse: this.nurse,
          recordsCode: "TurnHandover",
          handoverClass: "HandOn",
        },
      };
      let saveFlag = false;
      await InShiftHandon(params).then((res) => {
        if (this._common.isSuccess(res)) {
          saveFlag = true;
        }
      });
      return saveFlag;
    },
    /**
     * description: 进度条关闭函数
     * return {*}
     */
    progressClose() {
      this.progressFlag = false;
      this.getHandoverList();
    },
    /**
     * description: 交班检核
     * return {*}
     */
    saveCheck() {
      if (!this.saveHandoverList.length) {
        this._showTip("warning", "请先勾选汇总患者！");
        return false;
      }
      return true;
    },
    /**
     * description: 接班病人确认
     * return {*}
     */
    getHandoverInfo() {
      let repeatCommit = [];
      let commitArray = [];
      for (let i = 0; i < this.saveHandoverList.length; i++) {
        let item = this.saveHandoverList[i];
        commitArray.push(item.bedNumber + "床");
        if (item.handonDate) {
          //已存在 是重复接班
          repeatCommit.push(item.bedNumber + "床");
        }
      }
      let message =
        "<span>您要接班的床位是:</span><strong style='color:red;'>" + commitArray.toString() + "</strong><br/>";
      if (repeatCommit.length > 0) {
        message += "<span>重复接班床位:</span><strong style='color:red;'>" + repeatCommit.toString() + "</strong><br>";
        message += "<strong style='color:red;' >重复接班会覆盖上一次接班记录</strong>";
      }
      return message;
    },
    /**
     * description: 获取勾选数据
     * param {*} selection
     * return {*}
     */
    selectionHandover(selection) {
      this.saveHandoverList = selection;
    },
    /**
     * description: 交班时间变化
     * return {*}
     */
    async handoverDateChange() {
      await this.getAttendanceNurse();
      await this.getHandoverList();
    },
    /**
     * description: 班别变化
     * return {*}
     */
    async shiftChange() {
      await this.getAttendanceNurse();
      await this.getHandoverList();
    },
    /**
     * description: 护士变化
     * return {*}
     */
    nurseChange() {
      this.getHandoverList();
    },
    /**
     * description: 获取页面配置
     * return {*}
     */
    async getPageSetting() {
      const methodArr = [this.getHandonShift, this.getAttendanceNurse, this.getHandoverList];
      for (const method of methodArr) {
        await method();
      }
    },
    /**
     * description: 获取当前班别
     * return {*}
     */
    async getHandonShift() {
      await GetNowStationShiftData().then((res) => {
        if (this._common.isSuccess(res)) {
          //班别日期和班别初始化
          this.handoverDate = res.data?.shiftDate ?? undefined;
          this.stationShifts = res.data?.stationShifts ?? [];
          this.getInShiftHandoverStartAndEndDateTime();
        }
      });
    },
    /**
     * description: 获取派班护士
     * return {*}
     */
    async getAttendanceNurse() {
      let params = {
        shiftDate: this.handoverDate,
        stationID: this.user.stationID,
        shiftID: this.handoverShiftID,
      };
      await GetAttendanceNurse(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.nurseList = res.data;
          this.nurse = undefined;
          if (!res.data?.length || !this.user) {
            return;
          }
          if (this.nurseList.find((nurse) => nurse.userID == this.user.userID)) {
            this.nurse = this.user.userID;
          }
        }
      });
    },
    /**
     * description: 组装开始和结束时间
     * return {*}
     */
    getInShiftHandoverStartAndEndDateTime() {
      let sucShift = this.stationShifts.find((shift) => shift.id == this.handoverShiftID);
      this.endDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      if (!sucShift) {
        this.startDateTime = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      } else {
        this.startDateTime =
          this._datetimeUtil.formatDate(this.handoverDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(sucShift.shiftStartTime, "hh:mm");
      }
    },
    /**
     * description: 显示护理计划
     * param {*} inpatientID
     * return {*}
     */
    getShiftPlan(inpatientID) {
      let params = {
        inpatientID: inpatientID,
      };
      GetPatientInterventionList(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.length == 0) {
            this._showTip("warning", "未查到病人计划！");
          } else {
            this.planList = response.data;
            this.getSpanArr(this.planList);
            this.planShowFlag = true;
          }
        }
      });
    },
    /**
     * description: 获取合并数组
     * param {*} data
     * return {*}
     */
    getSpanArr(data) {
      this.spanArr = [];
      let pos = 0;
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].problem == data[i - 1].problem) {
            this.spanArr[pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            pos = i;
          }
        }
      }
    },
    /**
     * description: 相同项合并
     * param {*} rowIndex
     * param {*} columnIndex
     * return {*}
     */
    cellMerge({ rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        //合并第一列和第二列
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
  },
};
</script>

<style lang="scss" >
.in-shift-handon {
  height: 100%;
  .multiple-in-shift-handon-header {
    .handon-date {
      width: 163px;
    }
    .handon-nurse {
      width: 163px;
    }
    .handon-date-time {
      width: 223px;
    }
    .handon-button {
      float: right;
      margin-top: 10px;
    }
  }
  .multiple-in-shift-handon-content {
    height: 100%;
    .el-button--small {
      padding: 0;
      margin: 0;
    }
  }
}
</style>