/*
 * FilePath     : \src\utils\toast.js
 * Author       : 陈超然
 * Date         : 2020-05-19 17:17
 * LastEditors  : 苏军志
 * LastEditTime : 2020-06-03 20:34
 * Description  : 消息提示框增加关闭按钮
 */

import Vue from "vue";
import { Message } from "element-ui";
//  重写element ui的message组件，防止同时出现多个框，影响页面效果
let messageInstance = undefined;
const resetMessage = options => {
  if (messageInstance) {
    // 如果已存在，先关闭
    messageInstance.close();
  }
  messageInstance = Message(options);
};
["error", "success", "info", "warning"].forEach(type => {
  resetMessage[type] = options => {
    if (typeof options === "string") {
      options = {
        message: options
      };
    }
    options.type = type;
    return resetMessage(options);
  };
});
// 显示不同类型的操作提示框
//type：info,success,warning,error
const showTip = (type, msg) => {
  resetMessage({
    dangerouslyUseHTMLString: true, // 支持html标签
    message: msg,
    showClose: true, //支持提示框可以关闭
    type: type,
    offset: 200,
    duration: 2000
  });
};
Vue.prototype._showTip = showTip;
Vue.prototype._showMessage = resetMessage;
export default {
  showTip
};
