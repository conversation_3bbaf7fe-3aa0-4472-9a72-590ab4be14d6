/*
 * FilePath     : \src\api\Static.js
 * Author       : 周民安
 * Date         : 2021-07-19 16:45
 * LastEditors  : 来江禹
 * LastEditTime : 2023-07-05 14:40
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/Static";

export const urls = {
  GetAssessRange: baseUrl + "/GetAssessRange",
  GetRiskScreenRecordList: baseUrl + "/GetRiskScreenRecordList",
  GetMedicineScheduleStatistics: baseUrl + "/GetMedicineScheduleStatistics",
  GetByConditionConsultStatistics: baseUrl + "/GetByConditionConsultStatistics"
};
//获取风险等级
export const GetAssessRange = params => {
  return http.get(urls.GetAssessRange, params);
};
//获取患者信息
export const GetRiskScreenRecordList = params => {
  return http.get(urls.GetRiskScreenRecordList, params);
};
//获取统计给药医嘱执行率
export const GetMedicineScheduleStatistics = params => {
  return http.post(urls.GetMedicineScheduleStatistics, params);
};
// 根据条件查询会诊信息(统计用)
export const GetByConditionConsultStatistics = params => {
  return http.get(urls.GetByConditionConsultStatistics, params);
}
