/*
 * FilePath     : \src\api\InterventionFileUpload.js
 * Author       : 祝仕奇
 * Date         : 2021-10-21 14:45
 * LastEditors  : 苏军志
 * LastEditTime : 2022-09-06 18:46
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/InterventionFileUpload";

export const urls = {
  GetAllFilePath: baseUrl + "/GetAllFilePath",
  GetFileListBySystem: baseUrl + "/GetFileListBySystem",
  SaveFilePath: baseUrl + "/SaveFilePath",
  DeleteFilePath: baseUrl + "/DeleteFilePath",
  GetTeachingInterventionList: baseUrl + "/GetTeachingInterventionList"
};
// 获取全部查询数据
export const GetAllFilePath = params => {
  return http.get(urls.GetAllFilePath, params);
};
export const GetFileListBySystem = params => {
  return http.get(urls.GetFileListBySystem, params);
};
export const SaveFilePath = params => {
  return http.post(urls.SaveFilePath, params);
};
export const DeleteFilePath = params => {
  return http.post(urls.DeleteFilePath, qs.stringify(params));
};
export const GetTeachingInterventionList = params => {
  return http.get(urls.GetTeachingInterventionList, params);
};
