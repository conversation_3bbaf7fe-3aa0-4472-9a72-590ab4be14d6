/*
 * FilePath     : \ccc.web\src\utils\i18n\lang\en\pages\io\ioRecordMaintenance.js
 * Author       : 苏军志
 * Date         : 2021-11-01 11:59
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-06-01 10:49
 * Description  : 出入量维护画面ioRecordMaintenance
 */
export default {
  ioRecordMaintenance: {
    date: "Date",
    time: "Time",
    type: "Type",
    item: "Item",
    inputType: "Intake Type",
    inputContent: "Intake Content",
    numberOfTimes: "Number of times",
    amount: "Quantity",
    character: "Character",
    smell: "Smell",
    color: "Color",
    remarks: "Remarks",
    dialogTitleAdd: "Intake and output - Add",
    dialogTitleModify: "Intake and output - Modify",
    noSelectTip: "Please select the data to delete first!",
    batchSaveTip: "No change data, no need to save!",
    station: "Station",
    department: "Department",
    bed: "Bed",
    noDeptTip: "Please select Department!",
    addIoRecord: {
      input: "Intake",
      output: "Output",
      category: "Category",
      unit: "ml",
      infusionType0: "Nothing",
      infusionType10: "Venous Intake (general)",
      infusionType20: "Intravenous Pumping",
      saveTipItem: "Please select an item!",
      saveTipAttribute:
        "You must enter one of color, character, smell, quantity, times and remarks!",
      saveTipTimes1: "[times] Please enter a value",
      saveTipTimes2: "[times] cannot be negative!",
      saveTipTimes3: "[times] Please enter a positive integer!",
      saveTipAmount1: "[quantity] Please enter a value!",
      saveTipAmount2:
        "[quantity] Please enter a numerical value with up to two decimal places!",
      bringToNursingRecord: "Write nursing record",
      informPhysician: "Notify Physician"
    },
    tableWidth: {
      ioDate: 100,
      ioTime: 65,
      ioKind: 70,
      ioItem: 120,
      inPutType: 100,
      inPutContent: 80,
      intakeOutputTimes: 85,
      amount: 105,
      character: 105,
      smell: 80,
      color: 110,
      remarks: 85,
      operation: 85,
      informPhysician: 80,
      bringToNursingRecord: 80,
      station: 100,
      department: 100,
      bed: 100
    }
  }
};
