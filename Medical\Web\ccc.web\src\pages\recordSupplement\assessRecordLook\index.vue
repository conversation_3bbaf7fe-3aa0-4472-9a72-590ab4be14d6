<!--
 * FilePath     : \src\pages\recordSupplement\assessRecordLook\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-06-28 09:01
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-05-08 18:27
 * Description  : 出院病人评估记录和专项护理记录查看
-->
<template>
  <base-layout class="assess-record_look" header-height="auto">
    <search-patient-data
      slot="header"
      class="patient-info"
      :checkFlag="false"
      @selectPatientData="selectPatientData"
    ></search-patient-data>
    <div class="assess-record_look_tabs">
      <el-tabs v-if="isShow" @tab-click="handleClick" class="tabs" v-model="activeName">
        <el-tab-pane
          v-for="(component, index) in components"
          :key="index"
          :label="component.label"
          :name="component.name"
        ></el-tab-pane>
      </el-tabs>
      <el-tabs
        v-if="isShow && activeName == 'nursingRecordLook'"
        @tab-click="handleClick"
        class="tabs"
        v-model="childActiveName"
      >
        <el-tab-pane
          v-for="(component, index) in specificCareComponents"
          :key="index"
          :label="component.label"
          :name="component.name"
        ></el-tab-pane>
      </el-tabs>
      <div v-if="isShow" class="views">
        <router-view ref="childPage"></router-view>
      </div>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData.vue";
import { mapGetters } from "vuex";
import { GetSettingValuesByTypeCodeAndValue } from "@/api/Setting";
import { GetMenuByParentID } from "@/api/Menu";
export default {
  components: {
    baseLayout,
    searchPatientData,
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
      user: "getUser",
    }),
  },
  data() {
    return {
      patientInfo: undefined,
      activeName: "assessLook",
      childActiveName: "woundLook",
      isShow: false,
      components: [
        {
          label: "护理评估",
          name: "assessLook",
        },
        {
          label: "风险评估",
          name: "recordRiskRatingLook",
        },
        {
          label: "护理记录",
          name: "nursingRecordLook",
        },
        {
          label: "转运交接",
          name: "handoverLook",
        },
        {
          label: "出院小结",
          name: "handoverSBARLook",
        },
      ],
      specificCareComponents: [
        {
          label: "皮肤/伤口",
          name: "woundLook",
        },
        {
          label: "导管",
          name: "tubeLook",
        },
        {
          label: "出入量",
          name: "ioLook",
        },
        {
          label: "血糖记录单",
          name: "glucoseLook",
        },
        {
          label: "抢救",
          name: "rescueRecordLook",
        },
        {
          label: "约束",
          name: "restraintLook",
        },
        {
          label: "疼痛",
          name: "patientPainLook",
        },
        {
          label: "末梢血运",
          name: "peripheralCirculationLook",
        },
        {
          label: "泵入通路",
          name: "pumpingLook",
        },
        {
          label: "造口",
          name: "patientStomaRecordLook",
        },
        {
          label: "输血",
          name: "bloodTransfusionRecordLook",
        },
        {
          label: "溶栓用药",
          name: "patientThrombolysisLook",
        },
        {
          label: "饮食营养摄入单",
          name: "patientDietIntakeLook",
        },
        {
          label: "谵妄护理",
          name: "patientDeliriumLook",
        },
        {
          label: "产时记录",
          name: "deliveryRecordLook",
        },
        {
          label: "婴儿哺喂",
          name: "babyFeedingRecordLook",
        },
        {
          label: "CRRT记录单",
          name: "cRRTRecordLook",
        },
        {
          label: "镇静评估",
          name: "patientSedationLook",
        },
        {
          label: "无呕专项",
          name: "patientCINVLook",
        },
        {
          label: "生产流程记录",
          name: "patientDeliveryRecordLook",
        },
      ],
      //当前功能可使用权限ID配置
      userRoleID: undefined,
    };
  },
  async mounted() {
    if (this.inpatient) {
      this.patientInfo = this.inpatient;
    }
    await this.getSpecificCareMenuList();
  },
  methods: {
    /**
     * description: 系统顶部刷新按钮触发
     * return {*}
     */
    refreshData() {
      this.$nextTick(() => {
        if (this.$refs["childPage"] && this.$refs["childPage"].refreshData) {
          this.$refs["childPage"].refreshData();
        }
      });
    },
    /**
     * description: 选中病人
     * param {*} patient
     * return {*}
     */
    async selectPatientData(patient) {
      await this.getUserRoleIDSetting();
      var roles = this.user.roles.filter((role) => role >= this.userRoleID);
      if (!roles.length) {
        this._showTip("warning", "该用户无权限!");
        this.isShow = false;
        return;
      }
      this.isShow = true;
      this.patientInfo = patient;
      this.$store.commit("session/setPatientInfo", patient);
      this.activeName = "assessLook";
      let tab = {
        name: "assessLook",
      };
      this.handleClick(tab);
    },
    /**
     * description: 点击页签
     * param {*} tab
     * param {*} event
     * return {*}
     */
    handleClick(tab, event) {
      if (!this.patientInfo) {
        this._showTip("warning", "请先选择病人");
        return;
      }
      let url = {
        name: tab.name,
      };
      //护理评估特殊处理(查看明细只读)
      if (tab.name == "assessLook") {
        url.query = {
          onlyRead: true,
        };
      }
      // 护理记录特殊处理
      if (tab.name == "nursingRecordLook") {
        url.name = this.childActiveName;
        url.query = {
          model: "component",
          patientinfo: this.patientInfo,
        };
      }
      // 转运交接特殊处理
      if (tab.name == "handoverLook") {
        url.query = {
          model: "component",
          activeName: "handonSBAR",
        };
      }
      // 出院小结特殊处理
      if (tab.name == "handoverSBARLook") {
        url.query = {
          model: "component",
          handoverType: "DischargeAssess",
        };
      }
      this.$router.replace(url);
    },
    /**
     * description: 页面销毁
     * return {*}
     */
    destroyed() {
      this.$store.commit("session/setPatientInfo", undefined);
    },
    /**
     * description: 查找用户权限ID配置
     * return {*}
     */
    async getUserRoleIDSetting() {
      let params = {
        settingTypeCode: "DischargeRecordSearch",
        typeValue: "AuthorityRoleListID",
      };
      await GetSettingValuesByTypeCodeAndValue(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.userRoleID = Number(res.data[0]);
        }
      });
    },
    async getSpecificCareMenuList() {
      let params = {
        menuListID: 18,
      };
      await GetMenuByParentID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          let sucComponents = [];
          this.specificCareComponents.forEach((item) => {
            if (res.data.find((m) => `${m.router.substring(1)}Look` == item.name)) {
              sucComponents.push(item);
            }
          });
          this.specificCareComponents = sucComponents;
        } else {
          this.specificCareComponents = [];
        }
      });
    },
  },
};
</script>

<style lang="scss" >
.assess-record_look {
  .assess-record_look_tabs {
    height: 100%;
    .tabs {
      height: 40px;
    }
    .views {
      height: calc(100% - 80px);
    }
  }
}
</style>
