<!--
/*
 * FilePath     : \src\pages\patientObserve\ObserveTemplates.vue
 * Author       : 李艳奇
 * Date         : 2021-06-23 20:27
 * LastEditors  : 来江禹
 * LastEditTime : 2024-09-30 16:19
 * Description  : 观察措施维护模版
                  2022-01-10 配合后端调整前端参数
 */
 -->
<template>
  <base-layout show-header class="observe-template">
    <div slot="header">
      <span>病区：</span>
      <el-select
        v-model="stationID"
        class="observe-template-header-station"
        @change="changeStation"
        placeholder="请选择"
      >
        <el-option
          v-for="item in stationList"
          :key="item.stationID"
          :label="item.stationName"
          :value="item.stationID"
        ></el-option>
      </el-select>
      <div class="top-btn">
        <el-button type="primary" icon="iconfont icon-sort" @click="saveSort">模板顺序保存</el-button>
        <el-button class="add-button" icon="iconfont icon-add" @click="openObserveTemplateDialog()">新增</el-button>
      </div>
    </div>
    <el-table
      ref="templateTable"
      row-key="observeTemplateID"
      height="100%"
      :data="observeTemplateData"
      v-loading="loading"
      :element-loading-text="loadingText"
      border
      stripe
    >
      <el-table-column width="80" align="center">
        <template slot-scope="template" slot="header">
          <span>序号</span>
          <i class="iconfont icon-info" @click="showMessage(template)"></i>
        </template>
        <template slot-scope="template">
          <div>
            {{ template.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="stationID != 999"
        label="病区"
        prop="stationName"
        min-width="20"
        header-align="center"
        align="left"
      ></el-table-column>
      <el-table-column label="科室" prop="deptName" min-width="20" header-align="center" align="left"></el-table-column>
      <el-table-column
        label="模板类别"
        prop="templateCategoryName"
        min-width="20"
        header-align="center"
        align="left"
      ></el-table-column>
      <el-table-column
        label="模板名称"
        prop="templateName"
        min-width="20"
        header-align="center"
        align="left"
      ></el-table-column>
      <el-table-column label="模板内容" prop="templateContent" min-width="100" align="left"></el-table-column>
      <el-table-column label="维护时间" width="110" align="center">
        <template slot-scope="template">
          <span v-formatTime="{ value: template.row.modifyDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column
        label="维护人"
        prop="modifyEmployeeName"
        width="70"
        header-align="center"
        align="left"
      ></el-table-column>
      <el-table-column label="操作" width="65" align="center">
        <template
          v-if="user.roles.find((role) => role > 30) || user.userID == consultTemplate.row.modifyPersonID"
          slot-scope="consultTemplate"
        >
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="openObserveTemplateDialog(consultTemplate.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteObserveTemplate(consultTemplate.row.observeTemplateID)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 新增/修改弹框 -->
    <el-dialog
      title="观察措施模板维护"
      :visible.sync="showAddTemplate"
      custom-class="add-template"
      v-dialogDrag
      :close-on-click-modal="false"
      v-loading="dialogLoading"
      element-loading-text="保存中……"
    >
      <el-row>
        <el-col :span="14">
          <span class="label">病区：</span>
          <el-select v-model="templateStationID" clearable class="add-template-station">
            <el-option
              v-for="item in stationList"
              :key="item.stationID"
              :label="item.stationName"
              :value="item.stationID"
            ></el-option>
          </el-select>
        </el-col>
        <el-col :span="10">
          <span class="label">科室：</span>
          <dept-selector
            :clearable="true"
            label=""
            v-model="templateDept"
            :stationID="templateStationID"
            :defaultDepartmentList="defaultDepartmentList"
            class="add-template-department"
            width="180px"
          ></dept-selector>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="14">
          <span class="label">模板类型：</span>
          <el-select
            class="add-template-type"
            :clearable="true"
            v-model="templateCategory"
            placeholder="请输入模板类型"
          >
            <el-option
              v-for="(item, index) in templateTypeList"
              :key="index"
              :label="item.value"
              :value="item.label"
            ></el-option>
          </el-select>
        </el-col>
        <el-col :span="10">
          <span class="label">模板名称：</span>
          <el-input v-model="templateName" class="add-template-name" placeholder="请输入模板名字"></el-input>
        </el-col>
      </el-row>
      <div class="temp-wrap">
        <span class="label">模板内容：</span>
        <el-input
          type="textarea"
          :autosize="{ minRows: 10, maxRows: 10 }"
          placeholder="请输入模板内容"
          v-model="templateContent"
          class="add-template-content"
        ></el-input>
      </div>
      <div slot="footer">
        <el-button @click="showAddTemplate = false">取消</el-button>
        <el-button type="primary" @click="saveObserveTemplate()">确 定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetEmployeeSwitchStationList } from "@/api/User";
import { mapGetters } from "vuex";
import {
  InsertObserveTemplate,
  RemoveObserveTemplate,
  UptateObserveTemplate,
  GetObserveTemplateTypeList,
  GetStationObserveTemplatesBystationID,
  SaveObserveTemplatesSort,
} from "@/api/ObserveTemplate";
import deptSelector from "@/components/selector/deptSelector";
import { GetDepartmentDataByStationID } from "@/api/Station";
export default {
  components: {
    baseLayout,
    deptSelector,
  },
  data() {
    return {
      stationList: [],
      stationID: undefined,
      loading: false,
      loadingText: "加载中……",
      dialogLoading: false,
      //模板数据
      observeTemplateData: [],
      templateName: undefined,
      templateContent: undefined,
      templateStationID: undefined,
      templateDept: undefined,
      templateID: undefined,
      showAddTemplate: false,
      templateCategory: undefined,
      sort: undefined,
      templateTypeList: [],
      defaultDepartmentList: [],
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  async mounted() {
    this.init();
    //取得病区
    this.getStationList();
    //获取模板所加载数据
    this.getTemplateTypeList();
    //取得模板数据
    await this.getObserveTemplateData();
    //拖拽调用
    this.rowDrop();
  },
  methods: {
    /**
     * description: 页面初始化
     * param {*}
     * return {*}
     */
    init() {
      if (this.user) {
        this.stationID = this.user.stationID;
        this.stationList = [];
        //管理员登录默认显示公共模板
        if (this.user.roles && this.user.roles.length && this.user.roles.includes(99)) {
          this.stationList.push({
            stationID: 999,
            stationName: "公共",
          });
          this.stationID = 999;
          //默认科室
          this.defaultDepartmentList = [
            {
              id: 999,
              value: "公共",
            },
          ];
        } else {
          //BugFix:非管理人员科室下拉框无内容，显示科室ID
          let stationparams = {
            ID: this.stationID,
          };
          GetDepartmentDataByStationID(stationparams).then((result) => {
            if (this._common.isSuccess(result)) {
              this.defaultDepartmentList = result.data;
            }
          });
        }
      }
    },
    /**
     * description: 获取模板数据
     * param {*}
     * return {*}
     */
    async getObserveTemplateData() {
      this.observeTemplateData = [];
      let params = {
        stationID: this.stationID,
      };
      this.loading = true;
      this.loadingText = "加载中……";
      await GetStationObserveTemplatesBystationID(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.observeTemplateData = res.data;
          this.$nextTick(() => {
            this.$refs.templateTable.doLayout();
          });
        }
      });
    },
    /**
     * description: 获取病区
     * param {*}
     * return {*}
     */
    getStationList() {
      let params = {
        number: Math.random(),
      };
      GetEmployeeSwitchStationList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data && res.data.length > 0) {
            this.stationList = [...this.stationList, ...res.data];
          }
        }
      });
    },
    /**
     * description: 切换病区获取数据
     * param {*}
     * return {*}
     */
    async changeStation() {
      if (this.stationID == 999) {
        this.defaultDepartmentList = [
          {
            id: 999,
            value: "公共",
          },
        ];
      } else {
        this.defaultDepartmentList = [];
      }
      //获取模板所加载数据
      this.getTemplateTypeList();
      this.getObserveTemplateData();
    },
    /**
     * description:模板维护弹窗
     * param {*} data
     * return {*}
     */
    openObserveTemplateDialog(data) {
      this.showAddTemplate = true;
      //初始化模板框
      this.templateName = data ? data.templateName : "";
      this.templateContent = data ? data.templateContent : "";
      this.templateDept = data ? data.departmentListID : undefined;
      this.templateStationID = data ? data.stationListID : this.stationID;
      this.templateID = data ? data.observeTemplateID : undefined;
      this.templateCategory = data ? data.templateCategory : undefined;
      this.sort = data ? data.sort : this.observeTemplateData.length + 1;
    },
    /**
     * description: 模板保存
     * param {*}
     * return {*}
     */
    saveObserveTemplate() {
      // 只有非公共的科室才检核
      if (this.templateStationID == 999 && !this.templateDept) {
        this._showTip("warning", "请选择科室!");
        return;
      }
      if (!this.templateName) {
        this._showTip("warning", "请选择模板名称!");
        return;
      }
      if (!this.templateCategory) {
        this._showTip("warning", "请选择模板类型!");
        return;
      }
      if (!this.templateContent) {
        this._showTip("warning", "请填写模板内容!");
        return;
      }
      let params = {
        stationListID: this.templateStationID,
        departmentListID: this.templateDept,
        templateName: this.templateName,
        templateContent: this.templateContent,
        templateCategory: this.templateCategory,
        observeTemplateID: this.templateID,
        sort: this.sort,
        chinesePinyin: this._common.initial(this.templateName),
      };
      this.dialogLoading = true;
      this.templateID ? this.observeTemplateUpdate(params) : this.observeTemplateAdd(params);
    },
    /**
     * description: 新增模板保存
     * param {*} params
     * return {*}
     */
    observeTemplateAdd(params) {
      InsertObserveTemplate(params).then((res) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.showAddTemplate = false;
          this.getObserveTemplateData();
        }
      });
    },
    /**
     * description: 模板修改保存
     * param {*} params
     * return {*}
     */
    observeTemplateUpdate(params) {
      UptateObserveTemplate(params).then((res) => {
        this.dialogLoading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.showAddTemplate = false;
          this.getObserveTemplateData();
        }
      });
    },
    /**
     * description: 模板删除
     * param {*} consultGoalToEmployeeID
     * return {*}
     */
    deleteObserveTemplate(observeTemplateID) {
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          // 确认删除
          let prams = {
            observeTemplateID,
          };
          RemoveObserveTemplate(prams).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getObserveTemplateData();
            } else {
              this._showTip("warning", "删除失败！");
            }
          });
        }
      });
    },
    /**
     * description: 获取观察措施类型
     * param {*}
     * return {*}
     */
    getTemplateTypeList() {
      let params = {
        isCommon: this.stationID == 999,
      };
      GetObserveTemplateTypeList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.templateTypeList = res.data;
        }
      });
    },
    /**
     * description: 拖拽调用
     * param {*}
     * return {*}
     */
    rowDrop() {
      let bodyDom = this.$refs.templateTable.$el.querySelector(".el-table__body-wrapper tbody");
      this._tableRowDrop(bodyDom, this, "observeTemplateData");
    },
    /**
     * description: 排序保存
     * param {*}
     * return {*}
     */
    saveSort() {
      if (!this.observeTemplateData || this.observeTemplateData.length <= 0) {
        this._showTip("warning", "无病情观察模板!");
        return;
      }
      if (!this.observeTemplateData.find((item) => item.sortFlag)) {
        this._showTip("warning", "请先鼠标选中模板拖拽进行模板排序!");
        return;
      }
      let params = {
        view: this.observeTemplateData.map((template) => template.observeTemplateID),
      };
      this.loading = true;
      this.loadingText = "保存中……";
      SaveObserveTemplatesSort(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功!");
          this.getObserveTemplateData();
          return;
        }
      });
    },
    /**
     * description: 鼠标点击提示内容(拖拽排序提示)
     * param {*} messageContent
     * return {*}
     */
    showMessage(messageContent) {
      this._showMessage({
        message: "鼠标选中模板拖拽进行模板排序",
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
  },
};
</script>
<style lang="scss">
.observe-template {
  .top-btn {
    float: right;
  }
  .observe-template-header-station {
    width: 150px;
  }
  .add-template.el-dialog {
    width: 740px;
    height: 500px;
    .el-row {
      margin: 10px;
      .label {
        display: inline-block;
        width: 70px;
      }
      .add-template-station {
        width: 180px;
        .el-input.is-disabled .el-input__inner {
          background-color: #eee;
          color: #606266;
          border: 1px solid #ccc;
        }
      }
    }
    .temp-wrap {
      display: flex;
      margin: 10px;
    }
    .add-template-type,
    .add-template-name {
      width: 180px;
    }
    .add-template-content {
      width: 600px;
    }
  }
}
</style>
