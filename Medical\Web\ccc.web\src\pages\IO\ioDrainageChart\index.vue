<!--
 * FilePath     : \src\pages\IO\ioDrainageChart\index.vue
 * Author       : 来江禹
 * Date         : 2023-06-19 16:38
 * LastEditors  : 来江禹
 * LastEditTime : 2024-01-11 14:20
 * Description  : 跳转路径：http://localhost:8088/externalTransfer?noToken=true&clientType=1&hospitalID=2&language=1&isDialog=true&functionID=21&stationCode=2139&caseNumber=1553985
 * CodeIterationRecord:
-->

<template>
  <base-layout class="io-drainage-chart" headerHeight="auto">
    <div slot="header">
      <span>日期</span>
      <el-date-picker
        type="date"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        v-model="startDate"
        placeholder="请选择开始日期"
        @change="getChartData()"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        type="date"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        v-model="endDate"
        placeholder="请选择结束日期"
        @change="getChartData()"
      ></el-date-picker>
      <el-tooltip :content="showChart ? '隐藏图表' : '显示图表'">
        <el-checkbox-button v-model="showChart">
          <i class="iconfont icon-chart"></i>
        </el-checkbox-button>
      </el-tooltip>
      <el-tooltip :content="showTable ? '隐藏表格' : '显示表格'">
        <el-checkbox-button v-model="showTable">
          <i class="iconfont icon-list-icon"></i>
        </el-checkbox-button>
      </el-tooltip>
    </div>
    <div class="main-chart" v-loading="dataLoading" element-loading-text="加载中……">
      <ve-line
        :data="chartData"
        :grid="grid"
        :extend="extend"
        :tooltip="tooltip"
        :after-set-option="afterSetOption"
        v-if="showChart"
        :height="showTable ? convertPX(450) + 'px' : '100%'"
      ></ve-line>
      <el-table
        v-if="showTable && tableData.rows && tableData.rows.length"
        border
        stripe
        :height="showChart ? 'calc(100% - ' + convertPX(450) + 'px)' : '100%'"
        ref="chartTable"
        :data="tableData.rows"
      >
        <template v-for="(column, index) in tableData.columns">
          <el-table-column
            v-if="index == 0"
            :key="index"
            :label="column"
            align="center"
            :prop="column"
            :width="convertPX(200)"
          >
            <template slot-scope="scope">
              {{ scope.row[column] }}
            </template>
          </el-table-column>
          <el-table-column v-else :key="index" :label="column" align="center">
            <template slot-scope="scope">{{ scope.row[column] }}</template>
          </el-table-column>
        </template>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout.vue";
import { grid, extend, tooltip, setIODrainageChartOption } from "./chartOption";
import { GetIoDrainageChart } from "@/api/IO";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
    }),
  },
  data() {
    return {
      startDate: "",
      endDate: "",
      showChart: true,
      showTable: true,
      chartData: {},
      dataLoading: false,
      grid: grid,
      extend: extend,
      tooltip: tooltip,
      tableData: {},
    };
  },
  watch: {
    inpatient(newVal) {
      if (!newVal) return;
      this.init();
    },
  },
  created() {
    this.init();
  },
  methods: {
    /**
     * description: 初始化
     * return {*}
     */
    init() {
      this.endDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.startDate = this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -7, "yyyy-MM-dd");
      this.getChartData();
    },
    /**
     * description: 统计图撇配置参数
     * param {*} chart
     * return {*}
     */
    afterSetOption(chart) {
      setIODrainageChartOption(chart, this, this.chartData);
    },
    /**
     * description: 获取折线图数据
     * return {*}
     */
    getChartData() {
      if (!this.startDate || !this.endDate) {
        return;
      }
      if (!this.inpatient && !this.$route.query?.caseNumber) {
        return;
      }
      let params = {
        startDate: this.startDate,
        endDate: this.endDate,
        inpatientID: this.inpatient?.inpatientID ?? "",
        language: this.$route.query?.language ?? undefined,
        hospitalID: this.$route.query?.hospitalID ?? "",
        caseNumber: this.$route.query?.caseNumber ?? "",
      };
      this.chartData = this.clearData(this.chartData);
      this.tableData = this.clearData(this.tableData);
      this.dataLoading = true;
      GetIoDrainageChart(params).then((res) => {
        this.dataLoading = false;
        if (this._common.isSuccess(res)) {
          this.chartData = res.data;
          if (res.data?.rows) {
            this.getTableData(res.data);
          }
          this.$nextTick(() => {
            this.$refs.chartTable?.doLayout();
          });
        }
      });
    },
    /**
     * description: 清除数据
     * param {*} data
     * return {*}
     */
    clearData(data) {
      data.rows = [];
      data.columns = [];
      return data;
    },
    /**
     * description: 转换后端数据，返回表格数据
     * param {*} chartData
     * return {*}
     */
    getTableData(chartData) {
      let columns = [];
      chartData.rows.forEach((dataRow) => {
        let filter = this.tableData.columns.find((column) => {
          return column == dataRow[chartData.mapping];
        });
        if (!filter) {
          columns.push(dataRow[chartData.mapping]);
        }
      });
      this.tableData.columns = columns;
      this.tableData.columns.unshift("导管名称");
      chartData.columns.forEach((dataColumn) => {
        if (dataColumn != chartData.mapping) {
          let startRow = { 导管名称: dataColumn };
          let endRow = {};
          chartData.rows.forEach((row) => {
            let selectRow = row[dataColumn];
            if (selectRow) {
              let addEndRow = { [row[chartData.mapping]]: row[dataColumn] };
              endRow = { ...endRow, ...addEndRow };
            }
          });
          let countRow = { ...startRow, ...endRow };
          this.tableData.rows = this.tableData.rows.concat([countRow]);
        }
      });
    },
  },
};
</script>
<style lang="scss">
.io-drainage-chart {
  .main-chart {
    background-color: #ffffff;
    height: 100%;
    padding: 10px 0 10px 0;
    box-sizing: border-box;
  }
}
</style>