<!--
 * FilePath     : \ccc.web\src\pages\transferPages\transferRoleAuthority.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 王浩杰
 * LastEditTime : 2021-09-09 18:14
 * Description  : 串权限维护
--> 
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
// 代码需要迁移，暂时串到nursing
import { getOldNursingUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      user: "getUser",
    }),
  },
  created() {
    this.url = getOldNursingUrl() + "export/roleAuthority?userID=" + this.user.userID + "&token=" + this.token;
  },
};
</script>
