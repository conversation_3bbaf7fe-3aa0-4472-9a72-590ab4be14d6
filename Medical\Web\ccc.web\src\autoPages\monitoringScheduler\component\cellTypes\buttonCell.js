/*
 * FilePath     : \src\autoPages\monitoringScheduler\component\cellTypes\buttonCell.js
 * Author       : 杨欣欣
 * Date         : 2024-06-16 16:08
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-08-07 11:20
 * Description  : 自定义列内容（按钮类）
 * CodeIterationRecord:
 */
export default {
  name: "buttonCell",
  functional: true,
  render(h, context) {
    const { props, listeners } = context;
    const cell = props.row[props.column.index];
    const getButtonName = () => {
      if (!props.column.title) {
        return "";
      }
      const index = props.column.title.indexOf("<br/>");
      return index !== -1
        ? props.column.title.substring(0, index).trim()
        : props.column.title;
    };
    const buttonName = getButtonName();
    // 借column的name字段记录按钮名称
    props.column.name = buttonName;

    // 生成按钮VNode
    const elButtonVNode = h(
      "el-button",
      {
        props: {
          type: "primary",
          size: "mini",
        },
        on: {
          click: () => listeners["click-button"](props.row, props.column),
        },
      },
      buttonName
    );

    // 若assessValue不存在，直接返回
    if (!cell.assessValue) {
      return elButtonVNode;
    }
    // 若assessValue存在，需再套一层el-badge
    return h(
      "el-badge",
      {
        attrs: {
          "is-dot": cell.assessValue != "",
        },
      },
      [elButtonVNode]
    );
  },
};
