import { getWebProxyApiUrl } from "./setting";
function WebProxy() {
  this.config = {
    api: function (api) {
      return getWebProxyApiUrl() + "/" + api;
    },
  };
  this.extend = function (moudles) {
    for (var moudle in moudles) {
      moudles[moudle].config = this.config;
      this[moudle] = moudles[moudle];
    }
  };
}

var Client = function () {
  this.run = function (callback) {
    var api = this.config.api("client/run");
    ajax.get(api, function (result) {
      window.wp.run = true;
      if (callback) callback();
    });
  };
  this.version = function (callback) {
    var api = this.config.api("client/version");
    ajax.get(api, function (result) {
      // if (result.success) {
      if (callback) callback(result.data);
      // }
    });
  };
  this.update = function (url, callback) {
    var api = this.config.api("client/update") + "?url=" + url;
    ajax.get(api, function (result) {
      if (result.success) {
        window.wp.version = result.data;
        if (callback) callback();
      }
    });
  };
};
var Print = function () {
  this.viewOrders = function (model, callback) {
    var api = this.config.api("print/ViewOrders");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
  this.printOrders = function (model, callback) {
    var api = this.config.api("print/PrintOrders");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
  this.viewPatienBedLabel = function (model, callback) {
    var api = this.config.api("print/ViewPatienBedLabel");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
  this.printPatienBedLabel = function (model, callback) {
    var api = this.config.api("print/PrintPatienBedLabel");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
  //打印文件
  this.printFile = function (model, callback) {
    var api = this.config.api("print/PrintFile");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
  //预览检验标签
  this.viewTestsLabel = function (model, callback) {
    var api = this.config.api("print/ViewTestsLabel");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
  //打印检验标签
  this.printTestsLabel = function (model, callback) {
    var api = this.config.api("print/PrintTestsLabel");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
  this.viewTubeLabel = function (model, callback) {
    var api = this.config.api("print/ViewTubeLabel");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
  this.printTubeLabel = function (model, callback) {
    var api = this.config.api("print/PrintTubeLabel");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
};

var CA = function () {
  this.getCAUserInfo = function (model, callback) {
    var api = this.config.api("CA/GetCAUserInfo");
    ajax.post(api, model, function (result) {
      if (callback) callback(result);
    });
  };
};

var wp = new WebProxy();
wp.extend({
  client: new Client(),
  print: new Print(),
  ca: new CA(),
});

function beforeReturn(option) {
  if (option.xhr.readyState == 4) {
    var result = {};
    if (option.xhr.status == 200) {
      result = JSON.parse(option.xhr.responseText);
      result.success = result.IsSuccess;
      delete result.IsSuccess;
      result.data = result.Data;
      delete result.Data;
      result.message = result.Message;
      delete result.Message;
    } else {
      result.success = false;
      if (option.xhr.responseText.length > 0) {
        result.message = option.xhr.responseText;
      }
    }
    option.callback(result);
  }
}
var ajax = {
  get: function (url, fn) {
    var xhr = new XMLHttpRequest();
    xhr.open("GET", url, true);
    xhr.onreadystatechange = function () {
      beforeReturn({
        xhr: xhr,
        callback: fn,
      });
    };

    if (xhr) {
      xhr.send();
    } else {
      fn.call(this, xhr.responseText);
    }
  },
  post: function (url, data, fn) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.onreadystatechange = function () {
      beforeReturn({
        xhr: xhr,
        callback: fn,
      });
    };
    xhr.send(JSON.stringify(data));
  },
};

export { wp };
