/*
 * FilePath     : \ccc.web\src\api\Cache.js
 * Author       : 李正元
 * Date         : 2020-06-20 10:25
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-12-29 19:27
 * Description  : 缓存
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/cache";

export const urls = {
  //更新缓存
  update: baseUrl + "/Update",
  updateCacheByKey: baseUrl + "/UpdateCacheByKey",
  removeCacheByNameAsync: baseUrl + "/RemoveCacheByNameAsync",
};

//2020-06-20 更新缓存
export const Update = (params) => {
  return http.post(urls.update, params);
};
//根据表名进行清除缓存
export const UpdateCacheByKey = (params) => {
  return http.post(urls.updateCacheByKey, qs.stringify(params));
};
/**
 * @description: 根据表名称或者RecordsCode清除缓存
 * @param params
 * @return
 */
export const removeCacheByName = (params) => {
  return http.post(urls.removeCacheByNameAsync, qs.stringify(params));
};
