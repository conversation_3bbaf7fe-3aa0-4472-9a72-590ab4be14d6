/*
 * FilePath     : \src\api\PatientPerioperative.js
 * Author       : 胡洋
 * Date         : 2020-12-01 11:30
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:47
 * Description  : 介入手术室
 */
import http from "../utils/ajax";
const baseUrl = "/PatientPerioperative";

export const urls = {
  GetInterventionOPRecordList: baseUrl + "/GetInterventionOPRecordList",
  GetInterventionOPAssessView: baseUrl + "/GetInterventionOPAssessView",
  GetInterventionOPRecordByMainID: baseUrl + "/GetInterventionOPRecordByMainID",
  SaveInterventionOPRecord: baseUrl + "/SaveInterventionOPRecord",
  GetAssessRecordsCodeByDeptID: baseUrl + "/GetAssessRecordsCodeByDeptID",
  GetRecordByhisOperationNo: baseUrl + "/GetRecordByhisOperationNo",
  GetOperationHandoverList: baseUrl + "/GetOperationHandoverList",
  AddTransferOPHandover: baseUrl + "/AddTransferOPHandover"
};
// 获取介入手术记录列表
export const GetInterventionOPRecordList = params => {
  return http.get(urls.GetInterventionOPRecordList, params);
};

// 获取介入手术评估模板
export const GetInterventionOPAssessView = params => {
  return http.get(urls.GetInterventionOPAssessView, params);
};

// 根据mainID获取一条介入手术记录
export const GetInterventionOPRecordByMainID = params => {
  return http.get(urls.GetInterventionOPRecordByMainID, params);
};

// 保存介入手术记录
export const SaveInterventionOPRecord = params => {
  return http.post(urls.SaveInterventionOPRecord, params);
};

// 获取介入手术配置信息
export const GetAssessRecordsCodeByDeptID = params => {
  return http.get(urls.GetAssessRecordsCodeByDeptID, params);
};

// 根据hisOperationNo获取一条介入手术记录
export const GetRecordByhisOperationNo = params => {
  return http.get(urls.GetRecordByhisOperationNo, params);
};

//根据患者ID获取手术记录列表
export const GetOperationHandoverList = params => {
  return http.get(urls.GetOperationHandoverList, params);
};

//新增手术转运交接
export const AddTransferOPHandover = params => {
  return http.get(urls.AddTransferOPHandover, params);
};
