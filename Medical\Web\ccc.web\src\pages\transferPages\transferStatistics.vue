<!--
 * FilePath     : \src\pages\transferPages\transferStatistics.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-01-18 09:18
 * Description  : 串统计分析
--> 
<template>
  <div class="transfer-statistics">
    <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
  </div>
</template>
<script>
// 代码需要迁移，暂时串到nursing
import { getOldNursingUrl, getStatisticsUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
      language: "getLanguage",
    }),
  },
  watch: {
    // 单页面多路由
    $route: function (to, from) {
      if (to.name == "statistics" && to.path == from.path) {
        this.initPage(to.query);
      }
    },
  },
  created() {
    this.initPage(this.$route.query);
  },
  methods: {
    initPage(query) {
      let statisticsType = query?.statisticsType;
      let menuListID = query?.menuListID;
      let path = query?.path ?? "main";
      // if (!statisticsType && !menuListID) {
      //   this._showTip("warning", "缺少statisticsType或者menuListID参数，请确认菜单配置！");
      //   return;
      // }
      // 跳转旧统计页面
      if (statisticsType == "oldStatistics") {
        this.url = getOldNursingUrl() + "export/statistics?token=" + this.token;
      } else {
        // 跳转新的统计页面
        this.url =
          getStatisticsUrl() +
          path +
          "?statisticsType=" +
          statisticsType +
          "&menuListID=" +
          menuListID +
          "&token=" +
          this.token +
          "&hospitalID=" +
          this.hospitalInfo.hospitalID +
          "&language=" +
          this.language;
      }
    },
  },
};
</script>
<style lang="scss">
.transfer-statistics {
  height: 100%;
  width: 100%;
  padding-left: 5px;
  box-sizing: border-box;
}
</style>
