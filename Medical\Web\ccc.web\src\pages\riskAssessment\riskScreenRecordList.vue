<!--
 * FilePath     : \src\pages\riskAssessment\riskScreenRecordList.vue
 * Author       :
 * Date         : 2021-07-19 10:19
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-13 17:41
 * Description  :风险统计页面
-->
<template>
  <base-layout headerHeight="auto" class="risk-screen-record-list">
    <div slot="header">
      <el-switch v-model="typeSwitch" active-text="评价日期" inactive-text="入院日期"></el-switch>
      <el-date-picker
        v-model="queryCriteria.startTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="startPickerOption"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        v-model="queryCriteria.endTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="endPickerOption"
      ></el-date-picker>
      <el-radio-group class="record-type" v-model="recordType" @change="changeRecordType">
        <el-radio-button label="0">全部</el-radio-button>
        <el-radio-button label="1">风险</el-radio-button>
        <el-radio-button label="2">非风险</el-radio-button>
      </el-radio-group>
      <span>表单:</span>
      <el-select v-model="riskListID" clearable placeholder="请选择" style="width: 180px">
        <el-option
          v-for="item in riskListScreen"
          :key="item.recordListID"
          :label="item.recordName"
          :value="item.recordListID"
        ></el-option>
      </el-select>
      <span>病区:</span>
      <station-selector v-model="stationListID" clearable label="" width="160" :hospitalFlag="true"></station-selector>
      <span>等级:</span>
      <el-select v-model="assessScoreRangeIDs" clearable placeholder="请选择" style="width: 180px" multiple>
        <el-option
          v-for="item in assessRangeList"
          :key="item.assessScoreRangeID"
          :label="item.rangeContent"
          :value="item.assessScoreRangeID"
        ></el-option>
      </el-select>
      <el-button class="query-button" icon="iconfont icon-search" type="primary" @click="getRiskList">查询</el-button>
      <el-button class="print-button" icon="iconfont icon-arrow-download" @click="exportToExcel()">导出EXCEL</el-button>
    </div>
    <!-- 患者信息显示表 -->
    <el-table
      height="100%"
      border
      :data="patientScoreMain"
      class="table-class"
      stripe
      v-loading="loading"
      element-loading-text="加载中……"
    >
      <el-table-column prop="stationName" label="病区" align="center" width="120" sortable></el-table-column>
      <el-table-column prop="departmentName" label="科室" align="center" width="120" sortable></el-table-column>
      <el-table-column prop="bedNumber" label="床号" align="center" min-width="50" sortable></el-table-column>
      <el-table-column prop="localCaseNumber" label="病案号" align="center" width="100" sortable></el-table-column>
      <el-table-column prop="patientName" label="姓名" align="center" min-width="80" sortable></el-table-column>
      <el-table-column prop="phoneNumber" label="联系电话" align="center" width="120" sortable></el-table-column>
      <el-table-column prop="sex" label="性别" align="center" width="50" sortable></el-table-column>
      <el-table-column prop="age" label="年龄" align="center" width="50" sortable></el-table-column>
      <el-table-column prop="nursingLevel" label="护理级别" align="center" width="100" sortable></el-table-column>
      <el-table-column prop="scorePoint" label="评分" align="center" width="50" sortable></el-table-column>
      <el-table-column prop="risk" label="等级" align="center" width="80" sortable></el-table-column>
      <el-table-column prop="admissionDate" label="入院日期" align="center" width="120" sortable>
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.admissionDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="assessDateTime" label="评估日期时间" align="center" width="160" sortable>
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.assessDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="shiftName" label="班别" align="center" width="50" sortable></el-table-column>
      <el-table-column prop="employeeName" label="评估人" align="center" min-width="80" sortable></el-table-column>
      <el-table-column align="center" label="操作" width="65">
        <template slot-scope="scope">
          <el-tooltip content="查看风险明细">
            <i class="iconfont icon-more" @click="riskRowClick(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="centerDialogVisible"
      :title="dialogTitle"
      custom-class="no-footer"
      v-if="centerDialogVisible"
      fullscreen
    >
      <risk-component :params="componentParams"></risk-component>
    </el-dialog>
  </base-layout>
</template>
<script>
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import { export_json_to_excel } from "@/vendor/Export2Excel.js";
import { GetRecords } from "@/api/RecordsList";
import { GetAssessRange, GetRiskScreenRecordList } from "@/api/Static";
export default {
  components: {
    baseLayout,
    stationSelector,
    riskComponent,
  },
  data() {
    let that = this;
    return {
      //初始化为当前日期
      queryCriteria: {
        startTime: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
        endTime: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      },
      //时间的禁用
      startPickerOption: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.queryCriteria.endTime).getTime();
        },
      },
      endPickerOption: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.queryCriteria.startTime).getTime() - 8.64e7;
        },
      },
      riskListID: "",
      assessScoreRangeIDs: [],
      riskList: [],
      riskListScreen: [],
      assessRangeList: [],
      stationListID: undefined,
      patientScoreMain: [],
      loading: false,
      dialogTitle: "",
      centerDialogVisible: false,
      //表单状态0：全部，1：风险，2：非风险
      recordType: 0,
      typeSwitch: true,
      componentParams: undefined,
      record: undefined,
    };
  },
  watch: {
    riskListID(newVal) {
      this.record = this.riskList.find((record) => {
        return record.recordListID == newVal;
      });
      this.assessScoreRangeIDs = [];
      this.patientScoreMain = [];
      this.getAssessRange();
    },
    recordType() {
      this.fillterRecordList();
    },
    immediate: true,
  },
  created() {
    this.getRecords();
    this.fillterRecordList();
  },
  methods: {
    //获得下拉选框集合
    getRecords() {
      GetRecords().then((result) => {
        if (this._common.isSuccess(result)) {
          this.riskList = result.data;
          this.fillterRecordList();
        }
      });
    },
    changeRecordType() {
      this.riskListID = "";
      this.fillterRecordList();
    },
    fillterRecordList() {
      let riskListScreen = this.riskList;
      // 全部
      if (this.recordType == 0) {
        this.riskListScreen = riskListScreen;
      }
      // 风险
      if (this.recordType == 1) {
        this.riskListScreen = riskListScreen.filter((record) => {
          return record.recordType == "Risk";
        });
      }
      if (this.recordType == 2) {
        // 非风险
        this.riskListScreen = riskListScreen.filter((record) => {
          return record.recordType != "Risk";
        });
      }
    },
    getDialogTitle(row) {
      let str = row.bedNumber + "-" + row.patientName + "【" + row.sex + "-" + row.age + "】";
      return str;
    },
    getAssessRange() {
      let params = {
        recordListID: this.riskListID,
      };
      GetAssessRange(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.assessRangeList = result.data;
        }
      });
    },
    getRiskList() {
      let params = {
        typeSwitch: this.typeSwitch,
        stationID: this.stationListID,
        startDate: this.queryCriteria.startTime,
        endDate: this.queryCriteria.endTime,
        recordListID: this.riskListID,
        assessScoreRangeIDs: JSON.stringify(this.assessScoreRangeIDs),
      };
      this.loading = true;
      GetRiskScreenRecordList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.patientScoreMain = result.data;
        }
      });
    },
    riskRowClick(row) {
      this.dialogTitle = this.getDialogTitle(row);
      let patient = {
        inpatientID: row.inaptientID,
        age: row.age,
        genderCode: row.gender,
        departmentListID: row.departmentListID,
        stationID: row.stationID,
        dateOfBirth: row.dateOfBirth,
        bedNumber: row.bedNumber,
      };
      this.$emit("getPatientInfo", patient);
      this.componentParams = {
        patientInfo: patient,
        showPoint: this.record.showPointFlag,
        showTime: true,
        showStyle: this.record.showStyle,
        showBar: this.record.recordType == "Risk",
        recordListID: this.record.recordListID,
        recordsCode: this.record.recordsCode,
        patientScoreMainID: row ? row.patientScoreMainID : undefined,
        readOnly: true,
        assessTimeReadonly: true,
        assessTime: row.assessDateTime,
      };
      this.centerDialogVisible = true;
    },
    //导出Excel表格通用
    exportToExcel() {
      let mainData = this.patientScoreMain;
      const mainHeader = [
        "病区",
        "科室",
        "床号",
        "病案号",
        "姓名",
        "联系电话",
        "性别",
        "年龄",
        "护理级别",
        "评分",
        "等级",
        "入院日期",
        "评估日期时间",
        "班别",
        "评估人",
        "诊断",
      ];
      const mainFilterVal = [
        "departmentName",
        "stationName",
        "bedNumber",
        "localCaseNumber",
        "patientName",
        "phoneNumber",
        "sex",
        "age",
        "nursingLevel",
        "scorePoint",
        "risk",
        "admissionDate",
        "assessDateTime",
        "shiftName",
        "employeeName",
        "diagnosis",
      ];
      const dataMain = this.formatJson(mainFilterVal, mainData);
      export_json_to_excel(mainHeader, dataMain, this._datetimeUtil.getNowDate("yyyyMMdd") + "患者风险记录清单");
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
  },
};
</script>

<style lang="scss">
.risk-screen-record-list {
  .print-button {
    float: right;
    margin-top: 8px;
  }
}
</style>
