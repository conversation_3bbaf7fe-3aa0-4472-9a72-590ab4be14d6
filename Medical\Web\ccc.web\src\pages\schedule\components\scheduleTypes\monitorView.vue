<!--
 * FilePath     : \src\pages\schedule\components\scheduleTypes\monitorView.vue
 * Author       : 杨欣欣
 * Date         : 2021-10-21 10:30
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-15 19:07
 * Description  : 监测项组件，组装排程中的执行细项
 * CodeIterationRecord: 2744-作为IT人员，我需要排程执行疼痛及批量执行，可以跳转专项护理 2022-07-07 杨欣欣
-->
<template>
  <div class="monitor-view">
    {{ temp }}
    <template v-if="item.style == 'L'">
      <div class="div_L">
        <div class="label L">{{ getText(item.showName) }}</div>
      </div>
    </template>
    <template v-if="item.style == 'T'">
      <div class="td-wrap div_T">
        <div class="icon">
          <i v-if="item.showMessage" class="iconfont icon-info" @click="showMessage(item.showMessage)"></i>
        </div>
        <div class="label">{{ getText(item.showName) }}</div>
        <el-input v-model="item.scheduleData" @blur="changeValue(item)" style="width: 180px"></el-input>
        <span>{{ item.unit ? item.unit : "" }}</span>
        <span class="last-data" v-if="item.lastData">{{ item.lastData }} {{ item.unit ? item.unit : "" }}</span>
      </div>
    </template>
    <template v-if="item.style == 'TN'">
      <div class="div_TN">
        <div class="icon">
          <i v-if="item.showMessage" class="iconfont icon-info" @click="showMessage(item.showMessage)"></i>
        </div>
        <div class="label">{{ getText(item.showName) }}</div>
        <div class="input_TN">
          <el-input
            v-if="item.formula"
            v-formula="{ item: item, items: items }"
            name="TN"
            v-model="item.scheduleData"
            @blur="check(item)"
            @input="checkInputValue(item)"
            @keyup.38.native.stop="inputEnter($event, false)"
            @keyup.40.native.stop="inputEnter($event, true)"
            @keyup.enter.native.stop="inputEnter($event, true)"
            :readonly="isReadOnly"
            :class="item.inputValueColor"
            style="width: 65px"
          ></el-input>
          <el-input
            v-else
            name="TN"
            v-model="item.scheduleData"
            @blur="check(item)"
            @click.native="showKeyBoard($event)"
            @input="updateValue($event, item), checkInputValue(item)"
            @keyup.38.native.stop="inputEnter($event, false)"
            @keyup.40.native.stop="inputEnter($event, true)"
            @keyup.enter.native.stop="inputEnter($event, true)"
            :readonly="isReadOnly"
            :class="item.inputValueColor"
            style="width: 65px"
          ></el-input>
          <span>{{ item.unit ? item.unit : "" }}</span>
          <span class="last-data" v-if="item.lastData">{{ item.lastData }}</span>
        </div>
      </div>
    </template>
    <template v-if="item.style == 'R'">
      <div class="td-radio div_R">
        <div class="icon radio">
          <i v-if="item.showMessage" class="iconfont icon-info" @click="showMessage(item.showMessage)"></i>
        </div>
        <el-radio v-model="item.scheduleData" :label="item.showName" @click.native.stop.prevent="changeValue(item)">
          {{ item.showName }}
        </el-radio>
        <i class="iconfont icon-check-mark last-data" v-if="item.lastData"></i>
      </div>
    </template>
    <template v-if="item.style == 'C'">
      <div class="div_C">
        <div class="icon-c check">
          <i v-if="item.showMessage" class="iconfont icon-info" @click="showMessage(item.showMessage)"></i>
        </div>
        <el-checkbox v-model="checkStatus" @change="changeValue(item)">
          {{ item.showName }}
        </el-checkbox>
        <i class="iconfont icon-check-mark last-data" v-if="item.lastData"></i>
      </div>
    </template>
    <template v-if="item.style == 'B'">
      <div class="div_B">
        <el-badge
          v-if="item.scheduleData"
          :class="['badge-item', { 'is-string': !/^\d+$/.test(item.scheduleData) }]"
          :value="item.scheduleData"
        >
          <el-button size="mini" type="primary" @click="changeItem(item)">
            {{ item.showName ? item.showName.trim() : "" }}
          </el-button>
        </el-badge>
        <el-button v-else size="mini" type="primary" @click="changeItem(item)">
          {{ item.showName ? item.showName.trim() : "" }}
        </el-button>
      </div>
    </template>
    <key-board
      v-tobody="{ id: 'key-board' }"
      :show="isShowKeyBoard"
      :output="el"
      typeName="TN"
      @hide="hideKeyBoard"
    ></key-board>
  </div>
</template>

<script>
import keyBoard from "@/components/KeyBoard/KeyBoard";
export default {
  components: {
    keyBoard,
  },
  directives: {
    // 自定义指令，处理计算公式的结果
    formula: {
      // 组件更新时计算，实现类似v-model的功能
      componentUpdated(el, binding, vnode) {
        let formula = binding.value.item.formula;
        let params = formula.params;
        let expression = formula.expression;
        for (let param of params) {
          binding.value.items.find((i) => {
            if (i.interventionDetailID == param.id) {
              let value = 0;
              if (i.scheduleData) {
                value = i.scheduleData.trim();
              } else {
                return false;
              }
              expression = expression.replace(param.key, value);
              return true;
            }
          });
        }
        if (expression.indexOf("}") > 0) {
          binding.value.item.scheduleData = "";
        } else {
          let newValue =
            vnode.context._decimalUtil.decimalRound(eval(expression), formula.decimalRule, formula.decimalValue) + "";
          // 防止循环刷新
          if (binding.value.item.scheduleData == newValue) {
            return;
          } else {
            binding.value.item.scheduleData = newValue;
          }
        }
        vnode.context.$emit("changeValue", binding.value.item);
      },
    },
  },
  props: {
    item: {
      type: Object,
      require: true,
    },
    items: {
      type: Array,
      require: true,
    },
  },
  data() {
    return {
      isReadOnly: false,
      checkStatus: false,
      temp: "  ",
      isShowKeyBoard: false,
      el: undefined,
    };
  },
  watch: {
    item: {
      deep: true,
      immediate: true,
      handler(newValue, oldVale) {
        if (newValue && newValue.scheduleData) {
          if (this.item.style == "C") {
            this.checkStatus = true;
          } else {
            this.checkStatus = false;
          }
          this.$emit("changeValue", newValue);
        } else {
          this.checkStatus = false;
        }
      },
    },
    //监听assessListID 添加inputValueColor字段 只会在进入页面执行一次  --GPC
    "item.assessListID": {
      handler(newValue) {
        this.addInputValueColor(this.item);
      },
      immediate: true,
    },
    "item.scheduleData": {
      handler(newValue) {
        if (newValue) {
          //触发异常颜色
          this.checkInputValue(this.item);
        }
      },
    },
  },
  mounted() {
    this.temp = "";
  },
  methods: {
    getText(text) {
      if (!text) return "";
      if (text.indexOf(":") != -1 || text.indexOf("：") != -1) {
        return text;
      }
      return (text += "：");
    },
    updateValue(value, item) {
      item.scheduleData = value;
    },
    showKeyBoard(e) {
      if (this._common.isPC()) {
        this.isReadOnly = false;
        return;
      }
      this.isReadOnly = true;
      this.el = e.target;
      this.isShowKeyBoard = true;
    },
    hideKeyBoard() {
      this.el = undefined;
      this.isShowKeyBoard = false;
    },
    showMessage(messageContent) {
      this._showMessage({
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
    //获取弹窗数据添加颜色字段--GPC
    addInputValueColor(value) {
      if (value.triggerCondition) {
        value.inputValueColor = "";
        this.checkInputValue(value);
      }
    },
    //数值异常标红--GPC
    checkInputValue(value) {
      if (!value.triggerCondition || !value.scheduleData) {
        return;
      }
      value.triggerCondition.some((item) => {
        if (
          Number(value.scheduleData) >= Number(item.LowNotify) &&
          Number(value.scheduleData) <= Number(item.UpNotify)
        ) {
          this.$set(value, "inputValueColor", "red");
          return true;
        } else {
          this.$set(value, "inputValueColor", "");
        }
      });
    },
    //数据验证
    check(item) {
      if (item.scheduleData || item.scheduleData + "" === "0") {
        let checkTN = {
          controlerType: item.style,
          itemName: item.showName,
          assessValue: item.scheduleData,
          decimal: item.decimal,
          upError: item.upError,
          lowError: item.lowError,
          checkLevelDict: item.checkLevelDict,
        };
        // 检核TN条件，不符合检核，清空排程数据
        let result = this._common.checkAssessTN(checkTN);
        item.scheduleData = result.value;
        if (!result.flag) {
          this.$emit("checkTN", false);
          return;
        }
      }
      this.$emit("checkTN", true);
      this.changeValue(item);
    },
    changeValue(item) {
      if (item.style == "C") {
        if (!this.checkStatus) {
          item.scheduleData = "";
        } else {
          item.scheduleData = item.showName;
        }
      } else if (item.style == "R") {
        if (item.scheduleData == item.showName) {
          item.scheduleData = "";
        } else {
          item.scheduleData = item.showName;
        }
      }
      this.$emit("changeValue", item);
    },
    inputEnter(e, flag) {
      this.$emit("inputEnter", e, flag);
    },
    /**
     * description: 按钮点击事件触发函数，将细项抛给父组件
     * param {*} item
     * return {*}
     */
    changeItem(item) {
      if (item.scheduleData == null || item.scheduleData == "null") {
        item.scheduleData = "";
      }
      this.$emit("button-click", item);
    },
  },
};
</script>
<style lang="scss">
.monitor-view {
  display: inherit;
  margin: 3px 10px 3px 10px;
  .div_TN {
    display: flex;
    .icon {
      position: relative;
      top: 5px;
    }
    .label {
      position: relative;
      top: 3px;
    }
    .input_TN {
      display: inline-block;
      .red {
        & > input {
          color: #ff0000;
        }
      }
    }
  }
  .div_T {
    display: flex;
    .icon {
      position: relative;
      top: 5px;
    }
    .label {
      position: relative;
      top: 3px;
    }
  }
  .div_R {
    .radio {
      display: inline-block;
    }
  }
  .div_C {
    width: 100%;
    overflow: hidden;
    & > * {
      float: left;
    }
    .icon-c {
      width: 16px;
      height: 16px;
      margin-right: 5px;
      margin-top: -2px;
    }
    .el-checkbox {
      max-width: calc(100% - 50px);
    }
    .el-checkbox__label {
      white-space: initial;
    }
    .el-checkbox__input {
      vertical-align: top;
      margin-top: 3px;
    }
  }
  .div_B {
    position: relative;
    top: 5px;
    left: 10px;
  }
  .last-data {
    color: $base-color;
    margin: 0 0 0 5px;
  }
  .label {
    font-size: 14px;
    display: inline-block;
    &.L {
      color: $base-color;
    }
  }
  .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    &.check,
    &.label {
      position: relative;
      top: 5px;
    }
  }
}
</style>
