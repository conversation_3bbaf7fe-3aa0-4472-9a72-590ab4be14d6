/*
 * FilePath     : \ccc.web\src\utils\i18n\lang\zh\pages\io\ioRecordMaintenance.js
 * Author       : 苏军志
 * Date         : 2021-11-01 11:59
 * LastEditors  : 陈超然
 * LastEditTime : 2023-06-26 15:29
 * Description  : 出入量维护画面ioRecordMaintenance
 */
export default {
  ioRecordMaintenance: {
    date: "记录日期",
    time: "时间",
    type: "类别",
    item: "项目",
    inputType: "输入类型",
    inputContent: "输入内容",
    numberOfTimes: "次数",
    amount: "量",
    character: "性状",
    smell: "气味",
    color: "颜色",
    remarks: "备注",
    dialogTitleAdd: "出入量 - 新增",
    dialogTitleModify: "出入量 - 修改",
    noSelectTip: "请先选择要删除的数据！",
    batchSaveTip: "没有异动数据，无需保存！",
    station: "病区",
    department: "科室",
    bed: "床号",
    noDeptTip: "科室不能为空！",
    addIoRecord: {
      input: "输入",
      output: "输出",
      category: "种类",
      unit: "毫升",
      infusionType0: "无",
      infusionType10: "静脉输入(一般)",
      infusionType20: "静脉泵入",
      saveTipItem: "请选择项目！",
      saveTipAttribute:
        "【颜色】、【性状】、【气味】、【量】、【次数】、【备注】必须录入一个！",
      saveTipTimes1: "【次数】请输入数值",
      saveTipTimes2: "【次数】不能为负值！",
      saveTipTimes3: "【次数】请输入正整数",
      saveTipAmount1: "【量】请输入数值",
      saveTipAmount2: "【量】请输入数值且最多两位小数",
      bringToNursingRecord: "护理记录",
      informPhysician: "通知医师"
    },
    tableWidth: {
      ioDate: 100,
      ioTime: 65,
      ioKind: 70,
      ioItem: 70,
      inPutType: 120,
      inPutContent: 120,
      intakeOutputTimes: 50,
      amount: 100,
      character: 90,
      smell: 90,
      color: 110,
      remarks: 60,
      operation: 70,
      informPhysician: 45,
      bringToNursingRecord: 45,
      station: 100,
      department: 100,
      bed: 100
    }
  }
};
