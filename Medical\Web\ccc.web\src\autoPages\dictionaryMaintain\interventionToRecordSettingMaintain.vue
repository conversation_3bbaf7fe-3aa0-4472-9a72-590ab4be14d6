<!--
 * FilePath     : \src\autoPages\dictionaryMaintain\interventionToRecordSettingMaintain.vue
 * Author       : 曹恩
 * Date         : 2023-04-17 09:16
 * LastEditors  : 曹恩
 * LastEditTime : 2023-04-20 14:17
 * Description  : 排程是否带入护理记录单配置维护
 * CodeIterationRecord: 
 2023-04-17 En 3338-作为护理人员，我需要批量作业-批量执行处可以与排程执行处带入护理记录标记保持一致，以利快速作业
-->
<template>
  <base-layout class="intervention-to-record-setting-maintain" headerHeight="auto" :showFooter="true">
    <div slot="header">
      <station-selector label="病区：" :width="convertPX(180) + ''" v-model="stationID" :userID="user.userID" />
      <dept-selector
        label="科室："
        :width="convertPX(200) + ''"
        v-model="departmentListID"
        :stationID="stationID"
        @change="getData"
      />
      <span class="label">措施内容：</span>
      <el-input
        v-model="keyWords"
        @keyup.enter.native="getData"
        placeholder="输入措施名称"
        class="search-input"
        clearable
        @clear="getData"
      >
        <i slot="append" class="iconfont icon-search" @click="getData"></i>
      </el-input>
      <el-radio-group v-model="selectType">
        <el-radio v-for="(type, index) in scheduleTypes" :key="index" :label="type.key">{{ type.value }}</el-radio>
      </el-radio-group>
    </div>
    <div slot-scope="data" class="data-table">
      <el-table
        :height="data.height - 10"
        v-loading="loading"
        element-loading-text="加载中……"
        border
        stripe
        :data="showInterventions"
      >
        <el-table-column
          prop="actionTypeName"
          label="措施类型"
          :width="convertPX(120) + ''"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="intervention"
          label="措施名称"
          :min-width="convertPX(400) + ''"
          head-align="center"
        ></el-table-column>
        <el-table-column label="是否默认带入护理记录单" :width="convertPX(200) + ''" head-align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.bringMark"
              @change="changeRow(scope.row.interventionID, scope.row.bringMark)"
            ></el-switch>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-sizes="[25, 50, 100]"
        :page-size="pageSize"
        :total="interventionCount"
        layout="sizes, prev, pager, next"
      ></el-pagination>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout.vue";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import { GetInterventionToRecordSetting, UpdateInterventionToRecordSetting } from "@/api/Setting";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    stationSelector,
    deptSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      loading: false,
      stationID: undefined,
      departmentListID: undefined,
      keyWords: "",
      scheduleTypes: [],
      interventions: [],
      cloneInterventions: [],
      showInterventions: [],
      selectType: "-1",
      interventionCount: undefined,
      currentPage: 1,
      pageSize: 25,
    };
  },
  watch: {
    stationID: {
      handler() {
        this.keyWords = "";
        this.showInterventions = [];
      },
    },
    selectType() {
      this.showInterventionType();
    },
  },
  created() {
    this.stationID = this.user.stationID;
  },
  methods: {
    /**
     * description: 获取措施数据
     * return {*}
     */
    async getData() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return;
      }
      if (!this.departmentListID) {
        this._showTip("warning", "请选择科室");
        return;
      }
      let params = {
        stationID: this.stationID,
        departmentListID: this.departmentListID,
      };
      this.loading = true;
      this.tableData = [];
      await GetInterventionToRecordSetting(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.deelData(res.data);
        }
      });
    },
    /**
     * description: 处理措施分类
     * param {*} data Settting数据
     * return {*}
     */
    deelData(data) {
      this.scheduleTypes = [];
      this.interventions = [];
      data.forEach((item) => {
        let type = item.scheduleType;
        let label = type.typeName;
        this.scheduleTypes.push({ key: type.actionType, value: label });
        this.interventions.push({
          key: type.actionType,
          intervention: item.interventionSettingViews,
        });
      });
      this.scheduleTypes.unshift({
        key: "-1",
        value: "全部",
      });

      this.showInterventionType();
    },
    /**
     * description: 根据类型显示措施数据
     * return {*}
     */
    showInterventionType() {
      this.cloneInterventions = [];
      this.interventions.forEach((item) => {
        if (this.selectType === "-1" || this.selectType === item.key) {
          this.cloneInterventions = this.cloneInterventions.concat(item.intervention);
        }
      });
      if (this.keyWords) {
        // 按给定的关键字进行过滤
        this.cloneInterventions = this.cloneInterventions.filter(
          (element) => element.intervention.indexOf(this.keyWords) != -1
        );
      }
      this.interventionCount = this.cloneInterventions.length;
      this.queryByPage();
    },
    /**
     * description:更新Setting
     * param {*} interventionID 措施id
     * param {*} bringMark 带入标记
     * return {*}
     */
    async changeRow(interventionID, bringMark) {
      let params = {
        interventionID: interventionID,
        bringMark: bringMark,
        stationID: this.stationID,
        departmentListID: this.departmentListID,
      };
      this.loading = true;
      await UpdateInterventionToRecordSetting(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "修改成功！");
        }
      });
    },
    /**
     * description: 分页size变化
     * return {*}
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.queryByPage();
    },
    /**
     * description: 分页页号变化
     * return {*}
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      this.queryByPage();
    },
    /**
     * description: 分页查询
     * return {*}
     */
    queryByPage() {
      // 起始位置 = (当前页 - 1) x 每页的大小
      let start = (this.currentPage - 1) * this.pageSize;
      // 结束位置 = 当前页 x 每页的大小
      let end = this.currentPage * this.pageSize;
      this.showInterventions = this.cloneInterventions.slice(start, end);
    },
  },
};
</script>

<style lang="scss">
.intervention-to-record-setting-maintain {
  height: calc(100% - 10px);
  .search-input {
    width: 15%;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
}
</style>