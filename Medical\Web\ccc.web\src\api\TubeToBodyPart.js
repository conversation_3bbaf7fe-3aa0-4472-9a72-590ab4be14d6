/*
 * FilePath     : \ccc.web\src\api\TubeToBodyPart.js
 * Author       : 李艳奇
 * Date         : 2020-10-14 11:04
 * LastEditors  : 李艳奇
 * LastEditTime : 2020-10-20 11:21
 * Description  : 
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/TubeToBodyPart";

export const urls = {
  GetTubeList: baseUrl + "/GetTubeList",
  GetBodyPartList: baseUrl + "/GetBodyPartList",
  DeleteTubeToBodyPart: baseUrl + "/DeleteTubeToBodyPart",
  SaveTubeToBodyParts: baseUrl + "/SaveTubeToBodyParts"
};

// 获取所有的导管
export const GetTubeList = () => {
  return http.get(urls.GetTubeList);
};

// 获取指定导管对应的身体部位
export const GetBodyPartList = params => {
  return http.get(urls.GetBodyPartList + "?TubeListID=" + params);
};
// 删除指定的病区对导管记录
export const DeleteTubeToBodyPart = params => {
  return http.post(urls.DeleteTubeToBodyPart, qs.stringify(params));
};

// 添加指定的病区对导管记录
export const SaveTubeToBodyParts = params => {
  return http.post(urls.SaveTubeToBodyParts, params);
};

