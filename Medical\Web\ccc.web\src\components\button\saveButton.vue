<!--
 * FilePath     : \src\components\button\saveButton.vue
 * Author       : 苏军志
 * Date         : 2023-01-03 15:24
 * LastEditors  : 张现忠
 * LastEditTime : 2023-02-21 09:24
 * Description  : 新增按钮
 * CodeIterationRecord: 2023-02-21:修复保存按钮点击后触发两次的问题 -zxz
-->
<template>
  <el-button
    v-bind="$attrs"
    v-on="$listeners"
    class="save-button"
    type="primary"
    icon="iconfont icon-save-button"
    @click="handClick"
  >
    <slot>保存</slot>
  </el-button>
</template>

<script>
export default {
  name: "saveButton",
  methods: {
    handClick: function (...params) {
      this._common.session("actionButtonClass", "save-button");
    },
  },
};
</script>

<style>
</style>