/*
 * FilePath     : \src\api\JobTip.js
 * Author       : 苏军志
 * Date         : 2020-07-12 16:44
 * LastEditors  : 苏军志
 * LastEditTime : 2022-08-30 18:47
 * Description  : 工作提醒
 */

import http from "../utils/ajax";
const baseUrl = "/JobTip";

export const urls = {
  GetJobTipList: baseUrl + "/GetJobTipList",
  QueryPatientJobTipList: baseUrl + "/QueryPatientJobTipList",
  GetStationToJobTipTableHeader: baseUrl + "/GetStationToJobTipTableHeader",
  GetStationToJobTipList: baseUrl + "/GetStationToJobTipList",
};

// 获取工作提醒列表
export const GetJobTipList = params => {
  return http.get(urls.GetJobTipList, params);
};
//获取单病人的工作提醒清单
export const QueryPatientJobTipList = params => {
  return http.post(urls.QueryPatientJobTipList, params);
};
//获取病区工作提醒二级表头内容
export const GetStationToJobTipTableHeader = params => {
  return http.get(urls.GetStationToJobTipTableHeader, params);
};
//获取病区对公共提醒
export const GetStationToJobTipList = params => {
  return http.get(urls.GetStationToJobTipList, params)
}