<!--
 * FilePath     : \src\pages\recordSupplement\nursingRecord\nursingRecordOld.vue
 * Author       : 赵路广
 * Date         : 2019-10-01 08:00
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-11 14:57
 * Description  : 补护理记录页面
 * CodeIterationRecord:
                  2901-作为IT人员，我需要的记录补录时，可以勾选呼吸及辅助、起搏心率，以利于临床方便录入，及体温单呈现
                  3008-作为IT人员，我需要患者出院后，可以补录护理危重单数据 -zxz 2022-10-12 16:02
                  3304-作为护理人员，我需要记录补录新增时默认当前登陆账户病区且过滤病人病区，以减少病区错选问题 -杨欣欣
                  3386-作为护理人员，我需要记录补录意识、吸氧方式、护理级别、测温方式可以下拉选择，以利护理记录补录 -胡长攀
                  4033-作为护理人员，我需要新增完护理记录可以默认带入护理记录单，以提高补录效率

-->
<template>
  <base-layout class="nursing-record-old" header-height="auto" :showHeader="false">
    <div class="layout-wrap">
      <base-layout v-loading="loading" element-loading-text="加载中……" v-if="patient">
        <div class="layout-top" slot="header">
          <label>执行日期:</label>
          <el-date-picker
            class="data-select"
            v-model="nursingRecordDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择护理记录日期"
            @change="getPatientData()"
          ></el-date-picker>
          <el-select
            v-if="showNursingRecordPdfTypeOptions"
            v-model="nursingRecordPdfType"
            placeholder="请选择护理记录单类型"
            @change="getPatientData()"
          >
            <el-option
              v-for="item in nursingRecordPdfTypes"
              :key="item.settingValue"
              :label="item.description"
              :value="item.settingValue"
            ></el-option>
          </el-select>
          <div class="top-btn">
            <el-button class="add-button" @click="showEditDialog()" icon="iconfont icon-add">新增</el-button>
          </div>
        </div>
        <div slot-scope="layout" :style="{ height: layout.height + 'px' }" class="supply-data">
          <u-table
            :data="supplyDatas"
            ref="nursingRecordTable"
            border
            stripe
            highlight-current-row
            :height="layout.height"
            :row-height="35"
            use-virtual
            :show-body-overflow="'tooltip'"
            v-if="dataColumns && dataColumns.length > 0"
          >
            <u-table-column
              :label="item.description"
              v-for="(item, index) in dataColumns"
              align="center"
              :min-width="item.width ? item.width : 70"
              :fixed="index < 4"
              :key="item.key"
            >
              <template slot-scope="scope" v-if="scope.row[item.key]">
                <el-tooltip
                  :content="getTitle(scope.row[item.key])"
                  v-if="scope.row[item.key].isRetest || scope.row[item.key].bringTPRTime"
                >
                  <div :class="getClass(scope.row[item.key])">
                    {{ scope.row[item.key].value }}
                  </div>
                </el-tooltip>
                <template v-else>
                  {{ scope.row[item.key].value }}
                </template>
              </template>
              <u-table-column
                v-for="(childItem, childIndex) in item.child"
                :key="childIndex"
                :label="childItem.description"
                :name="childItem.Key"
                align="center"
                :min-width="childItem.width ? childItem.width : 70"
              >
                <template slot-scope="scope" v-if="scope.row[childItem.key]">
                  <el-tooltip
                    :content="getTitle(scope.row[childItem.key])"
                    v-if="scope.row[childItem.key].isRetest || scope.row[childItem.key].bringTPRTime"
                  >
                    <div :class="getClass(scope.row[childItem.key])">
                      {{ scope.row[childItem.key].value }}
                    </div>
                  </el-tooltip>
                  <template v-else>
                    {{ scope.row[childItem.key].value }}
                  </template>
                </template>
              </u-table-column>
            </u-table-column>
            <!-- 通知医师 -->
            <u-table-column
              label="通知医师"
              :name="optColumn.Key"
              class-name="opt-column"
              header-align="center"
              :width="60"
              align="center"
              fixed="right"
            >
              <template slot-scope="monitor">
                <div :class="['opt', { 'read-only': readOnly }]">
                  <el-checkbox
                    :class="retestNursingRecordID"
                    :disabled="readOnly"
                    v-model="monitor.row.informPhysician.value"
                    @change="changeRecord(monitor.row.nursingRecordID.value, '', monitor.row.informPhysician.value)"
                  />
                </div>
              </template>
            </u-table-column>
            <!-- 带入护理记录 -->
            <u-table-column
              label="带入护理记录"
              :name="optColumn.Key"
              class-name="opt-column"
              header-align="center"
              :width="60"
              align="center"
              fixed="right"
            >
              <template slot-scope="monitor">
                <div :class="['opt', { 'read-only': readOnly }]">
                  <el-checkbox
                    :class="retestNursingRecordID"
                    :disabled="readOnly"
                    :value="monitor.row.bringToNR.value == '1'"
                    @change="
                      changeRecord(
                        monitor.row.nursingRecordID.value,
                        monitor.row.bringToNR.value,
                        monitor.row.informPhysician.value
                      )
                    "
                  />
                </div>
              </template>
            </u-table-column>

            <!-- 操作列 -->
            <u-table-column
              :label="optColumn.description"
              :name="optColumn.Key"
              class-name="opt-column"
              header-align="center"
              :width="optColumn.width ? optColumn.width : 85"
              align="center"
              fixed="right"
            >
              <template slot-scope="monitor">
                <div class="opt">
                  <el-tooltip content="修改">
                    <i
                      class="iconfont iconfont icon-edit"
                      v-if="monitor.row.isRetest.value == 'true'"
                      @click="showRetest(monitor)"
                    ></i>
                    <i class="iconfont icon-edit save" v-else @click="showEditDialog(monitor, true)"></i>
                  </el-tooltip>
                </div>
                <div class="opt">
                  <el-tooltip content="复测体温" v-if="monitor.row.showRetestBtn.value == 'true'">
                    <i class="iconfont icon-temperature" @click="showRetest(monitor, 1)"></i>
                  </el-tooltip>
                </div>
                <div class="opt">
                  <el-tooltip content="删除">
                    <div class="iconfont icon-del" @click="deleteNursingRecord(monitor.row)"></div>
                  </el-tooltip>
                </div>
              </template>
            </u-table-column>
          </u-table>
        </div>
      </base-layout>
      <el-dialog
        :title="dialogTitle"
        :visible.sync="showDialog"
        :close-on-click-modal="false"
        v-dialogDrag
        class="nursing-dialog"
        v-loading="dialogLoading"
        v-if="showDialog"
        element-loading-text="保存中……"
      >
        <station-department-bed-date
          v-if="showDialog"
          :switch="componentsSwitch"
          :stDeptBed="stationDepartmentBed"
          :enteredStationList="enteredStationList"
          @custCkick="getComponentData"
        />
        <hr />
        <div class="date-time">
          <div class="date-time-column">
            <span class="label">执行日期：</span>
            <el-date-picker
              v-model="nursingRecordform.performDate"
              :default-value="nursingRecordform.performDate"
              class="dialog-input"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
              style="width: 120px"
            ></el-date-picker>
          </div>
          <div class="date-time-column">
            <span class="label">执行时间：</span>
            <el-time-picker
              v-model="nursingRecordform.performTime"
              :default-value="nursingRecordform.performTime"
              class="dialog-input"
              value-format="HH:mm"
              format="HH:mm"
              placeholder="选择时间"
              style="width: 100px"
            ></el-time-picker>
          </div>

          <div class="date-time-column">
            <span class="label">执行人：</span>
            <user-selector
              v-model="nursingRecordform.performEmployeeID"
              :stationID="stationDepartmentBed.stationID"
              label=""
              clearable
              filterable
              remoteSearch
              :disabled="selectUserFlag"
              width="120px"
            ></user-selector>
          </div>
          <div class="date-time-column">
            <span class="label">带入时间：</span>
            <el-select v-model="bringTPRCommonTime" placeholder="请选择" style="width: 100px">
              <el-option
                v-for="(point, index) in points"
                :key="index"
                :label="point.value"
                :value="point.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div ref="formatTable">
          <el-table class="header-table" border>
            <el-table-column width="310" label="项目" align="center"></el-table-column>
            <el-table-column width="140" label="数值" align="center"></el-table-column>
            <el-table-column width="127" label="带入体温单" align="center"></el-table-column>
          </el-table>
          <el-table class="format-table" :data="formats" border :show-header="false">
            <el-table-column width="160" align="center">
              <template slot-scope="scope">
                {{ scope.row.description.trim() }}
              </template>
            </el-table-column>
            <el-table-column>
              <template slot-scope="scope">
                <div v-if="scope.row.child.length == 0" class="wrap">
                  <el-select
                    v-if="firstLevelOptions.includes(scope.row.key)"
                    v-model="scope.row.value"
                    class="input-item"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="(assess, index) in scope.row.details"
                      :key="index"
                      :label="assess.label"
                      :value="assess.label"
                    ></el-option>
                  </el-select>
                  <el-input
                    name="TN"
                    class="input-item"
                    v-else
                    v-model="scope.row.value"
                    @keyup.38.native.stop="enterEvent($event, false)"
                    @keyup.40.native.stop="enterEvent($event, true)"
                    @keyup.enter.native.stop="enterEvent($event, true)"
                  />
                  <div class="table-divider" />
                  <el-checkbox
                    :class="retestNursingRecordID ? 'retest' : ''"
                    v-if="scope.row.tprFlag"
                    v-model="scope.row.bringTPR"
                    @change="changeBringTPR(scope.row)"
                  />
                  <el-select
                    v-if="scope.row.tprFlag && !retestNursingRecordID"
                    v-model="scope.row.bringTPRTime"
                    :disabled="!scope.row.bringTPR"
                    placeholder="请选择"
                    style="width: 85px"
                  >
                    <el-option
                      v-for="(point, index) in points"
                      :key="index"
                      :label="point.value"
                      :value="point.value"
                    ></el-option>
                  </el-select>
                </div>
                <el-table v-else class="child-format-table" :data="scope.row.child" :show-header="false">
                  <el-table-column width="150" align="center">
                    <template slot-scope="childScope">
                      {{ childScope.row.description.trim() }}
                    </template>
                  </el-table-column>
                  <el-table-column width="140" align="center">
                    <template slot-scope="childScope">
                      <el-checkbox
                        class="checkbox-item"
                        v-if="childScope.row.key == 147"
                        v-model="childScope.row.value"
                        :checked="childScope.row.value == '起搏心率' ? true : false"
                      />
                      <el-checkbox
                        class="checkbox-item"
                        v-else-if="childScope.row.key == 306"
                        v-model="childScope.row.value"
                        :checked="childScope.row.value == '呼吸机辅助' ? true : false"
                      />
                      <el-select
                        v-else-if="secondLevelOptions.includes(childScope.row.key)"
                        v-model="childScope.row.value"
                        class="input-item"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="(assess, index) in childScope.row.details"
                          :key="index"
                          :label="assess.label"
                          :value="assess.label"
                        ></el-option>
                      </el-select>
                      <!-- 吸氧方式，中山使用下拉框，嘉会没有details使用输入框 -->
                      <el-select
                        v-else-if="childScope.row.key == 11 && childScope.row.details"
                        v-model="childScope.row.value"
                        class="input-item"
                        placeholder="请选择"
                        multiple
                        clearable
                      >
                        <el-option
                          v-for="(assess, index) in childScope.row.details"
                          :key="index"
                          :label="assess.label"
                          :value="assess.label"
                        ></el-option>
                      </el-select>
                      <el-input
                        v-else
                        name="TN"
                        class="child-input-item"
                        v-model="childScope.row.value"
                        @keyup.38.native.stop="enterEvent($event, false)"
                        @keyup.40.native.stop="enterEvent($event, true)"
                        @keyup.enter.native.stop="enterEvent($event, true)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column width="125" align="center">
                    <template slot-scope="childScope">
                      <el-checkbox
                        class="tpr-checked"
                        v-if="childScope.row.tprFlag"
                        v-model="childScope.row.bringTPR"
                        @change="changeBringTPR(childScope.row)"
                      />
                      <el-select
                        v-if="childScope.row.tprFlag && !retestNursingRecordID"
                        v-model="childScope.row.bringTPRTime"
                        :disabled="!childScope.row.bringTPR"
                        placeholder="请选择"
                        style="width: 85px"
                      >
                        <el-option
                          v-for="(point, index) in points"
                          :key="index"
                          :label="point.value"
                          :value="point.value"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="intervention-div">
          <div class="title">执行措施</div>
          <el-input type="textarea" resize="none" :rows="4" v-model="nursingRecordform.performIntervention"></el-input>
        </div>
        <div slot="footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button v-if="checkResult" type="primary" @click="save">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </base-layout>
</template>

<script>
import {
  SaveNursingRecord,
  DeleteNursingRecord,
  UpdateNursingRecord,
  GetNursingRecordInfosById,
  GetFormat,
  GetPatientData,
  GetRetestNursingRecord,
  ChangeBringToNR,
} from "@/api/NursingRecord";
import { GetVitalSignPoint, GetOneSettingByTypeAndCode, GetSettingOptionsByTypeCode } from "@/api/Setting";
import { GetSettingSwitchByTypeCodeAndTypeValue } from "@/api/SettingDescription";

import searchPatientData from "@/pages/recordSupplement/components/searchPatientData.vue";
import stationDepartmentBedDate from "@/pages/recordSupplement/components/stationDepartmentBedDate.vue";
import baseLayout from "@/components/BaseLayout";
import userSelector from "@/components/selector/userSelector";
import { GetBringToNursingRecordFlagSetting } from "@/api/SettingDescription";
import { GetStationList } from "@/api/Station";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    searchPatientData,
    stationDepartmentBedDate,
    userSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      readOnly: "getReadOnly",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  data() {
    return {
      selectUserFlag: false,
      loading: false,
      //筛选记录日期
      nursingRecordDate: "",
      //是否显示弹窗
      showDialog: false,
      //弹窗标题
      dialogTitle: "",
      //弹窗遮罩
      dialogLoading: false,
      //暂存病区科室及床位
      stationDepartmentBed: {
        stationID: undefined, //调整为undefined是因为如果默认值是空字符串的话在userSelector组件中传参时会默认转换为string类型报错
        departmentListID: "",
        bedNumber: "",
        bedId: "",
      },
      //弹窗输入内容
      nursingRecordform: {},
      //样式
      formats: [],
      //病人基本信息
      nursingRecordformView: {},
      //病人住院基本信息
      patient: undefined,
      //护理记录清单
      nursingRecordList: [],
      //更新注记
      updateFlag: "",
      //编辑栏位
      optColumn: {},
      //数据栏位
      dataColumns: [],
      //补资料历史数据
      supplyDatas: [],
      // 体温单时间点集合
      points: [],
      // 保存时判断有没有要保存的值
      hasValue: false,
      chartNo: undefined,
      // 复测的原记录ID
      retestNursingRecordID: undefined,
      // 标识修改记录是否为复测体温记录
      isRetest: false,
      checkResult: true,
      bringTPRCommonTime: undefined,
      inputs: [],
      //选择的护理记录单样式内容
      nursingRecordPdfTypes: [],
      //默认普通护理记录单
      nursingRecordPdfType: "999999",
      //标志：显示选择不同的护理记录单pdf样式
      showNursingRecordPdfTypeOptions: false,
      // 设置病区科室床位日期组件是否显示
      componentsSwitch: {
        stationSwitch: true,
        departmentListSwitch: true,
        bedNumberSwitch: true,
        dateTimeSwitch: false,
      },
      //一级标题需要下拉框模式的EMRFieldID
      firstLevelOptions: [37, 121, 142, 14, 36, 370, 35, 371, 372, 379, 380, 381, 404, 405],
      //二级标题需要下拉框模式的EMRFieldID
      secondLevelOptions: [
        14, 142, 328, 365, 377, 378, 393, 383, 384, 385, 386, 387, 388, 390, 391, 398, 399, 401, 402, 403, 407, 408,
        409, 410, 411,
      ],
      //新增时带入护理记录单标识
      addBringNursingRecordFlag: false,
      //新增默认病区
      defaultStationID: undefined,
      //新增默认床号
      defaultBedNumber: undefined,
      //新增默认床位ID
      defaultBedId: undefined,
      //新增默认科室
      defaultDepartmentListID: undefined,
      //患者经历病区
      enteredStationList: [],
    };
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  watch: {
    "patientInfo.inpatientID": {
      async handler(newIndex) {
        this.getNursingRecordSetting();
        this.getEnteredStationList();
        await this.selectPatientData(this.patientInfo);
      },
    },
  },
  created() {
    this.points = [];
    GetVitalSignPoint({ index: Math.random() }).then((result) => {
      if (this._common.isSuccess(result)) {
        if (result.data) {
          result.data.forEach((point) => {
            this.points.push({
              value: point.display + ":00",
            });
          });
        }
      }
    });
  },
  async mounted() {
    this.getSetting();
    this.getAddNursingRecordFlagSwitch();
    this.getNursingRecordSetting();
    this.getEnteredStationList();
    await this.selectPatientData(this.patientInfo);
  },
  methods: {
    /**
     * description: 获取配置（用来确定部分内容是否需要渲染在页面上）
     * return {*}
     */
    getSetting() {
      let param = {
        settingType: 169,
        settingCode: "SwitchForMaintainer",
      };
      GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.typeValue == "False") {
            this.selectUserFlag = false;
          } else {
            this.selectUserFlag = true;
          }
        }
      });
    },
    /**
     * description: 获取新增护理记录是否默认带入护理记录开关
     * param {*}
     * return {*}
     */
    getAddNursingRecordFlagSwitch() {
      let param = {
        settingTypeCode: "SupplyBringToNursingRecordFlag",
      };
      GetBringToNursingRecordFlagSetting(param).then((result) => {
        if (this._common.isSuccess(result)) {
          this.addBringNursingRecordFlag = result.data;
        }
      });
    },
    /**
     * description: 获取补录护理记录单相关配置
     * return {*}
     */
    getNursingRecordSetting() {
      //获取是否显示选择护理记录单类型的下拉框配置
      let params = {
        index: Math.random(),
        settingTypeCode: "ShowNursingRecordPdfTypeOptions",
        typeValue: this.patientInfo.departmentListID,
      };
      GetSettingSwitchByTypeCodeAndTypeValue(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.showNursingRecordPdfTypeOptions = result.data;
        }
      });
      //获取补录的护理记录单类型
      let optionsParams = {
        index: Math.random(),
        settingTypeCode: "NursingRecordTypeOptions",
        typeValue: this.patientInfo.departmentListID,
      };
      GetSettingOptionsByTypeCode(optionsParams).then((result) => {
        if (this._common.isSuccess(result)) {
          this.nursingRecordPdfTypes = result.data;
        }
      });
    },
    getClass(item) {
      if (item.isRetest) {
        return "is-retest";
      }
      if (item.bringTPRTime) {
        return "is-bring-tpr";
      }
      return "";
    },
    getTitle(item) {
      if (item.isRetest) {
        return "复测";
      }
      if (item.bringTPRTime) {
        return "对应体温单时间点：" + item.bringTPRTime;
      }
      return "";
    },
    changeBringTPR(row) {
      if (!row.bringTPR) {
        row.bringTPRTime = undefined;
      }
      //绑定带入体温单的时间（如果需要带入体温单） --zxz
      if (row.bringTPR && this.bringTPRCommonTime) {
        row.bringTPRTime = this.bringTPRCommonTime;
      }
      if (row.isRetest) {
        row.bringTPRTime = undefined;
        row.bringTPR = true;
      }
    },
    change() {
      this.formats = [];
      this.nursingRecordList = [];
      this.optColumn = {};
      this.dataColumns = [];
      this.supplyDatas = [];
    },
    async showRetest(val, isRetest) {
      if (this.hospitalInfo.hospitalID == 2 || this.hospitalInfo.hospitalID == 4) {
        if (isRetest == "1") {
          if (val.row.isRetest.value == "true") {
            this.getNursingRecordInfosById(val.row, "NursingRecord");
            this.isRetest = true;
            this.retestNursingRecordID = val.row.nursingRecordID.value;
            this.nursingRecordID = val.row.nursingRecordID.value;
            this.showEditDialog(val, true, "NursingRecord");
          } else {
            //修改复测
            this.isRetest = true;
            this.retestNursingRecordID = val.row.nursingRecordID.value;
            this.nursingRecordID = val.row.nursingRecordID.value;
            //新增复测
            this.showEditDialog(this.patient, false, "NursingRecord", val.row["-4"].value);
          }
        } else {
          this.showEditDialog(val, true);
        }
        return;
      }

      this.retestNursingRecordID = val.row.nursingRecordID.value;
      let remeasureTPRID = val.row.remeasureTPRID.value;
      // 点击修改按钮修改复测记录
      if (val.row.isRetest.value == "true") {
        this.isRetest = true;
        this.showEditDialog(val, true, "NursingRecord");
      } else {
        this.isRetest = false;
        if (remeasureTPRID && remeasureTPRID != 0) {
          var record = {};
          // 点击复测按钮修改复测记录
          let params = {
            inpatientID: this.patient.inpatientID,
            remeasureTPRID: remeasureTPRID,
          };
          // 获取复测记录对应的护理记录
          await GetRetestNursingRecord(params).then((result) => {
            if (this._common.isSuccess(result) && result.data) {
              record.row = result.data;
            }
          });
          !!record.row
            ? this.showEditDialog(record, true, "NursingRecord")
            : this.showEditDialog(this.patient, false, "NursingRecord");
        } else {
          // // 点击复测按钮新增复测记录
          this.showEditDialog(this.patient, false, "NursingRecord");
        }
      }
    },
    async showEditDialog(val = this.patient, flag, type, performDate) {
      this.checkResult = true;
      //打开弹窗的时候初始化带入体温单的时间点
      this.bringTPRCommonTime = undefined;
      if (val.row) {
        //是否仅本人操作
        this.checkResult = await this._common.checkActionAuthorization(this.user, val.row.performEmployeeID.value);
        if (!this.checkResult) {
          this._showTip("warning", "非本人不可操作");
          return;
        }
      }
      if (this.loading) {
        return;
      }
      this.updateFlag = flag;
      let systemID = "";
      let title = "";
      if (type) {
        systemID = type;
      } else {
        systemID = "EMR";
        this.retestNursingRecordID = undefined;
        this.isRetest = false;
      }
      if (systemID == "EMR") {
        title = "护理记录";
      } else {
        title = "复测体温";
      }

      this.showDialog = true;
      //表示新增操作，进行新增逻辑处理
      if (!this.updateFlag) {
        ({ disabledFlag: this.selectUserFlag, saveButtonFlag: this.checkResult } =
          await this._common.userSelectorDisabled(this.user.userID, true, true, ""));
        this.stationDepartmentBed.stationID = this.defaultStationID ?? val.stationID;
        this.stationDepartmentBed.departmentListID = this.defaultDepartmentListID ?? val.departmentListID;
        this.stationDepartmentBed.bedNumber = this.defaultBedNumber ?? val.bedNumber;
        this.stationDepartmentBed.bedId = this.defaultBedId ?? val.bedID;
        this.stationDepartmentBed.inpatientID = val.inpatientID;
        this.dialogTitle = "新增" + title;
        this.nursingRecordformView = val;
        this.nursingRecordform = {};
        this.nursingRecordform.performEmployeeID = this.user.userID;
        this.formats = [];
        let params = {
          systemID: systemID,
          departmentListID: val.departmentListID,
          age: this.patient.age,
          recordsCode: this.nursingRecordPdfType,
        };
        //统一修改请求请求参数
        params = this.setDepartmentListIDInRequestParam(params);
        GetFormat(params).then((result) => {
          if (this._common.isSuccess(result)) {
            if (result.data && this.retestNursingRecordID) {
              result.data.forEach((format) => {
                format.isRetest = true;
                format.bringTPR = true;
                if (!format.edit) {
                  format.child.forEach((child) => {
                    child.isRetest = true;
                    child.bringTPR = true;
                    if (child.value && child.key == 11) {
                      child.value = child.value.split(",");
                    }
                  });
                }
              });
            }
            this.formats = result.data;
            this.$set(
              this.nursingRecordform,
              "performDate",
              performDate != undefined ? performDate : this._datetimeUtil.getNowDate()
            );
            this.$set(this.nursingRecordform, "performTime", this._datetimeUtil.getNowTime());
            this.$nextTick(() => {
              this.getAllInputs();
            });
          }
        });
      } else {
        ({ disabledFlag: this.selectUserFlag, saveButtonFlag: this.checkResult } =
          await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            val.row.performEmployeeID.value ? val.row.performEmployeeID.value : this.user.userID
          ));
        this.stationDepartmentBed.stationID = Number(val.row.stationID.value);
        this.stationDepartmentBed.departmentListID = Number(val.row.departmentListID.value);
        this.stationDepartmentBed.bedNumber = val.row.bedNumber.value;
        this.stationDepartmentBed.bedId = Number(val.row.bedID.value);
        this.stationDepartmentBed.inpatientID = val.row.inpatientID.value;
        this.dialogTitle = "修改" + title;
        this.getNursingRecordInfosById(val.row, systemID);
      }
    },
    //查询病人
    async selectPatientData(val,nursingRecordDate) {
      if (!val) {
        this.nursingRecordList = [];
      } else {
        this.patient = val;
        this.getNursingRecordDate(nursingRecordDate);
        //获取需要显示的护理记录内容
        await this.getPatientData();
      }
    },

    //根据id获取护理记录单
    getNursingRecordInfosById(val, systemID) {
      this.formats = [];
      //解决数据不清除，渲染的时候，user-selector中会发送两次请求,
      this.nursingRecordform = {};
      let params = {
        nursingRecordID: val.nursingRecordID.value,
        departmentListID: val.departmentListID.value,
        systemID: systemID,
        age: this.patient.age,
        recordsCode: this.nursingRecordPdfType,
      };
      //统一修改请求请求参数
      params = this.setDepartmentListIDInRequestParam(params);
      GetNursingRecordInfosById(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.nursingRecordform = result.data;
          result.data.extentionDatas.forEach((format) => {
            if (format.bringTPRTime) {
              format.bringTPRTime = this._datetimeUtil.formatDate(format.bringTPRTime, "hh:mm");
            }
            if (!format.edit) {
              format.child.forEach((child) => {
                if (child.bringTPRTime) {
                  child.bringTPRTime = this._datetimeUtil.formatDate(child.bringTPRTime, "hh:mm");
                }
                //吸氧方式多选回显处理
                if (child.value && child.key == 11) {
                  child.value = child.value.split(",");
                }
              });
            }
          });
          this.formats = result.data.extentionDatas;
          if (!this.isRetest && !val.performEmployeeID.value) {
            this.nursingRecordform.performEmployeeID = this.user.userID;
            val.performEmployeeID.value = this.user.userID;
          }
          this.$nextTick(() => {
            this.getAllInputs();
          });
        }
      });
    },
    /**
     * description: 更改护理记录 带入护理记录单标记、通知医师标记
     * param {*} nursingRecordID
     * param {*} bringToNRflag 带入护理记录单标记
     * param {*} informPhysicianFlag 通知医师标记
     * return {*}
     */
    changeRecord(nursingRecordID, bringToNRflag, informPhysicianFlag) {
      let _this = this;
      let params = {
        nursingRecordID: nursingRecordID,
        informPhysicianFlag: informPhysicianFlag,
        bringToNRflag: bringToNRflag,
      };
      if (bringToNRflag || bringToNRflag == null) {
        params.bringToNRflag = bringToNRflag == "1" ? "0" : "1";
      }
      ChangeBringToNR(params).then((result) => {
        if (_this._common.isSuccess(result)) {
          _this._showTip("success", "修改成功！");
          _this.selectPatientData(_this.patient, this.nursingRecordDate);
        }
      });
    },
    //删除护理记录单
    async deleteNursingRecord(row) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, row.performEmployeeID.value);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            nursingRecordID: row.nursingRecordID.value,
          };
          DeleteNursingRecord(params).then((result) => {
            if (_this._common.isSuccess(result)) {
              _this._showTip("success", "删除成功！");
              _this.selectPatientData(_this.patient, this.nursingRecordDate);
            }
          });
        }
      });
    },
    // 检核数据有效性
    checkData() {
      this.hasValue = false;
      for (let i = 0; i < this.formats.length; i++) {
        let format = this.formats[i];
        if (!format.edit) {
          for (let j = 0; j < format.child.length; j++) {
            let child = format.child[j];
            if (child.value) {
              if (child.bringTPR) {
                if (!child.bringTPRTime && !child.isRetest) {
                  this._showTip("warning", child.description + "没选绑定时间点！");
                  return false;
                }
              }
              this.hasValue = true;
              return true;
            } else {
              if (child.bringTPR) {
                this._showTip("warning", child.description + "没有录入数值！");
                return false;
              }
            }
          }
        } else {
          if (format.value) {
            if (format.bringTPR) {
              if (!format.bringTPRTime && !format.isRetest) {
                this._showTip("warning", format.description + "没选绑定时间点！");
                return false;
              }
            }
            this.hasValue = true;
            return true;
          } else {
            if (format.bringTPR) {
              this._showTip("warning", format.description + "没有录入数值！");
              return false;
            }
          }
        }
      }
      return true;
    },
    /**
     * description: 更新护理记录单
     * return {*}
     */
    updateNursingRecord() {
      this.dialogLoading = true;
      this.nursingRecordform.stationID = this.stationDepartmentBed.stationID;
      this.nursingRecordform.departmentListID = this.stationDepartmentBed.departmentListID;
      this.nursingRecordform.bedNumber = this.stationDepartmentBed.bedNumber;
      this.nursingRecordform.retestNursingRecordID = this.retestNursingRecordID;
      this.nursingRecordform.isRetest = this.isRetest;
      this.nursingRecordform.recordsCode = this.nursingRecordPdfType;
      let params = this.nursingRecordform;
      //起搏心率呼吸机辅助保存
      this.formats.forEach((data) => {
        //危重单测温方式特殊处理
        if (data?.value && data.key == 142) {
          let sucDetail = data.details.find((m) => m.label == data.value);
          sucDetail && (data.key = sucDetail.value);
        }
        data.child.forEach((childItem) => {
          if (childItem.key == 147) {
            if (childItem.value == true) {
              childItem.value = "起搏心率";
            } else {
              childItem.value = "";
            }
          }
          if (childItem.key == 306) {
            if (childItem.value == true) {
              childItem.value = "呼吸机辅助";
            } else {
              childItem.value = "";
            }
          }
          //测温方式特殊处理
          if (childItem?.value && childItem.key == 142) {
            let sucDetail = childItem.details.find((m) => m.label == childItem.value);
            sucDetail && (childItem.key = sucDetail.value);
          }
          //吸氧方式多选处理
          if (childItem?.value && childItem.key == 11) {
            childItem.value = childItem.value.toString();
          }
        });
      });
      params.datas = this.formats;
      return UpdateNursingRecord(params).then((result) => {
        this.dialogLoading = false;
        this.showDialog = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.selectPatientData(this.patient,this.nursingRecordform.performDate);
          this.nursingRecordform = {};
          this.retestNursingRecordID = undefined;
          this.isRetest = false;
        }
      });
    },
    //保存护理记录单
    saveNursingRecord() {
      this.dialogLoading = true;
      this.nursingRecordform.caseNumber = this.nursingRecordformView.caseNumber;
      this.nursingRecordform.chartNo = this.nursingRecordformView.chartNo;
      this.nursingRecordform.inpatientID = this.nursingRecordformView.inpatientID;
      this.nursingRecordform.patientID = this.nursingRecordformView.patientID;
      this.nursingRecordform.stationID = this.stationDepartmentBed.stationID;
      this.nursingRecordform.departmentListID = this.stationDepartmentBed.departmentListID;
      this.nursingRecordform.bedNumber = this.stationDepartmentBed.bedNumber;
      this.nursingRecordform.bedId = this.stationDepartmentBed.bedId;
      this.nursingRecordform.retestNursingRecordID = this.retestNursingRecordID;
      this.nursingRecordform.isRetest = this.isRetest;
      this.nursingRecordform.recordsCode = this.nursingRecordPdfType;
      if (this.addBringNursingRecordFlag && !this.updateFlag) {
        this.nursingRecordform.BringToNursingRecords = "1";
      }
      let params = this.nursingRecordform;
      //起搏心率呼吸机辅助保存
      this.formats.forEach((data) => {
        //危重单测温方式特殊处理
        if (data?.value && data.key == 142) {
          let sucDetail = data.details.find((m) => m.label == data.value);
          sucDetail && (data.key = sucDetail.value);
        }
        data.child.forEach((childItem) => {
          if (childItem.key == 147) {
            if (childItem.value == true) {
              childItem.value = "起搏心率";
            } else {
              childItem.value = "";
            }
          }
          if (childItem.key == 306) {
            if (childItem.value == true) {
              childItem.value = "呼吸机辅助";
            } else {
              childItem.value = "";
            }
          }
          //测温方式特殊处理，便于体温单绘制
          if (childItem?.value && childItem.key == 142) {
            let sucDetail = childItem.details.find((m) => m.label == childItem.value);
            sucDetail && (childItem.key = sucDetail.value);
          }
          //吸氧方式多选处理
          if (childItem?.value && childItem.key == 11) {
            childItem.value = childItem.value.toString();
          }
        });
      });
      params.datas = this.formats;
      return SaveNursingRecord(params).then((result) => {
        this.dialogLoading = false;
        this.showDialog = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.selectPatientData(this.patient,this.nursingRecordform.performDate);
          this.nursingRecordform = {};
          this.retestNursingRecordID = undefined;
          this.isRetest = false;
        }
      });
    },
    save() {
      if (!this.nursingRecordform.performEmployeeID) {
        this._showTip("warning", "请选择护士！");
        return;
      }
      if (!this.checkData()) {
        return;
      } else {
        if (!this.hasValue && !this.nursingRecordform.performIntervention) {
          this._showTip("warning", "没有要保存的数据！");
          return;
        }
      }
      if (!this.updateFlag) {
        this.saveNursingRecord();
      } else {
        this.updateNursingRecord();
      }
    },
    /**
     * description: 获取需要显示的护理记录内容
     * return {*}
     */
    async getPatientData() {
      this.loading = true;
      let params = {
        caseNumber: this.patient.caseNumber,
        systemID: "EMR",
        departmentListID: this.patient.departmentListID,
        nursingRecordDate: this.nursingRecordDate,
        age: this.patient.age,
        recordsCode: this.nursingRecordPdfType,
      };
      //统一修改请求请求参数
      params = this.setDepartmentListIDInRequestParam(params);
      await GetPatientData(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data) {
            if (result.data.formats) {
              this.optColumn = result.data.formats[result.data.formats.length - 1];
              this.dataColumns = result.data.formats;
              // 删除最后一个
              this.dataColumns.pop();
            }
            this.supplyDatas = result.data.datas;
            // 数据渲染完，重置表格，防止固定类样式错乱，高度不一致
            this.$nextTick(() => {
              if (this.$refs.nursingRecordTable) {
                this.$refs.nursingRecordTable.doLayout();
              }
            });
          }
        }
        this.loading = false;
      });
    },
    //填入筛选记录时间  --GPC
    getNursingRecordDate(nursingRecordDate) {
      if (nursingRecordDate) {
        this.nursingRecordDate = nursingRecordDate;
        return;
      } 
      //判断病人出院与否
      this.nursingRecordDate = this.patient.dischargeDate
          ? this._datetimeUtil.formatDate(this.patient.dischargeDate, "yyyy-MM-dd")
      : this._datetimeUtil.getNowDate();
    },
    //拿到新增弹窗中所有的name为'TN'的所有input
    getAllInputs() {
      let tabsLayout = this.$refs.formatTable;
      this.inputs = tabsLayout.getElementsByTagName("input");
      let sucInputs = [];
      for (let index = 0; index < this.inputs.length; index++) {
        let input = this.inputs[index];
        if (input.name && input.name == "TN") {
          sucInputs.push(input);
        }
      }
      this.inputs = sucInputs;
    },
    //上下键切换输入框
    enterEvent(nowInput, flag) {
      for (let index = 0; index < this.inputs.length; index++) {
        let input = this.inputs[index];
        if (input == nowInput.target) {
          if (flag && index == this.inputs.length - 1) {
            this.inputs[0].focus();
            break;
          } else if (!flag && index == 0) {
            this.inputs[this.inputs.length - 1].focus();
            break;
          } else {
            input.blur();
            this.inputs[flag ? index + 1 : index - 1].focus();
          }
        }
      }
    },
    /**
     * description: 根据前端参数选择情况调整请求数据的参数
     * return {*} param
     * param {*} param
     */
    setDepartmentListIDInRequestParam(param) {
      //ICU 厦门重症护理记录单 在departmentToAssess中配置的科室为0
      if (this.nursingRecordPdfType == "102" && param["departmentListID"]) {
        param["departmentListID"] = 0;
      }
      return param;
    },
    /**
     * description: 回调更新对象成员
     * param {*} tempStDeptBed
     * return {*}
     */
    getComponentData(tempStDeptBed) {
      this.stationDepartmentBed.stationID = tempStDeptBed.stationID;
      this.stationDepartmentBed.departmentListID = tempStDeptBed.departmentListID;
      this.stationDepartmentBed.bedNumber = tempStDeptBed.bedNumber;
      this.stationDepartmentBed.bedId = tempStDeptBed.bedId;
    },
    /**
     * description: 获取患者经历病区列表
     * return {*}
     */
    getEnteredStationList() {
      let params = {
        inpatientID: this.patientInfo.inpatientID,
      };
      GetStationList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.enteredStationList = result.data;
          let filterData = result.data.find((station) => station.id == this.user.stationID);
          if (filterData) {
            this.defaultStationID = filterData.id || undefined;
            this.defaultDepartmentListID = filterData.departmentListID || undefined;
            this.defaultBedNumber = filterData.id != this.patient.stationID ? filterData.bedNumber : undefined;
            this.defaultBedId = filterData.id != this.patient.stationID ? filterData.bedID : undefined;
          }
        }
      });
    },
  },
};
</script>
<style lang="scss">
.base-layout.nursing-record-old {
  height: 100%;
  .base-header {
    padding: 0 0 10px 0;
  }
  .layout-wrap {
    height: 100%;
    .layout-top {
      padding: 0 10px;
      .data-select {
        width: 120px;
      }
      .tip {
        margin-left: 20px;
        color: #ff0000;
      }
      .top-btn {
        float: right;
      }
    }
    .supply-data {
      // .row-style .cell div {
      //   max-height: 46px;
      // }
      div {
        &.is-bring-tpr {
          color: $base-color;
        }
        &.is-retest {
          color: #0000ff;
        }
      }
      .opt {
        display: inline-block;
        margin: 0.5px 3px;
        width: 16px;
        &.read-only {
          .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
            background-color: $base-color;
            border-color: $base-color;
            &::after {
              border-color: #ffffff;
            }
          }
          .el-checkbox__input.is-disabled .el-checkbox__inner {
            background-color: #ffffff;
          }
        }
      }
    }
  }
  .nursing-dialog {
    .el-dialog {
      width: 625px !important;
      hr {
        margin-left: 17px;
        margin-right: 28px;
        margin-bottom: 15px;
      }
    }
    .date-time {
      height: 70px;
      line-height: 35px;
      margin: -5px 0 10px 0;

      .date-time-column {
        width: 49%;
        display: inline-block;
        .label {
          margin-left: 10px;
        }
      }
    }
    .el-table.header-table {
      border-bottom: 0;
      .el-table__body-wrapper {
        display: none;
      }
    }
    .el-table.format-table {
      .el-table__body {
        tr:hover > td {
          background-color: #ffffff;
        }
        td {
          padding: 0;
          .cell {
            padding: 0 !important;
            .wrap {
              display: flex;
              box-sizing: border-box;
              .table-divider {
                display: inline-block;
                height: 40px;
                width: 1px;
                background-color: #cccccc;
                margin-left: 5px;
                &.no-tpr {
                  margin-left: 124px;
                }
              }
              .el-checkbox {
                top: 10px;
                margin-left: 10px;
                &.retest {
                  margin-left: 55px;
                }
              }
              .input-item {
                width: 120px;
                margin: 4px 4px 4px 160px !important;
              }
              .el-select {
                margin: auto 5px;
              }
            }
            .el-table.child-format-table {
              border: 0;
              &::before {
                display: none;
              }
              tr {
                &:last-child {
                  td {
                    border-bottom: 0;
                  }
                }
                td {
                  padding: 3px;
                  &:last-child {
                    border-right: 0;
                  }
                  .cell {
                    padding: 1px 4px !important;
                    .child-input-item {
                      width: 120px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .intervention-div {
      border: 1px solid #cccccc;
      border-top: 0;
      width: 100%;
      box-sizing: border-box;
      display: flex;
      .title {
        height: 99px;
        line-height: 99px;
        width: 232px;
        text-align: center;
        margin: auto 0;
        border-right: 1px solid #cccccc;
      }
      .el-textarea {
        flex: auto;
        width: 100%;
        margin: 5px 10px;
        box-sizing: border-box;
      }
    }
  }
  .temperature-list-dialog {
    & .el-dialog__body {
      height: calc(100% - 35px);
    }
  }
}
</style>
