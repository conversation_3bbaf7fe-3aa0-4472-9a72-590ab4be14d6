/*
 * FilePath     : \ccc.web\src\api\Stoma.js
 * Author       : 郭鹏超
 * Date         : 2021-01-05 15:59
 * LastEditors  : 郭鹏超
 * LastEditTime : 2021-01-12 15:46
 * Description  :造口专项护理API
 */
import http from "@/utils/ajax";
import qs from "qs";
const baseUrl = "/Stoma";

export const urls = {
  GetStomaRecordList: baseUrl + "/GetStomaRecordList",
  SaveStomaRecord: baseUrl + "/SaveStomaRecord",
  DeleteStomaRecord: baseUrl + "/DeleteStomaRecord",
  GetStomaCareMainsByID: baseUrl + "/GetStomaCareMainsByID",
  SavePatientStomaCareMain: baseUrl + "/SavePatientStomaCareMain",
  StomaStorage: baseUrl + "/StomaStorage",
  DeleteStomaCareData: baseUrl + "/DeleteStomaCareData",
  GetStomaAssessView: baseUrl + "/GetStomaAssessView"
};
//获取造口记录数据
export const GetStomaRecordList = params => {
  return http.get(urls.GetStomaRecordList, params);
};
//新增或修改造口记录
export const SaveStomaRecord = params => {
  return http.post(urls.SaveStomaRecord, params);
};
//删除造口
export const DeleteStomaRecord = params => {
  return http.post(urls.DeleteStomaRecord, qs.stringify(params));
};
//获取造口维护主表数据
export const GetStomaCareMainsByID = params => {
  return http.get(urls.GetStomaCareMainsByID, params);
};
//造口维护保存
export const SavePatientStomaCareMain = params => {
  return http.post(urls.SavePatientStomaCareMain, params);
};
//造口收纳
export const StomaStorage = params => {
  return http.post(urls.StomaStorage, params);
};
//造口维护记录删除
export const DeleteStomaCareData = params => {
  return http.post(urls.DeleteStomaCareData, qs.stringify(params));
};
//获取评估模板数据
export const GetStomaAssessView = params => {
  return http.get(urls.GetStomaAssessView, params);
};
