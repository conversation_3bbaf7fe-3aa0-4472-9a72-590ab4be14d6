<!--
 * FilePath     : \ccc.web\src\pages\medicineToRecord\index.vue
 * Author       : 郭自飞
 * Date         : 2020-11-11 16:54
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-09-10 14:55
 * Description  : 给药排程带入护理记录单
-->
<template>
  <base-layout class="medicine-record">
    <div slot="header">
      <span>开始日期:</span>
      <el-date-picker
        @change="queryDate()"
        v-model="shiftDate"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        style="width: 150px"
        default-value
      ></el-date-picker>
      <shift-selector :stationID="stationID" @select-item="changeShift" v-model="shiftID"></shift-selector>
      <el-button class="bring-record" type="primary" icon="iconfont icon-bring-into" @click="addMedicineRecord">
        带入记录
      </el-button>
    </div>

    <el-tabs v-model="activeName">
      <el-tab-pane label="未带入" name="first">
        <el-table
          ref="multipleTable"
          tooltip-effect
          :data="noMedicineList"
          border
          stripe
          height="100%"
          @selection-change="foodSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center"></el-table-column>
          <el-table-column prop="patientName" label="病人姓名" width="100" align="center"></el-table-column>
          <el-table-column prop="localCaseNumber" label="住院号" width="100" align="center"></el-table-column>
          <el-table-column prop="bedNumber" label="床号" width="60" align="center"></el-table-column>
          <el-table-column
            prop="orderContent"
            label="药物"
            min-width="120"
            align="left"
            header-align="center"
          ></el-table-column>
          <el-table-column
            prop="orderDescription"
            label="医嘱说明"
            min-width="100"
            align="left"
            header-align="center"
          ></el-table-column>
          <el-table-column prop="frequency" label="频次" min-width="80" align="center"></el-table-column>
          <el-table-column
            prop="orderRule"
            label="服法/途径"
            min-width="80"
            align="left"
            header-align="center"
          ></el-table-column>
          <el-table-column label="执行时间" width="160" align="center">
            <template slot-scope="scope">
              <span
                v-formatTime="{
                  value: scope.row.performDate,
                  type: 'dateTime',
                }"
              ></span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="已带入" name="second">
        <el-table tooltip-effect :data="yesMedicineList" border stripe>
          <el-table-column prop="patientName" label="病人姓名" width="100" align="center"></el-table-column>
          <el-table-column prop="localCaseNumber" label="住院号" width="100" align="center"></el-table-column>
          <el-table-column prop="bedNumber" label="床号" width="60" align="center"></el-table-column>
          <el-table-column
            prop="orderContent"
            label="药物"
            min-width="120"
            align="left"
            header-align="center"
          ></el-table-column>
          <el-table-column
            prop="orderDescription"
            label="医嘱说明"
            min-width="100"
            align="left"
            header-align="center"
          ></el-table-column>
          <el-table-column prop="frequency" label="频次" min-width="80" align="center"></el-table-column>
          <el-table-column
            prop="orderRule"
            label="服法/途径"
            min-width="80"
            align="left"
            header-align="center"
          ></el-table-column>
          <el-table-column prop="bedNumber" label="执行时间" width="160" align="center">
            <template slot-scope="scope">
              <span
                v-formatTime="{
                  value: scope.row.performDate,
                  type: 'dateTime',
                }"
              ></span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="60" align="center">
            <template slot-scope="consultOperation">
              <div>
                <el-tooltip content="删除">
                  <i
                    class="iconfont icon-del"
                    @click="delectMedicine(consultOperation.row.patientMedicineScheduleID)"
                  ></i>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </base-layout>
</template>
<script>
import { GetMedicineTORecordList, DeleteMedicineRecord, AddMedicineRecord } from "@/api/MedicineSchedule";
import baseLayout from "@/components/BaseLayout";
import shiftSelector from "@/components/selector/shiftSelector";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    shiftSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  watch: {
    shiftID(newVal) {
      if (!newVal) return;
      this.queryDate();
    },
  },
  data() {
    return {
      shiftDate: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
      shiftID: undefined,
      stationID: undefined,
      activeName: "first",
      yesMedicineList: [],
      noMedicineList: [],
      defaultList: [],
      selectList: [],
    };
  },
  mounted() {
    if (this.user) {
      this.stationID = this.user.stationID;
    }
  },
  methods: {
    queryDate() {
      if (!this.shiftID) {
        return;
      }
      let prarms = {
        shiftDate: this.shiftDate,
        shiftID: this.shiftID,
      };
      this.yesMedicineList = [];
      this.noMedicineList = [];
      this.defaultList = [];
      GetMedicineTORecordList(prarms).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data) {
            res.data.forEach((element) => {
              if (element.bringToNursingRecords == "1") {
                this.yesMedicineList.push(element);
              } else {
                this.noMedicineList.push(element);
              }
              if (element.criticallyFlag) {
                this.defaultList.push(element);
              }
            });
          }
          this.toggleSelection(this.defaultList);
        }
      });
    },
    delectMedicine(id) {
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          // 确认删除
          let prams = {
            patientMedicineScheduleID: id,
          };
          DeleteMedicineRecord(prams).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "确认成功");
              this.queryDate();
            }
          });
        }
      });
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    /**
     * description: 选择变化数据
     * return {*}
     * param {*} val
     */
    foodSelectionChange(val) {
      this.selectList = val;
    },
    /**
     * description: 保存给药记录到护理记录单
     * return {*}
     */
    addMedicineRecord() {
      let ids = [];
      this.selectList.forEach((item) => {
        ids.push(item.patientMedicineScheduleID);
      });
      if (ids.length <= 0) {
        return;
      }
      AddMedicineRecord(ids).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.queryDate();
        }
      });
    },
    async changeShift(shift) {
      this.shiftID = shift?.id;
    },
  },
};
</script>
<style lang="scss">
.medicine-record {
  .bring-record {
    float: right;
    margin-top: 10px;
  }
}
</style>
