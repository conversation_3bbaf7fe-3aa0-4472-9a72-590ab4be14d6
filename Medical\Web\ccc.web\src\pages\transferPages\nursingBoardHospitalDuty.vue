<!--
 * FilePath     : \ccc.web\src\pages\transferPages\nursingBoardHospitalDuty.vue
 * Author       : 苏军志
 * Date         : 2022-04-24 16:54
 * LastEditors  : LX
 * LastEditTime : 2022-05-20 16:23
 * Description  : 串到护理看板院内值班维护画面
 * CodeIterationRecord: 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getNursingBoard } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      language: "getLanguage",
      hospitalInfo: "getHospitalInfo",
      user: "getUser",
    }),
  },
  created() {
    this.url =
      getNursingBoard() +
      "hospitalDutyMaintain?hospitalID=" +
      this.hospitalInfo.hospitalID +
      "&language=" +
      this.language +
      "&userID=" +
      this.user.userID;
  },
};
</script>