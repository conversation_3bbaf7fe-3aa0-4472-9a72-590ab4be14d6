/*
 * FilePath     : \src\api\NursingPlan.js
 * Author       : 苏军志
 * Date         : 2020-05-05 00:06
 * LastEditors  : 孟昭永
 * LastEditTime : 2022-06-09 15:03
 * Description  :
 */
//2019-09-05新护理计划查询服务
import http from "../utils/ajax";
const baseUrl = "/nursing_plan";

export const urls = {
  GetSuggestProblem: baseUrl + "/GetSuggestProblem",
  GetPatientNursingClusterView: baseUrl + "/GetPatientNursingClusterView",
  GetHistoryPaitentProblem: baseUrl + "/HistoryPatientProblem",
  GetRelatedFactor: baseUrl + "/GetNursingRelatedFactor",
  GetIntervention: baseUrl + "/GetNursingPlanIntervention",
  GetHistoryIntervention: baseUrl + "/HistoryIntervention",
  GetPaitentProblemToIntervention: baseUrl + "/GetPaitentProblemToIntervention",
  GetNeedFirstFrequency: baseUrl + "/GetNeedFirstFrequency",
  SavePlan: baseUrl + "/Save",
  GetCanSelectNursingOrder: baseUrl + "/GetCanSelectNursingOrder",
  GetNursingPlanProblem: baseUrl + "/GetNursingPlanProblem",
  SaveCluster: baseUrl + "/SaveCluster",
  GetPatientNursingClusterViewBySourceID:
    baseUrl + "/GetPatientNursingClusterViewBySourceID"
};

//2019-09-05获取建议的护理问题内容
export const GetSuggestProblem = params => {
  return http.get(urls.GetSuggestProblem, params);
};
//集束护理问题
export const GetPatientNursingClusterView = params => {
  return http.get(urls.GetPatientNursingClusterView, params);
};
//历史护理问题
export const GetHistoryPaitentProblem = params => {
  return http.get(urls.GetHistoryPaitentProblem, params);
};
// 获取相关因素
export const GetRelatedFactor = params => {
  return http.get(urls.GetRelatedFactor, params);
};
// 护理护理措施
export const GetIntervention = params => {
  return http.get(urls.GetIntervention, params);
};
// 历史护理护理措施
export const GetHistoryIntervention = params => {
  return http.get(urls.GetHistoryIntervention, params);
};
export const GetPaitentProblemToIntervention = params => {
  return http.get(urls.GetPaitentProblemToIntervention, params);
};
// 获取需要执行首次的频次
export const GetNeedFirstFrequency = params => {
  return http.get(urls.GetNeedFirstFrequency, params);
};
// 保存护理计划
export const SavePlan = params => {
  return http.post(urls.SavePlan, params);
};
// 获取可以添加的集束护理问题
export const GetCanSelectNursingOrder = params => {
  return http.get(urls.GetCanSelectNursingOrder, params);
};
//护理计划的问题清单
export const GetNursingPlanProblem = params => {
  return http.get(urls.GetNursingPlanProblem, params);
};
// 保存集束护理及计划
export const SaveCluster = params => {
  return http.post(urls.SaveCluster, params);
};
// 根据风险获取集束护理
export const GetPatientNursingClusterViewBySourceID = params => {
  return http.get(urls.GetPatientNursingClusterViewBySourceID, params);
};
