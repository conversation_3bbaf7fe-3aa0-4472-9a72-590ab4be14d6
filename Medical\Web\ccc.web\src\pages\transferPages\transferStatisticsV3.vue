<!--
 * FilePath     : \src\pages\transferPages\transferStatisticsV3.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-07-09 11:11
 * Description  : 串统计分析
-->
<template>
  <div class="transfer-statisticsV3">
    <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
  </div>
</template>
<script>
// 代码需要迁移，暂时串到nursing
import { getStatisticsV3Url } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
      language: "getLanguage",
      user: "getUser",
      stationList: "getStationList",
    }),
  },
  watch: {
    // 单页面多路由
    $route: function (to, from) {
      if (to.name == "statisticsV3" && to.path == from.path) {
        this.initPage(to.query);
      }
    },
  },
  created() {
    this.initPage(this.$route.query);
  },
  methods: {
    initPage(query) {
      let dimensionTypeApiID = query?.dimensionTypeApiID;
      let path = query?.path;
      let typeCode = query?.typeCode;
      if (!path) {
        this._showTip("warning", "跳转地址有误！");
      }
      // 跳转新的统计页面
      this.url =
        getStatisticsV3Url() +
        path +
        "?dimensionTypeApiID=" +
        dimensionTypeApiID +
        "&typeCode=" +
        typeCode +
        "&system=CCC" +
        "&stationIDs=" +
        this.getUserStationIDArr() +
        "&hospitalID=" +
        this.hospitalInfo.hospitalID +
        "&themeColor=" +
        encodeURIComponent("#1cc6a3") +
        "&stationID=" +
        this.user.stationID;
    },
    /**
     * @description: 获取用户权限病区 40以上开放所有病区权限 40一下开放有权限的病区
     * @return
     */
    getUserStationIDArr() {
      let userStationArr = [];
      if (this.user.roles.some((role) => role > 40)) {
        return JSON.stringify(userStationArr);
      }
      userStationArr = (this.stationList["station_" + this.user.userID] ?? []).map((station) => station.id);
      return JSON.stringify(userStationArr);
    },
  },
};
</script>
<style lang="scss">
.transfer-statisticsV3 {
  height: 100%;
  width: 100%;
  padding-left: 5px;
  box-sizing: border-box;
}
</style>
