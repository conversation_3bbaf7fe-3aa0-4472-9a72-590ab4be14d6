/*
 * FilePath     : \ccc.web\src\utils\regular.js
 * Author       : 郭鹏超
 * Date         : 2020-10-16 11:19
 * LastEditors  : 李艳奇
 * LastEditTime : 2021-03-25 16:23
 * Description  : 正则匹配
 */

import Vue from "vue";

//匹配正整数 
//val检核值 
//zeroFlag整数是否包含0
let regularNumber = (val, zeroFlag = true) => {
  if (!val || val == "") {
    return;
  }
  let reg;
  if (zeroFlag) {
    reg = /^\d+$/;
  } else {
    reg = /^[1-9]\d*$/;
  }
  return reg.test(val);
}
//匹配小数 默认为正数小数点后两位 
//val 检核值  
//positiveFlag是否为正数 true 为正负数小数 false 为正数小数
//number 为小数点后最多保留几位小数
let regularDecimals = (val, positiveFlag = true, number = 2,) => {
  if (!val || val == "") {
    return;
  }
  let reg;
  if (positiveFlag) {
    eval("reg = /^([0-9]|-[0-9])+([.]{1}[0-9]{1," + number + "})?$/")
  } else {
    eval("reg = /^[0-9]+([.]{1}[0-9]{1," + number + "})?$/")
  }
  return reg.test(val);
}
Vue.prototype._regularNumber = regularNumber;
Vue.prototype._regularDecimals = regularDecimals;
export default {
  regularNumber,
  regularDecimals
};