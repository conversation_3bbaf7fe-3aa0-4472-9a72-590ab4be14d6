/*
 * FilePath     : \src\api\Pumping.js
 * Author       : 郭鹏超
 * Date         : 2021-09-16 15:18
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:47
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/Pumping";

export const urls = {
  GetPumpingDrugType: baseUrl + "/GetPumpingDrugType",
  GetPumpingDrugFrequency: baseUrl + "/GetPumpingDrugFrequency",
  GetPumpingAddRecordTableView: baseUrl + "/GetPumpingAddRecordTableView",
  GetPumpingtableHeader: baseUrl + "/GetPumpingtableHeader",
  PumingRecordSave: baseUrl + "/PumingRecordSave",
  GetPumpingRecordTableData: baseUrl + "/GetPumpingRecordTableData",
  GetPumpingCareMainTableData: baseUrl + "/GetPumpingCareMainTableData",
  PumingCareMianSave: baseUrl + "/PumingCareMianSave",
  AddNowPumingCareMian: baseUrl + "/AddNowPumingCareMian",
  DeletePumpingRecord: baseUrl + "/DeletePumpingRecord",
  DeletePumpingCareMain: baseUrl + "/DeletePumpingCareMain",
  GetNewCareMain: baseUrl + "/GetNewCareMain",
  PumpingEndCheck: baseUrl + "/PumpingEndCheck"
};
//获取药物类别
export const GetPumpingDrugType = params => {
  return http.get(urls.GetPumpingDrugType, params);
};
//获取药物频次
export const GetPumpingDrugFrequency = params => {
  return http.get(urls.GetPumpingDrugFrequency, params);
};
//获取新增主记录药物表格数据
export const GetPumpingAddRecordTableView = params => {
  return http.get(urls.GetPumpingAddRecordTableView, params);
};
//获取表格表头
export const GetPumpingtableHeader = params => {
  return http.get(urls.GetPumpingtableHeader, params);
};
///主记录保存
export const PumingRecordSave = params => {
  return http.post(urls.PumingRecordSave, params);
};
//获取主记录表格数据
export const GetPumpingRecordTableData = params => {
  return http.get(urls.GetPumpingRecordTableData, params);
};

//获取维护记录表格数据
export const GetPumpingCareMainTableData = params => {
  return http.get(urls.GetPumpingCareMainTableData, params);
};
///维护记录保存
export const PumingCareMianSave = params => {
  return http.post(urls.PumingCareMianSave, params);
};
//新增维护记录
export const AddNowPumingCareMian = params => {
  return http.get(urls.AddNowPumingCareMian, params);
};
//删除主记录
export const DeletePumpingRecord = params => {
  return http.get(urls.DeletePumpingRecord, params);
};
//删除维护记录
export const DeletePumpingCareMain = params => {
  return http.get(urls.DeletePumpingCareMain, params);
};
//获取一条虚拟排程
export const GetNewCareMain = params => {
  return http.get(urls.GetNewCareMain, params);
};
//泵入停止检核
export const PumpingEndCheck = params => {
  return http.get(urls.PumpingEndCheck, params);
};
