.el-dialog {
  height: 80%;
  width: 80%;
  margin-top: 10vh !important;
  &.is-fullscreen {
    overflow: hidden;
    margin-top: 0 !important;
  }
  .el-dialog__header {
    width: 100%;
    height: 30px;
    background-color: $base-color;
    padding: 3px 10px;
    box-sizing: border-box;
    .el-dialog__title {
      display: inline-block;
      width: calc(100% - 20px);
      overflow: hidden;
      color: #fff;
      font-size: 18px;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: keep-all;
    }
    .el-dialog__headerbtn {
      top: 0;
      right: 0;
      padding: 6px;
      border-radius: 0;
      box-sizing: border-box;
      &:hover {
        background-color: #ff0000;
      }
      .el-dialog__close {
        color: #fff;
        font-weight: 600;
      }
    }
  }
  .el-dialog__body {
    padding: 10px !important;
    box-sizing: border-box;
    overflow: auto;
    height: calc(100% - 65px);
  }
  &.no-footer .el-dialog__body {
    height: calc(100% - 35px);
  }
  .el-dialog__footer {
    height: 30px;
    padding: 5px 15px;
    box-sizing: border-box;
  }
}