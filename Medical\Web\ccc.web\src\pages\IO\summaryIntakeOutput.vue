<!--
 * FilePath     : \src\pages\IO\summaryIntakeOutput.vue
 * Author       : 胡长攀
 * Date         : 2023-11-08 14:44
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-01-12 16:38
 * Description  : 科室出入量统计图表
 -->

<template>
  <base-layout class="summary-intake-output">
    <div slot="header">
      <span>开始日期:</span>
      <el-date-picker
        v-model="startDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        class="date-picker"
        :picker-options="pickerOptions1"
        placeholder="开始日期"
      ></el-date-picker>
      <span>结束日期:</span>
      <el-date-picker
        v-model="endDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        class="date-picker"
        :picker-options="pickerOptions2"
        placeholder="结束日期"
      ></el-date-picker>
      <el-button class="query-button" icon="iconfont icon-search" @click="searchData()">查询</el-button>
      <export-excel :exportExcelOption="exportExcelOption" class="export-button"></export-excel>
    </div>
    <el-table
      ref="summaryTableData"
      :data="summaryIODatas"
      :span-method="spanRow"
      :header-cell-style="setSummaryColumnStyle"
      v-loading="loading"
      header-align="center"
      height="100%"
      border
    >
      <el-table-column fixed="left" label="患者信息" min-width="155">
        <template slot-scope="scope">
          <span>床号:</span>
          {{ scope.row.bedNumber }}
          <br />
          <span>姓名:</span>
          {{ scope.row.patientName }}
          <br />
          <span>住院号:</span>
          {{ scope.row.chartNO }}
          <br />
          <span>入院日期:</span>
          {{ scope.row.admissionDate }}
          <br />
          <span>入院时间:</span>
          {{ scope.row.admissionTime }}
        </template>
      </el-table-column>
      <el-table-column prop="summaryDate" label="日期" min-width="90" align="center"></el-table-column>
      <el-table-column label="汇总" prop="summary" header-align="center">
        <el-table-column prop="summaryDuration" label="汇总时长" min-width="55" header-align="center" />
        <el-table-column prop="inputValue" label="总入量" min-width="60" align="right" />
        <el-table-column prop="outputValue" label="总出量" min-width="60" align="right" />
        <el-table-column prop="differenceValue" label="平衡" min-width="60" align="right" />
      </el-table-column>
      <el-table-column prop="input" label="入量" header-align="center">
        <template>
          <el-table-column
            v-for="(column, index) in inputColumns"
            :key="index"
            :label="column.value"
            :prop="column.key"
            min-width="55"
            resizable
            header-align="center"
            align="right"
          ></el-table-column>
        </template>
      </el-table-column>
      <el-table-column prop="" label="出量" header-align="center">
        <template>
          <el-table-column
            v-for="(column, index) in outputColumns"
            :key="index"
            :label="column.value"
            :prop="column.key"
            min-width="45"
            resizable
            header-align="center"
            align="right"
          ></el-table-column>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetSummaryIntakeOutputViews, GetSummaryIntakeOutputColumns } from "@/api/IO";
import { mapGetters } from "vuex";
import exportExcel from "@/components/file/exportExcel.vue";
export default {
  components: {
    baseLayout,
    exportExcel,
  },
  data() {
    return {
      //开始时间
      startDate: "",
      //结束时间
      endDate: "",
      //病区ID
      stationID: "",
      //表格行数据
      summaryIODatas: [],
      //表格行数据
      copySummaryIODatas: [],
      //表格数据动态列
      columns: [],
      //加载
      loading: false,
      //输入的动态列
      inputColumns: [],
      //输出的动态列
      outputColumns: [],
      //表格数据导出所需参数
      exportExcelOption: {
        tableData: [],
        columnData: {},
        mergrData: [],
        merges: [],
        sheetName: "",
        fileName: "出入量汇总统计表",
        buttonName: "生成Excel",
      },
      //表格合并变量
      spanArr: [],
      pos: 0,
      summaryColumnLabels: [],
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
    //开始时间不可大于结束时间
    pickerOptions1() {
      let that = this;
      return {
        disabledDate(time) {
          return that._datetimeUtil.getTimeDifference(time, that.endDate, "date", "D") < 0;
        },
      };
    },
    //结束时间不可早于开始时间
    pickerOptions2() {
      let that = this;
      return {
        disabledDate(time) {
          return that._datetimeUtil.getTimeDifference(time, that.startDate, "date", "D") > 0;
        },
      };
    },
  },
  async mounted() {
    this.startDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
    this.endDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
    this.stationID = this.user.stationID;
    await this.getColumns();
  },
  watch: {
    startDate: {
      deep: true,
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.startDate = newValue;
        }
      },
    },
    endDate: {
      deep: true,
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.endDate = newValue;
        }
      },
    },
  },
  methods: {
    /**
     * @description: 查询io数据
     * @return
     */
    searchData() {
      let parms = {
        startDate: this.startDate,
        endDate: this.endDate,
        stationID: this.stationID,
      };
      this.loading = true;
      GetSummaryIntakeOutputViews(parms).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.summaryIODatas = res.data;
          this.getSpanArr(res.data);
          this.copySummaryIODatas = res.data;
          this.$nextTick(() => {
            //解决前端样式错乱问题
            this.$refs?.summaryTableData.doLayout();
          });
          //获取数据后，生成Excel所需要的相关数据
          this.exportExcel();
        }
      });
    },
    /**
     * @description: 获取标题列
     * @return
     */
    async getColumns() {
      await GetSummaryIntakeOutputColumns().then((res) => {
        if (this._common.isSuccess(res)) {
          this.columns = res.data;
          //输出列
          this.outputColumns = res.data.filter((item) => item.type == "output");
          //输入列
          this.inputColumns = res.data.filter((item) => item.type == "input");
          this.setTitle();
        }
      });
    },
    /**
     * @description: 设置汇总列头样式
     * @param column
     * @return
     */
    setSummaryColumnStyle({ column }) {
      if (column.property === "summary") {
        if (!this.summaryColumnLabels.length) {
          column.children.forEach((item) => {
            this.summaryColumnLabels.push(item.label);
          });
        }
        return { background: "#d7f499 !important" };
      }
      if (this.summaryColumnLabels.includes(column.label)) {
        return { background: "#d7f499 !important" };
      }
      return {};
    },
    //表格合并
    getSpanArr(data) {
      this.spanArr = [];
      if (!data) {
        return;
      }
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].chartNO == data[i - 1].chartNO) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    /**
     * @description: 行列合并
     * @return
     * @param row
     * @param column
     * @param rowIndex
     * @param columnIndex
     */
    spanRow({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        let _row = this.spanArr[rowIndex];
        let _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },

    /**
     * @description: 设置导出表格标题
     * @return
     */
    setTitle() {
      this.exportExcelOption.columnData = [];
      this.exportExcelOption.mergrData = [];
      //Key添加下标index_   解决Object自动排序导致列错乱问题
      this.exportExcelOption.columnData["1_bedNumber"] = "床号";
      this.exportExcelOption.columnData["2_patientName"] = "姓名";
      this.exportExcelOption.columnData["1_chartNO"] = "住院号";
      this.exportExcelOption.columnData["4_admissionDate"] = "入院日期";
      this.exportExcelOption.columnData["4_admissionTime"] = "入院时间";
      this.exportExcelOption.columnData["5_summaryDate"] = "日期";
      this.exportExcelOption.columnData["6_summaryDuration"] = "汇总时长";
      this.exportExcelOption.columnData["7_inputValue"] = "总入量";
      this.exportExcelOption.columnData["8_outputValue"] = "总出量";
      this.exportExcelOption.columnData["9_differenceValue"] = "平衡";
      this.columns.forEach((column, index) => {
        this.exportExcelOption.columnData[index + 10 + "_" + column.key] = column.value;
      });
      this.exportExcelOption.mergrData = ["基本信息", "", "", "", "", "日期", "汇总时长", "总入量", "总出量", "平衡"];
      for (let index = 0; index < this.inputColumns.length - 1; index++) {
        if (index == 0) {
          this.exportExcelOption.mergrData.push("入量");
        }
        this.exportExcelOption.mergrData.push("");
      }
      for (let index = 0; index < this.outputColumns.length - 1; index++) {
        if (index == 0) {
          this.exportExcelOption.mergrData.push("出量");
        }
        this.exportExcelOption.mergrData.push("");
      }
    },

    /**
     * @description: 设置表格数据及需要合并的行
     * @return
     */
    exportExcel() {
      this.exportExcelOption.merges = [];
      this.exportExcelOption.tableData = this.summaryIODatas;
      this.exportExcelOption.sheetName = this.startDate + "--" + this.endDate;
      if (!this.summaryIODatas) {
        return;
      }
      //设置合并的行和列
      for (let index = 0; index <= 8; index++) {
        //基本信息的标题行无需合并，及前5列
        if (index >= 5) {
          //标题前两行需要合并的行
          this.exportExcelOption.merges.push({ s: { c: index, r: 0 }, e: { c: index, r: 1 } });
        }
      }
      //标题前两行需要合并的列
      //基本信息合并列
      this.exportExcelOption.merges.push({
        s: { c: 0, r: 0 },
        e: { c: 4, r: 0 },
      });
      this.exportExcelOption.merges.push({
        s: { c: 10, r: 0 },
        e: { c: Number(10) + Number(this.inputColumns.length - 1), r: 0 },
      });
      this.exportExcelOption.merges.push({
        s: { c: 10 + this.inputColumns.length, r: 0 },
        e: { c: 10 + this.inputColumns.length + this.outputColumns.length - 1, r: 0 },
      });
      this.copySummaryIODatas = this._common.clone(this.summaryIODatas);
      //设置数据行合并
      for (let index1 = 0; index1 < this.summaryIODatas.length; index1++) {
        let row = this.summaryIODatas[index1];
        let patientRow = this.copySummaryIODatas.filter((m) => {
          return m.chartNO == row.chartNO;
        });
        if (patientRow.length > 0) {
          this.copySummaryIODatas.splice(0, patientRow.length);
          //列下标从0开始，前5列需合并行
          for (let index2 = 0; index2 <= 4; index2++) {
            //标题占用两行，开始下标是从0开始计算，所以此时数据的行应该从2开始
            this.exportExcelOption.merges.push({
              s: { c: index2, r: index1 + 2 },
              e: { c: index2, r: index1 + 2 + patientRow.length - 1 },
            });
          }
        }
      }
    },
  },
};
</script>

<style lang="scss">
.summary-intake-output {
  .date-picker {
    width: 120px;
  }
  .export-button {
    float: right;
  }
}
</style>
