/*
 * FilePath     : \ccc.web\src\api\PatientEvent.js
 * Author       : 郭鹏超
 * Date         : 2020-06-27 20:02
 * LastEditors  : 杨欣欣
 * LastEditTime : 2022-09-28 11:20
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/PatientEvent";
import qs from "qs";

export const urls = {
  GetEventList: baseUrl + "/GetEventList",
  GetPatientEventList: baseUrl + "/GetPatientEventList",
  SaveEvent: baseUrl + "/SaveEvent",
  DeleteEvent: baseUrl + "/DeleteEvent",
  GetEventAssessView: baseUrl + "/GetEventAssessView",
  GetEventDate: baseUrl + "/GetEventDate"
};

// 获病人事件的列表
export const GetEventList = params => {
  return http.get(urls.GetEventList, params);
};
// 获病人已有的事件列表
export const GetPatientEventList = params => {
  return http.get(urls.GetPatientEventList, params);
};
// 保存病人事件
export const SaveEvent = params => {
  return http.post(urls.SaveEvent, params);
};
// 删除病人事件
export const DeleteEvent = params => {
  return http.post(urls.DeleteEvent, qs.stringify(params));
};
// 获取患者事件详情
export const GetEventAssessView = params => {
  return http.get(urls.GetEventAssessView, params);
};
//获取某个患者事件时间
export const GetEventDate = params => {
  return http.get(urls.GetEventDate, params);
};
