<!--
 * FilePath     : \src\components\FileUpload.vue
 * Author       : 苏军志
 * Date         : 2022-09-05 10:31
 * LastEditors  : 苏军志
 * LastEditTime : 2022-09-06 19:06
 * Description  : 文件上传组件
 * CodeIterationRecord: 
-->
<template>
  <div class="file-upload">
    <el-popover placement="bottom" width="360" trigger="click">
      <div slot="reference">
        <slot name="trigger">
          <el-button type="primary" size="mini" class="iconfont icon-upload upload-button">上传文件</el-button>
        </slot>
      </div>
      <el-upload
        class="upload-file"
        ref="upload"
        drag
        multiple
        accept=".pdf,.mp3,.mp4"
        :file-list="fileList"
        :action="uploadUrl"
        :auto-upload="false"
        :on-remove="removeFileList"
        :on-change="changeFileList"
      >
        <i class="iconfont icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">只能上传.pdf、.mp3、.mp4文件</div>
      </el-upload>
    </el-popover>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      default: function () {
        return [];
      },
    },
    // 上传弹窗位置
    // 可传值：top/top-start/top-end/bottom/bottom-start/bottom-end/left/left-start/left-end/right/right-start/right-end
    placement: {
      default: "bottom",
    },
    uploadUrl: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      fileList: [],
    };
  },
  created() {
    this.fileList = this.value;
  },
  methods: {
    /**
     * description: 文件移除时触发
     * param {*} file 当前文件
     * param {*} fileList 文件列表
     * return {*}
     */
    removeFileList(file, fileList) {
      this.$emit("input", fileList);
    },
    /**
     * description: 文件异动时触发
     * param {*} file 当前文件
     * param {*} fileList 文件列表
     * return {*}
     */
    changeFileList(file, fileList) {
      // 删除重复上传文件
      for (let i = 0; i < fileList.length - 1; i += 1) {
        for (let j = 0; j < fileList.length; j += 1) {
          if (fileList[i].name === fileList[j].name && i != j) {
            fileList.splice(i, 1);
            break;
          }
        }
      }
      this.fileList = fileList;
      this.$emit("input", this.fileList);
    },
  },
};
</script>

<style lang="scss">
.file-upload {
  margin: 0 5px;
}
.upload-file {
  .el-upload-dragger {
    border-color: $base-color;
    .iconfont.icon-upload {
      font-size: 50px;
      margin-top: 30px;
    }
  }
  .el-upload__tip {
    color: #ff0000;
  }
  .el-upload-list {
    .el-icon-close {
      color: #ff0000;
      font-weight: bold;
    }
  }
}
</style>