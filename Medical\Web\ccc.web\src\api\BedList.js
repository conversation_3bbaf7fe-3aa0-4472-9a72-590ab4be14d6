/*
 * FilePath     : \ccc.web\src\api\BedList.js
 * Author       : 祝仕奇
 * Date         : 2021-11-07 18:13
 * LastEditors  : 杨欣欣
 * LastEditTime : 2022-09-30 11:07
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/bed";

export const urls = {
  // 根据病区获取床位集合
  GetBedListByStationId: baseUrl + "/GetBedListByStationId",
  SaveAdditionFlag: baseUrl + "/SaveAdditionFlag",
  // 根据病区获取床位集合（后端功能和GetBedListByStationId一样，只是返回数据格式不一样）
  GetDict: baseUrl + "/GetDict",
  GetBedListDict: baseUrl + "/GetBedListDict",
  GetBedList: baseUrl + "/GetBedList"
};

export const GetBedListByStationId = params => {
  return http.get(urls.GetBedListByStationId, params);
};

export const SaveAdditionFlag = params => {
  return http.post(urls.SaveAdditionFlag, qs.stringify(params));
};

export const GetDict = params => {
  return http.get(urls.GetDict, params);
};

export const GetBedListDict = params => {
  return http.get(urls.GetBedListDict, params);
};

export const GetBedList = params => {
  return http.get(urls.GetBedList, params);
};
