/*
 * FilePath     : \src\router\modules\singlePatientBatchExecution.js
 * Author       : 苏军志
 * Date         : 2022-09-19 17:43
 * LastEditors  : 来江禹
 * LastEditTime : 2025-03-22 09:24
 * Description  : 路由分包--单病人批量执行画面
 * CodeIterationRecord:
 */

import pages from "../pages";
import autoPages from "../autoPages";
export default {
  path: "/singlePatientBatchExecution",
  name: "singlePatientBatchExecution",
  component: autoPages.singlePatientBatchExecution,
  meta: {
    auth: true,
    isParent: true
  },
  children: [
    {
      path: "/batchExecutionSchedule",
      name: "batchExecutionSchedule",
      component: autoPages.batchExecutionSchedule,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-patientObserve",
      name: "batchTabs-patientObserve",
      component: pages.patientObserve,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-wound",
      name: "batchTabs-wound",
      component: autoPages.wound,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-io",
      name: "batchTabs-io",
      component: pages.ioRecordMaintenance,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-tube",
      name: "batchTabs-tube",
      component: pages.tube,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-glucose",
      name: "batchTabs-glucose",
      component: pages.glucose,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-rescueRecord",
      name: "batchTabs-rescueRecord",
      component: autoPages.rescueRecord,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-restraint",
      name: "batchTabs-restraint",
      component: autoPages.restraint,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-patientPain",
      name: "batchTabs-patientPain",
      component: autoPages.patientPain,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-peripheralCirculation",
      name: "batchTabs-peripheralCirculation",
      component: autoPages.peripheralCirculation,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-patientStomaRecord",
      name: "batchTabs-patientStomaRecord",
      component: autoPages.patientStomaRecord,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-pumping",
      name: "batchTabs-pumping",
      component: pages.pumping,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-patientThrombolysis",
      name: "batchTabs-patientThrombolysis",
      component: pages.patientThrombolysis,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-bloodTransfusionRecord",
      name: "batchTabs-bloodTransfusionRecord",
      component: pages.patientTransfusion,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-patientDietIntake",
      name: "batchTabs-patientDietIntake",
      component: pages.patientDietIntake,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-patientDelirium",
      name: "batchTabs-patientDelirium",
      component: pages.patientDelirium,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-neurovascularAssess",
      name: "batchTabs-neurovascularAssess",
      component: pages.patientNeurovascular,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    },
    {
      path: "/batchTabs-cRRTRecord",
      name: "batchTabs-cRRTRecord",
      component: autoPages.cRRTRecord,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    }
    ,
    {
      path: "/vital-sign",
      name: "vital-sign",
      component: pages.vitalSign,
      meta: {
        auth: true,
        parentPath: "/singlePatientBatchExecution"
      }
    }
  ]
};
