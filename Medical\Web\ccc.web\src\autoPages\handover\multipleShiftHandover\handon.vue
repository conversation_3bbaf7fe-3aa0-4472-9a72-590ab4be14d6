<!--
 * FilePath     : \ccc.web\src\autoPages\handover\multipleShiftHandover\handon.vue
 * Author       : 郭鹏超
 * Date         : 2023-05-05 11:19
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-05-19 21:42
 * Description  : 批量接班
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="multiple-handon">
    <div slot="header">
      <el-radio-group v-model="recordsCodeIndex">
        <el-radio :label="0">班别接班</el-radio>
        <el-radio :label="1">班内接班</el-radio>
      </el-radio-group>
    </div>
    <div class="multiple-handon-content">
      <component v-if="handoverTypeArr[recordsCodeIndex]" :is="handoverTypeArr[recordsCodeIndex]"></component>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import shiftHandon from "./components/shiftHandon";
import inShiftHandon from "./components/inShiftHandon";
export default {
  components: {
    baseLayout,
    shiftHandon,
    inShiftHandon,
  },
  data() {
    return {
      recordsCodeIndex: 0,
      handoverTypeArr: ["shiftHandon", "inShiftHandon"],
    };
  },
};
</script>

<style lang="scss" >
.multiple-handon {
  height: 100%;
  .multiple-handon-content {
    height: 100%;
  }
}
//message组件挂载至body  目前没找到对应属性开关
.multi-handon-msgbox-class {
  border: 0;
  .el-message-box__header {
    background-color: $base-color;
    color: #ffffff;
  }
  .el-message-box__title,
  .el-icon-close:before {
    color: #ffffff;
  }
}
</style>