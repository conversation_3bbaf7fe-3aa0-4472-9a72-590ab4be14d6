<!--
 * FilePath     : \src\pages\verify\index.vue
 * Author       : <PERSON><PERSON>uanLee
 * Date         : 2021-10-18 14:55
 * LastEditors  : 孟昭永
 * LastEditTime : 2023-03-21 09:32
 * Description  : 
 * CodeIterationRecord: 
 2022-05-25 2579-作为质控管理人员，我需要审核评量表，以利病历质控  zxz
 2022-06-08 2659 病历审查加不同审核内容的标识列，以便快速识别待审核内容 -En
 2022-06-15 bug审核后调整护理评估审核显示大号字体，页面左右边宽度调整 zxz
-->
<template>
  <base-layout class="verify-records" v-loading="loading" element-loading-text="审核中……">
    <div class="verify-condition" slot="header">
      <span>病区：</span>
      <station-selector v-model="stationID" label="" width="150"></station-selector>
      <span>病历：</span>
      <el-select placeholder="请选择要审核的病历类型" v-model="recordKind" style="width: 15%">
        <el-option
          v-for="item in recordKindList"
          :key="item.key"
          :label="item.label"
          :value="item.value"
          @click.native="changeRecordKind(item)"
        ></el-option>
      </el-select>
      <span>住院号：</span>
      <el-input v-model="localCaseNumber" placeholder="住院号" class="search-input" clearable>
        <i slot="append" class="iconfont icon-search" @click="selectVerifyRecord"></i>
      </el-input>
      <span>未审核记录：</span>
      <el-switch
        @change="selectVerifyRecord()"
        :active-value="0"
        :inactive-value="1"
        v-model="showCondition"
        class="switch-show"
      />
      <el-button
        v-if="showCondition == '0'"
        class="verify-button"
        type="primary"
        @click="verifyRecords"
        icon="iconfont icon-save-button"
      >
        审核
      </el-button>
    </div>
    <div class="verify-content">
      <el-tabs class="tabs" v-model="chooseRecord" @tab-click="selectVerifyRecord">
        <el-tab-pane
          v-for="record in eMRList"
          :label="record.fileClassName"
          :name="String(record.fileClassID)"
          :key="record.emrListID"
        ></el-tab-pane>
        <div class="record-list">
          <el-table
            :data="verifyRecordList"
            class="table"
            ref="verifyTable"
            @row-click="selectRecord"
            @selection-change="chooseVerify"
          >
            <el-table-column type="selection" prop="select" :width="convertPX(40)" align="center"></el-table-column>
            <el-table-column
              sortable
              prop="localCaseNumber"
              label="住院号"
              min-width="75"
              align="center"
            ></el-table-column>
            <el-table-column sortable prop="bedNumber" label="床号" min-width="60" align="center"></el-table-column>
            <el-table-column sortable prop="patientName" label="姓名" min-width="60" align="center"></el-table-column>
            <el-table-column prop="gender" label="性别" min-width="60" align="center"></el-table-column>
            <el-table-column prop="ageDetail" label="年龄" min-width="55" align="center"></el-table-column>
            <el-table-column
              prop="recordItem"
              label="病历项目"
              min-width="200"
              align="center"
              v-if="haveRecordItem()"
            ></el-table-column>
            <template v-if="recordKind !== 'EducationSheet'">
              <el-table-column
                v-if="recordKind === 'Record' && chooseRecord == '1'"
                sortable
                align="center"
                prop="writeTime"
                label="班别日期"
                width="105"
              >
                <template slot-scope="scope">
                  <span v-formatTime="{ value: scope.row.writeTime, type: 'date' }"></span>
                </template>
              </el-table-column>
              <el-table-column v-else sortable align="center" prop="writeTime" label="评估时间" width="105">
                <template slot-scope="scope">
                  <span v-formatTime="{ value: scope.row.writeTime, type: 'dateTime' }"></span>
                </template>
              </el-table-column>
              <el-table-column
                v-if="recordKind === 'Record' && chooseRecord == '1'"
                prop="shiftName"
                label="班别"
                align="center"
              ></el-table-column>
              <el-table-column
                v-if="recordKind === 'Record'"
                prop="writeEmployeeID"
                label="主责"
                align="center"
              ></el-table-column>
              <el-table-column v-else prop="writeEmployeeID" label="评估人" align="center"></el-table-column>
              <el-table-column v-if="recordKind === 'Scale'" sortable prop="content" label="评估结果" min-width="105">
                <template slot-scope="scope">
                  <span v-if="scope.row.riskLevel >= 50" class="height-risk">{{ scope.row.content }}</span>
                  <span v-else>{{ scope.row.content }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-if="recordKind === 'Scale'"
                prop="point"
                label="分數"
                min-width="60"
                align="center"
              ></el-table-column>
              <el-table-column prop="status" label="审核状态" min-width="100" align="center"></el-table-column>
              <el-table-column prop="verifyNurse" label="审核人" min-width="100" align="center"></el-table-column>
            </template>
          </el-table>
          <!-- fileclassid = 5是评估单，使用大字体显示审核明细 -->
          <div :class="{ 'record-detail': true, 'big-font': chooseRecord == 5 }" v-html="verifyContent"></div>
        </div>
      </el-tabs>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import { mapGetters } from "vuex";
import {
  GetVerifyList,
  GetVerifyLog,
  VerifyRecords,
  GetEMRScoreList,
  GetRecordDetail,
  GetEMRCategory,
  GetCommonEMRListView,
} from "@/api/VerifyRecord";
export default {
  components: {
    baseLayout,
    stationSelector,
  },
  data() {
    return {
      //页签
      tabkey: 1,
      //选中的病区
      stationID: undefined,
      //显示未审核记录
      showCondition: 0,
      //病区清单
      stationList: [],
      //电子病历清单
      eMRList: [],
      //待审核病历清单
      verifyRecordList: [],
      //在院唯一号
      localCaseNumber: "",
      //选中的页签
      chooseRecord: undefined,
      //选择的病历类型
      recordKind: "",
      //病历类型清单
      recordKindList: [],
      //病历内容
      verifyContent: "",
      //调用的服务
      verifyAPI: "",
      //选择要审核的病历
      chooseVerifyList: [],
      //审核中
      loading: false,
    };
  },
  created() {
    this.init();
    this.stationID = this.user.stationID;
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  methods: {
    async init() {
      this.loading = true;
      let params = {
        settingTypeCode: "EMRCategory",
      };
      await GetEMRCategory(params).then((result) => {
        if (this._common.isSuccess(result)) {
          result.data.forEach((item) => {
            this.recordKindList.push({
              key: item.key,
              value: item.value,
              label: item.label,
            });
          });
          this.loading = false;
        }
      });
    },
    /**
     * description: 改变病历类别-获取对应的病历集合
     * return {*}void
     * param {*} val：选中的病历类别
     */
    async changeRecordKind(val) {
      //风险量表和评估量表
      if (val.value == "Scale" || val.value == "EvaluationScale") {
        let params = val.value == "EvaluationScale" ? { evaluationScaleFlag: true } : {};
        //取得页签清单
        await GetEMRScoreList(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.eMRList = result.data;
            if (this.eMRList.length >= 0) {
              this.chooseRecord = String(this.eMRList[0].fileClassID);
              this.selectVerifyRecord();
            }
          }
        });
      }
      if (val.value == "AssessForm" || val.value == "Plan" || val.value == "Record" || val.value == "EducationSheet") {
        let params = {
          eMRCategory: val.value,
        };
        await GetCommonEMRListView(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.eMRList = result.data;
            if (this.eMRList.length >= 0) {
              this.chooseRecord = String(this.eMRList[0].fileClassID);
              this.selectVerifyRecord();
            }
          }
        });
      }
    },
    /**
     * description: 下拉框选择类别-点击触发的方法
     * return {*}
     */
    async selectVerifyRecord() {
      this.verifyRecordList = [];
      this.verifyAPI = "";
      this.verifyContent = "";
      //判断是否指显示未审核记录
      let status = "10";
      if (this.showCondition) {
        status = "30";
      }
      //透过选择到的标签寻找对应的API
      this.eMRList.forEach((item) => {
        if (String(item.fileClassID) == this.chooseRecord) {
          this.verifyAPI = item.verifyAPI;
        }
      });
      let params = {
        stationID: this.stationID,
        status: status,
        emrListID: this.chooseRecord,
        api: this.verifyAPI,
        localCaseNumber: this.localCaseNumber,
      };
      this.loading = true;
      await GetVerifyList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.verifyRecordList = result.data;
          if (result.data == null) {
            return;
          }
          //已审核过病历加上勾选
          this.$nextTick(function () {
            this.verifyRecordList.forEach((item) => {
              if (item.select) {
                this.$refs.verifyTable.toggleRowSelection(item, true);
              }
            });
            //默许呈现第一笔数据
            if (status == "30" && this.verifyRecordList.length > 0) {
              let id = {
                verifyRecordLogID: this.verifyRecordList[0].verifyRecordLogID,
              };
              GetVerifyLog(id).then((result) => {
                if (this._common.isSuccess(result)) {
                  this.verifyContent = result.data;
                  this.verifyRecordList[0].verifyContent = result.data;
                }
              });
            }
            this.$refs.verifyTable.doLayout();
          });
        }
      });
    },
    async selectRecord(row) {
      this.verifyContent = "";
      let params = {};
      //已有历史数据前端会留,因此不需要重新取
      if (row.verifyContent != null) {
        this.verifyContent = row.verifyContent;
        this.$refs.verifyTable.toggleRowSelection(row); //点击选中
        return;
      }
      if (this.showCondition == 0) {
        //未审核病历即时组装
        this.loading = true;
        await this.getRecord(row);
        this.loading = false;
        this.$refs.verifyTable.toggleRowSelection(row); //点击选中
      } else {
        //已审核过病历由日志表取内容
        params.verifyRecordLogID = row.verifyRecordLogID;
        await GetVerifyLog(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.verifyContent = result.data;
            row.verifyContent = result.data;
          }
        });
      }
    },
    //全選
    async chooseVerify(chooseRecords) {
      if (this.showCondition == 1) {
        return;
      }
      this.chooseVerifyList = chooseRecords;
      //加载病历
      this.loading = true;
      for (let index = 0; index < this.chooseVerifyList.length; index++) {
        if (this.chooseVerifyList[index].verifyContent == undefined) {
          await this.getRecord(this.chooseVerifyList[index]);
        }
      }
      this.loading = false;
    },
    //病历审核
    async verifyRecords() {
      this.loading = true;
      let verify = [];
      this.chooseVerifyList.forEach((item) => {
        let params = {
          key: item.verifyRecordID,
          value: item.verifyContent,
        };
        verify.push(params);
      });
      if (verify.length > 0) {
        await VerifyRecords(verify).then((result) => {
          this.loading = false;
          if (this._common.isSuccess(result)) {
            this.selectVerifyRecord();
          }
        });
      } else {
        this.loading = false;
      }
    },
    /**
     * description: 取得病历内容
     * return {*}void
     * param {*} row 审核记录行
     */
    async getRecord(row) {
      let params = {
        api: this.verifyAPI,
        key: row.recordKey,
        shiftDate: row.writeTime,
        stationID: row.stationID,
        inpatientID: row.inpatientID,
        departmentListID: row.departmentListID,
      };
      await GetRecordDetail(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (this.recordKind == "Scale" || this.recordKind == "EvaluationScale") {
            this.verifyContent =
              "<b>评估时间</b>：【" +
              row.writeTime +
              "】</br>" +
              result.data +
              "</br>" +
              "<b>评估结果</b>：【" +
              row.content +
              "】" +
              //如果分数为null,不显示分值
              (row.point ? row.point + "分" : "");
            row.verifyContent = this.verifyContent;
          }
          if (
            this.recordKind == "AssessForm" ||
            this.recordKind == "Plan" ||
            this.recordKind == "Record" ||
            this.recordKind == "EducationSheet"
          ) {
            row.verifyContent = result.data;
            this.verifyContent = result.data;
          }
          // this.$nextTick(() => {
          //   this.$refs.verifyTable.doLayout();
          // });
        }
      });
    },
    /**
     * description:是否需要显示病历内容标识
     * return {true/false}
     */
    haveRecordItem() {
      if (
        this.chooseRecord == "7" || //伤口
        this.chooseRecord == "11" || //导管
        this.chooseRecord == "29" || //疼痛
        this.chooseRecord == "5" //评估
      ) {
        return true;
      }
      return false;
    },
  },
};
</script>
<style lang="scss">
.verify-records {
  .switch-show {
    margin-left: 0px;
  }
  .search-input {
    width: 160px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .verify-button {
    margin-top: 5px;
    float: right;
  }
  .verify-content {
    height: 100%;
    .tabs {
      height: 100%;
      .el-tabs__content {
        height: 90%;
      }
      .record-list {
        height: 100%;
        width: 100%;
        .table {
          height: 100%;
          width: 55%;
          display: inline-block;
          .height-risk {
            color: red;
          }
          overflow-y: auto;
        }
        .record-detail {
          background-color: white;
          height: 100%;
          width: 44%;
          display: inline-block;
          position: absolute;
          right: 0;
          overflow-y: auto;
        }
        .big-font {
          font-size: 21px;
        }
      }
    }
  }
}
</style>