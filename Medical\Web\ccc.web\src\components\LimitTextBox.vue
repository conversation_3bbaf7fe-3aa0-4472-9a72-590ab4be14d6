<!--
 * FilePath     : \src\components\LimitTextBox.vue
 * Author       : 苏军志
 * Date         : 2020-04-25 14:17
 * LastEditors  : 苏军志
 * LastEditTime : 2020-04-25 15:04
 * Description  : 限制字数的多行文本框
 -->
<template>
  <div class="limit-text-box" :style="style">
    <textarea
      :rows="rows"
      :maxlength="maxLength"
      v-model="textContent"
      @input="change"
    >
    </textarea>
    <div :class="['tip', { max: inputLength == maxLength }]">
      {{ inputLength }}/{{ maxLength }}
    </div>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: String,
      required: true
    },
    maxLength: {
      type: Number,
      default: 500
    },
    rows: {
      type: Number,
      default: 5
    },
    width: {
      type: String,
      default: "300px"
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        this.textContent = newValue;
      }
    }
  },
  computed: {
    inputLength() {
      if (this.maxLength) {
        return this.textContent.length;
      } else {
        return 0;
      }
    },
    style() {
      return {
        width: this._common.getHeigt(this.width)
      };
    }
  },
  data() {
    return {
      textContent: ""
    };
  },
  methods: {
    change() {
      this.$emit("input", this.textContent);
    }
  }
};
</script>
<style>
.limit-text-box textarea {
  width: 100%;
  resize: none;
  color: #000;
  outline: none !important;
  border: 1px solid #4abfa2 !important;
  font-family: "Microsoft Yahei", "Avenir", Helvetica, Arial, sans-serif;
  padding: 5px;
  line-height: 20px;
  letter-spacing: 2px;
}
.limit-text-box .tip {
  width: 100%;
  text-align: right;
}
.limit-text-box .tip.max {
  color: #ff0000;
}
</style>
