/*
 * FilePath     : \src\utils\navigation.js
 * Author       : 苏军志
 * Date         : 2020-06-01 20:19
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-23 16:35
 * Description  :
 */

import { GetSession } from "@/api/User";
import autoTrack from "@/utils/autoTrack";
import common from "@/utils/common";
export default {
  set: (router, store, vue) => {
    //导航守卫
    router.beforeEach(async (to, from, next) => {
      // 切换页面前把上页面未完成的API请求全部取消
      if (window._apiRequestList.length > 0) {
        window._apiRequestList.forEach((request) => {
          request.cancel("switch||" + decodeURIComponent(request.key));
        });
        window._apiRequestList = [];
      }
      //根据配置写操作日志
      autoTrack.trackPageView(to);
      // 设置当前画面是否为只读
      if (to.meta.readOnly) {
        store.commit("session/setReadOnly", true);
      } else {
        if (from.meta.readOnly && from.path.indexOf(to.meta.parentPath) != -1) {
          to.query.isDischarge = true;
          store.commit("session/setReadOnly", true);
        } else {
          store.commit("session/setReadOnly", false);
        }
      }
      // 如果要跳转的画面是需要缓存的，需要处理下
      if (to.meta.keepAlive) {
        // 如果不是从其子画面跳转过来的，则需要刷新该缓存画面
        if (from.meta.parentPath !== to.path) {
          to.meta.refreshFlag = true;
        } else {
          to.meta.refreshFlag = false;
        }
      }
      // 不需要验证则直接跳转
      if (!to.meta.auth) {
        next();
      } else {
        // 如果需要验证则判断token
        if (to.path != "/login" && to.matched.some((m) => m.meta.auth)) {
          if (to.query.token) {
            // 如果有token则直接跳转
            if (store.state.auth) {
              if (store.state.auth.token != to.query.token) {
                store.commit("auth/setToken", to.query.token);
                await GetSession().then((result) => {
                  if (result.code === 1) {
                    store.commit("auth/login");
                    let user = {
                      roles: result.data.authorityID,
                      userID: result.data.userID,
                      userName: result.data.employeeName,
                      stationID: result.data.stationID,
                      stationCode: result.data.stationCode,
                    };
                    // 组装登录参数，为系统更新后自动重新登录准备
                    let params = {
                      userId: result.data.userName,
                      password: result.data.password,
                      language: result.data.languageID,
                      clientType: result.data.clientType,
                      hospitalID: result.data.hospitalID,
                    };
                    store.commit("session/setUser", user);
                    this.$store.commit("session/setLoginParams", params);
                    store.commit("session/setHospitalInfo", {
                      hospitalListID: result.data.hospitalID,
                    });
                  }
                });
              }
            }
            next();
            return;
          }
          // 对路由进行验证
          if (store.state.auth.isLogin) {
            next(); // 正常跳转到你设置好的页面
          } else {
            // 未登录则跳转到登陆界面
            next({
              path: "/login",
              query: {
                url: to.fullPath,
              },
            });
          }
        } else {
          next();
        }
      }
    });
    router.afterEach((to, from) => {
      let router =
        to.fullPath.includes("shortCutFlag") || to.fullPath.includes("isDialog")
          ? to.path
          : to.fullPath;
      // 写系统操作日志需要使用
      common.session("pagePath", router);
      // 根据当前路由处理按钮级权限
      let buttonAuthorityList = common.session("buttonAuthorityList");
      let buttonList = common.session("buttonList");
      if (
        !buttonAuthorityList ||
        buttonAuthorityList.length <= 0 ||
        !buttonList ||
        buttonList.length <= 0
      ) {
        return;
      }
      // 要跳转的路由没有查到数据，所有按钮显示
      buttonAuthorityList = buttonAuthorityList.filter(
        (item) => item.router == router
      );
      // 根据权限设置按钮是否显示
      let style = "";
      buttonList.forEach((button) => {
        // 默认显示
        let display = "";
        let buttonAuthority = buttonAuthorityList.find(
          (buttonAuthority) =>
            buttonAuthority.buttonListID == button.buttonListID
        );
        // 不为空并且状态为0 则隐藏该按钮
        if (buttonAuthority && buttonAuthority.status == 0) {
          display = "none";
        }
        // 组装每个按钮的样式，带注释
        style += `/* ${button.buttonName} */ \n .${button.buttonClass} { display: ${display} !important; } \n`;
      });
      // 在head中动态添加style
      common.addNewStyle(style);
    });
  },
};
