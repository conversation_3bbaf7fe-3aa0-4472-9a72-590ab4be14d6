<!--
 * FilePath     : d:\ccc\web\ccc.web\src\pages\nursingEvaluation\inHospital.vue
 * Author       : 郭自飞
 * Date         : 2020-05-05 14:57
 * LastEditors  : 曹恩
 * LastEditTime : 2021-05-29 09:48
 * Description  : 在院评价
 -->
 
<template>
  <patient-evaluation :inpatientid="inpatientID" :stationid="stationid" :warnFlag="true"></patient-evaluation>
</template>
<script>
import patientEvaluation from "./patientEvaluation";
import { mapGetters } from "vuex";
export default {
  components: {
    patientEvaluation,
  },
  data() {
    return {
      inpatientID: "",
      stationid: undefined,
    };
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
    }),
  },
  watch: {
    inpatient(newVal) {
      if (!newVal) return;
      this.inpatientID = this.inpatient.inpatientID;
      this.stationid = this.inpatient.stationID;
    },
  },
  activated() {},
  mounted() {
    if (this.inpatient) {
      this.inpatientID = this.inpatient.inpatientID;
      this.stationid = this.inpatient.stationID;
    }
  },
  methods: {},
};
</script>
<style lang="scss">
.in-hospital {
  .histroy-problem {
    height: 100%;
    overflow-y: scroll;
  }
  .switch-evaluate {
    margin-left: 10px;
  }
}
</style>
