<!--
 * FilePath     : \src\autoPages\dictionaryMaintain\interventionTriggerIntervention\index.vue
 * Author       : 郭鹏超
 * Date         : 2023-08-08 19:45
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-16 08:44
 * Description  : 触发措施配置维护
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="intervention-trigger-intervention">
    <div class="header" slot="header">
      <el-button type="success" class="add-button" @click="maintainRecord()" icon="iconfont icon-add">新增</el-button>
    </div>
    <div ref="triggerTable" v-loading="loading" element-loading-text="加载中……" class="content">
      <el-table
        v-if="tableData.length"
        height="100%"
        v-loading="loading"
        element-loading-text="加载中……"
        border
        stripe
        :data="tableData"
      >
        <el-table-column prop="assessName" label="评估项目" :min-width="convertPX(120)" align="left"></el-table-column>
        <el-table-column prop="sexName" label="性别" :min-width="convertPX(60)" align="center"></el-table-column>
        <el-table-column prop="drugTriggerType" label="途径" :min-width="convertPX(120)" align="left"></el-table-column>
        <el-table-column label="年龄" :min-width="convertPX(120)" align="center">
          <el-table-column prop="minAge" label="下限" :min-width="convertPX(100)" align="center"></el-table-column>
          <el-table-column prop="maxAge" label="上限" :min-width="convertPX(100)" align="center"></el-table-column>
        </el-table-column>
        <el-table-column prop="actionTypeName" label="评估项目值" :min-width="convertPX(120)" align="center">
          <el-table-column prop="minValue" label="下限" :min-width="convertPX(100)" align="center"></el-table-column>
          <el-table-column prop="maxValue" label="上限" :min-width="convertPX(100)" align="center"></el-table-column>
        </el-table-column>
        <el-table-column prop="frequency" label="触发频次" :min-width="convertPX(120)" align="left"></el-table-column>
        <el-table-column
          prop="triggerIntervention"
          label="触发措施名称"
          :min-width="convertPX(200)"
          align="left"
        ></el-table-column>
        <el-table-column label="操作" :width="convertPX(80)" head-align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="maintainRecord(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="deleteRecord(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      custom-class="trigger-intervention-dialog"
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      top="20vh"
    >
      <div class="dialog-content">
        <el-form ref="form" :model="saveView" label-width="80px">
          <el-form-item label="评估项目">
            <el-select v-model="saveView.assessCode">
              <el-option
                v-for="(item, index) in assessTypeList"
                :key="index"
                :label="item[1]"
                :value="item[0]"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="saveView.assessCode == 'PainDrug'" label="药物途径">
            <el-select v-model="saveView.drugTriggerTypeCode">
              <el-option
                v-for="(item, index) in drugTypeList"
                :key="index"
                :label="item[1]"
                :value="item[0]"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="性别">
            <el-select v-model="saveView.sex">
              <el-option label=" " value="S"></el-option>
              <el-option label="男" value="1"></el-option>
              <el-option label="女" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="年龄">
            <el-col :span="11"><el-input v-model="saveView.minAge"></el-input></el-col>
            <el-col :span="1" class="line">--</el-col>
            <el-col :span="11"><el-input v-model="saveView.maxAge"></el-input></el-col>
          </el-form-item>
          <el-form-item label="评估项目值">
            <el-col :span="11"><el-input v-model="saveView.minValue"></el-input></el-col>
            <el-col :span="1" class="line">--</el-col>
            <el-col :span="11"><el-input v-model="saveView.maxValue"></el-input></el-col>
          </el-form-item>
          <el-form-item label="频次">
            <frequency-selector
              v-model="saveView.frequencyID"
              :frequencyList="frequencyList"
              v-if="frequencyList && frequencyList.length > 0"
            ></frequency-selector>
          </el-form-item>
          <el-form-item label="触发措施">
            <el-select v-model="saveView.triggerInterventionID">
              <el-option
                v-for="(item, index) in interventionList"
                :key="index"
                :label="item[1]"
                :value="item[0]"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveTriggerIntervention">保存</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>

<script>
import BaseLayout from "@/components/BaseLayout";
import frequencySelector from "@/components/selector/frequencySelector";
import {
  GetTriggerInterventionTableData,
  GetTriggerInterventionSelectOption,
  SaveTriggerIntervention,
  DeleteTriggerIntervention,
} from "@/api/interventionTriggerIntervention.js";
import { GetTypeFrequency } from "@/api/Frequency";
export default {
  components: {
    BaseLayout,
    frequencySelector,
  },
  data() {
    return {
      loading: false,
      dialogTitle: "新增",
      dialogVisible: false,
      assessName: undefined,
      tableData: [],
      saveView: {
        id: undefined,
        assessCode: "",
        sex: "",
        minAge: undefined,
        maxAge: undefined,
        minValue: "",
        maxValue: "",
        frequencyID: 32,
        drugTriggerTypeCode: undefined,
        triggerInterventionID: undefined,
      },
      assessTypeList: [],
      drugTypeList: [],
      interventionList: [],
      frequencyList: [],
    };
  },
  mounted() {
    this.getSelectOption();
    this.GetTypeFrequency();
    this.getTableData();
  },
  methods: {
    /**
     * description: 获取表格数据
     * return {*}
     */
    getTableData() {
      this.tableData = [];
      this.loading = true;
      GetTriggerInterventionTableData().then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.tableData = res.data;
        }
      });
    },
    /**
     * description: 新增或修改弹窗初始化
     * param {*} row
     * return {*}
     */
    maintainRecord(row) {
      this.dialogTitle = !!row ? "修改" : "新增";
      this.saveView.id = row?.id ?? undefined;
      this.saveView.assessCode = row?.assessCode ?? undefined;
      this.saveView.sex = row?.sex ?? undefined;
      this.saveView.minAge = row?.minAge ?? undefined;
      this.saveView.maxAge = row?.maxAge ?? undefined;
      this.saveView.minValue = row?.minValue ?? undefined;
      this.saveView.maxValue = row?.maxValue ?? undefined;
      this.saveView.frequencyID = row?.frequencyID ?? 32;
      this.saveView.drugTriggerTypeCode = row?.drugTriggerTypeCode ?? undefined;
      this.saveView.triggerInterventionID = String(row?.triggerInterventionID ?? "");
      this.dialogVisible = true;
    },
    /**
     * description:新增修改保存
     * return {*}
     */
    saveTriggerIntervention() {
      SaveTriggerIntervention(this.saveView).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.dialogVisible = false;
          this._showTip("success", "保存成功");
          this.getTableData();
        }
      });
    },
    /**
     * description: 删除
     * param {*} row
     * return {*}
     */
    deleteRecord(row) {
      console.log("row: ", row);
      this.$confirm("是否删除该记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          id: row.id,
        };
        console.log("params: ", params);
        DeleteTriggerIntervention(params).then((res) => {
          this.loading = false;
          if (this._common.isSuccess(res)) {
            this._showTip("success", "删除成功");
            this.getTableData();
          }
        });
      });
    },
    /**
     * description: 获取弹窗下拉组件配置
     * return {*}
     */
    getSelectOption() {
      GetTriggerInterventionSelectOption().then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.assessTypeList = Object.entries(res.data.assessTypeList);
          this.interventionList = Object.entries(res.data.interventionList);
          this.drugTypeList = Object.entries(res.data.drugTypeList);
        }
      });
    },
    /**
     * description: 获取频次列表
     * return {*}
     */
    async GetTypeFrequency() {
      this.frequencyList = [];
      let params = {
        visibleFlag: true,
      };
      await GetTypeFrequency(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.frequencyList = result.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.intervention-trigger-intervention {
  height: 100%;
  .header {
    .add-button {
      float: right;
      margin-top: 8px;
    }
    .input {
      width: 300px;
    }
  }
  .content {
    height: 100%;
  }
  .trigger-intervention-dialog {
    height: 600px;
  }
  .line {
    text-align: center;
  }
}
</style>