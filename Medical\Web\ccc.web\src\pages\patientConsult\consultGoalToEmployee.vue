<!--
 * FilePath     : \src\pages\patientConsult\consultGoalToEmployee.vue
 * Author       : 郭自飞
 * Date         : 2020-10-26 14:17
 * LastEditors  : 来江禹
 * LastEditTime : 2024-04-02 14:31
 * Description  :
-->
<template>
  <base-layout show-header class="consult-employee">
    <div slot="header" class="header">
      <span>目的：</span>
      <el-select
        v-model="consultMainID"
        placeholder="请选择"
        class="header-select"
        @change="getConsultGoalById(consultMainID, true)"
      >
        <el-option
          v-for="item in consultGoalOne"
          :key="item.consultGoalID"
          :label="item.content"
          :value="item.consultGoalID"
        ></el-option>
      </el-select>
      <el-select v-model="consultDetailID" class="header-select" placeholder="请选择">
        <el-option
          v-for="item in consultGoalTwo"
          :key="item.consultGoalID"
          :label="item.content"
          :value="item.consultGoalID"
        ></el-option>
      </el-select>
      <span>病区：</span>
      <el-select v-model="stationID" class="header-select" placeholder="请选择">
        <el-option v-for="item in stationList" :key="item.id" :label="item.stationName" :value="item.id"></el-option>
      </el-select>
      <el-button class="query-button" icon="iconfont icon-search" @click="getByConsultGoalID()">查询</el-button>
      <el-button class="add-button" icon="iconfont icon-add" @click="saveConsultEmployeeDialog()">新增</el-button>
    </div>
    <el-table height="100%" :data="goalToEmployeeList" v-loading="loading" border stripe>
      <el-table-column label="目的" header-align="center" show-overflow-tooltip min-width="100" align="left">
        <template slot-scope="scope">
          <span>{{ scope.row.consultGoalMainName + "-" + scope.row.consultGoalDetailName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="人员" prop="employeeName" min-width="100" align="center"></el-table-column>
      <el-table-column label="病区" prop="stationName" min-width="100" align="center"></el-table-column>
      <el-table-column label="操作" width="70" align="center">
        <template slot-scope="consultEmployee">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="saveConsultEmployeeDialog(consultEmployee.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i
              class="iconfont icon-del"
              @click="deleteConsultEmployee(consultEmployee.row.consultGoalToEmployeeID)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="会诊人员维护"
      :visible.sync="dialogFormVisible"
      width="450px"
      v-dialogDrag
      :close-on-click-modal="false"
      v-loading="dialogLoading"
      element-loading-text="请稍等"
    >
      <div class="save-employee">
        <span>目的：</span>
        <el-select
          v-model="consultID"
          placeholder="请选择"
          class="employee-select"
          @change="getConsultGoalById(consultID)"
        >
          <el-option
            v-for="item in consultGoalOne"
            :key="item.consultGoalID"
            :label="item.content"
            :value="item.consultGoalID"
          ></el-option>
        </el-select>
        <el-select v-model="saveDate.consultGoalID" class="goal-select" placeholder="请选择">
          <el-option
            v-for="item in consultGoalTwo"
            :key="item.consultGoalID"
            :label="item.content"
            :value="item.consultGoalID"
          ></el-option>
        </el-select>
      </div>
      <br />
      <div class="save-employee">
        <span>病区：</span>
        <el-select
          v-model="saveDate.stationID"
          @change="selectStation(saveDate.stationID)"
          class="employee-select"
          placeholder="请选择"
        >
          <el-option
            v-for="item in initialStationList"
            :key="item.id"
            :label="item.stationName"
            :value="item.id"
          ></el-option>
        </el-select>
        <span>人员：</span>
        <el-select
          v-show="!add"
          v-model="saveDate.employeeID"
          filterable
          :filter-method="filterEmployeeList"
          class="employee-select"
          placeholder="请选择"
        >
          <el-option v-for="item in nurseList" :key="item.userID" :label="item.name" :value="item.userID"></el-option>
        </el-select>
        <el-select
          v-show="add"
          v-model="saveDate.employeeIDList"
          filterable
          :filter-method="filterEmployeeList"
          class="employee-select"
          multiple
          placeholder="请选择"
        >
          <el-option v-for="item in nurseList" :key="item.userID" :label="item.name" :value="item.userID"></el-option>
        </el-select>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="saveConsultEmployee">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetNurse } from "@/api/User";
import { mapGetters } from "vuex";
import { GetByConsultGoalID, SaveConsultEmployee, DeleteConsultEmployee } from "@/api/ConsultGoalToEmployee";
import { GetConsultGoal, GetConsultGoalById } from "@/api/PatientConsult";
import { GetStationList } from "@/api/Station";
export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      goalToEmployeeList: [],
      consultGoalOne: [],
      consultGoalTwo: [],
      stationList: [],
      initialStationList: [],
      nurseList: [],
      consultDetailID: undefined,
      consultMainID: undefined,
      stationID: undefined,
      consultID: undefined,
      saveDate: {
        consultGoalEmployeeID: 0,
        employeeID: undefined,
        consultGoalID: undefined,
        stationID: undefined,
        employeeIDList: [],
      },
      loading: false,
      dialogLoading: false,
      dialogFormVisible: false,
      add: false,
      copyNurseList: [],
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  mounted() {
    this.init();
    this.getStationList();
    this.stationID = this.user?.stationID;
  },
  methods: {
    /**
     * @description: 初始化函数
     * @return
     */
    init() {
      let prams = {
        level: 1,
      };
      GetConsultGoal(prams).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultGoalOne = res.data;
        }
      });
    },
    /**
     * @description: 获取病区下拉框数据
     * @return
     */
    getStationList() {
      GetStationList().then((res) => {
        if (this._common.isSuccess(res)) {
          this.initialStationList = this._common.clone(res.data);
          this.stationList = res.data;
          this.stationList.unshift({
            id: 0,
            stationName: "全部",
          });
        }
      });
    },
    /**
     * @description: 获取会诊人员信息
     * @return
     */
    getByConsultGoalID() {
      if (!this.consultDetailID || !this.consultMainID) {
        this._showTip("warning", "请完选择会诊目的");
        return;
      }
      let params = {
        consultGoalID: this.consultDetailID,
        stationID: this.stationID,
      };
      this.loading = true;
      GetByConsultGoalID(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.goalToEmployeeList = res.data;
        }
      });
    },
    /**
     * @description: 根据会诊一阶目的查询二阶目的
     * @param id
     * @param isMain
     * @return
     */
    getConsultGoalById(id, isMain) {
      //清空后面两个选项
      let prams = {
        consultMainID: id,
      };
      if (isMain == true) {
        this.consultDetailID = undefined;
      }
      if (isMain != "update") {
        this.saveDate.consultGoalID = undefined;
        this.saveDate.employeeID = undefined;
        this.saveDate.stationID = undefined;
      }
      GetConsultGoalById(prams).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultGoalTwo = res.data;
        }
      });
    },
    /**
     * @description: 根据病区查询人员
     * @param id
     * @param employeeID
     * @return
     */
    selectStation(id, employeeID) {
      let params = {
        stationID: id,
      };
      this.saveDate.employeeID = employeeID?.trim();
      GetNurse(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.nurseList = result.data;
          this.copyNurseList = result.data;
          this.saveDate.employeeIDList = [];
        }
      });
    },
    /**
     * @description: 会诊人员信息弹窗
     * @param data
     * @return
     */
    saveConsultEmployeeDialog(data) {
      this.dialogFormVisible = true;
      if (data) {
        //修改
        this.add = false;
        this.consultID = data.consultGoalMainID;
        this.saveDate.consultGoalID = data.consultGoalID;
        this.saveDate.consultGoalEmployeeID = data.consultGoalToEmployeeID;
        this.saveDate.stationID = data.stationID;
        this.getConsultGoalById(this.consultID, "update");
        this.selectStation(this.saveDate.stationID, data.employeeID);
      } else {
        //新增
        if (this.consultMainID) {
          this.add = true;
          this.consultID = this.consultMainID;
          this.saveDate.consultGoalEmployeeID = 0;
          this.saveDate.consultGoalID = this.consultDetailID;
          this.saveDate.stationID = !this.stationID ? this.user.stationID : this.stationID;
          this.saveDate.employeeID = undefined;
          this.selectStation(this.saveDate.stationID, this.saveDate.employeeID);
        } else {
          this.add = true;
          this.consultID = undefined;
          this.saveDate.consultGoalEmployeeID = 0;
          this.saveDate.consultGoalID = undefined;
          this.saveDate.stationID = undefined;
          this.saveDate.employeeID = undefined;
          this.nurseList = [];
          this.consultGoalTwo = [];
          this.saveDate.employeeIDList = [];
        }
      }
    },
    /**
     * @description: 会诊人员信息维护
     * @return
     */
    saveConsultEmployee() {
      if (!this.saveDate.consultGoalID || !this.saveDate.stationID) {
        if (this.add && this.saveDate.employeeIDList.length <= 0) {
          this._showTip("warning", "请完整填写信息");
          return;
        }
        if (this.add && !this.saveDate.employeeID) {
          this._showTip("warning", "请完整填写信息");
          return;
        }
      }
      this.dialogLoading = true;
      let params = {
        consultGoalToEmployeeID: this.saveDate.consultGoalEmployeeID,
        consultGoalID: this.saveDate.consultGoalID,
        employeeID: this.saveDate.employeeID,
        stationID: this.saveDate.stationID,
        employeeIDList: this.saveDate.employeeIDList,
      };
      return SaveConsultEmployee(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.stationID = this.saveDate.stationID;
          this.consultDetailID = this.saveDate.consultGoalID;
          this.consultMainID = this.consultID;
          this.getByConsultGoalID();
          if (this.saveDate.consultGoalEmployeeID) {
            this._showTip("success", "修改成功");
          } else {
            this._showTip("success", "新增成功");
          }
        }
        this.dialogLoading = false;
        this.dialogFormVisible = false;
      });
    },
    /**
     * @description: 删除数据
     * @param consultGoalToEmployeeID
     * @return
     */
    deleteConsultEmployee(consultGoalToEmployeeID) {
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          // 确认删除
          let prams = {
            consultEmployeeID: consultGoalToEmployeeID,
          };
          DeleteConsultEmployee(prams).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getByConsultGoalID();
            }
          });
        }
      });
    },
    /**
     * @description: 根据工号筛选人员数据
     * @param value
     * @return
     */
    filterEmployeeList(value) {
      if (!value) {
        this.nurseList = this.copyNurseList;
        return;
      }
      this.nurseList = this.copyNurseList.filter((nurse) => nurse.userID.includes(value));
    },
  },
};
</script>
<style lang="scss">
.consult-employee {
  .header {
    .header-select {
      width: 150px;
    }
    .add-button {
      float: right;
      margin-top: 9px;
    }
  }
  .save-employee {
    margin-top: 10px;
    margin-left: 15px;
    .employee-select {
      width: 150px;
    }
    .goal-select {
      width: 195px;
    }
  }
  .el-dialog {
    overflow: hidden;
    height: 50%;
  }
}
</style>
