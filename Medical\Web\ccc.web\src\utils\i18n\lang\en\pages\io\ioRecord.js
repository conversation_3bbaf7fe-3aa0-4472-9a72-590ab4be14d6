/*
 * FilePath     : \src\utils\i18n\lang\en\pages\io\ioRecord.js
 * Author       : 苏军志
 * Date         : 2021-11-02 15:17
 * LastEditors  : 苏军志
 * LastEditTime : 2021-11-04 08:47
 * Description  : 出入量记录单
 */
export default {
  ioRecord: {
    switchLabel: "Use shift aggregation:",
    conditionsLabel: "Aggregation conditions:",
    date: "Date",
    time: "Time",
    input: "Intake",
    output: "Output",
    monitoringButton: "Daily traffic monitoring",
    dailySummary: "Daily Summary",
    shiftSummary: "Summary of each shift",
    shift: "Shift",
    startTime: "Start Time",
    endTime: "End Time",
    inflow: "Net liquid inflow",
    balance: "Balance",
    subtotal: "Sub Tot",
    total: "Total:",
    checkDateTip: "Please check the date!"
  }
};
