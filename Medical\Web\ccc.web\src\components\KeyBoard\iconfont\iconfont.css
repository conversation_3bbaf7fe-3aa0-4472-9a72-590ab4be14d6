@font-face {font-family: "iconfont";
  src: url('iconfont.eot?t=1559788640599'); /* IE9 */
  src: url('iconfont.eot?t=1559788640599#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAQwAAsAAAAACNwAAAPjAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDKgqEdIQFATYCJAMUCwwABCAFhG0HVRuWB8gekiSQQAIFKKgiAMaIB/g19X1Kb5cCuzGDnZsEMpps51A4YGFYT2f+9ao/WoDZExZyRQQ/GSfvT2ZJVkeVRnVFfbkY+v/D/UwXbQPLZ9/mkh04wFHiaGDRV22RwncgB+S3DK91qQ77dgJqI9wgWBefmgH0ZGRbQLgmY3Cgl9DLFdSgUjUKTs2bBP0kqngvvgQ88X4//OMd9IhKQvbuuhBHgcifEX/1RXda9QocFMFwVvBnkTANkAlXCu3niIL4NCK1ejOxRYBKJfIz4s/pX/3/A+5EuBVeZbxG3MQ/XiI0EHk74FfJ8pEJLITIzxOEwM9ThMwvfb0E2LK5Ri/gJQD+Ify4qg5BEjKhpHx2d7fYst/K2bLMUFAfNADNfZ73draRgo2eMxa0HwHZ6Tye9WST4/dOwpkPfqZnn1xFsx8Lx9cNTO4/cxBNPTc0ffDUXtns+6+OdVnDkYn1ZRt6xJYaXt2cssVvvTY0/fbtoOTN1wMmDz8vo7brb2YzW28DTB99GS2e/xxr0vn1Gj3aoBrpss3OtJ9zq8EmK3usYbPTKiNrtHO93jozY7ieHe60oUcnt9as4agVM7ayMW5lo5dP8m3aCvIL5toCLq6SHDVOhpSKTSW30Wwyf9LGDPMKtbE3YENb+7uS85Kn5fwpb0PDLU70vbOcED5F2sChOvvBt7SXF16O1rTrCW6NjdS2tVmXHOMEendR7f4nrvqF5wrKyoR5VsJc15MrDF+Vvpz/5ai7GXc9ZJejt+JcSsK/p6W5WLnYGd/DL8e7TGuVt85Z3mp3HK+pJRx36Jxje60frKkdd8jMMOEAXF5MeaVyAC3padICQHoXS1SoeKYSJf43/I1Xvz+9O2ka+r8yzOCHy0yJp21p7tAC8EoFquDdtxUGAMosQUQMJSt8i9dAmbvkKbYyhl0RtRotcPf6MoxyoHExQWUggqjNcEgq49AZPw2KpnnQUFkJtakSzm7qg4iEPAxTtCAIuu1B1OkNJN1eoTP+MxSD/kFDdxBB7UCwvGTThCAziEEQ5yGFERWYjGTVtGniDfzSNMholTiKizxaFkQaWQjzcvdMpxKgGqIhDmhKGW+epzEasSosHt0NKpUsxiFWDkneXcrzXLCHB130Ru4kqwIG1kIgHA+iYAgVMDIklhrtDOYZZH4+DcTQUsKhip4qexYI0ZDNj/HizrMBOkGubtRzKYs1SjG88Xg0s4mGsFQw8WCFlDTOwnDFq+QgEs+dtEOUE8wD96Obyt2nV6ru8RTUyDOWEEMKOZTQ0HFJRsGbClhBsDii9DgES2WsVqNP4KRCw9k1UKyG5TwAAA==') format('woff2'),
  url('iconfont.woff?t=1559788640599') format('woff'),
  url('iconfont.ttf?t=1559788640599') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  url('iconfont.svg?t=1559788640599#iconfont') format('svg'); /* iOS 4.1- */
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-hide-keyboard:before {
  content: "\e6b5";
}

.icon-previous:before {
  content: "\e63f";
}

.icon-backspace:before {
  content: "\e709";
}

.icon-next:before {
  content: "\e6b6";
}

