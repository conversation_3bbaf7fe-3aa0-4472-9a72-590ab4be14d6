/*
 * FilePath     : \src\api\ElectBoard.js
 * Author       : 胡洋
 * Date         : 2020-10-15 09:17
 * LastEditors  : 胡洋
 * LastEditTime : 2020-10-24 10:39
 * Description  : 值班医师维护
 */
import http from "../utils/ajax";
const baseUrl = "/dutyDoctors";

export const urls = {
  GetDutyDoctors: baseUrl + "/GetDutyDoctors",
  SaveDoctor: baseUrl + "/SaveDoctor",
  GetDoctorWithDuty: baseUrl + "/GetDoctorWithDuty"
};

export const GetDutyDoctors = params => {
  return http.get(urls.GetDutyDoctors, params);
};
export const SaveDoctor = params => {
  return http.post(urls.SaveDoctor, params);
};
export const GetDoctorWithDuty = params => {
  return http.get(urls.GetDoctorWithDuty, params);
};
