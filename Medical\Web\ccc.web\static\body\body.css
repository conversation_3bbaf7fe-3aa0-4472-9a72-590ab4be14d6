.bodyMap {
  background-repeat: no-repeat;
}

.map_tab {
  position: relative;
}
.map_tab > span {
  background: transparent;
}
.map_tab .list_tab {
  width: 100%;
}
.map_tab .point {
  position: absolute;
  color: #0000ee;
  font-size: 14.4px;
  font-size: 0.9rem;
  cursor: pointer;
}
/* .map_tab .point.is-checked, */
.map_tab .point2.hasSelectedChild {
  color: #ff7400;
  font-weight: bold;
}
.map_tab .point2:hover {
  padding: 1px 4px;
  color: #ee0000;
  background-color: #000;
  opacity: 0.7;
  /* cursor: pointer; */
}
.map_tab .point input[type="checkbox"] {
  /* margin-top: -0.96px;
  margin-top: -0.06rem; */
  width: 13px;
  height: 13px;
  /* margin-top: 1px; */
  vertical-align: middle;
  zoom: 1.2;
  cursor: pointer;
  -webkit-appearance: none;
  vertical-align: middle;
  margin-top: 0;
  background: #fff;
  border: #999 solid 1px;
  border-radius: 3px;
}
.map_tab .point input[type="checkbox"]:checked {
  background: #ff7400;
  border: solid 1px #ff7400;
}
.map_tab .point input[type="checkbox"]:focus {
  outline: none;
}

.map_tab .point input[type="checkbox"]:checked::after {
  content: "";
  top: 3px;
  left: 6.5px;
  position: absolute;
  background: transparent;
  border: #fff solid 2px;
  border-top: none;
  border-right: none;
  height: 4px;
  width: 6px;
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.map_tab .reverse {
  color: #eee;
  /*主题色*/
  /*background-color: #35AE47;*/
  background-color: #4abfa2;
  font-size: 14.4px;
  font-size: 0.9rem;
  width: 52.8px;
  width: 3rem;
  height: 52.8px;
  height: 3rem;
  line-height: 52.8px;
  line-height: 3rem;
  text-align: center;
  border-radius: 52.8px;
  border-radius: 3.3rem;
  cursor: pointer;
  position: absolute;
  top: -10px;
  left: 0px;
}
.map_tab .dot {
  position: absolute;
  background-color: #ee0000;
  width: 1px;
  height: 1px;
  z-index: 999;
}

.tab {
  width: 100%;
  text-align: center;
  min-height: 28px;
  min-height: 1.75rem;
  padding: 0;
  border: 0;
  margin: 0;
}
.tab .tab__item {
  width: 50%;
  color: #222;
  float: left;
  display: inline-block;
  background-color: #ccc;
  height: 32px;
  height: 2rem;
  line-height: 32px;
  line-height: 2rem;
  cursor: default;
  min-height: 28px;
  min-height: 1.75rem;
  padding: 0;
  border: 0;
  margin: 0;
}
.tab .tab__item_selected {
  color: #eee;
  /*主题色*/
  background-color: #4abfa2;
}
.list_tab {
  padding-left: 16px;
  padding-left: 1rem;
}
.list_tab ul {
  padding: 0;
  margin: 0;
}
.list_tab ul li {
  width: 100%;
  height: 48px;
  height: 3rem;
  line-height: 48px;
  line-height: 3rem;
  font-size: 14.72px;
  font-size: 0.92rem;
  list-style-type: none;
  color: #333;
  border-bottom: 1px solid #eee;
  cursor: default;
  background-color: #fff;
}
.list_tab ul li input[type="checkbox"] {
  margin-top: -0.96px;
  margin-top: -0.06rem;
  vertical-align: middle;
  zoom: 1.2;
}
.list_tab ul li span {
  color: #aaa;
  position: absolute;
  right: 16px;
  right: 1rem;
  background: transparent;
}
.list_tab ul li a {
  background: transparent;
}
.list_tab ul .level0_select {
  cursor: pointer;
}
.list_tab ul .level0_select:hover {
  background-color: #eee;
}
.menuTitle {
  height: 32px;
  height: 2em;
  line-height: 32px;
  line-height: 2em;
  width: 100%;
  background-color: transparent;
  cursor: default;
}
.menuTitle span {
  background-color: transparent;
}
.menuTitle .titleName {
  color: #3b83e1;
  cursor: pointer;
}
.menuTitle .titleName:last-child {
  color: #ee0000;
  cursor: default;
}

.labelType {
  width: 24px;
  width: 1.5rem;
  height: 24px;
  height: 1.5rem;
  background-repeat: no-repeat;
  background-size: 24px 24px;
  background-size: 1.5rem 1.5rem;
  background-color: transparent;
  position: absolute;
}
