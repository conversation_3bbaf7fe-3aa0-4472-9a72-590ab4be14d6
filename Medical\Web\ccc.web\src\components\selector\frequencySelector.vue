<!--
 * FilePath     : \src\components\selector\frequencySelector.vue
 * Author       : 曹恩
 * Date         : 2021-10-14 10:41
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-04-18 16:41
 * Description  : 频次级联选择器
-->

<template>
  <div class="frequency-selector">
    <el-cascader
      :props="{ expandTrigger: expandTrigger, emitPath: emitPath }"
      :options="frequencyList"
      v-model="selected"
      @change="changeValue"
      :show-all-levels="allLevels"
      filterable
      :size="size"
    ></el-cascader>
  </div>
</template>
<script>
export default {
  props: {
    //措施frequencyID 有点地方是String 有的地方是Number
    value: {
      type: Number | String,
      default: "",
    },
    frequencyList: {
      type: Array,
    },
    //输入框中是否显示选中值的完整路径
    allLevels: {
      type: Boolean,
      default: false,
    },
    //尺寸 medium / small / mini
    size: {
      type: String,
      default: "mini",
    },
    // emitPath 是否返回由该节点所在的各级菜单的值所组成的数组
    emitPath: {
      type: Boolean,
      default: false,
    },
    //次级菜单的展开方式 click / hover
    expandTrigger: {
      type: String,
      default: "hover",
    },
  },
  data() {
    return {
      selected: "",
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.selected = newVal.toString();
      },
    },
  },
  methods: {
    changeValue(frequencyID) {
      this.$emit("input", frequencyID);
      this.$emit("select", frequencyID);
      let frequencyItem;
      this.frequencyList.forEach((frequencyList) => {
        if (frequencyList.children.find((frequency) => frequencyID == frequency.id)) {
          frequencyItem = frequencyList.children.find((frequency) => {
            return frequencyID == frequency.id;
          });
        }
      });
      if (frequencyItem) {
        this.$emit("select-item", frequencyItem);
      }
    },
  },
};
</script>