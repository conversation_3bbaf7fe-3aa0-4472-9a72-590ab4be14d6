<!--
 * FilePath     : \ccc.web\src\pages\dictionaryMaintain\settingDescription\index.vue
 * Author       : 胡长攀
 * Date         : 2022-12-30 14:00
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-01-10 09:15
 * Description  : settingDescription配置信息页
 * CodeIterationRecord: 
-->
<template>
  <base-layout :showHeader="false" class="setting-description">
    <el-table :data="tableData" v-loading="loading" element-loading-text="加载中……" border stripe>
      <el-table-column prop="settingTypeCode" label="编码" header-align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.settingTypeCode }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="说明" header-align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.description }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="controlerType" label="设置" align="center" width="180" resizable>
        <template slot-scope="scope">
          <el-switch
            v-if="scope.row.controlerType == 'S'"
            v-model="scope.row.typeValue"
            active-value="True"
            inactive-value="False"
          ></el-switch>
          <el-input-number v-else-if="scope.row.controlerType == 'TN'" v-model="scope.row.typeValue"></el-input-number>
          <el-input v-else v-model="scope.row.typeValue" placeholder="请输入内容" align="center" clearable></el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="50">
        <template slot-scope="scope">
          <el-tooltip content="保存">
            <i class="iconfont icon-edit" @click="updateSettingDescription(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import {
  //获取相应的配置信息
  GetSettingDescriptionBySettingType,
  //修改相关配置信息
  UpdateSettingDescription,
} from "@/api/SettingDescription";

export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      //表格数据
      tableData: [],
      //配置类型
      settingType: 0,
      loading: false,
    };
  },
  async mounted() {
    this.settingType = this.$route.query.settingType;
    //获取登录配置信息
    await this.getData();
  },
  methods: {
    /**
     * description: 获取登录配置信息
     * param {*} settingType
     * return {*}
     */
    async getData() {
      this.loading = true;
      await GetSettingDescriptionBySettingType({ settingType: this.settingType }).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.tableData = res.data;
        }
      });
    },
    /**
     * description: 修改登录配置信息
     * param {*} row
     * return {*}
     */
    async updateSettingDescription(row) {
      await UpdateSettingDescription(row).then((res) => {
        if (this._common.isSuccess(res)) {
          this.getData();
        }
      });
    },
  },
};
</script>

<style lang="scss">
</style>