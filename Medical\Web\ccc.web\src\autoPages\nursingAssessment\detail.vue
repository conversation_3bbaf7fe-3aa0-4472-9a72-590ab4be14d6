<!--
 * FilePath     : \src\autoPages\nursingAssessment\detail.vue
 * Author       : 苏军志
 * Date         : 2021-10-21 10:30
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-06-11 17:39
 * Description  : 护理评估详情页面
 * CodeIterationRecord: 2670-入院评估时间根据调整为根据配置决定 -杨欣欣
                        2799-作为护理人员，我需要跳转HIS病历关闭后，可以返回系统之前页面。 2022-07-24 杨欣欣
                        2855-作为IT人员，我需要依据提供的资料调整嘉会产科及新生儿科交接内容，以利于护理交班内容完整。 2022-08-24 杨欣欣
-->
<template>
  <base-layout class="assess-detail" header-height="auto">
    <div slot="header">
      <div class="assess-datetime">
        <span class="label">评估时间：</span>
        <el-date-picker
          v-model="assessDate"
          :picker-options="checkDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          class="assess-date"
          :disabled="disabled"
        ></el-date-picker>
        <el-time-picker
          v-model="assessTime"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          class="assess-time"
          :disabled="disabled"
        ></el-time-picker>
        <span class="label">{{ recordsInfo.description }}</span>
      </div>
      <div class="btn">
        <el-button
          type="primary"
          icon="iconfont icon-save-button"
          v-if="showEditButton && !onlyRead && !disabled"
          @click="save('S')"
        >
          保存
        </el-button>
        <el-button
          class="edit-button"
          icon="iconfont icon-temp-save"
          v-if="showEditButton && !onlyRead && !disabled"
          @click="save('T')"
        >
          暂存
        </el-button>
        <!-- 按钮嘉会使用 -->
        <el-button
          v-if="showRecordSwitch && !onlyRead"
          type="primary"
          icon="iconfont icon-chart"
          class="his-emr-button"
          @click="getHisDocument"
        >
          HIS病历
        </el-button>
        <el-button
          v-if="hisEmrButtonFlag && !onlyRead"
          type="primary"
          icon="iconfont icon-chart"
          class="his-emr-button"
          @click="getHisDocument()"
        >
          {{ hisEmrButton }}
        </el-button>
        <el-button
          v-if="holoEmrButtonFlag && !onlyRead"
          type="primary"
          icon="iconfont icon-chart"
          class="holo-emr-button"
          @click="getHisDocument('GetHoloDocumentUrl')"
        >
          {{ holoEmrButton }}
        </el-button>
        <lab-result-button v-if="!onlyRead" :case-number="patientInfo.caseNumber"></lab-result-button>
        <el-button class="print-button" icon="iconfont icon-back" @click="goBack">返回</el-button>
      </div>
    </div>
    <tabs-layout
      ref="tabsLayout"
      v-loading="loading"
      :element-loading-text="loadingText"
      :template-list="templateDatas"
      check-flag
      :showNav="true"
      :assessTime="selectTime"
      :model="type"
      @change-values="changeValues"
      @button-click="buttonClick"
      @button-record-click="buttonRecordClick"
      @checkTN="checkTN"
    />
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      fullscreen
      custom-class="link no-footer"
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
    </el-dialog>
    <!--弹出his病历页面-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="HIS病历"
      :visible.sync="showHis"
      fullscreen
      custom-class="link no-footer his-document"
    >
      <iframe v-if="showHis" ref="hisDialog"></iframe>
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonRecordTitle"
      :visible.sync="showButtonRecordDialog"
      custom-class="no-footer"
    >
      <risk-component :params="componentParams" @result="result"></risk-component>
    </el-dialog>
  </base-layout>
</template>

<script>
import {
  DeleteByNum,
  GetAdmissionAssessDate,
  GetAssessRecordsCodeByDeptID,
  GetAssessView,
  GetButtonData,
  SaveAssess,
} from "@/api/Assess";
import { GetHisDocumentUrl } from "@/api/Document";
import { GetDateTimeByAssessListID } from "@/api/PatientScore";
import { GetSettingValuesByTypeCodeAndValue, GetViewShow, GetOneSettingByTypeAndCode } from "@/api/Setting";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import baseLayout from "@/components/BaseLayout";
import tabsLayout from "@/components/tabsLayout/index";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import { SendRequestParams } from "@/utils/huiMei";
import { mapGetters } from "vuex";
import labResultButton from "@/components/button/labResultButton";
export default {
  components: {
    tabsLayout,
    baseLayout,
    riskComponent,
    labResultButton,
  },
  data() {
    return {
      showRecordSwitch: false,
      testFlag: false,
      type: "",
      num: 0,
      title: "",
      patient: {},
      inpatientID: "",
      assessMain: {},
      recordsInfo: {},
      recordsCode: "",
      templateDatas: [],
      contextValues: [],
      userID: "",
      testUrl: "",
      loading: false,
      loadingText: "",
      assessDate: "",
      assessTime: "",
      checkDate: {
        disabledDate: (time) => {
          return time.getTime() > new Date().getTime();
        },
      },
      buttonAssessListID: "",
      buttonName: "",
      showButtonDialog: false,
      checkTNFlag: true,
      // 是否可保存
      disabled: false,
      sort: 0,
      // 是否显示保存和暂存
      showEditButton: true,
      // 显示BR类弹窗
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR类弹窗所需参数
      componentParams: undefined,
      // BR项ID
      brAssessListID: undefined,
      // BR项修改序号
      sameTypeAssessListIDs: [],
      showHis: false,
      needCalcDateAssessListIDs: undefined,
      // 风险评估组件时间
      riskAssessDateTime: undefined,
      // 风险评估组件时间是否只读
      riskDateTimeReadonly: false,
      //操作功能是否开放，是否只读
      onlyRead: false,
      // 标记按钮跳转的页面是否使用评估时间
      useSourceAssessDateTime: false,
      // 上一次评估时间
      checkAssessDate: "",
      //入院评估标记
      admissionAssessFlag: false,
      //是否入院必须评估过敏的开关
      mustAssessAllergyFlag: false,
      // 中山护理评估中疼痛是1155，其他医院疼痛是1299
      painIDs: [1299, 1155],
      // 旧的模板是185，新的换成4570了
      allergyIDs: [185, 4570],
      //是否新打开页面显示检验报告
      showTestReportNewPageflag: false,
      holoEmrButton: "360视图",
      holoEmrButtonFlag: false,
      hisEmrButton: "HIS病历",
      hisEmrButtonFlag: false,
    };
  },
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      user: "getUser",
      token: "getToken",
    }),
    selectTime() {
      return this.assessDate + " " + this.assessTime;
    },
  },
  watch: {
    showButtonDialog(newVal, oldVal) {
      if (!newVal) {
        this.sameTypeAssessListIDs = [];
        this.updateButton(this.buttonAssessListID);
      }
    },
  },
  async created() {
    let paramsR = {
      settingTypeCode: "ViewDispable",
      typeValue: "CheckRcord",
    };
    await GetViewShow(paramsR).then((response) => {
      if (this._common.isSuccess(response)) {
        this.showRecordSwitch = response.data;
      }
    });
    let paramAllergy = {
      settingTypeCode: "AdmissionMustAssessAllergySwitch",
    };
    await GetSettingSwitchByTypeCode(paramAllergy).then((response) => {
      if (this._common.isSuccess(response)) {
        this.mustAssessAllergyFlag = response.data;
      }
    });
    let paramTest = {
      settingTypeCode: "ShowTestReportNewPageCode",
    };
    await GetSettingSwitchByTypeCode(paramTest).then((response) => {
      if (this._common.isSuccess(response)) {
        this.showTestReportNewPageflag = response.data;
      }
    });
    await this.getHoloDocumentFlag();
    await this.getHoloEmrTitle();
    await this.getHisDocumentFlag();
    await this.getHisEmrTitle();
    // 设置不可切换病人
    this._sendBroadcast("setPatientSwitch", false);
    // 第一次为入院评估
    this.patient = this.$route.params.patient;
    this.type = this.$route.params.type;
    this.num = this.$route.params.num;
    if (this.num == 1) {
      this.admissionAssessFlag = true;
    }
    this.inpatientID = this.$route.params.inpatientID;
    this.assessMain = this.$route.params.assessMain;
    this.sort = this.$route.params.sort;
    this.checkAssessDate = this.$route.params?.checkAssessDate;
    if (this.$route.query?.onlyRead) {
      this.onlyRead = true;
      this.disabled = true;
    }
    if (this.type == "add") {
      this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
      //如果是入院评估，那么根据配置决定是否使用入院患者事件的时间
      if (this.num === 1) {
        await this.setAssessDateBySetting();
      }
      //用户点击添加按钮时，更改为true
      this.showEditButton = true;
    } else {
      if (this.assessMain) {
        // 判断初始化评估模板的保存和暂存按钮是否显示
        await this.getEditAuthority();
        this.assessDate = this._datetimeUtil.formatDate(this.assessMain.assessDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(this.assessMain.assessTime, "hh:mm");
      }
    }
    this.getSwitchSetting("UseSourceAssessDateTime", (res) => (this.useSourceAssessDateTime = res));
    // 初始化评估模板
    await this.initTemplate();
  },
  methods: {
    /**
     * description: 初始化评估模板
     * return {*}
     */
    async initTemplate() {
      this.loading = true;
      this.loadingText = "加载中……";
      let mappingType = "";
      if (this.num == 1) {
        mappingType = "AdmissionAssess";
      } else {
        // 普通的评估
        mappingType = "PhysicalAssess";
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        age: this.patient.age,
        mappingType: mappingType,
      };
      let assessParams = {
        age: this.patient.age,
        departmentListID: this.patient.departmentListID,
        num: this.num,
      };
      if (this.type == "add") {
        assessParams.mainID = "";
        params.departmentListID = this.patient.departmentListID;
      } else {
        assessParams.mainID = this.assessMain.id;
        params.departmentListID = this.assessMain.departmentListID;
        params.recordsCode = this.assessMain.recordsCode;
      }
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.recordsInfo = result.data;
          this.recordsCode = this.recordsInfo.recordsCode;
        }
      });
      assessParams.inpatientID = this.inpatientID;
      assessParams.gender = this.patient.genderCode;
      assessParams.recordsCode = this.recordsCode;
      assessParams.dateOfBirth = this.patient.dateOfBirth;
      assessParams.sourceID = this.num + "";
      assessParams.sourceType = "AssessBR";
      assessParams.stationID = this.patient.stationID;
      // 获取评估模板
      await GetAssessView(assessParams).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 获取HIS病历
     * param {*}
     * return {*}
     */
    getHisDocument(buttonCode) {
      let params = {
        chartNo: this.patientInfo.chartNo,
        numberOfAdmissions: 0,
        caseNumber: this.patientInfo.caseNumber,
        buttonCode: buttonCode,
      };
      GetHisDocumentUrl(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.url) {
            let url = "";
            if (result.data.type == "file") {
              // 跳转文件预览画面 传参数
              let fileList = [result.data.url];
              url = "/filePreview?isDialog=true&fileList=" + JSON.stringify(fileList);
            } else {
              // 是网址 直接跳转
              url = result.data.url;
            }
            if (result.data.target == "dialog") {
              this.showHis = true;
              this.$nextTick(() => {
                this.$refs.hisDialog.contentWindow.location.replace(url);
              });
            } else {
              let newWindow = window.open(url);
              newWindow.onload = () => {
                newWindow.document.title = "HIS病历";
              };
            }
          }
        }
      });
    },
    /**
     * description: 获取修改、删除权限
     * return {*}
     */
    async getEditAuthority() {
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(this.assessMain.id, "PatientAssessMain");
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
    },
    /**
     * description: 返回到上一级
     * return {*}
     */
    goBack() {
      if (this.type == "add") {
        // 如果有串导管/伤口产生的暂存护理评估，在返回时删除
        let params = {
          inpatientID: this.inpatientID,
          num: this.num,
        };
        this.loading = true;
        this.loadingText = "处理中……";
        // 有则删除，没有返回true
        DeleteByNum(params).then((ret) => {
          this.loading = false;
          this.$router.go(-1);
        });
      } else {
        this.$router.go(-1);
      }
    },
    /**
     * description: 更新按钮角标
     * param {*} assessListID 按钮评估流水号
     * return {*}
     */
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      // 判断是否为过敏按钮，返回刷新病人头 显示新增过敏药物
      if (this.allergyIDs.includes(assessListID) && item.assessValue) {
        this._sendBroadcast("refreshInpatient");
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          let sameTypeAssessListIDs = this.$refs.tabsLayout.updateButtonItem(item);
          if (!this.sameTypeAssessListIDs || this.sameTypeAssessListIDs.length <= 0) {
            this.sameTypeAssessListIDs = sameTypeAssessListIDs;
          }
          // 相同的专项更新页签
          if (this.sameTypeAssessListIDs?.length) {
            this.sameTypeAssessListIDs.forEach((assessListID) => {
              this.updateButton(assessListID);
            });
          }
        }
      });
    },
    /**
     * description: 获取按钮值
     * param {*} assessListID 按钮评估流水号
     * return {*}
     */
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.inpatientID,
        recordsCode: this.recordsInfo.recordsCode,
        assessListID: assessListID,
        sourceID: this.num + "",
        sourceType: this.brAssessListID ? "AssessBR_" + this.brAssessListID : "",
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: 选中数据修改回调
     * param {*} data 调整后的选中数据
     * return {*}
     */
    changeValues(data) {
      this.assessDatas = data;
    },
    /**
     * description: TN类型项目修改回调
     * param {*} flag TN类型项目检核结果
     * return {*}
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 按钮点击回调
     * param {*} content 当前点击项目
     * return {*}
     */
    buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      if (url.indexOf("?") == -1) {
        url += "?num=" + this.num;
      } else {
        url += "&num=" + this.num;
      }
      url +=
        `&sort=${this.sort}&bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}` +
        `&userID=${this.user.userID}&token=${this.token}&isDialog=true`;
      // 根据配置决定是否传递评估时间
      if (this.useSourceAssessDateTime) {
        const assessDate = this._datetimeUtil.formatDate(this.assessDate, "yyyy-MM-dd");
        const assessTime = this._datetimeUtil.formatDate(this.assessTime, "hh:mm");
        url += `&sourceAssessDate=${assessDate}&sourceAssessTime=${assessTime}`;
      }
      this.showButtonDialog = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 风险按钮点击回调
     * param {*} content 当前点击项目
     * return {*}
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      await this.getScheduleDateTime();
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      this.componentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: "AssessBR_" + this.brAssessListID,
        sourceID: this.num + "",
        assessTime: this.riskAssessDateTime,
        assessTimeReadonly: this.riskDateTimeReadonly,
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    result(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.sameTypeAssessListIDs = [];
        this.updateButton(this.brAssessListID);
      }
    },
    /**
     * description: 获取需要保存的明细
     * param {*} type 类别，标记是否是暂存
     * return {*}
     */
    getSaveDetail(type) {
      // 如果不是暂存
      if (type == "S" && this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return undefined;
      }
      let details = [];
      let flag = true;
      for (let i = 0; i < this.assessDatas.length; i++) {
        let content = this.assessDatas[i];
        // 按钮不处理
        if (content.controlerType.trim() == "B") {
          continue;
        }
        let result = this._common.checkAssessTN(content);
        if (!result.flag) {
          flag = false;
          break;
        }
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        details.push(detail);
      }
      if (!flag) {
        return [];
      }
      return details;
    },
    /**
     * description: 保存
     * param {*} type 类别，标记是否是暂存
     * return {*}
     */
    async save(type) {
      // 只有新增入院护理评估保存时才检核疼痛和过敏
      if (type === "S" && this.admissionAssessFlag) {
        // 判断入院评估的疼痛和过敏是否有值
        for (let i = 0; i < this.templateDatas.length; i++) {
          for (let j = 0; j < this.templateDatas[i].groups.length; j++) {
            if (
              this.templateDatas[i].groups[j].contents.find(
                (m) => this.painIDs.includes(m.assessListID) && !m.assessValue
              )
            ) {
              this._showTip("warning", "【入院资料-疼痛】项目为必选项，但无评估内容");
              return;
            }
            if (
              this.templateDatas[i].groups[j].contents.find(
                (m) => this.allergyIDs.includes(m.assessListID) && !m.assessValue
              ) &&
              this.mustAssessAllergyFlag
            ) {
              this._showTip("warning", "【既往史-过敏史】项目为必选项，但无评估内容");
              return;
            }
          }
        }
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      if (this.loading) {
        return;
      }
      if (type === "T" && this.assessMain && this.assessMain.tempSaveMark === "S") {
        this._showTip("warning", "已评估的内容不允许暂存！");
        return;
      }
      if (!this.check()) {
        return;
      }
      // 明细表数据
      let details = this.getSaveDetail(type);
      if (!details || details.length <= 0) {
        return;
      }
      this.loading = true;
      this.loadingText = "保存中……";
      let assess = {
        InterventionMainID: this.recordsInfo.interventionMainID,
        details: details,
      };
      let mains = [];
      let main = {};
      // 主表数据
      if (this.assessMain) {
        // 修改
        main = this.assessMain;
      } else {
        // 新增
        main.NumberOfAssessment = this.num;
        main.InpatientID = this.patient.inpatientID;
        main.PatientID = this.patient.patientID;
        main.StationID = this.patient.stationID;
        main.BedID = this.patient.bedID;
        main.CaseNumber = this.patient.caseNumber;
        main.ChartNo = this.patient.chartNo;
        main.BedNumber = this.patient.bedNumber;
        main.DeleteFlag = "";
        main.emrFlag = null;
        main.NursingLevel = this.patient.nursingLevel;
        main.Sort = this.sort;
      }
      main.RecordsCode = this.recordsCode;
      main.AssessDate = this.assessDate;
      main.AssessTime = this.assessTime;
      mains.push(main);
      assess.mains = mains;
      main.tempSaveMark = type;
      return SaveAssess(assess).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          if (type == "S") {
            this._sendBroadcast("refreshInpatient");
            //正常跳转到风险评分页面
            this.$router.push({ path: "/nursingPlan?index=0" });
          } else {
            this.$router.replace({ path: "/assess" });
          }
          //向惠每发送智能推荐业务请求
          SendRequestParams();
        }
      });
    },
    /**
     * description: 根据配置设置入院护理评估时间
     * param {*}
     * return {*}
     */
    async setAssessDateBySetting() {
      await GetAdmissionAssessDate({ inpatientID: this.inpatientID }).then((result) => {
        if (this._common.isSuccess(result)) {
          this.assessDate = this._datetimeUtil.formatDate(result.data, "yyyy-MM-dd");
          this.assessTime = this._datetimeUtil.formatDate(result.data, "hh:mm");
        }
      });
    },
    /**
     * description: 检查此AssessListID是否要计算评估时间
     * param {*} assessListID
     * return {*}
     */
    async checkScheduleDateNeedCalc(assessListID) {
      if (!this.needCalcDateAssessListIDs) {
        const params = {
          settingTypeCode: "CalcDateTimeByAssessListID",
          typeValue: "Risk",
        };
        await GetSettingValuesByTypeCodeAndValue(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.needCalcDateAssessListIDs = result.data;
          }
        });
      }
      return (this.needCalcDateAssessListIDs || []).some((m) => m == assessListID);
    },
    /**
     * description: 根据AssessListID获取风险表评估时间
     * param {*}
     * return {*}
     */
    async getScheduleDateTime() {
      if (!(await this.checkScheduleDateNeedCalc(this.brAssessListID))) {
        return;
      }
      let scheduleDateTime = undefined;
      // 默认为护理评估时间
      this.riskAssessDateTime = `${this.assessDate} ${this.assessTime}`;
      const params = {
        inpatientID: this.patientInfo.inpatientID,
        patientID: this.patientInfo.patientID,
        assessListID: this.brAssessListID,
      };
      await GetDateTimeByAssessListID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          scheduleDateTime = result.data;
        }
      });
      // 依据接口返回值调整：是否只读、风险评估时间
      this.riskDateTimeReadonly = !!scheduleDateTime;
      if (scheduleDateTime) {
        this.riskAssessDateTime = scheduleDateTime;
      }
    },
    /**
     * description: 解构获取回传的时间
     * param {*} assessDate 评估日期
     * param {*} assessTime 评估时间
     * return {*}
     */
    getAssessDateTime({ assessDate, assessTime }) {
      this.riskAssessDateTime = `${assessDate} ${assessTime}`;
    },
    /**
     * description: 获取开关配置
     * param {*} settingTypeCode 配置码
     * param {*} callBack 回调函数，请求返回成功后执行
     * return {*}
     */
    getSwitchSetting(settingTypeCode, callBack) {
      GetSettingSwitchByTypeCode({ settingTypeCode: settingTypeCode }).then((res) => {
        if (this._common.isSuccess(res)) {
          callBack(res.data);
        }
      });
    },
    /**
     * description: 保存检核
     * return {*}
     */
    check() {
      if (!this.checkAssessDate) {
        return true;
      }
      let assessDataTime = `${this._datetimeUtil.formatDate(
        this.assessDate,
        "yyyy-MM-dd"
      )} ${this._datetimeUtil.formatDate(this.assessTime, "hh:mm")}`;
      if (this._datetimeUtil.getTimeDifference(this.checkAssessDate, assessDataTime, undefined, "M") < 0) {
        this._showTip("warning", "评估时间不可以早于上一次护理评估时间，可以前往补录页面调整！");
        return false;
      }
      return true;
    },
    /**
     * description: 获取360全息视图是否开启
     * param {*}
     * return {*}
     */
    async getHoloDocumentFlag() {
      let params = {
        settingType: 163,
        settingCode: "GetHoloDocument",
      };
      await GetOneSettingByTypeAndCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data.typeValue == "True") {
            this.holoEmrButtonFlag = true;
          }
        }
      });
    },
    /**
     * description: 360全息视图按钮标题配置
     * param {*}
     * return {*}
     */
    async getHoloEmrTitle() {
      let parameter = {
        settingType: 163,
        settingCode: "HoloButtonTitle",
      };
      GetOneSettingByTypeAndCode(parameter).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.typeValue) {
            this.holoEmrButton = result.data.typeValue;
          }
        }
      });
    },
    /**
     * description: 获取HIS病历是否开启
     * param {*}
     * return {*}
     */
    async getHisDocumentFlag() {
      let params = {
        settingType: 163,
        settingCode: "GetHisDocument",
      };
      await GetOneSettingByTypeAndCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data.typeValue == "True") {
            this.hisEmrButtonFlag = true;
          }
        }
      });
    },
    /**
     * description: HIS病历按钮标题配置
     * param {*}
     * return {*}
     */
    async getHisEmrTitle() {
      let parameter = {
        settingType: 163,
        settingCode: "HISButtonTitle",
      };
      GetOneSettingByTypeAndCode(parameter).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.typeValue) {
            this.hisEmrButton = result.data.typeValue;
          }
        }
      });
    },
  },
};
</script>
<style lang="scss">
.assess-detail {
  .base-header {
    .assess-datetime {
      float: left;
      .assess-date {
        width: 155px;
      }
      .assess-time {
        width: 100px;
      }
      .label {
        margin-left: 10px;
      }
    }
    .btn {
      float: right;
      .his-emr-button {
        border-color: #ef946c;
        background-color: #ef946c;
      }
      .holo-emr-button {
        border-color: #7b7de3;
        background-color: #7b7de3;
      }
    }
  }
  iframe {
    height: 99%;
    width: 100%;
    border: 0;
    overflow: hidden;
  }
  .lab iframe {
    overflow: auto;
  }
  .el-dialog.no-footer.link .el-dialog__body {
    height: calc(100% - 35px);
    background-color: #f3f3f3;
  }
  .his-document.el-dialog {
    .el-dialog__body {
      padding: 0;
      iframe {
        height: 99%;
        width: 100%;
        border: 0;
      }
    }
  }
}
</style>
