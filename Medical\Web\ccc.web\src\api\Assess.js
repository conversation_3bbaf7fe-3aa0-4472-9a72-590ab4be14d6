import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/assess";

export const urls = {
  GetSettingDescriptionInfo: baseUrl + "/GetSettingDescriptionInfo",
  GetAssessMainInfo: baseUrl + "/GetAssessMainInfo",
  GetAssessDetailInfoByMainID: baseUrl + "/GetAssessDetailInfoByMainID",
  DeleteByNum: baseUrl + "/DeleteByNum",
  DeleteItemByID: baseUrl + "/DeleteItemByID",
  PatientProblemAssessCheck: baseUrl + "/PatientProblemAssessCheck",
  SaveAssess: baseUrl + "/SaveAssess",
  GetPatientDataByBedNumber: baseUrl + "/GetPatientDataByBedNumber",
  GetDataByPatientAssess: "/PatientProfile/Profile/GetDataByPatientAssess",
  GetNowShiftByStationIDAsync: "/setting/GetNowShiftByStationIDAsync",
  GetAssessRecordsCodeByDeptID: baseUrl + "/GetAssessRecordsCodeByDeptID",
  GetRecordsCodeInfoByRecordCode: baseUrl + "/GetRecordsCodeInfoByRecordcode",
  GetAssessInteractionByID: baseUrl + "/GetAssessInteractionByID",
  GetAssessView: baseUrl + "/GetAssessView",
  GetCalcRiskAssessTotal: baseUrl + "/CalcRiskAssessTotal",
  //从后台获取验证用数据
  GetTextEntryByAssessListID: "/PatientProfile/GetTextEntryByAssessListID",
  GetMainByInpatient: baseUrl + "/GetMainByInpatient",
  //获取评估模板中按钮的值
  GetButtonData: baseUrl + "/GetButtonData",
  // 检核护理评估对应的护理问题是否已展开排程
  CheckScheduleByAssessMainID: baseUrl + "/CheckScheduleByAssessMainID",
  // 获取最后一次评估
  GetLastMain: baseUrl + "/GetLastMain",
  PatientIsAssess: baseUrl + "/PatientIsAssess",
  GetPatientAssessMainHistoryListByMainID:
    baseUrl + "/GetPatientAssessMainHistoryListByMainID",
  //获取ClinicalSetting数据（根据SettingTypeCode）
  GetClinicalSettingInfo: baseUrl + "/GetClinicalSettingInfo",
  GetAssessContentInHospital: baseUrl + "/GetAssessContentInHospital",
  GetPatientAssessMainHistoryList: baseUrl + "/GetPatientAssessMainHistoryList",
  SaveAssessHistory: baseUrl + "/SaveAssessHistory",
  GetAssessMainRow: baseUrl + "/GetAssessMainRow",
  // 更新患者评估流程码
  SuppleNursingProcedureCode: baseUrl + "/SuppleNursingProcedureCode",
  //
  SaveAssessSupplement: baseUrl + "/SaveAssessSupplement",
  GetAssessMaxSort: baseUrl + "/GetAssessMaxSort",
  // GetAdditionalAssess: baseUrl + "/",
  UpdateSupplementAssess: baseUrl + "/UpdateSupplementAssess",
  //获取非暂存评估列表
  GetAdditionalAssessMainInfo: baseUrl + "/GetAdditionalAssessMainInfo",
  GetPatientIsTransOut: baseUrl + "/GetPatientIsTransOut",
  //评估补录删除
  DelRecordItemByID: baseUrl + "/DelRecordItemByID",
  //风险评分
  RiskAssess: baseUrl + "/RiskAssess",
  // 获取BD类模板
  GetBDTemplate: baseUrl + "/GetBDTemplate",
  // 获取评估模板 测试使用
  GetTestAssessView: baseUrl + "/GetTestAssessView",
  GetRiskSaveCheckTime: baseUrl + "/GetRiskSaveCheckTime",
  GetAdmissionAssessDate: baseUrl + "/GetAdmissionAssessDate",
  PatientEventAssessCheckAsync: baseUrl + "/PatientEventAssessCheckAsync",
};

// 根据科室获取对应评估RecordsCode信息
export const GetAssessRecordsCodeByDeptID = (params) => {
  return http.get(urls.GetAssessRecordsCodeByDeptID, params);
};
// 根据RecordsCode获取对应评估RecordsCode信息
export const GetRecordsCodeInfoByRecordCode = (params) => {
  return http.get(urls.GetRecordsCodeInfoByRecordCode, params);
};
export const GetTextEntryByAssessListID = (params) => {
  return http.get(urls.GetTextEntryByAssessListID, params);
};
// 透过单位序获取单前班别信息
export const GetNowShiftByStationIDAsync = () => {
  return http.get(urls.GetNowShiftByStationIDAsync);
};
// 取得Profile资料
export const GetDataByPatientAssess = (params) => {
  return http.get(urls.GetDataByPatientAssess, params);
};
// 获取SettingDescription数据（根据SettingTypeCode）
export const GetSettingDescriptionInfo = (params) => {
  return http.get(urls.GetSettingDescriptionInfo, params);
};
// 根据病人获取评估主表
export const GetAssessMainInfo = (params) => {
  return http.get(urls.GetAssessMainInfo, params);
};
// 根据病人评估主表ID获取评估详细表
export const GetAssessDetailInfoByMainID = (params) => {
  return http.get(urls.GetAssessDetailInfoByMainID, params);
};
// 根据病人序号和评估次数删除暂存评估记录
export const DeleteByNum = (params) => {
  return http.post(urls.DeleteByNum, qs.stringify(params));
};
// 删除暂存数据
export const DeleteItemByID = (params) => {
  return http.post(urls.DeleteItemByID, qs.stringify(params));
};
// 病人评估在问题里是否结束
export const PatientProblemAssessCheck = (params) => {
  return http.get(urls.PatientProblemAssessCheck, params);
};
// 保存评估
export const SaveAssess = (params) => {
  return http.post(urls.SaveAssess, params);
};

//取得评估互斥
export const GetAssessInteractionByID = (params) => {
  return http.get(urls.GetAssessInteractionByID, params);
};
//取得评估模板
export const GetAssessView = (params) => {
  return http.get(urls.GetAssessView, params);
};

//取得评估汇总
export const GetCalcRiskAssessTotal = (params) => {
  return http.get(urls.GetCalcRiskAssessTotal, params);
};

//取得评估汇总
export const RiskAssess = (params) => {
  return http.get(urls.RiskAssess, params);
};

export const GetMainByInpatient = (params) => {
  return http.get(urls.GetMainByInpatient, params);
};
export const GetButtonData = (params) => {
  return http.get(urls.GetButtonData, params);
};
export const CheckScheduleByAssessMainID = (params) => {
  return http.get(urls.CheckScheduleByAssessMainID, params);
};
export const GetLastMain = (params) => {
  return http.get(urls.GetLastMain, params);
};
export const PatientIsAssess = (params) => {
  return http.get(urls.PatientIsAssess, params);
};
export const GetPatientAssessMainHistoryListByMainID = (params) => {
  return http.get(urls.GetPatientAssessMainHistoryListByMainID, params);
};
//获取ClinicalSetting数据（根据SettingTypeCode）
export const GetClinicalSettingInfo = (params) => {
  return http.get(urls.GetClinicalSettingInfo, params);
};
export const GetAssessContentInHospital = (params) => {
  return http.get(urls.GetAssessContentInHospital, params);
};
export const GetPatientAssessMainHistoryList = (params) => {
  return http.get(urls.GetPatientAssessMainHistoryList, params);
};
export const GetAssessMainRow = (params) => {
  return http.get(urls.GetAssessMainRow, params);
};
export const SaveAssessHistory = (params) => {
  return http.post(urls.SaveAssessHistory, params);
};
export const SuppleNursingProcedureCode = (params) => {
  return http.post(urls.SuppleNursingProcedureCode, qs.stringify(params));
};
//护理评估补录保存
export const SaveAssessSupplement = (params) => {
  return http.post(urls.SaveAssessSupplement, params);
};
//根据inpatietID获取评估序号最大值
export const GetAssessMaxSort = (params) => {
  return http.get(urls.GetAssessMaxSort, params);
};
// //根据inpatietID获取护理评估补录
// export const GetAdditionalAssess = params => {
//   return http.get(urls.GetAdditionalAssess, params);
// };
//根据inpatietID获取护理评估补录
export const UpdateSupplementAssess = (params) => {
  return http.post(urls.UpdateSupplementAssess, params);
};

//获取非暂存数据
export const GetAdditionalAssessMainInfo = (params) => {
  return http.get(urls.GetAdditionalAssessMainInfo, params);
};

//获取病人是否转科或者是否有入院评估
export const GetPatientIsTransOut = (params) => {
  return http.get(urls.GetPatientIsTransOut, params);
};

//评估补录删除
export const DelRecordItemByID = (params) => {
  return http.post(urls.DelRecordItemByID, qs.stringify(params));
};
// 获取BD类模板
export const GetBDTemplate = (params) => {
  return http.get(urls.GetBDTemplate, params);
};
//获取评估模板 测试使用
export const GetTestAssessView = (params) => {
  return http.get(urls.GetTestAssessView, params);
};
export const GetRiskSaveCheckTime = (params) => {
  return http.get(urls.GetRiskSaveCheckTime, params);
};
//获取默认的入院评估时间
export const GetAdmissionAssessDate = (params) => {
  return http.get(urls.GetAdmissionAssessDate, params);
};
//检核患者是否需要再次评估
export const PatientEventAssessCheckAsync = (params) => {
  return http.get(urls.PatientEventAssessCheckAsync, params);
};
