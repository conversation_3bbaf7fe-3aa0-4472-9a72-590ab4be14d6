/*
 * FilePath     : \src\api\DietIntake.js
 * Author       : 郭鹏超
 * Date         : 2021-07-29 18:53
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:44
 * Description  :饮食记录API
 */
import http from "../utils/ajax";
const baseUrl = "/DietIntake";

const urls = {
  GetDietIntakeAssesssView: baseUrl + "/GetDietIntakeAssesssView",
  DietIntakeSave: baseUrl + "/DietIntakeSave",
  GetDietIntakeTableView: baseUrl + "/GetDietIntakeTableView",
  DeleteDietIntake: baseUrl + "/DeleteDietIntake"
};
//获取评估模板
export const GetDietIntakeAssesssView = params => {
  return http.get(urls.GetDietIntakeAssesssView, params);
};
//饮食记录保存
export const DietIntakeSave = params => {
  return http.post(urls.DietIntakeSave, params);
};
//获取饮食记录表格数据
export const GetDietIntakeTableView = params => {
  return http.get(urls.GetDietIntakeTableView, params);
};
//删除饮食记录
export const DeleteDietIntake = params => {
  return http.get(urls.DeleteDietIntake, params);
};
