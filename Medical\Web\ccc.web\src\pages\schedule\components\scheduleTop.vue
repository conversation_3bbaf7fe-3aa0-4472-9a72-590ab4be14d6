<template>
  <div class="schedule-top">
    <div class="switch">
      全科：
      <el-switch v-model="switchAll" />
    </div>
    <div class="switch" v-if="!isBatch">
      有排程：
      <el-switch v-model="switchSchedule" />
    </div>
    <div class="switch">
      隐藏：
      <el-switch v-model="switchHide" />
    </div>
    <div class="switch" v-if="notPerformSwitch">
      未执行：
      <el-switch v-model="switchPerform" />
    </div>

    <div class="switch">
      班别日期：
      <el-date-picker
        type="date"
        placeholder="选择班别日期"
        v-model="tempShiftTime"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        style="width: 110px"
      ></el-date-picker>
    </div>
    <div class="switch">
      <shift-selector
        width="100"
        v-model="tempShift"
        :stationID="stationID"
        @select-item="changeShift"
      ></shift-selector>
    </div>
    <div class="switch" v-if="isBatch">
      <shift-times-selector width="110" v-model="shiftTimes" :shiftID="tempShift"></shift-times-selector>
    </div>
    <div class="switch">
      床号：
      <el-input
        class="bed-number"
        v-model="bedNumber"
        @keyup.enter.native="changeBadNumber"
        @clear="changeBadNumber"
        clearable
        placeholder="请输入床号"
      >
        <i slot="append" class="iconfont icon-search" @click="changeBadNumber"></i>
      </el-input>
    </div>
  </div>
</template>

<script>
import shiftSelector from "@/components/selector/shiftSelector";
import shiftTimesSelector from "@/components/selector/shiftTimesSelector";
import { GetOneSettingByTypeAndCode } from "@/api/Setting";
export default {
  components: { shiftSelector, shiftTimesSelector },
  props: {
    // 是否是批量
    isBatch: {
      type: Boolean,
      default: false,
    },
    shiftTime: {},
    shift: {
      type: Number,
    },
    stationID: {
      type: Number,
    },
    defaultBedNumber: {
      type: String,
    },
    hide: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    hide() {
      this.switchHide = this.hide;
    },
    switchAll(newValue) {
      this.$emit("switch-all", newValue);
      this.$emit("switch-hide", false);
      this.$emit("switch-schedule", false);
    },
    switchSchedule(newValue) {
      this.$emit("switch-schedule", newValue);
      this.$emit("switch-hide", false);
    },
    switchHide(newValue) {
      this.$emit("switch-hide", newValue);
    },
    switchPerform(newValue) {
      this.$emit("switch-perform", newValue);
    },
    tempShiftTime(newValue) {
      this.$emit("change-shift-time", newValue);
    },
    shiftTimes(newValue) {
      if (!newValue) return;
      this.$emit("change-shiftTimes", newValue);
    },
    shiftTime(newValue) {
      this.tempShiftTime = newValue;
    },
    shift: {
      immediate: true,
      handler(newValue) {
        this.tempShift = newValue;
      },
    },
    defaultBedNumber(newValue) {
      this.bedNumber = newValue;
      this.changeBadNumber();
    },
  },
  data() {
    return {
      switchAll: false,
      switchSchedule: false,
      switchHide: false,
      tempShiftTime: undefined,
      tempShift: "",
      shiftTimes: "",
      bedNumber: undefined,
      switchPerform: false,
      notPerformSwitch: false,
    };
  },
  created() {
    this.getNotPerformSwitch();
  },
  methods: {
    changeShift(shift) {
      this.$emit("change-shift", shift);
    },
    changeBadNumber() {
      this.$emit("switch-hide", false);
      this.$emit("change-bedNumber", this.bedNumber);
    },
    async getNotPerformSwitch() {
      let params = {
        settingType: 277,
        settingCode: "NotPerformSwitch",
      };
      await GetOneSettingByTypeAndCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data.typeValue == "True") {
            this.notPerformSwitch = true;
          }
        }
      });
    },
  },
};
</script>

<style lang="scss">
.schedule-top {
  width: 100%;
  .switch {
    margin-right: 5px;
    display: inline-block;
    .bed-number.el-input {
      width: 110px;
      .el-input__inner {
        padding: 0 5px;
      }
      .el-input-group__append {
        padding: 0 5px;
      }
      i {
        color: #8cc63e;
      }
    }
  }
}
</style>
