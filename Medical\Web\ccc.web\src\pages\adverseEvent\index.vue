<!--
 * FilePath     : \ccc.web\src\pages\adverseEvent\index.vue
 * Author       : 苏军志
 * Date         : 2021-09-13 10:09
 * LastEditors  : 马超
 * LastEditTime : 2022-03-28 15:18
 * Description  : 不良事件查看--目前包含：跌倒、压力性损伤、非计划拔管
-->
<template>
  <base-layout class="adverse-event" v-loading="loading" element-loading-text="加载中……">
    <div slot="header">
      <span class="label">开始日期:</span>
      <el-date-picker
        v-model="startDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="pickerOptionsStart"
      ></el-date-picker>
      <span class="label">结束日期:</span>
      <el-date-picker
        v-model="endDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 110px"
        placeholder="选择日期"
        :picker-options="pickerOptionsEnd"
      ></el-date-picker>
      <span class="label">分类:</span>
      <el-select v-model="adverseEventType" placeholder="选择分类" style="width: 120px">
        <el-option
          v-for="(type, index) in adverseEventTypes"
          :key="index"
          :label="type.description.trim()"
          :value="type.settingValue.trim()"
        ></el-option>
      </el-select>
      <span class="label">病区:</span>
      <station-selector
        v-model="stationID"
        label=""
        width="160"
        :hospitalFlag="true"
        @select-item="selectStation"
      ></station-selector>
      <el-button class="query-button" icon="iconfont icon-search" @click="getAdverseEvent">查询</el-button>
      <div class="top-btn">
        <el-button class="print-button" icon="iconfont icon-arrow-download" @click="exportToExcel()">导出</el-button>
      </div>
    </div>
    <el-table :data="tableData" border stripe height="100%" ref="tableData">
      <el-table-column
        v-for="(column, index) in tableColumns"
        :key="index"
        :label="column.title"
        :prop="column.name"
        :min-width="column.width ? column.width : 100"
        align="center"
      ></el-table-column>
    </el-table>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import { StaticAdverseEvent } from "@/api/AdverseEvent";
import { GetAdverseEventTypes } from "@/api/Setting";
import { export_json_to_excel } from "@/vendor/Export2Excel.js";
export default {
  components: { baseLayout, stationSelector },
  data() {
    let that = this;
    return {
      loading: false,
      // 病区，默认全院
      stationID: 999999,
      adverseEventTypes: [],
      adverseEventType: undefined,
      tableData: [],
      tableColumns: [],
      columns: [],
      stationName: "",
      typeName: "",
      startDate: undefined,
      endDate: undefined,
      //时间的禁用
      pickerOptionsStart: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.endDate).getTime();
        },
      },
      pickerOptionsEnd: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.startDate).getTime() - 8.64e7;
        },
      },
    };
  },
  watch: {
    adverseEventType(newValue) {
      let type = this.adverseEventTypes.find((type) => {
        return type.settingValue.trim() == newValue;
      });
      if (event) {
        this.typeName = type.description.trim();
      }
    },
  },
  created() {
    this.getAdverseEventTypes();
  },
  methods: {
    getAdverseEventTypes() {
      this.adverseEventTypes = [];
      GetAdverseEventTypes().then((result) => {
        if (this._common.isSuccess(result)) {
          this.adverseEventTypes = result.data;
        }
      });
    },
    getAdverseEvent() {
      if (this.loading) {
        return;
      }
      if (!this.startDate || !this.endDate) {
        this._showTip("warning", "请选择正确的时间区间！");
        return;
      }
      if (!this.adverseEventType) {
        this._showTip("warning", "请选择不良事件分类！");
        return;
      }
      this.tableData = [];
      this.columns = [];
      this.loading = true;
      let params = {
        startDate: this.startDate,
        endDate: this.endDate,
        type: this.adverseEventType,
        stationID: this.stationID,
      };
      StaticAdverseEvent(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data && res.data.rows) {
            this.tableData = res.data.rows;
            this.columns = res.data.columns;
            this.tableColumns = this.columns.filter((column) => {
              return column.isTitle;
            });
            this.$nextTick(() => {
              this.$refs.tableData.doLayout();
            });
          } else {
            this._showTip("warning", "选择的时间区段内没有数据！");
          }
        }
      });
    },
    selectStation(station) {
      this.stationName = station.stationName;
    },
    exportToExcel() {
      if (!this.tableData || this.tableData.length <= 0) {
        this._showTip("warning", "请先查询数据！");
        return;
      }
      let excelColumns = [];
      let excelFileds = [];
      let columnsFiled = [
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
        "U",
        "V",
        "W",
        "X",
        "Y",
        "Z",
        "AA",
        "AB",
        "AC",
      ];
      let merges = [];
      // 合并列数
      let mergeTimes = 0;
      for (let i = 0; i < this.columns.length; i++) {
        let column = this.columns[i];
        if (column.isTitle) {
          if (column.name.indexOf("_merge") == -1) {
            excelColumns.push(column.title);
            excelFileds.push(column.name);
          } else {
            excelColumns.push(column.title);
            if (column.colSpan) {
              let span = column.colSpan - 1;
              let start = i - mergeTimes;
              let item = columnsFiled[start] + "1:" + columnsFiled[start + span] + "1";
              merges.push(item);
              mergeTimes++;
              for (let j = 0; j < span; j++) {
                excelColumns.push("");
              }
            }
          }
        } else {
          excelFileds.push(column.name);
        }
      }
      let excelName = this.stationName + this.typeName + "不良事件_" + this.startDate + "至" + this.endDate;
      let dataMain = this.formatJson(excelFileds, this.tableData);
      export_json_to_excel(excelColumns, dataMain, excelName, merges, true);
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((jsonDataItem) =>
        filterVal.map((filterValItem) => {
          return jsonDataItem[filterValItem];
        })
      );
    },
  },
};
</script>

<style lang="scss">
.adverse-event {
  .label,
  .query-button {
    margin-left: 5px;
  }
  .top-btn {
    float: right;
  }
}
</style>