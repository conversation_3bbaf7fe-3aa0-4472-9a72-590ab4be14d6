<!--
 * FilePath     : \src\autoPages\recordSupplement\components\giveMedicineRecord.vue
 * Author       : AI Assistant
 * Date         : 2025-07-21
 * LastEditors  : AI Assistant
 * LastEditTime : 2025-07-21
 * Description  : 给药记录补录页面
 * CodeIterationRecord:
 -->
<template>
  <div class="give-medicine-record-wrapper">
    <base-layout class="give-medicine-record" header-height="auto">
      <div slot="header" class="give-medicine-record-header">
        <div class="header-controls">
          <span class="label">执行日期:</span>
          <el-date-picker
            v-model="performDate"
            type="date"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="选择日期"
            class="date-select"
            @change="getTableData"
          ></el-date-picker>
          <el-button
            type="primary"
            class="add-button give-medicine-record-header-add-button"
            @click="recordAddOrUpdate()"
            icon="iconfont icon-add"
          >
            新增
          </el-button>
        </div>
      </div>
      <div class="give-medicine-record-content">
        <el-table ref="giveMedicineTable" height="100%" :data="tableData" border stripe>
          <el-table-column prop="performDate" label="日期" :width="convertPX(100)" align="center"></el-table-column>
          <el-table-column prop="category" label="类别" :width="convertPX(80)" align="center"></el-table-column>
          <el-table-column prop="groupNumber" label="组号" :width="convertPX(80)" align="center"></el-table-column>
          <el-table-column
            prop="orderContent"
            label="医嘱内容"
            :width="convertPX(200)"
            header-align="center"
          ></el-table-column>
          <el-table-column prop="route" label="途径" :width="convertPX(100)" align="center"></el-table-column>
          <el-table-column prop="dosage" label="剂量/滴速" :width="convertPX(120)" align="center"></el-table-column>
          <el-table-column prop="frequency" label="频次" :width="convertPX(80)" align="center"></el-table-column>
          <el-table-column prop="doctor" label="医师" :width="convertPX(100)" align="center"></el-table-column>
          <el-table-column prop="performTime" label="执行时间" :width="convertPX(140)" align="center"></el-table-column>
          <el-table-column prop="endTime" label="结束时间" :width="convertPX(140)" align="center"></el-table-column>
          <el-table-column prop="performer" label="执行人员" :width="convertPX(100)" align="center"></el-table-column>
          <el-table-column label="操作" header-align="center" :width="convertPX(120)" align="center" fixed="right">
            <template slot-scope="scope">
              <el-tooltip content="修改">
                <i class="iconfont icon-edit" @click="recordAddOrUpdate(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="删除">
                <i @click="giveMedicineRecordDelete(scope.row)" class="iconfont icon-del"></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </base-layout>
    <!-- 新增/修改抽屉 -->
    <el-drawer
      :title="dialogTitle"
      :visible.sync="showDrawer"
      direction="rtl"
      :size="convertPX(800) + 'px'"
      :close-on-click-modal="false"
      :wrapperClosable="false"
    >
      <div class="drawer-content">
        <div class="form-section">
          <div class="form-row">
            <div class="form-item">
              <span class="label">执行日期:</span>
              <el-date-picker
                v-model="saveView.performDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                class="form-input"
              ></el-date-picker>
            </div>
            <div class="form-item">
              <span class="label">执行时间:</span>
              <el-time-picker
                v-model="saveView.performTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择时间"
                class="form-input"
                :disabled="!canEditPerformTime"
              ></el-time-picker>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <span class="label">类别:</span>
              <el-input v-model="saveView.category" placeholder="请输入类别" class="form-input"></el-input>
            </div>
            <div class="form-item">
              <span class="label">组号:</span>
              <el-input v-model="saveView.groupNumber" placeholder="请输入组号" class="form-input"></el-input>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item full-width">
              <span class="label">医嘱内容:</span>
              <el-input v-model="saveView.orderContent" placeholder="请输入医嘱内容" class="form-input"></el-input>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <span class="label">途径:</span>
              <el-input v-model="saveView.route" placeholder="请输入途径" class="form-input"></el-input>
            </div>
            <div class="form-item">
              <span class="label">剂量/滴速:</span>
              <el-input v-model="saveView.dosage" placeholder="请输入剂量/滴速" class="form-input"></el-input>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <span class="label">频次:</span>
              <el-input v-model="saveView.frequency" placeholder="请输入频次" class="form-input"></el-input>
            </div>
            <div class="form-item">
              <span class="label">医师:</span>
              <el-input v-model="saveView.doctor" placeholder="请输入医师" class="form-input"></el-input>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <span class="label">结束时间:</span>
              <el-time-picker
                v-model="saveView.endTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择时间"
                class="form-input"
              ></el-time-picker>
            </div>
            <div class="form-item">
              <span class="label">执行人员:</span>
              <user-selector v-model="saveView.userID" :disabled="disabledFlag" class="form-input"></user-selector>
            </div>
          </div>
        </div>
        <div class="drawer-footer">
          <el-button @click="showDrawer = false">取消</el-button>
          <el-button type="primary" @click="saveGiveMedicineRecord" :disabled="!saveFlag">保存</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import userSelector from "@/components/selector/userSelector";
import { mapGetters } from "vuex";
import {
  GetGiveMedicineRecordTableData,
  SaveGiveMedicineRecord,
  DeleteGiveMedicineRecord,
} from "@/api/GiveMedicineRecord";
export default {
  components: {
    baseLayout,
    userSelector,
  },
  props: {
    supplementPatient: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
    /**
     * description: 是否可以编辑执行时间
     * return {Boolean}
     */
    canEditPerformTime() {
      return this.saveView.orderRefillFlag === true;
    },
  },
  data() {
    return {
      loading: false,
      tableData: [],
      performDate: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      dialogTitle: "",
      showDrawer: false,
      disabledFlag: false,
      saveFlag: true,
      saveView: {
        id: "",
        inpatientID: "",
        patientID: "",
        performDate: "",
        performTime: "",
        category: "",
        groupNumber: "",
        orderContent: "",
        route: "",
        dosage: "",
        frequency: "",
        doctor: "",
        endTime: "",
        userID: "",
        orderRefillFlag: false,
      },
    };
  },
  mounted() {
    this.getTableData();
  },
  methods: {
    /**
     * description: 获取表格数据
     * return {*}
     */
    getTableData() {
      if (!this.supplementPatient || !this.supplementPatient.inpatientID) {
        return;
      }
      this.loading = true;
      const params = {
        inpatientID: this.supplementPatient.inpatientID,
        performDate: this.performDate,
      };
      GetGiveMedicineRecordTableData(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.tableData = res.data || [];
        }
      });
    },
    /**
     * description: 新增/修改点击弹窗
     * param {*} row
     * return {*}
     */
    recordAddOrUpdate(row) {
      if (row) {
        this.dialogTitle = "修改给药记录";
        this.saveView = {
          id: row.id,
          inpatientID: row.inpatientID,
          patientID: row.patientID,
          performDate: row.performDate,
          performTime: row.performTime,
          category: row.category,
          groupNumber: row.groupNumber,
          orderContent: row.orderContent,
          route: row.route,
          dosage: row.dosage,
          frequency: row.frequency,
          doctor: row.doctor,
          endTime: row.endTime,
          userID: row.userID,
          orderRefillFlag: row.orderRefillFlag,
        };
      } else {
        this.dialogTitle = "新增给药记录";
        this.saveView = {
          id: "",
          inpatientID: this.supplementPatient.inpatientID,
          patientID: this.supplementPatient.patientID,
          performDate: this.performDate,
          performTime: this._datetimeUtil.getNowTime("hh:mm"),
          category: "",
          groupNumber: "",
          orderContent: "",
          route: "",
          dosage: "",
          frequency: "",
          doctor: "",
          endTime: "",
          userID: this.user.userID,
          orderRefillFlag: true,
        };
      }
      this.showDrawer = true;
    },
    /**
     * description: 保存给药记录
     * return {*}
     */
    saveGiveMedicineRecord() {
      if (!this.saveView.performDate) {
        this._showTip("warning", "请选择执行日期");
        return;
      }
      if (!this.saveView.performTime) {
        this._showTip("warning", "请选择执行时间");
        return;
      }
      if (!this.saveView.orderContent) {
        this._showTip("warning", "请输入医嘱内容");
        return;
      }
      if (!this.saveView.userID) {
        this._showTip("warning", "请选择执行人员");
        return;
      }
      SaveGiveMedicineRecord(this.saveView).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功!");
          this.showDrawer = false;
          this.getTableData();
        }
      });
    },
    /**
     * description: 删除给药记录
     * param {*} row
     * return {*}
     */
    giveMedicineRecordDelete(row) {
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          const params = {
            id: row.id,
          };
          DeleteGiveMedicineRecord(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getTableData();
            }
          });
        }
      });
    },
  },
};
</script>
<style lang="scss">
.give-medicine-record-wrapper {
  height: 100%;
  .give-medicine-record {
    height: 100%;
    .give-medicine-record-header {
      .header-controls {
        display: flex;
        align-items: center;
        .label {
          margin-right: 10px;
          font-weight: bold;
        }
        .date-select {
          width: 155px;
          margin-right: 20px;
        }
        .give-medicine-record-header-add-button {
          float: right;
          margin-left: auto;
        }
      }
    }
    .give-medicine-record-content {
      height: 100%;
    }
  }
  .drawer-content {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .form-section {
      flex: 1;
      .form-row {
        display: flex;
        margin-bottom: 20px;
        .form-item {
          flex: 1;
          display: flex;
          align-items: center;
          margin-right: 20px;
          &:last-child {
            margin-right: 0;
          }
          &.full-width {
            flex: 2;
          }
          .label {
            width: 100px;
            text-align: right;
            margin-right: 10px;
            font-weight: bold;
          }
          .form-input {
            flex: 1;
          }
        }
      }
    }
    .drawer-footer {
      text-align: right;
      padding-top: 20px;
      border-top: 1px solid #e4e7ed;
    }
  }
}
</style>