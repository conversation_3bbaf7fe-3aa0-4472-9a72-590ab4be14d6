<!--
 * FilePath     : \src\pages\nursingEvaluation\index.vue
 * Author       : 郭自飞
 * Date         : 2020-05-05 14:57
 * LastEditors  : 曹恩
 * LastEditTime : 2023-08-02 09:05
 * Description  : 转科评价
 -->
<template>
  <base-layout show-header class="transfer">
    <div slot="header">
      <span>是否全部</span>
      <el-switch v-model="isAll" @change="init()"></el-switch>
      <span>日期:</span>
      <el-date-picker
        v-model="startTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 150px"
        placeholder="选择日期"
        :picker-options="pickerOptionscreate"
        @change="init()"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        v-model="endTime"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        style="width: 150px"
        placeholder="选择日期"
        :picker-options="pickerOptionsend"
        @change="init()"
      ></el-date-picker>
    </div>
    <el-table :data="transferList" border stripe height="100%">
      <el-table-column prop="bedNumber" label="床号" min-width="60" align="center"></el-table-column>
      <el-table-column prop="patientName" label="姓名" min-width="80" align="center"></el-table-column>
      <el-table-column prop="localCaseNumber" label="住院号" min-width="110" align="center"></el-table-column>
      <el-table-column prop="gender" label="性别" min-width="60" align="center"></el-table-column>
      <el-table-column prop="ageDetail" label="年龄" min-width="60" align="center"></el-table-column>
      <el-table-column
        prop="transferOutStation"
        label="转出科室"
        min-width="120"
        header-align="center"
        align="left"
      ></el-table-column>
      <el-table-column prop="transferInStation" label="转入科室" min-width="120" align="center"></el-table-column>
      <el-table-column label="转科时间" min-width="155" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.transferDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="评价" width="70" align="center">
        <template slot-scope="transfer">
          <el-tooltip content="评价">
            <router-link
              :to="{
                name: 'nursingEvaluation',
                query: {
                  inpatientID: transfer.row.inpatientID,
                  stationID: user.stationID,
                  isGoBack: true,
                  transferFlag: false,
                },
              }"
            >
              <div class="iconfont icon-edit"></div>
            </router-link>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import patientEvaluation from "./patientEvaluation";
import baseLayout from "@/components/BaseLayout";
import { GetTransferPatients } from "@/api/Inpatient";
import { mapGetters } from "vuex";
export default {
  components: {
    patientEvaluation,
    baseLayout,
  },
  data() {
    let that = this;
    return {
      transferList: [],
      isAll: false,
      startTime: undefined,
      endTime: undefined,
      pickerOptionscreate: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.endTime).getTime();
        },
      },
      pickerOptionsend: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.startTime).getTime() - 8.64e7;
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  mounted() {
    let newDate = this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd");
    this.startTime = this._datetimeUtil.addDate(newDate, -7, "yyyy-MM-dd");
    this.endTime = this._datetimeUtil.addDate(newDate, 1, "yyyy-MM-dd");
    if (this.user) {
      this.init();
    }
  },
  methods: {
    init() {
      let params = {
        isAll: this.isAll,
        startTime: this.startTime,
        endTime: this.endTime,
        stationID: this.user.stationID,
      };
      GetTransferPatients(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.transferList = res.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.transfer {
  .cell {
    .el-tooltip {
      color: #03b0a3;
    }
  }
}
</style>
