/*
 * FilePath     : \src\utils\i18n\index.js
 * Author       : 苏军志
 * Date         : 2021-10-30 10:17
 * LastEditors  : 苏军志
 * LastEditTime : 2022-02-08 10:56
 * Description  : 语言国际化
 */
import Vue from "vue";
// 引入common工具类
import common from "@/utils/common";
// 引入i18n模块
import VueI18n from "vue-i18n";
// 引入element-ui英文语言包
import elementEnLocale from "element-ui/lib/locale/lang/en";
// 引入element-ui中文简体语言包
import elementZhLocale from "element-ui/lib/locale/lang/zh-CN";
// 引入element-ui中文繁体语言包
import elementTwLocale from "element-ui/lib/locale/lang/zh-TW";
// 引入element-ui语言包模块
import elementLocale from "element-ui/lib/locale/";
// import umyUIEnlocale from "umy-ui/lib/locale/lang/en";
// import umyUIZhlocale from 'umy-ui/lib/locale/lang/zh-CN'
// import umyUIlocale from 'umy-ui/lib/locale/'
// 引入本地英文语言包
import enLoccal from "./lang/en/";
// 引入本地中文简体语言包
import zhLoccal from "./lang/zh/";
// 引入本地中文繁体语言包
import twLoccal from "./lang/tw/";

// 在vue中注册国际化
Vue.use(VueI18n);
// 引入语言包
const messages = {
  // 英文语言包
  en: {
    ...enLoccal,
    // ...umyUIEnlocale,
    ...elementEnLocale
  },
  // 中文简体语言包
  zh: {
    ...zhLoccal,
    // ...umyUIZhlocale,
    ...elementZhLocale
  },
  // 中文繁体语言包
  tw: {
    ...twLoccal,
    ...elementTwLocale
  }
};
// 创建国际化实例
const i18n = new VueI18n({
  // 取localStorage中的语言，取不到默认中文简体
  locale: common.storage("language") || "zh",
  messages
});
elementLocale.i18n((key, value) => i18n.t(key, value));
// umyUIlocale.i18n((key, value) => i18n.t(key, value));
export default i18n;
