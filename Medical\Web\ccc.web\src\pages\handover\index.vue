<!--
 * FilePath     : \src\pages\handover\index.vue
 * Author       : 郭自飞
 * Date         : 2020-05-19 09:20
 * LastEditors  : 来江禹
 * LastEditTime : 2023-08-17 08:55
 * Description  : 
--> 
<template>
  <div class="handon">
    <el-tabs class="handon-tab" v-model="activeName" v-if="model == 'page'">
      <el-tab-pane label="交班记录" name="handonSBAR"></el-tab-pane>
      <el-tab-pane label="交班报告" name="handoverQueryByInpatient"></el-tab-pane>
    </el-tabs>
    <div v-if="inpatient" :class="['handover-wrap', { component: model != 'page' }]">
      <handover-record
        v-if="activeName == 'handonSBAR'"
        :inpatientID="inpatient.inpatientID"
        :dischargedPreview="model == 'component'"
      />
      <handover-record-query v-if="activeName == 'handoverQueryByInpatient'" :inpatientID="inpatient.inpatientID" />
    </div>
  </div>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import HandoverRecord from "@/pages/handover/handonSBAR";
import HandoverRecordQuery from "@/pages/handover/handoverQueryByInpatient";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    HandoverRecord,
    HandoverRecordQuery,
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
    }),
  },
  data() {
    return {
      activeName: "handonSBAR",
      model: "page",
    };
  },
  created() {
    if (this.$route.query.model) {
      this.model = this.$route.query.model;
    }
    if (this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName;
    }
  },
  watch: {
    //切换病人数据，页签选中交班记录页签
    "inpatient.inpatientID": {
      async handler() {
        this.activeName = "handonSBAR";
      },
      immediate: true,
    },
  },
};
</script>

<style lang="scss">
.handon {
  height: 100%;
  .handon-tab {
    height: 40px;
  }
  .handover-wrap {
    height: calc(100% - 45px);
    margin-top: 5px;
    &.component {
      height: 100%;
    }
    #pane-handonSBAR {
      height: 100%;
    }
    #pane-handoverQueryByInpatient {
      height: 100%;
    }
  }
}
</style>