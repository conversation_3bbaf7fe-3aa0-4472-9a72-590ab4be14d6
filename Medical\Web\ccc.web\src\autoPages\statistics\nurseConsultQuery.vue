<!--
 * FilePath     : \src\autoPages\statistics\nurseConsultQuery.vue
 * Author       : 来江禹
 * Date         : 2024-03-22 16:01
 * LastEditors  : 来江禹
 * LastEditTime : 2024-04-15 16:02
 * Description  : 会诊统计页面
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="nurse-consult-query" v-loading="loading" element-loading-text="加载中……">
    <div slot="header">
      <div class="nurse-consult-header">
        <span>
          起始日期:
          <el-date-picker
            v-model="queryConsult.startDate"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="date"
            placeholder="选择日期"
            class="date-picker"
          ></el-date-picker>
        </span>
        <span class="header-span">
          结束日期:
          <el-date-picker
            v-model="queryConsult.endDate"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="date"
            placeholder="选择日期"
            class="date-picker"
          ></el-date-picker>
        </span>
        <!-- <span class="header-span"> -->
        <station-selector
          v-model="queryConsult.consultStationID"
          :hospitalFlag="true"
          label="发起病区："
          :width="convertPX(250) + 'px'"
        />
        <station-selector
          v-model="queryConsult.replyStationID"
          :hospitalFlag="true"
          label="回复病区："
          :width="convertPX(250) + 'px'"
        />
        <span class="header-span">
          目的:
          <el-select
            v-model="queryConsult.consultMainID"
            placeholder="请选择"
            class="select"
            @change="getConsultGoalByID"
          >
            <el-option
              v-for="item in consultGoalOne"
              :key="item.consultGoalID"
              :label="item.content"
              :value="item.consultGoalID"
            ></el-option>
          </el-select>
          <el-select v-model="queryConsult.consultDetailID" class="select" placeholder="请选择">
            <el-option
              v-for="item in consultGoalTwo"
              :key="item.consultGoalID"
              :label="item.content"
              :value="item.consultGoalID"
            ></el-option>
          </el-select>
        </span>
        <span class="count">筛选出{{ patientConsultList.length }}条</span>
        <el-button class="query-button" icon="iconfont icon-search" @click="getConsultStatistics()">查询</el-button>
        <el-button @click="exportEXCEL" class="print-button" icon="iconfont icon-arrow-download">导出EXCEL</el-button>
      </div>
    </div>
    <el-table
      :data="patientConsultList"
      border
      stripe
      height="100%"
      :default-sort="{ prop: 'consultDate', order: 'descending' }"
    >
      <el-table-column label="发起日期" :width="convertPX(240)" align="center" prop="consultDate" sortable>
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.consultDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="patientName" label="患者姓名" :width="convertPX(100)" align="left"></el-table-column>
      <el-table-column prop="gender" label="性别" :width="convertPX(50)" align="center"></el-table-column>
      <el-table-column prop="ageDetail" label="年龄" :width="convertPX(50)" align="center"></el-table-column>
      <el-table-column prop="chartNo" label="病历号" :width="convertPX(120)" align="center"></el-table-column>
      <el-table-column
        prop="numberOfAdmissions"
        label="住院次数"
        :width="convertPX(100)"
        align="left"
      ></el-table-column>
      <el-table-column
        prop="consultStationNmae"
        label="发起病区"
        :min-width="convertPX(75)"
        align="left"
      ></el-table-column>
      <el-table-column
        prop="consultEmployeeName"
        label="发起人员"
        :width="convertPX(100)"
        align="left"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="mainContent"
        label="会诊目的"
        :min-width="convertPX(70)"
        align="left"
      >
        <template slot-scope="scope">
          {{ scope.row.mainContent + "-" + scope.row.detailContent }}
        </template>
      </el-table-column>
      <el-table-column
        prop="replyStationNmae"
        label="回复病区"
        :min-width="convertPX(85)"
        align="left"
      ></el-table-column>
      <el-table-column label="回复日期" :width="convertPX(240)" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.replyDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="replyEmployeeName" label="出会诊人" :width="convertPX(100)" align="left"></el-table-column>
      <el-table-column prop="emergencyName" label="会诊分类" :width="convertPX(100)" align="center"></el-table-column>
      <el-table-column prop="consultState" label="状态" :width="convertPX(90)" align="center"></el-table-column>
    </el-table>
  </base-layout>
</template>
<script>
import stationSelector from "@/components/selector/stationSelector";
import { GetConsultGoal, GetConsultGoalById } from "@/api/PatientConsult";
import { GetByConditionConsultStatistics } from "@/api/Static";
import baseLayout from "@/components/BaseLayout";
export default {
  components: {
    baseLayout,
    stationSelector,
  },
  data() {
    return {
      consultGoalTwo: [],
      consultGoalOne: [],
      patientConsultList: [],
      queryConsult: {
        startDate: "",
        endDate: "",
        stationID: "",
        consultMainID: "",
        consultDetailID: "",
        consultStationID: 999999,
        replyStationID: 999999,
      },
      loading: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    /**
     * description: 初始化数据
     * return {*}
     */
    init() {
      this.queryConsult.startDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.queryConsult.endDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.getConsultMainGoal();
    },
    /**
     * description: 获取一阶会诊目的
     * return {*}
     */
    getConsultMainGoal() {
      let params = {
        level: 1,
      };
      GetConsultGoal(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.consultGoalOne = res.data;
          this.consultGoalOne.unshift({
            consultGoalID: 0,
            content: "全部",
          });
        }
      });
    },
    /**
     * description: 根据会诊一阶目的查询二阶目的
     * return {*}
     * param {*} id
     */
    async getConsultGoalByID(id) {
      let prams = {
        consultMainID: id,
      };
      if (id == 0) {
        this.consultGoalTwo = [];
      } else {
        await GetConsultGoalById(prams).then((res) => {
          if (this._common.isSuccess(res)) {
            this.consultGoalTwo = res.data;
          }
        });
      }
      this.queryConsult.consultDetailID = 0;
      this.consultGoalTwo.unshift({
        consultGoalID: 0,
        content: "全部",
      });
    },
    /**
     * description: 根据条件查询会诊数据
     * return {*}
     */
    getConsultStatistics() {
      this.loading = true;
      GetByConditionConsultStatistics(this.queryConsult).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.patientConsultList = res.data;
        }
      });
    },
    /**
     * description: 导出EXCEL
     * return {*}
     */
    exportEXCEL() {
      //EXCEL数据组装
      let temporary = [];
      let cloneList = this._common.clone(this.patientConsultList);
      cloneList.forEach((element) => {
        element.consultStationName = element.consultStationNmae;
        element.replyStationNmae = element.replyStationNmae;
        element.mainContent = element.mainContent + "-" + element.detailContent;
        element.consultState = element.consultState;
        if (element.consultDate) {
          element.consultDate = this._datetimeUtil.formatDate(element.consultDate, "yyyy-MM-dd hh:mm");
        } else {
          element.consultDate = "";
        }
        if (element.replyDate) {
          element.replyDate = this._datetimeUtil.formatDate(element.replyDate, "yyyy-MM-dd hh:mm");
        } else {
          element.replyDate = "";
        }
        temporary.push(element);
      });
      //excel数据导出
      require.ensure([], () => {
        const { export_json_to_excel } = require("@/vendor/Export2Excel");
        const tHeader = [
          "会诊发起日期",
          "患者姓名",
          "性别",
          "年龄",
          "病历号",
          "住院次数",
          "会诊发起病区",
          "会诊发起人",
          "会诊目的",
          "会诊回复病区",
          "会诊回复日期",
          "出会诊人",
          "急会诊标记",
          "状态",
        ];
        const filterVal = [
          "consultDate",
          "patientName",
          "gender",
          "ageDetail",
          "chartNo",
          "numberOfAdmissions",
          "consultStationName",
          "consultEmployeeName",
          "mainContent",
          "replyStationNmae",
          "replyDate",
          "replyEmployeeName",
          "emergencyName",
          "consultState",
        ];
        const list = temporary;
        const data = this.formatJson(filterVal, list);
        export_json_to_excel(tHeader, data, "会诊统计EXCEL");
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
  },
};
</script>
<style lang="scss">
.nurse-consult-query {
  .nurse-consult-header {
    .date-picker,
    .station-select {
      width: 120px;
    }
    .select {
      width: 140px;
    }
    .header-span {
      margin-left: 5px;
    }
    .count {
      margin-left: 10px;
      color: #ff0000;
    }
    .print-button {
      margin-top: 10px;
      float: right;
    }
  }
}
</style>
