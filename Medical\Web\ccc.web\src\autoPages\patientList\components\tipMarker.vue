<!--
 * FilePath     : \src\autoPages\patientList\components\tipMarker.vue
 * Author       : 苏军志
 * Date         : 2022-08-29 17:10
 * LastEditors  : 苏军志
 * LastEditTime : 2022-10-26 15:10
 * Description  : 提醒标记
 * CodeIterationRecord: 
-->
<template>
  <el-popover
    popper-class="tip-marker-warp"
    v-model="show"
    title="工作提醒"
    :width="width"
    trigger="hover"
    placement="bottom-start"
  >
    <i
      slot="reference"
      :class="['iconfont icon-message-notification tip-marker', { 'white-color': whiteColor }, { show: show }]"
    ></i>
    <i class="iconfont icon-message-notification tip-marker white-color"></i>
    <div class="tip-items">
      <div class="tip-item" v-for="(tip, index) in tipList" :key="index">
        <span :title="tip.remark">{{ tip.tipContent }}</span>
        <i class="iconfont icon-edit jump" @click="jumpPage(tip.router)"></i>
      </div>
    </div>
  </el-popover>
</template>

<script>
export default {
  props: {
    tipList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    whiteColor: {
      type: Boolean,
      default: false,
    },
    width: {
      default: "100",
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    jumpPage(router) {
      if (!router) {
        return;
      }
      this.$emit("jumpPage", router);
    },
  },
};
</script>

<style lang="scss">
.tip-marker-warp.el-popover {
  margin-top: 6px;
  border: 4px solid $base-color;
  padding: 5px 10px 0 10px;
  background-color: #fffff9;
  color: #000000;
  position: relative;
  .el-popover__title {
    margin: -5px -10px 0 -10px;
    padding: 1px 10px 6px 10px;
    letter-spacing: 2px;
    color: #ffffff;
    background-color: $base-color;
  }
  .tip-marker {
    position: absolute;
    top: -8px;
    left: 80px;
    animation: none;
    font-size: 20px !important;
  }
  .tip-items {
    .tip-item {
      position: relative;
      padding: 5px;
      border-bottom: 1px solid $base-color;
      &:last-child {
        border-bottom: 0;
      }
      .jump {
        position: absolute;
        right: 0;
        top: -1px;
        font-size: 18px !important;
      }
    }
  }
}
.tip-marker {
  position: absolute;
  width: 24px;
  height: 24px;
  font-size: 24px !important;
  background: linear-gradient(90deg, #ff0000, #ffa500);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  animation: 1s flicker 0s infinite;
  &.white-color {
    background: linear-gradient(90deg, #ffffff, #ffffff);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }
  &.show,
  &:hover {
    animation: none;
  }
}
@keyframes flicker {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>