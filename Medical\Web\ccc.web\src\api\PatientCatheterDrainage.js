import http from "../utils/ajax";
const baseUrl = "/io";
const baseUrls = "/Static";

export const urls = {
  GetCatheterDrainage: baseUrl + "/GetCatheterDrainage",
  GetDrainageStatistics: baseUrl + "/GetDrainageStatistics",
  StatisticsIOLine: baseUrls + "/StatisticsIOLine",
  GetIoInpatient: baseUrls + "/GetIoInpatient",
  GetTubeOutStatistics: baseUrl + "/GetTubeOutStatistics",
  GetOutputDocument: baseUrl + "/GetOutputDocument"
};
//查询导管引流液
export const GetCatheterDrainage = params => {
  return http.get(urls.GetCatheterDrainage, params);
};
//根据班别查询引流液汇总
export const GetDrainageStatistics = params => {
  return http.get(urls.GetDrainageStatistics, params);
};
//io统计图
export const StatisticsIOLine = params => {
  return http.get(urls.StatisticsIOLine, params);
};
//io柱状图数据
export const GetIoInpatient = params => {
  return http.get(urls.GetIoInpatient, params);
};
//获取IO记录
export const GetTubeOutStatistics = params => {
  return http.get(urls.GetTubeOutStatistics, params);
};
//获取IO每班每天汇总
export const GetOutputDocument = params => {
  return http.get(urls.GetOutputDocument, params);
};
