<!--
 * FilePath     : \ccc.web\src\pages\maintain\cacheList.vue
 * Author       : 李正元
 * Date         : 2020-06-20 09:50
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-01-16 10:57
 * Description  : 更新缓存
-->
<template>
  <base-layout class="maintain">
    <div slot="header">
      <el-button class="save-button" @click="updateCache">更新缓存</el-button>
    </div>
    <div class="cache">
      <el-table :data="cacheList" highlight-current-row @selection-change="isCheck">
        <el-table-column type="selection" width="55" prop="check"></el-table-column>
        <el-table-column prop="cacheType" label="缓存类别" align="left"></el-table-column>
        <el-table-column prop="cacheContent" label="缓存说明" align="left"></el-table-column>
        <el-table-column prop="cacheCode" label="缓存码" align="center"></el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetCacheList } from "@/api/Setting";
import { Update } from "@/api/Cache";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
  },
  created() {
    this.getCacheList();
  },
  computed: {
    ...mapGetters({
      localHospitalInfo: "getHospitalInfo",
    }),
  },
  data() {
    return {
      //缓存清单
      cacheList: [],
      //清除清单
      clearList: [],
    };
  },
  methods: {
    getCacheList() {
      GetCacheList().then((result) => {
        if (this._common.isSuccess(result)) {
          this.cacheList = result.data;
        }
      });
    },
    async updateCache() {
      let str = "";
      for (let index = 0; index < this.clearList.length; index++) {
        let item = this.clearList[index];
        let params = {
          type: item.cacheCode,
          hospitalID: this.localHospitalInfo.hospitalID,
        };
        await Update(params).then((result) => {
          if (this._common.isSuccess(result)) {
            if (str == "") {
              str = item.cacheContent;
            } else {
              str += "," + item.cacheContent;
            }
          }
        });
      }
      this._showTip("success", str + "更新成功");
    },
    isCheck(check) {
      this.clearList = check;
    },
  },
};
</script>
<style lang="scss">
.maintain {
  .header {
    .save-button {
      position: absolute;
      top: 15px;
      right: 20px;
    }
  }
}
</style>
