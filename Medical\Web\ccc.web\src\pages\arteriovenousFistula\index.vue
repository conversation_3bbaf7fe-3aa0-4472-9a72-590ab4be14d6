<!--
 * FilePath     : /src/pages/arteriovenousFistula/index.vue
 * Author       : 杨欣欣
 * Date         : 2022-10-08 14:17
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-05 17:10
 * Description  : 动静脉内瘘专项
 * CodeIterationRecord: 
-->
<template>
  <specific-care
    class="arteriovenous-fistula"
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    :editFlag="showEditButton"
    :careMainAddFlag="careMainAddFlag"
    @mainAdd="recordAdd"
    @maintainAdd="careMainAdd"
    @save="save"
    @cancel="drawerClose"
    @getHandOverFlag="getHandOverFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <!-- 主记录 -->
    <div slot="main-record">
      <el-table
        @row-click="recordClick"
        :data="recordTableData"
        border
        stripe
        height="100%"
        row-class-name="main-record-row"
        header-row-class-name="main-record-herder-row"
      >
        <el-table-column label="开始日期" width="110" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="开始时间" width="110" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="部位" prop="bodyShowName" align="center" />
        <el-table-column label="内瘘来源" prop="source" align="center" />
        <el-table-column label="记录人" prop="userName" width="80" />
        <el-table-column label="操作" width="90" align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="recordAdd(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="deleteRecord(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <packaging-table v-model="careMainTableData" :headerList="tableHeaderList">
        <!-- 评估类型 插槽 -->
        <div slot="assessType" slot-scope="row">
          <span v-if="row.row.recordsCode.indexOf('Start') != -1">开始评估</span>
          <span v-else>例行评估</span>
        </div>
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope">
          <el-tooltip content="修改" placement="top" v-if="scope.row.recordsCode.indexOf('Start') == -1">
            <div class="iconfont icon-edit" @click="careMainAdd(scope.row)"></div>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" v-if="scope.row.recordsCode.indexOf('Start') == -1">
            <div class="iconfont icon-del" @click="deleteCareMain(scope.row)"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 弹窗内容 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
    >
      <div slot="header">
        <span class="label">日期:</span>
        <el-date-picker
          class="date-picker"
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        />
        <el-time-picker
          class="time-picker"
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        />
        <station-selector v-model="stationID" label="病区:" width="160" />
        <dept-selector label="" width="140" v-model="departmentListID" :stationID="stationID" />
      </div>
      <body-and-assess-layout :link="link" :bodyShowFlag="type == 'Start' && Boolean(link)">
        <tabs-layout ref="tabsLayout" :template-list="templateDatas" @change-values="changeValues" @checkTN="checkTN" />
      </body-and-assess-layout>
    </base-layout>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import bodyAndAssessLayout from "@/components/bodyAndAssessLayout";
import { mapGetters } from "vuex";
import {
  GetArteriovenousFistulaRecordList,
  GetArteriovenousFistulaCareMainListByRecordID,
  GetArteriovenousFistulaRecordsCodeInfo,
  GetArteriovenousFistulaAssessView,
  GetBodyPartSort,
  AddArteriovenousFistulaRecord,
  AddArteriovenousFistulaCare,
  UpdateArteriovenousFistulaRecord,
  UpdateArteriovenousFistulaCare,
  DeleteArteriovenousFistulaByID,
  DeleteArteriovenousFistulaCare,
} from "@/api/ArteriovenousFistula";
import { GetBringToShiftSetting } from "@/api/Setting";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { GetBringToNursingRecordFlagSetting } from "@/api/SettingDescription";
export default {
  components: {
    specificCare,
    tabsLayout,
    baseLayout,
    packagingTable,
    stationSelector,
    deptSelector,
    bodyAndAssessLayout,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
    }),
  },
  props: {
    supplementPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      loading: false,
      patient: undefined,
      type: undefined,
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      //主记录变量
      recordTableData: [],
      recordID: undefined,
      currentRecord: undefined,
      //维护记录变量
      careMainTableData: [],
      careMainID: undefined,
      // 新增修改标记
      addFlag: true,
      showEditButton: true,
      //弹窗变量
      drawerLoading: false,
      drawerLoadingText: undefined,
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      departmentListID: undefined,
      templateDatas: [],
      recordsCodeInfo: {},
      assessDatas: [],
      checkTNFlag: true,
      recordsCode: undefined,
      handOverArr: [true, false],
      informPhysicianArr: [true, false],
      bringToNursingRecordArr: [true, false],
      settingHandOver: false,
      settingNursingRecord: false,
      //路由变量
      patientScheduleMainID: undefined,
      assessMainID: undefined,
      // 评估次数
      assessSort: 0,
      sourceID: undefined,
      sourceType: undefined,
      refillFlag: "",
      // 动态表头
      tableHeaderList: [],
      // 维护记录新增按钮开关
      careMainAddFlag: true,
      // 人体图链接
      link: "",
      bodyPartSort: undefined,
    };
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = "";
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplementPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplementPatient;
          this.refillFlag = "*";
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    init() {
      if (this.$route.query.patientScheduleMainID) {
        this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
      }
      this.assessMainID = this.$route.query.num;
      this.assessSort = this.$route.query.sort;
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
      if (this.patientScheduleMainID || this.assessMainID || this.sourceID || this.sourceType) {
        this._sendBroadcast("setPatientSwitch", false);
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
      localStorage.setItem("selectPart", JSON.stringify({}));
      localStorage.setItem("bodyPart", JSON.stringify({}));
      this.getRecordTableData();
      this.getBringHandOverSetting();
      this.getBringToNursingRecordSetting();
    },
    /**
     * description: 获取维护记录动态列
     * param {*}
     * return {*}
     */
    async getTableHeaderList() {
      let params = {
        fileClassID: 107,
        useDescription: "1||Table",
        newSourceFlag: true,
      };
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tableHeaderList = res.data;
        }
      });
    },
    /**
     * description: 弹窗保存按钮点击
     * param {*}
     * return {*}
     */
    async save() {
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      if (this.recordsCodeInfo.recordsCode.indexOf("Start") != -1) {
        await this.recordSave();
      }
      if (this.recordsCodeInfo.recordsCode.indexOf("Maintain") != -1) {
        await this.careMainSave();
      }
      this.drawerLoading = false;
      this.drawerLoadingText = "";
    },

    /*-------------主记录CRUD-------------*/

    /**
     * description: 获取主记录数据
     * param {*}
     * return {*}
     */
    async getRecordTableData() {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      this.loading = true;
      //获取病人主记录列表
      await GetArteriovenousFistulaRecordList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.recordTableData = result.data;
        }
      });
    },

    /**
     * description: 主记录新增修改
     * param {*} item 要保存的数据
     * return {*}
     */
    async recordAdd(item) {
      this.type = "Start";
      this.recordsCode = "ArteriovenousFistulaStart";
      this.link = "";
      if (item) {
        //权限检核
        await this.checkAuthor(
          item.patientArteriovenousFistulaRecordID,
          "PatientArteriovenousFistulaRecord",
          item.userID
        );
        this.addFlag = false;
        this.assessDate = this._datetimeUtil.formatDate(item.startDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.startTime, "hh:mm");
        this.stationID = item.stationID;
        this.departmentListID = item.departmentListID;
        this.recordID = item.patientArteriovenousFistulaRecordID;
        this.bodyPart = item.bodyPartID;
        this.bodyPartSort = item.bodyPartSort;
      } else {
        this.addFlag = true;
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.recordID = undefined;
        this.bodyPart = {};
      }
      this.openOrCloseDrawer(true, "动静脉内瘘主记录");
      await this.getAssessTemplate();
      this.getBodyPart();
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.bringToNursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingNursingRecord);
    },

    /**
     * description: 主记录保存
     * param {*}
     * return {*}
     */
    async recordSave() {
      // 数据验证
      if (!this.saveCheck("record")) {
        return;
      }
      let saveDataView = await this.createRecordSaveView();
      let req = this.addFlag
        ? AddArteriovenousFistulaRecord(saveDataView)
        : UpdateArteriovenousFistulaRecord(saveDataView);

      await req.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.addFlag ? "新增" : "修改"}成功！`);
        }
      });
      this.openOrCloseDrawer(false);
      this.fixTable();
      this.getRecordTableData();
    },
    /**
     * description: 创建主记录保存请求View
     * param {*}
     * return {*}
     */
    async createRecordSaveView() {
      if (this.addFlag) {
        await this.getBodyPartSort(localStorage.getItem("bodyPart"));
      }
      let saveData = {
        Details: this.getDetails(),
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        AssessSort: this.assessSort,
        PatientScheduleMainID: this.patientScheduleMainID,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        Record: {
          PatientArteriovenousFistulaRecordID: this.recordID,
          InpatientID: this.patient.inpatientID,
          StartDate: this.assessDate,
          StartTime: this.assessTime,
          StationID: this.stationID,
          departmentListID: this.departmentListID,
          RefillFlag: this.refillFlag,
          BodyPartID: localStorage.getItem("bodyPart"),
          BodyPartName: this.getBodyPartName(localStorage.getItem("bodyPart")),
          BodyPartSort: this.bodyPartSort,
        },
      };
      return saveData;
    },
    /**
     * description: 保存检核
     * param {*}
     * return {*}
     */
    saveCheck(type) {
      if (type == "record") {
        var selectPart = localStorage.getItem("bodyPart");
        if (selectPart == "{}" || selectPart == "[]") {
          this._showTip("warning", "请选择部位！");
          return false;
        }
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      return this.assessDatas.find((item) => !item.formula && !this._common.checkAssessTN(item)) ? false : true;
    },
    /**
     * description: 主记录删除
     * param {*} row 当前行数据
     * return {*}
     */
    async deleteRecord(row) {
      if (!row.patientArteriovenousFistulaRecordID) {
        this._showTip("删除失败，没有找到主记录！");
        return;
      }
      //权限检核
      await this.checkAuthor(row.patientArteriovenousFistulaRecordID, "PatientArteriovenousFistulaRecord", row.userID);
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            RecordID: row.patientArteriovenousFistulaRecordID,
          };
          this.loading = true;
          DeleteArteriovenousFistulaByID(params).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              this.fixTable();
              this._showTip("success", "删除成功！");
              this.getRecordTableData();
            }
          });
        }
      });
    },

    /*-------------维护记录CRUD-------------*/
    /**
     * description: 点击选中主记录，展示维护记录
     * param {*} row 当前行数据
     * return {*}
     */
    async recordClick(row) {
      this.currentRecord = row;
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (!this.showRecordArr[1]) {
        this.getRecordTableData();
        return;
      }
      this.recordTableData = [row];
      this.careMainAddFlag = !row.endDate;
      this.getCareMainTableData();
      await this.getTableHeaderList();
    },
    /**
     * description: 根据RecordID获取维护记录
     * param {*}
     * return {*}
     */
    async getCareMainTableData() {
      let params = {
        recordID: this.currentRecord.patientArteriovenousFistulaRecordID,
      };
      this.loading = true;
      await GetArteriovenousFistulaCareMainListByRecordID(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.careMainTableData = result.data;
        }
      });
    },

    /**
     * description: 维护记录新增修改
     * param {*} item 当前行数据
     * return {*}
     */
    async careMainAdd(item) {
      this.type = "Maintain";
      this.openOrCloseDrawer(true, "动静脉内瘘维护记录");

      this.recordID = this.currentRecord.patientArteriovenousFistulaRecordID;
      if (item) {
        //权限检核
        await this.checkAuthor(
          item.patientArteriovenousFistulaCareMainID,
          "PatientArteriovenousFistulaCareMain",
          item.userID
        );
        this.addFlag = false;
        this.careMainID = item.patientArteriovenousFistulaCareMainID;
        this.assessDate = this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.assessTime, "hh:mm");
        this.stationID = item.stationID;
        this.departmentListID = item.departmentListID;
        this.recordsCode = item.recordsCode;
      } else {
        this.addFlag = true;
        this.careMainID = undefined;
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowDate("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.bedID = this.patient.bedID;
        this.bedNumber = this.patient.bedNumber;
        this.recordsCode = "ArteriovenousFistulaMaintain";
      }

      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingNursingRecord);
      await this.getAssessTemplate();
    },

    /**
     * description: 维护记录保存
     * param {*}
     * return {*}
     */
    async careMainSave() {
      if (!this.saveCheck("careMain")) {
        return;
      }
      let saveData = this.createCareMainSaveView();
      let req = this.addFlag ? AddArteriovenousFistulaCare(saveData) : UpdateArteriovenousFistulaCare(saveData);

      await req.then((result) => {
        if (this._common.isSuccess(result)) {
          this.openOrCloseDrawer(false);
          this._showTip("success", `${this.addFlag ? "新增" : "修改"}成功！`);
          this.getCareMainTableData();
        }
      });
    },
    /**
     * description: 维护记录保存model
     * param {*}
     * return {*}
     */
    createCareMainSaveView() {
      let saveData = {
        PatientArteriovenousFistulaRecordID: this.currentRecord.patientArteriovenousFistulaRecordID,
        PatientArteriovenousFistulaCareMainID: this.careMainID,
        PatientScheduleMainID: this.patientScheduleMainID,
        BedID: this.bedID,
        BedNumber: this.bedNumber,
        StationID: this.stationID,
        DepartmentListID: this.departmentListID,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        RefillFlag: this.refillFlag,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        Details: this.getDetails(),
      };
      return saveData;
    },

    //维护记录删除
    async deleteCareMain(row) {
      if (!row) {
        this._showTip("warning", "删除失败，找不到维护记录");
      }
      // 是否仅本人操作
      await this.checkAuthor(
        row.patientArteriovenousFistulaCareMainID,
        "PatientArteriovenousFistulaCareMain",
        row.userID
      );
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let param = {
            CareMainID: row.patientArteriovenousFistulaCareMainID,
          };
          this.loading = true;
          DeleteArteriovenousFistulaCare(param).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              this.getCareMainTableData();
            }
          });
        }
      });
    },
    /**
     * description: 重置选中状态
     * param {*}
     * return {*}
     */
    fixTable() {
      this.showRecordArr = [true, false];
      this.currentRecord = undefined;
      this.careMainTableData = [];
    },

    /**
     * description: 获取评估模板
     * param {*}
     * return {*}
     */
    async getAssessTemplate() {
      this.drawerLoadingText = "加载中……";
      this.drawerLoading = true;

      await this.getRecordsCodeInfoByRecordCode();
      if (!this.recordsCodeInfo || !this.recordsCodeInfo.recordsCode) {
        this.openOrCloseDrawer(false);
        this._showTip("warning", "找不到评估模板！");
        return;
      }
      await this.getTemplateDatas();

      this.drawerLoading = false;
    },
    /**
     * description: 根据RecordsCode获取DepartmentToAssess数据
     * param {*}
     * return {*}
     */
    async getRecordsCodeInfoByRecordCode() {
      let params = {
        recordsCode: this.recordsCode,
        departmentListID: this.patient.departmentListID,
      };
      await GetArteriovenousFistulaRecordsCodeInfo(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.recordsCodeInfo = result.data;
        }
      });
    },
    /**
     * description: 获取评估模板
     * param {*}
     * return {*}
     */
    async getTemplateDatas() {
      let params = {
        recordID: this.recordID,
        careMainID: this.careMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        inpatientID: this.patient.inpatientID,
        dateOfBirth: this.patient.dateOfBirth,
      };
      this.templateDatas = [];
      await GetArteriovenousFistulaAssessView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 初始化部位相关变量
     * param {*}
     * return {*}
     */
    getBodyPart() {
      if (this.recordsCode.lastIndexOf("Start") == -1) {
        return;
      }
      let link = `../../static/body/mobileBody.html?recordsCode=${this.recordsCodeInfo.recordsCode}&gender=${this.patient.genderCode}`;
      localStorage.setItem("selectPart", this.bodyPart);
      if (this.addFlag) {
        localStorage.setItem("selectPart", JSON.stringify({}));
        localStorage.setItem("bodyPart", JSON.stringify({}));
      }
      this.$set(this, "link", `${link}&type=Common`);
    },
    /**
     * description: 评估组件回传数据
     * param {*} datas
     * return {*}
     */
    changeValues(datas) {
      this.assessDatas = datas;
    },

    /*-------------获取页面配置-------------*/
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "ArteriovenousFistula",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingHandOver = res.data;
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "ArteriovenousFistulaAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },

    /*-------------配合保存方法-------------*/
    /**
     * description: 获取保存明细
     * param {*}
     * return {*}
     */
    getDetails() {
      let details = [];
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        if (content.disableGroup != -1) {
          details.push(detail);
        }
      });
      return details;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 回显部位名称
     * param {*} bodyPart
     * return {*}
     */
    getBodyPartName(bodyPart) {
      var selectPart = JSON.parse(bodyPart);
      let name = "";
      if (selectPart instanceof Array) {
        // 多选
        if (selectPart && selectPart.length > 0) {
          selectPart.forEach((part) => {
            if (name) {
              name += "，" + part.bodyPartName;
            } else {
              name = part.bodyPartName;
            }
          });
        }
      } else {
        // 单选
        name = selectPart.bodyPartName;
      }
      return name;
    },
    /**
     * description: 获取部位序号
     * param {*} selectPart
     * return {*}
     */
    async getBodyPartSort(selectPart) {
      this.bodyPartSort = 0;
      let params = {
        StationID: this.patient.stationID,
        InpatientID: this.patient.inpatientID,
        BodyPartID: selectPart,
      };
      await GetBodyPartSort(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.bodyPartSort = result.data;
        }
      });
    },
    /**
     * description: 权限检核
     * param {*} id 主记录/主表ID
     * param {*} tableName
     * return {*}
     */
    async checkAuthor(id, tableName, userID) {
      let auth = await this._common.checkActionAuthorization(this.user, userID);
      if (!auth) {
        this._showTip("warning", "非本人不可操作");
        this.showEditButton = false;
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, tableName);
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
    },
    /*-------------专项护理组件逻辑-------------*/
    /**
     * description: 组件回传交班flag
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 通知医师标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.bringToNursingRecordArr[1] = flag;
    },
    /**
     * description: 弹窗关闭
     * param {*}
     * return {*}
     */
    drawerClose() {
      this.recordID = undefined;
      this.careMainID = undefined;
      this.templateDatas = [];
      this.assessDatas = [];
      this.showTemplateFlag = false;
    },
    /**
     * description: 弹窗开关
     * param {*} flag 开关动作
     * param {*} title 弹窗标题
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
  },
};
</script>

<style lang="scss" >
.arteriovenous-fistula {
  .base-layout {
    .base-header {
      .el-date-editor.date-picker {
        width: 120px;
      }
      .el-date-editor.time-picker {
        width: 80px;
      }
    }
  }
}
</style>