/*
 * FilePath     : \ccc.web\src\api\RestraintRecord.js
 * Author       : 郭鹏超
 * Date         : 2020-06-11 11:34
 * LastEditors  : 郭鹏超
 * LastEditTime : 2021-07-20 11:14
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientRestraintRecord";

export const urls = {
  GetRestraintRecordList: baseUrl + "/GetRestraintRecordList",
  SaveRestraint: baseUrl + "/SaveRestraint",
  DeleteRestraintByID: baseUrl + "/DeleteRestraintByID",
  StopRestraint: baseUrl + "/StopRestraint",
  DeleteRestraintCare: baseUrl + "/DeleteRestraintCare",
  GetRestraintAssessView: baseUrl + "/GetRestraintAssessView",
  GetCareByRecordID: baseUrl + "/GetCareByRecordID",
  SaveRestraintCare: baseUrl + "/SaveRestraintCare",
  GetPatientScoreID: baseUrl + "/GetPatientScoreID",
  GetRestraintOrder: baseUrl + "/GetRestraintOrder",
};

//根据单位代码和病人在院号获取数据
export const GetRestraintRecordList = (params) => {
  return http.get(urls.GetRestraintRecordList, params);
};
//保存约束记录及评估
export const SaveRestraint = (params) => {
  return http.post(urls.SaveRestraint, params);
};
//根据约束记录ID删除约束记录表、 约束评估主表， 明细表、 图片表、 及评估里约束数据
export const DeleteRestraintByID = (params) => {
  return http.post(urls.DeleteRestraintByID, qs.stringify(params));
};
//结束约束
export const StopRestraint = (params) => {
  return http.post(urls.StopRestraint, params);
};
//删除评估主表、明细表、图片表
export const DeleteRestraintCare = (params) => {
  return http.post(urls.DeleteRestraintCare, qs.stringify(params));
};

// 获取约束评估模板
export const GetRestraintAssessView = (params) => {
  return http.get(urls.GetRestraintAssessView, params);
};
//根据约束记录ID获取所有约束评估
export const GetCareByRecordID = (params) => {
  return http.get(urls.GetCareByRecordID, params);
};
//保存约束评估
export const SaveRestraintCare = (params) => {
  return http.post(urls.SaveRestraintCare, params);
};
//根据recordID获取风险主键ID

export const GetPatientScoreID = (params) => {
  return http.get(urls.GetPatientScoreID, params);
};
//获取约束医嘱开嘱医生
export const GetRestraintOrder = (params) => {
  return http.get(urls.GetRestraintOrder, params);
};
