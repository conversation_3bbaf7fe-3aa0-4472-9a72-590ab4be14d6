<!--
 * FilePath     : \ccc.web\src\pages\patientOrder\patientOrders.vue
 * Author       : 郭鹏超
 * Date         : 2020-05-08 10:49
 * LastEditors  : LX
 * LastEditTime : 2025-06-11 20:23
 * Description  : 
 -->
<template>
  <div class="order-wrap" element-loading-text="加载中……" v-loading="loading">
    <div class="order-top">
      <el-radio-group v-show="tabsBoole == '0'" @change="radioChange" class="order-radios" v-model="executeYesOrNO">
        <el-radio label="1">未执行</el-radio>
        <el-radio label="2">已执行</el-radio>
      </el-radio-group>
      <el-radio-group v-show="tabsBoole == '1'" @change="radioChange" class="order-radios" v-model="stopYesOrNO">
        <el-radio label="1">未停止</el-radio>
        <el-radio label="2">已停止</el-radio>
      </el-radio-group>
      <el-tabs class="order-tab" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="长期" name="1">
          <div class="order-content">
            <el-table stripe highlight-current-row :data="filterTableData" border height="100%" style="width: 100%">
              <el-table-column label="医嘱" class-name="order-colume" min-width="220">
                <template slot-scope="scope">
                  <p
                    :class="{ 'order-p': scope.row.orderContents.length > 1 }"
                    v-for="(v, i) in scope.row.orderContents"
                    :key="i"
                  >
                    {{ v.content }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="剂量" class-name="order-colume" width="80" header-align="center">
                <template slot-scope="scope">
                  <p
                    :class="{ 'order-p': scope.row.orderContents.length > 1 }"
                    v-for="(v, i) in scope.row.orderContents"
                    :key="i"
                  >
                    {{ !v.totalVolume ? "" : v.totalVolume + v.unit }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column
                v-for="(item, index) in headersData0"
                :key="index"
                :width="item.width"
                :prop="item.name"
                :label="item.title"
                :align="item.position"
                header-align="center"
              ></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="临时" name="0">
          <div class="order-content">
            <el-table stripe highlight-current-row :data="filterTableData" border height="100%" style="width: 100%">
              <el-table-column label="医嘱" class-name="order-colume" min-width="220">
                <template slot-scope="scope">
                  <p
                    :class="{ 'order-p': scope.row.orderContents.length > 1 }"
                    v-for="(v, i) in scope.row.orderContents"
                    :key="i"
                  >
                    {{ v.content }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="剂量" class-name="order-colume" width="80" header-align="center">
                <template slot-scope="scope">
                  <p
                    :class="{ 'order-p': scope.row.orderContents.length > 1 }"
                    v-for="(v, i) in scope.row.orderContents"
                    :key="i"
                  >
                    {{ !v.totalVolume ? "" : v.totalVolume + v.unit }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column
                v-for="(item, index) in headersData1"
                :key="index"
                :width="item.width"
                :prop="item.name"
                :label="item.title"
                :align="item.position"
                header-align="center"
              ></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="全部" name="2">
          <div class="order-content">
            <el-table stripe highlight-current-row :data="filterTableData" border height="100%" style="width: 100%">
              <el-table-column
                label="类型"
                prop="orderTypeName"
                align="center"
                width="40"
                header-align="center"
              ></el-table-column>
              <el-table-column label="医嘱" class-name="order-colume" min-width="200" header-align="center">
                <template slot-scope="scope">
                  <p
                    :class="{ 'order-p': scope.row.orderContents.length > 1 }"
                    v-for="(v, i) in scope.row.orderContents"
                    :key="i"
                  >
                    {{ v.content }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="剂量" class-name="order-colume" width="80" header-align="center">
                <template slot-scope="scope">
                  <p
                    :class="{ 'order-p': scope.row.orderContents.length > 1 }"
                    v-for="(v, i) in scope.row.orderContents"
                    :key="i"
                  >
                    {{ !v.totalVolume ? "" : v.totalVolume + v.unit }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column
                v-for="(item, index) in headersData0"
                :width="item.width"
                :key="index"
                :prop="item.name"
                :label="item.title"
                :align="item.position"
                header-align="center"
              ></el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { GetOrdersByInpatientID } from "@/api/Orders";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters({
      inpatientInfo: "getPatientInfo",
    }),
  },
  watch: {
    inpatientInfo: {
      handler(newValue) {
        this.noFilterTableData = [];
        this.filterTableData = [];
        if (!newValue) return;
        this.getData();
      },
      immediate: true,
    },
  },
  data() {
    return {
      activeName: "2",
      tabsBoole: "", //控制 全部 长期 临时
      stopYesOrNO: "1",
      executeYesOrNO: "1",
      noFilterTableData: [],
      filterTableData: [],
      loading: false,
      headersData0: [
        {
          name: "orderCode",
          title: "医嘱码",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "frequency",
          title: "频次",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "frequencyDescription",
          title: "频次说明",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "orderRule",
          title: "途径",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "startDateTime",
          title: "开始时间",
          rowspan: true,
          width: 110,
          position: "center",
        },
        {
          name: "orderDescription",
          title: "医生说明",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "doctor",
          title: "医生",
          rowspan: true,
          width: 70,
          unit: "%",
        },
        {
          name: "endDateTime",
          title: "结束时间",
          rowspan: true,
          width: 110,
          position: "center",
        },
      ],
      headersData1: [
        {
          name: "orderCode",
          title: "医嘱码",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "frequency",
          title: "频次",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "frequencyDescription",
          title: "频次说明",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "orderRule",
          title: "途径",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "startDateTime",
          title: "开始时间",
          rowspan: true,
          width: 110,
          position: "center",
        },
        {
          name: "orderDescription",
          title: "医生说明",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "doctor",
          title: "医生",
          rowspan: true,
          width: 70,
          unit: "%",
        },
        // {
        //   name: "performTime",
        //   title: "执行时间",
        //   rowspan: true,
        //   width: 110,
        //   position: "center",
        // },
        // {
        //   name: "performUser",
        //   title: "执行人",
        //   rowspan: true,
        //   width: 70,
        // },
      ],
      headersData2: [
        {
          name: "frequency",
          title: "频次",
          rowspan: true,
          width: 120,
          unit: "%",
        },
        {
          name: "frequencyDescription",
          title: "频次说明",
          rowspan: true,
          width: 80,
          unit: "%",
        },
        {
          name: "orderRule",
          title: "途径",
          rowspan: true,
          width: 70,
          unit: "%",
          position: "center",
        },
        {
          name: "startDateTime",
          title: "开始时间",
          rowspan: true,
          width: 110,
          position: "center",
        },
        {
          name: "orderDescription",
          title: "医生说明",
          rowspan: true,
          width: 80,
          unit: "%",
        },
        {
          name: "doctor",
          title: "医生",
          rowspan: true,
          width: 70,
          unit: "%",
        },
        {
          name: "endDateTime",
          title: "结束时间",
          rowspan: true,
          width: 110,
          position: "center",
        },
      ],
    };
  },

  mounted() {
    //默认显示全部数据表头
    this.headersData = this.headersData0;
  },

  methods: {
    //单选框筛选
    radioChange(v) {
      // 数据为空停止筛选
      if (this.noFilterTableData == null) {
        return;
      }
      if (v == "1") {
        //筛选未停止 未执行
        this.filterTableData = this.noFilterTableData.filter(
          (item) =>
            item.orderType == this.activeName &&
            (item.orderType == 0 ? item.performDateTime == null : item.orderStatus == "2")
        );
      } else {
        //筛选 已停止 已执行
        this.filterTableData = this.noFilterTableData.filter(
          (item) =>
            item.orderType == this.activeName &&
            (item.orderType == 0 ? item.performDateTime != null : item.orderStatus == "3")
        );
      }
    },
    //长期 临时 全部 筛选
    handleClick(tab) {
      //控制单选框D
      this.tabsBoole = tab.name;
      // 默认显示未执行 未停止
      this.stopYesOrNO = "1";
      this.executeYesOrNO = "1";
      if (this.noFilterTableData == null) {
        return;
      }
      //筛选全部数据
      if (tab.name == 2) {
        this.filterTableData = this.noFilterTableData;
      } else {
        //筛选长期 临时
        this.filterTableData = this.noFilterTableData.filter(
          (item) =>
            item.orderType == tab.name && (item.orderType == 0 ? item.performDateTime == null : item.orderStatus == "2")
        );
      }
    },
    //获取表格数据
    async getData() {
      if (this.inpatientInfo == null) {
        this._showTip("error", "未查询到病人信息！");
        return;
      }
      let params = {
        inpatientID: this.inpatientInfo.inpatientID,
      };
      this.loading = true;
      this.noFilterTableData = [];
      this.filterTableData = [];
      await GetOrdersByInpatientID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.loading = false;
          this.noFilterTableData = res.data;
          //所有日期取消秒数
          if (this.noFilterTableData != null) {
            this.noFilterTableData.map((item) => {
              item.startDateTime =
                item.startDateTime == null
                  ? null
                  : this._datetimeUtil.formatDate(item.startDateTime, "yyyy-MM-dd hh:mm");
              item.endDateTime =
                item.endDateTime == null ? null : this._datetimeUtil.formatDate(item.endDateTime, "yyyy-MM-dd hh:mm");
              item.performTime =
                item.performTime == null ? null : this._datetimeUtil.formatDate(item.performTime, "yyyy-MM-dd hh:mm");
            });
          }
          // 默认显示全部数据
          this.filterTableData = this.noFilterTableData;
        }
      });
    },
    /**
     * description: 刷新页面数据
     * return {*}
     */
    async refreshData() {
      this.loading = true;
      this.loadingText = "加载中……";
      //页面数据刷新
      await this.getData();
      this.loading = false;
    },
    /**
     * description: 获得同步刷新的请求参数 -RouterList.RefreshAPI = RefreshOrder
     * return {*}
     */
    getRefreshParams() {
      this.loading = true;
      return this.inpatientInfo ? { caseNumber: this.inpatientInfo.caseNumber } : {};
    },
  },
};
</script>

<style lang="scss">
.order-wrap {
  height: 100%;
  .order-top {
    height: 100%;
    position: relative;
    .order-tab {
      height: 100%;
      .el-tabs__content {
        height: calc(100% - 60px);
        .el-tab-pane {
          height: 100%;
        }
      }
      .el-tabs__nav-wrap::after {
        background-color: #f3f3f3;
      }
    }
    .order-content {
      height: 100%;
      .order-colume {
        .cell {
          padding: 1px 0 !important;
        }
        .el-table__row td {
          padding: 0 3px;
        }
        .cell > p {
          padding: 10px 10px;
          margin: 0;
          position: relative;
        }
        .order-p::after {
          content: "";
          width: 100%;
          height: 1px;
          display: block;
          background-color: #ebeef5;
          position: absolute;
          bottom: 0;
          left: 0;
        }
        .order-p:last-child::after {
          display: none;
        }
      }
    }
    .order-radios {
      float: right;
      position: absolute;
      top: 10px;
      right: 30px;
    }
  }
}
</style>
