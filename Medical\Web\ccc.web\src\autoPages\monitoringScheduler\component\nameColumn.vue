<!--
 * FilePath     : \src\autoPages\monitoringScheduler\component\nameColumn.vue
 * Author       : 杨欣欣
 * Date         : 2024-06-17 11:02
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-07-04 15:20
 * Description  : 首列，单病人显示时间，批量显示姓名、床号、时间
 * CodeIterationRecord: 
 -->
<template functional>
  <u-table-column
    :label="props.firstColumn.title"
    :width="props.width"
    :align="props.isSingle ? 'center' : 'left'"
    fixed="left"
    show-overflow-tooltip
  >
    <template slot-scope="{ row }">
      <div class="first-column">
        {{ props.isSingle ? row[props.firstColumn.index] : row[props.firstColumn.index].split("||").join("-") }}
      </div>
    </template>
  </u-table-column>
</template>
<script>
</script>
<style lang="scss">
.first-column {
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>