/*
 * FilePath     : \src\api\ExtraBedBinding.js
 * Author       : 祝仕奇
 * Date         : 2021-10-21 14:45
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:44
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/ExtraBedBinding";

export const urls = {
  //获取全部的Route（路由）
  GetAllExtraBedBinding: baseUrl + "/GetAllExtraBedBinding",
  SaveExtraBedBinding: baseUrl + "/SaveExtraBedBinding",
  UpdateExtraBedBinding: baseUrl + "/UpdateExtraBedBinding",
  DeleteExtraBedBinding: baseUrl + "/DeleteExtraBedBinding"
};
// 获取全部查询数据
export const GetAllExtraBedBinding = params => {
  return http.get(urls.GetAllExtraBedBinding, params);
};

export const SaveExtraBedBinding = params => {
  return http.post(urls.SaveExtraBedBinding, params);
};
export const UpdateExtraBedBinding = params => {
  return http.post(urls.UpdateExtraBedBinding, params);
};
export const DeleteExtraBedBinding = params => {
  return http.get(urls.DeleteExtraBedBinding, params);
};
