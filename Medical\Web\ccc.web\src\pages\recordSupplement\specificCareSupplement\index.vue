<!--
 * FilePath     : \src\pages\recordSupplement\SpecificCareSupplemnent\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-09-28 10:56
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-13 12:02
 * Description  : 专项护理补录主页面
 * CodeIterationRecord:
    2415-添加婴儿哺喂专项 2022-04-13 杨欣欣
    添加血糖补录专项 2022年5月24日10:28:46  C.M
    2810-添加CRRT专项 2022-08-12 杨欣欣
    3270-作为护理人员，我需要可以补录末梢血运记录，以利于记录单显示 2023-03-12 -zxz
-->

<template>
  <base-layout header-height="auto" class="specific-care-supplement">
    <search-patient-data
      class="patient-info"
      slot="header"
      @selectPatientData="selectPatientData"
      @change="change"
    ></search-patient-data>
    <div class="specific-care-tabs">
      <el-tabs class="tabs" v-if="showFlag" v-model="activeName">
        <el-tab-pane
          v-for="(component, index) in components"
          :key="index"
          :label="component.label"
          :name="component.name"
        ></el-tab-pane>
      </el-tabs>
      <div class="tabs-content">
        <component :is="activeName" :supplementPatient="patient" :index="randomIndex"></component>
      </div>
    </div>
  </base-layout>
</template>

<script>
import { GetMenuByParentID } from "@/api/Menu";
import baseLayout from "@/components/BaseLayout";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData";
import bloodTransfusionRecord from "@/pages/transfusion/index";
import wound from "@/autoPages/wound/index";
import patientDelivery from "@/autoPages/patientDelivery/index.vue";
import patientDeliveryRecord from "@/autoPages/patientDeliveryRecord/index.vue";
import patientDelirium from "@/pages/patientDelirium/index";
import patientThrombolysis from "@/pages/patientThrombolysis/index";
import babyFeedingRecord from "@/pages/patientBabyFeeding/index.vue";
import neurovascularAssess from "@/pages/PatientNeurovascular/index.vue";
import glucose from "@/pages/glucose/components/glucoseEdit";
import tube from "@/pages/tube/index.vue";
import io from "@/pages/IO/ioRecordMaintenance";
import cRRTRecord from "@/autoPages/cRRTRecord/index";
import rescueRecord from "@/autoPages/patientRescue/index";
import restraint from "@/autoPages/restraint/index";
import peripheralCirculation from "@/autoPages/peripheralCirculation/index";
import patientStomaRecord from "@/autoPages/patientStoma/index";
import patientPain from "@/autoPages/patientPain/index";
import flapCare from "@/autoPages/flap/index";
import patientSedation from "@/autoPages/sedation/index";
import patientCINV from "@/autoPages/patientCINV/index";
export default {
  components: {
    baseLayout,
    searchPatientData,
    bloodTransfusionRecord,
    wound,
    patientDelirium,
    patientThrombolysis,
    babyFeedingRecord,
    neurovascularAssess,
    glucose,
    tube,
    io,
    cRRTRecord,
    rescueRecord,
    restraint,
    patientDelivery,
    peripheralCirculation,
    patientStomaRecord,
    patientDeliveryRecord,
    patientPain,
    flapCare,
    patientSedation,
    patientCINV,
  },
  data() {
    return {
      patient: undefined,
      randomIndex: 1,
      showFlag: false,
      activeName: undefined,
      components: [
        {
          label: "出入量补录",
          name: "io",
        },
        {
          label: "输血补录",
          name: "bloodTransfusionRecord",
        },
        {
          label: "伤口补录",
          name: "wound",
        },
        {
          label: "谵妄补录",
          name: "patientDelirium",
        },
        {
          label: "溶栓用药",
          name: "patientThrombolysis",
        },
        {
          label: "婴儿喂养",
          name: "babyFeedingRecord",
        },
        {
          label: "神经血管评估",
          name: "neurovascularAssess",
        },
        {
          label: "血糖补录",
          name: "glucose",
        },
        {
          label: "导管补录",
          name: "tube",
        },
        {
          label: "CRRT记录单",
          name: "cRRTRecord",
        },
        {
          label: "抢救补录",
          name: "rescueRecord",
        },
        {
          label: "约束补录",
          name: "restraint",
        },
        {
          label: "产时记录补录",
          name: "patientDelivery",
        },
        {
          label: "末梢血运补录",
          name: "peripheralCirculation",
        },
        {
          label: "造口补录",
          name: "patientStomaRecord",
        },
        {
          label: "生产流程记录补录",
          name: "patientDeliveryRecord",
        },
        {
          label: "疼痛补录",
          name: "patientPain",
        },
        {
          label: "皮瓣护理补录",
          name: "flapCare",
        },
        {
          label: "镇静评估补录",
          name: "patientSedation",
        },
        {
          label: "无呕专项补录",
          name: "patientCINV",
        },
      ],
    };
  },
  created() {
    this.getSpecificCareMenuList();
  },
  methods: {
    change() {
      this.patient = undefined;
      this.randomIndex = 1;
      this.showFlag = false;
      if (this.components.length > 0) {
        this.$set(this, "activeName", this.components[0].name);
      }
    },
    //查询病人
    async selectPatientData(patient) {
      this.showFlag = true;
      this.patient = patient;
      this.randomIndex = Math.random();
    },
    getSpecificCareMenuList() {
      let params = {
        menuListID: 18,
      };
      GetMenuByParentID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          let sucComponents = [];
          this.components.forEach((item) => {
            if (res.data.find((m) => m.router.substring(1) == item.name)) {
              sucComponents.push(item);
            }
          });
          this.components = sucComponents;
          if (this.components.length > 0) {
            this.$set(this, "activeName", this.components[0].name);
          }
        } else {
          this.components = [];
        }
      });
    },
  },
};
</script>

<style lang="scss" >
.specific-care-supplement {
  height: 100%;
  .specific-care-tabs {
    height: 100%;
    .tabs {
      height: 40px;
    }
    .tabs-content {
      height: calc(100% - 40px);
    }
  }
}
</style>