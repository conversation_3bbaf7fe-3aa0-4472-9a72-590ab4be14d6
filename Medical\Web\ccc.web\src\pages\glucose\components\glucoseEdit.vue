<!--
 * FilePath     : \src\pages\glucose\components\glucoseEdit.vue
 * Author       : 李正元
 * Date         : 2020-11-03 09:12
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-12-17 17:59
 * Description  : 添加通知医师
 * CodeIterationRecord: 22-05-24 修复血糖专项修改对应时间，对应排程时间不修改 -En
                        22-09-24 增加是否显示血糖新增按钮配置（嘉会需求） -杨欣欣
                        22-11-03 调整血糖的录入方式 -杨欣欣
                        23-03-21 添加选择排程列，同步执行相关排程 -胡长攀
 -->
<template>
  <base-layout class="glucose-edit" v-loading="saveLoading">
    <div slot="header">
      查询范围：
      <el-date-picker
        v-model="startDate"
        type="date"
        placeholder="开始日期"
        :picker-options="pickerOptions"
        @change="getInsulinRecords"
      />
      -
      <el-date-picker v-model="endDate" type="date" placeholder="结束日期" @change="getInsulinRecords" />
      项目：
      <el-select v-model="filterItem" clearable placeholder="请选择项目" @change="filterDataByItem">
        <el-option
          v-for="item in testItemOption"
          :key="item.typeValue"
          :label="item.description"
          :value="item.typeValue"
        ></el-option>
      </el-select>
      <span class="count">筛选出{{ glucoseData.length }}条</span>
      <div class="header-right">
        <add-button v-if="!readonly" @click="addData">新增</add-button>
        <save-button v-if="!readonly" @click="batchSave">保存</save-button>
      </div>
    </div>
    <el-table
      :data="glucoseData"
      border
      stripe
      highlight-current-row
      height="100%"
      ref="glucoseDataTable"
      element-loading-text="加载中……"
      v-loading="recordLoading"
    >
      <el-table-column type="selection" :width="convertPX(40)" align="center" :selectable="selectable" />
      <el-table-column width="120" label="日期" align="center">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.insulinDate"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="date"
            class="date-picker"
            @change="selectRow(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column width="60" label="时间" align="center">
        <template slot-scope="scope">
          <el-time-picker
            v-model="scope.row.insulinTime"
            value-format="HH:mm"
            format="HH:mm"
            class="time-picker"
            @change="selectRow(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column width="100" label="记录项目">
        <template slot-scope="scope">
          <span :style="{ color: scope.row.drug == '2' ? bloodKetoneColor : '' }" v-if="scope.row.id">
            {{ getTestItem(scope.row.drug) }}
          </span>
          <el-select v-else v-model="scope.row.drug" placeholder="请选择项目" @change="changeType(scope.row)">
            <el-option
              v-for="item in testItemOption"
              :key="item.typeValue"
              :label="item.description"
              :value="item.typeValue"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column width="100" label="监测时机">
        <template slot-scope="scope">
          <el-select v-model="scope.row.aCorPCGlucose" placeholder="" @change="selectRow(scope.row)">
            <el-option
              v-for="item in scope.row.timingOption"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column v-if="showInsulinColumn" width="120" label="注射部位">
        <template slot-scope="scope">
          <el-select
            v-model="scope.row.bodyPartID"
            :disabled="scope.row.drug != '4'"
            clearable
            placeholder=""
            @change="selectRow(scope.row)"
          >
            <el-option
              v-for="item in bodyPartList"
              :key="item.typeValue"
              :label="item.description"
              :value="item.typeValue"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column width="120" label="记录结果">
        <template slot-scope="scope">
          <el-input v-model="scope.row.recordValue" @blur="checkTestItemValue(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column width="170" label="相关排程" v-if="glucoseScheduleFalg && !scheduleFlag">
        <template slot-scope="scope">
          <span v-if="scope.row.id">{{ scope.row.patientScheduleName }}</span>
          <el-select
            @visible-change="scheduleItemChange($event, scope.row)"
            v-else
            v-model="scope.row.patientScheduleMainID"
            placeholder="请选择排程"
            clearable
          >
            <el-option
              v-for="item in scope.row.optionalScheduleList"
              :disabled="item.disabled"
              :key="item.patientScheduleMainID"
              :label="item.patientScheduleName"
              :value="item.patientScheduleMainID"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column min-width="80" label="说明">
        <template slot-scope="scope">
          <el-input v-model="scope.row.insulinNote" @change="selectRow(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="通知医师" width="60" align="center">
        <template slot-scope="scope">
          <el-checkbox v-model="scope.row.informPhysician" @change="selectRow(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column v-if="!refillFlag" label="带入护理记录" width="100" align="center">
        <template slot-scope="scope">
          <el-checkbox v-model="scope.row.bringToNursingRecord" @change="selectRow(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column width="80" prop="employeeName" label="记录人" />
      <el-table-column width="70" align="center" v-if="!readonly" label="操作">
        <template slot-scope="scope">
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteData(scope.row, scope.$index)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <progress-view v-show="progressFlag" @closeProgress="restoreProgress" :tableData="messageData"></progress-view>
  </base-layout>
</template>
<script>
import { GetClinicalSettingInfo } from "@/api/Assess";
import { GetOneSettingByTypeAndCode, GetSettingGroupByTypeCodes } from "@/api/Setting";
import { Save, GetPatientInsulin, Delete } from "@/api/Glucose";
import { GetNoExecutionGlucoseScheduleByDate } from "@/api/PatientSchedule";
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
import progressView from "@/components/progressView";
import { GetSettingSwitchByTypeCode, GetSettingValueByTypeCodeAsync } from "@/api/SettingDescription";
export default {
  components: {
    baseLayout,
    progressView,
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
    readonly: {
      type: Boolean,
      return: false,
    },
  },
  data() {
    return {
      filterItem: "1",
      saveLoading: false,
      recordLoading: false,
      startDate: undefined,
      endDate: undefined,
      patient: {},
      //血糖数据
      glucoseData: [],
      allData: [],
      // 默认选中的血糖监测时机
      defaultInsulinTiming: undefined,
      // 检验项目下拉项
      testItemOption: [],
      // 是否能编辑删除该数据
      hasAuthority: false,
      // 是否显示胰岛素列
      showInsulinColumn: false,
      //胰岛素注射的身体部位配置数据
      bodyPartList: [],
      //建议的部位
      suggestBodyPart: undefined,
      sourceID: undefined,
      sourceType: undefined,
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      // 新增时，默认初始化的数据模板
      newRecordTemplate: {},
      // 监测项目映射关系
      testItemMapSetting: [
        {
          drug: 1,
          columnName: "glucose",
          settingTypeCode: "InsulinFrequency",
          option: [],
        },
        {
          drug: 2,
          columnName: "glucoseInUrine",
          settingTypeCode: "InsulinFrequency",
          option: [],
        },
        {
          drug: 3,
          columnName: "glucose",
          settingTypeCode: "InsulinFrequencyNewborn",
          option: [],
        },
        {
          drug: 4,
          columnName: "dose",
          settingTypeCode: "InsulinTreatmentFrequency",
          option: [],
        },
      ],
      //当天未执行的相关专项排程
      patientScheduleList: [],
      //查看是否为排程跳转
      scheduleFlag: false,
      //相关排程列是否显示
      glucoseScheduleFalg: true,
      //血酮颜色
      bloodKetoneColor: "#000",
      //补录标记
      refillFlag: "",
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
    pickerOptions() {
      let that = this;
      return {
        disabledDate(time) {
          return that._datetimeUtil.getTimeDifference(time, that.endDate, "date", "D") < 0;
        },
      };
    },
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = "";
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.refillFlag = "*";
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      async handler(newVal) {
        if (newVal) {
          await this.getTimingSettingGroup();
          await this.getInsulinRecords();
          this.initNewRecordTemplate();
          await this.getScheduleData();
        } else {
          this.glucoseData = [];
        }
      },
      immediate: true,
    },
  },
  async created() {
    this.sourceID = this.$route.query.sourceID;
    this.sourceType = this.$route.query.sourceType;
    if (this.$route.query.patientScheduleMainID || this.sourceID || this.readonly) {
      this.scheduleFlag = true;
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
    this.endDate = this._datetimeUtil.getNowDate();
    this.startDate = this._datetimeUtil.addDate(this.endDate, -7, "yyyy-MM-dd");
    this.getTestItemOption();
    await this.getDefaultTiming();
    this.getInsulinBodyPartList();
    await this.getScheduleData();
    this.getGlucoseScheduleSwitch();
    this.getSettingValueByTypeCode();
  },
  methods: {
    /**
     * description: 根据记录项目筛选数据
     * param {*}
     * return {*}
     */
    filterDataByItem(itemID) {
      this.glucoseData = !itemID ? this.allData : this.allData.filter(({ drug }) => drug === itemID);
    },
    /**
     * description: 获取病人历史血糖数据
     * param {*}
     * return {*}
     */
    async getInsulinRecords() {
      //查无病人数据
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        startDate: this.startDate,
        endDate: this.endDate,
      };
      this.recordLoading = true;
      //数据清空
      this.glucoseData = [];
      await GetPatientInsulin(params).then((response) => {
        if (this._common.isSuccess(response)) {
          let insulinRecords = response.data;
          // 分别找到当前行数据所对应的监测时机下拉项
          insulinRecords.forEach((record) => {
            let currItemTimingSetting = this.testItemMapSetting.find((m) => m.drug == record.drug);
            record.timingOption = currItemTimingSetting ? currItemTimingSetting.option : undefined;
          });
          this.glucoseData = insulinRecords;
          this.allData = this._common.clone(this.glucoseData);
          this.filterDataByItem(this.filterItem);
          this.suggestBodyPart = insulinRecords[0] && insulinRecords[0].suggestBodyPart;
        }
        this.recordLoading = false;
      });
    },
    /**
     * description: 已录入数据，显示监测项目
     * return {*}
     */
    getTestItem(drug) {
      return this.testItemOption.find((m) => m.typeValue === drug)?.description;
    },
    /**
     * description: 初始化新记录模板
     * param {*}
     * return {*}
     */
    initNewRecordTemplate() {
      // 默认选中血糖
      let { option } = this.testItemMapSetting.find((m) => m.drug == "1");
      this.newRecordTemplate = {
        insulinDate: this._datetimeUtil.getNow("yyyy-MM-dd"),
        insulinTime: this._datetimeUtil.getNow("hh:mm"),
        drug: "1",
        bodyPartID: undefined,
        recordValue: undefined,
        insulinNote: undefined,
        employeeName: this.user.userName,
        aCorPCGlucose: this.defaultInsulinTiming,
        timingOption: option.length ? option : undefined,
      };
    },
    /**
     * description: 新增记录数据
     * param {*}
     * return {*}
     */
    async addData() {
      if (this.$route.query.patientScheduleMainID) {
        let checkData = this.glucoseData.find(
          (data) => data.patientScheduleMainID == this.$route.query.patientScheduleMainID
        );
        if (checkData) {
          this._showTip("warning", "已有排程执行数据，不可新增!");
          return;
        }
      }
      let newRow = this._common.clone(this.newRecordTemplate);
      this.glucoseData.unshift(newRow);
      this.selectRow(newRow);
    },
    /**
     * description: 组装一行数据的View
     * param {*} row 行数据
     * return {*}
     */
    createView(row) {
      let mapSetting = this.testItemMapSetting.find((item) => item.drug == row.drug);
      if (!mapSetting) {
        return;
      }
      return {
        id: row.id,
        stationID: this.patient.stationID,
        inpatientID: this.patient.inpatientID,
        patientScheduleMainID: row.patientScheduleMainID ?? this.$route.query.patientScheduleMainID,
        insulinDate: row.insulinDate,
        insulinTime: row.insulinTime,
        // 借字段以区分不同的监测项目
        drug: row.drug,
        [mapSetting.columnName]: row.recordValue,
        bodyPartID: row.bodyPartID,
        aCorPCGlucose: row.aCorPCGlucose,
        insulinNote: row.insulinNote,
        informPhysician: row.informPhysician,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        settingTypeCode: mapSetting.settingTypeCode,
        refillFlag: this.refillFlag,
        index: Math.random(),
        bringToNursingRecord: row.bringToNursingRecord,
      };
    },
    /**
     * description: 批量保存异动数据
     * return {*}
     */
    async batchSave() {
      let table = this.$refs.glucoseDataTable;
      let selectRows = table && this._common.clone(table.selection);
      if (!selectRows || !selectRows.length) {
        this._showTip("warning", "请勾选要保存的数据!");
        return;
      }

      this.saveLoading = true;
      this.progressFlag = true;
      let successCount = 0;
      let failCount = 0;

      // 获取进度百分比
      let getPercent = (index) => Number((((index + 1) / selectRows.length) * 100).toFixed());
      // 保存检查公共方法
      let saveCheck = async (index, predicate, errMsg) => {
        let selectRow = selectRows[index];
        if (await predicate(selectRow)) return true;
        failCount++;
        this.messageData[0].value = getPercent(index);
        const insulinDate = this._datetimeUtil.formatDate(selectRows[index].insulinDate, "yyyy-MM-dd");
        const insulinTime = this._datetimeUtil.formatDate(selectRows[index].insulinTime, "hh:mm");
        this.messageData[3].value += `${insulinDate} ${insulinTime} ${errMsg}<br/>`;
        return false;
      };

      for (let index = 0; index < selectRows.length; index++) {
        // 必填项检查
        if (!(await saveCheck(index, this.requiredCheck, "【记录结果】、【备注】至少填一项"))) {
          continue;
        }
        //必填项检查 补录监测时机非必填 兼顾历史数据问题
        if (
          this.refillFlag != "*" &&
          !(await saveCheck(index, (selectRow) => selectRow.aCorPCGlucose, "请选择【监测时机】"))
        ) {
          continue;
        }
        // 必填项检查
        if (!(await saveCheck(index, (selectRow) => selectRow.drug, "请选择【记录项目】"))) {
          continue;
        }
        // 权限检查
        if (!(await saveCheck(index, this.authorityCheck, "非本人不可操作！"))) {
          continue;
        }

        // 创建保存的View
        let view = this.createView(selectRows[index]);
        // 保存请求
        await Save(view).then((response) => {
          if (response.code != 1) {
            failCount++;
            this.messageData[0].value = getPercent(index);
            const insulinDate = this._datetimeUtil.formatDate(selectRows[index].insulinDate, "yyyy-MM-dd");
            const insulinTime = this._datetimeUtil.formatDate(selectRows[index].insulinTime, "hh:mm");
            this.messageData[3].value += `${insulinDate} ${insulinTime} ${response.message}<br/>`;
            return;
          }
          successCount++;
          this.messageData[0].value = getPercent(index);
        });
      }
      await this.getScheduleData();
      this.messageData[1].value = `${successCount}条保存成功`;
      this.messageData[2].value = `${failCount}条保存失败`;
    },
    /**
     * description: 必填项检查回调
     * param {*} selectRow 选中行数据
     * return {*} 检查是否通过
     */
    requiredCheck(selectRow) {
      return selectRow.insulinNote || selectRow.recordValue === 0 || selectRow.recordValue;
    },
    /**
     * description: 权限检查回调
     * param {*} selectRow 选中行数据
     * return {*} 检查是否通过
     */
    async authorityCheck(selectRow) {
      if (!selectRow.id) {
        return true;
      }
      await this.checkAuthor(selectRow.id, "InsulinRecord", this.userID);
      return this.hasAuthority;
    },
    /**
     * description: 删除记录数据
     * param {*} row 当前行数据
     * param {*} index 当前行数据所处的下标
     * return {*}
     */
    async deleteData(row, index) {
      //来源于给药执行的胰岛素不可删除
      if (row.disabled) {
        this._showTip("warning", "当前行数据不可删除！");
        return;
      }
      if (row.id) {
        //权限检核
        await this.checkAuthor(row.id, "InsulinRecord", this.userID);
        if (!this.hasAuthority) {
          this._showTip("warning", "非本人不可操作！");
          return;
        }
      }
      this._deleteConfirm("", async (flag) => {
        if (flag) {
          if (row.id) {
            this.saveLoading = true;
            await Delete({ insulinRecordID: row.id }).then((response) => {
              if (this._common.isSuccess(response)) {
                this.saveLoading = false;
              }
            });
            this.getInsulinRecords();
            await this.getScheduleData();
          } else {
            this.glucoseData.splice(index, 1);
          }
          this._showTip("success", "删除成功");
        }
      });
    },
    /**
     * description: 记录项目改变时触发
     * param {*} row 项目对象
     * return {*}
     */
    changeType(row) {
      // 数据异动勾选
      this.selectRow(row);
      row.recordValue = undefined;
      row.bodyPartID = undefined;
      row.insulinNote = undefined;
      this.$set(row, "patientScheduleMainID", undefined);
      // 更改监测时机下拉项
      row.aCorPCGlucose = undefined;
      let currItemTimingSetting = this.testItemMapSetting.find((m) => m.drug == row.drug);
      row.timingOption = currItemTimingSetting ? currItemTimingSetting.option : undefined;
      if (row.drug === "1") {
        row.aCorPCGlucose = this.defaultInsulinTiming;
      }
      if (!this.bodyPartList.length) {
        return;
      }
      if (row.drug === "4") {
        // 获取下一次使用的身体部位
        row.bodyPartID = this.suggestBodyPart || "155";
      }
    },
    /**
     * description: 检核el-input输入框中输入的是否为数字
     * param {*} row 当前行数据
     * return {*}
     */
    checkTestItemValue(row) {
      this.selectRow(row);
      //匹配小数和整数的正则
      let reg = /^[\+\-]?\d*?\.?\d*?$/;
      if (!reg.test(row.recordValue)) {
        row.recordValue = "";
        return;
      }
      //过滤012这样的数值
      if (typeof row.recordValue != "number" && !row.recordValue.includes(".") && !row.recordValue.includes("-")) {
        row.recordValue = Number(row.recordValue);
      }
    },
    /**
     * description: 获取检查项目
     * param {*}
     * return {*}
     */
    getTestItemOption() {
      let params = {
        settingTypeCode: "InsulinTest",
      };
      GetClinicalSettingInfo(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.testItemOption = response.data;
          //记录项目中包含胰岛素,就显示胰岛素和身体部位两列
          this.showInsulinColumn = response.data.some((item) => item.settingValue == "InsulinTreatmentFrequency");
        }
      });
    },
    /**
     * description: 获取各项目的监测时机
     * return {*}
     */
    async getTimingSettingGroup() {
      let params = {
        settingTypeCodes: ["InsulinFrequency", "InsulinTreatmentFrequency", "InsulinFrequencyNewborn"],
      };
      await GetSettingGroupByTypeCodes(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.initTimingOptionsByItem(response.data);
        }
      });
    },
    /**
     * description: 根据项目配置码初始化监测时机下拉项字典
     * param {*} testItemMapSettings 各项目对应监测时机, {settingTypeCode: "", settings: [{},{}]}
     * return {*}
     */
    initTimingOptionsByItem(itemTimingSettings) {
      this.testItemMapSetting.forEach((itemTestTiming) => {
        let currTestItemMapping = itemTimingSettings.find((m) => m.settingTypeCode === itemTestTiming.settingTypeCode);
        if (currTestItemMapping) {
          let option = this._common.clone(currTestItemMapping.settings);
          itemTestTiming.option = option;
        }
      });
    },
    /**
     * description: 获取默认监测时机
     * param {*}
     * return {*}
     */
    async getDefaultTiming() {
      let params = {
        settingType: 160,
        settingCode: "DefaultInsulinFrequency",
      };
      await GetOneSettingByTypeAndCode(params).then((response) => {
        if (this._common.isSuccess(response) && response.data) {
          this.defaultInsulinTiming = response.data.typeValue;
        }
      });
    },
    /**
     * description: 获取胰岛素注射部位
     * param {*}
     * return {*}
     */
    getInsulinBodyPartList() {
      let params = {
        settingTypeCode: "InsulinBodyPart",
      };
      GetClinicalSettingInfo(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.bodyPartList = response.data;
        }
      });
    },
    /**
     * description: 行数据异动时自动选择当前行
     * param {*} row 当前异动行
     * return {*}
     */
    selectRow(row) {
      if (row.disabled) {
        return;
      }
      this.$nextTick(() => {
        let table = this.$refs.glucoseDataTable;
        if (table) {
          table.toggleRowSelection(row, true);
        }
      });
    },
    /**
     * description: 权限检核
     * param {*} id 行数据主键
     * param {*} tableName 行数据对应的数据表
     * param {*} userID 当前登录用户ID
     * return {*}
     */
    async checkAuthor(id, tableName, userID) {
      let checkResult = await this._common.checkActionAuthorization(this.user, userID);
      if (!checkResult) {
        this.hasAuthority = false;
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, tableName, !!this.refillFlag);
      if (ret) {
        this._showTip("warning", ret);
        this.hasAuthority = false;
      } else {
        this.hasAuthority = true;
      }
    },
    /**
     * description: 进度条关闭函数，重置进度条内的数据内容
     * param {*}
     * return {*}
     */
    async restoreProgress() {
      this.progressFlag = false;
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
      this.saveLoading = false;
      await this.getInsulinRecords();
    },
    /**
     * description: 获取当天未执行的相关专项排程
     * return {*}
     */
    async getScheduleData() {
      let params = {
        inpatientID: this.patient.inpatientID,
        scheduleDate: this._datetimeUtil.getNowDate(),
      };
      await GetNoExecutionGlucoseScheduleByDate(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.patientScheduleList = response.data;
        }
      });
    },
    /**
     * description: 不同项目选择不同类型的排程
     * params {*}
     * return {*}
     */
    scheduleItemChange(flag, row) {
      //关闭直接返回
      if (!flag || !this.patientScheduleList) {
        return;
      }
      let optionalScheduleList = [];
      //展开重新计算
      //过滤当前项目的排程
      if (row.drug == 1 || row.drug == 3) {
        optionalScheduleList = this.patientScheduleList.filter((m) => m.typeValue == 1);
      } else {
        optionalScheduleList = this.patientScheduleList.filter((m) => m.typeValue == row.drug);
      }
      optionalScheduleList.forEach((item) => {
        let selectData = this.glucoseData.find((data) => data.patientScheduleMainID == item.patientScheduleMainID);
        if (selectData && selectData.patientScheduleMainID != row.patientScheduleMainID) {
          this.$set(item, "disabled", true);
        } else {
          this.$set(item, "disabled", false);
        }
      });
      this.$set(row, "optionalScheduleList", optionalScheduleList);
    },
    /**
     * description: 血糖相关排程列是否显示
     * return {*}
     */
    async getGlucoseScheduleSwitch() {
      let param = {
        SettingTypeCode: "ShowGlucoseScheduleSwitch",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.glucoseScheduleFalg = response.data;
        }
      });
    },
    /**
     * description: 获取血酮颜色
     * return {*}
     */
    async getSettingValueByTypeCode() {
      let param = {
        SettingTypeCode: "BloodKetoneColor",
      };
      await GetSettingValueByTypeCodeAsync(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.bloodKetoneColor = response?.data ?? "#000";
        }
      });
    },
    /**
     * description: 复选框是否禁用
     * param {*} row
     * return {*}
     */
    selectable(row) {
      if (!row.disabled) {
        return true;
      }
      return false;
    },
  },
};
</script>
<style lang="scss">
.glucose-edit {
  height: 100%;
  .base-header {
    .el-date-editor {
      width: 120px;
    }
    .header-right {
      float: right;
    }
    .count {
      margin-left: 10px;
      color: #ff0000;
    }
  }
  .base-content {
    .base-content-wrap {
      .progress {
        z-index: 9999;
      }
      .el-date-editor.date-picker {
        width: 100px;
      }
      .el-date-editor.time-picker {
        width: 45px;
      }
    }
  }
}
</style>
