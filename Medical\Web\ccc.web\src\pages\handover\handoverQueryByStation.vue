<!--
 * FilePath     : \ccc.web\src\pages\handover\handoverQueryByStation.vue
 * Author       : 李正元
 * Date         : 2020-07-02 22:38
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-02-10 09:06
 * Description  : 护士交班单按照病区查询
-->
<template>
  <base-layout class="handover-station">
    <div slot="header">
      <station-selector v-model="stationID"></station-selector>
      <span>日期:</span>
      <el-date-picker
        class="picker-date"
        v-model="handoverDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        :picker-options="checkDate"
        @change="getHandoverTitleView()"
      ></el-date-picker>
      <div class="top-btn">
        <el-button class="print-button" icon="iconfont icon-print" @click="printPDF()">打印</el-button>
      </div>
    </div>
    <handover-report
      :handoverReportTitleView="handoverReportTitleView"
      v-loading.fullscreen.lock="loading"
      :handoverReportSbarView="handoverReportSbarView"
      :stationID="stationID"
      :showBodyPartFlag="bodyPartFlag"
      @getReportParams="getReportParams"
    ></handover-report>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="交班报告单"
      fullscreen
      :visible.sync="showPrint"
      v-loading="pdfLoading"
    >
      <iframe
        id="printIframe"
        :src="ftpPath"
        type="application/x-google-chrome-pdf"
        width="99%"
        height="98%"
        frameborder="1"
        scrolling="auto"
      />
    </el-dialog>
  </base-layout>
</template>
<script>
import { GetHandoverReportTitle, GetHandoverReportSBAR } from "@/api/HandoverReport.js";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { PrintHandoverSummaryPDF } from "@/api/Document.js";
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import handoverReport from "./components/handoverReport";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    stationSelector,
    handoverReport,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  mounted() {
    this.handoverDate = this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -1, "yyyy-MM-dd");
    this.stationID = this.user.stationID;
    this.getShowBodyPartFlag();
    this.getHandoverTitleView();
  },
  data() {
    return {
      loading: false,
      stationID: 0,
      handoverDate: undefined,
      handoverReportTitleView: {},
      handoverReportSbarView: [],
      inpatientIDArrString: "",
      recordsCode: "",
      //PDF路径
      ftpPath: "",
      //显示打印
      showPrint: false,
      //读取PDF
      pdfLoading: false,
      bodyPartFlag: true,
      // 日期不得大于当前日期
      checkDate: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        },
      },
    };
  },
  methods: {
    /**
     * description: 查询交班数据
     * return {*}
     */
    getHandoverTitleView() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return;
      }
      this.handoverReportSbarView = [];
      this.handoverReportTitleView = {};
      let params = {
        stationID: this.stationID,
        startDate: this.handoverDate,
        endDate: this.handoverDate,
        showSignFlag: true,
      };
      this.loading = true;
      GetHandoverReportTitle(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverReportTitleView = res.data;
        }
      });
    },
    /**
     * description: 获取交班内容
     * param {*} inpatientIDArrString
     * param {*} recordsCode
     * return {*}
     */
    getReportParams(inpatientIDArrString, recordsCode) {
      this.inpatientIDArrString = inpatientIDArrString;
      this.recordsCode = recordsCode;
      let handoverIDArr = inpatientIDArrString.split("||");
      let params = {
        inpatientIDArr: handoverIDArr,
        stationID: this.stationID,
        startDate: this.handoverDate,
        endDate: this.handoverDate,
        recordsCode,
      };
      this.loading = true;
      GetHandoverReportSBAR(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverReportSbarView = res.data;
        }
      });
    },
    /**
     * description: 报告打印
     * param {*}
     * return {*}
     */
    printPDF() {
      if (!this.inpatientIDArrString) {
        this._showTip("warning", "无病人交班数据");
        return;
      }
      this.ftpPath = "";
      this.showPrint = true;
      this.pdfLoading = true;
      let params = {
        handoverDate: this.handoverDate,
        stationID: this.stationID,
        inpatientIDArrString: this.inpatientIDArrString,
        recordsCode: this.recordsCode,
      };
      PrintHandoverSummaryPDF(params).then((res) => {
        this.pdfLoading = false;
        if (this._common.isSuccess(res)) {
          this.ftpPath = res.data;
        }
      });
    },
    /**
     * description: 获取是否显示身体部位
     * param {*}
     * return {*}
     */
    getShowBodyPartFlag() {
      let params = {
        settingTypeCode: "HandoverReportBodyPartShowFlag",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.bodyPartFlag = res.data;
        }
      });
    },
  },
};
</script>
<style lang='scss'>
.switch {
  margin-right: 5px;
  display: inline-block;
}
.top-btn {
  float: right;
}
.handover-station {
  .picker-date {
    width: 120px;
  }
}
</style>