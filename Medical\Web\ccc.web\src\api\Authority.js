/*
 * FilePath     : \src\api\Authority.js
 * Author       : 李青原
 * Date         : 2020-05-10 14:50
 * LastEditors  : 马超
 * LastEditTime : 2024-11-20 10:13
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";

const baseUrl = "/Authority";

export const urls = {
  GetUserList: baseUrl + "/GetUserList",
  GetRoleList: baseUrl + "/GetRoleList",
  GetRoleById: baseUrl + "/GetRoleById",
  DeleteRoleById: baseUrl + "/DeleteRoleById",
  UpdataRoleById: baseUrl + "/UpdataRoleById",
  AddRole: baseUrl + "/AddRole",
  GetFunctionTree: baseUrl + "/GetFunctionTree",
  GetFunctionIDByRoleId: baseUrl + "/GetFunctionIDByRoleId",
  GetButtonAuthorityByRoles: baseUrl + "/GetButtonAuthorityByRoles",
  GetAllRoleID: baseUrl + "/GetAllRoleID",
};

export const GetUserList = (params) => {
  return http.get(urls.GetUserList, params);
};
export const GetRoleList = () => {
  return http.get(urls.GetRoleList);
};
export const GetRoleById = (params) => {
  return http.get(urls.GetRoleById, params);
};
export const DeleteRoleById = (params) => {
  return http.post(urls.DeleteRoleById, qs.stringify(params));
};
export const UpdataRoleById = (params) => {
  return http.post(urls.UpdataRoleById, params);
};
export const AddRole = (params) => {
  return http.post(urls.AddRole, params);
};
export const GetFunctionTree = () => {
  return http.get(urls.GetFunctionTree);
};
export const GetFunctionIDByRoleId = (params) => {
  return http.get(urls.GetFunctionIDByRoleId, params);
};
export const GetButtonAuthorityByRoles = (params) => {
  return http.get(urls.GetButtonAuthorityByRoles, params);
};
/**
 * 获取所有角色ID
 */
export const GetAllRoleID = () => {
  return http.get(urls.GetAllRoleID);
};
