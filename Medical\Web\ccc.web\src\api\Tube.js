/*
 * FilePath     : \ccc.web\src\api\Tube.js
 * Author       : 李青原
 * Date         : 2020-06-24 10:19
 * LastEditors  : 杨欣欣
 * LastEditTime : 2022-12-01 15:30
 * Description  :
 */
import http from "@/utils/ajax";
import qs from "qs";
const baseUrl = "/tube";

export const urls = {
  GetRecord: baseUrl + "/GetRecord",
  RemovePatientTube: baseUrl + "/RemovePatientTube",
  ChangePatientTube: baseUrl + "/ChangePatientTube",
  DeleteTubeByID: baseUrl + "/DeleteTubeByID",
  SavePatientTube: baseUrl + "/SavePatientTube",
  GetTube: baseUrl + "/GetTube",
  GetTubeRecordsCodeInfo: baseUrl + "/GetTubeRecordsCodeInfo",
  GetTubeCare: baseUrl + "/GetTubeCare",
  DeleteTubeCare: baseUrl + "/DeleteTubeCare",
  SaveTubeCare: baseUrl + "/SaveTubeCare",
  GetTubeAssessList: baseUrl + "/GetTubeAssessList",
  GetTubeAssessView: baseUrl + "/GetTubeAssessView",
  GetTubeNumByHandover: baseUrl + "/GetTubeNumByHandover",
  GetTubeByID: baseUrl + "/GetTubeByID",
  GetRecordByTubeRecordID: baseUrl + "/GetRecordByTubeRecordID",
  DelPatientTubeDataByHandoverID: baseUrl + "/DelPatientTubeDataByHandoverID",
  GetRemoveReason: baseUrl + "/GetRemoveReason",
  GetRecordIDAndCareMainIDBySourceID:
    baseUrl + "/GetRecordIDAndCareMainIDBySourceID",
  GetFirstTubeCare: baseUrl + "/GetFirstTubeCare",
  GetIOSettingIDByTubeID: baseUrl + "/GetIOSettingIDByTubeID"
};
// 获取导管主信息
export const GetRecord = params => {
  return http.get(urls.GetRecord, params);
};

// 拔管
export const RemovePatientTube = params => {
  return http.post(urls.RemovePatientTube, params);
};
// 换管
export const ChangePatientTube = params => {
  return http.post(urls.ChangePatientTube, params);
};

// 根据导管ID删除导管主表、明细表、评估明细表数据
export const DeleteTubeByID = params => {
  return http.post(urls.DeleteTubeByID, qs.stringify(params));
};
// 保存插管数据
export const SavePatientTube = params => {
  return http.post(urls.SavePatientTube, params);
};
// 获取科室配置的导管
export const GetTube = params => {
  return http.get(urls.GetTube, params);
};
// 获取导管维护主表集合
export const GetTubeCare = params => {
  return http.get(urls.GetTubeCare, params);
};
// 获取导管对应的DepartmentToAssessInfo记录
export const GetTubeRecordsCodeInfo = params => {
  return http.get(urls.GetTubeRecordsCodeInfo, params);
};
// 删除病人导管维护数据
export const DeleteTubeCare = params => {
  return http.post(urls.DeleteTubeCare, qs.stringify(params));
};
// 保存导管维护明细
export const SaveTubeCare = params => {
  return http.post(urls.SaveTubeCare, params);
};
// 获取导管评估细项
export const GetTubeAssessList = params => {
  return http.get(urls.GetTubeAssessList, params);
};
// 获取导管评估细项
export const GetTubeAssessView = params => {
  return http.get(urls.GetTubeAssessView, params);
};
// 获取导管数量
export const GetTubeNumByHandover = params => {
  return http.get(urls.GetTubeNumByHandover, params);
};
//根据tubeListID获取导管信息
export const GetTubeByID = params => {
  return http.get(urls.GetTubeByID, params);
};
//根据TubeRecordID获取导管信息
export const GetRecordByTubeRecordID = params => {
  return http.get(urls.GetRecordByTubeRecordID, params);
};
// 获取导管数量
export const DelPatientTubeDataByHandoverID = params => {
  return http.get(urls.DelPatientTubeDataByHandoverID, params);
};
//获取拔管原因
export const GetRemoveReason = params => {
  return http.get(urls.GetRemoveReason, params);
};
// 根据来源ID获取导管主记录ID和维护记录ID
export const GetRecordIDAndCareMainIDBySourceID = params => {
  return http.get(urls.GetRecordIDAndCareMainIDBySourceID, params);
};
//获取第一条维护记录
export const GetFirstTubeCare = params => {
  return http.get(urls.GetFirstTubeCare, params);
};
// 根据导管ID查找对应IO项目ID
export const GetIOSettingIDByTubeID = params => {
  return http.get(urls.GetIOSettingIDByTubeID, params);
};
