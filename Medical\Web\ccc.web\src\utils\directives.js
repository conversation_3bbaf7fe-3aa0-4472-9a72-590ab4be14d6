import Vue from "vue";
// v-dialogDrag: 弹窗拖拽属性
Vue.directive("dialogDrag", {
  bind(el, binding, vnode, oldVnode) {
    const dialogHeaderEl = el.querySelector(".el-dialog__header");
    const dragDom = el.querySelector(".el-dialog");
    //dialogHeaderEl.style.cursor = 'move';
    dialogHeaderEl.style.cssText += ";cursor:move;";
    dragDom.style.cssText += ";top:0px;";

    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const sty = (() => {
      if (window.document.currentStyle) {
        return (dom, attr) => dom.currentStyle[attr];
      } else {
        return (dom, attr) => getComputedStyle(dom, false)[attr];
      }
    })();

    dialogHeaderEl.onmousedown = (e) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft;
      const disY = e.clientY - dialogHeaderEl.offsetTop;

      const screenWidth = document.body.clientWidth; // body当前宽度
      const screenHeight = document.documentElement.clientHeight; // 可见区域高度(应为body高度，可某些环境下无法获取)

      const dragDomWidth = dragDom.offsetWidth; // 对话框宽度
      const dragDomheight = dragDom.offsetHeight; // 对话框高度

      const minDragDomLeft = dragDom.offsetLeft;
      const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth;

      const minDragDomTop = dragDom.offsetTop;
      const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomheight;

      // 获取到的值带px 正则匹配替换
      let styL = sty(dragDom, "left");
      let styT = sty(dragDom, "top");

      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
      if (styL.includes("%")) {
        styL = +document.body.clientWidth * (+styL.replace(/\%/g, "") / 100);
        styT = +document.body.clientHeight * (+styT.replace(/\%/g, "") / 100);
      } else {
        styL = +styL.replace(/\px/g, "");
        styT = +styT.replace(/\px/g, "");
      }

      document.onmousemove = (e) => {
        // 通过事件委托，计算移动的距离
        let left = e.clientX - disX;
        let top = e.clientY - disY;

        // 边界处理
        if (-left > minDragDomLeft) {
          left = -minDragDomLeft;
        } else if (left > maxDragDomLeft) {
          left = maxDragDomLeft;
        }

        if (-top > minDragDomTop) {
          top = -minDragDomTop;
        } else if (top > maxDragDomTop) {
          top = maxDragDomTop;
        }

        // 移动当前元素
        dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`;
      };

      document.onmouseup = (e) => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    };
  },
});

// 长按事件指令
// 用法： v-longpress="{ function: 'showMessage', params: yourParams }"
Vue.directive("longpress", {
  bind: function (el, binding, vNode) {
    // 确保提供的表达式是函数
    if (!binding.value || !binding.value.function) {
      return;
    }
    if (typeof vNode.context[binding.value.function] !== "function") {
      console.warn(binding.value.function + "不是方法！");
    }
    // 定义变量
    let pressTimer = null;
    // 定义函数处理程序
    // 创建计时器（ 1秒后执行函数 ）
    let start = (e) => {
      if (e.type === "click" && e.button !== 0) {
        return;
      }
      if (pressTimer === null) {
        pressTimer = setTimeout(() => {
          // 执行函数
          vNode.context[binding.value.function](binding.value.params);
        }, 1000);
      }
    };
    // 取消计时器
    let cancel = (e) => {
      // 检查计时器是否有值
      if (pressTimer !== null) {
        clearTimeout(pressTimer);
        pressTimer = null;
      }
    };
    // 添加事件监听器
    el.addEventListener("mousedown", start);
    el.addEventListener("touchstart", start);
    // 取消计时器
    el.addEventListener("click", cancel);
    el.addEventListener("mouseout", cancel);
    el.addEventListener("touchend", cancel);
    el.addEventListener("touchcancel", cancel);
  },
});

// 回车事件
// 用法： v-enter="{ function: 'enterTest', params: yourParams }"
Vue.directive("enter", {
  bind(el, binding, vNode) {
    let keyupEvent = (event) => {
      if (event.target.type == "textarea") {
        return;
      }
      if (event.keyCode == 13) {
        // 先触发失焦事件，检核数值合理性
        event.target.blur();
        vNode.context[binding.value.function](binding.value.params);
      }
    };
    // 确保提供的表达式是函数
    if (!binding.value || !binding.value.function) {
      return;
    }
    if (typeof vNode.context[binding.value.function] !== "function") {
      console.warn(binding.value.function + "不是方法！");
    }
    el.tempKeyupEvent = keyupEvent;
    document.addEventListener("keyup", keyupEvent);
  },
  unbind(el) {
    document.removeEventListener("keyup", el.tempKeyupEvent);
    delete el.tempKeyupEvent;
  },
});

// 格式化时间指令 format不传，默认不带秒，value和type必须传
// <span v-formatTime="{ value: now, type: 'dateTime', format: 'yyyy-MM-dd hh:mm:ss' }"></span>
import datetimeUtil from "@/utils/datetimeUtil";
Vue.directive("formatTime", {
  bind(el, binding) {
    formatTime(el, binding);
  },
  update(el, binding) {
    formatTime(el, binding);
  },
});

var formatTime = (el, binding) => {
  let format = "yyyy-MM-dd hh:mm";
  let result = "";
  if (binding.value) {
    if (binding.value["type"] && binding.value["value"]) {
      // 判断类型
      if (binding.value["type"] == "date") {
        if (binding.value["format"]) {
          format = binding.value["format"];
        } else {
          format = "yyyy-MM-dd";
        }
      } else if (binding.value["type"] == "time") {
        if (binding.value["format"]) {
          format = binding.value["format"];
        } else {
          format = "hh:mm";
        }
      } else if (binding.value["type"] == "dateTime") {
        if (binding.value["format"]) {
          format = binding.value["format"];
        } else {
          format = "yyyy-MM-dd hh:mm";
        }
      }
      result = datetimeUtil.formatDate(binding.value["value"], format);
    }
    el.innerHTML = "";
    el.innerHTML = result;
  }
};
// 自定义指令，挂到body节点上
// 实例：v-tobody="{ id: 'id' }"，传入ID为了防止重复插入
Vue.directive("tobody", {
  inserted(el, binding, vnode) {
    var div = document.createElement("div");
    if (binding.value.id) {
      div.id = binding.value.id;
      if (document.getElementById(binding.value.id)) {
        return;
      }
    }
    el.parentElement.replaceChild(div, el);
    document.body.appendChild(el);
  },
  unbind(el, binding) {
    if (binding.value.id) {
      return;
    }
    document.body.removeChild(el);
  },
});
