<!--
 * FilePath     : \src\autoPages\patientDeliveryRecord\mainComponentSwitch.vue
* Author       : 杨欣欣
 * Date         : 2023-03-11 14:21
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-05-20 17:24
 * Description  : 维护记录切换组件，子组件共用方法、变量在此层定义
 * CodeIterationRecord:
-->
<template>
  <base-layout class="main-component-switch" v-loading="firstDrawerLoading">
    <div slot="header">
      <el-radio-group v-model="pageSetting" @input="resetHeaderParams">
        <el-radio-button v-for="pageSetting in pagesSetting" :key="pageSetting.sort" :label="pageSetting">
          {{ pageSetting.description }}
        </el-radio-button>
      </el-radio-group>
      <el-button @click="addOrModifyCareMain()" v-show="showAddBtn" class="add-button" icon="iconfont icon-add">
        新增
      </el-button>
      <el-button @click="deleteCareMain" type="danger" v-show="showDelBtn" icon="iconfont icon-del">删除</el-button>
      <el-button @click="syncTableData" type="primary" v-show="showRefreshBtn" icon="iconfont icon-refresh">
        获取新生儿建档信息
      </el-button>
    </div>
    <!-- 生产流程子组件 -->
    <keep-alive>
      <component
        :ref="pageSetting.settingValue"
        :mainSaveParams="mainSaveParams"
        :is="pageSetting.settingValue"
        :drawerSize="Number(parentDrawerHeight.replace('px', '')) * 0.8 + 'px'"
        @pageActivated="setProps"
        @pageInit="(value) => (firstDrawerLoading = value)"
        @refreshCurrentRecord="refreshCurrentRecord"
        @setInpatientInfo="setInpatientInfo"
      >
        <!-- 公共头部 -->
        <div slot="main-header">
          <span class="label">执行日期:</span>
          <el-date-picker
            class="date-picker"
            v-model="assessDate"
            type="date"
            :clearable="false"
            value-format="yyyy-MM-dd"
          />
          <el-time-picker
            class="time-picker"
            v-model="assessTime"
            :clearable="false"
            format="HH:mm"
            value-format="HH:mm"
          />
          <station-selector v-model="stationID" label="执行病区:" :width="convertPX(160) + ''" />
          <dept-selector label="" :width="convertPX(140) + ''" v-model="departmentListID" :stationID="stationID" />
        </div>
        <!-- 公共评估模板 -->
        <tabs-layout
          :ref="pageSetting.settingValue + 'TabsLayout'"
          slot="main-tabs-layout"
          v-loading="tabsLayoutLoading"
          checkFlag
          :element-loading-text="tabsLayoutText"
          :template-list="templateList"
          @change-values="(details) => (assessData = details)"
          @checkTN="(flag) => (checkFlag = flag)"
          @button-click="buttonClick"
          @button-record-click="buttonRecordClick"
        />
        <!-- 公共底部 -->
        <div slot="main-footer">
          <el-checkbox class="bring-checkbox" v-model="informPhysician">通知医师</el-checkbox>
          <el-checkbox class="bring-checkbox" v-model="bringToShift">带入交班</el-checkbox>
          <el-checkbox class="bring-checkbox" v-model="bringToNursingRecord">带入护理记录单</el-checkbox>
          <el-button @click="closeDrawer">取消</el-button>
          <el-button @click="save" v-show="mainSaveParams.showEditButton && !readOnly" type="primary">保存</el-button>
        </div>
      </component>
    </keep-alive>
    <!--弹出按钮链接框-->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonName"
      :visible.sync="showButtonDialog"
      fullscreen
      custom-class="no-footer specific-care-view"
      :append-to-body="true"
    >
      <iframe v-if="showButtonDialog" ref="buttonDialog" scrolling="no" frameborder="0" width="100%" height="99%" />
    </el-dialog>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="buttonRecordTitle"
      :visible.sync="showButtonRecordDialog"
      custom-class="no-footer"
      :append-to-body="true"
    >
      <risk-component :params="conponentParams" @result="result"></risk-component>
    </el-dialog>
  </base-layout>
</template>

<script>
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
import preDeliveryRecord from "@/autoPages/patientDeliveryRecord/components/preDeliveryRecord";
import delivery from "@/autoPages/patientDeliveryRecord/components/delivery";
import newbornAssess from "@/autoPages/patientDeliveryRecord/components/newbornAssess";
import newbornRecord from "@/autoPages/patientDeliveryRecord/components/newbornRecord";
import postDeliveryAssess from "@/autoPages/patientDeliveryRecord/components/postDeliveryAssess";
import deliveryCheck from "@/autoPages/patientDeliveryRecord/components/deliveryCheck";
import postDeliveryCheck from "@/autoPages/patientDeliveryRecord/components/postDeliveryCheck";
import preDeliveryCheck from "@/autoPages/patientDeliveryRecord/components/preDeliveryCheck";
import {
  GetDeliveryAssessViewAsync,
  AddPatientDeliveryRecordRecord,
  SavePatientDeliveryRecordCareMain,
  SyncTableData,
} from "@/api/PatientDeliveryRecord";
import { GetBringToShiftSetting, GetClinicSettingByTypeCode } from "@/api/Setting.js";
import { GetButtonData } from "@/api/Assess";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import { GetSettingValuesByTypeCodeAndValue } from "@/api/Setting";
import { GetDateTimeByAssessListID } from "@/api/PatientScore";
export default {
  components: {
    baseLayout,
    preDeliveryRecord,
    delivery,
    newbornAssess,
    newbornRecord,
    postDeliveryAssess,
    stationSelector,
    deptSelector,
    tabsLayout,
    riskComponent,
    deliveryCheck,
    postDeliveryCheck,
    preDeliveryCheck,
  },
  props: {
    mainSaveParams: {
      type: Object,
      default: () => {
        return {};
      },
      required: true,
    },
    parentDrawerHeight: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      firstDrawerLoading: false,
      // 组件公共成员默认值
      defaultParams: {
        // 跳转自动选中对应维护记录页签
        linkRecordsCode: undefined,
        // 主键ID
        recordID: "temp" + this._common.guid(),
        // 来源排程
        scheduleMainID: undefined,
        // 默认模板
        recordsCode: "PreDeliveryCheck",
        // 补录标记
        refillFlag: "",
        // 是否显示保存按钮
        showEditButton: true,
        // 权限检核方法
        checkAuthor: this.checkAuthor,
        // 抽屉弹出方向
        drawerDirection: "btt",
      },
      // 抽屉顶部页签参数
      pageSetting: {},
      pagesSetting: [],
      // 抽屉顶部按钮显示控制
      showAddBtn: false,
      showDelBtn: false,
      showRefreshBtn: false,
      selectedCareMain: undefined,
      // 新增时的头部成员
      departmentListID: undefined,
      stationID: undefined,
      assessDate: undefined,
      assessTime: undefined,
      // 评估模板组件成员
      tabsLayoutLoading: false,
      templateList: [],
      tabsLayoutText: "加载中……",
      assessData: [],
      checkTNFlag: true,
      // 新增时底部成员
      informPhysician: false,
      informPhysicianSetting: false,
      bringToShift: false,
      bringToShiftSetting: false,
      bringToNursingRecord: false,
      bringToNursingRecordSetting: false,
      // 弹窗相关参数
      buttonName: "",
      showButtonDialog: false,
      buttonAssessListID: "",
      buttonRecordTitle: "",
      showButtonRecordDialog: false,
      conponentParams: undefined,
      riskDateTimeReadonly: false,
      riskAssessDateTime: undefined,
      //待产产程记录及产后记录 新增默认时间配置
      haveDefalutTimeIntervalRecordsCode: ["DeliveryStart", "PostDeliveryAssess"],
      defalutTimeIntervalDeliveryStart: [],
      defalutTimeIntervalPostDeliveryAssess: [],
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      token: "getToken",
      readOnly: "getReadOnly",
    }),
    defaultParamKeys() {
      return Object.keys(this.defaultParams);
    },
    // 子组件实例
    childPageInstance() {
      return this.$refs[this.pageSetting.settingValue];
    },
    // 子组件中的评估模板实例
    childPageTabsLayoutInstance() {
      // TODO: 临时处理，解决表格模板页签之间切换，监测不到值变化，导致获取不到实例的问题
      const showFlag = this.childPageInstance?.drawerSetting?.showFlag;
      return this.$refs[this.pageSetting.settingValue + "TabsLayout"];
    },
  },
  watch: {
    mainSaveParams: {
      handler() {
        this.initParams();
      },
      immediate: true,
    },
    showButtonDialog(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
  },
  created() {
    //获取待产产程记录及产后记录 新增默认时间配置
    this.haveDefalutTimeIntervalRecordsCode.forEach((code) => this.getAddCareMainDefalutTimeInterval(code));
    this.getPagesSetting();
    this.getBringHandOverSetting();
    this.getCheckBoxSetting("DeliveryAutoInterventionToRecord", "bringToNursingRecord", "bringToNursingRecordSetting");
    this.getCheckBoxSetting("InformPhysician_PatientDeliveryRecord", "informPhysician", "informPhysicianSetting");
  },
  methods: {
    /**
     * description: 初始化未赋值的变量
     * return {*}
     */
    initParams() {
      if (!this.mainSaveParams) {
        this.mainSaveParams = this.defaultParams;
        return;
      }
      this.defaultParamKeys.forEach(
        (key) => (this.mainSaveParams[key] = this.mainSaveParams[key] || this.defaultParams[key])
      );
      this.stationID = this.mainSaveParams.currentPatient.stationID;
      this.departmentListID = this.mainSaveParams.currentPatient.departmentListID;
    },
    /**
     * description: 维护记录新增/修改，具体新增内容随子组件而变化
     * return {*}
     */
    async addOrModifyCareMain(row) {
      this.selectedCareMain = undefined;
      if (row) {
        await this.checkAuthor(row.patientDeliveryCareMainID, "PatientDeliveryCareMain", row.userID);
        if (this.childPageInstance) {
          this.childPageInstance.careMains = [row];
        }
        this.selectedCareMain = row;
        this.selectedCareMain.isAdd = false;
        this.assessDate = row.assessDate;
        this.assessTime = row.assessTime;
        this.stationID = row.stationID;
        this.departmentListID = row.departmentListID;
        this.bringToNursingRecord = row.bringToNursingRecord;
        this.bringToShift = row.bringToShift;
      } else {
        const addCheck = this.childPageInstance?.addCheck;
        if (addCheck && !(await addCheck())) {
          return;
        }

        this.selectedCareMain = {
          isAdd: true,
          patientDeliveryCareMainID: this._common.guid(),
        };
        this.mainSaveParams.showEditButton = true;
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
        this.stationID = this.mainSaveParams.currentPatient.stationID;
        this.departmentListID = this.mainSaveParams.currentPatient.departmentListID;
        this.bringToShift = this.bringToShiftSetting;
        this.informPhysician = this.informPhysicianSetting;
        this.bringToNursingRecord = this.bringToNursingRecordSetting;
        //新增记录 依据配置调整默认时间
        if (this.haveDefalutTimeIntervalRecordsCode.includes(this.mainSaveParams.recordsCode)) {
          this.renewAssessDateTime(this.mainSaveParams.recordsCode);
        }
      }
      const openOrCloseDrawer = this.childPageInstance?.openOrCloseDrawer;
      openOrCloseDrawer && openOrCloseDrawer(true, row ? "修改" : "新增");
      await this.getDeliveryAssessView();
    },
    /**
     * description: 权限检核
     * param {*} id 主记录/主表ID
     * param {*} tableName
     * return {*}
     */
    async checkAuthor(id, tableName, userID) {
      const checkResult = await this._common.checkActionAuthorization(this.user, userID);
      if (!checkResult) {
        this.mainSaveParams.showEditButton = false;
        this._showTip("warning", "非本人不可操作");
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, tableName);
      if (ret) {
        this.mainSaveParams.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.mainSaveParams.showEditButton = true;
      }
    },
    /**
     * description: 获取评估模板
     * return {*}
     * param {*}
     */
    async getDeliveryAssessView() {
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "加载中……";
      const params = {
        recordsCode: this.mainSaveParams.recordsCode,
        age: this.mainSaveParams.currentPatient.age,
        gender: this.mainSaveParams.currentPatient.genderCode,
        departmentListID: this.mainSaveParams.currentPatient.departmentListID,
        inpatientID: this.mainSaveParams.currentPatient.inpatientID,
        dateOfBirth: this.mainSaveParams.currentPatient.dateOfBirth,
        recordID: this.mainSaveParams.recordID.includes("temp") ? undefined : this.mainSaveParams.recordID,
        careMainID: `${this.selectedCareMain.isAdd ? "temp_" : ""}${this.selectedCareMain.patientDeliveryCareMainID}`,
      };
      this.templateList = [];
      await GetDeliveryAssessViewAsync(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.templateList = result.data;
        }
      });
      this.tabsLayoutLoading = false;
      this.tabsLayoutText = "";
    },
    /**
     * description: 保存记录
     * return {*}
     */
    async save() {
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "保存中……";
      if (!this.saveCheck()) {
        this.tabsLayoutLoading = false;
        this.tabsLayoutText = "";
        return;
      }
      if (this.mainSaveParams.recordID.includes("temp")) {
        await this.recordSave();
      } else {
        await this.careMainSave();
      }
    },
    /**
     * description: 主记录保存
     * return {*}
     */
    async recordSave() {
      const saveView = {
        main: {
          patientDeliveryRecordID: this.mainSaveParams.recordID,
          addDate: this.assessDate + " " + this.assessTime,
          inpatientID: this.mainSaveParams.currentPatient.inpatientID,
          patientID: this.mainSaveParams.currentPatient.patientID,
          occuredStationID: this.stationID,
          stationID: this.mainSaveParams.currentPatient.stationID,
          occuredDepartmentID: this.departmentListID,
          departmentListID: this.mainSaveParams.currentPatient.departmentListID,
          chartNo: this.mainSaveParams.currentPatient.chartNo,
          bedNumber: this.mainSaveParams.currentPatient.bedNumber,
          bedID: this.mainSaveParams.currentPatient.bedID,
          caseNumber: this.mainSaveParams.currentPatient.caseNumber,
          patientScheduleMainID: this.mainSaveParams.scheduleMainID,
          refillFlag: this.mainSaveParams.refillFlag,
        },
        nursingLevel: this.mainSaveParams.currentPatient.nursingLevel,
        gender: this.mainSaveParams.currentPatient.gender,
        recordsCode: this.pageSetting.typeValue,
        bringToShift: this.bringToShift,
        bringToNursingRecord: this.bringToNursingRecord,
        details: this.getDetails(),
        patientDeliveryCareMainID: this.selectedCareMain.patientDeliveryCareMainID,
      };
      await AddPatientDeliveryRecordRecord(saveView).then(async (result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
        }
        this.tabsLayoutLoading = false;
        this.tabsLayoutText = "";
        this.mainSaveParams.recordID = this.mainSaveParams.recordID.substring(4);
        const openOrCloseDrawer = this.childPageInstance?.openOrCloseDrawer;
        //刷新主记录
        this.refreshCurrentRecord();
        openOrCloseDrawer && openOrCloseDrawer(false);
      });
      // 保存后，重新获取子组件内容
      const childPageInit = this.childPageInstance?.init;
      childPageInit && (await childPageInit());
    },
    /**
     * description: 维护记录保存
     * return {*}
     */
    async careMainSave() {
      const saveView = {
        isAdd: this.selectedCareMain.isAdd,
        main: {
          patientDeliveryRecordID: this.mainSaveParams.recordID,
          patientDeliveryCareMainID: this.selectedCareMain.patientDeliveryCareMainID,
          inpatientID: this.mainSaveParams.currentPatient.inpatientID,
          patientID: this.mainSaveParams.currentPatient.patientID,
          stationID: this.mainSaveParams.currentPatient.stationID,
          departmentListID: this.mainSaveParams.currentPatient.departmentListID,
          chartNo: this.mainSaveParams.currentPatient.chartNo,
          bedNumber: this.mainSaveParams.currentPatient.bedNumber,
          bedID: this.mainSaveParams.currentPatient.bedID,
          caseNumber: this.mainSaveParams.currentPatient.caseNumber,
          nursingLevel: this.mainSaveParams.currentPatient.nursingLevel,
          patientScheduleMainID: this.selectedCareMain?.patientScheduleMainID || this.mainSaveParams.scheduleMainID,
          recordsCode: this.pageSetting.typeValue,
          assessDate: this.assessDate,
          assessTime: this.assessTime,
          bringToShift: this.bringToShift,
          informPhysician: this.informPhysician,
          bringToNursingRecord: this.bringToNursingRecord,
          refillFlag: this.mainSaveParams.refillFlag,
        },
        newBornID: this.childPageInstance?.selectedNewBorn?.newBornID,
        gender: this.mainSaveParams.currentPatient.gender,
        details: this.getDetails(),
      };
      await SavePatientDeliveryRecordCareMain(saveView).then((result) => {
        this.tabsLayoutLoading = false;
        this.tabsLayoutText = "";
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
        }
      });
      //刷新主记录
      this.refreshCurrentRecord();
      const openOrCloseDrawer = this.childPageInstance?.openOrCloseDrawer;
      openOrCloseDrawer && openOrCloseDrawer(false);
      // 保存后，重新获取子组件内容
      const childPageInit = this.childPageInstance?.init;
      childPageInit && (await childPageInit());
    },
    /**
     * description: 明细获取
     * return {*}
     */
    getDetails() {
      const details = [];
      //组装保存Detail数据
      for (const content of this.assessData) {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          bookMarkID: content.bookMarkID,
          assessValueJson: content.controlerType == "BD" && content.assessValue ? content.assessValue : undefined,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        details.push(detail);
      }
      return details;
    },
    /**
     * description: 评估保存检核
     * return {*} true/false
     * param {*}
     */
    saveCheck() {
      if (!this.stationID) {
        this._showTip("warning", "请选择发生病区");
        return false;
      }
      if (!this.departmentListID) {
        this._showTip("warning", "请选择发生科室");
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      if (!this.assessData.length) {
        this._showTip("warning", "请选择或填写相关项目！");
        return false;
      }
      if (!this.childPageTabsLayoutInstance?.checkRequire()) {
        return false;
      }
      return true;
    },
    /**
     * description: 获取页签配置
     * return {*}
     */
    getPagesSetting() {
      const params = {
        settingTypeCode: "DeliveryMaintainType",
      };
      GetClinicSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res) && res.data.length) {
          if (this.mainSaveParams.linkRecordsCode) {
            this.pageSetting = res.data.find((m) => m.settingValue === this.mainSaveParams.linkRecordsCode);
            this.pagesSetting = [this.pageSetting];
            return;
          }
          this.pagesSetting = res.data;
          this.pageSetting = res.data[0];
        }
      });
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "Delivery",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.bringToShift = res.data;
          this.bringToShiftSetting = res.data;
        }
      });
    },
    /**
     * description: 获取带入护理记录、通知医师默认勾选配置
     * param {*} settingTypeCode 大分类
     * param {*} checkBoxPropName 勾选项变量名
     * param {*} settingPropName 配置项变量名
     * return {*}
     */
    getCheckBoxSetting(settingTypeCode, checkBoxPropName, settingPropName) {
      let params = {
        settingTypeCode: settingTypeCode,
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this[checkBoxPropName] = res.data;
          this[settingPropName] = res.data;
        }
      });
    },
    /**
     * description: 接收子组件计算后回传的数据
     * param {*} returnValues 回传对象
     * return {*}
     */
    setProps(returnValues) {
      this.showAddBtn = returnValues.showAddBtn ?? false;
      this.showDelBtn = returnValues.showDelBtn ?? false;
      this.showRefreshBtn = returnValues.showRefreshBtn ?? false;
      this.assessDate = returnValues.assessDate ?? this._datetimeUtil.getNowDate();
      this.assessTime = returnValues.assessTime ?? this._datetimeUtil.getNowTime();

      if (returnValues.careMainID) {
        this.selectedCareMain = {
          isAdd: false,
          patientDeliveryCareMainID: returnValues.careMainID,
        };
      } else {
        this.selectedCareMain = {
          isAdd: true,
          patientDeliveryCareMainID: this._common.guid(),
        };
      }
    },
    /**
     * description: 页签切换，重置组件头部控件的变量
     * return {*}
     */
    resetHeaderParams(newVal) {
      this.mainSaveParams.showEditButton = true;
      this.mainSaveParams.recordsCode = newVal.typeValue;
      this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
      this.stationID = this.mainSaveParams.currentPatient.stationID;
      this.departmentListID = this.mainSaveParams.currentPatient.departmentListID;
    },
    /**
     * description: 点击取消，关闭抽屉
     * return {*}
     */
    closeDrawer() {
      if (this.childPageInstance?.openOrCloseDrawer) {
        this.childPageInstance.openOrCloseDrawer(false);
      } else {
        this.$parent.closeDrawer();
      }
    },
    /**
     * description: 重置选中的页签，一级抽屉关闭时调用
     * return {*}
     */
    resetPage() {
      this.pageSetting = this.pagesSetting[0];
      this.resetHeaderParams(this.pageSetting);
      this.firstDrawerLoading = false;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    result(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brAssessListID);
      }
    },
    /**
     * description: 添加完更新按钮数量
     * return {*}
     * param {*} assessListID 更新明细按钮的assessListID
     */
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.childPageTabsLayoutInstance?.updateButtonItem) {
          this.childPageTabsLayoutInstance.updateButtonItem(item);
        }
      });
    },
    /**
     * description: 评估组件按钮事件
     * return {*}
     * param {*} content 跳转明细项
     */
    buttonClick(content) {
      this.showButtonDialog = true;
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.mainSaveParams.currentPatient.bedNumber.replace(
        /\+/g,
        "%2B"
      )}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        `&sourceID=${this.selectedCareMain.patientDeliveryCareMainID}` +
        `&scheduleDate=${this.assessDate}` +
        `&scheduleTime=${this.assessTime}` +
        "&sourceType=Delivery" +
        "&isDialog=true";
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog?.contentWindow?.location?.replace(url);
      });
    },
    /**
     * description: 初始化BR组件
     * return {*}
     * param {*} content BR跳转风险项
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      await this.getScheduleDateTime(content.assessListID);
      this.conponentParams = {
        patientInfo: this.mainSaveParams.currentPatient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: `Delivery_${content.assessListID}`,
        sourceID: this.selectedCareMain.patientDeliveryCareMainID,
        assessTime: this.riskAssessDateTime,
        assessTimeReadonly: this.riskDateTimeReadonly,
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 根据AssessListID获取风险表评估时间
     * param {*}
     * return {*}
     */
    async getScheduleDateTime(assessListID) {
      if (!(await this.checkScheduleDateNeedCalc(assessListID))) {
        return;
      }
      let scheduleDateTime = undefined;
      // 默认为维护记录评估时间
      this.riskAssessDateTime = `${this.assessDate} ${this.assessTime}`;
      const params = {
        inpatientID: this.mainSaveParams.currentPatient.inpatientID,
        patientID: this.mainSaveParams.currentPatient.patientID,
        assessListID: assessListID,
      };
      await GetDateTimeByAssessListID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          scheduleDateTime = result.data;
        }
      });
      // 依据接口返回值调整：是否只读、风险评估时间
      this.riskDateTimeReadonly = !!scheduleDateTime;
      if (scheduleDateTime) {
        this.riskAssessDateTime = scheduleDateTime;
      }
    },
    /**
     * description: 检查此AssessListID是否要计算评估时间
     * param {*} assessListID
     * return {*}
     */
    async checkScheduleDateNeedCalc(assessListID) {
      if (!this.needCalcDateAssessListIDs) {
        const params = {
          settingTypeCode: "CalcDateTimeByAssessListID",
          typeValue: "Risk",
        };
        await GetSettingValuesByTypeCodeAndValue(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.needCalcDateAssessListIDs = result.data;
          }
        });
      }
      return (this.needCalcDateAssessListIDs || []).some((m) => m == assessListID);
    },
    /**
     * description: 更新按钮数量API
     * return {*}
     * param {*} assessListID 更新明细按钮的assessListID
     */
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.mainSaveParams.currentPatient.inpatientID,
        recordsCode: this.mainSaveParams.recordsCode,
        assessListID: assessListID,
        sourceID: this.selectedCareMain.patientDeliveryCareMainID,
        sourceType: `Delivery_${assessListID}`,
        isEdit: !this.selectedCareMain.isAdd,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: 刷新主记录
     * return {*}
     */
    refreshCurrentRecord() {
      this.$emit("refreshCurrentRecord");
    },
    /**
     * @description: 更新当前患者信息
     * @param {*} newInpatientInfo 新的患者信息
     * @return {*}
     */
    setInpatientInfo(newInpatientInfo) {
      this.$emit("setInpatientInfo", newInpatientInfo);
    },
    /**
     * @description: 删除维护记录
     * @return {*}
     */
    deleteCareMain() {
      this.childPageInstance?.deleteCareMain?.();
    },
    /**
     * @description: 拉取表格数据
     * @return {*}
     */
    async syncTableData() {
      const params = {
        caseNumber: this.mainSaveParams.motherInfo.caseNumber,
      };
      this.firstDrawerLoading = true;
      await SyncTableData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.childPageInstance.getCareMains();
        }
      });
      this.firstDrawerLoading = false;
    },
    /**
     * description: 获取待产产程记录及产后记录 新增默认时间配置
     * return {*}
     */
    getAddCareMainDefalutTimeInterval(recordsCode) {
      let params = {
        settingTypeCode: "DeliveryRecordDefaultTimeInterval",
        typeValue: recordsCode,
      };
      GetSettingValuesByTypeCodeAndValue(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this["defalutTimeInterval" + recordsCode] = res?.data ?? [];
        }
      });
    },
    /**
     * description: 新增默认时间处理
     * param {*} recordsCode
     * return {*}
     */
    renewAssessDateTime(recordsCode) {
      if (!recordsCode) {
        return;
      }
      let careMains = this.childPageInstance.careMains;
      let setting = this["defalutTimeInterval" + this.mainSaveParams.recordsCode];
      if (!setting?.length) {
        return;
      }
      let currentPatientDeliveryRecord = this.mainSaveParams?.currentPatientDeliveryRecord;
      this["renewAssessDateTime" + recordsCode](careMains, setting, currentPatientDeliveryRecord);
    },
    /**
     * description: 待产产程观察记录 宫口全开后新增默认为最后一次加配置的分钟数
     * param {*} careMains
     * param {*} setting
     * param {*} currentPatientDeliveryRecord
     * return {*}
     */
    renewAssessDateTimeDeliveryStart(careMains, setting, currentPatientDeliveryRecord) {
      if (!careMains?.length) {
        return;
      }
      let lastCareMain = careMains[careMains.length - 1];
      if (!lastCareMain?.assessDate || !lastCareMain?.assessTime) {
        return;
      }
      //index=0 工口全开之前默认时间配置
      //index=1 工口全开之后默认时间配置
      if (setting.length < 2) {
        return;
      }
      let newAssessDateTime = this.assessDateTimeAddMinutes(
        lastCareMain.assessDate,
        lastCareMain.assessTime,
        setting[currentPatientDeliveryRecord?.fullyDilatedTime ? 1 : 0]
      );
      this.assessDate = newAssessDateTime.assessDate;
      this.assessTime = newAssessDateTime.assessTime;

      //
    },
    /**
     * description: 产后记录在胎盘娩出后 新增维护记录时间默认为胎盘娩出时间加配置分钟数
     * param {*} careMains
     * param {*} setting
     * param {*} currentPatientDeliveryRecord
     * return {*}
     */
    renewAssessDateTimePostDeliveryAssess(careMains, setting, currentPatientDeliveryRecord) {
      //胎盘未娩出 或者 维护记录数量大于配置数量 直接返回
      if (!currentPatientDeliveryRecord?.thirdStageCompletedTime || careMains?.length >= setting?.length) {
        return;
      }
      let thirdStageCompletedTime = currentPatientDeliveryRecord.thirdStageCompletedTime;

      this.assessDate = this._datetimeUtil.addMinutes(
        thirdStageCompletedTime,
        setting[careMains?.length],
        "yyyy-MM-dd"
      );
      this.assessTime = this._datetimeUtil.addMinutes(thirdStageCompletedTime, setting[careMains?.length], "hh:mm");
    },
    /**
     * description: 分钟计算处理
     * param {*} assessDate
     * param {*} assessTime
     * param {*} minutes
     * return {*}
     */
    assessDateTimeAddMinutes(assessDate, assessTime, minutes) {
      let lastCareMainAssessDate = this._datetimeUtil.formatDate(assessDate, "yyyy-MM-dd");
      let lastCareMainAssessTime = this._datetimeUtil.formatDate(assessTime, "hh:mm");
      let assessDateTime = lastCareMainAssessDate + " " + lastCareMainAssessTime;
      return {
        assessDate: this._datetimeUtil.addMinutes(assessDateTime, minutes, "yyyy-MM-dd"),
        assessTime: this._datetimeUtil.addMinutes(assessDateTime, minutes, "hh:mm"),
      };
    },
  },
};
</script>

<style lang="scss"></style>
