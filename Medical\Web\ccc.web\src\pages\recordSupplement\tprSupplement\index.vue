<!--
 * FilePath     : \src\pages\recordSupplement\tprSupplement\index.vue
 * Author       : 苏军志
 * Date         : 2020-10-10 09:50
 * LastEditors  : 来江禹
 * LastEditTime : 2025-01-16 08:50
 * Description  :
-->
<template>
  <base-layout class="tpr-supplement" header-height="auto">
    <search-patient-data
      class="patient-info"
      slot="header"
      @selectPatientData="selectPatientData"
      @change="change"
    ></search-patient-data>
    <div class="main-tabs">
      <el-button
        v-if="isShow"
        class="temperature-button"
        type="primary"
        icon="iconfont icon-temperature"
        @click="getTemperatureTabsData"
      >
        体温单
      </el-button>
      <el-tabs class="tabs" v-model="activeName" v-if="isShow">
        <el-tab-pane
          v-for="(component, index) in components"
          :key="index"
          :label="component.label"
          :name="component.name"
        >
          <component
            v-if="patientInfo && activeName == component.name"
            :is="component.name"
            :patientinfo="patientInfo"
            :supplemnentPatient="patientInfo"
            :index="randomIndex"
            model="component"
          ></component>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :fullscreen="true"
      :title="getTemperatureTitle('体温单')"
      :visible.sync="temperatureVisiable"
      v-if="temperatureVisiable"
      v-loading="temperatureLoadingBoole"
      element-loading-text="加载中……"
    >
      <temperature-list v-model="temperatureTabsData"></temperature-list>
    </el-dialog>
  </base-layout>
</template>
<script>
import { GetPatientVitalSignData } from "@/api/VitalSign";
import baseLayout from "@/components/BaseLayout";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData";
import IoRecordSupplement from "@/pages/IO/ioRecordMaintenance";
import tubeSupplement from "@/pages/tube/index";
import PatientEvent from "@/pages/patientEvent/index";
import ProfileLogSupplement from "../profileLogSupplement/index";
import PatientNursingRecordDetailSupplement from "../patientNursingRecordDetailSupplement/index";
import nursingRecord from "../nursingRecord/index";
import drugAllergy from "@/pages/patientAllergy/index";
import temperatureList from "@/components/TemperatureList";
import { GetMenuByID } from "@/api/Menu";
import { GetClinicSettingByTypeCode } from "@/api/Setting";
export default {
  components: {
    baseLayout,
    searchPatientData,
    IoRecordSupplement,
    tubeSupplement,
    PatientEvent,
    ProfileLogSupplement,
    PatientNursingRecordDetailSupplement,
    temperatureList,
    nursingRecord,
    drugAllergy,
  },
  data() {
    return {
      patientInfo: undefined,
      activeName: "tubeSupplement",
      randomIndex: 1,
      isShow: false,
      //体温单开关
      temperatureLoadingBoole: false,
      //体温单tabs和PDF数据
      temperatureTabsData: {},
      //体温单弹窗开关
      temperatureVisiable: false,
      // 动态组件集合
      components: [
        {
          index: 0,
          label: "生命体征",
          name: "nursingRecord",
        },
        {
          index: 1,
          label: "出入量",
          name: "ioRecordSupplement",
        },
        {
          index: 2,
          label: "患者事件",
          name: "patientEvent",
        },
        {
          index: 3,
          label: "导管",
          name: "tubeSupplement",
        },
        {
          index: 4,
          label: "过敏药物",
          name: "drugAllergy",
        },
        // {
        //   index: 5,
        //   label: "其他",
        //   name: "profileLogSupplement",
        // },
      ],
      //过敏药物菜单ID
      allergyFunctionID: 85,
       // 病历与切换时间点的TypeValue对照关系
       dataSourceTypeValueMapping: {
        nursingRecord: "NewNursingRecodLimitDateTime",
        healthEducationRecord: "HealthEduRecordLimitDateTime",
        turningRecord: "TurningRecordPdfLimitDateTime",
      },
      switchSettings: []
    };
  },
  mounted() {
    this.GetMenu();
  },
  methods: {
    change() {
      this.patientInfo = undefined;
      this.randomIndex = 1;
      this.activeName = "nursingRecord";
      this.isShow = false;
    },
    /**
     * description: 查询病人
     * param {*} patientInfo
     * return {*}
     */
    selectPatientData(patientInfo) {
      this.isShow = true;
      this.patientInfo = patientInfo;
      this.randomIndex = Math.random();
      this.getLimitDate();
    },
    /**
     * description: 组装体温单标题
     * param {*} value
     * return {*}
     */
    getTemperatureTitle(value) {
      let title = "";
      title =
        this.patientInfo.bedNumber +
        " - " +
        this.patientInfo.patientName +
        "【" +
        this.patientInfo.gender +
        "-" +
        this.patientInfo.age +
        "】" +
        "-- " +
        value;
      return title;
    },
    /**
     * description: 获取该病人体温单数据
     * return {*}
     */
    getTemperatureTabsData() {
      this.temperatureVisiable = true;
      this.temperatureLoadingBoole = true;
      let params = {
        inPatientID: this.patientInfo.inpatientID,
        startTime: this.patientInfo.admissionDate,
        endTime: this.patientInfo.dischargeDateTimeView
          ? this.patientInfo.dischargeDateTimeView
          : this._datetimeUtil.getNow(),
      };
      GetPatientVitalSignData(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.length == 0) {
            this._showTip("warning", "暂无数据");
            return;
          }
          this.temperatureTabsData = res.data;
          this.temperatureLoadingBoole = false;
        }
      });
    },
    /**
     * description:获取过敏药物菜单 用于是否显示
     * return {*}
     */
    GetMenu() {
      let params = {
        menuListID: this.allergyFunctionID,
      };
      GetMenuByID(params).then((result) => {
        if (result.code == 0) {
          this.components.splice(
            this.components.findIndex((component) => component.name.indexOf("drugAllergy") != -1),
            1
          );
        }
      });
    },
    /**
     * description: 获取切换新旧版护理记录时间配置
     * return {*}
     */
     async getLimitDate() {
      let params = {
        settingTypeCode: "NewNursingRecord",
        index: Math.random(),
      };
      await GetClinicSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.switchSettings = res.data;
          this.getComponentName();
        }
      });
    },
     /**
     * description: 根据目前的切换配置，决定是否显示其他页签
     * param {*}
     * return {*}
     */
     getComponentName() {
      if(this.components.find(item => item.label === "其他")){
        return;
      }
      const typeValue = this.dataSourceTypeValueMapping["nursingRecord"];
      const settingValue = this.switchSettings.find((m) => m.typeValue == typeValue)?.settingValue;
      if (!settingValue || !this.patientInfo || !this.patientInfo.admissionDate) {
        this.components.push(
        {
          index: 5,
          label: "其他",
          name: "patientNursingRecordDetailSupplement",
        });
      }
      let admissionDate = this._datetimeUtil.formatDate(this.patientInfo.admissionDate, "yyyy-MM-dd hh:mm");
      let limitDate = this._datetimeUtil.formatDate(settingValue, "yyyy-MM-dd hh:mm");
      if (admissionDate <= limitDate) {
        this.components.push(
        {
          index: 5,
          label: "其他",
          name: "patientNursingRecordDetailSupplement",
        });
      } else {
        return;
      }
    },
  },
};
</script>
<style lang="scss">
.tpr-supplement {
  height: 100%;
  .main-tabs {
    position: relative;
    height: calc(100% - 30px);
    .temperature-button {
      position: absolute;
      top: 4px;
      right: 10px;
      z-index: 100;
    }
    .tabs {
      height: 100%;
      .el-tabs__content {
        height: calc(100% - 30px);
        .el-tab-pane {
          height: 100%;
        }
      }
    }
  }
}
</style>
