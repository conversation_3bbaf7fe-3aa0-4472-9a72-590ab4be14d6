<template>
  <div v-loading="loading" :element-loading-text="loadingText.load" class="temperature-list">
    <div class="vital-sign-tabs">
      <el-tabs size="mini" class="tabs" v-model="activeName">
        <el-tab-pane v-for="(item, index) in tabsData" :label="item.startTime + '-' + item.endTime" :key="index">
          <el-image
            v-if="imgBaseDataArr[index]"
            class="img"
            :src="imgBaseDataArr[index]"
            :preview-src-list="showImgBaseDataArr"
            :initial-index="index"
            :z-index="9999"
            @click.native="getImgList(imgBaseDataArr, index)"
          ></el-image>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
  
<script>
export default {
  props: {
    value: {
      type: Object,
      required: true,
    },
    query: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      tabsData: [],
      imgBaseDataArr: [],
      activeName: "0",
      loading: false,
      carouselBoole: false,
      showImgBaseDataArr: [],
    };
  },
  computed: {
    loadingText() {
      return this.$t("loadingText");
    },
  },
  watch: {
    value: {
      handler(newValue) {
        //判断值是否为空
        if (!newValue?.tabs?.length) {
          this.tabsData = [];
          return;
        }
        if (!newValue?.imageList?.length) {
          this.imgBaseDataArr = [];
          return;
        }
        this.tabsData = newValue.tabs;
        this.imgBaseDataArr = newValue.imageList;
        //默认显示最后一张
        this.activeName = (newValue.tabs.length - 1).toString();
        this.getTabsData();
      },
    },
  },
  mounted() {
    this.activeName = (this.tabsData.length - 1).toString();
  },
  methods: {
    //获取tabs数据
    getTabsData() {
      if (this.tabsData.length == 0) {
        return;
      }
      this.tabsData.map((item) => {
        item.startTime = this._datetimeUtil.formatDate(item.startTime, "yyyy/MM/dd");
        item.endTime = this._datetimeUtil.formatDate(item.endTime, "yyyy/MM/dd");
      });
    },
    /**
     * description: 图片点击放大图，重新放置图片List（左右切换功能）
     * param {*} imgList
     * param {*} index
     * return {*}
     */
    getImgList(imgList, index) {
      this.showImgBaseDataArr = [];
      if (imgList.length == 1) {
        this.showImgBaseDataArr.push(imgList[0]);
      } else if (imgList.length == 0) {
        return;
      } else {
        for (let i = 0; i < imgList.length; i++) {
          this.showImgBaseDataArr.push(imgList[i + index]);
          if (i + index >= imgList.length - 1) {
            index = 0 - (i + 1);
          }
        }
      }
    },
  },
};
</script>
<style lang="scss" >
.temperature-list {
  height: calc(100% - 10px);
  .vital-sign-tabs {
    height: 100%;
    background-color: #fff;
    .el-tabs {
      height: 100%;
      background-color: #525659;
      .el-tabs__item {
        font-size: 12px;
      }
      .el-tabs__content {
        height: calc(100% - 45px);
        .img {
          width: 650px;
          height: 100%;
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          margin: auto;
          .el-image-viewer__close {
            top: auto;
            bottom: 33px;
            right: calc(50% - 120px);
            font-size: 28px;
            color: #ffffff;
            z-index: 200;
          }
          .el-icon-c-scale-to-original,
          .el-icon-full-screen {
            margin-left: -40px;
          }
          .el-icon-refresh-left,
          .el-icon-refresh-right {
            display: none;
          }
        }
      }
    }
  }
}
</style>
