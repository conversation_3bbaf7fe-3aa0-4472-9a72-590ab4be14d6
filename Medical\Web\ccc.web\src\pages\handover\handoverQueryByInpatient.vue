<!--
 * FilePath     : \ccc.web\src\pages\handover\handoverQueryByInpatient.vue
 * Author       : 张现忠
 * Date         : 2021-11-05 18:35
 * LastEditors  : 郭鹏超
 * LastEditTime : 2022-09-19 16:51
 * Description  : -护士交班单按照病人查询
 * CodeIterationRecord: 调整交班类别下拉框改为交班类别和交班子类两个下拉框来选择交班的类型进行交班记录的查询
                        2022-07-21 10:04 bug修复：病人接班，选择交接类别后，交接子类会有默认内容（没有清理的旧的选中内容）          
-->

<template>
  <base-layout class="handover-inpatient">
    <div slot="header">
      <span>班别开始日期：</span>
      <el-date-picker
        v-model="queryData.startDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        class="date-picker"
        :picker-options="pickerOptionscreate"
        placeholder="选择日期"
      ></el-date-picker>
      <span class="label">班别结束日期：</span>
      <el-date-picker
        v-model="queryData.endDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        class="date-picker"
        :picker-options="pickerOptionsend"
        placeholder="选择日期"
      ></el-date-picker>
      <span class="label">交接类别：</span>
      <el-select
        v-model="queryData.handoverType"
        @change="getHandOverType(undefined, queryData.handoverType, 'handoverTypeItems')"
        placeholder="请选择"
        class="handover-type"
      >
        <el-option v-for="item in handoverTypes" :key="item.key" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <span class="label">交接子类：</span>
      <el-select v-model="queryData.recordsCode" placeholder="请选择" class="handover-type">
        <el-option
          v-for="item in handoverTypeItems"
          :key="item.key"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <div class="top-btn">
        <el-button class="query-button" icon="iconfont icon-search" @click="getPatientHandover()">查询</el-button>
      </div>
    </div>
    <handover-report
      :handoverReportTitleView="handoverReportTitleView"
      v-loading.fullscreen.lock="loading"
      :handoverReportSbarView="handoverReportSbarView"
      :showSignFlag="showSignFlag"
      :showBodyPartFlag="bodyPartFlag"
    ></handover-report>
  </base-layout>
</template>
<script>
import { GetHandoverReportTitle, GetSingleHandoverReportSBAR } from "@/api/HandoverReport.js";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import baseLayout from "@/components/BaseLayout";
import handoverReport from "./components/handoverReport";
import { GetSelectSetting } from "@/api/Setting";
//引用病人基本信息组件
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    handoverReport,
  },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
    }),
  },
  watch: {
    inpatientID: {
      handler() {
        this.initialization();
      },
    },
  },
  props: { inpatientID: { type: String, default: "", required: true } },
  data() {
    let that = this;
    return {
      loading: false,
      queryData: {
        startDate: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
        endDate: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
        inpatientID: "",
        handoverType: "",
        recordsCode: "",
      },
      showSignFlag: false,
      handoverReportTitleView: {},
      handoverReportSbarView: [],
      //交班类别配置
      handoverTypes: [],
      handoverTypeItems: [],
      bodyPartFlag: true,
      pickerOptionscreate: {
        disabledDate(time) {
          //开始时间的禁用
          return time.getTime() > new Date(that.queryData.endDate).getTime();
        },
      },
      pickerOptionsend: {
        disabledDate(time) {
          //结束时间的禁用
          return time.getTime() < new Date(that.queryData.startDate).getTime() - 8.64e7;
        },
      },
    };
  },
  activated() {
    this.getHandOverType();
  },
  mounted() {
    this.getShowBodyPartFlag();
    this.getHandOverType();
  },
  methods: {
    /**
     * description: 页面初始化
     * param {*}
     * return {*}
     */
    initialization() {
      this.queryData = {
        startDate: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
        endDate: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
        handoverType: "",
      };
      this.upperExhibition = [];
      this.shiftData = [];
      this.nurseTableDatas = [];
      this.patientInformation = [];
    },
    /**
     * description: 查询交班数据
     * param {*}
     * return {*}
     */
    async getPatientHandover() {
      if (!this.queryData.handoverType) {
        this._showTip("warning", "请选择交班类型");
        return;
      }
      if (!this.queryData.recordsCode) {
        this._showTip("warning", "请选择交班子类");
        return;
      }
      this.loading = true;
      await this.getHandoverTitleView();
      await this.getHandoverSBARView();
      this.loading = false;
    },
    /**
     * description: 获取交班报告顶部内容
     * param {*}
     * return {*}
     */
    async getHandoverTitleView() {
      if (!this.patient.stationID) {
        this._showTip("warning", "请选择病区");
        return;
      }
      let params = {
        stationID: this.patient.stationID,
        startDate: this.queryData.startDate,
        endDate: this.queryData.endDate,
        showSignFlag: this.showSignFlag,
      };
      await GetHandoverReportTitle(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.handoverReportTitleView = res.data;
        }
      });
    },
    /**
     * description: 获取交班内容
     * param {*} params
     * return {*}
     */
    async getHandoverSBARView() {
      let params = {
        inpatientID: this.inpatientID,
        stationID: this.patient.stationID,
        startDate: this.queryData.startDate,
        endDate: this.queryData.endDate,
        handoverType: this.queryData.handoverType,
        recordsCode: this.queryData.recordsCode,
      };
      await GetSingleHandoverReportSBAR(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.handoverReportSbarView = res.data;
        }
      });
    },

    /**
     * description: 获取交班类型
     * params {*}
     * return {*}
     * param {*} typeCode
     * param {*} typeValue
     * param {*} key
     */
    async getHandOverType(typeCode = "HandoverSetting", typeValue = "HandoverCategory", key = "handoverTypes") {
      //清空之前缓存的值
      this[key] = [];
      this.queryData.recordsCode = undefined;
      let params = {
        typeCode,
        typeValue,
        addDefaultFlag: true,
      };
      await GetSelectSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this[key] = res.data;
        }
      });
    },
    /**
     * description: 获取是否显示身体部位
     * param {*}
     * return {*}
     */
    getShowBodyPartFlag() {
      let params = {
        settingTypeCode: "HandoverReportBodyPartShowFlag",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.bodyPartFlag = res.data;
        }
      });
    },
  },
};
</script>
<style lang='scss'>
.handover-inpatient {
  .handover-type {
    width: 150px;
  }
  .date-picker {
    width: 120px;
  }
  .top-btn {
    // float: right;
    display: inline-block;
  }
  .base-header {
    height: auto !important;
  }
}
</style>