function BodyPartService() {
  this.getBodyPart = function(tubeID, gender, callback) {
    $Common.ajax.get({
      url:
        $Common.ServiceUrl +
        "/BodyPart/GetTubeBodyPart?tubeID=" +
        $.trim(tubeID) +
        "&gender=" +
        gender,
      callback: callback
    });
  };
  this.getBodyPartList = function(gender, callback) {
    $Common.ajax.get({
      url: $Common.ServiceUrl + "/BodyPart/GetBodyPartList?gender=" + gender,
      callback: callback
    });
  };
  this.getListByRecordsCode = function(recordsCode, gender, callback) {
    $Common.ajax.get({
      url:
        $Common.ServiceUrl +
        "/BodyPart/GetListByRecordsCode?recordsCode=" +
        recordsCode +
        "&gender=" +
        gender,
      callback: callback
    });
  };
}
