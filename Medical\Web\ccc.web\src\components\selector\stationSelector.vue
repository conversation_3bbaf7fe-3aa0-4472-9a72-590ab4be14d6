<!--
 * FilePath     : \src\components\selector\stationSelector.vue
 * Author       : 苏军志
 * Date         : 2019-11-20 20:54
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-13 11:26
 * Description  : 病区下拉选择组件
 * CodeIterationRecord: 2022-09-24 改为从缓存获取病区列表 -杨欣欣
-->
<template>
  <div class="station-selector">
    <span :class="{ label: label }">{{ label }}</span>
    <el-select
      v-model="selected"
      :disabled="inputDisabled"
      :clearable="clearable"
      :placeholder="$t('placeholder.station')"
      :style="style"
      filterable
      @change="changeValue"
    >
      <el-option
        v-for="(station, index) in stationList"
        :key="index"
        :label="station.stationName"
        :value="station.id"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
import { GetStationList } from "@/api/Station";
import { mapGetters } from "vuex";
export default {
  props: {
    value: {
      type: Number,
    },
    label: {
      type: String,
      default: "病区：",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: "160px",
    },
    hospitalFlag: {
      type: Boolean,
      default: false,
    },
    userID: {
      type: String,
      default: undefined,
    },
    inpatientID: {
      type: String,
      default: undefined,
    },
    // 只有一条数据时呈现样式，可支持参数Text || List，默认List
    onlyOneDataStyle: {
      type: String,
      default: "List",
    },
  },
  data() {
    return {
      selected: "",
      stationList: [],
    };
  },
  computed: {
    ...mapGetters({
      localStationList: "getStationList",
      user: "getUser",
    }),
    inputDisabled() {
      return this.disabled || (this.stationList.length == 1 && this.onlyOneDataStyle == "Text");
    },
    style() {
      return {
        width: this._convertUtil.getHeigt(this.width, true),
      };
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal, oldVal) {
        this.selected = newVal;
      },
    },
    selected: {
      immediate: true,
      handler(newVal, oldVal) {
        if (!newVal && this.user) {
          this.selected = this.user.stationID;
          this.changeValue(this.selected);
        }
      },
    },
  },
  async created() {
    await this.getStationList();
    this.$emit("default-select-deptID", this.stationList[0]?.departmentListID);
    this.$emit("default-select-bedID", this.stationList[0]?.bedID);
  },
  methods: {
    /**
     * description: 获取病区列表，若缓存获取不到则发送请求
     * param {*}
     * return {*}
     */
    async getStationList() {
      // 组Key
      let sessionKey = "station";
      if (this.userID) sessionKey += "_" + this.userID;
      if (this.inpatientID) sessionKey += "_" + this.inpatientID;

      // 根据key取到缓存，直接使用
      if (this.localStationList && this.localStationList[sessionKey] && this.localStationList[sessionKey].length > 0) {
        this.stationList = this._common.clone(this.localStationList[sessionKey]);
      } else {
        let params = {
          userID: this.userID,
          inpatientID: this.inpatientID,
          index: Math.random(),
        };
        await GetStationList(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.stationList = this._common.clone(result.data);
            // 存入{ key : result.data }
            this.$store.commit("session/setStationList", {
              [sessionKey]: result.data,
            });
          }
        });
      }
      if (this.hospitalFlag && !this.stationList.find((station) => station.id == 999999)) {
        this.stationList.unshift({ stationName: "全院", id: 999999 });
      }
      let station = this.stationList.find((station) => {
        return station.id == this.selected;
      });
      if (station) {
        this.$emit("default-select-deptID", station.departmentListID);
        this.$emit("default-select-bedID", station.bedID);
      }
    },
    changeValue(stationID) {
      if (!stationID) {
        this.$emit("input", undefined);
        this.$emit("default-select-deptID", undefined);
        this.$emit("default-select-bedID", undefined);
        return;
      }
      // 实现双向绑定
      this.$emit("input", stationID);
      this.$emit("select", stationID);
      let station = this.stationList.find((station) => {
        return station.id == stationID;
      });
      if (station) {
        this.$emit("select-item", station);
        this.$emit("change", station);
        this.$emit("default-select-deptID", station.departmentListID);
        this.$emit("default-select-bedID", station.bedID);
      }
    },
  },
};
</script>
<style lang="scss">
.station-selector {
  display: inline-block;
  .label {
    margin-left: 5px;
  }
  .el-select {
    .el-input.is-disabled {
      .el-input__suffix {
        display: none;
      }
      .el-input__inner {
        background-color: #ffffff;
      }
      .el-input__inner {
        color: #606266;
      }
    }
    .el-input__inner {
      padding-left: 5px;
    }
  }
}
</style>
