<!--
 * FilePath     : \src\pages\transferPages\nursingBoardBulletinMaintain.vue
 * Author       : 苏军志
 * Date         : 2022-04-29 16:30
 * LastEditors  : 苏军志
 * LastEditTime : 2022-05-02 15:03
 * Description  :  串到护理看板病区公告内容维护
 * CodeIterationRecord: 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getNursingBoard } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      language: "getLanguage",
      hospitalInfo: "getHospitalInfo",
      token: "getToken",
      user: "getUser",
    }),
  },
  created() {
    this.url =
      getNursingBoard() +
      "customBulletinMaintain?hospitalID=" +
      this.hospitalInfo.hospitalID +
      "&language=" +
      this.language +
      "&token=" +
      this.token +
      "&stationID=" +
      this.user.stationID +
      "&userID=" +
      this.user.userID;
  },
};
</script>