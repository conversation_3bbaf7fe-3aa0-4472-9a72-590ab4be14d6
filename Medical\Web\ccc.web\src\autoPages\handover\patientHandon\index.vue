<!--
 * FilePath     : \ccc.web\src\autoPages\handover\patientHandon\index.vue
 * Author       : 郭鹏超
 * Date         : 2020-05-19 09:20
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-06-02 09:28
 * Description  : 
--> 
<template>
  <div class="common-handon">
    <el-tabs class="handon-tab" v-model="activeName" v-if="model == 'page'">
      <el-tab-pane label="交班记录" name="patientHandon"></el-tab-pane>
      <el-tab-pane label="交班报告" name="inpatientHandoverReport"></el-tab-pane>
    </el-tabs>
    <div v-if="inpatient" :class="['handover-wrap', { component: model != 'page' }]">
      <component
        v-if="inpatient"
        :is="activeName"
        :inpatientID="inpatient.inpatientID"
        :dischargedPreview="model == 'component'"
      ></component>
    </div>
  </div>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import patientHandon from "@/autoPages/handover/patientHandon/patientHandon.vue";
import inpatientHandoverReport from "@/autoPages/handover/handoverReport/inpatientHandoverReport";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    patientHandon,
    inpatientHandoverReport,
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
    }),
  },
  data() {
    return {
      activeName: "patientHandon",
      model: "page",
    };
  },
  created() {
    if (this.$route.query.model) {
      this.model = this.$route.query.model;
    }
    if (this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName;
    }
  },
  activated() {
    // 设置可切换病人
    this._sendBroadcast("setPatientSwitch", true);
  },
};
</script>

<style lang="scss">
.common-handon {
  height: 100%;
  .handon-tab {
    height: 40px;
  }
  .handover-wrap {
    background-color: #fff;
    height: calc(100% - 45px);
    &.component {
      height: 100%;
    }
  }
}
</style>