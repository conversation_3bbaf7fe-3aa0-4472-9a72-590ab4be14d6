<!--
 * FilePath     : \src\pages\batchTubeDrainage\multyPatientRecordDrainage.vue
 * Author       : 胡长攀
 * Date         : 2024-04-20 11:43
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-04-25 09:07
 * Description  : 多患者批量录入引流液
 * CodeIterationRecord:
 -->
<template>
  <batch-input-drainage :showAllFlag="true"></batch-input-drainage>
</template>
<script>
import batchInputDrainage from "./components/batchInputDrainage";
export default {
  components: {
    batchInputDrainage,
  },
};
</script>
<style lang="scss">
</style>