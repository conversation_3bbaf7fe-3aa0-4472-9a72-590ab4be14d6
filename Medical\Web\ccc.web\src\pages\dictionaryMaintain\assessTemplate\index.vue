<!--
 * FilePath     : \ccc.web\src\pages\dictionaryMaintain\assessTemplate\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-06-18 16:38
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-12-29 20:05
 * Description  : 测试评估模板数据页面(丛博使用)
-->
<template>
  <base-layout class="question-main" headerHeight="auto">
    <div slot="header">
      <label>医院:</label>
      <el-select style="width: 100px" v-model="hospitalListID">
        <el-option v-for="(item, index) in localhospitalList" :key="index" :label="item.hospitalName" :value="item.hospitalID"></el-option>
      </el-select>
      <label>病区科室:</label>
      <station-selector v-model="stationID" label="" width="160"></station-selector>
      <dept-selector label="" width="140" v-model="departmentListID" :stationID="stationID"></dept-selector>
      <label>性别:</label>
      <el-select style="width: 80px" v-model="gender" placeholder="性别">
        <el-option
          v-for="(applyDomain, index) in genderArr"
          :key="index"
          :label="applyDomain.label"
          :value="applyDomain.value"
        ></el-option>
      </el-select>
      <label>年龄:</label>
      <el-input v-model="age" placeholder="年龄" style="width: 80px"></el-input>
      <label>RecordsCode:</label>
      <el-input
        v-model="recordsCode"
        placeholder="请输入recordsCode"
        style="width: 170px"
        @keyup.enter.native="search()"
      ></el-input>
      <el-button @click="search()" class="query-button" icon="iconfont icon-search">查询</el-button>
      <el-button @click="updateCacheByKey()" style="float: right; margin: 9px" class="query-button">更新缓存</el-button>
      <el-input v-model="keyCode" placeholder="" style="width: 170px; float: right"></el-input>
      <label style="float: right">表名称或RecordsCode：</label>
    </div>
    <div v-loading="layoutLoading" style="height: 100%">
      <tabs-layout :template-list="templateDatas" />
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import { GetTestAssessView } from "@/api/Assess.js";
import { removeCacheByName } from "@/api/Cache.js";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    stationSelector,
    deptSelector,
    tabsLayout,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      hospitalInfo: "getHospitalInfo",
      localhospitalList: "getHospitalList",
    }),
  },
  data() {
    return {
      layoutLoading: false,
      templateDatas: [],
      recordsCode: undefined,
      age: undefined,
      gender: undefined,
      departmentListID: undefined,
      stationID: undefined,
      genderArr: [
        {
          label: "男",
          value: "1",
        },
        {
          label: "女",
          value: "2",
        },
      ],
      hospitalListID: undefined,
      keyCode: undefined,
    };
  },
  mounted() {
    this.hospitalListID = this.hospitalInfo.hospitalID;
  },
  methods: {
    /**
     * @description: 评估模板查询
     * @return 
     */    
    search() {
      let params = {
        recordsCode: this.recordsCode,
        age: this.age,
        gender: this.gender,
        departmentListID: this.departmentListID,
        dateOfBirth: this.dateOfBirth,
        hospitalID: this.hospitalListID,
        stationID: this.stationID ? this.stationID : 0,
      };
      this.layoutLoading = true;
      GetTestAssessView(params).then((result) => {
        this.layoutLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * @description: 更新缓存
     * @return 
     */    
    updateCacheByKey() {
      if (!this.keyCode) {
        this._showTip("warning", "请填写需要更新缓存的表名称或者RecordsCode");
        return;
      }
      let params = {
        cacheName: this.keyCode,
        hospitalID:this.hospitalListID
      };
      removeCacheByName(params).then((res) => {
        if (!res.message) {
          this._showTip("success", this.keyCode + "表更新缓存成功");
        } else {
          this._showTip("error", this.keyCode + "表更新缓存失败");
        }
      });
    },
  },
};
</script>
<style>
</style>