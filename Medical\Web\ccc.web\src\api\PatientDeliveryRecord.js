/*
 * FilePath     : \src\api\PatientDeliveryRecord.js
 * Author       : 杨欣欣
 * Date         : 2023-03-12 16:24
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-04-06 11:34
 * Description  : 产程专项API
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
const baseUrl = "/patientDeliveryRecord";

const urls = {
  GetPatientDeliveryRecordRecordViews:
    baseUrl + "/GetPatientDeliveryRecordRecordViews",
  GetPatientDeliveryRecordCareMainViews:
    baseUrl + "/GetPatientDeliveryRecordCareMainViews",
  GetPatientDeliveryRecordCareMainView:
    baseUrl + "/GetPatientDeliveryRecordCareMainView",
  GetPatientDeliveryRecordNewbornViews:
    baseUrl + "/GetPatientDeliveryRecordNewbornViews",
  CheckCareMainExistence: baseUrl + "/CheckCareMainExistence",
  GetDeliveryAssessViewAsync: baseUrl + "/GetDeliveryAssessViewAsync",
  AddPatientDeliveryRecordRecord: baseUrl + "/AddPatientDeliveryRecordRecord",
  SavePatientDeliveryRecordCareMain:
    baseUrl + "/SavePatientDeliveryRecordCareMain",
  DeletePatientDeliveryRecordRecord:
    baseUrl + "/DeletePatientDeliveryRecordRecord",
  DeletePatientDeliveryRecordCareMain:
    baseUrl + "/DeletePatientDeliveryRecordCareMain",
  SyncTableData: baseUrl + "/SyncTableData"
};
// 获取主记录
export const GetPatientDeliveryRecordRecordViews = params => {
  return http.get(urls.GetPatientDeliveryRecordRecordViews, params);
};
// 获取维护记录列表（表格+模板使用）
export const GetPatientDeliveryRecordCareMainViews = params => {
  return http.get(urls.GetPatientDeliveryRecordCareMainViews, params);
};
// 获取单条维护记录（纯模板类型维护记录使用）
export const GetPatientDeliveryRecordCareMainView = params => {
  return http.get(urls.GetPatientDeliveryRecordCareMainView, params);
};
// 获取新生儿评估的部分内容，作为新生儿记录切换选项
export const GetPatientDeliveryRecordNewbornViews = params => {
  return http.get(urls.GetPatientDeliveryRecordNewbornViews, params);
};
// 获取维护记录是否存在
export const CheckCareMainExistence = params => {
  return http.get(urls.CheckCareMainExistence, params);
};
// 获取评估模板
export const GetDeliveryAssessViewAsync = params => {
  return http.get(urls.GetDeliveryAssessViewAsync, params);
};
// 新增主记录
export const AddPatientDeliveryRecordRecord = params => {
  return http.post(urls.AddPatientDeliveryRecordRecord, params);
};
// 保存维护记录
export const SavePatientDeliveryRecordCareMain = params => {
  return http.post(urls.SavePatientDeliveryRecordCareMain, params);
};
// 删除主记录
export const DeletePatientDeliveryRecordRecord = params => {
  return http.get(urls.DeletePatientDeliveryRecordRecord, params);
};
// 删除维护记录
export const DeletePatientDeliveryRecordCareMain = params => {
  return http.get(urls.DeletePatientDeliveryRecordCareMain, params);
};
// 同步表格数据
export const SyncTableData = params => {
  return http.get(urls.SyncTableData, params);
};
