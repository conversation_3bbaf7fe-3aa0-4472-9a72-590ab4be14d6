/*
 * FilePath     : \src\pages\glucose\components\chartOption.js
 * Author       : 来江禹
 * Date         : 2023-06-29 09:06
 * LastEditors  : 来江禹
 * LastEditTime : 2025-03-19 14:06
 * Description  :
 * CodeIterationRecord:
 */

export const setGlucoseChartOption = (chart, _this) => {
  let symbolSize = _this.convertPX(8);
  let fontSize = _this.convertPX(15);
  let yTitleFontSize = _this.convertPX(12);
  let yAxisLine = {
    show: true,
    lineStyle: {
      width: _this.convertPX(1)
    }
  };
  let yAxisTick = {
    show: true,
    lineStyle: {
      width: _this.convertPX(1)
    }
  };
  let yRightAxisLabel = {
    show: true,
    fontWeight: "bold",
    fontSize: _this.convertPX(13),
    margin: _this.convertPX(8),
    padding: [0, 5, 0, 0]
  };
  let seriesLabel = {
    show: false,
    position: "top",
    fontWeight: "bold",
    fontSize: fontSize
  };
  let seriesOptions = {
    type: "line",
    connectNulls: true,
    smooth: false,
    showAllSymbol: true,
    label: seriesLabel,
    symbolSize: symbolSize
  };
  chart.setOption({
    xAxis: {
      axisLabel: {
        textStyle: {
          fontWeight: "bold",
          fontSize: _this.convertPX(14)
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: "#000000",
          width: _this.convertPX(1)
        }
      }
    },
    yAxis: [
      {
        position: "left",
        type: "value",
        name: "mmol/l",
        min: 0,
        max: 40,
        interval: 4,
        nameTextStyle: {
          padding: [0, 0, _this.convertPX(5), _this.convertPX(-25)],
          fontWeight: "bold",
          fontSize: yTitleFontSize
        },
        axisLine: yAxisLine,
        axisTick: yAxisTick,
        axisLabel: yRightAxisLabel
      },
      {
        position: "right",
        type: "value",
        name: _this.chartShowInsulinFlag ? "单位" : "",
        min: 0, //Y轴最小值
        max: 50,
        interval: 5,
        nameTextStyle: {
          padding: [0, _this.convertPX(-30), _this.convertPX(5), _this.convertPX(-35)],
          fontWeight: "bold",
          fontSize: yTitleFontSize,
        },
        //坐标轴线
        axisLine: {
          show: _this.chartShowInsulinFlag,
          lineStyle: {
            width: _this.convertPX(1)
          }
        },
        axisTick: {
          show: _this.chartShowInsulinFlag,
          lineStyle: {
            width: _this.convertPX(1)
          }
        },
        axisLabel: {
          show: _this.chartShowInsulinFlag,
          fontWeight: "bold",
          fontSize: _this.convertPX(13),
          margin: _this.convertPX(8),
          padding: [0, 0, 0, 4]
        }
      },
    ],
    series: [
      { yAxisIndex: 1, ...seriesOptions },
      { yAxisIndex: 1, ...seriesOptions },
      { yAxisIndex: 0, ...seriesOptions },
    ]
  });
};
export const grid = {
  top: 60,
  height: "auto",
  bottom: 5,
  right: 10,
  left: 10
};

export const tooltip = {
  trigger: "axis",
  // 将 tooltip 框限制在图表的区域内
  confine: true,
  axisPointer: {
    animation: false
  },
  formatter: function (params) {
    var result = "";
    for (var i = 0; i < params.length; i++) {
      // 判断当前 series 中数据是否为空
      if (!params[i].data[1]) {
        continue;
      } // 自定义 tooltip 内容
      result += params[i].marker + params[i].seriesName + ": " + params[i].value[1] + "<br>";
    }
    return result;
  },
};
export const extend = {
  series: {
    type: "line",
    connectNulls: true,
    smooth: false
  }
};
export const chartSettings = {
  labelMap: {
    dateTime: "日期",
    glucose: "血糖(mmol/l)",
    glucoseInUrine: "血酮(mmol/l)",
    insulin: "胰岛素(单位)",
  },
};
export const chartExtend = {
  //使用series时type需要配置否则会报错
  series: {
    type: "line",
    connectNulls: true,
    smooth: false,
  },
};
