<template>
  <base-layout class="file-path">
    <div slot="header">
      <span slot="label">文件名：</span>
      <el-input
        v-model="fileNameLike"
        style="width: 250px"
        @keyup.enter.native="selectLike"
        placeholder="输入你要查询的文件名称"
        class="search-input"
        @clear="getData"
      >
                 
        <i slot="append" class="iconfont icon-search" @click="selectLike"></i>
             
      </el-input>
      <div class="top-btn">
        <el-button type="success" @click="insertEvent(1)" icon="iconfont icon-add ">新增</el-button>
      </div>
    </div>
    <div slot-scope="data">
      <ux-grid
        v-loading="loading"
        :element-loading-text="loadingText"
        :height="data.height"
        border
        stripe
        keep-source
        ref="filePathTable"
        use-virtual
        highlightCurrentRow
        :edit-config="{ trigger: 'click', mode: 'cell' }"
      >
        <!-- <ux-table-column field="system" title="系统类别" edit-render header-align="center">
          <template v-slot:edit="scope">
            <el-input v-model="scope.row.system"></el-input>
          </template>
        </ux-table-column> -->
        <!-- 列对应属性 -->
        <ux-table-column field="stationID" min-width="120" title="病区" edit-render header-align="center">
          <template slot="edit" slot-scope="scope">
            <el-select v-model="scope.row.stationID" @change="$refs.filePathTable.updateStatus(scope)">
              <el-option v-for="item in stationList" :key="item.id" :value="item.id" :label="item.stationName" />
            </el-select>
          </template>
          <template slot-scope="scope">
            {{ getLabels(scope.row.stationID, stationList, "id", "stationName", true) }}
          </template>
        </ux-table-column>
        <!-- 列对应属性 -->
        <ux-table-column field="interventionID" title="措施" min-width="200" edit-render header-align="center">
          <template slot="edit" slot-scope="scope">
            <el-select v-model="scope.row.interventionID" filterable @change="$refs.filePathTable.updateStatus(scope)">
              <el-option v-for="item in teachingList" :key="item.id" :value="item.id" :label="item.intervention" />
            </el-select>
          </template>
          <template slot-scope="scope">
            {{ getLabels(scope.row.interventionID, teachingList, "id", "intervention", true) }}
          </template>
        </ux-table-column>
        <!-- 列对应属性 -->
        <ux-table-column field="fileName" title="文件名称" min-width="160" edit-render header-align="center">
          <template slot="edit" slot-scope="scope">
            <el-input v-model="scope.row.fileName"></el-input>
          </template>
        </ux-table-column>
        <!-- 列对应属性 -->
        <ux-table-column
          field="fileType"
          title="文件类型"
          width="85"
          header-align="center"
          align="center"
        ></ux-table-column>
        <!-- 列对应属性 -->
        <ux-table-column field="modifyDate" title="维护时间" width="120" header-align="center" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.modifyDate, type: 'datetime' }"></span>
          </template>
        </ux-table-column>
        <!-- 列对应属性 -->
        <ux-table-column field="modifyPersonID" width="100" title="维护人员" header-align="center"></ux-table-column>
        <ux-table-column title="操作" width="130" fixed="right" align="center" header-align="center">
          <template slot-scope="scope">
            <el-tooltip content="保存">
              <i class="iconfont icon-edit" @click="saveEvent(scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="handleDelete(scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="上传">
              <el-upload
                ref="upload"
                class="upload-demo"
                multiple
                :auto-upload="false"
                :file-list="fileList"
                accept=".pdf,.mp3,.mp4"
                :on-change="getFile"
                :action="uploadUrl()"
                :show-file-list="false"
              >
                <i class="iconfont icon-upload" @click="clinicRow = scope.row"></i>
              </el-upload>
            </el-tooltip>
            <el-tooltip content="预览">
              <i class="iconfont icon-preview" @click="showDialog(scope.row)"></i>
            </el-tooltip>
          </template>
        </ux-table-column>
      </ux-grid>
      <el-dialog title="资料预览" v-dialogDrag :close-on-click-modal="false" :visible.sync="dialogVisible" fullscreen>
        <file-preview :datas="teachingData" :v-if="showTeaching"></file-preview>
      </el-dialog>
    </div>
  </base-layout>
</template>
<script>
import {
  GetFileListBySystem,
  DeleteFilePath,
  SaveFilePath,
  GetTeachingInterventionList,
} from "@/api/InterventionFileUpload";
import baseLayout from "@/components/BaseLayout.vue";
import { GetStationList } from "@/api/Station";
import FilePreview from "@/components/FilePreview";
import { mapGetters } from "vuex";
export default {
  components: { baseLayout, FilePreview },
  name: "upload",
  data() {
    return {
      loading: false,
      loadingText: "加载中……",
      //主页面表格数据
      tableData: [],
      filePathID: undefined,
      filePath: "",
      fileNameLike: "",
      //文件集合
      fileList: [],
      cloneTableData: [],
      clinicRow: {},
      stationList: [],
      isAdd: false,
      showTeaching: false,
      teachingData: [],
      teachingList: [],
      dialogVisible: false,
      system: "",
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  async created() {
    await this.init();
  },
  methods: {
    async init() {
      this.system = this.$route.query.system;
      await GetStationList().then((result) => {
        if (this._common.isSuccess(result)) {
          this.stationList = result.data;
          if (this.stationList) {
            this.stationList.unshift({ stationName: "全院", id: 999999 });
          }
        }
      });
      await GetTeachingInterventionList().then((result) => {
        if (this._common.isSuccess(result)) {
          this.teachingList = result.data;
        }
      });
      this.getData();
    },
    //浏览
    showTeachingData(row) {
      this.teachingData = [];
      let previewPath = row.serverPath + "/" + row.filePath;
      this.teachingData.push(previewPath);

      this.showTeaching = true;
    },
    //上传的路径
    uploadUrl: () => {
      return "";
    },
    //获取所有宣教数据
    async getData() {
      this.loading = true;
      this.loadingText = "加载中……";
      let params = {
        system: "Teaching",
      };
      await GetFileListBySystem(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.tableData = res.data;
          this.cloneTableData = this._common.clone(this.tableData);
          this.$refs.filePathTable.reloadData(this.tableData);
        }
      });
    },
    //根据ID删除行
    handleDelete(row) {
      this._deleteConfirm("", (flag) => {
        let _this = this;
        if (flag) {
          let params = {
            FilePathID: row.filePathID,
          };
          this.loading = true;
          this.loadingText = "删除中……";
          DeleteFilePath(params).then((response) => {
            this.loading = false;
            if (this._common.isSuccess(response)) {
              this._showTip("success", "删除成功！");
              this.isAdd = false;
              this.getData();
            }
          });
        }
      });
    },
    //点击上传文件确定后，获取文件信息
    getFile(file, fileList) {
      //限制一条数据绑定一个文件
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]];
      }
      //取到文件名开始到最后一个点的长度
      let fileNameLength = file.name.lastIndexOf(".");
      //截取文件名和文件后缀
      let name = file.name.substring(0, fileNameLength);
      let extName = file.name.substring(fileNameLength + 1, file.name.length);
      //文件类型限制列表
      let list = ["pdf", "mp3", "mp4"];
      //将文件后缀与list中给出的后缀进行匹配
      var isTrue = list.find((item) => {
        return item == extName;
      });
      if (isTrue.length == 0) {
        this._showTip("warning", "文件格式不正确");
        this.$refs.upload.clearFiles();
        return false;
      }
      //类型没有问题，再进行回显
      this.clinicRow.fileName = name;
      this.clinicRow.fileType = extName;
      this.clinicRow.serverPath = "";
      //文件转换base64成功后，拿到base64串，存入newData
      this.getBase64(file.raw).then((resBase64) => {
        this.filePath = resBase64.split(",")[1];
      });
    },
    //文件转换成流
    getBase64(file) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader();
        let fileResult = "";
        //开始传
        reader.readAsDataURL(file);
        reader.onload = function () {
          fileResult = reader.result;
        };
        (reader.onerror = function (error) {
          reject(error);
        }),
          (reader.onloadend = function () {
            resolve(fileResult);
          });
      });
    },
    //保存
    async saveEvent(row, scope) {
      //获取字段进行非空判断
      //判断system属性不能为空
      // if (!row.system) {
      //   return this._showTip("warning", "请填写类别");
      // }
      if (this.system == "Teaching" && !row.interventionID) {
        return this._showTip("warning", "请选择措施！");
      }
      // if (row.system == this.$route.query.system && !row.interventionID) {
      //   return this._showTip("warning", "请填写措施");
      // }
      //判断fileName属性不能为空
      if (!row.fileName) {
        return this._showTip("warning", "请填写文件名！");
      }
      //判断fileType属性不能为空
      if (!row.fileType) {
        return this._showTip("warning", "请上传文件！");
      }
      //点击保存按钮的当前行未发生改变且当前行的ID不存在，则无需保存
      if (row.filePathID != undefined && !this.$refs.filePathTable.isUpdateByRow(row)) {
        this._showTip("warning", "当前行未发生变化，无需保存");
        this.isAdd = false;
        this.getData();
        return false;
      }
      //将当前行文本内容存入newData
      let params = this._common.clone(row);
      params.filePath = this.filePath ? this.filePath : "";
      this.loading = true;
      this.loadingText = "保存中……";
      await SaveFilePath(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.isAdd = false;
          this._showTip("success", "保存成功");
          this.filePath = "";
          this.getData();
        }
      });
    },
    //模糊查询
    async selectLike() {
      if (this.fileNameLike) {
        this.tableData = this.cloneTableData.filter((element) => {
          let str = element.fileName;
          if (str.indexOf(this.fileNameLike) != -1) {
            return element.fileName;
          }
        });
      } else {
        this.tableData = this.cloneTableData;
      }
      this.$refs.filePathTable.reloadData(this.tableData);
    },
    //表格新增一行
    async insertEvent(row) {
      if (this.isAdd) {
        this._showTip("error", "请保存当前行后再进行新增！");
        return false;
      }
      let { row: newRow } = await this.$refs.filePathTable.insertRow(row);
      //设置默认病区为全院
      newRow.stationID = this.user.stationID;
      newRow.modifyPersonID = this.user.userName;
      newRow.modifyDate = this._datetimeUtil.getNow("yyyy-MM-dd hh:mm");
      newRow.system = this.system;
      await this.$refs.filePathTable.setActiveRow(newRow);
      this.isAdd = true;
    },
    //根据病区序号自动选择下拉框选项
    getLabels(value, items, key, name, isNumber) {
      if (!value) {
        return "";
      }
      let values = [];
      if (typeof value == "object") {
        values = value;
      } else {
        values = this.stringToArray(value, isNumber);
      }
      let label = "";
      values.forEach((val) => {
        let item = items.find((item) => {
          return item[key] == val;
        });

        if (item) {
          if (label) {
            label += "," + item[name];
          } else {
            label = item[name];
          }
        }
      });
      return label;
    },
    stringToArray(str, isNumber) {
      let array = [];
      if (typeof str == "string" && str.indexOf(",") != -1) {
        let strs = str.split(",");
        if (!isNumber) {
          array = strs;
        } else {
          strs.forEach((id) => {
            array.push(Number(id));
          });
        }
      } else {
        if (isNumber) {
          array = [Number(str)];
        } else {
          array = [str];
        }
      }
      return array;
    },
    showDialog(row) {
      this.showTeachingData(row);
      this.dialogVisible = true;
    },
  },
};
</script>
<style lang="scss">
.file-path {
  .top-btn {
    float: right;
  }
  .search-input {
    width: 70%;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .upload-demo {
    display: inline-block;
    vertical-align: top;
  }
}
</style>