/*
 * FilePath     : \ccc.web\src\utils\common.js
 * Author       : 马超
 * Date         : 2024-08-05 17:16
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-29 19:42
 * Description  :
 * CodeIterationRecord:
 */
import { GetEditAuthority } from "@/api/CheckAuthority";
import { SaveWebLog } from "@/api/Log";
import { GetOneSettingByTypeAndCode } from "@/api/Setting";
import http from "@/utils/ajax";
import pinyin from "@/utils/pinyin";
import showTip from "@/utils/toast";
import { GetIsHeadNurse } from "@/api/User";
/**
 * 克隆对象
 * obj：被克隆对象
 */
let clone = (obj) => {
  if (null == obj || "object" != typeof obj) {
    return obj;
  }
  if (obj instanceof Date) {
    var copy = new Date();
    copy.setTime(obj.getTime());
    return copy;
  }
  if (obj instanceof Array) {
    var copy = [];
    for (var i = 0; i < obj.length; i++) {
      copy[i] = clone(obj[i]);
    }
    return copy;
  }
  if (obj instanceof Object) {
    var copy = {};
    for (var attr in obj) {
      if (obj.hasOwnProperty(attr)) {
        copy[attr] = clone(obj[attr]);
      }
    }
    return copy;
  }
  return {};
};
/**
 * 生成GUID
 * showLine：是否显示线条，默认不显示
 */
let guid = (showLine) => {
  var guid = "";
  for (var i = 1; i <= 32; i++) {
    var n = Math.floor(Math.random() * 16.0).toString(16);
    guid += n;
    if (showLine) {
      if (i == 8 || i == 12 || i == 16 || i == 20) {
        guid += "-";
      }
    }
  }
  return guid;
};
// 判断浏览器类型，PC返回true，移动端返回false
let isPC = () => {
  const ua = navigator.userAgent.toLowerCase();
  const isMobile =
    /mobile|android|iphone|ipad|windows phone|iemobile|webos|blackberry|opera mini/i.test(
      ua
    );
  // 补充检测：平板可能被误判为电脑端（如 iPad 的某些 User Agent）
  const isTablet = /(ipad|tablet|playbook|silk)|(android(?!.*mobile))/i.test(
    ua
  );
  // 若检测到移动端或平板，则返回 false（非电脑）
  if (isMobile || isTablet) return false;
  // 进一步检测屏幕尺寸和触控支持
  const hasTouch = "ontouchstart" in window || navigator.maxTouchPoints > 0;
  const isSmallScreen = window.innerWidth < 1024; // 根据需求调整阈值
  // 如果屏幕较小且有触控支持，可能是移动设备
  return !(hasTouch && isSmallScreen);
};

// 存储/读取localStorage数据
let storage = (name, value) => {
  if (!localStorage) return;
  if (value != undefined) {
    localStorage.setItem(name, JSON.stringify(value));
  } else {
    let val = localStorage.getItem(name);
    if (val) {
      return JSON.parse(val);
    }
    return "";
  }
};

// 存储/读取sessionStorage数据
let session = (name, value) => {
  if (!sessionStorage) return;
  if (value != undefined) {
    sessionStorage.setItem(name, JSON.stringify(value));
  } else {
    let val = sessionStorage.getItem(name);
    if (val) {
      return JSON.parse(val);
    }
    return "";
  }
};
//清除session 支持清除单个及多个 数据类型:Array String Number
const removeSession = (keyData) => {
  if (!keyData) {
    return;
  }
  if (Array.isArray(keyData)) {
    //去重
    let keyArr = [...new Set(keyData)];
    if (keyArr.length == 0) {
      return;
    }
    keyArr.forEach((key) => {
      sessionStorage.removeItem(key);
    });
  }
  if (typeof keyData === "string" || typeof keyData === "number") {
    sessionStorage.removeItem(keyData);
  }
};

// 判断API返回是否成功
let isSuccess = (result) => {
  if (!result) {
    return false;
  }
  if (result.code == 1) {
    return true;
  } else {
    if (result.message) {
      showTip.showTip("warning", result.message);
    }
    return false;
  }
};
let getHeigt = (val) => {
  // 如果是auto则直接返回
  if (val == "auto") {
    return "auto";
  }
  // 如果是以px结尾则直接返回
  if (val.indexOf("px", val.length - 2) != -1) {
    return val;
  }
  if (val.indexOf("%") != -1) {
    return val;
  }
  // 如果不是数字，则返回auto
  if (isNaN(val)) {
    return "auto";
  } else {
    return val + "px";
  }
};
// 颜色转换
let colorRGB2Hex = (color) => {
  var rgb = color.split(",");
  var r = parseInt(rgb[0].split("(")[1]);
  var g = parseInt(rgb[1]);
  var b = parseInt(rgb[2].split(")")[0]);

  var hex = "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  return hex.toUpperCase();
};
/**
 * description: 检核TN类项目数据的有效性
 * param {*} item TN类项目，必须包含属性：
 *           controlerType：项目类型
 *           itemName：项目名称
 *           assessValue：项目数值
 *           decimalLen：可保留小数位数
 *           upValue：上限数值
 *           lowValue：下限数值
 *           textEnable：是否可以录入文本，true可以录入，false不可以录入
 *           checkLevelDict：检核级别：1、强检核 2、弱检核，不传默认为1
 * return {*} 返回对象{flag: false, value: ""}
 *           flag：检核结果，通过true，不通过false
 *           value：矫正后的值，例：传入36. 矫正后返回36.0
 */
let checkAssessTN = (item) => {
  let result = {
    flag: false,
    value: "",
  };
  let value = item.assessValue + "";
  // 非TN类项目，空值、NA值 - 直接返回
  if (item.controlerType.trim() != "TN" || !value || value == "NA") {
    result.value = value;
    result.flag = true;
    return result;
  }
  // 允许输入文本(输入的是文本时，不进行检核)
  if (item.textInput && isNaN(value)) {
    result.value = value;
    result.flag = true;
    return result;
  }
  // 获取提示显示名称
  let name = getTipName(item);
  // 判断是否是数值
  if (value.trim().length == 0 || isNaN(value)) {
    showTip.showTip("error", name + "请输入数值");
    result.flag = false;
    return result;
  }
  let decimalLenTip = "",
    upLowTip = "";
  let decimalPassFlag = true,
    upLowPassFlag = true;
  // 检核小数点位数
  [decimalLenTip, decimalPassFlag] = checkDecimalLen(item, value);
  // 检核失败，弹窗提示
  if (!decimalPassFlag) {
    showTip.showTip("error", decimalLenTip);
    return result;
  }
  // 小数位数检核成功后，检核上下限
  [upLowTip, upLowPassFlag] = checkUpAndLowValue(item, value);
  // 检核失败，弹窗提示
  if (!upLowPassFlag) {
    showTip.showTip("error", upLowTip);
    return result;
  }
  // 检核项存在提示信息，(CheckLevel-弱检核项)
  if (decimalLenTip || upLowTip) {
    showTip.showTip("error", decimalLenTip || upLowTip);
  }
  // 检核通过
  result.flag = true;
  result.value = value;
  return result;
};
/**
 * @description: 检核小数点位数
 * @param {Object} item 项目内容
 * @param {String} value TN输入的值
 * @returns {[string, boolean]} 1.提示信息；2.检核结果
 */
let checkDecimalLen = (item, value) => {
  let tip = "";
  let flag = true;
  let pos = value.indexOf(".");
  if (pos <= 0) {
    return [tip, flag];
  }
  let name = getTipName(item);
  if (pos == value.length - 1) {
    value += "0";
  }
  if (!item.decimal) {
    tip = name + "不能录入小数";
    return [tip, flag];
  }
  // 检核小数点位数
  var len = value.length - pos - 1;
  if (len > item.decimal) {
    tip = name + "只能录入" + item.decimal + "位小数";
    flag = getResultByCheckLevel(item, "Decimal");
    return [tip, flag];
  }
  return [tip, flag];
};
/**
 * @description: 检核上下限
 * @param {Object} item 项目内容
 * @param {String} value TN输入的值
 * @returns {[string, boolean]} 1.提示信息；2.检核结果
 */
let checkUpAndLowValue = (item, value) => {
  let tip = "";
  let flag = true;
  // 检核上下限
  let val = parseFloat(value);
  let name = getTipName(item);
  if ((item.upError || item.upError == 0) && item.upError < val) {
    tip = name + "的值不能大于" + item.upError;
    flag = getResultByCheckLevel(item, "UpError");
    return [tip, flag];
  }
  if ((item.lowError || item.lowError == 0) && item.lowError > val) {
    tip = name + "的值不能小于" + item.lowError;
    flag = getResultByCheckLevel(item, "LowError");
    return [tip, flag];
  }
  return [tip, flag];
};
/**
 * @description: 根据检核等级获取检核结果
 * @param {Object} item 项目内容
 * @param {String} propName 检核的属性名称
 * @returns {Object} 检核结果
 */
let getResultByCheckLevel = (item, propName) => {
  return item.checkLevelDict && item.checkLevelDict[propName] == 2
    ? true
    : false;
};
/**
 * @description: 获取显示的提示项目名称
 * @param {Object} item 项目内容
 * @returns {string} 提示名称
 */
let getTipName = (item) => {
  let name = item.itemName;
  // 处理换行
  if (name.indexOf("<br/>") != -1) {
    name = name.split("<br/>")[0];
  }
  // 处理英文括号
  if (name.indexOf("(") != -1) {
    name = name.split("(")[0];
  }
  // 处理中文括号
  if (name.indexOf("（") != -1) {
    name = name.split("（")[0];
  }
  return "【" + name + "】";
};
//获取汉字全拼
let completeSpelling = (value) => {
  return pinyin.pinyin.getFullChars(value);
};
//获取汉字首字母
let initial = (value) => {
  return pinyin.pinyin.getCamelChars(value);
};
//动态加载js
//params ：url => 加载的数据URL
//params :callback => 回调函数，需要在加载脚本前做的事情
//params :id =>可null ,如果传ID，那么先查看是否已经加载（但是RPC时可能会加载失败），已经加载过一次，删除节点，
let LoadJs = (url, id, callback) => {
  if (id) {
    var e = document.getElementById(id);
    if (e) {
      e.parentNode.removeChild(e);
    }
  }
  var script = document.createElement("script"),
    //获取回调函数
    fn = callback || function () {};
  script.type = "text/javascript";
  script.id = id;
  //IE
  if (script.readyState) {
    script.onreadystatechange = function () {
      if (script.readyState == "loaded" || script.readyState == "complete") {
        script.onreadystatechange = null;
        fn();
      }
    };
  } else {
    //其他浏览器
    script.onload = function () {
      fn();
    };
  }
  script.src = url;
  //加载js脚本文件
  document.getElementsByTagName("head")[0].appendChild(script);
};

// 获取编辑权限，false有编辑权限；true无编辑权限
// 参数：dataID：修改记录主键；dataType：记录类型
let getEditAuthority = async (dataID, dataType, refillFlag) => {
  // 如果画面是只读，直接返回无编辑权限
  if (session("readOnly")) {
    return false;
  }
  let key = dataType + "||" + dataID;
  let keyValue = session(key);
  if (keyValue) {
    return keyValue;
  }
  session(key, undefined);
  let params = {
    dataID: dataID,
    dataType: dataType,
    refillFlag: refillFlag,
  };
  let ret = "";
  await GetEditAuthority(params).then((result) => {
    if (isSuccess(result)) {
      session(key, undefined);
    } else {
      let message = result?.message;
      session(key, message);
      ret = message;
    }
  });
  return ret;
};
//检核操作权限
//参数：userID当前用户ID，userIDs可操作用户的集合
//返回值:checkResult是true有操作权限，false没有操作权限
let checkActionAuthorization = async (user, userIDs) => {
  // 如果画面是只读，直接返回无编辑权限
  if (session("readOnly")) {
    return false;
  }
  let checkResult = true; //默认检核通过
  let CheckFlag = false; //默认配置不检核
  let keyValue = session("CheckFlag");
  if (keyValue) {
    CheckFlag = keyValue;
  } else {
    let params = {
      settingType: 155,
      settingCode: "CheckActionAuthorization",
    };
    await GetOneSettingByTypeAndCode(params).then((result) => {
      if (isSuccess(result)) {
        if (result.data.typeValue == "True") {
          CheckFlag = true;
        }
      }
    });
    session("CheckFlag", CheckFlag);
  }

  if (CheckFlag && userIDs) {
    if (
      user.userID.trim() == userIDs.trim() ||
      user.userName.trim() == userIDs.trim() ||
      userIDs.trim() == "System" ||
      userIDs.trim() == "Sync" ||
      userIDs.trim() == "External" ||
      userIDs.trim() == "TongBu"
    ) {
      checkResult = true;
    } else {
      checkResult = false;
    }
  }
  return checkResult;
};
// 检测版本是否最新
let checkVersion = () => {
  // 请求静态json文件获取最新的版本号
  let url = `http://${
    window.location.host
  }/static/version.json?t=${new Date().getTime()}`;
  http.get(url).then((res) => {
    const version = res.version;
    const clientVersion = storage("system-version");
    // 若本地版本和最新版本不同，则刷新页面并保存最新版本
    if (version !== clientVersion) {
      storage("system-version", version);
      window.location.reload();
    }
  });
};
let addNewStyle = (newStyle) => {
  let styleElement = document.getElementById("dynamic_styles");
  if (styleElement) {
    document.head.removeChild(styleElement);
  }
  styleElement = document.createElement("style");
  styleElement.type = "text/css";
  styleElement.id = "dynamic_styles";
  document.head.appendChild(styleElement);
  styleElement.appendChild(document.createTextNode(newStyle));
};
/**
 * @description:
 * @param tableDom
 * @return
 */
let getTableOneRowHeight = (
  tableDom,
  tableRowClassName,
  tableHeaderClassName
) => {
  if (!tableDom) {
    return;
  }
  let tableOneRowHeight = undefined;
  const oneRowHeight =
    tableDom.querySelector(tableRowClassName)?.offsetHeight ?? 0;
  const tableHeaderHeight =
    tableDom.querySelector(tableHeaderClassName)?.offsetHeight ?? 0;
  tableOneRowHeight = oneRowHeight + tableHeaderHeight;
  const tableBody = tableDom.querySelector(".el-table__body-wrapper");
  // 出现了横向滚动条
  if (tableBody?.scrollWidth > tableBody.clientWidth) {
    tableOneRowHeight += 14;
  }
  //设置最高高度
  if (tableOneRowHeight > 150) {
    tableOneRowHeight = 150;
  }
  return tableOneRowHeight ?? 150;
};
/**
 * description: 保存前端日志
 * param {*} logData
 * param {*} logType
 * return {*}
 */
const saveWebLog = (logData) => {
  if (!logData || !logData.logType || !logData.logData) {
    return;
  }
  SaveWebLog(logData);
};
/**
 * @description: 越界判断
 * @param dom
 * @return
 */
const domAdjustPosition = (dom) => {
  if (!dom) {
    return undefined;
  }
  const rect = dom.getBoundingClientRect();

  // 检查元素是否在视口的下方
  if (rect.bottom > window.innerHeight) {
    return "bottom";
  }

  // 检查元素是否在视口的右侧
  if (rect.right > window.innerWidth) {
    return "right";
  }

  // 检查元素是否在视口的上方
  if (rect.top < 0) {
    return "top";
  }

  // 检查元素是否在视口的左侧
  if (rect.left < 0) {
    return "left";
  }
  return undefined;
};

//#region 用户选择器禁用
/**
 * 用于补录处理禁用下拉
 * @param {*} userID 当前的用户ID
 * @param {*} isAdd 是否为新增数据
 * @param {*} isSupplement 是否为补录数据
 * @param {*} rowUserID 当前数据要对比的人员ID
 * @returns disabledFlag 是否禁用下拉，True禁用，saveButtonFlag 是否显示保存按钮 True显示
 */
const userSelectorDisabled = async (userID, isAdd, isSupplement, rowUserID) => {
  let disabledFlag = false;
  let saveButtonFlag = true;
  let checkFlag = await shouldCheckUserSelector();
  if (!checkFlag) {
    return { disabledFlag, saveButtonFlag };
  }
  let headNurseFlag = await isHeadNurse(userID);
  if (!isSupplement) {
    return { disabledFlag, saveButtonFlag };
  }
  if (!isAdd) {
    return handleEditMode(rowUserID, userID, headNurseFlag);
  }
  return { disabledFlag: !headNurseFlag, saveButtonFlag };
};
/**
 * 获取当前环境是否检核补录下拉
 */
const shouldCheckUserSelector = async () => {
  let settingParams = {
    settingType: 339,
    settingCode: "CheckUserSelectorDisabled",
    index: Math.random(),
  };
  let result = await GetOneSettingByTypeAndCode(settingParams);
  return isSuccess(result) && result.data.typeValue === "True";
};
/**
 * 当前用户是否为护士长
 * @param {*} userID 用户ID
 * @returns
 */
const isHeadNurse = async (userID) => {
  let userParams = {
    userID,
    index: Math.random(),
  };
  let result = await GetIsHeadNurse(userParams);
  return isSuccess(result) ? result.data : false;
};
/**
 * 是否禁用
 * @param {*} rowUserID 行数据人ID
 * @param {*} userID 当前人ID
 * @param {*} headNurseFlag 当前人是否为护士长
 * @returns
 */
const handleEditMode = (rowUserID, userID, headNurseFlag) => {
  if (rowUserID === userID || headNurseFlag) {
    return { disabledFlag: true, saveButtonFlag: true };
  }
  return { disabledFlag: true, saveButtonFlag: false };
};
//#endregion

/**
 * 按照指定的各个key自定义升降的排序
 *     demo：sortByKeys(arr, ['sex', "age", "class", "height"], [1, 0, 0, 1]);
 *     说明：集合按照sex升序，age降序，class降序，height升序进行排序
 * @param arr 数据源
 * @param keys 排序的key
 * @param isAscend 默认为全升序(1为升序，0为降序)
 * @returns
 */
const sortByKeys = (arr, keys, isAscend) => {
  let keyLen = keys.length;
  if (keyLen <= 0) {
    console.error("排序keys为空！");
    return;
  }
  let newAscend = [];
  if (!isAscend) {
    for (let i = 0; i < keyLen; i++) {
      newAscend.push(1);
    }
  } else {
    newAscend = isAscend;
  }
  if (keyLen != newAscend.length) {
    console.error("排序keys和排序规则数量不一致！");
    return;
  }
  arr.sort((arr1, arr2) => {
    for (let index = 0; index < keys.length; index++) {
      let key = keys[index];
      let arr1Value = arr1[key];
      let arr2Value = arr2[key];
      if (arr1Value === arr2Value) {
        continue;
      } else {
        let isAsc = newAscend[index];
        return isAsc ? arr1Value - arr2Value : arr2Value - arr1Value;
      }
    }
    return 0;
  });
};
const removeEmptyHtmlTags = (html) => {
  let newHtml = clone(html);
  // 正则表达式，匹配空的html标签，包含标签内只有 空格、&nbsp;不区分大小写
  const regex = /<([\w:]+)(\s+[^>]*)?>(?:\s|&nbsp;?|&#160;?)*<\/\1>/gi;
  let previous;
  do {
    previous = newHtml;
    newHtml = newHtml.replace(regex, "");
  } while (previous !== newHtml);
  return newHtml;
};
export default {
  clone,
  guid,
  isPC,
  storage,
  session,
  removeSession,
  isSuccess,
  getHeigt,
  colorRGB2Hex,
  checkAssessTN,
  completeSpelling,
  initial,
  LoadJs,
  getEditAuthority,
  checkActionAuthorization,
  checkVersion,
  addNewStyle,
  getTableOneRowHeight,
  saveWebLog,
  domAdjustPosition,
  userSelectorDisabled,
  sortByKeys,
  removeEmptyHtmlTags,
};
