---
description: 
globs: 
alwaysApply: true
---
# 编码规范指南

你是以下技术栈的专家：
- Javascript
- Node.js
- Vue2
- Webpack
- Element UI
- echarts
- axios

### 核心原则：

- 编写简洁、技术性的响应，并提供准确的 Javascript 示例
- 优先使用迭代和模块化，而不是代码重复定义
- 使用描述性变量名，包含助动词（如 isLoading）
- components下的组件使用驼峰命名，首字母大写（如 components/AuthWizard）
- 变量、方法名使用驼峰命名，首字母小写
- 方法名使用动词或者动词+名词形式
- vue组件使用驼峰命名，首字母小写
- api文件使用驼峰命名，首字母大写
- 一律使用undefined，不准使用null
- vue组件标签顺序：template -> script -> style
- vue组件script标签中，选项顺序：components -> props -> data -> computed -> watch -> created -> mounted -> methods
- 注释需在代码上一行
- 尽量使用const声明变量，其次let
- css使用scss书写
- css的class名称，全部小写，用“-”连接单词
- css样式值为0时，省略单位
- 一个方法不可超过50行
- 不准使用行内样式
- 日期格式：yyyy-MM-dd
- 时间格式：HH:mm
- 模板中，日期格式化使用v-formatTime自定义指令
- 使用this._common.isSuccess判断判断API返回的code，不准使用else
- 操作session和storage使用this._common下的session和storage方法
- 删除数据前提示确认框需使用this.deleteConfirm方法
- 页面提示使用this._showTip
- 表格表头居中对齐，表格内容字数确定的居中对齐，不定的左对齐
- 表格同一添加border和stripe

### JavaScript/TypeScript 规范：

- 条件语句中避免不必要的大括号
- 单行条件语句不可省略大括号
- 禁止三元表达式嵌套，使用阅读性更好的条件语句
- if-else 过多时优化为 map 设计

### 错误处理优先级：
- 在函数开始处处理错误和边界情况
- 对错误条件使用提前返回，避免深层嵌套的 if 语句
- 将正常执行路径放在函数末尾以提高可读性
- 避免不必要的 else 语句；使用 if-return 模式
- 使用守卫子句尽早处理前置条件和无效状态
- 实现适当的错误日志记录和用户友好的错误消息
- 考虑使用自定义错误类型或错误工厂以保持错误处理的一致性

### 依赖及第三方库：
- Vue 2.5.2
- Element UI 2.12.0
- vue-router 3.0.1
   