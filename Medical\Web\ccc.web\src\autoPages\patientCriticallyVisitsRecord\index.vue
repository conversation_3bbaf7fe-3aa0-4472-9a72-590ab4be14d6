<!--
 * FilePath     : \src\autoPages\patientCriticallyVisitsRecord\index.vue
 * Author       : 胡长攀
 * Date         : 2024-06-26 15:13
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-02-22 10:16
 * Description  : 危重患者访视记录页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout
    class="patient-critically-visits-record"
    v-loading="loading"
    element-loading-text="加载中……"
    header-height="auto"
  >
    <div slot="header" class="top">
      <el-radio-group v-model="visitsType" @change="filterCriticallyPatientList">
        <el-radio-button :label="1">未访视</el-radio-button>
        <el-radio-button :label="2">已访视</el-radio-button>
        <el-radio-button :label="0">不访视</el-radio-button>
      </el-radio-group>
      <station-selector
        v-model="stationID"
        :hospitalFlag="true"
        :width="convertPX(200) + 'px'"
        class="select-station"
        @select-item="changeStation"
        @change="getCriticallyPatientList"
      ></station-selector>
      <dept-selector
        :clearable="true"
        :disabled="deptSelectorDisabled"
        :width="convertPX(200) + 'px'"
        v-model="departmentListID"
        :stationID="stationID"
        @change="filterCriticallyPatientList"
      />
      <label>病案号：</label>
      <el-input
        v-model="chartNO"
        class="search-inpatient-input"
        placeholder="请输入病案号"
        @change="filterCriticallyPatientList"
      >
        <i slot="append" class="iconfont icon-search"></i>
      </el-input>
      <label>危重日期：</label>
      <el-date-picker
        class="top-date-picker"
        v-model="criticalStartDate"
        type="date"
        clearable
        placeholder="选择日期"
        @change="filterCriticallyPatientList"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        class="top-date-picker"
        v-model="criticalEndDate"
        type="date"
        clearable
        placeholder="选择日期"
        @change="filterCriticallyPatientList"
      ></el-date-picker>
      <label>访视日期：</label>
      <el-date-picker
        class="top-date-picker"
        v-model="visitDate"
        type="date"
        clearable
        placeholder="选择日期"
        @change="filterCriticallyPatientList"
      ></el-date-picker>
      <label>访视人员：</label>
      <el-select
        class="top-select"
        v-model="visitEmployeeID"
        clearable
        placeholder="请选择"
        @change="filterCriticallyPatientList"
      >
        <el-option v-for="item in visitEmployees" :key="item.key" :label="item.value" :value="item.value"></el-option>
      </el-select>
    </div>
    <el-table ref="criticallyPatientDataTable" :data="showCriticallyPatientList" border stripe highlight-current-row>
      <el-table-column
        label="类型"
        prop="criticalIllnessTypeName"
        :width="convertPX(80)"
        align="center"
      ></el-table-column>
      <el-table-column label="病区" prop="stationName" :min-width="convertPX(150)"></el-table-column>
      <el-table-column label="科室" prop="departmentListName" :min-width="convertPX(150)"></el-table-column>
      <el-table-column label="病历号" prop="chartNo" align="center" :width="convertPX(150)"></el-table-column>
      <el-table-column label="姓名" prop="patientName" align="center" :width="convertPX(120)"></el-table-column>
      <el-table-column label="性别" prop="gender" align="center" :width="convertPX(80)"></el-table-column>
      <el-table-column label="年龄" prop="age" align="center" :width="convertPX(80)"></el-table-column>
      <el-table-column label="病危时间" align="center" :width="convertPX(150)">
        <template slot-scope="scope">
          <span
            :class="{ 'text-red-bold': scope.row.timeoutPromptFlag }"
            v-formatTime="{ value: scope.row.occurDateTime, type: 'dateTime' }"
          ></span>
        </template>
      </el-table-column>
      <el-table-column
        label="病危诊断"
        prop="criticalIllnessDiagnosis"
        align="center"
        :min-width="convertPX(150)"
      ></el-table-column>
      <el-table-column label="访视时间" align="center" :width="convertPX(150)">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.headNurseVisitsDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column
        label="访视人员"
        prop="visitsEmployeeName"
        align="center"
        :width="convertPX(120)"
      ></el-table-column>
      <el-table-column label="记录时间" align="center" :width="convertPX(150)">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.recordDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :width="convertPX(100)">
        <template slot-scope="scope">
          <el-tooltip v-if="nurseInputFlag" content="患者情况">
            <i class="iconfont icon-patient-situation" @click="openDialog(scope.row, '1')"></i>
          </el-tooltip>
          <el-tooltip v-if="headNurseInputFlag" content="科护士长指导内容">
            <i class="iconfont icon-head-nurse-guidance-content" @click="openDialog(scope.row, '2')"></i>
          </el-tooltip>
          <el-tooltip v-if="nursingDepartmentInputFlag" content="三级访视指导内容">
            <i class="iconfont icon-third-level-visits-guidance-content" @click="openDialog(scope.row, '3')"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="dialogTitle"
      :visible.sync="showDialog"
      custom-class="detail-dialog"
    >
      <table class="detail-dialog-table" cellpadding="7">
        <tr v-for="(row, index) in tableFields" :key="index">
          <td v-for="column in row" :key="column.valueKey" class="detail-dialog-column" :colspan="column.colspan">
            <div class="basic-content">
              <div class="basic-label">{{ column.label }}</div>
              ：
              <div class="basic-label-value">
                <span v-if="column.type === 'text'">{{ currentRowData[column.valueKey] }}</span>
                <span
                  v-else-if="column.type === 'dateTime'"
                  v-formatTime="{ value: currentRowData[column.valueKey], type: 'dateTime' }"
                ></span>
              </div>
            </div>
          </td>
        </tr>
        <tr v-for="(row, index) in tableFields2" :key="index">
          <td colspan="3" class="detail-dialog-column">
            <div class="other-content">
              <div class="other-label">{{ row.label }}：</div>
              <div v-if="row.hasRecordTime" class="content-time">
                <div class="content-time-label">记录时间：</div>
                <el-date-picker
                  class="content-time-date-picker"
                  v-model="currentRowData[row.timeValueKey]"
                  type="datetime"
                  format="yyyy-MM-dd HH:mm"
                  :disabled="inputFlag !== row.disabledCondition"
                ></el-date-picker>
              </div>
              <el-input
                class="dialog-content-textarea"
                type="textarea"
                placeholder="请输入内容"
                v-model="currentRowData[row.labelValueKey]"
                :maxlength="row.maxLength"
                :show-word-limit="row.showWordLimit"
                :disabled="inputFlag !== row.disabledCondition"
                :rows="row.rows"
              ></el-input>
            </div>
          </td>
        </tr>
      </table>
      <span slot="footer">
        <el-checkbox v-model="isNeedVisits">是否需要访视</el-checkbox>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRecord">保 存</el-button>
      </span>
    </el-dialog>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import deptSelector from "@/components/selector/deptSelector";
import stationSelector from "@/components/selector/stationSelector";
import { mapGetters } from "vuex";
import { GetHeadNurseList } from "@/api/Station";
import { GetCriticallyPatientList, SaveCriticallyPatientVisitsRecord } from "@/api/PatientCriticallyVisitsRecord";
export default {
  components: {
    baseLayout,
    deptSelector,
    stationSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      //科室
      departmentListID: undefined,
      //病区
      stationID: undefined,
      //病历号
      chartNO: undefined,
      //是否禁用科室选择器
      deptSelectorDisabled: false,
      //加载
      loading: false,
      //访视类型
      visitsType: 1,
      //病危开始时间
      criticalStartDate: "",
      //病危结束时间
      criticalEndDate: "",
      //访视时间
      visitDate: "",
      //访视人员
      visitEmployeeID: "",
      //访视人员列表
      visitEmployees: [],
      //病危患者列表
      criticallyPatientList: [],
      showCriticallyPatientList: [],
      //是否显示弹窗
      showDialog: false,
      //当前行数据
      currentRowData: {},
      //护士长输入标记
      nurseInputFlag: false,
      //科护士长输入标记
      headNurseInputFlag: false,
      //护理部输入标记
      nursingDepartmentInputFlag: false,
      //弹窗标题
      dialogTitle: "",
      //输入标记，1:护士长，2:科护士长，3:护理部
      inputFlag: undefined,
      //是否需要访视
      isNeedVisits: true,
      tableFields: [
        [
          {
            label: "病房",
            valueKey: "stationName",
            type: "text",
            colspan: 1,
          },
          {
            label: "护士长",
            valueKey: "nurseName",
            type: "text",
            colspan: 1,
          },
          {
            label: "科护士长",
            valueKey: "headNurseName",
            type: "text",
            colspan: 1,
          },
        ],
        [
          {
            label: "患者姓名",
            valueKey: "patientName",
            type: "text",
            colspan: 1,
          },
          {
            label: "性别",
            valueKey: "gender",
            type: "text",
            colspan: 1,
          },
          {
            label: "年龄",
            valueKey: "age",
            type: "text",
            colspan: 1,
          },
        ],
        [
          {
            label: "入院时间",
            valueKey: "admissionDateTime",
            type: "dateTime",
            colspan: 1,
          },
          {
            label: "通知时间",
            valueKey: "informFamilyDateTime",
            type: "dateTime",
            colspan: 1,
          },
        ],
        [
          {
            label: "入院诊断",
            valueKey: "admissionDiagnosis",
            type: "text",
            colspan: 3,
          },
        ],
      ],
      tableFields2: [
        {
          label: "病危诊断",
          labelValueKey: "criticalIllnessDiagnosis",
          hasRecordTime: true,
          timeValueKey: "visitsDateTime",
          disabledCondition: "1",
          maxLength: 1000,
          showWordLimit: false,
          rows: 1,
        },
        {
          label: "简要病情",
          labelValueKey: "briefDiseaseCondition",
          hasRecordTime: false,
          timeValueKey: "",
          disabledCondition: "1",
          maxLength: 1000,
          showWordLimit: true,
          rows: 3,
        },
        {
          label: "观察要点",
          labelValueKey: "observationPoints",
          hasRecordTime: false,
          timeValueKey: "",
          disabledCondition: "1",
          maxLength: 1000,
          showWordLimit: true,
          rows: 3,
        },
        {
          label: "科护士长指导内容",
          labelValueKey: "headNurseGuidanceContent",
          hasRecordTime: true,
          timeValueKey: "headNurseVisitsDateTime",
          disabledCondition: "2",
          maxLength: 1000,
          showWordLimit: true,
          rows: 3,
        },
        {
          label: "三级访视指导内容",
          labelValueKey: "thirdLevelPatrolGuidanceContent",
          hasRecordTime: true,
          timeValueKey: "nursingDeptVisitsDateTime",
          disabledCondition: "3",
          maxLength: 1000,
          showWordLimit: true,
          rows: 3,
        },
      ],
    };
  },
  async created() {
    await this.init();
    await this.getCriticallyPatientList();
    this.filterCriticallyPatientList();
  },
  methods: {
    /**
     * @description: 初始化
     * @return
     */
    async init() {
      await GetHeadNurseList().then(async (res) => {
        if (this._common.isSuccess(res)) {
          this.visitEmployees = res.data;
        }
      });
      this.nurseInputFlag = this.user.roles.includes(40);
      this.headNurseInputFlag = this.visitEmployees.some((employee) => {
        return this.user.userID === employee.key;
      });
      this.nursingDepartmentInputFlag = this.user.roles.includes(70);
    },
    /**
     * @description: 获取危重患者访视记录
     * @return
     */
    async getCriticallyPatientList() {
      let param = {
        stationID: this.stationID,
      };
      this.loading = true;
      await GetCriticallyPatientList(param).then(async (result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.criticallyPatientList = result.data;
          this.showCriticallyPatientList = result.data;
          this.filterCriticallyPatientList();
        }
      });
    },
    /**
     * @description: 打开弹窗
     * @param row 当前行数据
     * @param inputFlag 输入标记，1:护士长，2:科护士长，3:护理部
     * @return
     */
    openDialog(row, inputFlag) {
      if ((inputFlag === "2" || inputFlag === "3") && !row.employeeID && !row.visitsDateTime) {
        this._showTip("warning", "请护士长填写患者情况");
        return;
      }
      if (inputFlag === "3" && !row.headNurseEmployeeID && !row.headNurseVisitsDateTime) {
        this._showTip("warning", "请科护士长填写指导内容");
        return;
      }
      this.showDialog = true;
      this.dialogTitle = row.patientName + "【" + row.gender + "-" + row.age + "】--" + row.admissionDiagnosis;
      this.currentRowData = this._common.clone(row);
      this.isNeedVisits = row.visitsFlag !== 0;
      this.inputFlag = inputFlag;
    },
    /**
     * @description: 保存记录
     * @return
     */
    async saveRecord() {
      let checkFlag = await this.saveRecordCheck();
      if (!checkFlag) {
        return;
      }
      this.currentRowData.visitsFlag = !this.isNeedVisits ? 0 : this.currentRowData.headNurseGuidanceContent ? 2 : 1;
      this.loading = true;
      await SaveCriticallyPatientVisitsRecord(this.currentRowData).then(async (result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.showDialog = false;
          this._showTip("success", "保存成功！");
          await this.getCriticallyPatientList();
        }
      });
    },
    /**
     * @description: 保存检核
     * @return
     */
    async saveRecordCheck() {
      if (this.inputFlag === "1") {
        if (!this.currentRowData.criticalIllnessDiagnosis) {
          this._showTip("warning", "请输入病危诊断！");
          return false;
        }
        if (!this.currentRowData.briefDiseaseCondition) {
          this._showTip("warning", "请输入简要病情！");
          return false;
        }
        if (!this.currentRowData.observationPoints) {
          this._showTip("warning", "请输入观察要点！");
          return false;
        }
        if (!this.currentRowData.visitsDateTime) {
          this._showTip("warning", "请选择记录时间！");
          return false;
        }
        this.currentRowData.employeeID = this.user.userID;
        this.currentRowData.visitsDateTime = this._datetimeUtil.formatDate(
          this.currentRowData.visitsDateTime,
          "yyyy-MM-dd hh:mm"
        );
        return true;
      }
      if (this.inputFlag === "2") {
        if (!this.currentRowData.headNurseGuidanceContent) {
          this._showTip("warning", "请输入科护士长指导内容！");
          return false;
        }
        if (!this.currentRowData.headNurseVisitsDateTime) {
          this._showTip("warning", "请选择记录时间！");
          return false;
        }
        this.currentRowData.headNurseEmployeeID = this.user.userID;
        this.currentRowData.headNurseVisitsDateTime = this._datetimeUtil.formatDate(
          this.currentRowData.headNurseVisitsDateTime,
          "yyyy-MM-dd hh:mm"
        );
        return true;
      }

      if (!this.currentRowData.thirdLevelPatrolGuidanceContent) {
        this._showTip("warning", "请输入三级访视指导内容！");
        return false;
      }
      if (!this.currentRowData.nursingDeptVisitsDateTime) {
        this._showTip("warning", "请选择记录时间！");
        return false;
      }
      this.currentRowData.nursingDeptEmployeeID = this.user.userID;
      this.currentRowData.nursingDeptVisitsDateTime = this._datetimeUtil.formatDate(
        this.currentRowData.nursingDeptVisitsDateTime,
        "yyyy-MM-dd hh:mm"
      );
      return true;
    },
    /**
     * @description: 筛选危重患者
     * @return
     */
    filterCriticallyPatientList() {
      if (!this.criticallyPatientList) {
        return;
      }
      this.showCriticallyPatientList = this.criticallyPatientList.filter((item) => {
        const matchesChartNO = this.chartNO ? item.chartNo === this.chartNO : true;
        const matchesVisitsType = item.visitsFlag === this.visitsType;
        const departmentListID = this.departmentListID ? item.departmentListID === this.departmentListID : true;
        const matchesCriticalStartDate = this.criticalStartDate
          ? this._datetimeUtil.formatDate(item.occurDateTime, "yyyy-MM-dd") >=
            this._datetimeUtil.formatDate(this.criticalStartDate, "yyyy-MM-dd")
          : true;
        const matchesCriticalEndDate = this.criticalEndDate
          ? this._datetimeUtil.formatDate(item.occurDateTime, "yyyy-MM-dd") <=
            this._datetimeUtil.formatDate(this.criticalEndDate, "yyyy-MM-dd")
          : true;
        const matchesVisitDate = this.visitDate
          ? item.headNurseVisitsDateTime
            ? this._datetimeUtil.formatDate(item.headNurseVisitsDateTime, "yyyy-MM-dd") ===
              this._datetimeUtil.formatDate(this.visitDate, "yyyy-MM-dd")
            : false
          : true;
        const matchesVisitEmployee = this.visitEmployeeID ? item.visitsEmployeeID === this.visitEmployeeID : true;
        return (
          matchesChartNO &&
          matchesVisitsType &&
          departmentListID &&
          matchesCriticalStartDate &&
          matchesCriticalEndDate &&
          matchesVisitDate &&
          matchesVisitEmployee
        );
      });
    },
    /**
     * @description: 选择病区回调函数
     * @param station 病区
     * @return
     */
    changeStation(station) {
      if (station.id === 999999) {
        this.deptSelectorDisabled = true;
        this.departmentListID = undefined;
        return;
      }
      this.deptSelectorDisabled = false;
    },
  },
};
</script>

<style lang="scss">
.patient-critically-visits-record {
  .top {
    .search-inpatient-input {
      width: 150px;
      .el-input-group__append {
        padding: 0 5px;
        color: #8cc63e;
      }
    }
    .top-date-picker {
      width: 140px;
      .el-input__suffix {
        display: block;
      }
    }
    .top-select {
      width: 120px;
    }
  }
  .icon-patient-situation::before {
    color: #ff7400;
  }
  .icon-head-nurse-guidance-content::before {
    color: #1cc6a3;
  }
  .icon-third-level-visits-guidance-content::before {
    color: #409eff;
  }
  .detail-dialog.el-dialog {
    .el-dialog__body {
      padding: 0;
    }
    .el-dialog__footer {
      padding: 0px 20px;
      margin-top: -5px;
    }
    .detail-dialog-table {
      width: 100%;
      padding: 10px;
      table-layout: fixed;

      .detail-dialog-column {
        .basic-content {
          display: flex;
          flex-wrap: nowrap;
          .basic-label {
            width: 60px;
            flex-shrink: 0;
            text-align: justify;
            text-align-last: justify;
          }
          .basic-label-value {
            width: 100%;
            margin-right: 10px;
            border: 1px solid #d5d5d5;
            padding: 3px 0px 3px 10px;
            border-radius: 4%;
            color: #8f8a8a;
          }
        }
        .other-content {
          display: flex;
          flex-wrap: wrap;
          .other-label {
            display: flex;
            flex: 2;
          }
          .content-time {
            display: flex;
            flex: 1;
            margin-right: 10px;
            .content-time-label {
              display: flex;
              flex-direction: row-reverse;
              width: 100%;
            }
            .content-time-date-picker {
              width: 100%;
              .el-input__inner {
                border: 1px solid $base-color;
              }
            }
          }
        }
        .dialog-content-textarea {
          font-size: 13px;
          margin-right: 10px;
          margin-top: 5px;
          .el-textarea__inner {
            border: 1px solid $base-color;
          }
        }
        .el-textarea.is-disabled .el-textarea__inner {
          background-color: #ffffff;
          border: 1px solid #d5d5d5;
        }
        .el-input.is-disabled .el-input__inner {
          background-color: #ffffff;
          border: 1px solid #d5d5d5 !important;
        }
      }
    }
  }
  .text-red-bold {
    color: red;
    font-weight: bold;
  }
}
</style>