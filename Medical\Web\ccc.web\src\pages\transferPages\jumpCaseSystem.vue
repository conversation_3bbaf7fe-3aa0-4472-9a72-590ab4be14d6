<!--
 * FilePath     : \src\pages\transferPages\jumpCaseSystem.vue
 * Author       : 郭鹏超
 * Date         : 2022-04-15 16:37
 * LastEditors  : 苏军志
 * LastEditTime : 2024-04-05 14:14
 * Description  : 跳转个案管理系统相关画面
 * CodeIterationRecord: 
-->
<template>
  <div class="jump-case-system">
    <!-- <div class="header">
      <span class="label">床号：</span>
      <el-input
        v-model="bedNumber"
        placeholder="请输入床号"
        class="search-input"
        @keyup.native="getPatientData($event, 'bedNumber')"
      />
      <span class="label">住院号：</span>
      <el-input
        v-model="localCaseNumber"
        placeholder="请输入住院号"
        class="search-input"
        @keyup.native="getPatientData($event, 'localCaseNumber')"
      />
    </div>
    <iframe v-if="url" :src="url" frameborder="0"></iframe> -->
  </div>
</template>

<script>
// import { GetPatientDataByBedNumber, GetInpatientDataByLocalCaseNumber } from "@/api/Inpatient";
export default {
  data() {
    return {
      // baseUrl: "http://*********/crm/",
      baseUrl: "http://*************:8993/hospital-mr",
      // createRecord: "hospital-mr",
      // url: "",
      chartNo: "",
      deptCode: "",
      // bedNumber: "",
      // localCaseNumber: "",
    };
  },

  created() {
    this.chartNo = this.$route.query.chartNo;
    this.deptCode = this.$route.query.deptCode;
    // this.bedNumber = this.$route.query.bedNumber;
    // this.localCaseNumber = this.$route.query.localCaseNumber;
    this.loadUrl();
  },
  methods: {
    loadUrl() {
      // let url = this.baseUrl + this.createRecord;
      // if (url.indexOf("?") > -1) {
      //   url = url + "&patient_id=" + this.chartNo + "&dept_code=" + this.deptCode;
      // } else {
      //   url = url + "?patient_id=" + this.chartNo + "&dept_code=" + this.deptCode;
      // }
      // this.url = url;
      let url = `${this.baseUrl}?patient_id=${this.chartNo}&dept_code=${this.deptCode}`;
      window.location.href = url;
    },
    // async getPatientData(event, type) {
    //   if (type == "bedNumber") {
    //     this.localCaseNumber = "";
    //   } else {
    //     this.bedNumber = "";
    //   }
    //   // 非回车键返回
    //   if (event.keyCode != 13) {
    //     return;
    //   }
    //   if (type == "bedNumber") {
    //     let params = {
    //       bedNumber: this.bedNumber,
    //     };
    //     await GetPatientDataByBedNumber(params).then((result) => {
    //       if (this._common.isSuccess(result)) {
    //         this.chartNo = result.data.chartNo;
    //         this.deptCode = result.data.departmentCode;
    //       }
    //     });
    //   } else {
    //     let params = {
    //       number: this.localCaseNumber,
    //     };
    //     await GetInpatientDataByLocalCaseNumber(params).then((result) => {
    //       if (this._common.isSuccess(result) && result.data && result.data.length > 0) {
    //         this.chartNo = result.data[0].chartNo;
    //         this.deptCode = result.data[0].departmentCode;
    //       }
    //     });
    //   }
    //   this.url = "";
    //   this.$nextTick(() => {
    //     this.loadUrl();
    //   });
    // },
  },
};
</script>

<style lang="scss">
.jump-case-system {
  height: 100%;
  width: 100%;
  // .header {
  //   height: 40px;
  //   line-height: 30px;
  //   padding: 5px 20px;
  //   box-sizing: border-box;
  //   background-color: #ffffff;
  //   .search-input {
  //     width: 100px;
  //     height: 30px;
  //     margin-right: 10px;
  //     .el-input__inner {
  //       height: 24px;
  //     }
  //   }
  // }
  // iframe {
  //   margin-top: 10px;
  //   height: calc(100% - 56px);
  //   width: 100%;
  // }
}
</style>