.mask {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9998;
  background-color: transparent;
}

.key-main {
  background-color: #696969;
  position: fixed;
  left: 65px;
  bottom: 0px;
  width: calc(100% - 85px);
  padding: 5px;
  margin: 0 5px;
  z-index: 9999;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
}

.show-enter-active,
.show-leave-active {
  transition: all 0.5s;
}

.show-enter,
.show-leave-to {
  bottom: -50%;
  opacity: 0.5;
}

.show-enter-to,
.show-leave {
  opacity: 1;
}

.key-main table {
  width: calc(100% - 10px);
  height: 100%;
}

.key-main tr {
  width: 100%;
  height: calc(20%);
  text-align: center;
}

.key-main .btn {
  background-color: #fff;
  color: #000;
  margin: 1px;
  padding: 6px;
  border-radius: 10px;
  font-size: 26px;
  line-height: 100%;
}

.key-main .btn:active {
  background-color: #4abfa2;
  color: #fff;
}

.key-main .btn [class^="iconfont"] {
  margin-top: 4px;
  font-size: 26px;
  color: #4abfa2;
}

.key-main .clear.btn {
  font-size: 26px;
  color: #4abfa2;
}

.key-main .clear.btn:active,
.key-main .btn:active [class^="iconfont"] {
  color: #fff;
}

.focus-border {
  outline: none !important;
  border: 1px solid #4abfa2 !important;
}
