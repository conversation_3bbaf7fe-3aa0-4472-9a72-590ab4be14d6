<!--
 * FilePath     : \src\autoPages\login\index.vue
 * Author       : 苏军志
 * Date         : 2022-05-08 19:16
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-19 10:19
 * Description  : 登录画面
 * CodeIterationRecord:
-->
<template>
  <div class="login-main" v-enter="{ function: 'login' }">
    <img :class="['background-image', `hospital${hospitalID}`]" :src="homePageImage" />
    <div :class="['background-mark', `hospital${hospitalID}`]"></div>
    <div :class="['logo', `hospital${hospitalID}`]">
      <img class="logo-img" :src="hospitalImage" />
    </div>
    <div :class="['login-left-img-wrap', `hospital${hospitalID}`]">
      <img class="login-left-img" :src="homePageImage" />
    </div>
    <div :class="['login-form', `hospital${hospitalID}`]" v-loading="loading" element-loading-text="登录中……">
      <div class="login-top">
        <div :class="['hospital-name', { 'is-education': isEducation }]" v-html="hospitalName"></div>
        <img class="title" src="../../../static/images/title.png" />
      </div>
      <div class="login-text">
        <!-- 填充测试账号，快捷键 填充：ctrl + alt + f，清空：ctrl + alt + c -->
        <el-input
          @keydown.ctrl.alt.70.native.stop="fillTestAccount(true)"
          @keyup.ctrl.alt.67.native.stop="fillTestAccount(false)"
          @keyup.enter.native.stop="nextFocus($event)"
          :placeholder="loginTexts.userPalceholder"
          v-model="userName"
        >
          <template slot="prepend"><i class="iconfont icon-login-user"></i></template>
        </el-input>
        <el-input
          class="password"
          :placeholder="loginTexts.passwordPalceholder"
          v-model="password"
          maxlength="20"
          @focus="$refs.passwordInp.type = 'password'"
          ref="passwordInp"
          @keyup.enter.native="login()"
        >
          <template slot="prepend"><i class="iconfont icon-login-password"></i></template>
        </el-input>
      </div>
      <div class="login-operation">
        <el-button class="login-button" :disabled="!isAgreeClaimer" @click="login()" type="primary">
          {{ loginTexts.loginBtn }}
        </el-button>
        <el-select
          v-if="showSelectHospital"
          placeholder="请选择医院"
          v-model="hospitalID"
          class="select-hospital"
          @change="changeHospital"
        >
          <el-option
            v-for="item in localHospitalList"
            :key="item.hospitalID"
            :label="item.hospitalName"
            :value="item.hospitalID"
          ></el-option>
        </el-select>
        <el-select v-model="languageID" class="language" @change="changeLanguage">
          <el-option
            v-for="item in languageList"
            :key="item.languageID"
            :label="item.languageName"
            :value="item.languageID"
          ></el-option>
        </el-select>
      </div>
    </div>
    <div v-if="showCALogin && caParams" class="ca-login-qr">
      <img :src="caParams.qrData" alt="CA扫码登录" />
      <span class="ca-text">【云医签】扫码登录</span>
    </div>
    <div :class="['login-claimer', `hospital${hospitalID}`]">
      <el-checkbox v-model="isAgreeClaimer">{{ loginTexts.disclaimers }}</el-checkbox>
      <div>{{ loginTexts.disclaimersContent }}</div>
    </div>
    <div :class="['system-logo', `hospital${hospitalID}`]"></div>
    <div :class="['footer', `hospital${hospitalID}`]">
      <div class="system">{{ $t("label.systemName") }}</div>
      <div class="version">
        {{ systemVersion }}
      </div>
      <div class="footer-right">
        <el-tooltip :content="loginTexts.clearCacheTip" placement="top" effect="light">
          <a target="_blank" @click="clearCache">{{ loginTexts.clearCache }}</a>
        </el-tooltip>
        <router-link target="_blank" :to="{ path: '/help' }">{{ loginTexts.help }}</router-link>
        <span>&copy; {{ loginTexts.company }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import {
  GetHospitalList,
  GetLanguage,
  GetServerDateTime,
  GetVersionByHospitalID,
  GetAppConfigSetting,
} from "@/api/Setting";
import { GetSettingSwitch } from "@/api/SettingDescription";
import { GetSession, userLogin, GenCAQrCode, CheckCASignInfo, CheckLoginAndCA, SyncEmployeeCAData } from "@/api/User";
import { encryption } from "@/utils/encryptionUtils";
import { getEducationEdition } from "@/utils/setting";
import { mapGetters } from "vuex";
import { wp } from "@/utils/web-proxy";
export default {
  data() {
    return {
      homePageImage: "../../../static/images/login_bg.jpg",
      hospitalImage: "../../../static/images/small-logo.png",
      hospitalName: "OOOO医院",
      hospitalID: undefined,
      systemVersion: "2.0.0",
      languageList: [],
      languageID: undefined,
      url: "",
      userName: "",
      password: "",
      isAgreeClaimer: true,
      loading: false,
      clientType: 1,
      showSelectHospital: false,
      encryptionFlag: "",
      passwordInputType: "",
      worker: undefined,
      // 目前只有宏力使用
      showCALogin: false,
      caParams: undefined,
      // ca二维码1分钟内有效，超时自动刷新
      caQRCodeRefreshTime: 60,
      //Ukey拔出时清除登录状态API
      removeSessionAPI: undefined,
      //该医院是否使用UKey并进行登录检核
      checkLoginAndUkeyCAFlag: false,
    };
  },
  computed: {
    ...mapGetters({
      localHospitalList: "getHospitalList",
      localHospitalInfo: "getHospitalInfo",
      localLanguageList: "getLanguageList",
      isLogin: "isLogin",
    }),
    loginTexts() {
      return this.$t("login");
    },
    isEducation() {
      return getEducationEdition();
    },
  },
  async created() {
    // 系统打开 先刷新一次，保证最新
    this.reload();
    await this.getRemoveSessionAPI();
    this.$store.commit("session/setExternalParams", undefined);
    this.url = this.$route.query.url;
    // 获取服务器时间
    await this.getServerDateTime();
    // 打开定时矫正时间线程
    this.openTimeWorker();
    // 获取医院信息
    await this.getHospital();
    // 获取语言列表
    await this.getLanguage();
    if (this._common.isPC()) {
      this.clientType = 1;
    } else {
      this.clientType = 3;
    }
    await this.getEncryptionSetting();
    await this.getCheckLoginAndUkeyCAFlag();
  },
  methods: {
    //填充测试账号，快捷键 填充：ctrl + alt + f，清空：ctrl + alt + c
    fillTestAccount(flag) {
      if (flag) {
        this.userName = "test001";
        this.password = "dis0.+";
      } else {
        this.userName = "";
        this.password = "";
      }
    },
    async getEncryptionSetting() {
      let param = {
        SettingTypeCode: "LoginEncryption",
        HospitalID: this.hospitalID,
      };
      await GetSettingSwitch(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.encryptionFlag = response.data;
        }
      });
    },
    // 刷新系统，保证最新版本
    reload() {
      let lastReloadTimestamp = this._common.storage("lastReloadTimestamp");
      let newTimestamp = new Date().getTime();
      // 5分钟内不刷新
      if (!lastReloadTimestamp || newTimestamp - lastReloadTimestamp > 1000 * 60 * 5) {
        window.location.reload();
        this._common.storage("lastReloadTimestamp", newTimestamp);
      }
    },
    clearCache() {
      sessionStorage.clear();
      localStorage.clear();
      this.$router.go(0);
    },
    async getServerDateTime() {
      await GetServerDateTime().then((res) => {
        let timeDiff = 0;
        if (this._common.isSuccess(res) && res.data) {
          // 将本地和服务器时间的时间差存到session中
          // 预估api固定响应时间50毫秒
          // Date._localDate()为封装的获取本地时间方法
          timeDiff = new Date(res.data).getTime() + 50 - Date._localDate().getTime();
        }
        this._common.session("timeDiff", timeDiff);
      });
    },
    // 开启多线程 在后台线程中开启定时器
    openTimeWorker() {
      const workerScript = `
        let timer = undefined;
        // 接受主线程发送的消息
        onmessage = (event)=> {
          if(event.data == 'close'){
            // 关闭定时器
            if(timer){
              clearInterval(timer);
            }
            // 关闭自己所属线程
            self.close();
          }
        }
        // 开启定时器
        timer = setInterval(()=> {
          // 随便返回 ，这里只是为了触发主线程的onmessage
          postMessage("child worker!")
        }, 1000);
      `;
      // 从内联JavaScript创建Worker
      const workerScriptBlob = new Blob([workerScript]);
      const workerScriptBlobUrl = URL.createObjectURL(workerScriptBlob);
      // 创建新的线程前先停止旧的线程
      this.stopWorker();
      this.worker = new Worker(workerScriptBlobUrl);
      // 半小时矫正一次服务器时间
      let time = 60 * 30;
      this.worker.onmessage = async (event) => {
        if (this.showCALogin && !this.isLogin) {
          // 每秒扫描ca是否授权
          this.checkCASignInfo();
          this.caQRCodeRefreshTime--;
          if (this.caQRCodeRefreshTime == 0) {
            this.getCAQrCode();
          }
        }
        time--;
        if (time == 0) {
          await this.getServerDateTime();
          time = 60 * 30;
        }
      };
    },
    /**
     * description: 停止线程
     * param {*}
     * return {*}
     */
    stopWorker() {
      if (this.worker) {
        this.worker.postMessage("close");
        this.worker.terminate();
        this.worker = undefined;
      }
    },
    async getHospital() {
      // 1、判断本地缓存有没有医院信息
      if (this.localHospitalInfo && this.localHospitalInfo.hospitalID) {
        // 1、本地缓存有医院信息
        // 1.1、获取服务器最新版本号
        let version = await this.getVersionByHospitalID(this.localHospitalInfo.hospitalID);
        // 1.2、判断本地缓存版本号和服务器版本号是否一致
        // 1.2.2 本地版本号和服务器版本号不一致，获取最新信息并更新本地缓存
        if (version && version != this.localHospitalInfo.systemVersion) {
          await this.getHospitalList(this.localHospitalInfo.hospitalID);
        } else {
          // 1.2.1 本地版本号和服务器版本号一致，直接使用本地缓存
          this.setHospitalInfo(this.localHospitalInfo);
        }
      } else {
        // 2、没有本地缓存，直接调API获取医院信息，并缓存本地
        await this.getHospitalList();
      }
      if (this.localHospitalList && this.localHospitalList.length > 0) {
        this.showSelectHospital = true;
      }
    },
    async getVersionByHospitalID(hospitalID) {
      let version = "";
      let params = {
        hospitalID,
      };
      await GetVersionByHospitalID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          version = result.data;
        }
      });
      return version;
    },
    async getHospitalList(hospitalID) {
      let params = {
        clientType: "1",
      };
      await GetHospitalList(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          if (result.data.length == 1) {
            this.showSelectHospital = false;
            this.setHospitalInfo(result.data[0]);
          } else {
            if (hospitalID) {
              this.changeHospital(hospitalID);
            }
            this.$store.commit("session/setHospitalList", result.data);
          }
        }
      });
    },
    changeHospital(hospitalID) {
      let hospitalInfo = this.localHospitalList.find((hospital) => {
        return hospital.hospitalID == hospitalID;
      });
      if (hospitalInfo) {
        this.setHospitalInfo(hospitalInfo);
      }
      this.getEncryptionSetting();
    },
    setHospitalInfo(hospitalInfo) {
      this.homePageImage = hospitalInfo.homePageImage;
      this.hospitalImage = hospitalInfo.logoImage;
      this.hospitalName = hospitalInfo.hospitalName;
      this.systemVersion = hospitalInfo.systemVersion;
      this.hospitalID = hospitalInfo.hospitalID;
      this.showCALogin = this.hospitalID === "1" && hospitalInfo.singleSignOn;
      if (this.showCALogin) {
        this.getCAQrCode();
      }
      if (this.hospitalID === "5") {
        if (this.hospitalName.includes(" ")) {
          this.hospitalName = this.hospitalName.replace(" ", "<br>");
        }
      }
      this.$store.commit("session/setHospitalInfo", hospitalInfo);
    },
    async getLanguage() {
      if (this.localLanguageList && this.localLanguageList.length > 0) {
        this.languageList = this.localLanguageList;
      } else {
        await GetLanguage().then((result) => {
          if (this._common.isSuccess(result)) {
            if (result.data) {
              this.languageList = [];
              result.data.forEach((language) => {
                this.languageList.push({
                  languageID: language.language,
                  languageName: language.description.trim(),
                  languageLocale: language.typeValue.trim(),
                });
              });
              this.$store.commit("session/setLanguageList", this.languageList);
            }
          }
        });
      }
      // 默认上一次语言,若没有 默认中文简体
      let languageLocale = this._common.storage("language") || "zh";
      let language = this.languageList.find((language) => {
        return language.languageLocale == languageLocale;
      });
      if (language) {
        this.setLanguage(language);
      }
    },
    changeLanguage(languageID) {
      let language = this.languageList.find((language) => {
        return language.languageID == languageID;
      });
      if (language) {
        this.setLanguage(language);
      }
    },
    // 设置当前语言
    setLanguage(language) {
      this.languageID = language.languageID;
      this.$store.commit("session/setLanguage", language.languageLocale);
      this._common.storage("language", language.languageLocale);
      this.$i18n.locale = language.languageLocale;
    },
    /**
     * 获取ca登录二维码
     */
    getCAQrCode() {
      GenCAQrCode({ hospitalID: this.hospitalID }).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.caParams = result.data;
        }
      });
    },
    /**
     * 检核ca授权
     */
    checkCASignInfo() {
      const params = {
        appId: this.caParams.appId,
        bizSn: this.caParams.bizSn,
        msg: this.caParams.msg,
      };
      CheckCASignInfo(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.login(result.data.userID, result.data.password);
        }
      });
    },
    async login(userName, password) {
      if (!this.isAgreeClaimer) {
        return;
      }
      let newUserName = this.userName;
      let newPassword = this.password;
      userName && (newUserName = userName);
      password && (newPassword = password);
      if (!this.hospitalID) {
        this._showTip("warning", this.loginTexts.selectHospitalTip);
        return;
      }
      if (!newUserName || !newPassword) {
        this._showTip("warning", this.loginTexts.passwordErrorTip);
      } else {
        if (this.encryptionFlag) {
          newPassword = encryption(newPassword.trim());
        }
        let checkSuccess = await this.getCALoginCheck(newUserName.trim());
        let params = {
          userId: newUserName.trim(),
          password: newPassword.trim(),
          language: this.languageID,
          clientType: this.clientType,
          hospitalID: this.hospitalID,
          ukeyPassedFlag: checkSuccess,
        };
        this.loading = true;
        await userLogin(params).then(async (result) => {
          this.loading = false;
          if (this._common.isSuccess(result)) {
            //登录成功清空密码，防止浏览器弹出“是否保存密码”的提示
            this.password = "";
            //登录成功
            let hasCA = result.data.hasCA;
            this.$store.commit("auth/login");
            this.$store.commit("auth/setToken", result.data.token);
            this.$store.commit("session/setCurrentPatient", "");
            // 获取session
            await GetSession().then((result) => {
              if (this._common.isSuccess(result)) {
                let user = {
                  roles: result.data.authorityID,
                  userID: result.data.userID,
                  userName: result.data.employeeName,
                  stationID: result.data.stationID,
                  stationCode: result.data.stationCode,
                };
                this.$store.commit("session/setUser", user);
                // 组装登录参数，为系统更新后自动重新登录准备
                this.$store.commit("session/setLoginParams", params);
                if (hasCA && !this.showCALogin) {
                  this.$router.replace({ path: "/caAuthorize", query: { url: this.url } });
                } else {
                  // 页面跳转
                  if (this.url && this.url.length > 0) {
                    this.$router.replace(this.url);
                  } else {
                    this.$router.replace({ path: "/patientList" });
                  }
                }
              }
            });
          }
        });
      }
    },
    //用户名输入框enter切换
    nextFocus(e) {
      this.$nextTick(() => {
        let input = this.$refs.passwordInp;
        if (input) {
          e.target.blur();
          input.focus();
        }
      });
    },
    // 获取CA登录检核
    async getCAUserInfoPromise() {
      return new Promise((resolve, reject) => {
        wp.ca.getCAUserInfo(this.removeSessionAPI, (response) => {
          if (response.data) {
            resolve({
              caUserID: response.data.CAUserID,
              stampImageBase64: response.data.StampImageBase64,
            });
          } else {
            reject(console.log("getCAUserInfo调用异常"));
          }
        });
      });
    },

    // 在 async 函数中使用 await 来确保顺序执行
    async getCALoginCheck(userID) {
      let checkSuccess = false;
      if (!this.checkLoginAndUkeyCAFlag) {
        return checkSuccess;
      }
      try {
        let { caUserID, stampImageBase64 } = await this.getCAUserInfoPromise();
        if (!caUserID || !stampImageBase64) {
          return checkSuccess;
        }
        // 检核比对登录账户与CA账户的一致性
        let checkParams = {
          caUserID: caUserID,
          userID: userID,
        };
        let result = await CheckLoginAndCA(checkParams);
        if (this._common.isSuccess(result)) {
          if (result.data) {
            checkSuccess = result.data;
            if (checkSuccess) {
              // 呼叫同步CA
              let params = {
                CAUserID: caUserID,
                StampImageBase64: stampImageBase64,
              };
              await SyncEmployeeCAData(params);
            }
          }
        }
      } catch (error) {
        console.log("getCALoginCheck调用错误");
      }
      return checkSuccess;
    },
    //获取Ukey拔出时清除登录状态的API
    async getRemoveSessionAPI() {
      let params = {
        settingType: "Configs",
        settingCode: "RemoveSessionAPI",
      };
      await GetAppConfigSetting(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.removeSessionAPI = result.data;
        }
      });
    },
    //该医院是否使用UKey并进行登录检核
    async getCheckLoginAndUkeyCAFlag() {
      let param = {
        SettingTypeCode: "CheckLoginAndUkeyCA",
        HospitalID: this.hospitalID,
      };
      await GetSettingSwitch(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.checkLoginAndUkeyCAFlag = response.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
@import "login.scss";
.login-main {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .background-image {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  /* 遮罩层 */
  .background-mark {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: #000000;
    filter: alpha(opacity=30);
    -moz-opacity: 0.3;
    opacity: 0.3;
  }
  .logo {
    position: relative;
    width: 128px;
    height: 128px;
    border-radius: 128px;
    background-color: #ffffff;
    top: 30px;
    left: 30px;
    border: 8px solid $base-color;
    opacity: 0.98;
    .logo-img {
      position: absolute;
      width: 90px;
      height: 90px;
      left: 19px;
      top: 18px;
      border-radius: 10px;
    }
  }
  .login-left-img-wrap {
    background-color: #ffffff;
    position: absolute;
    width: 1200px;
    height: 410px;
    z-index: 998;
    left: calc(50% - 600px);
    top: calc(50% - 260px);
    box-shadow: #333333 0px 0px 15px;
    border-radius: 30px;
    display: none;
    .login-left-img {
      width: 550px;
      height: 370px;
      margin: 20px;
      border-radius: 30px;
      z-index: 999;
    }
  }
  .login-form {
    background-color: #ffffff;
    position: absolute;
    width: 600px;
    height: 370px;
    z-index: 999;
    box-shadow: #333333 0px 0px 15px;
    border-radius: 6px;
    left: calc(50% - 300px);
    top: calc(50% - 240px);
    .login-top {
      height: 114px;
      border-bottom: 2px solid $base-color;
      text-align: center;
      .hospital-name {
        height: 50px;
        line-height: 60px;
        font-size: 40px;
        color: $base-color;
        letter-spacing: 5px;
        &.is-education {
          color: #ff0000;
          &::after {
            content: "(教学版)";
          }
        }
      }
      .title {
        margin-top: 8px;
        height: 46px;
      }
    }
    .login-text {
      padding: 20px 60px;
      .el-input {
        height: 50px;
        line-height: 50px;
        border: 1px solid #cccccc;
        background-color: #ffffff;
        &.password {
          border-top: 0;
        }
        .el-input-group__prepend {
          height: 50px;
          line-height: 50px;
          border-radius: 0;
          border: 0;
          .iconfont {
            cursor: default;
            margin: 0;
            font-size: 30px;
          }
        }
        .el-input__inner {
          font-size: 30px;
          border: 0px;
          height: 100% !important;
          outline: none;
          padding-left: 20px;
          background-color: #ffffff;
          &:-webkit-autofill {
            box-shadow: 0 0 0 1000px #ffffff inset !important;
          }
        }
      }
    }
    .login-operation {
      padding: 0 60px;
      .login-button {
        width: 100%;
        height: 50px;
        line-height: 50px;
        padding: 0;
        font-size: 26px;
        margin: 0;
        &.is-disabled {
          border-color: #cccccc;
          background-color: #cccccc;
          color: #333333;
          cursor: default;
        }
      }
      .el-select.select-hospital {
        position: absolute;
        bottom: 6px;
        left: 0px;
        width: 460px;
        .el-input__inner {
          font-size: 16px;
          border: 0;
          height: 20px;
          line-height: 20px;
          color: #aaaaaa;
          &::placeholder {
            color: #ff0000;
          }
        }
        .el-input__icon {
          display: none;
        }
      }
      .el-select.language {
        position: absolute;
        bottom: 6px;
        right: 0px;
        width: 130px;
        .el-input__inner,
        .el-input__icon {
          font-size: 14px;
          border: 0;
          height: 20px;
          line-height: 20px;
          color: #aaaaaa;
          @at-root .zh#app & {
            color: $base-color;
          }
          @at-root .en#app & {
            color: #f54d0b;
          }
        }
        .el-input__inner {
          padding: 0 36px 0 0;
          text-align: right;
        }
      }
    }
  }
  .ca-login-qr {
    position: absolute;
    right: calc(50% - 660px);
    top: calc(50% - 240px);
    height: 370px;
    width: 340px;
    background: #ffffff;
    z-index: 998;
    text-align: center;
    img {
      height: 300px;
      width: 340px;
    }
    .ca-text {
      display: block;
      color: #ff0000;
      font-weight: bold;
      margin-top: -10px;
      font-size: 30px;
    }
  }
  .login-claimer {
    position: absolute;
    left: calc(50% - 300px);
    top: calc(50% + 144px);
    width: 600px;
    padding: 5px 5px 5px 10px;
    color: #666666;
    background-color: #ffffff;
    box-shadow: #333333 0px 0px 15px;
    box-sizing: border-box;
    border-radius: 6px;
    font-size: 16px;
    letter-spacing: 1px;
    line-height: 24px;
    opacity: 0.7;
    z-index: 999;
  }
  .system-logo {
    position: absolute;
    width: 84px;
    height: 80px;
    border-radius: 6px;
    background-color: #fff;
    bottom: 50px;
    right: 16px;
    background-image: url("../../../static/images/logo.png");
    background-position: center center;
    background-size: 72px 72px;
    background-repeat: no-repeat;
    opacity: 0.25;
  }
  .footer {
    position: absolute;
    width: 100%;
    height: 42px;
    line-height: 42px;
    color: #ffffff;
    background-color: #000000;
    opacity: 0.5;
    z-index: 999;
    bottom: 0px;
    font-size: 18px;
    div {
      display: inline-block;
      height: 42px;
      line-height: 42px;
      cursor: default;
    }
    .system {
      margin-left: 20px;
    }
    .version {
      margin-left: 6px;
      color: $base-color;
    }
    .footer-right {
      float: right;
      margin-right: 20px;
      a {
        padding-right: 16px;
        color: #ffffff;
        text-decoration: none;
        user-select: none;
        cursor: pointer;
        &:hover {
          color: $base-color;
        }
      }
    }
    span {
      padding-right: 3px;
    }
  }
}
</style>
