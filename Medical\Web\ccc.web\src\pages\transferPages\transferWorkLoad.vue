<!--
 * FilePath     : \src\pages\transferPages\transferWorkLoad.vue
 * Author       : 张现忠
 * Date         : 2021-03-31 10:02
 * LastEditors  : 张现忠
 * LastEditTime : 2021-03-31 10:30
 * Description  : 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getStatisticsUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
      //获取当前登录用户信息
      userInfo: "getUser",
    }),
  },
  created() {
    this.url =
      getStatisticsUrl() +
      "workLoad?token=" +
      this.token +
      "&hospitalID=" +
      this.hospitalInfo.hospitalID +
      "&stationID=" +
      this.userInfo.stationID;
  },
};
</script>
<style lang="scss">
.transfer-statistics {
  height: 100%;
  width: 100%;
}
</style>