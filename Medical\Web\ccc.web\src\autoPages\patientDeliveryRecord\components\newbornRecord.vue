<!--
 * FilePath     : \src\autoPages\patientDeliveryRecord\components\newbornRecord.vue
 * Author       : 杨欣欣
 * Date         : 2023-03-13 16:17
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-12-04 14:56
 * Description  : 新生儿记录
 * CodeIterationRecord: 
-->
<template>
  <base-layout
    class="newborn-record"
    :show-header="newbornViews.length > 1"
    v-loading="loading"
  >
    <div slot="header">
      <el-select v-model="selectedNewBorn" value-key="newBornID">
        <el-option
          v-for="newbornView in newbornViews"
          :key="newbornView.newBornID"
          :label="'第' + newbornView.newBornNum + '胎'"
          :value="newbornView"
        ></el-option>
      </el-select>
    </div>
    <packaging-table
      v-model="showCareMains"
      :headerList="tableHeaders"
      @rowClick="updateCareMain"
    >
      <!-- 操作 插槽-->
      <div slot="operate" slot-scope="scope">
        <el-tooltip content="修改">
          <div
            @click.stop="updateCareMain(scope.row)"
            class="iconfont icon-edit"
          ></div>
        </el-tooltip>
        <el-tooltip content="删除">
          <div
            @click.stop="delCareMain(scope.row)"
            class="iconfont icon-del"
          ></div>
        </el-tooltip>
      </div>
    </packaging-table>
    <!-- 新增抽屉 -->
    <el-drawer
      :title="drawerSetting.title"
      :visible.sync="drawerSetting.showFlag"
      :direction="drawerSetting.direction"
      :size="drawerSize"
      custom-class="care-main-drawer"
      :wrapperClosable="false"
      :modal="false"
      :append-to-body="false"
      @closed="getCareMains"
    >
      <base-layout :showFooter="true">
        <div slot="header">
          <slot name="main-header"></slot>
        </div>
        <slot name="main-tabs-layout"></slot>
        <div slot="footer">
          <slot name="main-footer"></slot>
        </div>
      </base-layout>
    </el-drawer>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import {
  GetPatientDeliveryRecordCareMainViews,
  GetPatientDeliveryRecordNewbornViews,
  DeletePatientDeliveryRecordCareMain
} from "@/api/PatientDeliveryRecord";
export default {
  components: {
    baseLayout,
    packagingTable
  },
  props: {
    mainSaveParams: {
      type: Object,
      required: true,
      default: () => {
        return undefined;
      }
    },
    drawerSize: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      tableHeaders: [],
      newbornViews: [],
      selectedNewBorn: {},
      // 完整的维护记录列表（所有新生儿的维护记录数据）
      fullCareMains: [],
      drawerSetting: {
        title: "",
        showFlag: false,
        direction: this.mainSaveParams.drawerDirection
      }
    };
  },
  computed: {
    parentInstance() {
      return this.$parent?.$parent;
    },
    // 呈现的维护记录列表
    showCareMains() {
      return (
        this.fullCareMains.filter(
          m => m.newBornID === this.selectedNewBorn.newBornID
        ) ?? []
      );
    }
  },
  watch: {
    selectedNewBorn(newVal) {
      this.$emit("setInpatientInfo", newVal);
    }
  },
  async mounted() {
    await this.getTableHeaderList();
  },
  async activated() {
    const returnValues = {
      showAddBtn: true
    };
    this.$emit("pageActivated", returnValues);
    await this.getNewbornRecords();
    await this.getCareMains();
  },
  methods: {
    /**
     * description: 获取动态表头
     * return {*}
     * param {*}
     */
    async getTableHeaderList() {
      let params = {
        fileClassID: 560,
        fileClassSub: "DeliveryNewbornMaintain",
        useDescription: "1||Table",
        newSourceFlag: true
      };
      this.tableHeaders = [];
      await GetCareMainTableHeader(params).then(res => {
        if (this._common.isSuccess(res)) {
          this.tableHeaders = res.data;
        }
      });
    },
    /**
     * description: 新增前检核，无新生儿信息不可新增新生儿记录
     * return {*}
     */
    addCheck() {
      if (!this.newbornViews.length) {
        this._showTip("warning", "无新生儿信息，不可新增新生儿记录！");
        return false;
      }
      return true;
    },
    /**
     * description: 获取新生儿评估数据,用于切换选中的新生儿
     * return {*}
     * param {*}
     */
    getNewbornRecords() {
      if (this.mainSaveParams.recordID.includes("temp")) {
        this.newbornViews = [];
        return;
      }
      let params = {
        recordID: this.mainSaveParams.recordID
      };
      this.loading = true;
      GetPatientDeliveryRecordNewbornViews(params).then(res => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.newbornViews = res.data;
          if (this.newbornViews.length) {
            this.selectedNewBorn = this.newbornViews[0];
          }
        }
      });
    },
    /**
     * description: 获取维护数据
     * return {*}
     */
    async getCareMains() {
      if (this.mainSaveParams.recordID.includes("temp")) {
        this.fullCareMains = [];
        return;
      }
      let params = {
        recordID: this.mainSaveParams.recordID,
        recordsCode: this.mainSaveParams.recordsCode
      };
      this.loading = true;
      await GetPatientDeliveryRecordCareMainViews(params).then(res => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.fullCareMains = res.data;
        }
      });
    },
    /**
     * description: 更新维护记录
     * param {*} row 当前行
     * return {*}
     */
    async updateCareMain(row) {
      if (this.parentInstance && this.parentInstance.addOrModifyCareMain) {
        await this.parentInstance?.addOrModifyCareMain(row);
      }
    },
    /**
     * description: 删除维护
     * param {*} row 当前行
     * return {*}
     */
    async delCareMain(row) {
      this._deleteConfirm("", async flag => {
        if (flag) {
          this.loading = true;
          let params = {
            careMainID: row.patientDeliveryCareMainID
          };
          await DeletePatientDeliveryRecordCareMain(params).then(res => {
            this.loading = false;
            if (this._common.isSuccess(res)) {
              // 刷新主记录
              this.$emit("refreshCurrentRecord", this.mainSaveParams.recordID);
              this._showTip("success", "删除成功");
            }
          });
          await this.getCareMains();
        }
      });
    },
    /**
     * description: 专项护理弹窗开关函数
     * return {*}
     * param {*} flag 弹框开关，type 新增/修改
     */
    openOrCloseDrawer(flag, type) {
      this.drawerSetting.showFlag = flag;
      if (flag) {
        this.drawerSetting.title = `第${this.selectedNewBorn.newBornNum}胎 -- 新生儿评估${type}`;
      }
    }
  }
};
</script>

<style lang="scss">
.newborn-record {
  .care-main-drawer {
    .base-content {
      overflow: hidden;
    }
    .base-footer {
      display: flex;
      justify-content: flex-end;
      bottom: 5px;
      background-color: #fff;
      .bring-checkbox {
        margin-right: 15px;
      }
    }
  }
}
</style>
