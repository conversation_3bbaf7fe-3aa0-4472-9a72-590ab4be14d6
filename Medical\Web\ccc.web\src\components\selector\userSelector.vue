<template>
  <div class="user-selector">
    <span class="label">{{ label }}</span>
    <el-select
      v-model="selected"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      remote
      :remote-method="filterData"
      :loading="loading"
      :allow-create="createFlag"
      placeholder="请选择人员"
      @clear="clear"
      @change="changeValue"
      :style="style"
      popper-class="user-popper"
    >
      <el-option
        v-for="(user, index) in showList"
        :key="index"
        :class="{ 'same-name': user.sameName }"
        :label="user.userName"
        :value="user.userID"
      >
        <div>
          <span class="el-option-left">{{ user.userName }}</span>
          <span class="el-option-right">{{ user.userID }}</span>
        </div>
        <div class="el-option-station-name" v-if="user.sameName">{{ user.stationName }}</div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { GetUserByStationID, GetUserByKeyword } from "@/api/User";
import { mapGetters } from "vuex";
export default {
  props: {
    label: {
      type: String,
      default: "人员：",
    },
    value: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    stationID: {
      type: Number,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    remoteSearch: {
      type: Boolean,
      default: false,
    },
    allowCreate: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: "120px",
    },
    sessionUser: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    stationID: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          if (!this.disabled && this.selected) {
            this.$emit("input", undefined);
          }
          this.innerStationID = this.stationID;
          this.init();
        }
      },
    },
    value: {
      immediate: true,
      handler(newVal) {
        this.selected = newVal;
        if (newVal) {
          this.filterData(newVal);
        }
      },
    },
  },
  data() {
    return {
      selected: "",
      userList: undefined,
      loading: false,
      showList: undefined,
      innerStationID: undefined,
      createFlag: false,
    };
  },
  computed: {
    ...mapGetters({
      patient: "getCurrentPatient",
      user: "getUser",
      stationNurseList: "getStationNurseList",
    }),
    style() {
      return {
        width: this._convertUtil.getHeigt(this.width, true),
      };
    },
  },
  created() {
    this.createFlag = this.allowCreate;
    if (this.user && this.user.stationID) {
      this.innerStationID = this.user.stationID;
    }
    if (this.patient && this.patient.stationID) {
      this.innerStationID = this.patient.stationID;
    }
    if (this.stationID) {
      this.innerStationID = this.stationID;
    }
    if (this.innerStationID) {
      this.init();
    }
    if (this.sessionUser && this.user) {
      this.selected = this.user.userName;
      this.changeValue(this.user.userID);
    }
  },
  methods: {
    init() {
      let key = this.innerStationID;
      // 如果缓存有，直接取
      if (this.stationNurseList[key]) {
        this.userList = this.stationNurseList[key];
        if (!this.selected) {
          this.showList = this.userList;
        }
        return;
      }
      let params = {
        stationID: this.innerStationID,
        // 为了解决首次同时加载多个此组件时，部分组件下拉框数据不显示的异常
        index: Math.random(),
      };
      GetUserByStationID(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.userList = result.data;
          let newStationNurseList = this.stationNurseList;
          newStationNurseList[key] = this.userList;
          this.$store.commit("session/setStationNurseList", newStationNurseList);
          if (!this.selected) {
            this.showList = this.userList;
          }
        } else {
          this.userList = [];
        }
      });
    },
    filterData(keyword) {
      if (!keyword) {
        this.showList = this.userList;
        return;
      }
      if (this.userList) {
        // 先过滤本地的用户名或工号
        this.showList = this.userList.filter((user) => {
          return (
            user.userName.toLowerCase().indexOf(keyword.toLowerCase()) != -1 ||
            user.userID.toLowerCase().indexOf(keyword.toLowerCase()) != -1
          );
        });
        // 如果有候选项，不允许创建新项目
        if (this.showList?.length) {
          this.createFlag = false;
          return;
        }
      }
      // 如果开启远程搜索，读API过滤
      if (this.remoteSearch) {
        this.loading = true;
        let params = {
          keyword: keyword,
          index: Math.random(),
        };
        GetUserByKeyword(params).then((result) => {
          this.loading = false;
          if (this._common.isSuccess(result)) {
            this.showList = result.data;
            // 如果有候选项，不允许创建新项目，反之这依据参数决定是否允许创建新项目
            if (this.showList?.length) {
              this.createFlag = false;
            } else {
              this.createFlag = this.allowCreate;
            }
          }
        });
      }
    },
    changeValue(userID) {
      // 实现双向绑定
      this.$emit("input", userID);
      this.$emit("select", userID);
      if (userID) {
        let user = this.userList.find((user) => {
          return user.userID == userID;
        });
        if (user) {
          this.$emit("select-user", user);
        }
      } else {
        this.$emit("select-user", undefined);
      }
    },
    clear() {
      this.selected = undefined;
      this.$emit("input", undefined);
      this.$emit("select", undefined);
      this.$emit("select-user", undefined);
      this.showList = this.userList;
    },
  },
};
</script>
<style lang="scss">
.user-selector {
  display: inline-block;
  .label {
    margin-left: 5px;
  }
  .el-select {
    .el-input.is-disabled {
      .el-input__inner {
        color: #606266;
      }
    }
    .el-input__inner {
      padding-left: 5px;
      height: 26px;
      line-height: 26px;
    }
  }
}
.user-popper.el-popper {
  .same-name.el-select-dropdown__item {
    height: auto;
    display: flex;
    flex-direction: column;
  }
  .el-option-left {
    float: left;
  }
  .el-option-right {
    float: right;
    color: #8492a6;
    font-size: 12px;
    margin-left: 10px;
  }
  .el-option-station-name {
    height: 10px;
    line-height: 10px;
    font-size: 12px;
    margin-bottom: 3px;
    color: #8492a6;
    text-align: right;
  }
  .el-select-dropdown__item.selected,
  .el-select-dropdown__item.hover {
    .el-option-station-name,
    .el-option-right {
      color: #ffe4b5;
    }
  }
}
</style>
