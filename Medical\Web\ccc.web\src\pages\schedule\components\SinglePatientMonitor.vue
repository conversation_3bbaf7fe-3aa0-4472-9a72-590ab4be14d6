<!--
 * FilePath     : \src\pages\schedule\components\SinglePatientMonitor.vue
 * Author       : 苏军志
 * Date         : 2020-03-31 17:38
 * LastEditors  : 张现忠
 * LastEditTime : 2025-01-06 16:12
 * Description  : 单病人监测排程表
 * CodeIterationRecord: 
 2744-作为IT人员，我需要排程执行疼痛及批量执行，可以跳转专项护理 2022-07-07 杨欣欣
 2789-作为护理人员，我需要批量监测有通知医师选项，以利护理记录完整 2022-07-18 En
 2869-导管跳转IO改版 2022-12-01 杨欣欣
-->
<template>
  <base-layout class="single-patient-monitor">
    <div slot="header">
      <span>班别日期：{{ params.scheduleDate }}</span>
      <span class="shift">班别：{{ params.shiftName }}</span>
      <el-button type="primary" class="save-button" icon="iconfont icon-save-button" @click="saveAllData">
        保存
      </el-button>
    </div>
    <div slot-scope="layout" class="single-patient-monitor-content" :style="{ height: layout.height + 'px' }">
      <u-table
        ref="monitorData"
        class="monitor-data"
        :data="monitorData"
        border
        stripe
        :height="layout.height"
        :row-height="38"
        use-virtual
        v-loading="loading"
        :element-loading-text="loadingText"
        @selection-change="selectItem"
      >
        <u-table-column
          :selectable="checkCheckBox"
          type="selection"
          :width="convertPX(50)"
          align="center"
          fixed
        ></u-table-column>
        <!-- 排程时间 -->
        <u-table-column
          :label="timeColumn.title"
          :name="timeColumn.name"
          :min-width="convertPX(timeColumn.width)"
          align="center"
          fixed="left"
        >
          <template slot-scope="monitor">
            <div class="title-div">{{ monitor.row[timeColumn.index] }}</div>
          </template>
        </u-table-column>
        <template v-for="(col, index) in dataColumns">
          <!-- 数据列 -->
          <u-table-column
            :key="index"
            :label="col.title"
            :name="col.name"
            class-name="monitor-column"
            :min-width="convertPX(col.width)"
            v-if="!col.hiddenFlag"
            align="center"
          >
            <template slot="header" slot-scope="scope">
              <span v-html="scope.column.label"></span>
            </template>
            <!-- 有子列 -->
            <template v-if="col.hasChild && col.childColumns.length > 0">
              <template v-for="(childCol, cIndex) in col.childColumns">
                <u-table-column
                  :key="cIndex"
                  :label="childCol.title"
                  :name="childCol.name"
                  align="center"
                  :min-width="convertPX(childCol.width)"
                  v-if="!childCol.hiddenFlag"
                >
                  <template slot="header" slot-scope="scope">
                    <span v-html="scope.column.label"></span>
                  </template>
                  <template slot-scope="monitor">
                    <div v-if="monitor.row[childCol.index]" class="input-wrap">
                      <el-input
                        v-if="monitor.row[childCol.index].style == 'TN' && monitor.row[childCol.index].formula"
                        v-formula="{
                          item: monitor.row[childCol.index],
                          items: monitor.row,
                        }"
                        name="TN"
                        v-model="monitor.row[childCol.index].assessValue"
                        @click.native="showKeyBoard($event)"
                        @input="checkInputValue(monitor.row[childCol.index])"
                        :class="monitor.row[childCol.index].assessValueColor"
                        :readonly="isReadOnly"
                        v-direction="{ x: childCol.index, y: monitor.$index }"
                      ></el-input>
                      <el-input
                        v-if="monitor.row[childCol.index].style == 'TN' && !monitor.row[childCol.index].formula"
                        name="TN"
                        v-model="monitor.row[childCol.index].assessValue"
                        @change="check(monitor.row[childCol.index], monitor.row)"
                        @click.native="showKeyBoard($event)"
                        @input="checkInputValue(monitor.row[childCol.index])"
                        :class="monitor.row[childCol.index].assessValueColor"
                        :readonly="isReadOnly"
                        v-direction="{ x: childCol.index, y: monitor.$index }"
                      ></el-input>
                      <el-input
                        v-if="monitor.row[childCol.index].style == 'T'"
                        v-model="monitor.row[childCol.index].assessValue"
                        @blur="changeValue(monitor.row[childCol.index])"
                        v-direction="{ x: childCol.index, y: monitor.$index }"
                      ></el-input>
                      <el-select
                        v-if="monitor.row[childCol.index].style == 'D'"
                        v-model="monitor.row[childCol.index].assessValue"
                        placeholder="请选择"
                        clearable
                        @change="toggleSelection([monitor.row], checkCheckBox(monitor.row))"
                      >
                        <el-option
                          v-for="(opt, index) in monitor.row[childCol.index].radioOpt"
                          :key="index"
                          :label="opt.showName"
                          :value="String(opt.interventionDetailID)"
                        ></el-option>
                      </el-select>
                      <el-checkbox
                        v-if="monitor.row[childCol.index].style == 'C' && !monitor.row[childCol.index].formula"
                        :checked="monitor.row[childCol.index].assessValue ? true : false"
                        @change="changeInputValue($event, monitor.row[childCol.index])"
                      ></el-checkbox>
                      <!-- 有隐藏监测列时显示 -->
                      <template v-if="childCol.hiddenIndex">
                        <span
                          v-show="false"
                          v-formula="{
                            item: monitor.row[childCol.hiddenIndex],
                            items: monitor.row,
                            formulaItem: monitor.row[childCol.index],
                          }"
                        >
                          {{ monitor.row[childCol.hiddenIndex].assessValue }}
                        </span>
                      </template>
                    </div>
                  </template>
                </u-table-column>
              </template>
            </template>
            <template slot-scope="monitor">
              <div v-if="monitor.row[col.index]" class="input-wrap">
                <el-input
                  v-if="monitor.row[col.index].style == 'TN' && monitor.row[col.index].formula"
                  v-formula="{ item: monitor.row[col.index], items: monitor.row }"
                  name="TN"
                  v-model="monitor.row[col.index].assessValue"
                  @change="check(monitor.row[col.index], monitor.row)"
                  @click.native="showKeyBoard($event)"
                  @input="checkInputValue(monitor.row[col.index])"
                  :class="monitor.row[col.index].assessValueColor"
                  :readonly="isReadOnly"
                  v-direction="{ x: col.index, y: monitor.$index }"
                ></el-input>
                <el-input
                  v-if="monitor.row[col.index].style == 'TN' && !monitor.row[col.index].formula && !isCopy"
                  name="TN"
                  v-model="monitor.row[col.index].assessValue"
                  v-select-linkage="{ row: monitor.row, index: col.index }"
                  @change="check(monitor.row[col.index], monitor.row)"
                  @click.native="showKeyBoard($event)"
                  @input="checkInputValue(monitor.row[col.index])"
                  :class="monitor.row[col.index].assessValueColor"
                  :readonly="isReadOnly"
                  v-direction="{ x: col.index, y: monitor.$index }"
                ></el-input>
                <!-- 解决因为自定义指令v-select-linkage 无法复制测温方式 -->
                <el-input
                  v-if="monitor.row[col.index].style == 'TN' && !monitor.row[col.index].formula && isCopy"
                  name="TN"
                  v-model="monitor.row[col.index].assessValue"
                  @change="check(monitor.row[col.index], monitor.row)"
                  @click.native="showKeyBoard($event)"
                  @input="checkInputValue(monitor.row[col.index])"
                  :class="monitor.row[col.index].assessValueColor"
                  :readonly="isReadOnly"
                  v-direction="{ x: col.index, y: monitor.$index }"
                ></el-input>
                <el-checkbox
                  v-if="
                    monitor.row[col.index].style == 'TN' &&
                    !monitor.row[col.index].formula &&
                    monitor.row[col.index].assessListID == '1295' &&
                    monitor.row[col.hiddenIndex && monitor.row[col.hiddenIndex] ? col.hiddenIndex : col.index].showFlag
                  "
                  :checked="
                    monitor.row[col.hiddenIndex && monitor.row[col.hiddenIndex] ? col.hiddenIndex : col.index]
                      .temperatureStatus == 2
                  "
                  @input="
                    checkInputValue(
                      monitor.row[col.hiddenIndex && monitor.row[col.hiddenIndex] ? col.hiddenIndex : col.index],
                      col.hiddenIndex && monitor.row[col.hiddenIndex] ? monitor.row[col.index] : undefined
                    )
                  "
                  @change="
                    changeInputValue(
                      $event,
                      monitor.row[col.hiddenIndex && monitor.row[col.hiddenIndex] ? col.hiddenIndex : col.index],
                      col.hiddenIndex && monitor.row[col.hiddenIndex] ? monitor.row[col.index] : undefined
                    )
                  "
                ></el-checkbox>
                <!-- 有隐藏监测列时显示 -->
                <template v-if="col.hiddenIndex">
                  <span
                    v-show="false"
                    v-formula="{
                      item: monitor.row[col.hiddenIndex],
                      items: monitor.row,
                      formulaItem: monitor.row[col.index],
                    }"
                  >
                    {{ monitor.row[col.hiddenIndex].assessValue }}
                  </span>
                </template>
                <el-input
                  v-if="monitor.row[col.index].style == 'T'"
                  v-model="monitor.row[col.index].assessValue"
                  v-direction="{ x: col.index, y: monitor.$index }"
                ></el-input>
                <el-select
                  v-if="monitor.row[col.index].style == 'D'"
                  v-model="monitor.row[col.index].assessValue"
                  placeholder="请选择"
                  clearable
                  :disabled="monitor.row[col.index].disabled"
                  @change="toggleSelection([monitor.row], checkCheckBox(monitor.row))"
                >
                  <el-option
                    v-for="(opt, index) in monitor.row[col.index].radioOpt"
                    :key="index"
                    :label="opt.showName"
                    :value="String(opt.interventionDetailID)"
                  ></el-option>
                </el-select>
                <el-checkbox
                  v-if="monitor.row[col.index].style == 'C' && !monitor.row[col.index].formula"
                  :checked="monitor.row[col.index].assessValue ? true : false"
                  @change="changeInputValue($event, monitor.row[col.index])"
                ></el-checkbox>
                <el-button
                  v-if="monitor.row[col.index].style == 'B' && !monitor.row[col.index].assessValue"
                  size="mini"
                  type="primary"
                  @click="openButtonDialog(monitor.row, col.index)"
                >
                  {{ formatButtonShowName(monitor.row[col.index].showName) }}
                </el-button>
                <el-badge
                  v-if="monitor.row[col.index].style == 'B' && monitor.row[col.index].assessValue"
                  :class="[
                    'badge-item',
                    {
                      'is-string': !/^\d+$/.test(monitor.row[col.index].assessValue),
                    },
                  ]"
                  :is-dot="monitor.row[col.index].assessValue != ''"
                >
                  <el-button size="mini" type="primary" @click="openButtonDialog(monitor.row, col.index)">
                    {{ formatButtonShowName(monitor.row[col.index].showName) }}
                  </el-button>
                </el-badge>
              </div>
            </template>
          </u-table-column>
        </template>

        <!-- 执行日期列 -->
        <u-table-column
          :label="performDateColumn.title"
          :name="performDateColumn.name"
          class-name="date"
          :width="convertPX(performDateColumn.width)"
          align="center"
          fixed="right"
        >
          <template slot-scope="monitor">
            <el-date-picker
              v-model="monitor.row[performDateColumn.index].value"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              :class="monitor.row[optColumn.index].hasValue == '1' ? 'saved' : 'no-save'"
              @change="
                checkTime(monitor.row[performDateColumn.index].value, monitor.row[performTimeColumn.index].value)
              "
            ></el-date-picker>
          </template>
        </u-table-column>
        <!-- 执行时间列 -->
        <u-table-column
          :label="performTimeColumn.title"
          :name="performTimeColumn.name"
          class-name="date"
          :width="convertPX(performTimeColumn.width)"
          align="center"
          fixed="right"
        >
          <template slot-scope="monitor">
            <el-time-picker
              v-model="monitor.row[performTimeColumn.index].value"
              value-format="HH:mm"
              format="HH:mm"
              :class="monitor.row[optColumn.index].hasValue == '1' ? 'saved' : 'no-save'"
              @change="
                checkTime(monitor.row[performDateColumn.index].value, monitor.row[performTimeColumn.index].value)
              "
            ></el-time-picker>
          </template>
        </u-table-column>
        <!-- 通知医师列 -->
        <u-table-column
          :label="informPhysicianColumn.title"
          :name="informPhysicianColumn.name"
          :width="convertPX(50)"
          align="center"
          fixed="right"
        >
          <template slot-scope="monitor">
            <div>
              <el-checkbox
                :value="monitor.row[informPhysicianColumn.index].value == '1'"
                @change="changeOperateColumn(monitor.row[informPhysicianColumn.index])"
              />
            </div>
          </template>
        </u-table-column>
        <!-- 带入交班列 -->
        <u-table-column
          :label="bringToShiftColumn.title"
          :name="bringToShiftColumn.name"
          :width="convertPX(50)"
          align="center"
          fixed="right"
        >
          <template slot-scope="monitor">
            <div>
              <el-checkbox
                :value="monitor.row[bringToShiftColumn.index].value == '1'"
                @change="changeOperateColumn(monitor.row[bringToShiftColumn.index])"
              />
            </div>
          </template>
        </u-table-column>
        <!-- 操作列 -->
        <u-table-column
          :label="optColumn.title"
          :name="optColumn.name"
          class-name="opt-column"
          :width="convertPX(isBatchCopyFlag ? 200 : 104)"
          align="center"
          fixed="right"
        >
          <template slot-scope="monitor" v-if="monitor.row[optColumn.index].hasButton == '1'">
            <div class="opt" v-if="isBatchCopyFlag">
              <el-tooltip content="复制">
                <i class="iconfont icon-copy" @click="copy(monitor.row, monitor.$index)"></i>
              </el-tooltip>
            </div>
            <div class="opt" v-if="isBatchCopyFlag">
              <el-tooltip content="粘贴" v-if="copyIndex !== monitor.$index">
                <i class="iconfont icon-paste" @click="paste(monitor.row)"></i>
              </el-tooltip>
            </div>
            <div class="opt">
              <el-tooltip content="清除">
                <i class="iconfont icon-clear" @click="clear(monitor.row, monitor.$index)"></i>
              </el-tooltip>
            </div>
            <div class="opt">
              <el-tooltip content="仪器数据">
                <i class="iconfont icon-clinic1" @click="showClinicDialog(monitor.row)"></i>
              </el-tooltip>
            </div>
          </template>
        </u-table-column>
      </u-table>
      <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
      <key-board
        v-tobody="{ id: 'key-board' }"
        :show="isShowKeyBoard"
        :output="el"
        typeName="TN"
        @hide="hideKeyBoard"
      ></key-board>
      <el-dialog
        custom-class="single-patient-clinic-dialog"
        :title="dialogTitle"
        :append-to-body="true"
        :visible.sync="dialogVisible"
        width="70%"
        v-if="dialogVisible"
      >
        <clinic-view
          v-model="timeRange"
          :clinicPrams="clinicPrams"
          @getSelectClinicData="getSelectClinicData"
        ></clinic-view>
        <div slot="footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveClinicData()">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 按钮弹出框 -->
      <el-dialog
        :title="buttonName"
        :close-on-click-modal="false"
        :visible.sync="showButtonDialog"
        fullscreen
        custom-class="no-footer specific-care-view"
        append-to-body
      >
        <iframe ref="buttonDialog" width="100%" height="100%"></iframe>
      </el-dialog>
    </div>
  </base-layout>
</template>

<script>
import { GetBatchMonitorByPatientID, GetButtonData, SaveMultiSchedule } from "@/api/PatientSchedule";
import { GetOneSettingByTypeAndCode } from "@/api/Setting";
import baseLayout from "@/components/BaseLayout";
import keyBoard from "@/components/KeyBoard/KeyBoard";
import progressView from "@/components/progressView";
import { mapGetters } from "vuex";
import clinicView from "./scheduleTypes/clinicView";
import monitorView from "./scheduleTypes/monitorView";
export default {
  components: {
    keyBoard,
    monitorView,
    baseLayout,
    clinicView,
    progressView,
  },
  directives: {
    // 自定义指令，处理计算公式的结果
    formula: {
      /**
       * description: 页面异动执行
       * param {*} el
       * param {*} binding
       * param {*} vnode
       * return {*}
       */
      componentUpdated(el, binding, vnode) {
        binding.def.formulaMethods(el, binding, vnode);
      },
      /**
       * description: 初始化执行
       * param {*} el
       * param {*} binding
       * param {*} vnode
       * return {*}
       */
      inserted(el, binding, vnode) {
        binding.def.formulaMethods(el, binding, vnode);
      },
      /**
       * description: 组件更新时计算，实现类似v-model的功能
       * return {*}
       * param {*} el
       * param {*} binding
       * param {*} vnode
       */
      formulaMethods(el, binding, vnode) {
        let formula = binding.value.item.formula;
        if (!formula) {
          return;
        }
        let params = formula.params;
        let expression = formula.expression;
        let formulaItem = binding.value.formulaItem;
        for (let param of params) {
          for (let key in binding.value.items) {
            let item = binding.value.items[key];
            if (item && item.constructor === Object) {
              let value = 0;
              //处理下拉框公式计算
              if (item.style == "D" && item?.radioOpt?.length) {
                let isParamsRadioOpt = item?.radioOpt.find((option) => option.assessListID == param.id);
                if (isParamsRadioOpt) {
                  if (item.assessValue && item.assessValue == isParamsRadioOpt.interventionDetailID) {
                    value = isParamsRadioOpt?.linkForm.trim();
                  }
                  expression = expression.replace(param.key, value);
                }
                if (item.assessValue) {
                  let successRadioOpt = item?.radioOpt.find(
                    (option) => option.interventionDetailID == item.assessValue
                  );
                  if (successRadioOpt && successRadioOpt.assessListID == param.id) {
                    value = successRadioOpt.linkForm.trim() || 0;
                    expression = expression.replace(param.key, value);
                  }
                }
              }
              if (item.assessListID == param.id) {
                value = item.assessValue.trim();
                expression = expression.replace(param.key, value);
              }
            }
          }
        }
        if (expression.indexOf("}") > 0) {
          binding.value.item.assessValue = "";
        } else {
          let newValue = "";
          //解决eval(expression)中expression不规范导致报错
          try {
            newValue =
              vnode.context._decimalUtil.decimalRound(eval(expression), formula.decimalRule, formula.decimalValue) + "";
          } catch (error) {
            newValue = "";
          }
          // 防止循环刷新
          if (binding.value.item.assessValue == newValue) {
            return;
          } else {
            binding.value.item.assessValue = newValue;
            //隐藏复测勾选框联动
            formulaItem && vnode.context.checkInputValue(binding.value.item, formulaItem);
          }
        }
      },
    },
    // 各监测数据联动处理
    "select-linkage": {
      defaultSetting: new Map([
        ["1295", "4701"],
        ["1299", "6633240"],
      ]),
      // 缓存默认内容
      inserted(el, binding) {
        let selectItem = binding.def.getSelectItem(binding);
        el.dataset.selectDefaultValue = selectItem?.assessValue ?? undefined;
      },
      componentUpdated(el, binding, vnode) {
        let _this = vnode.context;
        const { row, index } = binding.value;
        const bindItem = row[index];
        let selectItem = binding.def.getSelectItem(binding);
        if (!selectItem) {
          return;
        }
        /**
         * 由于修改表格中的元素会导致父组件更新而触发钩子函数，导致测温方式一直赋值默认值而无法修改
         * 所以增加判断，如果体温未发生变化，则测温方式的对象值保持现状。
         * Tips：不可使用binding.oldValue，因为父组件更新后，oldValue和value的值是一样的
         **/
        if (el.dataset.oldValue && el.dataset.oldValue == bindItem.assessValue) {
          return;
        }
        // 查出测温方式dom，设置为disabled，并且清空值
        // 体温是否有输入决定测温方式下拉项是否弃用
        _this.$set(selectItem, "assessValue", bindItem.assessValue ? el.dataset.selectDefaultValue : "");
        selectItem.disabled = !bindItem.assessValue;
        // 将本次修改的体温值作为旧值缓存起来，方便下次比对使用
        el.dataset.oldValue = bindItem.assessValue;
      },
      // 检核并且得到对应下拉框数据
      getSelectItem(binding) {
        const { row, index } = binding.value;
        const bindItem = row[index];
        // 判断是否符合下拉框联动条件
        if (!binding.def.defaultSetting.has(bindItem?.assessListID)) {
          return;
        }
        // 判断是否找到对应下拉框
        let selectItem = Object.values(row).find(
          (item) => item?.assessListID == binding.def.defaultSetting.get(bindItem?.assessListID)
        );
        return selectItem;
      },
    },
  },
  props: {
    params: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      loadingText: "加载中……",
      loading: false,
      isShowKeyBoard: false,
      isReadOnly: false,
      el: undefined,
      timeColumn: [],
      performDateColumn: {},
      performTimeColumn: {},
      optColumn: {},
      dataColumns: {},
      monitorData: [],
      copyRow: undefined,
      copyIndex: undefined,
      //点击复制为 true 粘贴完成为false
      isCopy: false,
      //获取仪器数据所需params
      clinicPrams: {},
      //选中监测数据
      selectMonitorData: {},
      //选中仪器数据
      selectClinicData: {},
      //弹窗标题
      dialogTitle: "",
      dialogVisible: false,
      clinicLoadingFlag: false,
      timeRange: 30,
      // 是否开启复制粘贴功能
      isBatchCopyFlag: true,
      //複製項目
      copyID: [],
      //勾选数据
      allData: [],
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      //进度条开关
      progressFlag: false,
      //按钮弹窗开关
      showButtonDialog: false,
      //按钮弹窗名称
      buttonName: "",
      //点击按钮后，缓存次单元格及此行的部分字段，用于数据更新
      buttonClickRow: {
        rowData: undefined,
        //打开按钮弹窗的行下标
        painColIndex: undefined,
        //打开按钮窗口的所属行排程ID
        rowScheduleMainID: "",
        buttonInterventionDetailID: undefined,
      },
      painScoreThreshold: undefined,
      //通知医师列
      informPhysicianColumn: {},
      //带入交班列
      bringToShiftColumn: {},
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getCurrentPatient",
      token: "getToken",
    }),
  },
  watch: {
    params: {
      immediate: true,
      handler(newValue) {
        this.copyIndex = undefined;
        this.copyRow = undefined;
        this.init();
      },
    },
    showButtonDialog(newVal) {
      if (!newVal) {
        this.updateButton();
      }
    },
  },
  mounted() {
    this.getPainScoreSetting();
    this.initCopy();
  },
  created() {
    //添加输入框上下左右功能
    this._direction(this);
  },
  methods: {
    //初始化函数
    async initCopy() {
      await this.getTableBatchCopyFlag();
    },
    async getTableBatchCopyFlag() {
      // 默认为true
      let param = { settingType: 126, settingCode: "BatchCopyFlag" };
      await GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.typeValue == "False") {
            this.isBatchCopyFlag = false;
          }
        }
      });
    },
    showKeyBoard(e) {
      if (this._common.isPC()) {
        this.isReadOnly = false;
        return;
      }
      this.isReadOnly = true;
      this.el = e.target;
      this.isShowKeyBoard = true;
    },
    hideKeyBoard() {
      this.el = undefined;
      this.isShowKeyBoard = false;
    },
    init() {
      this.dataColumns = [];
      this.monitorData = [];
      this.copyID = [];
      this.bringToShiftColumn = {};
      this.informPhysicianColumn = {};
      if (!this.params && !this.params.inpatientID) {
        return;
      }
      this.loading = true;
      this.loadingText = "加载中……";
      let params = {
        inpatientID: this.params.inpatientID,
        scheduleDate: this.params.scheduleDate,
        shiftID: this.params.shiftID,
      };
      GetBatchMonitorByPatientID(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result) && result.data) {
          this.timeColumn = result.data.columns[0];
          this.optColumn = result.data.columns[result.data.columns.length - 1];
          this.bringToShiftColumn = result.data.columns[result.data.columns.length - 2];
          this.informPhysicianColumn = result.data.columns[result.data.columns.length - 3];
          this.performTimeColumn = result.data.columns[result.data.columns.length - 4];
          this.performDateColumn = result.data.columns[result.data.columns.length - 5];
          this.dataColumns = result.data.columns;
          this.copyID = result.data.copyID;
          // 删除第一列和最后五列
          this.dataColumns.shift();
          this.dataColumns.pop();
          this.dataColumns.pop();
          this.dataColumns.pop();
          this.dataColumns.pop();
          this.dataColumns.pop();
          this.monitorData = result.data.rows;
          this.addassessValueColor(this.monitorData);
          this.$nextTick(() => {
            this.$refs.monitorData.doLayout();
          });
        }
      });
    },
    /**
     * description: 数据验证
     * params: 检核项目
     * return: bool
     */
    check(item, row) {
      if (item.assessValue || item.assessValue + "" === "0") {
        let checkTN = {
          controlerType: item.style,
          itemName: item.showName,
          assessValue: item.assessValue,
          decimal: item.decimal,
          upError: item.upError,
          lowError: item.lowError,
          checkLevelDict: item.checkLevelDict,
        };
        // 检核疼痛阈值  配置来自ClinicSetting 特殊处理
        if (item.assessListID == "1299" && item.assessValue >= this.painScoreThreshold) {
          this._showTip("warning", "疼痛评分>=" + this.painScoreThreshold + "分，请到专项护理填写");
          item.assessValue = undefined;
          return false;
        }
        // 检核TN条件，不符合检核，清空排程数据
        let result = this._common.checkAssessTN(checkTN);
        item.assessValue = result.value;
        if (!result.flag) {
          return false;
        }
      }

      if (row && this.checkCheckBox(row)) {
        let isEdit = row[Object.keys(row).length - 1].hasValue ? true : false;
        // 修改，后端需要做处理
        if (isEdit) {
          this.toggleSelection([row], true);
          return;
        }
        let hasValue = false;
        // 非修改，判断若没有有效的列就取消勾选
        for (let index in row) {
          let item = row[index];
          // 排除第一列、日期列、时间列、操作列
          if (index == 0 || !item.assessListID) {
            continue;
          }
          if (item.assessValue) {
            hasValue = true;
          }
        }
        this.toggleSelection([row], hasValue);
      }
    },
    /**
     * description: 检核执行时间是否超过当前时间
     * param {*} performDate 执行日期
     * param {*} performTime 执行时间
     * return {*}
     */
    checkTime(performDate, performTime) {
      let nowData = this._datetimeUtil.getNowDate();
      // 对传过来的参数进行格式化
      performDate = this._datetimeUtil.formatDate(performDate, "yyyy-MM-dd");
      performTime = this._datetimeUtil.formatDate(performTime, "hh:mm");
      if (performDate > nowData || (performDate == nowData && performTime > this._datetimeUtil.getNowTime("hh:mm"))) {
        this._showTip("warning", "执行时间不能超过当前时间!");
        return false;
      }
      return true;
    },
    async save() {
      let successMessage = "";
      let failMessage = "";
      this.progressFlag = true;
      //用于保存成功后取消勾选取消勾选
      let saveIndex = 0;
      let cloneData = this._common.clone(this.allData);
      for (let i = 0; i < cloneData.length; i++) {
        let row = cloneData[i];
        let optIndex = -1;
        let details = [];
        let performDate = this.params.scheduleDate;
        let performTime = undefined;
        let allDefaultFlag = true;
        let informPhysicianFlag = false;
        let bringToShiftFlag = false;
        //进度条组件提示信息
        let message = "";
        for (let index in row) {
          let item = row[index];
          // 排除时间和操作列
          if (index == 0) {
            performTime = item;
            continue;
          }
          if (item.hasButton != undefined) {
            optIndex = index;
            continue;
          }
          //排除录入空值的小便次数
          if (item.interventionDetailID == "30005010" && !item.assessValue) {
            continue;
          }
          //排除录入空值的大便次数
          if (item.interventionDetailID == "30005020" && !item.assessValue) {
            continue;
          }
          if (allDefaultFlag) {
            allDefaultFlag = item.assessValue ? false : true;
          }
          if (item.patientScheduleMainIDs && item.patientScheduleMainIDs.length) {
            item.patientScheduleMainIDs.forEach((mainID) => {
              let saveFlag = true;
              let detail = {
                PatientScheduleMainID: mainID,
                InterventionDetailID: item.interventionDetailID,
                AssessListID: item.assessListID,
                InpatientID: this.params.inpatientID,
                ScheduleData: item.assessValue ? item.assessValue : "",
                //复测需要字段 --GPC
                temperatureStatus: item.temperatureStatus,
              };
              if (item.style == "D" && item.assessValue) {
                let sucRadioOpt = item.radioOpt.find(
                  (radioOptItem) => radioOptItem.interventionDetailID == item.assessValue.trim()
                );
                if (sucRadioOpt) {
                  detail.AssessListID = sucRadioOpt.assessListID;
                  detail.InterventionDetailID = sucRadioOpt.interventionDetailID;
                  detail.ScheduleData = sucRadioOpt.showName;
                } else {
                  saveFlag = false;
                }
              }
              if ((item.style == "T" || item.style == "TN") && !detail.ScheduleData) {
                saveFlag = false;
              }
              if (saveFlag) {
                details.push(detail);
              }
            });
          }
          if (item.name == "performDate") {
            performDate = item.value;
          }
          if (item.name == "performTime") {
            performTime = item.value;
          }
          if (item.name == "informPhysician") {
            informPhysicianFlag = item.value == "1" ? true : false;
          }
          if (item.name == "bringToShift") {
            bringToShiftFlag = item.value == "1" ? true : false;
          }
        }
        //获取保存数据提示字符出
        let messageItem = row["0"];
        let progress = (((i + 1) / cloneData.length) * 100).toFixed(0);
        //检核是否有无数据提交
        //未保存过数据切保存数据为空提示填写数据
        if (!row[optIndex].hasValue && allDefaultFlag) {
          message = messageItem + "请填写数据！";
          if (failMessage) {
            failMessage += "、" + messageItem;
          } else {
            failMessage = messageItem;
          }
          this.messageData[0].value = Number(progress);
          this.messageData[2].value = failMessage;
          this.messageData[3].value = message;
          saveIndex += 1;
          continue;
        }
        if (!this.checkTime(performDate, performTime)) {
          message = messageItem + "执行时间不能超过当前时间";
          if (failMessage) {
            failMessage += "、" + messageItem;
          } else {
            failMessage = messageItem;
          }
          this.messageData[0].value = Number(progress);
          this.messageData[2].value = failMessage;
          this.messageData[3].value = message;
          saveIndex += 1;
          continue;
        }
        //全部数据更改时间
        if (performDate && performTime) {
          details.forEach((detail) => {
            detail.scheduleDate = performDate;
            detail.scheduleTime = this._datetimeUtil.formatDate(performTime, "hh:mm");
            detail.informPhysician = informPhysicianFlag;
            detail.bringToShift = bringToShiftFlag;
          });
        }
        await SaveMultiSchedule(details).then((result) => {
          if (result.code == 1) {
            // 保存成功在操作栏上打上保存标记，切换编辑图标用
            this.$set(this.allData[saveIndex][optIndex], "hasValue", "1");
            //保存成功拼接成功字符
            if (successMessage) {
              successMessage += "、" + messageItem;
            } else {
              successMessage = messageItem;
            }
            //保存成功取消勾选
            this.toggleSelection([this.allData[saveIndex]], false);
          } else {
            if (failMessage) {
              failMessage += "、" + messageItem;
            } else {
              failMessage = messageItem;
            }
          }
        });

        //配置进度条内容
        this.messageData[0].value = Number(progress);
        this.messageData[1].value = successMessage;
        this.messageData[2].value = failMessage;
      }
    },

    clear(row, clearIndex) {
      for (let index in row) {
        // 排除时间和操作列
        if (index == 0 || row[index].hasButton != undefined) {
          continue;
        }
        if (row[index].assessValue) {
          row[index].assessValue = "";
          this.checkInputValue(row[index]);
        }
      }
      // 如果当前行是复制行，则同时清除
      if (this.copyIndex && this.copyIndex == clearIndex) {
        this.copyRow = undefined;
        this.copyIndex = undefined;
        this.isCopy = false;
      }
    },
    copy(row, index) {
      this.isCopy = true;
      this.copyRow = this._common.clone(row);
      this.copyIndex = index;
    },
    paste(row) {
      let noCopy = false;
      for (let copyIndex in this.copyRow) {
        noCopy = false;
        // 排除时间和操作列
        if (copyIndex == 0 || this.copyRow[copyIndex].hasButton != undefined) {
          continue;
        }
        if (row[copyIndex]) {
          for (let id in this.copyID) {
            if (this.copyID[id] == row[copyIndex].assessListID) {
              noCopy = true;
            }
          }
          if (noCopy) {
            continue;
          }
          if (row[copyIndex].style == "D" && this.copyRow[copyIndex].assessValue) {
            this.$set(row[copyIndex], "disabled", false);
          }
          this.$set(row[copyIndex], "assessValue", this.copyRow[copyIndex].assessValue);

          this.checkInputValue(row[copyIndex]);
        }
      }
      this.isCopy = false;
      this.copyRow = undefined;
      this.copyIndex = undefined;
    },
    //获取表格数据添加颜色字段--GPC
    addassessValueColor(value) {
      for (let i = 0; i < value.length; i++) {
        for (let item in value[i]) {
          if (value[i][item].triggerCondition) {
            value[i][item].showFlag = false;
            const relateVisibleColumnIndex = this.dataColumns.find(
              (column) => column.index == item && column.hiddenFlag
            )?.sort;
            const formulaItem = relateVisibleColumnIndex ? value[i][relateVisibleColumnIndex] : undefined;
            this.checkInputValue(value[i][item], formulaItem);
          }
        }
      }
    },
    //数值异常标红--GPC
    checkInputValue(value, formulaItem) {
      this.isCopy = false;
      if (!value.triggerCondition) {
        return;
      }
      value.triggerCondition.some((item) => {
        if (Number(value.assessValue) >= Number(item.LowNotify) && Number(value.assessValue) <= Number(item.UpNotify)) {
          this.$set(value, "assessValueColor", "red");
          if (item.ReTest) {
            //体温过高触发复测选框
            this.$set(value, "assessValueColor", "red reduce");
            //有隐藏项时 调整1295输入框样式 处理复测勾选框换行
            formulaItem && this.$set(formulaItem, "assessValueColor", "red reduce");
            // 条件符合，改为高温且复测
            if (value.temperatureStatus === 0) {
              this.$set(value, "temperatureStatus", 2);
            }
            this.$set(value, "showFlag", true);
          }
          return true;
        } else {
          this.$set(value, "assessValueColor", "");
          formulaItem && this.$set(formulaItem, "assessValueColor", "");
          this.$set(value, "temperatureStatus", 0);
          this.$set(value, "showFlag", false);
        }
      });
    },
    //更改多选框绑定值
    changeInputValue(value, row) {
      //体温负责选框赋值
      if (row.style == "TN") {
        if (value) {
          this.$set(row, "temperatureStatus", 2);
        } else {
          this.$set(row, "temperatureStatus", 1);
        }
      }
      //选框赋值
      if (row.style == "C") {
        this.$set(row, "assessValue", value ? row.showName : "");
      }
    },
    //监测仪器按钮
    showClinicDialog(value) {
      //打开弹窗重置获取仪器时间范围
      this.timeRange = 30;
      this.dialogVisible = true;
      this.selectMonitorData = value;
      //获取仪器数据api所需数据
      this.getClinitParams(value);
      this.dialogTitle = value["0"] + " -- 仪器监测数据";
    },
    //获取仪器数据api所需数据
    getClinitParams(value) {
      this.clinicPrams = {};
      let valueArr = Object.values(value);
      let patientScheduleMainIDArr = [];
      let scheduleTime = valueArr[0];
      let inpatientId = undefined;
      valueArr.forEach((item) => {
        if (typeof item == "object" && item.patientScheduleMainIDs) {
          patientScheduleMainIDArr = this.combineArr(patientScheduleMainIDArr, item.patientScheduleMainIDs);
          if (!inpatientId) {
            inpatientId = item.inpatientID;
          }
        }
      });
      this.clinicPrams = {
        inpatientID: inpatientId,
        patientScheduleMainIDs: patientScheduleMainIDArr,
        scheduleDate: this.params.scheduleDate,
        scheduleTime: scheduleTime,
      };
    },
    //勾选仪器数据
    getSelectClinicData(row) {
      this.selectClinicData = {};
      this.selectClinicData = row;
    },
    //回传数据到监测页面
    saveClinicData() {
      //Object.entries 将对象变为数组形式为[[key,value],[key,value]]
      let cinicDataArr = Object.entries(this.selectClinicData);
      let monitorDataArr = Object.entries(this.selectMonitorData);
      //用于判断仪器数据有没有带入对应的监测数据
      let dataFlag = false;
      cinicDataArr.forEach((cinicItem) => {
        //回传监测数据
        let valueFlag = monitorDataArr.find(
          (monitorItem) =>
            typeof monitorItem[1] == "object" &&
            monitorItem[1].assessListID &&
            monitorItem[1].assessListID == cinicItem[0]
        );
        if (valueFlag) {
          if (cinicItem[1] != "") {
            dataFlag = true;
          }
          this.selectMonitorData[valueFlag[0]].assessValue = cinicItem[1];
          //检核回传值
          this.checkInputValue(this.selectMonitorData[valueFlag[0]]);
        }
      });
      if (!dataFlag) {
        this.dialogVisible = false;
        this._showTip("warning", "无仪器数据对应数据要带入");
        return;
      }
      cinicDataArr.forEach((cinicItem) => {
        //回传日期时间
        let dateTimeFlag = monitorDataArr.find(
          (monitorItem) =>
            typeof monitorItem[1] == "object" && monitorItem[1].name && monitorItem[1].name == cinicItem[0]
        );
        if (dateTimeFlag) {
          this.selectMonitorData[dateTimeFlag[0]].value = cinicItem[1];
        }
      });
      this.dialogVisible = false;
    },
    //获取两个数组的并集
    combineArr(arrOne, arrTwo) {
      return [...new Set([...arrOne, ...arrTwo])];
    },

    //设置不符合条件不允许勾选
    checkCheckBox(row, index) {
      let arr = Object.values(row);
      if (arr[arr.length - 1].hasButton == "1") {
        return true;
      } else {
        return false;
      }
    },
    //勾选触发
    selectItem(item) {
      this.allData = item;
    },
    //一键保存
    async saveAllData() {
      if (this.allData.length == 0) {
        this._showTip("warning", "请勾选需要保存的数据");
        return;
      }
      await this.save();
    },
    //异动病人数据自动勾选
    toggleSelection(rows, flag) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.monitorData.toggleRowSelection([
            {
              row: row,
              selected: flag,
            },
          ]);
        });
      } else {
        this.$refs.monitorData.clearSelection();
      }
    },
    //进度条重置
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    //进度条关闭函数
    progressClose() {
      this.progressFlag = false;
      this.renewMessageData();
    },
    /**
     * description: 打开弹窗，跳转专项
     * param {*} row 当前单元格数据
     * return {*}
     */
    openButtonDialog(rowData, painColIndex) {
      if (!this.checkCheckBox(rowData)) {
        this._showTip("warning", "不可提前执行排程！");
        return;
      }
      // 当前单元格数据
      let cellData = rowData[painColIndex];
      // 缓存数据，更新角标使用
      this.buttonClickRow = {
        rowData: rowData,
        painColIndex: painColIndex,
        rowScheduleMainID: cellData.patientScheduleMainIDs[0],
        buttonInterventionDetailID: cellData.interventionDetailID,
      };

      this.buttonName = this.formatButtonShowName(cellData.showName);
      let url = cellData.linkForm;
      if (!url) {
        return;
      }
      this.showButtonDialog = true;

      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.patientInfo.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        "&isDialog=true" +
        `&patientScheduleMainID=${this.buttonClickRow.rowScheduleMainID}` +
        `&sourceID=${this.buttonClickRow.rowScheduleMainID}` +
        "&sourceType=Schedule";

      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 对评估细项的值进行更新
     * param {*}
     * return {*}
     */
    async updateButton() {
      let item = await this.getButtonValue();
      if (!item) {
        return;
      }
      let buttonCell = this.buttonClickRow.rowData[this.buttonClickRow.painColIndex];
      this.$set(buttonCell, "assessValue", item.assessValue);
    },
    /**
     * description: 更新按钮回显数据
     * params {*}
     * return {*}
     */
    async getButtonValue() {
      let item = undefined;
      let params = {
        inpatientID: this.patientInfo.inpatientID,
        nursingInterventionDetailID: this.buttonClickRow.buttonInterventionDetailID,
        sourceID: this.buttonClickRow.rowScheduleMainID,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: 更改通知医师标记/带入交班
     * param {*} optrow 当前操作列数据
     */
    changeOperateColumn(optrow) {
      optrow.value = optrow.value == "1" ? "0" : "1";
    },
    /**
     * description: 格式化按钮类的显示名称
     * param {*} showName
     * return {*}
     */
    formatButtonShowName(showName) {
      if (showName) {
        const index = showName.lastIndexOf("<br/>");
        return index != -1 ? showName.substring(0, index).trim() : showName;
      } else {
        return "";
      }
    },
    /**
     * description:获取疼痛需要去专项录入分数阈值
     * return {*}
     */
    async getPainScoreSetting() {
      let param = { settingType: 168, settingCode: "PainScoreThreshold" };
      await GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.painScoreThreshold = Number(response.data.typeValue);
        }
      });
    },
  },
};
</script>

<style lang="scss">
.single-patient-monitor {
  height: 100%;
  width: 100%;
  .save-button {
    float: right;
    margin-top: 5px;
  }
  .shift {
    margin-left: 20px;
  }
  .single-patient-monitor-content {
    position: relative;
  }
  .monitor-data {
    th .cell {
      line-height: 18px;
    }
    .title-div {
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .date {
      .no-save {
        .el-input__inner {
          color: #ff0000;
        }
      }
      .el-input__prefix {
        display: none;
      }
      .el-date-editor {
        width: 100%;
        .el-input__inner {
          padding-left: 3px;
        }
      }
    }
    .monitor-column {
      .el-input__inner {
        padding: 5px;
      }
      .el-input__suffix {
        right: 0;
      }
      sup {
        top: 3px;
        right: 8px;
        height: 8px;
      }
      .reduce {
        & > input {
          padding: 0;
          text-align: center;
        }
        width: 30px;
      }
    }
    .opt-column {
      .opt {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin: 0 3px;
      }
    }
    .cell {
      .el-checkbox {
        margin: 0 auto;
      }
      .input-wrap {
        white-space: nowrap;
      }
    }
    .red {
      & > input {
        color: #ff0000;
      }
      display: inline-block;
    }
  }
}
.single-patient-clinic-dialog {
  width: 900px;
  height: 600px;
}
.specific-care-view {
  background-color: #f3f3f3;
  iframe {
    height: 99%;
    border: none;
  }
}
</style>
