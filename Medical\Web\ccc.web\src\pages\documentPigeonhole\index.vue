<!--
 * FilePath     : \ccc.web\src\pages\documentPigeonhole\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-05-09?09:14
 * LastEditors  : 孟昭永
 * LastEditTime : 2024-10-31 09:52
 * Description  : 病案归档
-->
<template>
  <base-layout class="documen-pigeonhole">
    <div class="top" slot="header">
      <el-radio-group v-model="pdfStatus" @change="search">
        <el-radio-button :label="false">未归档</el-radio-button>
        <el-radio-button :label="true">已归档</el-radio-button>
      </el-radio-group>
      <label>查询日期:</label>
      <el-date-picker
        class="dataSelect"
        style="width: 120px"
        v-model="startDate"
        type="date"
        value-format="yyyy-MM-dd"
      ></el-date-picker>
      <label class="symbol">-</label>
      <el-date-picker
        class="dataSelect"
        style="width: 120px"
        v-model="endDate"
        type="date"
        value-format="yyyy-MM-dd"
      ></el-date-picker>
      <station-selector
        :userID="user.userID"
        hospitalFlag
        label="病区:"
        onlyOneDataStyle="Text"
        width="180"
        class="select-station"
        v-model="stationID"
        @change="changeValues"
      ></station-selector>
      <label>住院号:</label>
      <el-input v-model="localCaseNumber" class="topId" placeholder="请输入住院号" clearable></el-input>
      <el-button type="primary" @click="search">查询</el-button>
    </div>
    <div class="content" v-loading="loading" element-loading-text="加载中……">
      <el-table height="100%" :data="patientTableList" border stripe>
        <el-table-column prop="stationName" label="病区" min-width="100"></el-table-column>
        <el-table-column prop="departmentListName" label="科室" min-width="100"></el-table-column>
        <el-table-column
          header-align="center"
          align="center"
          prop="patientName"
          label="姓名"
          min-width="100"
        ></el-table-column>
        <el-table-column align="center" prop="localCaseNumber" label="住院号" min-width="100"></el-table-column>
        <el-table-column align="center" prop="ageDetail" label="年龄" min-width="100"></el-table-column>
        <el-table-column align="center" prop="gender" label="性别" min-width="100"></el-table-column>
        <el-table-column align="center" label="入院日期" min-width="100">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.admissionDateTimeView, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="出院日期" min-width="100">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.dischargeDateTimeView, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="emrArchivingFlag" label="是否归档" width="100"></el-table-column>
        <el-table-column align="center" label="操作" width="70">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.emrArchivingFlag == '未归档'" content="归档">
              <i @click="documentIn(scope.row)" class="iconfont icon-archivedFile"></i>
            </el-tooltip>
            <el-tooltip v-if="scope.row.emrArchivingFlag == '已归档'" content="解档">
              <i @click="documentOut(scope.row)" class="iconfont icon-solutionFile"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import { GetEMRArchivingPatients, PatientEMRArchiving, PatientEMRFileRelease } from "@/api/Inpatient";
import { mapGetters } from "vuex";
import stationSelector from "@/components/selector/stationSelector";
export default {
  components: {
    baseLayout,
    stationSelector,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      startDate: undefined,
      endDate: undefined,
      localCaseNumber: undefined,
      patientTableList: [],
      pdfStatus: false,
      stationID: undefined,
      loading: false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.startDate = this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -7, "yyyy-MM-dd");
      this.endDate = this._datetimeUtil.getNowDate();
    },
    search() {
      this.patientTableList = [];
      this.loading = true;
      if (this.localCaseNumber) {
        this.stationID = undefined;
      }
      let params = {
        startTime: this.startDate,
        endTime: this.endDate,
        localCaseNumber: this.localCaseNumber,
        pdfStatus: this.pdfStatus,
        stationID: this.stationID,
      };
      GetEMRArchivingPatients(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.patientTableList = res.data;
        }
      });
    },
    documentIn(row) {
      let params = {
        inpatientID: row.inpatientID,
      };
      this._confirm("病历归档后无法更改病历,您确认要归档么?", "归档确认", (flag) => {
        if (flag) {
          PatientEMRArchiving(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "归档成功");
              this.search();
            }
          });
        }
      });
    },
    changeValues() {
      this.localCaseNumber = undefined;
    },
    documentOut(row) {
      let params = {
        inpatientID: row.inpatientID,
      };
      this._confirm("您确认要解档么?", "解档确认", (flag) => {
        if (flag) {
          PatientEMRFileRelease(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "解档成功");
              this.search();
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.documen-pigeonhole {
  .top {
    .topId {
      width: 160px;
      .el-input-group__append {
        padding: 0 5px;
      }
      i {
        color: #8cc63e;
      }
      label {
        font-size: 13px;
      }
    }
  }
  .content {
    height: 100%;
  }
}
</style> 