<!--
 * FilePath     : \src\pages\measuresTheSummary\index.vue
 * Author       : 郭鹏超
 * Date         : 2020-04-20 15:33
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-23 16:37
 * Description  : 费用核查
 -->

<template>
  <base-layout class="measures-the-summary" headerHeight="auto">
    <div class="top" slot="header">
      <label>选择日期:</label>
      <el-date-picker
        class="dataSelect"
        v-model="startTime"
        type="date"
        @change="search"
        value-format="yyyy-MM-dd"
        placeholder="选择开始日期"
      ></el-date-picker>
      <label class="symbol">-</label>
      <el-date-picker
        class="dataSelect"
        v-model="endTime"
        type="date"
        @change="search"
        value-format="yyyy-MM-dd"
        placeholder="选择结束日期"
      ></el-date-picker>
      <div class="header-item">
        <label>措施:</label>
        <el-select class="measure-select" v-model="interventionID" @change="search" placeholder="请选择措施">
          <el-option
            v-for="item in interventionList"
            :key="item.id"
            :label="item.intervention"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <label>住院号:</label>
        <el-input
          v-model="localCaseNumber"
          class="search-inpatient-input"
          @keyup.enter.native="search"
          placeholder="请输入住院号"
        >
          <i slot="append" class="iconfont icon-search" @click="search"></i>
        </el-input>
      </div>
      <div class="header-item">
        <el-checkbox v-model="showDischargeFlag">显示已离院</el-checkbox>
      </div>
    </div>
    <div slot-scope="data">
      <el-table
        :height="data.height"
        :span-method="objectSpanMethod"
        empty-text="暂无数据"
        border
        :data="mainTableData"
        highlight-current-row
        ref="billingTable"
        v-loading="tableLoadingFlag"
        element-loading-text="加载中……"
      >
        <el-table-column prop="bedNumber" label="床号" align="left" width="70" />
        <el-table-column prop="chartNO" label="病案号" align="center" width="100" />
        <el-table-column prop="patientName" label="姓名" width="100" />
        <el-table-column prop="gender" label="性别" align="center" width="60" />
        <el-table-column prop="age" label="年龄" align="center" width="60" />
        <el-table-column prop="admissionDate" label="入院时间" align="center" width="120">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.admissionDate, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="numberOfAdmissions" label="入院次数" align="center" width="70" />
        <el-table-column prop="intervention" label="措施" min-width="140" />
        <el-table-column prop="count" align="center" width="100" label="已执行次数" />
        <el-table-column prop="fix" align="center" width="50" label="明细">
          <template slot-scope="scope">
            <el-tooltip content="明细">
              <i @click="showDetail(scope.$index, scope.row)" class="iconfont icon-more"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        :title="dialogTitle"
        v-dialogDrag
        fullscreen
        custom-class="no-footer"
        :close-on-click-modal="false"
        :visible.sync="dialogFormVisible"
      >
        <base-layout class="dialogContent">
          <div class="tableHeader" slot="header">
            <label>执行状态:</label>
            <el-select class="status-select" v-model="status" @change="fixStatus" placeholder="请选择执行状态">
              <el-option
                v-for="item in statusOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <div class="dialog-header-item">
              <label>日期范围:</label>
              <el-date-picker
                readonly
                class="dataSelect"
                v-model="startTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择开始日期"
              ></el-date-picker>
              <label class="symbol">-</label>
              <el-date-picker
                readonly
                class="dataSelect"
                v-model="endTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择结束日期"
              ></el-date-picker>
            </div>
            <div class="dialog-header-item">
              <label>护理单元:</label>
              <span>{{ stationName }}</span>
            </div>
            <div class="top-btn">
              <el-button class="print-button" icon="iconfont icon-arrow-download" @click="exportToExcel">
                导出
              </el-button>
            </div>
          </div>
          <el-table
            slot-scope="dialogData"
            :height="dialogData.height"
            empty-text="暂无数据"
            class="tableRight"
            :data="showDialogTableData"
            highlight-current-row
            border
          >
            <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
            <el-table-column prop="performDate" label="日期" width="120" align="center">
              <template slot-scope="scope">
                <span v-formatTime="{ value: scope.row.performDate, type: 'date' }"></span>
              </template>
            </el-table-column>
            <el-table-column prop="performTime" label="执行时间" align="center" width="100">
              <template slot-scope="scope">
                <span v-formatTime="{ value: scope.row.performTime, type: 'time' }"></span>
              </template>
            </el-table-column>
            <el-table-column prop="performUserName" label="执行人" width="70"></el-table-column>
            <el-table-column prop="status" label="执行状态" align="center" width="100"></el-table-column>
            <el-table-column prop="shift" label="班别日期" align="center" width="150"></el-table-column>
            <el-table-column prop="sourceType" label="来源" align="center" width="90"></el-table-column>
            <el-table-column prop="remarks" label="备注" min-width="170"></el-table-column>
          </el-table>
        </base-layout>
      </el-dialog>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import { GetInterventionsAsync, GetInterventionDetailsAsync, GetInterventionList } from "@/api/measuresTheSummary";
import { GetStationByID } from "@/api/Station";
import { export_json_to_excel } from "@/vendor/Export2Excel.js";
import { mapGetters } from "vuex";
import { GetInpatientDataByLocalCaseNumber } from "@/api/Inpatient";
export default {
  components: {
    baseLayout,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  data() {
    return {
      localCaseNumber: "",
      dialogFormVisible: false,
      //病人ID
      chartNO: "",
      //查询开始日期
      startTime: "",
      //查询结束如期
      endTime: "",
      //措施ID
      interventionID: "",
      //患者姓名
      patientName: "",
      //弹窗标题
      dialogTitle: "",
      //措施
      intervention: "",
      //执行状态
      status: "",
      //措施表格主数据
      mainTableData: [],
      //弹窗表格数据
      dialogTableData: [],
      //弹窗表格筛选备用数据
      showDialogTableData: [],
      mainTableCloneData: [],
      showDischargeFlag: undefined,
      // 措施下拉框数据
      interventionList: [],
      //执行状态下拉框数据
      statusOption: [
        {
          value: "全部",
          label: "全部",
        },
        {
          value: "已执行",
          label: "已执行",
        },
        {
          value: "未执行",
          label: "未执行",
        },
      ],
      //合并单元格需要数据
      spanArr: [],
      spanPos: 0,
      stationID: "",
      stationName: "",
      tableLoadingFlag: false,
    };
  },
  mounted() {
    this.stationID = this.user.stationID;
    //设置搜索的默认时间段为前一个月至今
    this.endTime = this._datetimeUtil.getNowDate();
    this.startTime = this._datetimeUtil.addDate(this.endTime, -30, "yyyy-MM-dd");
    //获取措施下拉框数据
    this.getMeasuresData();
    //获取对应单位
    this.getStationData();
    this.$watch("showDischargeFlag", () => {
      this.filterTable();
      this.merage();
    });
    this.showDischargeFlag = false;
    //调用获取措施主数据
    this.search();
  },
  methods: {
    //获取措施下拉框信息
    getMeasuresData() {
      this.interventionList = [];
      GetInterventionList().then((res) => {
        if (this._common.isSuccess(res)) {
          this.interventionList = res.data;
          this.interventionList.unshift({
            id: "",
            intervention: "全部",
          });
        }
      });
    },
    //获取科室信息
    getStationData() {
      if (this.stationID == "") {
        return;
      }
      let params = {
        ID: this.stationID,
      };
      GetStationByID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.stationName = res.data.stationName;
        }
      });
    },
    //表格合并数据处理
    merage() {
      this.spanArr = [];
      if (!this.mainTableData) {
        return;
      }
      for (let i = 0; i < this.mainTableData.length; i += 1) {
        if (i === 0) {
          // 第一行必须存在
          this.spanArr.push(1);
          this.spanPos = 0;
        } else {
          if (
            this.mainTableData[i].bedNumber === this.mainTableData[i - 1].bedNumber &&
            this.mainTableData[i].chartNO === this.mainTableData[i - 1].chartNO &&
            this.mainTableData[i].patientName === this.mainTableData[i - 1].patientName &&
            this.mainTableData[i].gender === this.mainTableData[i - 1].gender &&
            this.mainTableData[i].numberOfAdmissions === this.mainTableData[i - 1].numberOfAdmissions
          ) {
            this.spanArr[this.spanPos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.spanPos = i;
          }
        }
      }
    },
    //表格合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex <= 6) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    //班次日期字段合并
    dataFix(v) {
      v.map((item) => (item.shift = item.shiftDate.substr(0, 10) + "   " + item.shiftName));
    },
    //筛选措施状态
    fixStatus() {
      if (this.status == "全部") {
        this.showDialogTableData = this.dialogTableData;
      } else {
        this.showDialogTableData = this.dialogTableData.filter((item) => item.status == this.status);
      }
    },
    //获取措施详细数据
    showDetail(index, row) {
      this.status = "全部";
      this.dialogFormVisible = true;
      this.patientName = row.patientName;
      this.intervention = row.intervention;
      this.dialogTitle =
        row.bedNumber +
        "-" +
        row.patientName +
        "-" +
        row.gender +
        " -- " +
        row.intervention +
        " -- " +
        this.stationName;
      let params = {
        stationID: this.stationID,
        inpatientID: row.inpatientID,
        startTime: this.startTime,
        endTime: this.endTime,
        interventionID: row.interventionID,
      };
      GetInterventionDetailsAsync(params).then((res) => {
        if (this._common.isSuccess(res)) {
          res.data.forEach((data) => {
            data.patientName = this.patientName;
            data.performDate = this._datetimeUtil.formatDate(data.performDate, "yyyy-MM-dd");
            data.performTime = this._datetimeUtil.formatDate(data.performTime, "hh:mm");
          });
          this.dialogTableData = res.data;
          this.dataFix(this.dialogTableData);
          this.showDialogTableData = res.data;
        }
      });
    },
    //获取措施汇总数据
    async search() {
      if (this.localCaseNumber) {
        await this.getInpatientDataByLocalCaseNumber();
      } else {
        this.chartNO = "";
      }
      let params = {
        stationID: this.stationID,
        chartNO: this.chartNO,
        startTime: this.startTime,
        endTime: this.endTime,
        interventionID: this.interventionID,
      };
      this.tableLoadingFlag = true;
      await GetInterventionsAsync(params).then((res) => {
        this.tableLoadingFlag = false;
        if (this._common.isSuccess(res)) {
          this.mainTableData = res.data;
          this.mainTableCloneData = this._common.clone(this.mainTableData);
          this.filterTable();
          this.merage();
        }
      });
    },
    //根据住院号或者病人ID获取ChartNo
    async getInpatientDataByLocalCaseNumber() {
      let params = {
        number: this.localCaseNumber,
      };
      await GetInpatientDataByLocalCaseNumber(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.length > 0) {
            this.chartNO = res.data[res.data.length - 1].chartNo;
          }
        }
      });
    },
    filterTable() {
      if (this.showDischargeFlag) {
        this.mainTableData = this.mainTableCloneData;
      } else {
        this.mainTableData = this.mainTableCloneData.filter((billing) => {
          return !billing.dischargeFlag;
        });
      }
    },
    exportToExcel() {
      let mainData = this.dialogTableData;
      const mainHeader = ["日期", "执行时间", "班别", "执行人", "执行状态", "姓名", "来源", "备注"];
      const mainFilterVal = [
        "performDate",
        "performTime",
        "shift",
        "performUserName",
        "status",
        "patientName",
        "sourceType",
        "remarks",
      ];
      const dataMain = this.formatJson(mainFilterVal, mainData);
      export_json_to_excel(
        mainHeader,
        dataMain,
        "【" + this.startTime + "-" + this.endTime + "】" + this.patientName + this.intervention,
        undefined,
        true
      );
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) => filterVal.map((j) => v[j]));
    },
  },
};
</script>

<style lang="scss">
.measures-the-summary {
  .top {
    margin: 5px 0;
    .header-item {
      display: inline-block;
    }
    .search-inpatient-input {
      width: 160px;
      .el-input-group__append {
        padding: 0 5px;
        color: #8cc63e;
      }
    }
    label {
      margin-left: 5px;
    }
    .symbol {
      margin-left: 0;
    }
    .dataSelect {
      width: 120px;
    }
    .measure-select {
      width: 150px;
      margin-right: 10px;
    }
  }
  .tableHeader {
    line-height: 50px;
    .status-select {
      width: 100px;
    }
  }
  .top-btn {
    float: right;
  }
}
.section-input {
  width: 120px;
  margin-left: 30px;
}
.dialogContent {
  height: 100%;
  .dialog-header-item {
    display: inline-block;
  }
}
.el-dialog {
  .dataSelect {
    width: 120px;
  }
}
</style>
