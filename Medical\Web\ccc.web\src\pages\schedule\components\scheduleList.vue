<!--
 * FilePath     : \src\pages\schedule\components\scheduleList.vue
 * Author       : 曹恩
 * Date         : 2022-06-17 17:26
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-15 19:04
 * Description  : 排程执行页
 * CodeIterationRecord: 2744-作为IT人员，我需要排程执行疼痛及批量执行，可以跳转专项护理 2022-07-07 杨欣欣
                        2975-作为护理人员，我需要在排程执行页面，已执行排程放置最下，未执行放置上方，以利措施执行
-->
<template>
  <div class="schedule-list">
    <div class="top">
      <div class="schedule-list-top-patient-info" v-if="scheduleParams.patientInfo">
        {{
          scheduleParams.patientInfo +
          " " +
          scheduleParams.scheduleDate +
          " " +
          scheduleParams.startTime +
          "-" +
          scheduleParams.endTime +
          " 的护理排程"
        }}
      </div>
      <div class="btn-wrap">
        <el-button type="primary" icon="iconfont icon-time" @click="showMonitor">监测排程表</el-button>
        <el-button class="print-button" icon="iconfont icon-view" @click="showMeasures('bedsideObservation')">
          病情观察
        </el-button>
        <el-button class="edit-button" icon="iconfont icon-add" @click="showAddSchedule">加入排程</el-button>
        <lab-result-button :case-number="scheduleParams.caseNumber || ''"></lab-result-button>
        <el-button class="clinic-btn" icon="iconfont icon-clinic1" v-if="showClinic" @click="showClinicData">
          仪器数据
        </el-button>
      </div>
    </div>
    <el-radio-group v-model="selectType" class="schedule-type">
      <el-radio v-for="(type, index) in scheduleTypes" :key="index" :label="type.key">{{ type.value }}</el-radio>
    </el-radio-group>
    <el-table
      height="calc(100% - 100px)"
      :data="showSchedules"
      border
      stripe
      row-class-name="table-row"
      ref="scheduleTable"
    >
      <el-table-column label="计划时间" width="70" align="center">
        <template slot-scope="schedule">
          <span v-formatTime="{ value: schedule.row.scheduleTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="执行项目" min-width="200" header-align="center">
        <template slot-scope="schedule">
          <div :class="['intervention-name', { alert: schedule.row.alert }]">
            <div class="icon">
              <i
                v-if="schedule.row.inforbuttonContent"
                class="iconfont icon-info"
                @click="showMessage(schedule.row.inforbuttonContent)"
              ></i>
            </div>
            <span v-html="schedule.row.interventionName"></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140" header-align="center">
        <template slot-scope="schedule">
          <div class="operate-icon">
            <el-tooltip
              content="执行排程"
              v-if="schedule.row.completeMark == '0' || schedule.row.completeMark == '1'"
              placement="left"
            >
              <i class="iconfont icon-execute" @click="showExecute(schedule.row)"></i>
            </el-tooltip>
          </div>
          <div class="operate-icon" v-if="schedule.row.actionType != 5">
            <el-tooltip
              content="暂停执行"
              v-if="!schedule.row.performDate && (schedule.row.completeMark == '0' || schedule.row.completeMark == '3')"
              placement="right"
            >
              <i class="iconfont icon-pause" @click="showNoExecute(schedule.row)"></i>
            </el-tooltip>
          </div>
          <div class="operate-icon" v-if="schedule.row.actionType != 5">
            <el-tooltip
              content="延迟执行"
              v-if="!schedule.row.performDate && (schedule.row.completeMark == '0' || schedule.row.completeMark == '2')"
              placement="right"
            >
              <i class="iconfont icon-delay" @click="showDelayExecute(schedule.row)"></i>
            </el-tooltip>
          </div>
          <div class="operate-icon">
            <el-tooltip content="复制排程" v-if="schedule.row.copyScheduleFlag" placement="right">
              <i class="iconfont icon-copy" @click="showCopySchedule(schedule.row)"></i>
            </el-tooltip>
          </div>
          <div class="operate-icon" v-if="schedule.row.actionType != 5">
            <el-tooltip content="删除排程" v-if="showDeleteFlag && schedule.row.completeMark == '0'" placement="right">
              <i class="iconfont icon-del" @click="deleteCheck(schedule.row)"></i>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="执行时间" width="80" align="center">
        <template slot-scope="schedule">
          <span v-formatTime="{ value: schedule.row.performTime, type: 'time' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="执行人" prop="performEmployeeName" width="80" align="center"></el-table-column>
      <el-table-column label="执行说明" header-align="center" min-width="100">
        <template slot-scope="schedule">
          {{ getCommentText(schedule.row) }}
        </template>
      </el-table-column>
      <el-table-column label="完成" width="50" align="center">
        <template slot-scope="schedule">
          <i class="iconfont icon-check-mark" v-if="schedule.row.completeMark != 0"></i>
        </template>
      </el-table-column>
      <el-table-column label="记录" width="50" align="center">
        <template slot-scope="schedule">
          <i
            class="iconfont icon-check-mark"
            v-if="schedule.row.bringToNursingRecord == 1 && schedule.row.performDate != null"
          ></i>
        </template>
      </el-table-column>
      <el-table-column label="交班" width="50" align="center">
        <template slot-scope="schedule">
          <i
            class="iconfont icon-check-mark"
            v-if="schedule.row.bringToShift == 1 && schedule.row.performDate != null"
          ></i>
        </template>
      </el-table-column>
      <el-table-column label="来源" width="60" align="center">
        <template slot-scope="schedule">
          <el-tooltip
            v-for="(source, index) in schedule.row.sourceFlagItems"
            :key="index"
            :content="source.description"
            placement="top"
          >
            <div :class="['source-flag', source.typeValue]">
              {{ source.settingValue }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 显示仪器数据 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('仪器数据', false)"
      :visible.sync="showClinicDataFlag"
      fullscreen
      custom-class="no-footer"
    >
      <clinic-data-view :params="clinicDataParams"></clinic-data-view>
    </el-dialog>
    <!-- 显示监测排程表 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('监测排程表', false)"
      :visible.sync="showMonitorFlag"
      fullscreen
      custom-class="no-footer"
    >
      <monitoring-scheduler :queryParams="monitorParams" />
    </el-dialog>
    <!-- 显示观察措施 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle(addMeasuresFlag ? '病情观察' : '', true)"
      :visible.sync="showMeasuresFlag"
      fullscreen
      :append-to-body="true"
      custom-class="no-footer"
    >
      <component
        v-if="showMeasuresFlag"
        :is="measuresDialogComponent"
        :params="measuresDialogParams"
        @close="showMeasuresFlag = false"
      ></component>
    </el-dialog>
    <!-- 显示加入排程 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('加入排程', false)"
      :visible.sync="showAddScheduleFlag"
      custom-class="no-footer add-schedule"
    >
      <add-schedule
        v-if="showAddScheduleFlag"
        :addParams="addScheduleParams"
        @close="close($event, 'showAddScheduleFlag')"
      ></add-schedule>
    </el-dialog>
    <!-- 排程执行 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('', true)"
      :visible.sync="showExecuteFlag"
      :modal-append-to-body="false"
      :custom-class="'no-footer execute' + ' ' + (currentSchedule.interventionType == 'A' ? 'assess' : '')"
    >
      <schedule-perform v-if="showExecuteFlag" :params="currentSchedule" @result="executeResult"></schedule-perform>
    </el-dialog>
    <!-- 显示外部连接类排程执行 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('', true)"
      fullscreen
      custom-class="no-footer"
      :visible.sync="showExtExecuteFlag"
    >
      <iframe v-if="showExtExecuteFlag" ref="showExtExecuteDialog" width="100%" height="100%" frameborder="0"></iframe>
    </el-dialog>
    <!-- 措施触发措施 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('临床决策支持建议干预措施', false)"
      :visible.sync="showTrigger"
    >
      <trigger-schedule
        v-if="showTrigger"
        :triggerParams="triggerParams"
        @close="close($event, 'showTrigger')"
      ></trigger-schedule>
    </el-dialog>
    <!-- 暂停执行 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('暂停执行', true)"
      custom-class="no-execute-dialog"
      :visible.sync="showNoExecuteFlag"
    >
      <el-row>
        <el-col :span="4" class="label reason">暂停原因：</el-col>
        <el-col :span="20">
          <el-radio-group v-model="notPerformReason">
            <el-radio v-for="(reason, index) in notPerformReasons" :key="index" :label="reason.typeValue">
              {{ reason.description }}
            </el-radio>
          </el-radio-group>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="4" class="label">原因备注：</el-col>
        <el-col :span="20">
          <el-input
            v-model="notPerformComment"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 4 }"
            placeholder="请输入内容"
            resize="none"
          />
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="showNoExecuteFlag = false">取消</el-button>
        <el-button type="primary" @click="saveReasons">确定</el-button>
      </div>
    </el-dialog>
    <!-- 延迟执行 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('延迟执行', true)"
      custom-class="delay-execute-dialog"
      :visible.sync="showDelayExecuteFlag"
    >
      <el-row>
        <el-col :span="5" class="label">延迟到时间：</el-col>
        <el-col :span="19">
          <el-date-picker
            :picker-options="expireTimeOption"
            class="date-picker"
            v-model="endDate"
            type="date"
            :clearable="false"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          />
          <el-time-picker
            v-model="endTime"
            class="time-picker"
            placeholder="选择时间"
            format="HH:mm"
            value-format="HH:mm"
          ></el-time-picker>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="5" class="label reason">延迟原因：</el-col>
        <el-col :span="19">
          <el-radio-group v-model="delayPerformReason">
            <el-radio v-for="(reason, index) in delayPerformReasons" :key="index" :label="reason.typeValue">
              {{ reason.description }}
            </el-radio>
          </el-radio-group>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="5" class="label">原因备注：</el-col>
        <el-col :span="19">
          <el-input
            v-model="delayPerformComment"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 4 }"
            placeholder="请输入内容"
            resize="none"
          />
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="showDelayExecuteFlag = false">取消</el-button>
        <el-button type="primary" @click="saveReasons">确定</el-button>
      </div>
    </el-dialog>
    <!-- 复制排程 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('复制排程', true)"
      custom-class="copy-schedule"
      :visible.sync="showCopyScheduleFlag"
    >
      <el-row>
        <el-col :span="7" class="label">复制排程到时间：</el-col>
        <el-col :span="17">
          <el-date-picker
            :picker-options="expireTimeOption"
            class="date-picker"
            v-model="endDate"
            type="date"
            :clearable="false"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          />
          <el-time-picker
            v-model="endTime"
            class="time-picker"
            placeholder="选择时间"
            format="HH:mm"
            value-format="HH:mm"
          ></el-time-picker>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button @click="showCopyScheduleFlag = false">取消</el-button>
        <el-button type="primary" @click="saveCopySchedule">确定</el-button>
      </div>
    </el-dialog>
    <!-- 删除排程原因 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :title="getTitle('排程删除', true)"
      :visible.sync="deleteDialogVisible"
      custom-class="delete-dialog"
    >
      <div class="delete-comment">
        <label class="delete-comment-label">原因备注:</label>
        <el-input
          v-model="deleteReason"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 4 }"
          placeholder="请输入内容"
          resize="none"
          class="delete-comment-input"
        ></el-input>
      </div>
      <div slot="footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="deleteSchedule">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import bedsideObservation from "@/pages/patientObserve/bedsideObservation";
import observeEvalutaion from "@/pages/patientObserve/observeEvalutaion";
import monitoringScheduler from "@/autoPages/monitoringScheduler";
import schedulePerform from "./schedulePerform";
import clinicDataView from "./ClinicDataView";
import addSchedule from "./addSchedule";
import triggerSchedule from "./scheduleTypes/triggerSchedule";
import { GetSettingDescriptionInfo, GetLastMain } from "@/api/Assess";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import labResultButton from "@/components/button/labResultButton";
import {
  GetPatientSchedule,
  PerformCheck,
  DeletePatientSchedule,
  SaveDelayORNotPerformReason,
  CopySchedule,
} from "@/api/PatientSchedule";
import { GetPatientProblemInfo } from "@/api/PatientProblem";
import { GetServerDateTime, GetClinicShow } from "@/api/Setting";
import { CheckPatientInDept } from "@/api/PatientCheck";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    bedsideObservation,
    observeEvalutaion,
    schedulePerform,
    addSchedule,
    triggerSchedule,
    monitoringScheduler,
    clinicDataView,
    labResultButton,
  },
  props: {
    scheduleParams: {
      type: Object,
      required: true,
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  watch: {
    scheduleParams: {
      immediate: true,
      handler(newValue) {
        if (newValue && newValue.inpatientID) {
          this.getPatientSchedule();
        }
      },
    },
    // 排程类型切换
    selectType() {
      this.showScheduleByType();
    },
    // 显示仪器数据
    showClinicDataFlag(newValue) {
      if (!newValue) {
        this.$emit("refresh");
      } else {
        this.getScroll("get");
      }
    },
    // 显示监测排程表
    showMonitorFlag(newValue) {
      if (!newValue) {
        this.$emit("refresh");
      } else {
        this.getScroll("get");
      }
    },
    // 显示病情观察
    showMeasuresFlag(newValue) {
      if (!newValue) {
        this.$emit("refresh");
      } else {
        this.getScroll("get");
      }
    },
    // 显示加入排程
    showTrigger(newValue) {
      if (!newValue) {
        this.$emit("refresh");
      }
    },
    // 显示外部连接类排程执行
    showExtExecuteFlag(newValue) {
      if (!newValue) {
        this.$emit("refresh");
      } else {
        this.getScroll("get");
      }
    },
  },
  data() {
    return {
      deleteReason: "",
      deleteDialogVisible: false,
      deleteScheduleMainID: "",
      showDeleteFlag: undefined,
      scheduleTypes: [],
      patientSchedules: [],
      showSchedules: [],
      showScheduleSortFlag: false,
      selectType: "-1",
      monitorParams: undefined,
      showMonitorFlag: false,
      showClinicDataFlag: false,
      clinicDataParams: undefined,
      showMeasuresFlag: false,
      measuresDialogComponent: "",
      measuresDialogParams: undefined,
      showAddScheduleFlag: false,
      showExecuteFlag: false,
      showTrigger: false,
      triggerParams: {},
      showExtExecuteFlag: false,
      showNoExecuteFlag: false,
      showDelayExecuteFlag: false,
      showCopyScheduleFlag: false,
      currentSchedule: {},
      notPerformReasons: [],
      delayPerformReasons: [],
      notPerformReason: undefined,
      delayPerformReason: undefined,
      notPerformComment: "",
      delayPerformComment: "",
      settingType: "",
      addScheduleParams: {},
      scrollTop: 0,
      //延迟日期时间
      endDate: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd"),
      endTime: "",
      //延迟日期不能小于当前日期
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 8.64e7;
        },
      },
      scheduleDate: "",
      scheduleTime: "",
      //是否显示仪器数据按钮
      showClinic: false,
      //是否为新增病情观察
      addMeasuresFlag: true,
    };
  },
  mounted() {
    //获取已执行排程是否放到最后配置
    this.getScheduleSortSetting();
    //加上判断仪器数据按钮是否显示
    GetClinicShow().then((response) => {
      if (this._common.isSuccess(response)) {
        this.showClinic = response.data;
      }
    });
    let _this = this;
    window.onmessage = function (e) {
      this.showExtExecuteFlag = false;
      if (e.data == "closeDialog") {
        _this.showExtExecuteFlag = false;
        _this.$nextTick(() => {
          _this.getScroll("set");
        });
      }
    };
  },
  methods: {
    /**
     * @description: 获取已执行排程是否放到最后配置
     */
    getScheduleSortSetting() {
      let param = {
        settingTypeCode: "ShowSchedulesSort",
      };
      GetSettingSwitchByTypeCode(param).then((res) => {
        if (this._common.isSuccess(res)) {
          this.showScheduleSortFlag = res.data;
        }
      });
    },
    /**
     * @description: 获取弹窗标题
     * @param content
     * @param flag
     * @return
     */
    getTitle(content, flag) {
      let title = "";
      if (!this.scheduleParams || !this.scheduleParams.patientInfo) {
        return title;
      }
      title = this.scheduleParams.patientInfo;
      if (content) {
        title += "- " + content + " ";
      }
      if (flag && this.currentSchedule && this.currentSchedule.interventionName) {
        title += "- " + this.currentSchedule.interventionName.replace("<br>", "；").replace("</br>", "；");
      }
      return title;
    },
    async getSettingDescriptionInfo() {
      await GetSettingDescriptionInfo({
        settingTypeCode: this.settingType,
      }).then((result) => {
        if (this._common.isSuccess(result)) {
          // 是否可以删除
          if (this.settingType == "ShowDeleteSchedule") {
            if (result.data && result.data.length > 0) {
              this.showDeleteFlag = result.data[0].typeValue == "true";
            } else {
              this.showDeleteFlag = false;
            }
          } else if (this.settingType == "NotPerformReason") {
            // 暂停执行原因
            this.notPerformReasons = result.data;
          } else if (this.settingType == "DelayPerformReason") {
            // 延迟执行原因
            this.delayPerformReasons = result.data;
          }
        }
      });
    },
    /**
     * @description: 获取病人的排程
     */
    getPatientSchedule() {
      if (this.showDeleteFlag == undefined) {
        this.settingType = "ShowDeleteSchedule";
        this.getSettingDescriptionInfo();
      }
      let params = {
        inpatientID: this.scheduleParams.inpatientID,
        stationID: this.scheduleParams.stationID,
        shiftID: this.scheduleParams.shiftID,
        scheduleDate: this.scheduleParams.shiftDate,
        startTime: this.scheduleParams.startTime,
        endTime: this.scheduleParams.endTime,
        interventionID: this.scheduleParams.interventionID,
      };
      GetPatientSchedule(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.dealData(result.data);
        }
      });
    },
    /**
     * @description 将从API获取的数据处理成页面需要的数据
     * @param data 原始数据
     */
    dealData(data) {
      let allPerformCount = 0;
      let allScheduleCount = 0;
      this.scheduleTypes = [];
      this.patientSchedules = [];
      data.forEach((item) => {
        let type = item.scheduleType;
        let label = "(" + type.performCount + "/" + type.scheduleCount + ") " + type.typeName;
        this.scheduleTypes.push({ key: type.actionType, value: label });
        allPerformCount = parseInt(allPerformCount) + parseInt(type.performCount);
        allScheduleCount = parseInt(allScheduleCount) + parseInt(type.scheduleCount);
        this.patientSchedules.push({
          key: type.actionType,
          schedule: item.patientScheduleType,
        });
      });
      this.scheduleTypes.unshift({
        key: "-1",
        value: "(" + allPerformCount + "/" + allScheduleCount + ") 全部",
      });
      this.showScheduleByType();
      this.getScroll("set");
    },
    /**
     * @description: 获取排程执行说明
     * @param schedule
     * @return
     */
    getCommentText(schedule) {
      let comment = "";
      if (schedule.performComment) {
        comment = schedule.performComment;
      }
      if (schedule.delayOrNotReason) {
        if (comment.length > 0) {
          comment += ";";
        }
        comment += schedule.delayOrNotReason;
      }
      if (schedule.delayOrNotComment) {
        if (comment.length > 0) {
          comment += ";";
        }
        comment += schedule.delayOrNotComment;
      }
      return comment;
    },
    /**
     * @description: 根据排程类型显示对应的排程
     */
    showScheduleByType() {
      this.showSchedules = [];
      let tempSchedules = [];
      this.patientSchedules.forEach((item) => {
        if (this.selectType === "-1" || this.selectType === item.key) {
          tempSchedules = tempSchedules.concat(item.schedule);
        }
      });
      tempSchedules.sortBy("scheduleTime");
      this.showSchedules = tempSchedules;
      if (this.showScheduleSortFlag == true) {
        //把执行过的排程放到最下方
        this.showSchedules.sortBy("completeMark", "a");
      }
      this.$nextTick(() => {
        if (this.$refs.scheduleTable) {
          this.$refs.scheduleTable.doLayout();
        }
      });
    },
    /**
     * @description: 显示消息
     * @param messageContent
     * @return
     */
    showMessage(messageContent) {
      this._showMessage({
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 5000,
      });
    },
    /**
     * @description: 显示仪器数据弹窗
     */
    showClinicData() {
      this.showClinicDataFlag = true;
      this.clinicDataParams = {
        inpatientID: this.scheduleParams.inpatientID,
        scheduleDate: this.scheduleParams.shiftDate,
      };
    },
    /**
     * @description: 监测排程表弹窗
     */
    showMonitor() {
      this.showMonitorFlag = true;
      this.monitorParams = {
        inpatientID: this.scheduleParams.inpatientID,
        scheduleDate: this.scheduleParams.shiftDate,
        shiftID: this.scheduleParams.shiftID,
        shiftName: this.scheduleParams.shiftName,
        index: Math.random(),
      };
    },
    /**
     * @description: 显示病情观察弹窗
     * @param component
     * @param patientScheduleMainID
     * @return
     */
    showMeasures(component, patientScheduleMainID) {
      //点击按钮不需要拼接措施内容  措施内容置空
      this.currentSchedule = patientScheduleMainID ? this.currentSchedule : {};
      this.addMeasuresFlag = patientScheduleMainID ? false : true;
      this.measuresDialogComponent = component;
      this.measuresDialogParams = {
        scheduleFlag: true,
        isDialog: true,
      };
      if (patientScheduleMainID) {
        this.measuresDialogParams.patientScheduleMainID = patientScheduleMainID;
      }
      this.showMeasuresFlag = true;
    },
    /**
     * @description: 显示加入排程弹窗
     */
    async showAddSchedule() {
      this.getScroll("get");
      let params = {
        inpatientID: this.scheduleParams.inpatientID,
      };
      let eventFlag;
      await CheckPatientInDept(params).then((result) => {
        if (this._common.isSuccess(result)) {
          eventFlag = result.data.isChecked;
        }
      });
      if (!eventFlag) {
        this._showTip("warning", "患者不在科，暂无法添加排程!");
        return;
      }
      let flag = false;
      await GetLastMain(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.tempSaveMark == "T") {
            this._showTip("warning", "有暂存护理评估！");
            flag = true;
          }
        } else {
          this._showTip("warning", "尚未进行护理评估！");
          flag = true;
        }
      });
      if (flag) {
        return;
      }
      params.stationID = this.scheduleParams.stationID;
      params.isAddSchedule = true;
      GetPatientProblemInfo(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data && result.data.length > 0) {
            this.addScheduleParams = {
              problems: result.data,
              inpatientID: this.scheduleParams.inpatientID,
              stationID: this.scheduleParams.stationID,
              scheduleDate: this.scheduleParams.scheduleDate,
              scheduleTime: this.scheduleParams.startTime,
              shiftID: this.scheduleParams.shiftID,
            };
            this.showAddScheduleFlag = true;
          } else {
            this._showTip("warning", "尚未设定护理计划！");
          }
        }
      });
    },
    /**
     * @description: 加入排程弹窗关闭
     * @param flag
     * @param type
     * @return
     */
    close(flag, type) {
      if (type) {
        this[type] = false;
      }
      if (flag) {
        this.$emit("refresh");
      }
    },
    /**
     * @description: 显示执行排程弹窗
     * @param schedule
     * @return
     */
    async showExecute(schedule) {
      // 已执行排程判断是否是本人或护士长
      if (schedule.completeMark == "1") {
        let { _, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          schedule.performEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("error", "已执行排程，非本人不可修改！");
          return;
        }
      }
      // 判断是否可以执行此排程
      let params = {
        scheduleDate: schedule.scheduleDate,
        scheduleTime: schedule.scheduleTime,
      };
      PerformCheck(params).then((result) => {
        // 可以执行
        if (this._common.isSuccess(result)) {
          this.getScroll("get");
          // 检核类型
          this.checkType(schedule);
        }
      });
    },
    /**
     * @description: 检核排程类型
     * @param schedule
     * @return
     */
    checkType(schedule) {
      if (schedule.pdaFlag) {
        this._showTip("warning", "此措施只能在移动设备上执行！");
        return;
      }
      if (schedule.actionType == 5) {
        this._showTip("info", "目前正在执行给药,请谨慎作业！");
        // 给药排成采用D类型执行
        schedule.interventionType = "D";
      }
      if (schedule.sourceFlag == "A") {
        this._showTip("warning", "护理评估带入措施，请由护理评估页面进行修改！");
        return;
      }
      if (schedule.sourceFlag == "Z") {
        this._showTip("warning", "专项评估带入措施，请由专项评估页面进行修改！");
        return;
      }
      if (schedule.sourceFlag == "T" && schedule.sourceFlagDetail == "AssessContent") {
        this._showTip("warning", "评估带入措施，请由评估页面(参考来源)进行修改！");
        return;
      }
      if (schedule.sourceFlag == "R" && schedule.sourceFlagDetail == "Risk") {
        this._showTip("warning", "风险带入措施，请由风险评分页面进行修改！");
        return;
      }
      if (!schedule.interventionType) {
        schedule.interventionType = "D";
      }
      this.currentSchedule = schedule;
      if (schedule.interventionType == "L") {
        if (schedule.performUrl) {
          //病情观察特殊处理
          if (schedule.performUrl.includes("bedsideObservation")) {
            this.showMeasures("bedsideObservation", schedule.patientScheduleMainID);
            return;
          }
          //病情观察评价特殊处理
          if (schedule.performUrl.includes("observeEvalutaion")) {
            this.showMeasures("observeEvalutaion", schedule.patientScheduleMainID);
            return;
          }
          this.currentSchedule.performUrl += this.currentSchedule.performUrl.includes("?") ? "&" : "?";
          this.currentSchedule.performUrl += `patientScheduleMainID=${schedule.patientScheduleMainID}`;

          this.currentSchedule.performUrl +=
            `&inpatientID=${this.scheduleParams.inpatientID}` +
            `&scheduleDate=${schedule.scheduleDate}` +
            `&scheduleTime=${schedule.scheduleTime}` +
            `&completeMark=${schedule.completeMark}` +
            `&stationID=${this.scheduleParams.stationID}` +
            `&departmentListID=${this.scheduleParams.departmentListID}` +
            `&nursingLevel=${this.scheduleParams.nursingLevel}` +
            `&performDate=${schedule?.performDate ?? ""}` +
            `&performTime=${schedule?.performDate ?? ""}` +
            "&isDialog=true";
          this.showExtExecuteFlag = true;

          this.$nextTick(() => {
            this.$refs.showExtExecuteDialog.contentWindow.location.replace(this.currentSchedule.performUrl);
          });
        }
      } else {
        this.showExecuteFlag = true;
        this.currentSchedule.inpatientID = this.scheduleParams.inpatientID;
        this.$set(this.currentSchedule, "index", Math.random());
      }
    },
    /**
     * @description: 排程执行结果
     * @param flag
     * @param triggerParams
     * @return
     */
    executeResult(flag, triggerParams) {
      this.showExecuteFlag = false;
      if (flag) {
        this.$emit("refresh");
      }
      if (triggerParams) {
        this.showTrigger = true;
        this.triggerParams = triggerParams;
      }
    },
    /**
     * @description: 显示暂停执行弹窗
     * @param schedule
     * @return
     */
    showNoExecute(schedule) {
      this.getScroll("get");
      this.showNoExecuteFlag = true;
      this.currentSchedule = schedule;
      this.settingType = "NotPerformReason";
      this.notPerformReason = schedule.delayOrNotReasonCode || "";
      this.notPerformComment = schedule.delayOrNotComment || "";
      this.getSettingDescriptionInfo();
    },
    /**
     * @description: 显示延迟执行弹窗
     * @param schedule
     * @return
     */
    showDelayExecute(schedule) {
      this.endDate = schedule.scheduleDate;
      this.endTime = schedule.scheduleTime;
      this.scheduleDate = schedule.scheduleDate;
      this.scheduleTime = schedule.scheduleTime;
      this.getScroll("get");
      this.showDelayExecuteFlag = true;
      this.currentSchedule = schedule;
      this.settingType = "DelayPerformReason";
      this.delayPerformReason = schedule.delayOrNotReasonCode || "";
      this.delayPerformComment = schedule.delayOrNotComment || "";
      this.getSettingDescriptionInfo();
    },
    /**
     * @description: 暂停执行或延迟执行保存
     */
    saveReasons() {
      if (this.scheduleDate >= this.endDate && this.scheduleTime > this.endTime) {
        this._showTip("warning", "保存失败，延迟时间不能小于计划时间！");
        return;
      }
      let params = {
        patientScheduleMainID: this.currentSchedule.patientScheduleMainID,
        settingTypeCode: this.settingType,
      };
      if (this.settingType == "NotPerformReason") {
        params.typeValue = this.notPerformReason;
        params.comment = this.notPerformComment;
      } else if (this.settingType == "DelayPerformReason") {
        params.typeValue = this.delayPerformReason;
        params.comment = this.delayPerformComment;
        //延迟时间参数
        params.DelayDate = this.endDate;
        params.DelayTime = this.endTime;
      }
      if (!params.typeValue && !params.comment) {
        this._showTip("warning", "请选择或输入原因！");
        return;
      }
      return SaveDelayORNotPerformReason(params).then((result) => {
        this._showTip("success", "保存成功！");
        if (this.settingType == "NotPerformReason") {
          this.showNoExecuteFlag = false;
        } else if (this.settingType == "DelayPerformReason") {
          this.showDelayExecuteFlag = false;
        }
        this.$emit("refresh");
      });
    },
    /**
     * @description: 显示复制排程弹窗
     * @param schedule
     * @return
     */
    async showCopySchedule(schedule) {
      await GetServerDateTime().then((res) => {
        if (this._common.isSuccess(res)) {
          let time = res.data;
          this.endDate = this._datetimeUtil.formatDate(time, "yyyy-MM-dd");
          this.endTime = this._datetimeUtil.formatDate(time, "hh:mm");
        } else {
          this.endDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
          this.endTime = this._datetimeUtil.getNowTime("hh:mm");
        }
      });
      this.getScroll("get");
      this.showCopyScheduleFlag = true;
      this.currentSchedule = schedule;
    },
    /**
     * @description: 复制排程保存
     */
    saveCopySchedule() {
      let params = {
        PatientScheduleMainID: this.currentSchedule.patientScheduleMainID,
        DelayDate: this.endDate,
        DelayTime: this.endTime,
      };

      return CopySchedule(params).then((result) => {
        this._showTip("success", "保存成功！");
        this.showCopyScheduleFlag = false;
        this.$emit("refresh");
      });
    },
    /**
     * @description: 删除排程前检核
     * @param schedule
     * @return
     */
    deleteCheck(schedule) {
      this.getScroll("get");
      this.deleteScheduleMainID = "";
      this.deleteReason = "";
      this.currentSchedule = schedule;
      if (schedule.actionType == 5) {
        this._showTip("warning", "给药医嘱不允许删除！");
        return;
      }
      if (schedule.sourceFlagDetail && schedule.sourceFlagDetail != "复制排程") {
        this._showTip("warning", "常规护理措施不允许删除！");
        return;
      }
      if (schedule.sourceFlag == "G") {
        this._showTip("warning", "观察措施带入措施，请由观察措施页面进行删除！");
        return;
      }
      if (schedule.sourceFlag == "A") {
        this._showTip("warning", "护理评估带入措施，请由护理评估页面进行删除！");
        return;
      }
      if (schedule.sourceFlag == "Z") {
        this._showTip("warning", "专项评估带入措施，请由专项评估页面进行删除！");
        return;
      }
      this._deleteConfirm(undefined, (flag) => {
        if (flag) {
          this.deleteScheduleMainID = schedule.patientScheduleMainID;
          this.deleteDialogVisible = true;
        }
      });
    },
    /**
     * @description: 删除排程
     */
    deleteSchedule() {
      let params = {
        patientScheduleMainID: this.deleteScheduleMainID,
        deleteNote: this.deleteReason,
      };
      DeletePatientSchedule(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.deleteDialogVisible = false;
          this._showTip("success", "删除成功！");
          this.$emit("refresh");
        }
      });
    },
    /**
     * @description: 获取/设置滚动条位置
     * @param todo set get
     * @return
     */
    getScroll(todo) {
      this.$nextTick(() => {
        let el = document.getElementsByClassName("schedule-data-wrap")[0];
        if (!el) {
          let el = document.getElementsByClassName("base-content")[0];
        }
        if (!el) {
          return;
        }
        if (todo == "set") {
          if (this.scrollTop != 0) {
            el.scrollTop = this.scrollTop;
            this.scrollTop = 0;
          }
        } else if (todo == "get") {
          this.scrollTop = el.scrollTop;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.schedule-list {
  height: 100%;
  .top {
    background-color: #fff;
    height: 50px;
    line-height: 50px;
    padding: 0 10px;
    margin-bottom: 10px;
    .schedule-list-top-patient-info {
      float: left;
    }
    .btn-wrap {
      float: right;
      .clinic-btn {
        color: white;
        background-color: #9b25b3;
      }
    }
  }
  .schedule-type.el-radio-group {
    background-color: #fff;
    width: 100%;
    padding: 10px 10px 0 0;
    box-sizing: border-box;
    .el-radio {
      margin: 0 0 10px 10px;
    }
  }
  .el-table {
    .table-row {
      .icon-check-mark {
        cursor: default;
      }
      .intervention-name {
        display: flex;
        align-items: center;
        &.alert {
          color: #ff0000;
        }
        .icon {
          width: 16px;
          height: 18px;
          margin: 0 2px;
          .icon-info {
            margin: 0;
          }
        }
      }
      .source-flag {
        display: inline-block;
        height: 18px;
        width: 18px;
        line-height: 16px;
        padding: 1px 0 1px 1px;
        border-radius: 10px;
        text-align: center;
        font-size: 12px;
        color: #fff;
        font-weight: bold;
        &.S,
        &.A {
          background-color: #f5c181;
        }
        &.T,
        &.N,
        &.G {
          background-color: #8ed6ff;
        }
        &.O {
          background-color: #21e6c1;
        }
        &.Z {
          background-color: #a5aeff;
        }
        &.R {
          background-color: #8ed6ff;
        }
      }
      .operate-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin: 0 3px;
      }
    }
  }
  /* 无底部弹窗样式 */
  .el-dialog.no-footer .el-dialog__body {
    padding-bottom: 0 !important;
  }
  /* 弹窗样式 */
  .el-dialog:not(.add-schedule) {
    .el-row {
      padding-right: 20px;
      margin-top: 10px;
      .label {
        text-align: right;
        &.reason {
          margin-top: 5px;
        }
      }
      .date-picker {
        width: 120px;
      }
      .time-picker {
        width: 120px;
      }
    }
  }
  /* 排程执行弹窗 */
  .execute.el-dialog {
    top: 2vh !important;
    margin-top: 2vh !important;
    height: 90%;
    width: 740px;
    &.assess {
      width: 80%;
    }
    .el-dialog__body {
      padding: 0 !important;
    }
  }
  /* 排程延迟执行暂停执行弹窗 */
  .no-execute-dialog.el-dialog,
  .delay-execute-dialog.el-dialog {
    top: 15vh !important;
    width: 580px;
    height: 300px;
    .el-radio-group {
      .el-radio {
        width: 90px;
        margin-right: 10px;
        margin-top: 10px;
      }
    }
  }
  /* 复制排程弹窗 */
  .copy-schedule.el-dialog {
    top: 20vh !important;
    margin-top: 20vh !important;
    width: 520px;
    height: 150px;
  }
  /* 删除弹窗 */
  .delete-dialog.el-dialog {
    top: 20vh !important;
    margin-top: 20vh !important;
    width: 500px;
    height: 200px;
    .delete-comment {
      .delete-comment-input {
        margin-top: 5px;
      }
    }
  }
  /* L类排程执行弹窗 */
  .external-url.el-dialog {
    background-color: #f3f3f3;
    .el-dialog__body {
      padding: 5px;
      height: calc(100% - 20px);
      iframe {
        height: calc(100% - 5px);
      }
    }
  }
}
</style>
