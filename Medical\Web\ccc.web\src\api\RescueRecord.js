import http from "../utils/ajax";
const baseUrl = "/PatientRescue";

export const urls = {
  AddRescueRecord: baseUrl + "/AddRescueRecord",
  AddRescueCareMain: baseUrl + "/AddRescueCareMain",
  StopRescue: baseUrl + "/StopRescue",
  DeleteRescueRecord: baseUrl + "/DeleteRescueRecord",
  DeleteRescueCareMain: baseUrl + "/DeleteRescueCareMain",
  UpdateRescueRecord: baseUrl + "/UpdateRescueRecord",
  UpdateRescueCareMain: baseUrl + "/UpdateRescueCareMain",
  GetPatientRescueRecords: baseUrl + "/GetPatientRescueRecords",
  GetRescueAssessView: baseUrl + "/GetRescueAssessView",
  GetCareMainsByRecordID: baseUrl + "/GetCareMainsByRecordID",
  GetRescueMedications: baseUrl + "/GetRescueMedications",
};

/**
 * @description: 新增主记录
 * @param params
 * @return 
 */
export const AddRescueRecord = params => http.post(urls.AddRescueRecord, params);

/**
 * @description: 新增维护记录
 * @param params
 * @return 
 */
export const AddRescueCareMain = params => http.post(urls.AddRescueCareMain, params);

/**
 * @description: 停止记录
 * @param params
 * @return 
 */
export const StopRescue = params => http.post(urls.StopRescue, params);

/**
 * @description: 删除主记录
 * @param params
 * @return 
 */
export const DeleteRescueRecord = params => http.get(urls.DeleteRescueRecord, params);

/**
 * @description: 删除维护记录
 * @param params
 * @return 
 */
export const DeleteRescueCareMain = params => http.get(urls.DeleteRescueCareMain, params);

/**
 * @description: 更新主记录
 * @param params
 * @return 
 */
export const UpdateRescueRecord = params => http.post(urls.UpdateRescueRecord, params);

/**
 * @description 更新维护记录
 * @param params 
 * @returns 
 */
export const UpdateRescueCareMain = params => http.post(urls.UpdateRescueCareMain, params);

/**
 * @description: 获取主记录列表
 * @param params
 * @return 
 */
export const GetPatientRescueRecords = params => http.get(urls.GetPatientRescueRecords, params);

/**
 * @description: 获取评估模板
 * @param params
 * @return 
 */
export const GetRescueAssessView = params => http.get(urls.GetRescueAssessView, params);

/**
 * @description: 获取维护记录列表
 * @param params
 * @return 
 */
export const GetCareMainsByRecordID = params => http.get(urls.GetCareMainsByRecordID, params);

/**
 * @description: 获取给药
 * @param params
 * @return 
 */
export const GetRescueMedications = params => http.get(urls.GetRescueMedications, params);
