/*
 * FilePath     : \src\store\modules\session.js
 * Author       : 李青原
 * Date         : 2020-05-04 19:57
 * LastEditors  : 来江禹
 * LastEditTime : 2023-04-05 17:45
 * Description  :
 */
import common from "@/utils/common";
import { PatientAuthority } from "@/utils/huiMei";
export default {
  namespaced: true,
  state: {
    mqMessageCount: common.session("mqMessageCount"), // 当前用户MQ消息数量
    currentPatient: common.session("currentPatient"), // 当前病人
    patientInfo: common.session("patientInfo"), // 当前病人
    user: common.session("user"), // 当前用户
    hospitalList: common.storage("hospitalList"), // 当前hospital信息
    hospitalInfo: common.storage("hospitalInfo"), // 当前hospital信息
    languageList: common.storage("languageList"), // 系统语言集合
    language: common.storage("language"), // 系统语言
    readOnly: common.session("readOnly"), // 只读
    remBaseSize: common.session("remBaseSize"), // px转rem 比例
    loginParams: common.session("loginParams"), // 登录参数，为系统更新后自动重新登录准备
    externalParams: common.session("externalParams"), // 第三方跳转CCC携带参数，为系统更新后自动重新登录准备
    stationList: common.session("stationList"), // 病区列表
    deptList: common.session("deptList"), // 科室列表
    bedList: common.session("bedList"), // 床位列表
    bedNumberWidth: common.storage("bedNumberWidth"), // 病人头组件床位宽度
    stationNurseList: common.session("stationNurseList") || {} ,// 病区护士
    specialDrawerHeightSwitch: common.session("specialDrawerHeightSwitch")//专项弹窗高度
  },
  mutations: {
    setMQMessageCount(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.mqMessageCount = value;
      common.session("mqMessageCount", value);
    },
    setUser(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.user = value;
      common.session("user", value);
    },
    setCurrentPatient(state, value) {
      if (value == undefined) {
        value = "";
      }
      //记录上一个病人
      let lastPatient = state.currentPatient;
      state.currentPatient = value;
      common.session("currentPatient", value);
      //value == "" 是为了当值为""的时候，还显示上一个病人的窗体,刚登入的时候，获取上个病人为null，特做处理
      if (
        value == "" ||
        !lastPatient ||
        lastPatient.localCaseNumber != value.localCaseNumber
      ) {
        //进行病人登录认证
        PatientAuthority(value, state.user);
      }
    },
    setPatientInfo(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.patientInfo = value;
      common.session("patientInfo", value);
    },
    setHospitalList(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.hospitalList = value;
      common.storage("hospitalList", value);
    },
    setHospitalInfo(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.hospitalInfo = value;
      common.storage("hospitalInfo", value);
    },
    setLanguageList(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.languageList = value;
      common.storage("languageList", value);
    },
    setLanguage(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.language = value;
      common.storage("language", value);
    },
    setReadOnly(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.readOnly = value;
      common.session("readOnly", value);
    },
    setRemBaseSize(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.remBaseSize = value;
      common.session("remBaseSize", value);
    },
    setLoginParams(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.loginParams = value;
      common.session("loginParams", value);
    },
    setExternalParams(state, value) {
      if (value == undefined) {
        value = "";
      }
      state.externalParams = value;
      common.session("externalParams", value);
    },
    setStationList(state, value) {
      if (!value) {
        value = "";
      }
      if (!state.stationList) {
        state.stationList = {};
      }
      if (typeof value === "string") {
        delete state.stationList[value];
      } else {
        Object.assign(state.stationList, value);
      }
      common.session("stationList", state.stationList);
    },
    setDeptList(state, value) {
      if (!value) {
        value = [];
      }
      state.deptList = value;
      common.session("deptList", value);
    },
    setBedList(state, value) {
      if (!value) {
        value = "";
      }
      state.bedList = value;
      common.session("bedList", value);
    },
    setBedNumberWidth(state, value) {
      if (!value) {
        value = "";
      }
      state.bedNumberWidth = value;
      common.storage("bedNumberWidth", value);
    },
    setStationNurseList(state, value) {
      if (!value) {
        value = "";
      }
      state.stationNurseList = value;
      common.session("stationNurseList", value);
    },
    setSpecialDrawerHeightSwitch(state, value) {
      if (!value) {
        value = "";
      }
      state.specialDrawerHeightSwitch = value;
      common.session("specialDrawerHeightSwitch", value);
    }
  }
};
