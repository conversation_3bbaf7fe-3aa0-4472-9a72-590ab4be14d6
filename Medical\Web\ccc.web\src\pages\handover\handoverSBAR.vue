<!--

 * FilePath     : \src\pages\handover\handoverSBAR.vue
 * Author       : 李青原
 * Date         : 2020-05-05 14:57
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-16 16:03
 * Description  : 病人sbar交班
                  2021-04-12出院小结不再绑定需要先问题评价
                  判断是否与HIS对接为hisFlag,对接时不出现返回钮
                  2021-07-28加上preview参数用来判断tabsLayout是否只能预览,默许可编辑
                  2022-01-25 2397 因应需要可以透过不同科室对应不同出院内容把handocerType与recordsCode分开 -正元
-->
<template>
  <base-layout v-loading="loadingAll" element-loading-text="加载中……">
    <div slot="header" class="sbar-header">
      <span>评估日期:</span>
      <el-date-picker
        v-model="handoverDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        class="date-picker"
        :clearable="false"
        :disabled="model == 'component'"
      ></el-date-picker>
      <span>评估时间:</span>
      <el-time-picker
        v-model="handoverTime"
        :clearable="false"
        format="HH:mm"
        value-format="HH:mm"
        placeholder="选择时间"
        style="width: 80px"
        :disabled="model == 'component'"
      ></el-time-picker>
      <!-- 模板选择 -->
      <span>{{ handOverTypeText }}:</span>
      <el-select
        v-model="templateValue"
        :disabled="typeDisabled"
        placeholder="请选择"
        @change="changeTemplate"
        style="width: 150px"
      >
        <el-option
          v-for="item in templateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <div class="button-line">
        <el-button v-if="activeName == 'assessment' && showVitalButtonFlag" type="primary" @click="openClinic">
          生命体征
        </el-button>
        <el-button type="primary" v-if="!disabled && checkResult" icon="iconfont icon-save-button" @click="save">
          保 存
        </el-button>
        <el-button
          class="edit-button"
          icon="iconfont icon-pdf"
          v-if="handoverType == 'DischargeAssess' && updateFlag"
          @click="getPDF"
        >
          离院小结
        </el-button>

        <el-button
          v-if="handoverID && handoverID.indexOf('handover-') == -1 && handoverType == 'DischargeAssess' && checkResult"
          @click="deleteHandOver"
          icon="iconfont icon-del"
          type="danger"
        >
          删除
        </el-button>
        <el-button
          v-if="!hisFlag && model == 'page'"
          title="返回上一页"
          class="print-button"
          icon="iconfont icon-back"
          @click="goBack"
        >
          返回
        </el-button>
      </div>
    </div>
    <div class="handover-sbar" v-loading="loading" :element-loading-text="loadingText">
      <el-tabs class="handover-tabs" v-model="activeName" @tab-click="activeControl">
        <el-tab-pane
          v-for="(item, index) in paneList"
          :key="index"
          :label="item.label"
          :name="item.name"
          class="tab-pane"
        ></el-tab-pane>
      </el-tabs>
      <div class="pane-content">
        <div v-if="activeName == 'assessment'" :class="showRisk ? 'assessment-wrap-risk' : 'assessment-wrapno-risk'">
          <div class="assess">
            <tabs-layout
              ref="tabsLayout"
              :template-list="templateDatas"
              :disabled="preview"
              @button-click="buttonClick"
              @button-record-click="buttonRecordClick"
              @change-values="changeValues"
              @checkTN="checkTN"
              :checkFlag="true"
            />
          </div>
          <div class="risk" v-if="showRisk">
            <div class="risk-title">A-风险评估</div>
            <el-table class="risk-table" border stripe @row-click="getRiskData" :show-header="false" :data="riskTable">
              <el-table-column prop="recordName"></el-table-column>
              <el-table-column width="40">
                <template slot-scope="scope">
                  {{ scope.row.showPointFlag ? scope.row.point : "" }}
                </template>
              </el-table-column>
              <el-table-column prop="scoreRangeContent" width="50"></el-table-column>
              <el-table-column prop="confirm">
                <template slot-scope="scope">{{ scope.row.confirm == true ? "已确认" : "未确认" }}</template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div v-if="activeName == 'detailed'" class="handover-div">
          {{ handoverData.status }}
          <div class="title">交班明细:</div>
          <div class="handover">
            <handover :disabled="false" ref="handover" @need-risk="getRiskData" :componentData="handoverData" />
          </div>
        </div>
        <div v-if="activeName == 'evaluate'" class="evaluate-div">
          <!-- 添加判断的的原因是在病人被更改前会将先置为undefined,此时获取信息会报错 -->
          <patient-evaluation
            ref="evaluate"
            :inpatientid="patient ? patient.inpatientID : ''"
            :stationid="patient ? patient.stationID : 0"
            :showcommit="false"
            :all-Flag="false"
            :assessDate="handoverDate"
            :assessTime="handoverTime"
          ></patient-evaluation>
        </div>
      </div>

      <el-dialog title="离院小结" v-dialogDrag :close-on-click-modal="false" :visible.sync="pdfvisiable" fullscreen>
        <iframe
          id="printIframe"
          class="pdf-show"
          :src="ftpPath"
          type="application/x-google-chrome-pdf"
          width="100%"
          height="100%"
          frameborder="1"
          scrolling="auto"
        />
      </el-dialog>
      <!-- 风险保存  -->
      <el-dialog
        v-loading="riskLoading"
        :element-loading-text="loadingText"
        v-dialogDrag
        :close-on-click-modal="false"
        width="60%"
        :title="dialogTitle"
        :visible.sync="updateVisible"
        custom-class="no-footer"
      >
        <risk-component :params="conponentParams" @result="getChecked"></risk-component>
      </el-dialog>
      <!--弹出按钮链接框-->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonName"
        :visible.sync="showButtonDialog"
        fullscreen
        custom-class="no-footer"
      >
        <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
      </el-dialog>
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonRecordTitle"
        :visible.sync="showButtonRecordDialog"
        custom-class="no-footer"
      >
        <risk-component :params="conponentParams" @result="result"></risk-component>
      </el-dialog>
    </div>
    <el-drawer
      title="生命体征"
      :modal-append-to-body="false"
      :visible.sync="showClinicFlag"
      :destroy-on-close="true"
      direction="btt"
      size="60%"
      custom-class="clinic-drawer"
      :wrapperClosable="false"
    >
      <el-table
        class="risk-table"
        border
        stripe
        :data="apparatusTable"
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <el-table-column prop="perfromTime" label="时间" align="center">
          <template slot-scope="scope">{{ scope.row.PerfromTime ? scope.row.PerfromTime.value : "" }}</template>
        </el-table-column>
        <el-table-column prop="temperature" label="体温" align="center">
          <template slot-scope="scope">{{ scope.row.Temperature ? scope.row.Temperature.value : "" }}</template>
        </el-table-column>
        <el-table-column prop="pulseRate" label="脉率" align="center">
          <template slot-scope="scope">{{ scope.row.PulseRate ? scope.row.PulseRate.value : "" }}</template>
        </el-table-column>
        <el-table-column prop="heartRate" label="心率" align="center">
          <template slot-scope="scope">{{ scope.row.HeartRate ? scope.row.HeartRate.value : "" }}</template>
        </el-table-column>
        <el-table-column prop="breathe" label="呼吸" align="center">
          <template slot-scope="scope">{{ scope.row.Breathe ? scope.row.Breathe.value : "" }}</template>
        </el-table-column>
        <el-table-column prop="systolic" label="收缩压" align="center">
          <template slot-scope="scope">{{ scope.row.Systolic ? scope.row.Systolic.value : "" }}</template>
        </el-table-column>
        <el-table-column prop="diastolic" label="舒张压" align="center">
          <template slot-scope="scope">{{ scope.row.Diastolic ? scope.row.Diastolic.value : "" }}</template>
        </el-table-column>
        <el-table-column prop="oxyhemoglobin" label="血氧饱和度" align="center">
          <template slot-scope="scope">{{ scope.row.Oxyhemoglobin ? scope.row.Oxyhemoglobin.value : "" }}</template>
        </el-table-column>
      </el-table>
    </el-drawer>
  </base-layout>
</template>
<script>
import { mapGetters } from "vuex";
import BaseLayout from "@/components/BaseLayout";
import tabsLayout from "@/components/tabsLayout/index";
import Handover from "@/components/handoverSBAR";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import shiftSelector from "@/components/selector/shiftSelector";
import PatientEvaluation from "@/pages/nursingEvaluation/patientEvaluation";
import { GetButtonData } from "@/api/Assess";
import { GetNowStationShiftData } from "@/api/StationShift";
import {
  GetPatientSBARHandovers,
  SaveSBARHandover,
  UpdateSBARHandover,
  GetPatientAssessHandovers,
  UpdateAssessHandover,
  SaveAssessHandover,
  GetHandoverIDByOperationNo,
  DeleteHandOverByHandOverID,
} from "@/api/Handover";
import { GetClinicalBySettingTypeCodeAndValue, GetHandOverType } from "@/api/Setting";
import { GetDischargeAssessPDF } from "@/api/Document";
import { DelPatientWoundDataByHandoverID } from "@/api/WoundRecord";
import { DelPatientTubeDataByHandoverID } from "@/api/Tube";
import { GetCalcRiskAssessTotal } from "@/api/Assess";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { GetVitalSignByInpatientID } from "@/api/operation";
export default {
  components: {
    tabsLayout,
    Handover,
    riskComponent,
    shiftSelector,
    BaseLayout,
    PatientEvaluation,
  },
  props: {
    usingType: {
      type: String,
      default: undefined,
    },
    defaultHandoverType: {
      type: String,
      default: undefined,
    },
  },
  watch: {
    "patient.inpatientID": {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        if (!this.initFlag) {
          this.init();
        } else {
          this.initFlag = true;
        }
      },
    },
    showButtonDialog(newVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
        //添加过敏药物后返回刷新病人头 显示新增过敏药物
        // 旧的模板是185，新的换成4570了
        if (this.buttonAssessListID == 185 || this.buttonAssessListID == 4570) {
          this.initFlag = false;
          this._sendBroadcast("refreshInpatient");
        }
      }
    },
    // 路由不同重新刷新页面
    $route: {
      handler(val, oldval) {
        if (val.fullPath != oldval.fullPath) {
          this.$router.go(0);
        }
      },
      // 深度观察监听
      deep: true,
    },
    /**
     * description: 死亡处理
     * return {*}
     */
    deathFlag: {
      handler(val) {
        this.deathSwitch && this.deathDealWith(val);
      },
    },
  },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
      user: "getUser",
      token: "getToken",
      currentPatient: "getCurrentPatient",
    }),
  },
  data() {
    return {
      showVitalButtonFlag: false,
      apparatusTable: [],
      riskTable: [],
      templateDatas: [],
      handoverData: {
        handoverInfo: {
          situation: "",
          background: "",
          recommendation: "",
          bodyPartImage: "",
          assement: "",
        },
        riskAssessResult: [],
      },
      //交班类型
      handoverType: "",
      dialogTitle: "",
      // 修改风险dialog变量
      updateVisible: false,
      //风险数据
      riskList: [],
      //风险保存loading
      loading: false,
      //当前风险recordsListID
      recordsListID: "",
      //评估选中数组
      assessDatas: [],
      //交班日期时间
      handoverDate: undefined,
      handoverTime: undefined,
      //班次ID
      handoverShiftID: "",
      //病区ID
      stationID: undefined,
      //存在交班ID即请求对应交班内容
      handoverID: "",
      //页签控制器
      activeName: "",
      //handoverID存在为true,否则为false
      updateFlag: false,
      //风险保存API
      riskLoading: false,
      buttonAssessListID: "",
      buttonName: "",
      showButtonDialog: false,
      // 是否可修改
      disabled: false,
      ftpPath: "",
      //pdf展示弹窗
      pdfvisiable: false,
      //护理评价页面存在护理问题 默认不存在
      haveProblem: false,
      loadingText: "",
      paneSetting: {
        Risk: { paneName: "", showFlag: false },
        Evaluate: { paneName: "", showFlag: false },
        Assessment: { paneName: "", showFlag: false },
        Handover: { paneName: "", showFlag: false },
      },
      paneList: [],
      paneListIndex: undefined,
      checkTNFlag: true,
      loadingAll: false,
      shiftInfo: {},
      // 是否his系统跳转
      hisFlag: false,
      // his手术序号
      hisOperationNo: undefined,
      //CareDirect 手术唯一码
      patientOperationID: undefined,
      //显示风险
      showRisk: true,
      handoffNurseID: undefined,
      // 显示BR类弹窗
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR项
      brItem: undefined,
      templateOptions: [],
      templateValue: "",
      //2021-07-28新增tabsLayout交班内容是否可编辑,默许可以
      preview: false,
      handoffNurseID: undefined,
      checkResult: true,
      model: "page",
      recordsCode: "",
      handOverTypeText: "转运交接",
      initFlag: true,
      typeDisabled: false,
      //死亡
      deathSwitch: false,
      deathFlag: false,
      deathAssessListID: 3578,
      daethNotAssessBookMarkID: ["20", "40"],
      daethNotAssessBookMarkViews: [],
      conponentParams: undefined,
      showClinicFlag: false,
    };
  },
  mounted() {
    let params = {
      settingTypeCode: "SwitchForHandoverVitalSignButton",
    };
    GetSettingSwitchByTypeCode(params).then((res) => {
      if (this._common.isSuccess(res)) {
        this.showVitalButtonFlag = res.data;
      }
    });
  },
  // 解决当前子路由不触发页面钩子
  async created() {
    this.model = this.$route.query.model ? this.$route.query.model : this.usingType;
    this.handoverType = this.$route.query.handoverType ? this.$route.query.handoverType : this.defaultHandoverType;
    this.recordsCode = this.$route.query.recordsCode;
    if (!this.handoverType) {
      this.handoverType = "WardHandover";
    }
    if (this.handoverType) {
      if (!this.recordsCode) {
        this.recordsCode = this.handoverType;
      }
      await this.GetTransferSetting();
      this.templateValue = this.recordsCode;
    }
    if (this.$route.query.preview) {
      this.preview = this.$route.query.preview;
    }
    //类型是否可切换
    if (this.$route.query.typeDisabled) {
      this.typeDisabled = eval(this.$route.query.typeDisabled);
    }
    //出院小结不允许切换类型
    if (this.handoverType == "DischargeAssess") {
      this.typeDisabled = true;
    }
    this.init();
  },
  methods: {
    async init() {
      //勾选死亡是否特殊处理开关
      this.getDeathSwitch();
      this.handoverID = this.$route.query.handoverID ? this.$route.query.handoverID : "";
      if (this.handoverID) {
        this.disabled = this.$route.query.disabled;
      }
      this.patientOperationID = this.$route.query.patientOperationID;
      this.hisOperationNo = this.$route.query.hisOperationNo;
      this.handoffNurseID = this.$route.query.nurseID;
      this.hisFlag = this.$route.query.hisFlag;
      if (this.hisOperationNo && this.hisFlag && !this.handoverID) {
        await this.getHandoverIDByOperationNo();
      }
      if (this.preview) {
        this.disabled = true;
      }
      this.stationID = this.user.stationID;
      this.handoverDate = this._datetimeUtil.getNowDate();
      this.handoverTime = this._datetimeUtil.getNowTime("hh:mm");
      //解决已出院评价病人班别不显示 --GPC
      await this.getNowShiftData();
      //此处根据handoverType调取模板
      this.loading = true;
      this.loadingText = "加载中……";
      // 检查页签配置
      //修复保存问题  --GPC
      if (this.handoverID && this.handoverID.indexOf("handover-") == -1) {
        //修改不可切换病人
        this._sendBroadcast("setPatientSwitch", false);
        this.updateFlag = true;
        //当离院小结跳转时，没有交班日期
        if (!this.hisFlag) {
          this.handoverDate = this.$route.query.handoffDate;
          this.handoverTime = this.$route.query.handoffTime;
          this.handoverShiftID = this.$route.query.handoverShift;
        }
      } else {
        if (this.$route.query.inpatientID) {
          this._sendBroadcast("setPatientSwitch", false);
        }
        this.updateFlag = false;
        this.handoverID = "handover-" + this._common.guid();
      }
      //检查页签配置
      await this.getSetting().then(() => {
        if (this.paneList && this.paneList.length > 0) {
          if (this.paneList[0].value == "Assessment") {
            this.activeControl({ name: "assessment" });
          } else if (this.paneList[0].value == "Handover") {
            this.activeControl({ name: "detailed" });
          } else if (this.paneList[0].value == "Evaluate") {
            this.activeControl({ name: "evaluate" });
          }
        }
      });
      this.loading = false;
    },
    changeValues(datas) {
      this.assessDatas = datas;
      this.deathFlag =
        this.deathSwitch &&
        this.handoverType == "DischargeAssess" &&
        !!this.assessDatas.find((item) => item.assessListID == this.deathAssessListID);
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    async getAssess() {
      //修复进入页面报错问题 --GPC
      if (!this.patient && !this.currentPatient) {
        return;
      }
      // 根据handoverType获取模板
      this.loadingText = "加载中……";
      this.loadingAll = true;
      let params = {
        handoverType: this.handoverType,
        recordsCode: this.recordsCode,
        handoverID: this.handoverID,
        inpatientID: this.patient?.inpatientID ?? this.currentPatient.inpatientID,
        sourceType: "HandoverBR",
        patientOperationID: this.patientOperationID,
      };
      if (!params.recordsCode) {
        params.recordsCode = params.handoverType;
      }
      let data = undefined;
      this.templateDatas = [];
      await GetPatientAssessHandovers(params).then((result) => {
        this.loadingAll = false;
        this.initFlag = false;
        if (this._common.isSuccess(result)) {
          data = result.data;
          // 如果有handoverid 标识
          this.handoverID = data.handoverID;
          this.showRisk = data.showRisk;
          this.recordsCode = data.recordsCode;
          this.templateValue = data.recordsCode;
          if (this.handoverID.indexOf("handover-") == -1) {
            this.updateFlag = true;
          }
          this.$set(this, "handoverData", result.data.handoverRecord);
          this.templateDatas = data.layoutGroupList;
          this.riskTable = data.handoverRecord.riskAssessResult;
          //初始化处理死亡勾选
          this.deathSwitch && this.deathDealWith(this.deathFlag);
          if (data.handoverRecord.handoverInfo.handonDate) {
            this.disabled = true;
          }
          if (data.handoverRecord.handoverInfo.handoffNurseID) {
            this.handoffNurseID = data.handoverRecord.handoverInfo.handoffNurseID;
          }
        }
      });
      this.checkResult = await this._common.checkActionAuthorization(this.user, this.handoffNurseID);
      if (!this.checkResult) {
        this.disabled = true;
      }
      return data;
    },
    getSBAR() {
      this.loadingText = "加载中……";
      this.loading = true;
      let params = null;
      if (this.updateFlag) {
        params = {
          handoverID: this.handoverID,
          recordsCode: this.recordsCode,
        };
      } else {
        params = {
          recordsCode: this.recordsCode,
          inpatientID: this.patient.inpatientID,
          shiftID: this.handoverShiftID,
          shiftDate: this.shiftDate,
        };
      }
      GetPatientSBARHandovers(params).then((response) => {
        if (this._common.isSuccess(response)) {
          //填充日期
          if (!response.data) return;
          this.$set(this, "handoverData", response.data.handoverRecord);
        }
        this.loading = false;
      });
    },
    //保存方法
    async save(flag) {
      this.loadingText = "保存中……";
      this.loadingAll = true;
      // 判断页签名称
      switch (this.activeName) {
        case "evaluate":
          this.$refs.evaluate.commit();
          this.getSetting();
          this.loadingAll = false;
          break;
        case "assessment":
          if (this.haveProblem) {
            this._showTip("warning", "请先评价护理问题！");
            this.loadingAll = false;
            break;
          }
          //检核是否有风险未保存
          if (this.showRisk) {
            if (flag != "riskSave") {
              if (!this.checkRiskSave()) {
                this._showTip("warning", "您有未确认风险评分");
                this.loadingAll = false;
                return;
              }
            }
          }
          this.saveAssess(flag);
          break;
        case "detailed":
          this.saveSBAR();
          break;
        default:
          this.loadingAll = false;
      }
    },

    // 获取需要保存的明细
    getSaveDetail() {
      for (let index = 0; index < this.paneList.length; index++) {
        const item = this.paneList[index];
        if (item.name == "assessment") {
          this.paneListIndex = index;
        }
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return undefined;
      }
      let details = [];
      let flag = true;
      for (let i = 0; i < this.assessDatas.length; i++) {
        let content = this.assessDatas[i];
        // 按钮不处理
        if (content.controlerType.trim() == "B") {
          continue;
        }
        let result = this._common.checkAssessTN(content);
        if (!result.flag) {
          flag = false;
          break;
        }
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          controlerType: content.controlerType,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        details.push(detail);
      }
      if (!flag) {
        return [];
      }
      return details;
    },

    async saveAssess(flag) {
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        this.loading = false;
        return;
      }
      this.loadingText = "保存中……";
      this.riskLoading = true;
      // 至少填写一个评估项
      if (this.assessDatas.length == 0) {
        this._showTip("warning", "评估必须填写内容！");
        this.updateVisible = false;
        this.riskLoading = false;
        this.loadingAll = false;
        return;
      }
      let list = this.getSaveDetail();
      if (!list || list.length <= 0) {
        this.updateVisible = false;
        this.riskLoading = false;
        this.loadingAll = false;
        return;
      }
      //是修改且无前缀事进行修改
      if (this.updateFlag && this.handoverID.indexOf("handover-") == -1) {
        let params = {
          AssessList: list,
          ScoreID: flag == "riskSave" ? this.recordsListID : undefined,
          ScoreDatas: flag == "riskSave" ? this.riskList : undefined,
          inpatientID: this.patient.inpatientID,
          ID: this.handoverID,
          // shiftDate: this.handoverDate,
          // shiftID: this.handoverShiftID,
          SourceID: this.hisOperationNo,
          HandoffTime: this.handoverTime,
          HandoffDay: this.handoverDate,
          HandoffNurseID: this.handoffNurseID,
          HandoverType: this.handoverType,
        };
        await UpdateAssessHandover(params).then((response) => {
          this.updateVisible = false;
          this.loadingAll = false;
          if (this._common.isSuccess(response)) {
            if (response.data) {
              this._showTip("success", "保存成功！");
              this.getAssess().then((data) => {
                if (!data) return;
                if (data && data.layoutGroupList) {
                  this.riskTable = data.handoverRecord.riskAssessResult;
                  this.templateDatas = data.layoutGroupList;
                }
              });
              //除风险保存外跳转到下一个页面
              if (flag != "riskSave") {
                this.getSBAR();
                if (this.paneSetting["Handover"].showFlag) {
                  this.activeName = "detailed";
                }
              }
            } else {
              this._showTip("warning", "保存失败！");
            }
          }
          this.riskLoading = false;
          this.riskLoading = false;
          //this.loadingAll = false;
          this.loadingText = "加载中……";
        });
      } else {
        if (this.updateFlag) {
          let params = {
            handOverID: this.$route.query.handoverID,
          };
          await DeleteHandOverByHandOverID(params).then();
        }
        let params = {
          patientHandOver: {
            AssessList: list,
            ScoreID: flag == "riskSave" ? this.recordsListID : undefined,
            ScoreDatas: flag == "riskSave" ? this.riskList : undefined,
            inpatientID: this.patient.inpatientID,
            ID: this.handoverID,
            SourceID: this.hisOperationNo,
            HandoffDay: this.handoverDate,
            HandoffTime: this.handoverTime,
            HandoffNurseID: this.handoffNurseID,
            HandoverType: this.handoverType,
          },
          recordsCode: this.recordsCode,
        };
        await SaveAssessHandover(params).then((result) => {
          this.loadingAll = false;
          if (this._common.isSuccess(result)) {
            if (result.data != "") {
              this.handoverID = result.data;
              this.updateFlag = true;
              this._showTip("success", "保存成功！");
              this.updateVisible = false;
              this.getAssess().then((data) => {
                if (!data) return;
                if (data && data.layoutGroupList) {
                  this.riskTable = data.handoverRecord.riskAssessResult;
                  this.templateDatas = data.layoutGroupList;
                }
              });
              //除风险保存外跳转到下个页面
              if (flag != "riskSave") {
                this.getSBAR();
                if (this.paneSetting["Handover"].showFlag) {
                  this.activeName = "detailed";
                }
              }
            }
            if (result.data == "") {
              this._showTip("success", "保存失败！");
              return;
            }
          }
          this.riskLoading = false;
          this.loading = false;
          this.loadingText = "加载中……";
        });
      }
    },
    async saveSBAR() {
      this.paneList.forEach((item, index) => {
        if (item.name == "detailed") {
          this.paneListIndex = index;
        }
      });
      let handoverValue = this.$refs.handover.getValue();
      // 在出院小结页面内容没有渲染完成，去点记保存按钮时，recordsCode会为空值
      if (!handoverValue || !handoverValue.recordsCode) {
        this._showTip("warning", "数据加载中，请稍后保存！");
        this.loadingAll = false;
        return;
      }
      if (!handoverValue.wordNumberFlag) {
        this._showTip("warning", "请精简内容！");
        this.loadingAll = false;
        return;
      }
      handoverValue.handoverType = this.handoverType;
      handoverValue.chartNo = this.patient.chartNo;
      handoverValue.bedNumber = this.patient.bedNumber;
      handoverValue.hosipital = this.patient.hospital;
      handoverValue.inpatientID = this.patient.inpatientID;
      handoverValue.HandoffDay = this.handoverDate;
      handoverValue.handoffTime = this.handoverTime;
      handoverValue.stationID = this.patient.stationID;
      //调用修改
      if (this.updateFlag) {
        handoverValue.id = this.handoverID;
        UpdateSBARHandover(handoverValue).then((response) => {
          this.loadingAll = false;
          this.loading = false;
          this.loadingText = "加载中……";
          //修改SBAR
          if (this._common.isSuccess(response)) {
            if (response.data) {
              this._showTip("success", "保存成功！");
              if (this.paneSetting["Evaluate"].showFlag) {
                this.activeName = "evaluate";
              }
            } else {
              this._showTip("warning", "保存失败！");
            }
          }
          this.getSBAR();
        });
      }
      //调用新增
      else {
        SaveSBARHandover(handoverValue).then((response) => {
          if (this._common.isSuccess(response)) {
            if (response.data) {
              this._showTip("success", "保存成功！");
              if (this.paneSetting["Evaluate"].showFlag) {
                this.activeName = "evaluate";
              }
            } else {
              this._showTip("warning", "保存失败！");
            }
          }
          this.getSBAR();
          this.loading = false;
          this.loadingAll = false;
          this.loadingText = "加载中……";
        });
      }
    },
    getRiskData(row) {
      if (this.model == "component") {
        return;
      }
      this.recordsListID = row.recordID;
      this.dialogTitle = row.recordName;
      this.updateVisible = true;
      this.conponentParams = {
        patientInfo: this.patient,
        showPoint: row.showPointFlag,
        showTime: true,
        showStyle: row.showStyle,
        showBar: true,
        recordListID: row.recordID,
        recordsCode: row.recordsCode,
        sourceID: this.updateFlag ? this.handoverID : "",
        readOnly: this.disabled,
        submitFlag: false,
      };
    },
    getChecked(flag, val) {
      this.riskList = flag ? val : [];
      this.save("riskSave");
    },
    async activeControl(tab, event) {
      switch (tab.name) {
        case "evaluate":
          this.activeName = "evaluate";
          break;
        case "assessment":
          this.activeName = "assessment";
          await this.getAssess().then((data) => {
            if (!data) return;
            if (data && data.layoutGroupList) {
              //记录已存在则回显日期和班别
              if (data.handoffDate) {
                this.handoverDate = data.handoffDate;
                this.handoverTime = data.handoverRecord.handoverInfo.handoffTime;
              }
              if (data.shift) {
                this.getShiftID(data.shift);
              }
              this.riskTable = data.handoverRecord.riskAssessResult;
            }
            if (this.haveProblem) {
              if (this.paneSetting["Evaluate"].showFlag) {
                this.activeName = "evaluate";
              }
            } else if (this.templateDatas.length == 0) {
              this.activeControl({ name: "detailed" });
            } else {
              if (this.paneSetting["Assessment"].showFlag) {
                this.activeName = "assessment";
              }
            }
          });
          break;
        case "detailed":
          this.activeName = "detailed";
          this.getSBAR();
          break;
      }
    },
    async getSetting() {
      // 生成对象
      let params = {
        settingTypeCode: "HandoverFunctionShift",
        typeValue: this.recordsCode,
      };
      await GetClinicalBySettingTypeCodeAndValue(params).then((response) => {
        if (this._common.isSuccess(response)) {
          let data = response.data;
          if (data.length == 0) return;
          this.paneList = [];
          for (let i = 0; i < data.length; i++) {
            if (!this.paneSetting[data[i].settingValue]) {
              continue;
            }
            this.paneSetting[data[i].settingValue].paneName = data[i].description;
            this.paneSetting[data[i].settingValue].showFlag = true;
            let value = data[i].settingValue;
            let param = {
              label: data[i].description,
              value: data[i].settingValue,
              name:
                value == "Assessment"
                  ? "assessment"
                  : value == "Handover"
                  ? "detailed"
                  : value == "Evaluate"
                  ? "evaluate"
                  : "",
            };
            this.paneList.push(param);
          }
        }
      });
    },
    getPDF() {
      this.loadingAll = true;
      let params = {
        inpatientID: this.patient.inpatientID,
        handoverID: this.handoverID,
      };
      return GetDischargeAssessPDF(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.ftpPath = response.data;
          this.pdfvisiable = true;
        }
        this.loadingAll = false;
      });
    },
    async goBack() {
      //保存后就不清除添加的导管
      // 页面初始化失败
      if (this.handoverID == "") {
        this.$router.go(-1);
        return;
      } else if (!this.updateFlag) {
        let params = {
          handoverID: this.handoverID.replace("handover-", ""),
          inpatientID: this.patient.inpatientID,
          user: this.user.userID,
        };
        //删除未保存伤口和导管
        await DelPatientTubeDataByHandoverID(params).then((response) => {
          if (this._common.isSuccess(response)) {
            return;
          }
        });
        await DelPatientWoundDataByHandoverID(params).then((response) => {});
        this.$router.go(-1);
      } else {
        this.$router.go(-1);
      }
    },
    /**
     * description: 获得字符串实际长度，中文2，英文1
     * param {*} str 要获得长度的字符串
     * return {*}
     */
    getlength(str) {
      var realLength = 0,
        len = str.length,
        charCode = -1;
      for (var i = 0; i < len; i++) {
        charCode = str.charCodeAt(i);
        if (charCode >= 0 && charCode <= 128) realLength += 1;
        else realLength += 2;
      }
      return realLength;
    },

    buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      this.paneList.forEach((item, index) => {
        this.paneListIndex = index;
      });
      if (!url) {
        return;
      }
      if (url.indexOf("?") == -1) {
        url += "?handoverID=" + this.handoverID.replace("handover-", "");
      } else {
        url += "&handoverID=" + this.handoverID.replace("handover-", "");
      }
      url += "&userID=" + this.user.userID + "&isDialog=true";
      if (this.$route.query.isDischarge) {
        url += "&isDischarge=true";
        if (this.$route.query.inpatientID && url.indexOf("inpatientID") == -1) {
          url += "&inpatientID=" + this.$route.query.inpatientID;
        }
      }
      if (this.patient.bedNumber.replace(/\+/g, "%2B")) {
        url += "&bedNumber=" + this.patient.bedNumber.replace(/\+/g, "%2B");
      }
      if (this.token) {
        url += "&token=" + this.token;
      }
      if (this.hisOperationNo) {
        url += "&hisOperationNo=" + this.hisOperationNo;
      }
      url += "&sourceID=" + this.handoverID?.replace("handover-", "");
      this.showButtonDialog = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    async buttonRecordClick(content) {
      this.paneListIndex = this.paneList?.length ? this.paneList?.length - 1 : undefined;
      let record = content.brParams || {};
      this.brItem = content;
      this.buttonRecordTitle = content.itemName;
      this.conponentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: "HandoverBR",
        sourceID: this.handoverID.replace("handover-", ""),
        assessTime:
          this._datetimeUtil.formatDate(this.handoverDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.handoverTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    result(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brItem.assessListID);
        // 更新右侧风险内容
        this.getCalcRiskAssessTotal();
      }
    },
    getCalcRiskAssessTotal() {
      let params = {
        inpatientID: this.patient.inpatientID,
        sourceID: this.handoverID.replace("handover-", ""),
        sourceType: "handover",
      };
      GetCalcRiskAssessTotal(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.riskTable = result.data;
        }
      });
    },
    async getButtonValue(assessListID) {
      let item = "";
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.handoverType,
        assessListID: assessListID,
        sourceID: this.handoverID.replace("handover-", ""),
        sourceType: "HandoverBR",
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    async getNowShiftData() {
      // #mzy# 解决HIS跳转时，无病区班别问题
      let params = { stationID: undefined };
      if (this.hisFlag) {
        params.stationID = this.currentPatient.stationID;
        this.stationID = this.currentPatient.stationID;
      }
      await GetNowStationShiftData(params).then((data) => {
        if (this._common.isSuccess(data)) {
          let result = data.data;
          this.shiftInfo = result;
          this.shiftDate = result.shiftDate;
          //当前班别(默许班别
          this.handoverShiftID = result.nowShift.id;
        }
      });
    },
    // 同步班别
    getShiftID(shiftCode) {
      if (this.shiftInfo.stationShifts) {
        let array = this.shiftInfo.stationShifts;
        array.forEach((ele) => {
          if (ele.shift.trim() == shiftCode.trim()) {
            this.handoverShiftID = ele.id;
          }
        });
      }
    },
    //数据处理方法
    getUpdateData(data) {
      //创建tableData
      let tableData = [];
      // 创建一个数组
      var childrenArray = [];
      //创建首层对象
      var obj = {
        content: "",
        recordsFormatID: -1,
        radioCheck: "",
        checkedList: [],
        haveCheck: false,
      };
      // 最后数组克隆
      //创建一个判断值
      var content = "";
      // 数据处理部分
      for (let i = 0; i < data.headerExtention.length; i++) {
        let item = data.headerExtention[i];
        const recordsFormatID = item[1].recordsFormatID + "";
        // 判断是否同属于一栏
        if (item[0].content == obj.content) {
          // 同一组
          // 创建对象:
          let lineItem = this.creatItem(item[1], data.rows[0][recordsFormatID]);
          if (lineItem.checkFlag) {
            if (lineItem.child.controlerType == "R") {
              obj.radioCheck = lineItem.child.recordsFormatID;
            }
            if (lineItem.child.controlerType == "C") {
              obj.checkedList.push(lineItem.child.recordsFormatID);
            }
            obj.haveCheck = true;
          }
          childrenArray.push(this._common.clone(lineItem.child));
          if (i == data.headerExtention.length - 1) {
            let children = this._common.clone(childrenArray);
            obj.childrenArray = children;
            let objItem = this._common.clone(obj);
            tableData.push(objItem);
          }
          continue;
        } else {
          // 不相等
          let children = this._common.clone(childrenArray);
          obj.childrenArray = children;
          let objItem = this._common.clone(obj);
          tableData.push(objItem);
          childrenArray = [];
          obj.radioCheck = "";
          obj.checkedList = [];
          let lineItem = this.creatItem(item[1], data.rows[0][recordsFormatID]);
          if (lineItem.checkFlag) {
            if (lineItem.child.controlerType == "R") {
              obj.radioCheck = lineItem.child.recordsFormatID;
            }
            if (lineItem.child.controlerType == "C") {
              obj.checkedList.push(lineItem.child.recordsFormatID);
            }
            obj.haveCheck = true;
          }
          childrenArray.push(this._common.clone(lineItem.child));
          obj.content = item[0].content;
          obj.recordsFormatID = item[0].recordsFormatID;
          //最后一位存入数组
          if (i == data.headerExtention.length - 1) {
            let children = this._common.clone(childrenArray);
            obj.childrenArray = children;
            let objItem = this._common.clone(obj);
            tableData.push(objItem);
          }
        }
      }
      //清除首个无用元素
      tableData.shift();
      let riskRangeStr = data.scoreLimit;
      let riskRange = this.getRiskRange(data.scoreLimitViews);
      return {
        tableData: tableData,
        riskRangeStr: riskRangeStr,
        riskRange: riskRange,
      };
    },
    creatItem(item, row) {
      let recordsFormatID = item.recordsFormatID;
      let flag = row[recordsFormatID] == "V" ? true : false;
      let childItem = {
        assessListID: item.assessListID,
        point: item.point,
        recordsFormatID: recordsFormatID,
        content: item.content,
        controlerType: row.controlerType,
        showMessage: item.showMessage,
      };
      //如果
      return { child: childItem, checkFlag: flag };
    },
    getWatchData(data) {
      let nowcontent = "";
      let tableData = [];
      //格式化返回值
      for (let i = 0; i < data.headerExtention.length; i++) {
        let itemArray = data.headerExtention[i];
        let item = {
          content: itemArray[0].content,
          childContent: itemArray[1].content,
          point: itemArray[1].point,
          recordsFormatID: itemArray[1].recordsFormatID,
        };
        if (itemArray[0].content != nowcontent) {
          nowcontent = itemArray[0].content;
          item.headflag = true;
          item.rowSpan = itemArray[0].rowSpan;
        }
        tableData.push(item);
      }
      let checkedArray = data.rows;
      let columnArray = [];
      for (let j = 0; j < checkedArray.length; j++) {
        let obj = {
          date: checkedArray[j].date.replace(/\//g, "-"),
          time: checkedArray[j].time.substring(0, 5),
        };
        columnArray.push(obj);
        //总分计数
        let totalNumber = 0;
        for (let i = 0; i < tableData.length; i++) {
          //此处得到一个数组
          let recordsFormatID = tableData[i].recordsFormatID;
          if (checkedArray[j][recordsFormatID][recordsFormatID] == "V") {
            totalNumber += tableData[i].point;
            this.$set(tableData[i], "time" + j, "V");
          } else {
            this.$set(tableData[i], "time" + j, "");
          }
          columnArray[j].attrName = "time" + j;
        }
        //传入总分
        columnArray[j].totalNumber = totalNumber;
      }
      //生成了两个参数： tableData  和   columnArray
      let riskRangeStr = data.scoreLimit;
      let riskRange = data.scoreLimitViews;
      return {
        tableData: tableData,
        columnArray: columnArray,
        riskRange: this.getRiskRange(riskRange),
        riskRangeStr: riskRangeStr,
      };
    },
    getRiskRange(data) {
      let riskRange = data;
      let unit = 1;
      let length = riskRange.length - 1;
      if (riskRange[length].scoreUpperLimit > 99) {
        unit = 10;
      } else if (riskRange[length].scoreUpperLimit > 30) {
        unit = 5;
      } else if (riskRange[length].scoreUpperLimit > 20) {
        unit = 2;
      }
      let barArray = [];
      for (let i = 0; i < riskRange.length; i++) {
        let rangeArray = [];
        for (let j = riskRange[i].scoreLowerLimit; j <= riskRange[i].scoreUpperLimit; j++) {
          if (j == riskRange[i].scoreLowerLimit) {
            if (j == 0) {
              rangeArray.push(j);
            } else {
              rangeArray.push(j - 1);
              rangeArray.push(j);
            }
            continue;
          }
          if (j % unit == 0) {
            rangeArray.push(j);
            continue;
          }
          if (j == riskRange[i].scoreUpperLimit) {
            rangeArray.push(j);
          }
        }
        let item = {
          name: riskRange[i].rangeContent,
          color: riskRange[i].showColor,
          values: rangeArray,
        };
        barArray.push(item);
      }
      return barArray;
    },
    //检核是不是所有风险都有保存
    checkRiskSave() {
      return this.riskTable.find((item) => !item.confirm) ? false : true;
    },
    async getHandoverIDByOperationNo() {
      let params = {
        inpatientID: this.currentPatient.inpatientID,
        hisOperationNo: this.hisOperationNo,
        recordsCode: this.handoverType,
      };
      await GetHandoverIDByOperationNo(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.handoverID = result.data;
        }
      });
    },
    async deleteHandOver() {
      let params = {
        handOverID: this.handoverID,
      };
      this._deleteConfirm("", (flag) => {
        if (flag) {
          DeleteHandOverByHandOverID(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.updateFlag = false;
              this.handoverID = "handover-" + this._common.guid();
              this.activeControl({ name: "assessment" });
            }
          });
        }
      });
    },
    async changeTemplate() {
      this.handoverID = "handover-" + this._common.guid();
      this.activeName = "assessment";
      this.paneSetting = {
        Evaluate: { paneName: "", showFlag: false },
        Assessment: { paneName: "", showFlag: false },
        Handover: { paneName: "", showFlag: false },
      };
      this.recordsCode = this.templateValue;
      await this.getSetting();
      await this.getAssess();
    },
    /**
     * description:  获取转运类型
     * params {*}
     * return {*}
     */
    async GetTransferSetting() {
      let params = {
        typeValue: this.handoverType,
      };
      await GetHandOverType(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data && res.data.length) {
            this.handOverTypeText = res.data[0].label;
            this.templateOptions = res.data[0].childrenItem;
          }
        }
      });
    },
    /**
     * description: 死亡处理
     * param {*} flag
     * return {*}
     */
    deathDealWith(flag) {
      if (!this.templateDatas || !this.templateDatas.length || this.activeName != "assessment") {
        return;
      }
      let dealWithObj = {
        notAssessBookMartViews: [],
        //评估模板是否有既往史和出院宣教
        findNotAssessBookMartViews: () => {
          let notAssessBookMartViews = this.templateDatas.filter((view) =>
            this.daethNotAssessBookMarkID.includes(view.bookMarkID)
          );
          //隐藏内容保留 已被死亡取消勾选回显
          notAssessBookMartViews.length && (this.daethNotAssessBookMarkViews = notAssessBookMartViews);
          return notAssessBookMartViews;
        },
        //死亡勾选
        deathIsTrueMethod: () => {
          //数组取差集
          this.$set(this, "templateDatas", [
            ...new Set(this.templateDatas.filter((x) => !new Set(this.daethNotAssessBookMarkViews).has(x))),
          ]);
          this.showRisk = false;
        },
        //死亡取消勾选 回显评估 风险
        deathIsFalseMethod: () => {
          //找不到需要回显的内容 并且可以拿到上次已隐藏内容
          if (!dealWithObj.notAssessBookMartViews.length && this.daethNotAssessBookMarkViews.length) {
            this.$set(this, "templateDatas", [...this.templateDatas, ...this.daethNotAssessBookMarkViews]);
          }
          this.showRisk = true;
        },
      };
      dealWithObj.notAssessBookMartViews = dealWithObj.findNotAssessBookMartViews();
      flag ? dealWithObj.deathIsTrueMethod() : dealWithObj.deathIsFalseMethod();
    },
    /**
     * description: 勾选死亡是否特殊处理开关
     * return {*}
     */
    getDeathSwitch() {
      let params = {
        settingTypeCode: "DischangeAssessDeathDealWithFlag",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.deathSwitch = res.data && this.handoverType == "DischargeAssess";
        }
      });
    },
    /**
     * description: 加载最新生命体征数据
     * param {*}
     * return {*}
     */
    openClinic() {
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      GetVitalSignByInpatientID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.apparatusTable = res.data;
        }
      });
      this.showClinicFlag = true;
    },
    /**
     * description:单击触发事件
     * param {*}当前行数据
     * return {*}
     */
    handleCurrentChange(value) {
      if (!value) {
        return;
      }
      let valueArr = Object.values(value);
      for (let i = 0; i < this.templateDatas.length; i++) {
        for (let j = 0; j < this.templateDatas[i].groups.length; j++) {
          if (this.templateDatas[i].groups[j].contents.length == 0) {
            continue;
          }
          for (let k = 0; k < this.templateDatas[i].groups[j].contents.length; k++) {
            let keyValue = valueArr.find((item) => item.id == this.templateDatas[i].groups[j].contents[k].assessListID);
            if (keyValue) {
              this.$set(this.templateDatas[i].groups[j].contents[k], "assessValue", keyValue.value);
            }
          }
        }
      }
      this.$nextTick(() => {
        this.$refs.tabsLayout.init();
      });
      this.showClinicFlag = false;
    },
  },
};
</script>

<style lang="scss">
.sbar-header {
  .button-line {
    float: right;
  }

  .date-picker {
    width: 120px;
  }
}

.handover-sbar {
  height: 100%;

  .handover-tabs {
    height: 35px;
  }

  .pane-content {
    height: calc(100% - 45px);

    .evaluate-div {
      height: 100%;
    }

    .handover-div {
      height: 100%;

      .title {
        background-color: #ebf7df;
        height: 30px;
        line-height: 30px;
        box-sizing: border-box;
        padding-left: 5px;
        border-top: 1px solid #c4c4c4;
        border-left: 1px solid #c4c4c4;
        border-right: 1px solid #c4c4c4;
      }

      .handover {
        height: calc(100% - 30px);
        box-sizing: border-box;
      }
    }

    .assessment-wrap-risk {
      height: 100%;

      .assess {
        height: 100%;
        width: calc(100% - 260px);
        display: inline-block;
        box-sizing: border-box;
      }

      .risk {
        height: 100%;
        vertical-align: top;
        width: 255px;
        display: inline-block;
        box-sizing: border-box;

        .risk-title {
          height: 40px;
          line-height: 40px;
          background-color: #ebf7df;
          padding-left: 10px;
          border: 1px solid #cccccc;
          border-bottom: 0;
        }

        .risk-table {
          height: calc(100% - 40px);
          overflow: hidden;
          overflow-y: auto;
        }
      }
    }

    .assessment-wrapno-risk {
      height: 100%;

      .assess {
        height: 100%;
        width: 100%;
        display: inline-block;
        box-sizing: border-box;
      }
    }
  }

  .handover-tabs {
    height: 35px;

    #pane-assessment {
      height: 100%;
    }

    .tab-pane {
      height: 100%;
    }

    .el-tabs__content {
      height: calc(100% - 60px);
      box-sizing: border-box;

      // tabs内置id
      #pane-evaluate {
        height: 100%;
      }

      #pane-detailed {
        height: 100%;
      }
    }
  }

  iframe {
    height: 99%;
    width: 100%;
    border: 0;
    overflow: hidden;
  }
}
</style>
