/*
 * FilePath     : \ccc.web\src\api\PatientForm.js
 * Author       : 杨欣欣
 * Date         : 2022-06-01 17:09
 * LastEditors  : 杨欣欣
 * LastEditTime : 2022-06-22 16:20
 * Description  :
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientForm";

export const urls = {
  GetPatientFormRecordView: baseUrl + "/GetPatientFormRecordView",
  GetAssessView: baseUrl + "/GetAssessView",
  //获取评估模板中按钮的值
  GetButtonData: baseUrl + "/GetButtonData",
  AddPatientForm: baseUrl + "/AddPatientForm",
  UpdatePatientForm: baseUrl + "/UpdatePatientForm",
  DeleteRecord: baseUrl + "/DeleteRecord"
};

export const GetPatientFormRecordView = params => {
  return http.get(urls.GetPatientFormRecordView, params);
};
export const GetAssessView = params => {
  return http.post(urls.GetAssessView, qs.stringify(params));
};
export const GetButtonData = params => {
  return http.get(urls.GetButtonData, params);
};
export const AddPatientForm = params => {
  return http.post(urls.AddPatientForm, params);
};
export const UpdatePatientForm = params => {
  return http.post(urls.UpdatePatientForm, params);
};
export const DeleteRecord = params => {
  return http.get(urls.DeleteRecord, params);
};
