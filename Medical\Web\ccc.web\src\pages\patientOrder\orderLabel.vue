<!--
 * FilePath     : \src\pages\patientOrder\orderLabel.vue
 * Author       : 苏军志
 * Date         : 2020-07-07 19:12
 * LastEditors  : 马超
 * LastEditTime : 2025-06-30 09:19
 * Description  : 打印医嘱标签 
--> 
<template>
  <base-layout class="order-label" headerHeight="auto" v-loading="loading" :element-loading-text="loadingText">
    <div slot="header" class="top">
      <el-radio-group v-model="selectDate">
        <el-radio-button :label="previousDate">前一天</el-radio-button>
        <el-radio-button :label="selectDate" disabled>{{ selectDate }}</el-radio-button>
        <el-radio-button :label="nextDate">后一天</el-radio-button>
      </el-radio-group>
      <span class="label">床号：</span>
      <el-input
        class="search-input"
        v-model="bedNumber"
        placeholder="请输入床号"
        clearable
        @keyup.enter.native="getOrderLabels"
        @clear="getOrderLabels"
      >
        <i slot="append" class="iconfont icon-search" @click="getOrderLabels"></i>
      </el-input>
      <!-- 新加内容 -->
      <span class="label">医嘱内容：</span>
      <el-input
        class="search-input"
        v-model="orderContent"
        placeholder="请输入内容"
        clearable
        @keyup.enter.native="filterOrderContent"
        @clear="filterOrderContent"
      >
        <i slot="append" class="iconfont icon-search" @click="filterOrderContent"></i>
      </el-input>
      <span class="label">给药途径：</span>
      <el-input
        class="search-input"
        v-model="drugWay"
        placeholder="请输入途径"
        clearable
        @keyup.enter.native="filterDrugWay"
        @clear="filterDrugWay"
      >
        <i slot="append" class="iconfont icon-search" @click="filterDrugWay"></i>
      </el-input>
      <!-- 结束 -->
      <div class="top-right">
        <span class="label">时间区间：</span>
        <el-time-picker
          is-range
          v-model="selectTime"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
          class="range-time"
          format="HH:mm"
          @change="getOrderLabels"
        ></el-time-picker>
      </div>
    </div>
    <el-table border :data="administrationList" height="80" class="administration-table">
      <el-table-column
        v-for="(administration, index) in administrationColumns"
        :key="index"
        :label="administration.title"
        :index="index"
        align="center"
      >
        <template slot-scope="scope">
          <div
            :class="['column', { 'is-select': selectRowIndex >= 0 && selectColumnIndex == scope.column.index }]"
            @click="changeType(scope.column, scope.$index)"
          >
            {{ scope.row[administration.name] }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="switch-group">
      <el-radio-group v-model="printFlag">
        <el-radio-button label="0">未打印</el-radio-button>
        <el-radio-button label="1">已打印</el-radio-button>
      </el-radio-group>
      <el-radio v-model="orderType" label="长期"></el-radio>
      <el-radio v-model="orderType" label="临时"></el-radio>
      <el-radio v-model="orderType" label="全部"></el-radio>
      <el-checkbox v-model="newOrderFlag" class="new-order">今日新开</el-checkbox>
      <el-tooltip effect="light" content="有已停止数据，若要查看，请勾选" placement="right" v-model="hasStopTask">
        <el-checkbox v-model="stopOrderFlag" class="stop-order">已停止</el-checkbox>
      </el-tooltip>
      <div class="right-button">
        <el-button type="primary" icon="iconfont icon-refresh" @click="getOrderLabels">刷新</el-button>
        <el-button class="print-button" icon="iconfont icon-print" @click="printLabel">打印</el-button>
        <el-button type="danger" icon="iconfont icon-stop" @click="cancelTask">取消</el-button>
      </div>
    </div>
    <div class="print-main" ref="orderTable">
      <div class="print-view">
        <!-- <div class="print-img" :style="{ 'background-image': labelSrc }"></div> -->
        <img :src="labelSrc" alt="医嘱瓶签" class="print-img" />
        <div class="print-merge">
          <div>
            合并打印：
            <el-switch v-model="switchMerge" />
          </div>
          <div class="merge-rule">合并规则：</div>
          <ul>
            <li class="rule">
              <div class="dot"></div>
              <div>同一个病人</div>
            </li>
            <li class="rule">
              <div class="dot"></div>
              <div>执行时间相同</div>
            </li>
            <li class="rule">
              <div class="dot"></div>
              <div>用药途径一致</div>
            </li>
          </ul>
        </div>
      </div>
      <u-table
        ref="labelTable"
        border
        highlight-current-row
        :data="showLabelList"
        :height="tableHeight"
        :row-height="30"
        class="print-data"
        :row-class-name="setRowClassName"
        bigDataCheckbox
        use-virtual
        @row-click="showLabel"
        @select="selectTask"
        @select-all="selectAllTask"
      >
        <u-table-column
          type="selection"
          :width="convertPX(40)"
          align="center"
          :label-class-name="switchMerge ? 'hide-check' : ''"
        ></u-table-column>
        <u-table-column label="床号" prop="bedNo" min-width="40" align="center"></u-table-column>
        <u-table-column label="姓名" prop="patientName" width="65" align="center"></u-table-column>
        <u-table-column label="类型" prop="orderType" min-width="40" align="center"></u-table-column>
        <u-table-column label="预计执行" width="60" align="center">
          <template slot-scope="label">
            <span v-formatTime="{ value: label.row.scheduleTime, type: 'time' }"></span>
          </template>
        </u-table-column>
        <u-table-column align="center" label-class-name="header" class-name="order" min-width="255">
          <template slot="header" slot-scope="label">
            <div class="cell content">医嘱内容</div>
            <div class="cell dosage">剂量</div>
            <div class="cell dose-per-unit">用量</div>
            <div class="cell drug-spec">规格</div>
          </template>
          <template slot-scope="label">
            <u-table
              :data="label.row.orders"
              :show-header="false"
              use-virtual
              :highlight-current-row="false"
              :class="['order-content', { one: label.row.orders.length == 1 }]"
            >
              <u-table-column label="医嘱" width="45%" header-align="center">
                <template slot-scope="order">
                  <span :class="order.row.isDanger ? 'is-danger' : ''">
                    {{ order.row.content }}
                  </span>
                </template>
              </u-table-column>
              <u-table-column label="剂量" width="16%" align="center">
                <template slot-scope="order">
                  {{ order.row.dosage + order.row.unit }}
                </template>
              </u-table-column>
              <u-table-column label="用量" prop="dosePerUnit" width="15%" align="center"></u-table-column>
              <u-table-column label="规格" prop="drugSpec" width="24%" align="center"></u-table-column>
            </u-table>
          </template>
        </u-table-column>
        <u-table-column label="给药途径" prop="administration" min-width="50" header-align="center"></u-table-column>
        <u-table-column label="频次" prop="frequency" min-width="50" header-align="center"></u-table-column>
        <u-table-column
          label="开嘱医生"
          prop="doctor"
          width="65"
          align="center"
          label-class-name="doctor-header"
        ></u-table-column>
        <u-table-column label="开立时间" width="110" align="center">
          <template slot-scope="label">
            <span v-formatTime="{ value: label.row.startDateTime, type: 'dateTime' }"></span>
          </template>
        </u-table-column>
      </u-table>
    </div>
  </base-layout>
</template>
<script>
import { GetOrdersTaskList, MergePrint, OrdersTaskCancelByID, OrdersTaskPrint } from "@/api/Orders";
import baseLayout from "@/components/BaseLayout";
import shiftSelector from "@/components/selector/shiftSelector";
import { wp } from "@/utils/web-proxy";
import { mapGetters } from "vuex";
import { GetSettingValueByTypeCodeAsync } from "@/api/SettingDescription";
export default {
  components: {
    baseLayout,
    shiftSelector,
  },
  data() {
    return {
      // 加载提示
      loading: false,
      loadingText: "加载中……",
      // 选择的日期
      selectDate: undefined,
      bedNumber: "",
      // 选择的时间区间
      selectTime: [],
      // 给药途径列
      administrationColumns: [],
      // 给药途径数据
      administrationList: [],
      // 选择的给药途径
      administration: "全部",
      // 选择的给药途径行号
      selectRowIndex: 0,
      // 选择的给药途径列号
      selectColumnIndex: 0,
      // 原始医嘱标签列表
      orderLabelList: [],
      // 要显示的符合条件的医嘱标签列表
      showLabelList: [],
      // 查询出来的医嘱标签列表
      labelList: [],
      // 打印标记
      printFlag: 0,
      // 长期/临时/全部医嘱类别
      orderType: "全部",
      // 医嘱标签预览图片src
      labelSrc: undefined,
      // 是否合并打印
      switchMerge: false,
      // 选择要打印的医嘱任务
      printTaskList: [],
      // 过滤今日新开医嘱
      newOrderFlag: false,
      stopOrderFlag: false,
      tableHeight: 0,
      orderContent: undefined,
      drugWay: undefined,
      // 当前途径是否有已停止数据
      hasStopTask: false,
      // 前一天日期
      previousDate: undefined,
      // 后一天日期
      nextDate: undefined,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  watch: {
    selectDate: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          // 更新前一天和后一天的日期
          this.previousDate = this._datetimeUtil.addDate(newValue, -1, "yyyy-MM-dd");
          this.nextDate = this._datetimeUtil.addDate(newValue, 1, "yyyy-MM-dd");
          // 设置时间范围
          this.selectTime = [newValue + " 00:00:00", newValue + " 23:59:59"];
          this.getOrderLabels();
        }
      },
    },
    printFlag(newValue) {
      if (newValue) {
        this.deelData();
      }
    },
    orderType(newValue) {
      if (newValue) {
        this.deelData();
      }
    },
    switchMerge(newValue) {
      this.$refs.labelTable.clearSelection();
      this.printTaskList = [];
    },
    newOrderFlag(newValue) {
      this.deelData();
    },
    stopOrderFlag(newValue) {
      this.deelData();
    },
  },
  mounted() {
    this.$nextTick(() => {
      // 页面渲染完成后的回调
      this.tableHeight = this.$refs.orderTable.offsetHeight;
    });
    this.init();
  },
  methods: {
    /**
     * description: 系统顶部刷新按钮触发同步后
     * return {*}
     * param {*} refreshResult 执行同步方法的调用结果
     */
    refreshData(refreshResult) {
      if (refreshResult && refreshResult.data) {
        this.getOrderLabels();
        return;
      }
      this._showTip("warning", "刷新失败");
    },
    /**
     * description:系统顶部刷新按钮触发同步前获取请求参数
     * return {*}
     */
    getRefreshParams() {
      let params = {
        stationID: this.user.stationID,
        startDateTime: this.selectDate + " " + this._datetimeUtil.formatDate(this.selectTime[0], "hh:mm:ss"),
        endDateTime: this.selectDate + " " + this._datetimeUtil.formatDate(this.selectTime[1], "hh:mm:ss"),
      };
      return params;
    },
    // 初始化
    init() {
      const currentDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.selectDate = currentDate;
      // 初始化前一天和后一天的日期
      this.previousDate = this._datetimeUtil.addDate(currentDate, -1, "yyyy-MM-dd");
      this.nextDate = this._datetimeUtil.addDate(currentDate, 1, "yyyy-MM-dd");
      this.selectTime = [currentDate + " 00:00:00", currentDate + " 23:59:59"];
      this.getSettingValueByTypeCode();
    },
    /**
     * description: 获取血酮颜色
     * return {*}
     */
    async getSettingValueByTypeCode() {
      let param = {
        SettingTypeCode: "PrintFlagUrl",
      };
      await GetSettingValueByTypeCodeAsync(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.bloodKetoneColor = response?.data ?? "#000";
        }
      });
    },
    // 获取医嘱任务列表
    async getOrderLabels() {
      this.administrationColumns = [];
      this.administrationList = [];
      this.orderLabelList = [];
      this.showLabelList = [];
      this.labelList = [];
      this.labelSrc = undefined;
      this.switchMerge = false;
      this.printTaskList = [];
      let params = {
        stationID: this.user.stationID,
        startTime:
          this.selectDate +
          " " +
          this._datetimeUtil.formatDate(
            this.selectTime && this.selectTime.length > 0 ? this.selectTime[0] : "",
            "hh:mm:ss"
          ),
        endTime:
          this.selectDate +
          " " +
          this._datetimeUtil.formatDate(
            this.selectTime && this.selectTime.length > 1 ? this.selectTime[1] : "",
            "hh:mm:ss"
          ),
        bedNumber: this.bedNumber,
      };
      this.loadingText = "加载中……";
      this.loading = true;
      await GetOrdersTaskList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          if (result.data) {
            this.administrationColumns = result.data.administrationTable.columns;
            this.administrationList = result.data.administrationTable.rows;
            this.orderLabelList = result.data.labels;
            this.deelData();
          }
        }
      });
    },
    // 根据条件处理数据
    deelData() {
      this.labelSrc = undefined;
      this.hasStopTask = false;
      this.showLabelList = [];
      this.labelList = [];
      this.orderLabelList.forEach((label) => {
        let tempLabel = undefined;
        if (this.printFlag == 0) {
          // 未打印
          if (!label.printFlag) {
            tempLabel = label;
          }
        } else {
          // 已打印
          if (label.printFlag) {
            tempLabel = label;
          }
        }
        if (tempLabel) {
          let tempTask = undefined;
          // 过滤选择途径
          if (this.administration == "全部") {
            tempTask = tempLabel.task;
          } else {
            if (tempLabel.task.medicineType && tempLabel.task.medicineType.trim() == this.administration) {
              tempTask = tempLabel.task;
            }
          }
          if (tempTask) {
            //若都开启
            if (this.stopOrderFlag && this.newOrderFlag) {
              //过滤掉未停止的且非本日的
              if (!(tempTask.hisStatus == 3 && tempTask.newOrderFlag)) {
                tempTask = undefined;
              }
            } else {
              //某一个开启了
              if (this.newOrderFlag) {
                if (!tempTask.newOrderFlag) {
                  tempTask = undefined;
                }
              } else if (this.stopOrderFlag) {
                if (tempTask.hisStatus != 3) {
                  tempTask = undefined;
                }
              }
            }
          }
          //过滤掉已停止的，不再默认显示
          if (tempTask) {
            if (!this.stopOrderFlag && tempTask.hisStatus == 3) {
              this.hasStopTask = true;
              tempTask = undefined;
            }
          }
          if (tempTask) {
            // 过滤是临时医嘱还是长期医嘱
            if (this.orderType == "全部") {
              this.labelList.push(tempTask);
            } else {
              if (tempTask.orderType && tempTask.orderType.trim() == this.orderType) {
                this.labelList.push(tempTask);
              }
            }
          }
        }
      });
      this.showLabelList = this._common.clone(this.labelList);
      // 默认选择第一行
      if (this.showLabelList.length > 0) {
        this.$nextTick(() => {
          this.$refs.labelTable.setCurrentRow(this.showLabelList[0]);
          this.showLabel(this.showLabelList[0]);
          this.$refs.labelTable?.doLayout();
        });
      }
    },
    // 给药途径改变
    changeType(column, rowIndex) {
      this.selectRowIndex = rowIndex;
      this.selectColumnIndex = column.index;
      this.administration = column.label;
      this.deelData();
    },
    setRowClassName({ row, rowIndex }) {
      if (row.newOrderFlag) {
        return "new-order-row";
      } else if (row.hisStatus == 3) {
        return "stop-order-row";
      }
      return "";
    },
    // 显示医嘱标签预览
    showLabel(row) {
      wp.print.viewOrders(row, (result) => {
        if (result.success) {
          this.labelSrc = result.data;
        } else {
          this.showTip(result);
        }
      });
    },
    // 提示信息
    showTip(result) {
      this._showTip("warning", result.message ? result.message : "连接打印服务失败！请确认打印插件是否启动!");
    },
    // 检核是否符合合并条件
    checkMerge(row, mergeCondtion) {
      // 床号
      if (row.bedNo != mergeCondtion.bedNo) {
        this._showTip("warning", "床号不一致，违反合并规则！");
        return false;
      }
      // 预计执行时间
      if (row.scheduleTime != mergeCondtion.scheduleTime) {
        this._showTip("warning", "预计执行时间不一致，违反合并规则！");
        return false;
      }
      // 用药途径
      if (row.administration != mergeCondtion.administration) {
        this._showTip("warning", "用药途径不一致，违反合并规则！");
        return false;
      }
      return true;
    },
    // 选择医嘱任务
    selectTask(selection, row) {
      //根据床号排打印顺序
      selection.sort(function (a, b) {
        return a.bedNo - b.bedNo;
      });
      this.printTaskList = selection;
      // 处理打印预览
      if (selection.length > 0) {
        // 设置当前行
        this.$refs.labelTable.setCurrentRow(row);
        let task = this._common.clone(selection[0]);
        if (selection.length > 1) {
          // 如果开启了合并打印
          if (this.switchMerge) {
            let mergeCondtion = {
              bedNo: task.bedNo,
              scheduleTime: task.scheduleTime,
              administration: task.administration,
            };
            // 如果符合合并条件，组装预览标签
            if (this.checkMerge(row, mergeCondtion)) {
              for (var i = 1; i < selection.length; i++) {
                selection[i].orders.forEach((order) => {
                  task.orders.push(order);
                });
              }
              // 显示合并后的标签预览
              this.showLabel(task);
            } else {
              // 符合合并条件，取消选择
              this.$refs.labelTable.toggleRowSelection(row, false);
              // 删除最后一个，最后一个就是当前选择的行
              selection.pop();
              return;
            }
          }
        } else {
          // 非合并模式只有第一条才显示预览
          this.showLabel(task);
        }
      } else {
        this.labelSrc = undefined;
      }
    },
    // 全选
    selectAllTask(selection) {
      this.printTaskList = selection;
    },
    // 打印医嘱标签
    async printLabel() {
      if (this.printTaskList.length <= 0) {
        this._showTip("warning", "请选择医嘱！");
        return;
      }
      let printTask = [];
      // 处理合并打印
      if (this.switchMerge) {
        let task = this._common.clone(this.printTaskList[0]);
        let ids = [];
        for (var i = 0; i < this.printTaskList.length; i++) {
          ids.push(this.printTaskList[i].id);
        }
        await MergePrint(ids).then((result) => {
          if (this._common.isSuccess(result)) {
            task.id = result.data;
            for (var i = 1; i < this.printTaskList.length; i++) {
              this.printTaskList[i].orders.forEach((order) => {
                task.orders.push(order);
              });
            }
          }
        });
        printTask.push(task);
      } else {
        printTask = this.printTaskList;
      }
      // 打印
      this.loadingText = "打印中……";
      this.loading = true;
      return wp.print.printOrders(printTask, (result) => {
        this.loading = false;
        if (result.success) {
          var tasks = [];
          for (var i = 0; i < printTask.length; i++) {
            tasks.push({
              ID: printTask[i].id,
              Source: printTask[i].source,
            });
          }
          let param = {
            ordersTasks: tasks,
            userID: this.user.userID ?? "sys",
          };
          OrdersTaskPrint(param).then((result) => {
            if (this._common.isSuccess(result)) {
              this.getOrderLabels();
            }
          });
        } else {
          this.showTip(result);
        }
      });
    },
    // 取消医嘱任务
    cancelTask() {
      if (this.printTaskList.length <= 0) {
        this._showTip("warning", "请选择医嘱！");
        return;
      }
      this._confirm("确认要取消医嘱吗", "取消医嘱确认", (flag) => {
        if (flag) {
          let ids = [];
          for (var i = 0; i < this.printTaskList.length; i++) {
            ids.push(this.printTaskList[i].id);
          }
          OrdersTaskCancelByID(ids).then((result) => {
            if (this._common.isSuccess(result)) {
              this.getOrderLabels();
            }
          });
        }
      });
    },
    /**
     * description: 2022年6月21日14:53:44 用来处理根据医嘱内容筛选
     * param {*}
     * return {*}
     */
    filterOrderContent() {
      if (!this.orderContent) {
        this.showLabelList = this.labelList;
        return;
      }
      this.showLabelList = this.labelList.filter((task) => {
        if (task.orders.find((order) => order.content.indexOf(this.orderContent) != -1)) {
          return task;
        }
      });
    },
    /**
     * description: 2022年6月22日11:14:15 用来处理根据给药途径内容筛选
     * param {*}
     * return {*}
     */
    filterDrugWay() {
      if (!this.drugWay) {
        this.showLabelList = this.labelList;
        return;
      }
      this.showLabelList = this.labelList.filter((m) => {
        return m.administration.indexOf(this.drugWay) != -1;
      });
    },
  },
};
</script>
<style lang="scss">
.order-label {
  .top {
    .label {
      margin-left: 10px;
    }
    .search-input {
      width: 160px;
      .el-input-group__append {
        padding: 0 5px;
      }
      i {
        color: #8cc63e;
      }
    }
    .el-radio-group {
      .el-radio-button {
        &.is-disabled {
          .el-radio-button__inner {
            background-color: #1cc6a3 !important;
            border-color: #1cc6a3 !important;
            color: #ffffff !important;
            cursor: default;
          }
        }
      }
    }
    .top-right {
      display: inline-block;
      .shift-selector {
        margin-right: 5px;
      }
      .range-time {
        width: 150px;
      }
    }
  }
  .administration-table {
    td {
      .cell > .column {
        cursor: pointer;
        height: 36px;
        line-height: 36px;
        &.is-select {
          background-image: url("../../../static/images/text-select.png");
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 40px;
        }
      }
    }
  }
  .switch-group {
    margin-top: 10px;
    background-color: #ffffff;
    height: 50px;
    line-height: 50px;
    padding: 0 10px;
    .el-radio {
      margin: 0 0 0 10px;
      .el-radio__label {
        padding-left: 5px;
      }
    }
    .right-button {
      float: right;
    }
    .new-order,
    .stop-order {
      margin-left: 10px;
      .el-checkbox__label {
        margin-left: 5px;
      }
    }
  }
  .print-main {
    margin-top: 10px;
    height: calc(100% - 150px);
    .print-view {
      float: left;
      width: 280px;
      height: 100%;
      margin-right: 10px;
      text-align: center;
      .print-img {
        border: 1px solid #d9d9d9;
        max-width: 280px;
        border-radius: 5px;
        background-color: #ffffff;
      }
      .print-merge {
        text-align: left;
        padding: 15px;
        .merge-rule {
          margin-top: 15px;
        }
        ul {
          margin: 5px 0;
          border-top: 1px solid $base-color;
          list-style: none;
          li div {
            display: inline-block;
            height: 34px;
            line-height: 34px;
            &.dot {
              width: 10px;
              height: 10px;
              border-radius: 10px;
              margin-right: 5px;
              background-color: $base-color;
            }
          }
        }
      }
    }
    .print-data {
      float: right;
      width: calc(100% - 300px);
      /* 新开医嘱 */
      .new-order-row {
        background-color: #e5fce5;
      }
      /* 停止医嘱 */
      .stop-order-row {
        background-color: #f5f5dc;
      }
      td.order,
      th.header,
      td.order .cell {
        padding: 0 !important;
      }
      .cell {
        &.hide-check {
          .el-checkbox {
            display: none;
          }
        }
        /* 医嘱内容子标题 */
        &.header {
          display: flex;
          height: 55px;
          div {
            border-right: 1px solid #cccccc;
            align-items: center;
            display: flex;
            justify-content: center;
            &:last-child {
              border-right: none;
            }
          }
          .content {
            width: 45%;
          }
          .dosage {
            width: 16%;
          }
          .dose-per-unit {
            width: 15%;
          }
          .drug-spec {
            width: 24%;
          }
        }
        &.doctor-header {
          width: 60px;
        }
        /* 医嘱内容子el-table */
        .order-content {
          .el-table {
            border: 0;
            background-color: transparent;
          }
          table {
            width: 100% !important;
            .el-table__row {
              background-color: transparent;
              &.current-row td {
                background-color: transparent;
                font-weight: normal;
                .cell,
                .cell * {
                  font-weight: normal;
                  background-color: transparent;
                }
              }
              &:last-child td {
                border-bottom: 0;
              }
              td {
                &.dose-per {
                  border-right: 0;
                }
                .cell {
                  .is-danger {
                    color: #ff0000;
                  }
                }
              }
            }
          }
        }
      }
      .el-table--striped tr.el-table__row--striped.current-row td,
      .el-table__body tr.current-row td {
        * {
          font-weight: normal;
        }
      }
    }
  }
}
</style>
