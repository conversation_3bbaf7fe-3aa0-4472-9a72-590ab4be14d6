/*
 * FilePath     : \ccc.web\src\api\NurseBed.js
 * Author       : 郭鹏超
 * Date         : 2020-07-03 09:20
 * LastEditors  : 李帅
 * LastEditTime : 2021-11-09 15:53
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/job/nurse";

export const urls = {
  //根据病区获取工作岗位
  GetNurseBed: baseUrl + "/GetNurseBed",
  //删除床位
  DeleteBed: baseUrl + "/DeleteBed",
  //取消责任护士标记
  CancelNurse: baseUrl + "/CancelNurse",
  //获取科室岗位
  GetDeptmentJobByNurse: baseUrl + "/GetDeptmentJob",
  //保存责护管床配置
  SaveNurseBed: baseUrl + "/SaveNurseBed",
  //获取已派床位及未派床位的数量
  GetNurseCount: baseUrl + "/GetNurseCount",
  //删除所有床位
  DeleteAllBeds: baseUrl + "/DeleteAllBeds"
};
//根据病区获取工作岗位
export const GetNurseBed = params => {
  return http.get(urls.GetNurseBed, params);
};
//取消责任护士标记
export const CancelNurse = params => {
  return http.post(urls.CancelNurse, params);
};
//删除床位
export const DeleteBed = params => {
  return http.post(urls.DeleteBed, qs.stringify(params));
};
//获取科室岗位
export const GetDeptmentJobByNurse = params => {
  return http.get(urls.GetDeptmentJobByNurse, params);
};
//保存科室岗位关系
export const SaveNurseBed = params => {
  return http.post(urls.SaveNurseBed, params);
};
//获取已派床位及未派床位的数量
export const GetNurseCount = params => {
  return http.get(urls.GetNurseCount, params);
};
//删除所有床位
export const DeleteAllBeds = params => {
  return http.post(urls.DeleteAllBeds, qs.stringify(params));
};
