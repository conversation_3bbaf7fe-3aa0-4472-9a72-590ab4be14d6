<!--
 * FilePath     : \src\autoPages\handover\components\handoverReport.vue
 * Author       : 郭鹏超
 * Date         : 2021-11-25 09:18
 * LastEditors  : 来江禹
 * LastEditTime : 2024-06-03 10:38
 * Description  : 交班报告组件
 * CodeIterationRecord:
-->
<template>
  <div class="handover-report">
    <!-- 头部内容 -->
    <el-table
      v-for="(viewItem, index) in handoverReportTitle"
      :key="index"
      :data="viewItem[1]"
      :show-header="false"
      border
      v-show="viewItem[1] && viewItem[1].length > 0"
      :class="'table-' + viewItem[0]"
    >
      <template v-if="viewItem[1] && viewItem[1].length > 0">
        <el-table-column
          v-for="(item, itemIndex) in Object.keys(viewItem[1][0])"
          :key="itemIndex"
          :prop="item"
          align="center"
          :min-width="convertPX(25)"
        >
          <template slot-scope="scope">
            <div v-if="viewItem[0] != 'signData'">{{ scope.row[item] }}</div>
            <div v-else>
              <div
                v-if="scope.$index > 0"
                @click="getHandoverSBAR(item, scope.row[item])"
                :class="{ 'is-select': checkedProp == item }"
              >
                {{ scope.row[item].numberOrLabel }}
              </div>
              <el-tooltip
                v-else
                placement="top"
                :disabled="!scope.row[item].description"
                :content="scope.row[item].description"
              >
                <div>{{ scope.row[item].numberOrLabel }}</div>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <!-- 交班内容 -->
    <div :class="{ 'handover-report-sbar': true, 'show-sign': !showSignFlag }">
      <el-table
        v-if="reportSbarView && reportSbarView.length > 0"
        :data="reportSbarView"
        :show-header="false"
        border
        class="table-sbar"
      >
        <el-table-column
          v-for="(item, itemIndex) in Object.keys(reportSbarView[0])"
          :key="itemIndex"
          :prop="item"
          :width="item == 'name' ? convertPX(185) : undefined"
          header-align="center"
        >
          <template slot-scope="scope">
            <div
              :class="{ title: scope.$index == 0 }"
              v-html="scope.row[item][item == 'name' ? 'bedAndName' : 'situation']"
            ></div>
            <body-image
              v-if="showBodyPartFlag && scope.row[item].handoverID && scope.$index > 0 && item != 'name'"
              :type="'tooltip'"
              :handoverID="scope.row[item].handoverID"
              :patientName="scope.row[item].patientName"
            ></body-image>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import bodyImage from "@/components/bodyImage";
export default {
  components: {
    bodyImage,
  },
  props: {
    //顶部内容
    handoverReportTitleView: {
      type: Object,
      default: () => {
        return {};
      },
    },
    //交班内容
    handoverReportSbarView: {
      type: Array,
      default: () => {
        return [];
      },
    },
    //是否显示统计数据 默认显示
    showSignFlag: {
      type: Boolean,
      default: true,
    },
    //是否显示人体图
    showBodyPartFlag: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      handoverReportTitle: [],
      checkedProp: "",
      reportSbarView: [],
    };
  },
  watch: {
    handoverReportTitleView: {
      handler(newValue) {
        this.handoverReportTitle = Object.entries(newValue ?? {});
      },
      immediate: true,
    },
    handoverReportSbarView: {
      handler(newValue) {
        this.reportSbarView = this.handoverReportSbarView;
      },
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 传送点选统计人数病人inpatientID集合
     * param {*} prop
     * param {*} item
     * return {*}
     */
    getHandoverSBAR(prop, item) {
      this.checkedProp = prop;
      if (!item || !item.inpatientIDArr) {
        this.reportSbarView = [];
        return;
      }
      this.$emit("getReportParams", item.inpatientIDArr, item.recordsCode);
    },
  },
};
</script>

<style lang="scss" >
.handover-report {
  height: 100%;
  .table-signData {
    .cell {
      height: 38px;
      line-height: 38px;
      cursor: pointer;
      & > div {
        height: 100%;
      }
    }

    .is-select {
      background-image: url("../../../../static/images/text-select.png");
      background-position: center center;
      background-repeat: no-repeat;
      background-size: 40px;
    }
  }
  .handover-report-sbar {
    height: calc(100% - 180px);
    overflow: hidden;
    overflow-y: auto;
  }
  .show-sign {
    height: calc(100% - 80px);
  }
  .table-sbar {
    .title {
      text-align: center;
    }
  }
  .icon-body {
    position: absolute;
    top: 2px;
    right: -5px;
    color: #1cc6a3;
    font-size: 20px;
    font-weight: 600;
  }
}
</style>