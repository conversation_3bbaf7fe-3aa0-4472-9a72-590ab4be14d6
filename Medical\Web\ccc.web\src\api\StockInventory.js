/*
 * FilePath     : \src\api\StockInventory.js
 * Author       : 孟昭永
 * Date         : 2021-04-25 18:19
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:48
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/StockInventory";

export const urls = {
  GetStockInventoryMain: baseUrl + "/GetStockInventoryMain",
  GetStockInventoryDetail: baseUrl + "/GetStockInventoryDetail",
  SaveStockInventory: baseUrl + "/SaveStockInventory"
};

export const GetStockInventoryMain = params => {
  return http.get(urls.GetStockInventoryMain, params);
};

export const GetStockInventoryDetail = params => {
  return http.get(urls.GetStockInventoryDetail, params);
};

export const SaveStockInventory = params => {
  return http.post(urls.SaveStockInventory, params);
};
