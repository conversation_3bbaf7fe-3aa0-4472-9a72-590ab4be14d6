<!--
 * FilePath     : \src\pages\glucose\index.vue
 * Author       : 李正元
 * Date         : 2020-11-03 09:12
 * LastEditors  : 来江禹
 * LastEditTime : 2024-04-29 14:32
 * Description  : 血糖记录单
 -->

<template>
  <div class="glucose-main">
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="(component, index) in components"
        :key="index"
        :label="component.label"
        :name="component.name"
      ></el-tab-pane>
    </el-tabs>
    <component class="glucose-component" :is="activeName" :patientInfo="patient" :readonly="readonly" />
  </div>
</template>
<script>
import glucoseEdit from "@/pages/glucose/components/glucoseEdit";
import glucoseChart from "@/pages/glucose/components/glucoseChart";
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    glucoseEdit,
    glucoseChart,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patient: "getPatientInfo",
    }),
  },
  data() {
    return {
      //点中的页签名称,默许为glucoseEdit
      activeName: "glucoseEdit",
      //页签配置
      components: [
        { index: 0, label: "血糖记录", name: "glucoseEdit" },
        { index: 1, label: "血糖统计", name: "glucoseChart" },
      ],
      readonly: false,
    };
  },
  created() {
    this.readonly = !!this.$route.query?.readonly;
  },
  methods: {},
};
</script>
<style lang="scss">
.glucose-main {
  height: 100%;
  .glucose-component {
    height: calc(100% - 30px);
  }
}
</style>
