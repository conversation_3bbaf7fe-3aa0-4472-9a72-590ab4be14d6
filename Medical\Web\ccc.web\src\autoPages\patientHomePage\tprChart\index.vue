<!--
 * FilePath     : \src\autoPages\patientHomePage\tprChart\index.vue
 * Author       : 苏军志
 * Date         : 2020-11-17 11:16
 * LastEditors  : 来江禹
 * LastEditTime : 2023-06-09 16:13
 * Description  : JH-062  病人主页监测生命体征默认时间间隔改为当下时间往前一周
                  生命体征添加中心静脉压  多条Y添加
-->

<template>
  <base-layout class="tpr-chart" headerHeight="auto">
    <div class="top" slot="header">
      <!-- 日期 -->
      <span>{{ tprChart.date }}:</span>
      <el-date-picker
        v-model="startDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        class="tpr-date"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        v-model="endDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        class="tpr-date"
      ></el-date-picker>
      <el-tooltip :content="showChart ? '隐藏图表' : '显示图表'">
        <el-checkbox-button v-model="showChart">
          <i class="iconfont icon-chart"></i>
        </el-checkbox-button>
      </el-tooltip>
      <el-tooltip :content="showTable ? '隐藏表格' : '显示表格'">
        <el-checkbox-button v-model="showTable">
          <i class="iconfont icon-list-icon"></i>
        </el-checkbox-button>
      </el-tooltip>
      <div class="vital-sign">
        <!-- 最新生命体征 -->
        <span class="vital-sign-title">{{ tprChart.latestVitalSigns }}:</span>
        <!-- 体温 -->
        <el-tag type="success" effect="plain">{{ tprChart.temperature }}:{{ vitalSign.temperature }}°C</el-tag>
        <!-- 脉率 -->
        <el-tag type="success" effect="plain">
          {{ tprChart.pulseRate }}:{{ vitalSign.pulse }}{{ tprChart.pulseRateUnit }}
        </el-tag>
        <!-- 心率 -->
        <el-tag type="success" effect="plain">
          {{ tprChart.heartRate }}:{{ vitalSign.heartRate }}{{ tprChart.heartRateUnit }}
        </el-tag>
        <!-- 呼吸 -->
        <el-tag type="success" effect="plain">
          {{ tprChart.respiratoryRate }}:{{ vitalSign.breath }}{{ tprChart.respiratoryRateUnit }}
        </el-tag>
        <!-- 血压 -->
        <el-tag type="success" effect="plain">
          {{ tprChart.bloodPressure }}:{{ vitalSign.systolic }}/{{ vitalSign.pressure }}mmHg
        </el-tag>
        <!-- 中心静脉压 mmHg-->
        <el-tag type="success" v-if="vitalSign.cvpMmHg" effect="plain">
          {{ tprChart.cvp }}:{{ vitalSign.cvpMmHg }}mmHg
        </el-tag>
        <!-- 中心静脉压 cmH2O-->
        <el-tag type="success" v-if="vitalSign.cvpCmH2O" effect="plain">
          {{ tprChart.cvp }}:{{ vitalSign.cvpCmH2O }}cmH2O
        </el-tag>
      </div>
      <div class="top-btn">
        <!-- 返回 -->
        <el-button class="print-button" icon="iconfont icon-back" @click="$router.go(-1)" v-if="this.transferFlag">
          {{ button.back }}
        </el-button>
      </div>
    </div>
    <div class="chart-wrap" v-if="chartData" :loading="loading" element-loading-text="加载中……">
      <ve-line
        :data="chartData"
        :grid="grid"
        :extend="extend"
        :tooltip="tooltip"
        :settings="chartSettings"
        v-if="showChart"
        :after-set-option="afterSetOption"
        :height="showTable ? convertPX(500) + 'px' : '100%'"
      ></ve-line>
      <el-table
        v-if="showTable"
        :data="chartData.rows"
        border
        stripe
        :height="showChart ? 'calc(100% - ' + convertPX(500) + 'px)' : '100%'"
        class="chart-table"
        ref="chartTable"
      >
        <template v-for="(column, index) in chartData.columns">
          <el-table-column
            v-if="index == 0"
            :key="index"
            :label="column"
            align="center"
            :prop="column"
            sortable
            :sort-orders="['ascending', 'descending']"
            :sort-method="sortMethod"
            :width="convertPX(200)"
          >
            <template slot-scope="data">
              {{ data.row[column] }}
            </template>
          </el-table-column>
          <el-table-column v-else :key="index" :label="column" align="center">
            <template slot-scope="data">
              {{ data.row[column] }}
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetPatientVitalSign } from "@/api/Handover";
import { GetTPRChartData } from "@/api/NursingRecord";
import { mapGetters } from "vuex";
import { setChartOption, grid, tooltip, extend } from "./chartOption";
export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      grid: grid,
      tooltip: tooltip,
      extend: extend,
      rightYAxis: "",
      loading: false,
      startDate: undefined,
      endDate: undefined,
      // 病人最新生命体征数据
      vitalSign: {},
      //显示统计数据
      chartData: {
        columns: [],
        rows: [],
      },
      //跳转标记
      transferFlag: true,
      showChart: true,
      showTable: true,
    };
  },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
    }),
    chartSettings() {
      return {
        axisSite: { right: this.rightYAxis },
      };
    },
    button() {
      return this.$t("button");
    },
    tprChart() {
      return this.$t("tprChart");
    },
  },
  watch: {
    startDate(newValue) {
      if (newValue) {
        this.getTPRChartData();
      }
    },
    endDate(newValue) {
      if (newValue) {
        this.getTPRChartData();
      }
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
  },

  created() {
    // 设置不可切换病人
    this._sendBroadcast("setPatientSwitch", false);
  },
  methods: {
    /**
     * description: 页面初始化
     * return {*}
     */
    init() {
      this.transferCheck();
      this.startDate = this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -6, "yyyy-MM-dd");
      this.endDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.getVitalSign();
    },
    /**
     * description: 获取病人最新生命提振数据
     * return {*}
     */
    getVitalSign() {
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      GetPatientVitalSign(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.vitalSign = response.data;
        }
      });
    },
    /**
     * description: 获取生命体征数据
     * return {*}
     */
    getTPRChartData() {
      if (!this.startDate || !this.startDate || !this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        startDate: this.startDate,
        endDate: this.endDate,
      };
      this.loading = true;
      GetTPRChartData(params).then((result) => {
        this.loading = false;
        this.chartData.rows = [];
        this.chartData.columns = [];
        if (this._common.isSuccess(result) && result.data) {
          this.rightYAxis = result.data.rightYAxis;
          delete result.data["rightYAxis"];
          this.chartData = result.data;
          this.$nextTick(() => {
            this.$refs.chartTable.sort("日期时间", "descending");
            this.$refs.chartTable.doLayout();
          });
        }
      });
      /**
       * description: 跳转检核
       * return {*}
       */
    },
    transferCheck() {
      if (this.$route.query.caseNumber == this.patient.caseNumber) {
        this.transferFlag = false;
      }
    },
    /**
     * description: 统计图撇配置参数
     * param {*} chart
     * return {*}
     */
    afterSetOption(chart) {
      setChartOption(chart, this);
    },
    /**
     * description: 排序
     * param {*} a
     * param {*} b
     * return {*}
     */
    sortMethod(a, b) {
      return a["日期时间"] > b["日期时间"];
    },
    /**
     * description: 获取时间列宽度
     * param {*} index
     * return {*}
     */
    getWidth(index) {
      return this.convertPX(this.tprChart.tableWidth[index]);
    },
  },
};
</script>
<style lang="scss">
.tpr-chart {
  .top {
    .vital-sign {
      display: inline-block;
      .el-tag.el-tag--success {
        border-color: #c2f77d;
        color: black;
      }
    }
    .tpr-date {
      width: 110px;
    }

    .top-btn {
      float: right;
    }
    .el-checkbox-button__inner {
      padding: 2px 5px;
      border-left: 1px solid #dcdfe6;
    }
    .el-checkbox-button.is-focus .el-checkbox-button__inner {
      border-left: 1px solid #1cc6a3;
    }
  }
  .chart-wrap {
    height: 100%;
    background-color: #ffffff;
    padding: 10px 0 10px 0;
    box-sizing: border-box;
  }
}
</style>
