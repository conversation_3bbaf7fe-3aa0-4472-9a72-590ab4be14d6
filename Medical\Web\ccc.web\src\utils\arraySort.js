/*
 * FilePath     : \src\utils\arraySort.js
 * Author       : 苏军志
 * Date         : 2022-08-31 17:04
 * LastEditors  : 苏军志
 * LastEditTime : 2022-08-31 17:42
 * Description  : 数组排序
 * CodeIterationRecord: 
 */
const compare = function (prop, sortType) {
  return function (obj1, obj2) {
    var val1 = obj1[prop];
    var val2 = obj2[prop];
    // 如果是数字，按数字排序
    if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
      val1 = Number(val1);
      val2 = Number(val2);
    }
    // 默认正序
    if (!sortType) {
      sortType = 'a'
    }
    if (sortType == "a") {
      // 正序
      if (val1 > val2) {
        return 1;
      }
      else if (val1 < val2) {
        return -1;
      }
      else {
        return 0;
      }
    } else {
      // 倒叙
      if (val1 > val2) {
        return -1;
      }
      else if (val1 < val2) {
        return 1;
      }
      else {
        return 0;
      }

    }
  }
}

/**
 * description: Array本地对象增加一个原型方法，用于数组对象排序
 * param {*} prop 要排序的属性名
 * param {*} sortType 排序方式，a：正序，d：倒叙，不传默认正序
 * return {*}
 * 调用示例：
 *     正序:
 *        arr.sortBy("sort");
 *        arr.sortBy("sort", "a");
 *      倒叙：
 *        arr.sortBy("sort", "d");
 */
Array.prototype.sortBy = function (prop, sortType) {
  this.sort(compare(prop, sortType))
}