/*
 * FilePath     : \ccc.web\src\api\thrombolysis.js
 * Author       : 郭鹏超
 * Date         : 2021-05-01 09:50
 * LastEditors  : 郭鹏超
 * LastEditTime : 2022-01-05 10:39
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/thrombolysis";

export const urls = {
  GetThrombolysisRecordList: baseUrl + "/GetThrombolysisRecordList",
  GetThrombolysisAssessView: baseUrl + "/GetThrombolysisAssessView",
  GetThrombolysisMainList: baseUrl + "/GetThrombolysisMainList",
  DeleteThrombolysisMainByMainID: baseUrl + "/DeleteThrombolysisMainByMainID",
  DeleteThrombolysisRecordByRecordID:
    baseUrl + "/DeleteThrombolysisRecordByRecordID",
  SaveThrombolysisRecord: baseUrl + "/SaveThrombolysisRecord",
  SaveThrombolysisMain: baseUrl + "/SaveThrombolysisMain",
  EndThrombolysisAssessment: baseUrl + "/EndThrombolysisAssessment",
  GetRecordIDByscheduleMainID: baseUrl + "/GetRecordIDByscheduleMainID",
  GetObserveTemplate: baseUrl + "/GetObserveTemplate"
};

export const GetThrombolysisRecordList = params => {
  return http.get(urls.GetThrombolysisRecordList, params);
};
export const GetThrombolysisMainList = params => {
  return http.get(urls.GetThrombolysisMainList, params);
};
export const DeleteThrombolysisRecordByRecordID = params => {
  return http.get(urls.DeleteThrombolysisRecordByRecordID, params);
};
export const DeleteThrombolysisMainByMainID = params => {
  return http.get(urls.DeleteThrombolysisMainByMainID, params);
};
export const GetThrombolysisAssessView = param => {
  return http.get(urls.GetThrombolysisAssessView, param);
};

export const SaveThrombolysisRecord = param => {
  return http.post(urls.SaveThrombolysisRecord, param);
};
export const SaveThrombolysisMain = param => {
  return http.post(urls.SaveThrombolysisMain, param);
};

export const EndThrombolysisAssessment = param => {
  return http.post(urls.EndThrombolysisAssessment, param);
};
//根据排程ID获取recordID 为排程跳专项护理使用
export const GetRecordIDByscheduleMainID = params => {
  return http.get(urls.GetRecordIDByscheduleMainID, params);
};
//获取观察措施对应模板
export const GetObserveTemplate = params => {
  return http.get(urls.GetObserveTemplate, params);
};
