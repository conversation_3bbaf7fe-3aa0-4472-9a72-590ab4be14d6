/*
 * FilePath     : \Core3.1_CareDirect\Medical\Web\ccc.web\src\api\StationSwitch.js
 * Author       : 杨欣欣
 * Date         : 2021-10-21 15:04
 * LastEditors  : 杨欣欣
 * LastEditTime : 2021-10-21 16:17
 * Description  : 
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/stationSwitch";

export const urls = {
  GetSwitchData: baseUrl + "/GetSwitchData",
  updateSwitch: baseUrl + "/updateSwitch"
};

export const UpdateSwitch = params => {
  return http.post(urls.updateSwitch, qs.stringify(params))
};
export const GetSwitchData = params => {
  return http.get(urls.GetSwitchData, params)
};
