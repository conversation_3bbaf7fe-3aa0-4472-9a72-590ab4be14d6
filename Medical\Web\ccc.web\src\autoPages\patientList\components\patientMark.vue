<!--
 * FilePath     : \src\autoPages\patientList\components\patientMark.vue
 * Author       : 苏军志
 * Date         : 2025-01-16 08:34
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-29 11:29
 * Description  : 患者标签
 * CodeIterationRecord: 
 -->
<template>
  <div
    :class="`patient-mark-${type} ${markStyle.iconText.length > 1 ? 'multi-text' : ''}`"
    :style="{
      color: showGroup ? markStyle.iconColor : '#ffffff',
      backgroundColor: markStyle.color,
    }"
    :title="markStyle.remarkDetail ? markStyle.remarkDetail : markStyle.remark"
  >
    <div class="text">
      {{ markStyle.iconText }}
    </div>
  </div>
</template>
<script>
export default {
  props: {
    markStyle: {
      type: Object,
      required: true,
    },
    type: {
      type: String,
      default: "1",
    },
    showGroup: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
<style lang="scss">
.patient-mark-1 {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  margin: 1px 2px;
  font-size: 14px;
  padding: 1px;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
  border: 1px solid #dfdfdf;
  border-radius: 3px;
  &.multi-text {
    font-size: 12px;
    width: 32px;
    .text {
      scale: 0.55;
    }
  }
}
.patient-mark-2 {
  display: inline-block;
  width: 20px;
  height: 18px;
  line-height: 18px;
  margin: 1px 2px;
  font-size: 16px;
  padding: 4px;
  font-weight: bold;
  text-align: center;
  border: 1px solid #dfdfdf;
  border-radius: 3px;
  &.multi-text {
    font-size: 12px;
    width: 32px;
    .text {
      scale: 0.55;
    }
  }
}
@media screen and (max-width: 1280px) {
  .patient-mark-1 {
    width: 16px;
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    padding: 1px 2px;
  }
}
</style>