<!--
 * FilePath     : \src\components\tabsLayout\layoutItem.vue
 * Author       : 马超
 * Date         : 2024-08-05 17:16
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-07 11:55
 * Description  : 
 * CodeIterationRecord: 
 -->
<template>
  <div class="layout-item" :style="{ '--color': item.fontColor && item.fontColor.trim() ? item.fontColor.trim() : '' }">
    <!-- 自定义指令必须先刷新才管用 -->
    {{ temp }}
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'T'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      class="input-content"
      :title="item.description"
      :style="{ backgroundColor: item.bwidthgetWidthackgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <template v-if="item.readOnly">
        <span v-if="item.assessValue">
          <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() : "" }}：</span>
          {{ item.assessValue }}
          {{ item.unit == null ? "" : item.unit.trim() }}
        </span>
      </template>
      <template v-else>
        <span v-if="item.contentILevel !== '1' && item.itemName">{{ item.itemName.trim() }}：</span>
        <el-input
          :style="{ width: getWidth(item.width, 100) }"
          v-model="item.assessValue"
          :disabled="item.disabled"
          @blur="changeItem(item)"
        />
        <span class="unit">
          {{ item.unit == null ? "" : item.unit.trim() }}
        </span>
      </template>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'TA'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      class="textarea-content"
      :title="item.description"
      :style="{ backgroundColor: item.bwidthgetWidthackgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <template v-if="item.readOnly">
        <span v-if="item.assessValue" class="textarea-item">
          <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() : "" }}：</span>
          {{ item.assessValue }}
        </span>
      </template>
      <template v-else>
        <span v-if="item.contentILevel !== '1' && item.itemName" class="textarea-item">
          {{ item.itemName.trim() }}：
        </span>
        <el-input
          :style="{ width: getWidth(item.width, 100) }"
          type="textarea"
          autosize
          v-model="item.assessValue"
          :disabled="item.disabled"
          @blur="changeItem(item)"
        />
      </template>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'MT'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      class="mt-content"
      :title="item.description"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <mt-item
        v-model="item.assessValue"
        :assessValueStyle="item.assessValueStyle"
        :readonly="item.readOnly"
        :label="item.contentILevel !== '1' && item.itemName ? item.itemName.trim() : ''"
        :title="item.itemName ? item.itemName.trim() : ''"
        :template="item.mtTemplate"
        :width="getWidth(item.width, 400)"
        :custom-methods="customMethods"
        @change="changeItem(item)"
      ></mt-item>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'TN'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      class="input-content"
      :title="item.description"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <template v-if="item.readOnly">
        <div v-if="item.assessValue">
          <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() : "" }}：</span>
          {{ item.assessValue }}
          {{ item.unit == null ? "" : item.unit.trim() }}
        </div>
      </template>
      <template v-else>
        <span v-if="item.contentILevel !== '1' && item.itemName">{{ item.itemName.trim() }}：</span>
        <el-input
          v-if="item.formula && formulaParamOptions"
          :style="{ width: getWidth(item.width, 60) }"
          v-formula="{ item: item, items: formulaParamOptions }"
          v-model="item.assessValue"
          readonly
          :disabled="item.disabled"
          @blur="changeItem(item)"
          @keyup.37.native.stop="inputEnter($event, false)"
          @keyup.39.native.stop="inputEnter($event, true)"
          @keyup.enter.native.stop="inputEnter($event, true)"
        />
        <el-input
          v-else
          :style="{ width: getWidth(item.width, 60) }"
          v-model="item.assessValue"
          :name="item.controlerType.trim()"
          @blur="changeItem(item)"
          @click.native="showKeyBoard($event)"
          @input="updateValue($event, item)"
          :readonly="isReadOnly"
          :disabled="item.disabled"
          @keyup.37.native.stop="inputEnter($event, false)"
          @keyup.39.native.stop="inputEnter($event, true)"
          @keyup.enter.native.stop="inputEnter($event, true)"
        />
        <span class="unit">
          {{ item.unit == null ? "" : item.unit.trim() }}
        </span>
      </template>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'E'"
      class="user-content"
      :title="item.description"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <template v-if="item.readOnly">
        <div v-if="item.assessValue">
          <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() : "" }}：</span>
          {{ item.assessValue }}
          {{ item.unit == null ? "" : item.unit.trim() }}
        </div>
      </template>
      <template v-else>
        <span v-if="item.contentILevel !== '1' && item.itemName">{{ item.itemName.trim() }}：</span>
        <user-selector
          v-model="item.assessValue"
          clearable
          filterable
          remoteSearch
          allowCreate
          :sessionUser="item.defaultValue == '1'"
          label=""
          @select="changeItem(item)"
          :width="getWidth(item.width, 100)"
        ></user-selector>
      </template>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'R'"
      :title="item.description"
      :style="{
        backgroundColor: item.backgroundColor && item.assessValue == '1' ? item.backgroundColor.trim() : 'transparent',
      }"
    >
      <template v-if="item.readOnly">
        <span v-if="item.assessValue" class="check-item rb-checked">{{ item.itemName.trim() }}</span>
      </template>
      <div v-else :class="['check-item', { 'rb-checked': item.assessValue == '1' }]" @click="changeItem(item)">
        <i :class="['iconfont', item.assessValue == '1' ? 'icon-radio-select' : 'icon-radio-normal']"></i>
        <span v-longpress="{ function: 'showMessage', params: item.description }" v-if="item.itemName">
          {{ item.itemName.trim() }}
        </span>
      </div>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'C'"
      :title="item.description"
      :style="{
        backgroundColor: item.backgroundColor && item.assessValue == '1' ? item.backgroundColor.trim() : 'transparent',
      }"
    >
      <template v-if="item.readOnly">
        <span v-if="item.assessValue" class="check-item cb-checked">{{ item.itemName.trim() }}</span>
      </template>
      <div v-else :class="['check-item', { 'cb-checked': item.assessValue == '1' }]" @click="changeItem(item)">
        <i :class="['iconfont', item.assessValue == '1' ? 'icon-checked' : 'icon-no-checked']"></i>
        <span v-longpress="{ function: 'showMessage', params: item.description }" v-if="item.itemName">
          {{ item.itemName.trim() }}
        </span>
      </div>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'B'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      :title="item.description"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
      class="button-content"
    >
      <el-badge
        v-if="item.assessValue"
        :class="['badge-item', { 'is-string': !/^\d+$/.test(item.assessValue) && !isAssessValueTrue(item.assessValue) }]"
        :is-dot="isAssessValueTrue(item.assessValue)"
        :value="isAssessValueTrue(item.assessValue) ? undefined : item.assessValue"
      >
        <el-button size="mini" type="primary" @click="changeItem(item)">
          {{ item.itemName ? item.itemName.trim() : "" }}
        </el-button>
      </el-badge>
      <el-button v-else size="mini" type="primary" @click="changeItem(item)">
        {{ item.itemName ? item.itemName.trim() : "" }}
      </el-button>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'BR'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      :title="item.description"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
      class="button-content"
    >
      <el-badge
        v-if="item.assessValue"
        :class="['badge-item', { 'is-string': !/^\d+$/.test(item.assessValue) }]"
        :value="item.assessValue"
      >
        <el-button size="mini" type="primary" @click="brClick(item)">
          {{ item.itemName ? item.itemName.trim() : "" }}
        </el-button>
      </el-badge>
      <el-button v-else size="mini" type="primary" @click="brClick(item)">
        {{ item.itemName ? item.itemName.trim() : "" }}
      </el-button>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'BD'"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <bd-item :item="item" @change="changeItem(item)"></bd-item>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'P'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      class="color-context"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <template v-if="item.readOnly">
        <span v-if="item.assessValue">
          <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() + "：" : "" }}</span>
          {{ getColorName(item.assessValue, item.colorList) }}
        </span>
      </template>
      <color-picker
        v-else
        :label="item.contentILevel !== '1' && item.itemName ? item.itemName.trim() + '：' : ''"
        :colorArray="item.colorList"
        v-model="item.assessValue"
        :title="item.description"
        :width="getWidth(item.width, 60)"
        @change="changeItem(item)"
      ></color-picker>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'PL'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      class="color-pl-context"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <template v-if="item.readOnly">
        <span v-if="item.assessValue">
          <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() + "：" : "" }}</span>
          {{ getColorName(item.assessValue, item.colorList) }}
        </span>
      </template>
      <color-picker
        v-else
        :label="item.contentILevel !== '1' && item.itemName ? item.itemName.trim() + ':' : ''"
        :colorArray="item.colorList"
        :flat="true"
        :height="getWidth(0, 40)"
        width="auto"
        v-model="item.assessValue"
        @change="changeItem(item)"
      />
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'DT'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      :title="item.description"
      class="date-time-content"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() + ":" : "" }}</span>
      <template v-if="item.readOnly">
        <span v-if="item.assessValue">
          {{ item.assessValue }}
        </span>
      </template>
      <template v-else>
        <el-date-picker
          v-model="item.assessValue"
          type="datetime"
          :clearable="false"
          :disabled="item.disabled"
          @change="changeItem(item)"
          :default-value="new Date()"
          placeholder="选择日期时间"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          :picker-options="checkDate"
          :style="{ width: getWidth(item.width, 215) }"
        ></el-date-picker>
      </template>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'D'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      :title="item.description"
      class="date-content"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() + ":" : "" }}</span>
      <template v-if="item.readOnly">
        <span v-if="item.assessValue">
          {{ item.assessValue }}
        </span>
      </template>
      <template v-else>
        <el-date-picker
          v-model="item.assessValue"
          type="date"
          :picker-options="checkDate"
          :disabled="item.disabled"
          :clearable="false"
          @change="changeItem(item)"
          placeholder="选择日期"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          :style="{ width: getWidth(item.width, 150) }"
        ></el-date-picker>
      </template>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'TM'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      :title="item.description"
      class="time-content"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() + ":" : "" }}</span>
      <template v-if="item.readOnly">
        <span v-if="item.assessValue">
          {{ item.assessValue }}
        </span>
      </template>
      <template v-else>
        <el-time-picker
          v-model="item.assessValue"
          placeholder="选择时间"
          :clearable="false"
          :disabled="item.disabled"
          @change="changeItem(item)"
          value-format="HH:mm"
          format="HH:mm"
          :style="{ width: getWidth(item.width, 95) }"
        ></el-time-picker>
      </template>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'I'"
      v-longpress="{ function: 'showMessage', params: item.description }"
      :title="item.description"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <span v-if="item.contentILevel !== '1'">{{ item.itemName ? item.itemName.trim() + ":" : "" }}</span>
      <img-upload
        @change="changeImg"
        :max-num="imageMaxNum"
        :width="convertPX(300)"
        :default-imgs="item.imageList"
      ></img-upload>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'G'"
      class="graph-item"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <img v-for="(img, index) in item.imageList" :key="index" :src="img" />
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'BP'"
      class="body-part"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <body-part
        v-model="selectBodyPart"
        label=""
        :type="item.bodyPartType"
        :recordsCode="item.bodyPartRecordsCode"
        :gender="gender"
        @change="changeItem(item)"
        :width="getWidth(item.width, 225)"
      ></body-part>
    </div>
    <div
      v-if="item.controlerType && item.controlerType.trim() == 'CS'"
      class="cascade-selector"
      :style="{ backgroundColor: item.backgroundColor ? item.backgroundColor.trim() : 'transparent' }"
    >
      <el-cascader
        :props="{ emitPath: false, expandTrigger: 'hover' }"
        :options="item.selectOptionList"
        :show-all-levels="false"
        v-model="item.assessValue"
        @change="changeItem(item)"
        filterable
        :disabled="item.disabled"
        :style="{ width: getWidth(item.width, 160) }"
      ></el-cascader>
    </div>
    <key-board
      v-tobody="{ id: 'key-board' }"
      :show="isShowKeyBoard"
      :output="el"
      typeName="TN"
      @hide="hideKeyBoard"
    ></key-board>
  </div>
</template>
<script>
import ImgUpload from "@/components/ImgUpload";
import colorPicker from "@/components/colorPicker/colorPicker";
import keyBoard from "@/components/KeyBoard/KeyBoard";
import userSelector from "@/components/selector/userSelector";
import bdItem from "@/components/tabsLayout/bdItem";
import bodyPart from "@/components/bodyPart";
import mtItem from "@/components/tabsLayout/mtItem";
export default {
  props: {
    // 同组项目集合
    items: { require: true },
    // 当前项目
    item: { require: true },
    // 图片最大数量
    imageMaxNum: {},
    // 评估时间
    assessTime: {},
    // 计算公式 参数候选项集合
    formulaParamOptions: {},
    // 性别，获取身体部位需要
    gender: {},
    customMethods: {
      type: Function,
      default: undefined,
    },
  },
  components: {
    keyBoard,
    ImgUpload,
    colorPicker,
    userSelector,
    bdItem,
    bodyPart,
    mtItem,
  },
  data() {
    return {
      isReadOnly: false,
      temp: " ",
      isShowKeyBoard: false,
      el: undefined,
      defaultData: "",
      selectBodyPart: undefined,
      // 限制日期选择
      checkDate: {
        disabledDate: (time) => {
          let startTime = -1;
          let endTime = -1;
          let tempTime = new Date(time).getTime();
          // 限制日期 向前推past天，向后推future天
          if (this.item.past || this.item.past == 0) {
            if (this.item.past == 0) {
              startTime = new Date(this._datetimeUtil.getNowDate()).getTime();
            } else {
              startTime = new Date(
                this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), Number(`-${this.item.past}`))
              ).getTime();
            }
          }
          if (this.item.future || this.item.future == 0) {
            if (this.item.future == 0) {
              endTime = new Date(this._datetimeUtil.getNowDate()).getTime();
            } else {
              endTime = new Date(
                this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), this.item.future)
              ).getTime();
            }
          }
          let startFlag = false;
          let endFlag = false;
          if (startTime !== -1) {
            startFlag = tempTime < startTime;
          }
          if (endTime !== -1) {
            endFlag = tempTime > endTime;
          }
          return startFlag || endFlag;
        },
      },
    };
  },
  watch: {
    item: {
      immediate: true,
      deep: true,
      handler(newValue) {
        if (newValue.controlerType.trim() == "B") {
          this.$emit("b-change-value", newValue);
        } else if (newValue.controlerType.trim() == "BP") {
          if (newValue.linkForm) {
            let str = newValue.linkForm.split("||");
            if (str.length > 1) {
              newValue.bodyPartType = str[0];
              newValue.bodyPartRecordsCode = str[1];
            }
          }
          if (newValue.assessValue) {
            this.selectBodyPart = JSON.parse(newValue.assessValue);
          } else {
            this.selectBodyPart = newValue.bodyPartType === "CommonMulti" ? [] : {};
          }
        } else if (newValue.controlerType.trim() == "BR" || newValue.controlerType.trim() == "BD") {
          this.changeItem(newValue);
        } else if (newValue.controlerType.trim() == "CS" && newValue.assessValue) {
          this.changeItem(newValue);
        }
        // else if (newValue.controlerType.trim() == "C" && newValue.assessValue == "1" && !newValue.initFlag) {
        //   // 防止死循环
        //   newValue.initFlag = true;
        //   newValue.assessValue = "";
        //   this.changeItem(newValue);
        // }
      },
    },
    assessTime: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          if (
            (this.item.controlerType.trim() == "D" ||
              this.item.controlerType.trim() == "DT" ||
              this.item.controlerType.trim() == "TM") &&
            this.item.defaultValue
          ) {
            if (
              this.item.defaultValue.trim() == "A" ||
              (this.item.defaultValue.trim() == "1" && !this.item.assessValue)
            ) {
              if (this.item.controlerType.trim() == "D") {
                this.item.assessValue = this._datetimeUtil.formatDate(this.assessTime, "yyyy-MM-dd");
              }
              if (this.item.controlerType.trim() == "DT") {
                this.item.assessValue = this._datetimeUtil.formatDate(this.assessTime, "yyyy-MM-dd hh:mm");
              }
              if (this.item.controlerType.trim() == "TM") {
                this.item.assessValue = this._datetimeUtil.formatDate(this.assessTime, "hh:mm");
              }
              this.changeItem(this.item);
            }
          }
        }
      },
    },
  },
  mounted() {
    // 自定义指令必须先刷新才管用，模拟刷新组件
    this.temp = "";
  },
  directives: {
    // 自定义指令，处理计算公式的结果
    formula: {
      // 组件更新时计算，实现类似v-model的功能
      componentUpdated(el, binding, vnode) {
        let formula = binding.value.item.formula;
        let params = formula.params;
        let expression = formula.expression;
        let isDate = false;
        // 标识是否全部取默认值
        let allDefaultFlag = true;
        for (let param of params) {
          let value = "";
          if (param.defaultValue || param.defaultValue === "0") {
            value = param.defaultValue;
          }
          let item = binding.value.items.find((item) => {
            return item.assessListID == param.id && item.controlerType.trim() != "L";
          });
          if (item) {
            if (item.controlerType.trim() === "C" || item.controlerType.trim() === "R") {
              if (item.assessValue && !isNaN(item.linkForm)) {
                value = item.linkForm;
                // 有非默认值的项
                allDefaultFlag = false;
              }
            } else {
              if (item.assessValue || (value == "" && !item.assessValue)) {
                value = item.assessValue;
                // 有非默认值的项
                allDefaultFlag = false;
              }
            }
            if (
              item.controlerType.trim() == "D" ||
              item.controlerType.trim() == "DT" ||
              item.controlerType.trim() == "TM"
            ) {
              isDate = true;
              let dateStr = vnode.context._datetimeUtil.formatDate(value, "yyyy-MM-dd hh:mm");
              let date = new Date(dateStr);
              value = date.getTime();
            }
            if ((!value && value !== "0") || isNaN(value)) {
              binding.value.item.assessValue = "";
              vnode.context.changeItem(binding.value.item);
              return false;
            }
          }

          // 替换全部，解决同一参数在公式里出现多次
          let reg = new RegExp(param.key.replace("{", "\\{").replace("}", "\\}"), "g");
          expression = expression.replace(reg, value);
        }
        if (expression.indexOf("}") > 0) {
          binding.value.item.assessValue = "";
        } else {
          if (isDate) {
            if (formula.unit.trim() == "days") {
              // 计算日期相差天数
              expression = "(" + expression + ") / (1000 * 60 * 60 * 24)";
            } else if (formula.unit.trim() == "hours") {
              // 计算日期相差小时数
              expression = "(" + expression + ") / (1000 * 60 * 60)";
            }
          }
          let result =
            vnode.context._decimalUtil.decimalRound(eval(expression), formula.decimalRule, formula.decimalValue) + "";
          // useParamsDefault当所有参数都用默认值参与计算时，计算结果是否有效，true有效；false无效
          // 如果结果为0且全部是默认值且计算结果无效，返回空
          // 或结果为0，又为隐藏其他项目开关的，返回空
          // || (result + "" == "0" && binding.value.item.disableGroup)
          if (result + "" == "0" && allDefaultFlag && !formula.useParamsDefault) {
            binding.value.item.assessValue = "";
          } else {
            binding.value.item.assessValue = result;
          }
        }
        vnode.context.changeItem(binding.value.item);
      },
    },
  },
  methods: {
    getWidth(width, defaultWidth) {
      return (width > 0 ? this.convertPX(Number(width) + 20) : this.convertPX(defaultWidth)) + "px";
    },
    showMessage(messageContent) {
      if (!messageContent) {
        return;
      }
      this._showMessage({
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
    changeImg(imgs) {
      this.$emit("changeImg", imgs);
    },
    changeItem(item) {
      if (item.assessValue == null || item.assessValue == "null") {
        item.assessValue = "";
      }
      if (item.controlerType.trim() == "BP") {
        item.assessValue = JSON.stringify(this.selectBodyPart);
      }
      this.$emit("change-value", item);
    },
    /**
     * @description: 检查是否是布尔true
     * @param assessValue 值
     * @return 
     */
    isAssessValueTrue(assessValue) {
      const formatValue = assessValue?.trim()?.toLowerCase();
      return formatValue === 'true';
    },
    brClick(item) {
      this.$emit("br-click", item);
    },
    getColorName(color, colorList) {
      tempColor = colorList.find((tempColor) => {
        return tempColor.color.trim() == color.trim();
      });
      if (tempColor) {
        return tempColor.colorName.trim();
      }
      return "";
    },
    updateValue(value, item) {
      item.assessValue = value;
    },
    showKeyBoard(e) {
      // 判断浏览器类型，PC返回true，移动端返回false
      if (this._common.isPC()) {
        this.isReadOnly = false;
        return;
      }
      this.isReadOnly = true;
      this.el = e.target;
      this.isShowKeyBoard = true;
    },
    hideKeyBoard() {
      this.el = undefined;
      this.isShowKeyBoard = false;
    },
    inputEnter(e, flag) {
      this.$emit("inputEnter", e, flag);
    },
  },
};
</script>
<style lang="scss">
.layout-item {
  .el-input {
    &.is-disabled .el-input__inner {
      background-color: #eee;
      color: #606266;
      border: 1px solid #ccc;
    }
    .el-input__inner {
      height: 24px !important;
      line-height: 24px !important;
    }
    .el-input-group__append {
      height: 22px !important;
      line-height: 22px !important;
    }
    .el-input__icon {
      line-height: 26px;
    }
  }
  .mt-content {
    margin-bottom: 10px;
  }
  .mt-content,
  .input-content {
    font-size: 16px;
    color: #000;
    margin-top: 3px;
    display: flex;
    white-space: nowrap;
    .el-input__inner {
      padding: 0 5px;
    }
    .unit {
      margin-left: 3px;
    }
  }
  .textarea-content {
    font-size: 16px;
    color: #000;
    margin-top: 3px;
  }
  .check-item {
    cursor: pointer;
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    &.rb-checked,
    &.cb-checked {
      color: var(--color, $base-color);
    }
    i::before {
      background-color: #ffffff;
    }
    .iconfont {
      margin: 0;
      font-size: 16px;
      color: $base-color;
      &.icon-radio-select,
      &.icon-checked {
        color: var(--color, $base-color);
      }
    }
  }
  .date-time-content .el-input,
  .date-content .el-input,
  .time-content .el-input {
    .el-input__inner {
      height: 26px;
      line-height: 26px;
    }
    .el-input__prefix {
      top: -2px;
    }
  }
  .button-content {
    line-height: 21px;
    .el-button {
      padding: 3px 12px;
    }
    .badge-item {
      margin-right: 5px;
      &.is-string {
        margin-top: -12px;
        .is-fixed {
          top: -4px;
        }
      }
      .is-fixed {
        z-index: 999;
        top: 3px;
        right: 12px;
      }
      .is-fixed.is-dot {
        top: 3px;
        right: 5px;
      }
    }
  }
  .color-pl-context {
    display: flex;
    .color-label {
      white-space: nowrap;
    }
    .color {
      display: inline-block;
      cursor: pointer;
      margin: 0 0 5px 10px;
      border: 1px solid #ebeef5;
      box-sizing: border-box;
      &.selected {
        border: 1px solid #ff7400;
      }
      .color-block {
        width: 45px;
        height: 16px;
        border-bottom: 1px solid #ebeef5;
        box-sizing: border-box;
      }
      .color-name {
        width: 45px;
        text-align: center;
        font-size: 16px;
        margin-top: -2px;
      }
    }
  }
  .graph-item img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  .body-part .select-body-part,
  .body-part .select-body-part .body-part-name {
    height: 26px;
    line-height: 26px;
  }
  @media screen and (max-width: 1024px) {
    .input-content,
    .check-item,
    .check-item .iconfont,
    .color-pl-context .color .color-name {
      font-size: 14px;
    }
  }
}
</style>
