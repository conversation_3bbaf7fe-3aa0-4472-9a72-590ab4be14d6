<!--
 * FilePath     : \src\autoPages\cRRTRecord\index.vue
 * Author       : 杨欣欣
 * Date         : 2022-08-01 09:33
 * LastEditors  : 来江禹
 * LastEditTime : 2024-06-11 09:32
 * Description  : CRRT记录单
 * CodeIterationRecord: 2933-作为IT人员，我需要优化CRRT专项护理及病历 -杨欣欣
-->
<template>
  <specific-care
    class="crrt-record"
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    :editFlag="checkResult"
    :careMainAddFlag="careMainAddFlag"
    :drawerSize="supplementFlag ? '80%' : ''"
    @mainAdd="recordAdd"
    @maintainAdd="careMainAdd"
    @save="saveCRRT"
    @cancel="drawerClose"
    @getHandOverFlag="getHandOverFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    :mainTableHeight="tableOneRowHeight"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <!-- 主记录 -->
    <div slot="main-record">
      <packaging-table
        v-model="recordTableData"
        :headerList="recordTableHeaderList"
        @rowClick="recordClick"
        ref="recordTable"
      >
        <!-- 治疗时间 插槽 -->
        <div slot="durationOfTherapy" slot-scope="scope">
          <span v-if="scope.row.crrtDurationOfTherapy">
            {{ scope.row.crrtDurationOfTherapy + "h" }}
          </span>
          <span v-if="scope.row.crrtDurationOfTherapyTime">
            {{ scope.row.crrtDurationOfTherapyTime + "min" }}
          </span>
        </div>
        <!-- 操作 插槽-->
        <div fixed="right" slot="operate" slot-scope="scope">
          <el-tooltip content="修改">
            <div @click.stop="recordAdd(scope.row)" class="iconfont icon-edit"></div>
          </el-tooltip>
          <el-tooltip content="停止">
            <div @click.stop="recordEnd(scope.row)" :class="['iconfont', { 'icon-stop': !scope.row.endDate }]"></div>
          </el-tooltip>
          <el-tooltip content="删除">
            <div @click.stop="deleteRecord(scope.row)" class="iconfont icon-del"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <packaging-table v-model="careMainTableData" :headerList="careMainTableHeaderList">
        <!-- 评估类型 插槽 -->
        <div slot="assessType" slot-scope="row">
          <span v-if="row.row.recordsCode.indexOf('Start') != -1">开始评估</span>
          <span v-else-if="row.row.recordsCode.indexOf('End') != -1">结束评估</span>
          <span v-else>例行评估</span>
        </div>
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope" v-if="scope.row.recordsCode.indexOf('Start') == -1">
          <el-tooltip content="修改" placement="top">
            <div class="iconfont icon-edit" @click="careMainAdd(scope.row)"></div>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <div class="iconfont icon-del" @click="deleteCareMain(scope.row)"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 抽屉 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
    >
      <div slot="header">
        <span class="label">日期:</span>
        <el-date-picker
          class="date-picker"
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        ></el-date-picker>
        <el-time-picker
          class="time-picker"
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        ></el-time-picker>
        <station-selector v-model="stationID" label="病区:" :width="convertPX(420) + 'px'" />
        <dept-selector label="" :width="convertPX(350) + 'px'" v-model="departmentListID" :stationID="stationID" />
        <nurse-selector
          v-if="recordsCode && recordsCode.indexOf('Start') != -1"
          :disabled="!selectUserFlag"
          label="记录人员："
          :width="convertPX(240) + 'px'"
          v-model="userID"
          :stationID="stationID"
        ></nurse-selector>
      </div>
      <tabs-layout
        ref="tabsLayout"
        :template-list="templateDatas"
        @button-click="buttonClick"
        @change-values="changeValues"
        @checkTN="checkTN"
      />
    </base-layout>
    <!-- 弹窗 -->
    <div class="drawer-dialog" slot="drawer-dialog">
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonName"
        :visible.sync="showButtonDialog"
        fullscreen
        custom-class="no-footer specific-care-view"
        :append-to-body="true"
      >
        <iframe v-if="showButtonDialog" ref="buttonDialog" scrolling="no" frameborder="0" width="100%" height="99%" />
      </el-dialog>
    </div>
  </specific-care>
</template>

<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import nurseSelector from "@/components/selector/nurseSelector";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import { mapGetters } from "vuex";
import {
  GetCRRTRecordList,
  GetCRRTCareMainListByRecordID,
  GetCRRTRecordsCodeInfo,
  GetCRRTAssessView,
  AddCRRTRecord,
  AddCRRTCare,
  AddCRRTEnd,
  UpdateCRRTRecord,
  UpdateCRRTCare,
  DeleteCRRTByID,
  DeleteCRRTCare,
} from "@/api/CRRTRecord";
import { GetBringToShiftSetting } from "@/api/Setting";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { GetButtonData } from "@/api/Assess";
import { GetBringToNursingRecordFlagSetting, GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  components: {
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    baseLayout,
    nurseSelector,
    packagingTable,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      loading: false,
      patient: undefined,
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      //主记录表格表头加第一行的高度
      tableOneRowHeight: undefined,
      //主记录变量
      recordTableData: [],
      recordID: undefined,
      currentRecord: undefined,
      //维护记录变量
      careMainTableData: [],
      careMainID: undefined,
      // 新增修改标记
      addFlag: true,
      //弹窗变量
      drawerLoading: false,
      drawerLoadingText: undefined,
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      userID: undefined,
      departmentListID: undefined,
      templateDatas: [],
      recordsCodeInfo: {},
      assessDatas: [],
      checkTNFlag: true,
      recordsCode: undefined,
      handOverArr: [true, false],
      informPhysicianArr: [true, false],
      bringToNursingRecordArr: [true, false],
      settingHandOver: false,
      settingNursingRecord: false,
      //路由变量
      patientScheduleMainID: undefined,
      assessMainID: undefined,
      // 评估次数
      assessSort: 0,
      sourceID: undefined,
      sourceType: undefined,
      supplementFlag: "",
      checkResult: true,
      selectUserFlag: false,
      // 动态表头
      recordTableHeaderList: [],
      careMainTableHeaderList: [],
      // 维护记录新增按钮开关
      careMainAddFlag: true,
      // 专项按钮跳转相关参数
      showButtonDialog: false,
      buttonName: "",
    };
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.supplementFlag = "";
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.supplementFlag = "*";
          this.bringToNursingRecordArr = [false, false];
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
    showButtonDialog(newVal) {
      if (!newVal) {
        this.updateButton();
      }
    },
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    async init() {
      if (this.$route.query.patientScheduleMainID) {
        this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
      }
      this.assessMainID = this.$route.query.num;
      this.assessSort = this.$route.query.sort;
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
      if (this.patientScheduleMainID || this.assessMainID || this.sourceID || this.sourceType) {
        this._sendBroadcast("setPatientSwitch", false);
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
      await this.getRecordTableData();
      await this.getTableHeaderList("Record", "recordTableHeaderList");
      this.$refs.recordTable?.doLayout();
      await this.getTableHeaderList("Maintain", "careMainTableHeaderList");
      this.getBringHandOverSetting();
      this.getBringToNursingRecordSetting();
      this.getModifyUserIDSetting();
    },
    /**
     * description: 获取维护记录动态列
     * param {*} classSub 副类别
     * param {*} headerList 要赋值的变量
     * return {*}
     */
    async getTableHeaderList(classSub, headerList) {
      let params = {
        fileClassID: 101,
        fileClassSub: classSub,
        useDescription: "1||Table",
        newSourceFlag: true,
      };
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this[headerList] = res.data;
        }
      });
    },
    /**
     * description: 弹窗保存按钮点击
     * param {*}
     * return {*}
     */
    async saveCRRT() {
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      if (this.recordsCodeInfo.recordsCode.indexOf("Start") != -1) {
        await this.recordSave();
      }
      if (this.recordsCodeInfo.recordsCode.indexOf("Maintain") != -1) {
        await this.careMainSave();
      }
      if (this.recordsCodeInfo.recordsCode.indexOf("End") != -1) {
        await this.recordEndSave();
      }
      this.drawerLoading = false;
      this.drawerLoadingText = "";
    },

    /*-------------主记录CRUD-------------*/

    /**
     * description: 获取主记录数据
     * param {*}
     * return {*}
     */
    async getRecordTableData(recordID = undefined) {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        recordID,
      };
      this.loading = true;
      //获取病人主记录列表
      await GetCRRTRecordList(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.recordTableData = result.data;
          this.currentRecord = recordID && this.recordTableData?.length ? this.recordTableData[0] : undefined;
        }
      });
    },

    /**
     * description: 主记录新增修改
     * param {*} item 要保存的数据
     * return {*}
     */
    async recordAdd(item) {
      this.recordsCode = "CRRTStart";
      this.openOrCloseDrawer(true, "CRRT主记录");
      if (item) {
        //权限检核
        await this.checkAuthor(item.patientCRRTRecordID, "PatientCRRTRecord", item.userID);
        this.addFlag = false;
        this.careMainID = item.patientCRRTCareMainID;
        this.assessDate = this._datetimeUtil.formatDate(item.startDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.startTime, "hh:mm");
        this.stationID = item.stationID;
        this.departmentListID = item.departmentListID;
        this.recordID = item.patientCRRTRecordID;
        this.userID = item.userID;
      } else {
        this.addFlag = true;
        this.careMainID = "temp_" + this._common.guid();
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.userID = this.user.userID;
        this.recordID = undefined;
      }
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.bringToNursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingNursingRecord);
      await this.getAssessTemplate();
    },

    /**
     * description: 主记录保存
     * param {*}
     * return {*}
     */
    async recordSave() {
      // 数据验证
      if (!this.saveCheck()) {
        return;
      }
      let saveDataView = this.createRecordSaveView();
      let req = this.addFlag ? AddCRRTRecord(saveDataView) : UpdateCRRTRecord(saveDataView);

      await req.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.addFlag ? "新增" : "修改"}成功！`);
        }
      });
      this.openOrCloseDrawer(false);
      this.fixTable();
      this.getRecordTableData();
    },
    /**
     * description: 创建主记录保存请求View
     * param {*}
     * return {*}
     */
    createRecordSaveView() {
      let saveData = {
        PatientCRRTCareMainID: this.getCareMainID(),
        Details: this.getDetails(),
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        NursingLevel: this.patient.nursingLevelCode,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        AssessSort: this.assessSort,
        PatientScheduleMainID: this.patientScheduleMainID,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        Record: {
          PatientCRRTRecordID: this.recordID,
          InpatientID: this.patient.inpatientID,
          StartDate: this.assessDate,
          StartTime: this.assessTime,
          StationID: this.stationID,
          DepartmentListID: this.departmentListID,
          addEmployeeID: this.userID,
          RefillFlag: this.supplementFlag,
        },
      };
      return saveData;
    },
    /**
     * description: 保存检核
     * param {*}
     * return {*}
     */
    saveCheck() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return false;
      }
      if (!this.departmentListID) {
        this._showTip("warning", "请选择科室");
        return false;
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        return false;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      return true;
    },

    /**
     * description: 主记录停止
     * return {*} void
     * param {*} item 当前主记录行数据
     */
    async recordEnd(item) {
      //是否仅本人操作
      this.checkResult = true;

      this.addFlag = true;
      this.currentRecord = item;
      this.recordsCode = "CRRTEnd";
      this.recordID = item.patientCRRTRecordID;
      this.careMainID = "temp_" + this._common.guid();
      this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
      this.stationID = this.patient.stationID;
      this.departmentListID = this.patient.departmentListID;
      this.bedID = this.patient.bedID;
      this.bedNumber = this.patient.bedNumber;

      this.$set(this.handOverArr, 1, this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, this.settingNursingRecord);
      this.openOrCloseDrawer(true, "CRRT停止");
      await this.getAssessTemplate();
    },

    /**
     * description: 主记录停止保存
     * param {*}
     * return {*}
     */
    async recordEndSave() {
      if (!this.saveCheck()) {
        return;
      }
      let saveData = {
        PatientCRRTRecordID: this.recordID,
        PatientCRRTCareMainID: this.getCareMainID(),
        PatientScheduleMainID: this.patientScheduleMainID,
        NursingLevel: this.patient.nursingLevelCode,
        StationID: this.stationID,
        DepartmentListID: this.departmentListID,
        BedID: this.bedID,
        BedNumber: this.bedNumber,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        RefillFlag: this.supplementFlag,
        Details: this.getDetails(),
        SourceID: this.sourceID,
        SourceType: this.sourceType,
      };

      let req = this.addFlag ? AddCRRTEnd(saveData) : UpdateCRRTCare(saveData);
      await req.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.addFlag ? "停止" : "修改"}成功！`);
          this.fixTable();
          this.getRecordTableData();
        }
        this.openOrCloseDrawer(false);
      });
    },
    /**
     * description: 主记录删除
     * param {*} row 当前行数据
     * return {*}
     */
    async deleteRecord(row) {
      //权限检核
      await this.checkAuthor(row.patientCRRTRecordID, "PatientCRRTRecord", row.userID);
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            RecordID: row.patientCRRTRecordID,
          };
          this.loading = true;
          DeleteCRRTByID(params).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              this.fixTable();
              this._showTip("success", "删除成功！");
              this.getRecordTableData();
            }
          });
        }
      });
    },

    /*-------------维护记录CRUD-------------*/
    /**
     * description: 点击选中主记录，展示维护记录
     * param {*} row 当前行数据
     * return {*}
     */
    async recordClick(row) {
      this.currentRecord = row;
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.recordTableData = [row];
        this.careMainAddFlag = !row.endDate;
        this.$nextTick(() => {
          this.tableOneRowHeight = this._common.getTableOneRowHeight(
            this.$refs.recordTable?.$el,
            ".main-record-row",
            ".main-record-header-row"
          );
        });
        this.getCareMainTableData();
      } else {
        this.getRecordTableData();
      }
    },
    /**
     * description: 根据RecordID获取维护记录
     * param {*}
     * return {*}
     */
    async getCareMainTableData() {
      let params = {
        recordID: this.currentRecord.patientCRRTRecordID,
      };
      this.loading = true;
      await GetCRRTCareMainListByRecordID(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.careMainTableData = result.data;
        }
      });
    },

    /**
     * description: 维护记录新增修改
     * param {*} item 当前行数据
     * return {*}
     */
    async careMainAdd(item) {
      this.openOrCloseDrawer(true, "CRRT维护记录");

      this.recordID = this.currentRecord.patientCRRTRecordID;
      if (item) {
        //权限检核
        await this.checkAuthor(item.patientCRRTCareMainID, "PatientCRRTCareMain", item.userID);
        this.addFlag = false;
        this.careMainID = item.patientCRRTCareMainID;
        this.assessDate = this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.assessTime, "hh:mm");
        this.stationID = item.stationID;
        this.departmentListID = item.departmentListID;
        this.recordsCode = item.recordsCode;
        this.userID = item.userID;
      } else {
        this.addFlag = true;
        this.careMainID = "temp_" + this._common.guid();
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowDate("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.bedID = this.patient.bedID;
        this.bedNumber = this.patient.bedNumber;
        this.recordsCode = "CRRTMaintain";
      }

      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingNursingRecord);
      await this.getAssessTemplate();
    },

    /**
     * description: 维护记录保存
     * param {*}
     * return {*}
     */
    async careMainSave() {
      if (!this.saveCheck()) {
        return;
      }
      let saveData = this.createCareMainSaveView();
      let req = this.addFlag ? AddCRRTCare(saveData) : UpdateCRRTCare(saveData);

      await req.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.addFlag ? "新增" : "修改"}成功！`);
          this.getRecordTableData(this.currentRecord.patientCRRTRecordID);
          this.getCareMainTableData();
        }
        this.openOrCloseDrawer(false);
      });
    },
    /**
     * description: 维护记录保存model
     * param {*}
     * return {*}
     */
    createCareMainSaveView() {
      let saveData = {
        PatientCRRTRecordID: this.currentRecord.patientCRRTRecordID,
        PatientCRRTCareMainID: this.getCareMainID(),
        PatientScheduleMainID: this.patientScheduleMainID,
        BedID: this.bedID,
        BedNumber: this.bedNumber,
        StationID: this.stationID,
        DepartmentListID: this.departmentListID,
        NursingLevel: this.patient.nursingLevelCode,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        RefillFlag: this.supplementFlag,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        Details: this.getDetails(),
      };
      return saveData;
    },

    /**
     * description: 维护记录删除
     * param {*} row 删除行
     * return {*}
     */
    async deleteCareMain(row) {
      // 是否仅本人操作
      await this.checkAuthor(row.patientCRRTCareMainID, "PatientCRRTCareMain", row.userID);
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let param = {
            CareMainID: row.patientCRRTCareMainID,
          };
          this.loading = true;
          DeleteCRRTCare(param).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              row.recordsCode.includes("End") && (this.careMainAddFlag = true);
              this.getRecordTableData(row.patientCRRTRecordID);
              this.getCareMainTableData();
            }
          });
        }
      });
    },
    /**
     * description: 重置选中状态
     * param {*}
     * return {*}
     */
    fixTable() {
      this.showRecordArr = [true, false];
      this.currentRecord = undefined;
      this.careMainTableData = [];
    },

    /**
     * description: 获取评估模板
     * param {*}
     * return {*}
     */
    async getAssessTemplate() {
      this.drawerLoadingText = "加载中……";
      this.drawerLoading = true;
      let params = {
        recordsCode: this.recordsCode,
        departmentListID: this.patient.departmentListID,
      };
      await GetCRRTRecordsCodeInfo(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo || !this.recordsCodeInfo.recordsCode) {
        this.openOrCloseDrawer(false);
        this._showTip("warning", "找不到评估模板！");
        return;
      }
      params = {
        recordID: this.recordID,
        careMainID: this.careMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        inpatientID: this.patient.inpatientID,
        dateOfBirth: this.patient.dateOfBirth,
        addFlag: this.addFlag,
      };
      this.templateDatas = [];
      await GetCRRTAssessView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
      this.drawerLoading = false;
    },
    /**
     * description: 评估组件回传数据
     * param {*} datas
     * return {*}
     */
    changeValues(datas) {
      this.assessDatas = datas;
    },
    /**
     * description: 评估组件按钮事件
     * return {*}
     * param {*} content 跳转明细项
     */
    buttonClick(content) {
      this.showButtonDialog = true;
      this.buttonName = content.itemName;
      this.assessListID = content.assessListID;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        `&sourceID=${this.getCareMainID()}` +
        "&sourceType=CRRT" +
        "&isDialog=true";
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 获取维护记录CareMainID
     * return {*} 维护记录CareMainID
     * param {*}
     */
    getCareMainID() {
      let tempCareMainID = "";
      if (this.careMainID) {
        if (this.careMainID.indexOf("temp") != -1) {
          tempCareMainID = this.careMainID.split("_")[1];
        } else {
          tempCareMainID = this.careMainID;
        }
      }
      return tempCareMainID;
    },
    /*-------------获取页面配置-------------*/
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "CRRT",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingHandOver = res.data;
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "CRRTAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },

    /*-------------配合保存方法-------------*/
    /**
     * description: 获取保存明细
     * param {*}
     * return {*}
     */
    getDetails() {
      let details = [];
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        if (content.disableGroup != -1) {
          details.push(detail);
        }
      });
      return details;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 权限检核
     * param {*} id 主记录/主表ID
     * param {*} tableName
     * return {*}
     */
    async checkAuthor(id, tableName, userID) {
      this.checkResult = await this._common.checkActionAuthorization(this.user, userID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, tableName, !!this.supplementFlag);
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
    },
    /*-------------专项护理组件逻辑-------------*/
    /**
     * description: 组件回传交班flag
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 通知医师标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.bringToNursingRecordArr[1] = flag;
    },
    /**
     * description: 弹窗关闭
     * param {*}
     * return {*}
     */
    drawerClose() {
      this.recordID = undefined;
      this.careMainID = undefined;
      this.templateDatas = [];
      this.assessDatas = [];
      this.showTemplateFlag = false;
    },
    /**
     * description: 弹窗开关
     * param {*} flag 开关动作
     * param {*} title 弹窗标题
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },
    /**
     * description: 获取是否可以更改记录人
     * param {*}
     * return {*}
     */
    getModifyUserIDSetting() {
      let param = {
        SettingTypeCode: "CrrtMaintainer",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.selectUserFlag = response.data;
        }
      });
    },
    /**
     * description: 添加完更新按钮数据
     * return {*}
     */
    async updateButton() {
      let item = await this.getButtonValue(this.assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },

    /**
     * description: 获取按钮值
     * return {*}
     * param {*} assessListID
     */
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCode,
        assessListID: assessListID,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
  },
};
</script>

<style lang="scss" >
.crrt-record {
  .base-layout {
    .base-header {
      .date-picker {
        width: 160px;
      }
      .time-picker {
        width: 100px;
      }
    }
  }
}
</style>