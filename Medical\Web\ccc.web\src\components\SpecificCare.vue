<!--
 * FilePath     : \src\components\SpecificCare.vue
 * Author       : 郭鹏超
 * Date         : 2021-03-27 16:01
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-06-11 15:37
 * Description  : 专项护理组件
 组件使用介绍：
        交互prop：
          1)value:用于控制drawer弹窗的开关 已双向绑定
          2)drawerTitle:用于控制drawer弹窗的标题
          3)drawerDirection:用户控制drawer弹窗弹出方向，可选项：rtl(右to左)、 ltr（左to右）、ttb（顶to底）、btt（底to顶）
            可以不传，若不传 PC端认btt（底部），平板端依据屏幕方向自动识别
          4)drawerSize:用于控制drawer弹窗大小 默认为70%，drawerDirection为rtl和ltr时drawerSize表示宽度，
            drawerDirection为ttb和btt时drawerSize表示高度
          5)showRecordArr:用于控制顶部主记录与维护记录勾选框的默认勾选 为数组 第一个元素控制主记录 第二个元素控制维护记录
            true为勾选 false为不勾选 默认为勾选主记录
          6)showRecordLabelArr:顶部右侧两个勾选的label 默认为主记录 维护记录
          7)mainTitle:用于控制主记录的标题 为数组 数组长度为1时显示文字 为2时显示为现有记录与历史记录的按钮 默认为显示主记录文字
          8)mainTableHeight主记录收起时主记录表格高度 默认为72 依据各父页面自动计算好传入
          9)editFlag:控制是否显示弹窗底部操作按钮
          10)informPhysicianFlag: 用于控制通知医师勾选框 默认[false,false] arr[0]控制勾选框是否显示 arr[1]勾选框的值
          11)handOverFlag: 用于控制带入交班勾选框 默认[false,false] arr[0]控制勾选框是否显示 arr[1]勾选框的值
          12)recordTitleSlotFalg:主记录顶部DOM是否使用自定义DOM 默认显示为主记录 支持自定义DOM插槽
          13)careMainTitleSlotFalg:维护记录顶部DOM是否使用自定义DOM 默认显示为维护记录 支持自定义DOM插槽
          14)recordAddFlag:主记录右侧新增按钮是否显示 (维护记录显示时 主记录自动隐藏)
          15)careMainAddFlag:维护记录右侧按钮是否显示
          16)previewFlag:是否显示弹窗保存按钮
        事件发射:
          1)mainAdd:主记录新增事件
          2)maintainAdd:维护记录新增事件
          3)save:drawer弹窗保存事件
          4)cancel:drawer弹窗取消事件
          5)tableDataFlag:主记录顶部为按钮时 按钮切换事件
          6)getMainFlag:顶部主记录勾选框勾选与不勾选事件
          7)getMaintainFlag:顶部维护记录勾选框勾选与不勾选事件
        插槽slot:
          1)main-record:主记录表格插槽
          2)maintain-record:维护记录插槽
          3)drawer-content:drawer弹窗内容插槽
          4)specific-care-time:顶部左侧插槽 用于存放筛选记录的时间
          5)getHandOverFlag:弹窗底部带入交班勾选事件
          6)getNursingRecordFlag:弹窗底部带入护理记录勾选事件
          7)getInformPhysicianFlag:弹窗底部通知医师勾选事件
          8)record-title:主记录左侧标题插槽
          9)careMain-title:维护记录左侧标题插槽
          10)drawer-dialog:页面弹窗插槽
-->
<template>
  <div class="specific-care" ref="specificCare">
    <div class="specific-care-content">
      <div class="main-record" :style="{ height: mainRecordHeight }">
        <div ref="mainRecordHeader" class="main-record-header">
          <label v-if="!recordTitleSlotFalg">主记录</label>
          <div class="record-title" v-if="recordTitleSlotFalg">
            <slot name="record-title"></slot>
          </div>
          <el-button
            class="add-button"
            v-if="!(!mainFlag && maintainFlag) && recordAddFlag"
            @click="mainAdd()"
            icon="iconfont icon-add"
          >
            新增
          </el-button>
        </div>
        <div ref="mainTable" class="main-record-content">
          <slot name="main-record"></slot>
        </div>
      </div>
      <div ref="maintainRecord" class="maintain-record" v-if="maintainFlag" :style="{ height: maintainRecordHeight }">
        <div class="maintain-record-header">
          <label v-if="!careMainTitleSlotFalg">维护记录</label>
          <div class="careMain-title" v-if="careMainTitleSlotFalg">
            <slot name="careMain-title"></slot>
          </div>
          <el-button v-if="careMainAddFlag" @click="maintainAdd()" class="add-button" icon="iconfont icon-add">
            新增
          </el-button>
        </div>
        <div class="maintain-record-content">
          <slot name="maintain-record"></slot>
        </div>
      </div>
    </div>
    <el-drawer
      :title="drawerTitle"
      :modal-append-to-body="false"
      :visible.sync="showMaintainFlag"
      :destroy-on-close="true"
      :direction="direction"
      :size="drawerHeight"
      @closed="cancel()"
      custom-class="specific-caredrawer"
      ref="specificCareDrewer"
      :wrapperClosable="false"
      :append-to-body="false"
    >
      <div :class="['drawer-content', { edit: editFlag }]">
        <slot name="drawer-content"></slot>
      </div>
      <div v-if="editFlag" class="drawer-footer">
        <el-checkbox
          v-if="informPhysicianFlag[0]"
          @change="emitInformPhysicianFlag"
          class="bring-checkbox"
          v-model="informPhysician"
        >
          通知医师
        </el-checkbox>
        <el-checkbox v-if="handOverFlag[0]" @change="emitHandOverFlag" class="bring-checkbox" v-model="bringToHandOver">
          带入交班
        </el-checkbox>
        <el-checkbox
          v-if="nursingRecordFlag[0]"
          @change="emitNursingRecordFlag"
          class="bring-checkbox"
          v-model="bringToNursingRecord"
        >
          带入护理记录单
        </el-checkbox>
        <el-button @click="cancel()">取消</el-button>
        <el-button v-if="!previewFlag" @click="save()" type="primary">保存</el-button>
      </div>
    </el-drawer>
    <div>
      <slot name="drawer-dialog"></slot>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters({
      specialDrawerHeightSwitch: "getSpecialDrawerHeightSwitch",
    }),
  },
  props: {
    //drawer弹窗开关
    value: {
      type: Boolean,
      default: false,
    },
    //drawer弹窗标题
    drawerTitle: {
      type: String,
      default: "",
    },
    // drawer弹窗方向
    drawerDirection: {
      type: String,
      default: "",
    },
    // drawerDirection为rtl和ltr时drawerSize表示宽度，drawerDirection为ttb和btt时drawerSize表示高度
    drawerSize: {
      default: "",
    },
    //顶部主记录与维护记录label
    showRecordLabelArr: {
      type: Array,
      default: () => {
        return ["主记录", "维护记录"];
      },
    },
    //顶部主记录与维护记录开关
    showRecordArr: {
      type: Array,
      default: () => {
        return [true, false];
      },
    },
    //主记录标题及显示形式 数组长度为1显示文字 长度为2显示开关
    mainTitle: {
      type: Array,
      default: () => {
        return ["主记录"];
      },
    },
    //主记录收起时表格高度
    mainTableHeight: {
      type: Number,
      default: 72,
    },
    //是否带入护理记录
    nursingRecordFlag: {
      type: Array,
      default: () => {
        return [false, false];
      },
    },
    //是否通知医师
    informPhysicianFlag: {
      type: Array,
      default: () => {
        return [false, false];
      },
    },
    //是否带入交班
    handOverFlag: {
      type: Array,
      default: () => {
        return [false, false];
      },
    },
    //主记录顶部是否使用插槽标记
    recordTitleSlotFalg: {
      type: Boolean,
      default: false,
    },
    //维护记录顶部是否使用插槽标记
    careMainTitleSlotFalg: {
      type: Boolean,
      default: false,
    },
    //主记录新增按钮是否显示
    recordAddFlag: {
      type: Boolean,
      default: true,
    },
    //维护记录新增按钮是否显示
    careMainAddFlag: {
      type: Boolean,
      default: true,
    },
    //是否为预览模式.true为预览模式
    previewFlag: {
      type: Boolean,
      default: false,
    },
    //控制是否显示弹窗底部操作按钮
    editFlag: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    value: {
      handler(newValue) {
        this.showMaintainFlag = newValue;
      },
      immediate: true,
    },
    showMaintainFlag: {
      handler(newValue) {
        this.$emit("input", newValue);
        this.countDrawerSize();
      },
    },
    showRecordArr: {
      handler(newVal) {
        if (!newVal.length) {
          return;
        }
        this.mainFlag = newVal[0];
        this.maintainFlag = newVal[1];
        this.showRecord();
      },
      immediate: true,
    },
    nursingRecordFlag: {
      handler(newValue) {
        this.bringToNursingRecord = newValue[1];
        this.emitNursingRecordFlag(this.bringToNursingRecord);
      },
      immediate: true,
    },
    handOverFlag: {
      handler(newValue) {
        this.bringToHandOver = newValue[1];
        this.emitHandOverFlag(this.bringToHandOver);
      },
      immediate: true,
      deep: true,
    },
    informPhysicianFlag: {
      handler(newValue) {
        this.informPhysician = newValue[1];
        this.emitInformPhysicianFlag(this.informPhysician);
      },
      immediate: true,
      deep: true,
    },
    mainTableHeight: {
      handler(newValue) {
        this.showRecord();
      },
    },
  },
  data() {
    return {
      //切换页面开关
      mainFlag: true,
      maintainFlag: false,
      //drawer弹窗开关
      showMaintainFlag: false,
      //drawer弹窗方向
      direction: "btt",
      //主记录高度
      mainRecordHeight: "",
      //维护记录高度
      maintainRecordHeight: "",
      //切换历史记录与现有记录开关
      typeFlag: false,
      //带入交班
      bringToHandOver: this.handOverFlag[1],
      //带入护理记录
      bringToNursingRecord: this.nursingRecordFlag[1],
      //通知医师
      informPhysician: false,
      //抽屉高度
      drawerHeight: "",
    };
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.setDrawerDirection();
        this.showRecord();
      })();
    };
    this.setDrawerDirection();
  },
  destroyed() {
    window.onresize = undefined;
  },
  methods: {
    /**
     * description: 计算抽屉高度
     * return {*}
     */
    countDrawerSize() {
      //父组件传drawerSize优先使用
      if (this.drawerSize) {
        this.drawerHeight = this.drawerSize;
        return;
      }
      //抽屉打开方式不是从下往上
      if (this.direction != "btt") {
        this.drawerHeight = "80%";
        return;
      }
      this.$nextTick(() => {
        //弹窗高度遮挡主记录
        if (this.specialDrawerHeightSwitch) {
          this.drawerHeight = this.$refs.specificCare.offsetHeight + "px";
          return;
        }
        //弹窗高度显示主记录(主记录无数据，显示表头；主记录有数据，显示主记录第一行以及表头)
        let tableDom = this.$refs.mainTable;
        //获取不到表格DOM元素，赋值高度80%返回
        if (!tableDom) {
          this.drawerHeight = "80%";
          return;
        }
        let headerDom = tableDom.getElementsByTagName("thead");
        let rowDom = tableDom.getElementsByTagName("tbody")[0].getElementsByTagName("tr");
        let rowDomHeight = rowDom && rowDom.length > 0 ? rowDom[0].offsetHeight : 0;
        let headerDomHeight = headerDom && headerDom.length > 0 ? headerDom[0].offsetHeight : 0;
        this.drawerHeight =
          this.$refs.specificCare.offsetHeight -
          this.$refs.mainRecordHeader.offsetHeight -
          rowDomHeight -
          headerDomHeight +
          "px";
      });
    },
    setDrawerDirection() {
      this.direction = "btt";
      let a = Math.random();
      if (this.drawerDirection) {
        // 父层传参，以传参为主
        this.direction = this.drawerDirection;
      } else {
        // 父层没有传参
        if (this._common.isPC()) {
          // PC端 默认下向上弹
          this.direction = "btt";
        } else {
          if (document.body.clientWidth > document.body.clientHeight) {
            // 平板  横屏从右向左弹
            this.direction = "rtl";
          } else {
            // 平板 竖屏从下向上弹
            this.direction = "btt";
          }
        }
      }
    },
    //新增主记录按钮事件
    mainAdd() {
      this.$emit("mainAdd");
    },
    //新增维护记录按钮事件
    maintainAdd() {
      this.$emit("maintainAdd");
    },
    //弹窗取消事件
    cancel() {
      this.value = false;
      this.$emit("input", this.value);
      this.$emit("cancel");
    },
    //弹窗保存事件
    save() {
      this.$emit("save");
    },
    //切换历史记录和现有记录 false为现有记录 ture为历史输血
    postTypeFlag(flag) {
      this.$emit("tableDataFlag", flag);
    },
    //发射勾选交班数据
    emitHandOverFlag(flag) {
      this.$emit("getHandOverFlag", flag);
    },
    //发射勾选带入护理记录单数据
    emitNursingRecordFlag(flag) {
      this.$emit("getNursingRecordFlag", flag);
    },
    emitInformPhysicianFlag(flag) {
      this.$emit("getInformPhysicianFlag", flag);
    },
    //顶部主记录维护记录勾选框事件  表格显示形态
    showRecord(flag) {
      if (!this.mainFlag && !this.maintainFlag) {
        this[flag] = true;
        return;
      }
      if (flag == "mainFlag") {
        //勾选或取消主记录事件
        this.$emit("getMainFlag", this.mainFlag);
      }
      if (flag == "maintainFlag") {
        //勾选或取消主记录事件
        this.$emit("getMaintainFlag", this.maintainFlag);
      }
      if (!flag) {
        this.$emit(this.maintainFlag ? "getMaintainFlag" : "getMainFlag", true);
      }
      //只勾选主记录  只显示主记录数据
      if (this.mainFlag && !this.maintainFlag) {
        this.mainRecordHeight = "calc(100% - " + this.convertPX(5) + "px)";
      }
      //勾选主记录和维护记录  主记录数据和维护数据各占50%
      if (this.mainFlag && this.maintainFlag) {
        this.mainRecordHeight = "calc(50% - " + this.convertPX(5) + "px)";
        this.maintainRecordHeight = "50%";
      }
      //只勾选维护记录 主记录只显示选中行 维护记录填充剩余页面
      if (!this.mainFlag && this.maintainFlag) {
        this.mainRecordHeight = this.mainTableHeight + this.convertPX(63) + "px";
        this.maintainRecordHeight = "calc(100% - " + (this.mainTableHeight + this.convertPX(70)) + "px)";
      }
    },
  },
};
</script>

<style lang="scss" >
.specific-care {
  height: 100%;
  box-sizing: border-box;
  .specific-care-top {
    height: 45px;
    margin-bottom: 5px;
    padding: 0 10px;
    box-sizing: border-box;
    background-color: #fff;
    & > div {
      float: left;
      width: 50%;
      height: 45px;
      line-height: 45px;
    }
    .specific-care-top-right {
      text-align: right;
    }
  }

  .specific-care-content {
    height: 100%;
    overflow-y: auto;
    .main-record {
      margin-bottom: 5px;
    }
    .main-record,
    .maintain-record {
      overflow: hidden;
      height: 50%;
      background-color: #fff;
      padding: 5px 5px 10px 5px;
      box-sizing: border-box;
      .main-record-header,
      .maintain-record-header {
        height: 50px;
        padding: 4px 0;
        box-sizing: border-box;
        margin-bottom: 0;
        .add-button {
          float: right;
        }
        .record-title {
          display: inline-block;
          height: 50px;
          font-size: 20px;
          font-weight: 600;
        }
        .careMain-title {
          display: inline-block;
        }
      }
      .main-record-content,
      .maintain-record-content {
        height: calc(100% - 40px);
        & > div {
          height: 100%;
        }
      }
    }
  }
  .specific-caredrawer {
    background-color: #f3f3f3;
    .el-drawer__header span {
      outline: none;
    }
    .el-drawer__body {
      height: calc(100% - 35px);
      .drawer-content {
        height: 100%;
        &.edit {
          height: calc(100% - 40px);
        }
        .base-header .label {
          margin-left: 5px;
          display: inline-block;
        }
      }
    }
    .drawer-footer {
      bottom: 5px;
      background-color: #fff;
      .bring-checkbox {
        margin-right: 15px;
      }
    }
  }
  // el-drawer遮罩层颜色
  .v-modal {
    opacity: 0.3;
  }
}
</style>