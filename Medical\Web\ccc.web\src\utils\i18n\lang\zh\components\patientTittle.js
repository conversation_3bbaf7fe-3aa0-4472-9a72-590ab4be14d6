/*
 * FilePath     : \src\utils\i18n\lang\zh\components\patientTittle.js
 * Author       : 苏军志
 * Date         : 2021-11-11 08:43
 * LastEditors  : 苏军志
 * LastEditTime : 2022-12-16 17:52
 * Description  : 病人头组件语言包
 */
export default {
  patientTittle: {
    refreshDiagnostics: "刷新诊断",
    diagnosis: "诊断",
    bedNumberTip: "请输入床号！",
    patientName: "姓名",
    localCaseNumber: "住院号",
    attendingDoctor: "主治医师",
    gender: "性别",
    admissionTime: "入院时间",
    chiefComplaint: "主诉",
    principalNurse: "主责护士",
    age: "年龄",
    birthday: "出生日期",
    nursingLevel: "护理级别",
    allergicHistory: "过敏史",
    widths: {
      firstColumn: 66,
      thirdColumn: 65,
      fifthColumn: 65,
      seventhColumn: 65
    }
  }
};
