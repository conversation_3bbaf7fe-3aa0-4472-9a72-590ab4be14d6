/*
 * @Author: 李艳奇
 * @Date: 2020-09-29 15:59:29
 * LastEditTime : 2021-06-17 17:45
 * LastEditors  : 曹恩
 * @Description: In User Settings Edit
 * FilePath     : d:\ccc\web\ccc.web\src\api\UserCheck.js
 */
import http from "../utils/ajax";
const baseUrl = "/UserCheck";
import qs from "qs";

export const urls = {
  UserSaveCheck: baseUrl + "/UserSaveCheck",
  //派班检核
  PatientAttendanceCheck: baseUrl + "/PatientAttendanceCheck"
};

export const UserSaveCheck = params => {
  return http.post(urls.UserSaveCheck, qs.stringify(params));
};
//派班检核
export const PatientAttendanceCheck = params => {
  return http.get(urls.PatientAttendanceCheck, params);
};
