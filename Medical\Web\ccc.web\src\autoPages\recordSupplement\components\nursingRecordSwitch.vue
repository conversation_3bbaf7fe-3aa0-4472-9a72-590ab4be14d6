<!--
 * FilePath     : \src\autoPages\recordSupplement\components\nursingRecordSwitch.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-17 16:44
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-29 19:36
 * Description  : 
 * CodeIterationRecord: 
 -->
<template>
  <div class="nursing-record">
    <component :is="componentName" :patientInfo="patient" :supplementPatient="patient"></component>
  </div>
</template>

<script>
import { GetClinicSettingByTypeCode } from "@/api/Setting";
import nursingRecordOld from "@/pages/recordSupplement/nursingRecord/nursingRecordOld";
import nursingRecord from "@/autoPages/recordSupplement/components/nursingRecord";
export default {
  components: {
    nursingRecordOld,
    nursingRecord,
  },
  props: {
    patient: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    "patient.admissionDateTimeView": {
      handler() {
        this.setActiveComponent();
      },
    },
  },
  data() {
    return {
      componentName: "",
      switchDateTime: undefined,
    };
  },
  async created() {
    await this.getSwitchDateTimeSetting();
  },
  methods: {
    /**
     * @description: 获取配置
     * @return
     */
    async getSwitchDateTimeSetting() {
      let params = {
        settingTypeCode: "NewNursingRecord",
      };
      const res = await GetClinicSettingByTypeCode(params);
      if (!this._common.isSuccess(res)) {
        return;
      }
      this.switchDateTime = res.data.find((m) => m.typeValue == "NewNursingRecodLimitDateTime")?.settingValue;
      this.setActiveComponent();
    },
    /**
     * @description: 设置使用新/旧护理记录补录组件
     * @return
     */
    setActiveComponent() {
      if (!this.switchDateTime || !this.patient || !this.patient.admissionDateTimeView) {
        this.componentName = "";
        return;
      }
      let admissionDate = this._datetimeUtil.formatDate(this.patient.admissionDateTimeView, "yyyy-MM-dd hh:mm");
      let limitDate = this._datetimeUtil.formatDate(this.switchDateTime, "yyyy-MM-dd hh:mm");
      this.componentName = admissionDate <= limitDate ? "nursingRecordOld" : "nursingRecord";
    },
  },
};
</script>
<style lang="scss">
.nursing-record {
  height: 100%;
  .layout-top {
    padding: 0 10px;
    .data-select {
      width: 120px;
    }
    .tip {
      margin-left: 20px;
      color: #ff0000;
    }
    .top-btn {
      float: right;
    }
  }
}
</style>
