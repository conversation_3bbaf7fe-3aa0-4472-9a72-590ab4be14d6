<!--
 * FilePath     : /src/autoPages/patientDelivery/index.vue
 * Author       : 郭鹏超
 * Date         : 2021-04-22 17:06
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-06 10:52
 * Description  : 产程专项护理
 * CodeIterationRecord: 2022-08-18 2876-专项增加带入护理记录选框 -杨欣欣
                        2022-08-24 2855-作为IT人员，我需要依据提供的资料调整嘉会产科及新生儿科交接内容，以利于护理交班内容完整。 杨欣欣
                        2022-12-01 2869-跳转IO改版 杨欣欣
                        2023-02-15 3256-专项动态表头调整、补录调整、自适应调整 曹恩
-->
<template>
  <div class="patient-delivery-wrap">
    <specific-care
      v-model="showMaintainFlag"
      :showRecordArr="showRecordArr"
      :showRecordLabelArr="['产程主记录', '产程维护记录']"
      :handOverFlag="handOverArr"
      :nursingRecordFlag="nursingRecordArr"
      :drawerTitle="deliveryDrawerTitle"
      :informPhysicianFlag="informPhysicianArr"
      :previewFlag = previewFlag
      drawerHeight="700"
      :mainTableHeight="tableOneRowHeight"
      :editFlag="showEditButton"
      :drawerSize="refillFlag ? '80%' : ''"
      @mainAdd="mainAddOrFix"
      @maintainAdd="maintainAddOrFix"
      @save="deliverySave()"
      @cancel="deliveryCancel()"
      @getMainFlag="getMainFlag"
      @getMaintainFlag="getMaintainFlag"
      @getHandOverFlag="getHandOverFlag"
      @getNursingRecordFlag="getBringToNursingRecordFlag"
      @getInformPhysicianFlag="getInformPhysicianFlag"
      class="patient-delivery"
      v-loading="loading"
      element-loading-text="加载中……"
    >
      <!-- 主记录 -->
      <div slot="main-record">
        <packaging-table v-model="mainRecord" :headerList="mianTableHeader" @rowClick="currentTableList">
          <!-- 评估类型 插槽 -->
          <div slot="sourceFlag" slot-scope="scope">
            <el-tooltip :content="scope.row.sourceContent" placement="top">
              <div :class="['flag', scope.row.sourceFlag]">
                <span v-if="scope.row.sourceContent == 'A'">护</span>
                <span v-if="scope.row.sourceContent == 'H'">班</span>
              </div>
            </el-tooltip>
          </div>
          <!-- 操作 插槽-->
          <div slot="operate" slot-scope="scope">
            <el-tooltip v-if="!scope.row.thirdStageCompletedTime" content="结束">
              <div @click.stop="endDelivery(scope.row)" class="iconfont icon-stop"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="deleteMain(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </div>
        </packaging-table>
      </div>
      <!-- 维护记录 -->
      <div slot="maintain-record">
        <packaging-table ref="maintainTable" v-model="maintainRecord" :headerList="maintainTableHeader">
          <!-- 评估类型 插槽 -->
          <div slot="assessType" slot-scope="row">
            <span v-if="row.row.recordsCode.indexOf('Start') != -1">开始评估</span>
            <span v-else-if="row.row.recordsCode.indexOf('End') != -1">结束评估</span>
            <span v-else>例行评估</span>
          </div>
          <!-- 操作 插槽-->
          <div slot="operate" slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="maintainAddOrFix(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip v-if="scope.row.recordsCode != 'DeliveryStart'" content="删除">
              <div @click="deleteMaintain(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </div>
        </packaging-table>
      </div>
      <!-- 弹窗内容 -->
      <base-layout slot="drawer-content" header-height="auto">
        <div slot="header">
          <span class="label">执行日期:</span>
          <el-date-picker
            class="date-picker"
            v-model="performDate"
            type="date"
            :clearable="false"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          />
          <el-time-picker
            class="time-picker"
            v-model="performTime"
            :clearable="false"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
          />
          <station-selector v-model="currentStation" label="执行病区:" width="160" />
          <dept-selector label="" width="140" v-model="currentDepartment" :stationID="currentStation" />
        </div>
        <tabs-layout
          ref="tabsLayout"
          v-loading="tabsLayoutLoading"
          checkFlag
          :element-loading-text="tabsLayoutText"
          @button-click="buttonClick"
          :template-list="templateDatas"
          @change-values="changeValues"
          @checkTN="checkTN"
          @button-record-click="buttonRecordClick"
        />
      </base-layout>
      <!--弹出按钮链接框-->
      <div class="drawer-dialog" slot="drawer-dialog">
        <el-dialog
          v-dialogDrag
          :close-on-click-modal="false"
          :title="buttonName"
          :visible.sync="showButtonDialog"
          fullscreen
          custom-class="no-footer specific-care-view"
        >
          <iframe v-if="showButtonDialog" ref="buttonDialog" scrolling="no" frameborder="0" width="100%" height="99%" />
        </el-dialog>
        <el-dialog
          v-dialogDrag
          :close-on-click-modal="false"
          :title="buttonRecordTitle"
          :visible.sync="showButtonRecordDialog"
          custom-class="no-footer"
        >
          <risk-component :params="conponentParams" @result="result"></risk-component>
        </el-dialog>
      </div>
    </specific-care>
  </div>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import { GetButtonData, GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { mapGetters } from "vuex";
import {
  GetRecordTableView,
  GetCareMainTableView,
  DeleteRecord,
  DeleteCareRecord,
  SaveRecord,
  SaveCareMain,
  GetDeliveryAssessViewAsync,
} from "@/api/Delivery";
import { GetBringToShiftSetting } from "@/api/Setting.js";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import packagingTable from "@/components/table/index";
export default {
  computed: {
    ...mapGetters({
      patientInfo: "getPatientInfo",
      user: "getUser",
      token: "getToken",
    }),
  },
  components: {
    baseLayout,
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    riskComponent,
    packagingTable,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      patient: undefined,
      showMaintainFlag: false,
      deliveryDrawerTitle: "",
      showRecordArr: [true, false],
      recordsCodeInfo: {},
      //维护弹窗顶部数据
      performDate: undefined,
      performTime: undefined,
      currentStation: undefined,
      currentDepartment: undefined,
      //评估模板loading开关
      tabsLayoutLoading: false,
      tabsLayoutText: "加载中……",
      //维护弹窗模板数据
      templateDatas: [],
      //评估模板返回数据
      assessDatas: [],
      checkTNFlag: true,
      // 显示BR类弹窗
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR项目ID
      brAssessListID: undefined,
      //BR组件参数
      conponentParams: undefined,
      //主记录表格数据
      mainRecord: [],
      //维护记录表格数据
      maintainRecord: [],
      //选中主记录表格数据
      currentMainRecord: undefined,
      //主记录动态表头
      mianTableHeader: [],
      //维护记录动态表头
      maintainTableHeader: [],
      recordID: undefined,
      careMainID: "",
      //主记录表格表头加第一行的高度
      tableOneRowHeight: undefined,
      buttonAssessListID: "",
      buttonName: "",
      showButtonDialog: false,
      settingHandOver: false,
      handOverArr: [true, false],
      settingBringToNursingRecord: false,
      nursingRecordArr: [false, false],
      informPhysicianArr: [true, false],
      //是否能编辑删除该数据
      showEditButton: true,
      //补录标记
      refillFlag: undefined,
      //主记录loading
      loading: false,
      previewFlag: false,
    };
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = undefined;
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.refillFlag = "*";
          this.nursingRecordArr = [false, false];
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.showRecordArr = [true, false];
          this.getMainFlag(true);
        }
      },
      immediate: true,
    },
    showButtonDialog(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
  },
  mounted() {
    //跳转不允许切换病人  右键进入除外
    if (Object.keys(this.$route.query).length > 0 && !this.$route.query.shortCutFlag) {
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
    if (this.$route.query.patientScheduleMainID && this.$route.query.patientScheduleMainID != "null") {
      this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
    }
    this.getBringHandOverSetting();
    this.getBringToNursingRecordSetting();
    this.getRecordTableHeader();
    this.getShowBringToNursingRecordSetting();
  },
  methods: {
    /**
     * description: 获取主记录表头
     * return {*}
     * param {*}
     */
    async getRecordTableHeader() {
      let params = {
        fileClassID: 38,
        fileClassSub: "DeliveryRecord",
        useDescription: "1||Table",
      };
      this.mianTableHeader = [];
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.mianTableHeader = res.data;
        }
      });
    },
    /**
     * description: 获取维护记录表头
     * return {*}
     * param {*}
     */
    async getTableHeaderList() {
      let params = {
        fileClassID: 38,
        fileClassSub: "DeliveryCareMain",
        useDescription: "1||Table",
      };
      this.maintainTableHeader = [];
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.maintainTableHeader = res.data;
        }
      });
    },
    /**
     * description: 获取主记录表格数据
     * return {*}
     * param {*}
     */
    getMainRecord(recordID = undefined) {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        recordID,
      };
      this.loading = true;
      GetRecordTableView(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.mainRecord = res.data;
          this.currentMainRecord = recordID && this.mainRecord?.length ? this.mainRecord[0] : undefined;
        }
      });
    },
    /**
     * description: 获取维护表格数据
     * return {*}
     * param {*}
     */
    getMaintainRecord() {
      let params = {
        recordID: this.currentMainRecord.patientDeliveryRecordID,
      };
      this.loading = true;
      GetCareMainTableView(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.maintainRecord = res.data;
        }
      });
    },
    /**
     * description: 主记录新增或修改
     * return {*}
     * param {*}item 主记录
     */
    async mainAddOrFix(item) {
      this.showEditButton = true;
      if (item) {
        let userID = undefined;
        if (item.addUserName == this.user.userName) {
          userID = this.user.userID;
        }
        //判断是否可修改该数据
        this.showEditButton = await this._common.checkActionAuthorization(this.user, userID);
        if (this.showEditButton) {
          let ret = await this._common.getEditAuthority(
            item.patientDeliveryRecordID,
            "PatientDeliveryRecord",
            this.refillFlag ? true : false
          );
          if (ret) {
            this.showEditButton = false;
            this._showTip("warning", ret);
          } else {
            this.showEditButton = true;
          }
        }
      }
      //打开弹窗 修改弹窗标题
      this.openOrCloseDrawer(true, "新增产程记录");
      //修改评估表单号
      this.recordsCodeInfo.recordsCode = "DeliveryStart";
      //填充弹窗顶部数据
      this.performDate = item
        ? this._datetimeUtil.formatDate(item.addDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = item
        ? this._datetimeUtil.formatDate(item.addDate, "hh:mm:ss")
        : this._datetimeUtil.getNowTime("hh:mm:ss");
      this.currentStation = item ? item.occuredStationID : this.patient.stationID;
      this.currentDepartment = item ? item.occuredDepartmentID : this.patient.departmentListID;
      this.currentMainRecord = item ? item : undefined;
      this.recordID = item ? item.patientDeliveryRecordID : undefined;
      this.careMainID = item ? item.patientDeliveryCareMainID : "temp_" + this._common.guid();
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      this.getDeliveryAssessView();
    },
    /**
     * description: 维护主表新增或修改
     * return {*}
     * param {*} item 维护记录
     */
    async maintainAddOrFix(item) {
      this.showEditButton = true;
      if (this.refillFlag != "*" && item) {
        //判断是否可修改该数据
        let userID = undefined;
        if (item.addUserName == this.user.userName) {
          userID = this.user.userID;
        }
        this.showEditButton = await this._common.checkActionAuthorization(this.user, userID);
        if (this.showEditButton) {
          let ret = await this._common.getEditAuthority(
            item.patientDeliveryCareMainID,
            "PatientDeliveryCareMain",
            this.refillFlag ? true : false
          );
          if (ret) {
            this.showEditButton = false;
            this._showTip("warning", ret);
          } else {
            this.showEditButton = true;
          }
        }
      }
      if (this.refillFlag === "*" && item) {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(this.user.userID,false,true,item.addUserID);
        this.previewFlag = !saveButtonFlag;
      }
      //新增维护记录检核
      if (!item && !this.maintainAddCheck()) {
        return;
      }
      this.openOrCloseDrawer(true, "产程记录维护");
      this.recordsCodeInfo.recordsCode = item ? item.recordsCode : "DeliveryMaintain";
      //填充弹窗顶部数据
      this.performDate = item
        ? this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd")
        : this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = item
        ? this._datetimeUtil.formatDate(item.assessTime, "hh:mm:ss")
        : this._datetimeUtil.getNowTime("hh:mm:ss");
      this.currentStation = item ? item.stationID : this.patient.stationID;
      this.currentDepartment = item ? item.departmentListID : this.patient.departmentListID;
      this.careMainID = item ? item.patientDeliveryCareMainID : "temp_" + this._common.guid();
      this.recordID = item ? undefined : this.currentMainRecord.patientDeliveryRecordID;
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      this.getDeliveryAssessView();
    },
    /**
     * description: 结束产程
     * return {*}
     * param {*} item 维护记录
     */
    endDelivery(item) {
      this.showEditButton = true;
      this.openOrCloseDrawer(true, "产程结束");
      this.recordsCodeInfo.recordsCode = "DeliveryEnd";
      //填充弹窗顶部数据
      this.performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.performTime = this._datetimeUtil.getNowTime("hh:mm:ss");
      this.currentStation = this.patient.stationID;
      this.currentDepartment = this.patient.departmentListID;
      this.currentMainRecord = item;
      this.recordID = item.patientDeliveryRecordID;
      this.careMainID = "temp_" + this._common.guid();
      this.$set(this.handOverArr, 1, item ? item.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, item && item.informPhysician ? true : false);
      this.$set(this.nursingRecordArr, 1, item ? item.bringToNursingRecord : this.settingBringToNursingRecord);
      this.getDeliveryAssessView();
    },
    /**
     * description: 获取评估模板
     * return {*}
     * param {*}
     */
    async getDeliveryAssessView() {
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "加载中……";
      let params = {
        inpatientID: this.patient.inpatientID,
        departmentListID: this.patient.departmentListID,
        mappingType: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
      };
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.recordsCodeInfo = result.data;
        } else {
          this.recordsCodeInfo.recordsCode = undefined;
        }
      });
      if (!this.recordsCodeInfo || !this.recordsCodeInfo.recordsCode) {
        this.openOrCloseDrawer(false);
        this._showTip("warning", "找不到评估模板！");
        return;
      }
      params = {
        careMainID: this.careMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        inpatientID: this.patient.inpatientID,
        dateOfBirth: this.patient.dateOfBirth,
      };
      if (this.recordID) {
        params.recordID = this.recordID;
      }
      this.templateDatas = [];
      await GetDeliveryAssessViewAsync(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
      this.tabsLayoutLoading = false;
      this.tabsLayoutText = "";
    },
    /**
     * description: 组件保存事件
     * return {*}
     * param {*}
     */
    async deliverySave() {
      this.tabsLayoutLoading = true;
      this.tabsLayoutText = "保存中……";
      //主记录保存
      if (this.recordsCodeInfo.recordsCode == "DeliveryStart") {
        await this.deliveryRecordSave();
        return;
      }
      //维护记录和产程结束保存
      if (this.recordsCodeInfo.recordsCode == "DeliveryMaintain" || this.recordsCodeInfo.recordsCode == "DeliveryEnd") {
        await this.deliveryCareMainSave();
        return;
      }
      this.tabsLayoutLoading = false;
      this.tabsLayoutText = "";
    },
    /**
     * description: 主记录保存
     * return {*}
     * param {*}
     */
    async deliveryRecordSave() {
      //保存检核
      if (!this.saveCheck()) {
        return;
      }
      // 新增保存数据
      let saveData = {
        Main: {},
        Details: [],
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        InformPhysician: this.informPhysicianArr[1],
        RefillFlag: this.refillFlag,
        BringToShift: this.handOverArr[1],
        BringToNursingRecord: this.nursingRecordArr[1],
      };
      //如果来源于措施执行则填入
      if (this.patientScheduleMainID) {
        saveData.PatientScheduleMainID = this.patientScheduleMainID;
      }
      //组装保存Detail数据
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          bookMarkID: "DeliveryStart",
          assessValueJson: content.controlerType == "BD" && content.assessValue ? content.assessValue : undefined,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        saveData.Details.push(detail);
      });
      //组装保存main数据
      if (this.currentMainRecord) {
        saveData.Main.patientDeliveryRecordID = this.currentMainRecord.patientDeliveryRecordID;
      }
      saveData.Main.inpatientID = this.patient.inpatientID;
      saveData.Main.addDate = this.performDate + " " + this.performTime;
      saveData.Main.occuredStationID = this.currentStation;
      saveData.Main.occuredDepartmentID = this.currentDepartment;
      saveData.Main.PatientScheduleMainID = this.patientScheduleMainID;
      saveData.Main.RefillFlag = this.refillFlag;
      saveData.patientDeliveryCareMainID = this.careMainID;
      await SaveRecord(saveData).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
          this.openOrCloseDrawer(false);
          this.showRecordArr = [true, false];
          this.getMainFlag(true);
        }
      });
    },
    /**
     * description: 维护记录和产程结束保存
     * return {*}
     * param {*}
     */
    async deliveryCareMainSave() {
      //保存检核
      if (!this.saveCheck()) {
        this.tabsLayoutLoading = false;
        this.tabsLayoutText = "";
        return;
      }

      //排除结束评估保存
      if (!this.currentMainRecord) {
        return;
      }
      let saveData = {
        Main: {},
        Details: [],
        HandoverID: "",
      };
      //组装保存Detail数据
      this.assessDatas.forEach((content) => {
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
          bookMarkID: content.bookMarkID,
          assessValueJson: content.controlerType == "BD" && content.assessValue ? content.assessValue : undefined,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        saveData.Details.push(detail);
      });
      saveData.Main.patientDeliveryRecordID = this.currentMainRecord.patientDeliveryRecordID;
      saveData.Main.inpatientID = this.currentMainRecord.inpatientID;
      saveData.Main.recordsCode = this.recordsCodeInfo.recordsCode;
      saveData.Main.assessDate = this.performDate;
      saveData.Main.assessTime = this.performTime;
      saveData.Main.bringToShift = this.handOverArr[1];
      saveData.Main.bringToNursingRecord = this.nursingRecordArr[1];
      saveData.Main.informPhysician = this.informPhysicianArr[1];
      saveData.Main.StationID = this.currentStation;
      saveData.Main.DepartmentListID = this.currentDepartment;
      saveData.Main.PatientScheduleMainID = this.patientScheduleMainID;
      saveData.Main.RefillFlag = this.refillFlag;
      if (this.careMainID.indexOf("temp") == -1) {
        saveData.Main.patientDeliveryCareMainID = this.careMainID;
        saveData.isAdd = false;
      } else {
        saveData.Main.patientDeliveryCareMainID = this.careMainID.split("_")[1];
        saveData.Main.numberOfAssessment = this.maintainRecord.length + 1;
        saveData.isAdd = true;
      }
      await SaveCareMain(saveData).then(async (result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功");
          this.getMainRecord(this.currentMainRecord.patientDeliveryRecordID);
          await this.getMaintainList(this.currentMainRecord);
          this.openOrCloseDrawer(false);
        }
      });
    },
    /**
     * description: 底部弹窗取消事件
     * param {*}
     * return {*}
     */
    deliveryCancel() {
      this.openOrCloseDrawer(false, "");
      this.recordID = undefined;
      this.careMainID = undefined;
    },
    /**
     * description: 主记录删除
     * return {*}
     * param {*} item 主记录
     */
    async deleteMain(item) {
      //判断是否可修改或删除该数据
      this.showEditButton = true;
      if (item) {
        let userID = undefined;
        if (item.addUserName == this.user.userName) {
          userID = this.user.userID;
        }
        //判断是否可修改该数据
        this.showEditButton = await this._common.checkActionAuthorization(this.user, userID);
        if (this.showEditButton) {
          let ret = await this._common.getEditAuthority(
            item.patientDeliveryRecordID,
            "PatientDeliveryRecord",
            this.refillFlag ? true : false
          );
          if (ret) {
            this.showEditButton = false;
            this._showTip("warning", ret);
            return;
          } else {
            this.showEditButton = true;
          }
        }
        if (this.refillFlag === "*") {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,item.addUserID);
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            recordID: item.patientDeliveryRecordID,
          };
          DeleteRecord(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getMainRecord();
              this.showRecordArr = [true, false];
              this.maintainRecord = [];
            }
          });
        }
      });
    },
    /**
     * description: 维护记录删除
     * return {*}
     * param {*} item 维护记录
     */
    async deleteMaintain(item) {
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(
        item.patientDeliveryCareMainID,
        "PatientDeliveryCareMain",
        this.refillFlag ? true : false
      );
      if (this.refillFlag === "*" && item) {
        let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,item.addUserID);
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            careMainID: item.patientDeliveryCareMainID,
          };
          DeleteCareRecord(params).then(async (res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getMainRecord(this.currentMainRecord.patientDeliveryRecordID);
              await this.getMaintainList(this.currentMainRecord);
            }
          });
        }
      });
    },
    /**
     * description: 点击主记录切换页面形态
     * return {*}
     * param {*} item 主记录
     */
    async currentTableList(item) {
      //切换页面形态 主记录只显示选中记录
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        await this.getMaintainList(item);
      }
    },
    /**
     * description: 获取维护记录
     * return {*}
     * param {*} item
     */
    async getMaintainList(item) {
      this.mainRecord = [item];
      this.currentMainRecord = item;
      await this.getMaintainRecord();
      //获取维护动态列
      await this.getTableHeaderList();
      if (this.$refs.maintainTable) {
        this.$refs.maintainTable.doLayout();
      }
    },
    /**
     * description: 主记录勾选框事件
     * return {*}
     * param {*} flag 获取主记录标记
     */
    getMainFlag(flag) {
      if (flag) {
        this.getMainRecord();
        this.maintainRecord = [];
      } else {
        if (!this.currentMainRecord && this.mainRecord.length > 0) {
          this.currentMainRecord = this.mainRecord[0];
          this.getMaintainRecord();
        }
      }
    },
    /**
     * description: 维护记录勾选框事件
     * return {*}
     * param {*} flag 获取维护记录标记
     */
    getMaintainFlag(flag) {
      if (flag && this.currentMainRecord) {
        this.getMaintainRecord();
      } else {
        this.getMainRecord();
      }
    },
    /**
     * description: 评估模板返回数据
     * return {*}
     * param {*} datas 评估模板返回数据
     */
    changeValues(datas) {
      this.assessDatas = datas;
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 维护记录新增检核
     * return {*}
     * param {*}
     */
    maintainAddCheck() {
      if (!this.currentMainRecord) {
        this._showTip("warning", "未选择主记录");
        return false;
      }
      if (this.currentMainRecord.thirdStageCompletedTime) {
        this._showTip("warning", "该记录已结束");
        return false;
      }
      return true;
    },
    /**
     * description: 初始化BR组件
     * return {*}
     * param {*} content BR跳转风险项
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      this.conponentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: "Delivery" + "_" + content.assessListID,
        sourceID: this.getCareMainID(),
        assessTime:
          this._datetimeUtil.formatDate(this.performDate, "yyyy-MM-dd") +
          " " +
          this._datetimeUtil.formatDate(this.performTime, "hh:mm"),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    result(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag) {
        // 保存成功，回显数据
        this.updateButton(this.brAssessListID);
      }
    },

    /**
     * description: 获取维护记录CareMainID
     * return {*} 维护记录CareMainID
     * param {*}
     */
    getCareMainID() {
      let tempCareMainID = "";
      if (this.careMainID) {
        if (this.careMainID.indexOf("temp") != -1) {
          tempCareMainID = this.careMainID.split("_")[1];
        } else {
          tempCareMainID = this.careMainID;
        }
      }
      return tempCareMainID;
    },
    /**
     * description: 评估保存检核
     * return {*} true/false
     * param {*}
     */
    saveCheck() {
      if (this.currentStation == "") {
        this._showTip("warning", "请选择发生病区");
        return false;
      }
      if (this.currentDepartment == "") {
        this._showTip("warning", "请选择发生科室");
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        this.tabsLayoutLoading = false;
        return false;
      }
      if (!this.$refs.tabsLayout.checkRequire()) {
        this.tabsLayoutLoading = false;
        return false;
      }
      return true;
    },
    /**
     * description: 专项护理弹窗开关函数
     * return {*}
     * param {*} flag 弹框开关，title 弹框标题
     */
    openOrCloseDrawer(flag, title) {
      this.showMaintainFlag = flag;
      this.deliveryDrawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
    },
    /**
     * description: 获取表格第一行加表头高度
     * return {*}
     * param {*}
     */
    getMainTableOneRowhight() {
      this.$nextTick(() => {
        let oneDom = document.getElementsByClassName("patient-delivery")[0];
        let twoDom = oneDom.getElementsByClassName("maintain-record-row");
        let headerTwoDom = oneDom.getElementsByClassName("maintain-record-herderRow");
        let twoDomHeight = twoDom.length > 0 ? twoDom[0].offsetHeight : 0;
        let headerTwoDomHeight = headerTwoDom[0].offsetHeight;
        this.tableOneRowHeight = twoDomHeight + headerTwoDomHeight;
      });
    },
    /**
     * description: 评估组件按钮事件
     * return {*}
     * param {*} content 跳转明细项
     */
    buttonClick(content) {
      this.showButtonDialog = true;
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        `&sourceID=${this.getCareMainID()}` +
        "&sourceType=Delivery" +
        "_" +
        content.assessListID +
        "&isDialog=true";
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 添加完更新按钮数量
     * return {*}
     * param {*} assessListID 更新明细按钮的assessListID
     */
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    /**
     * description: 更新按钮数量API
     * return {*}
     * param {*} assessListID 更新明细按钮的assessListID
     */
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessListID: assessListID,
        sourceID: this.getCareMainID(),
        sourceType: "Delivery" + "_" + assessListID,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * description: 带入交班勾选框事件
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记
     * param {*} flag 回传的勾选状态
     * return {*}
     */
    getBringToNursingRecordFlag(flag) {
      this.nursingRecordArr[1] = flag;
    },
    //通知医师标记
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "Delivery",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (this.handOverArr[0]) {
            this.settingHandOver = res.data;
          }
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "DeliveryAutoInterventionToRecord",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingBringToNursingRecord = res.data;
        }
      });
    },
    /**
     * @description: 获取显示带入护理记录单按钮开关
     * @return
     */
    async getShowBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "ShowDeliveryBringToNursingRecord",
      };
      let showBringToNursingRecord = false;
      await GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          showBringToNursingRecord = res.data;
        }
      });
      if (showBringToNursingRecord) {
        this.$set(this.nursingRecordArr, 0, true);
      }
    },
  },
};
</script>

<style lang="scss">
.patient-delivery-wrap {
  height: 100%;
  .patient-wound {
    .drawer-content {
      .date-picker {
        width: 120px;
      }
      .time-picker {
        width: 80px;
      }
    }
    .station-selector {
      .label {
        margin-left: 0px;
      }
    }
    .specific-care-view {
      background-color: #f3f3f3;
      iframe {
        height: 99%;
        border: none;
      }
    }
  }
}
</style>
