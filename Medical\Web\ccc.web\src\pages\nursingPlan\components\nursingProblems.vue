<!--
 * FilePath     : \src\pages\nursingPlan\components\nursingProblems.vue
 * Author       : 李青原
 * Date         : 2020-05-07 11:15
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-16 08:33
 * Description  : 病人护理问题页面
 -->
<template>
  <base-layout class="nursing-problem" v-loading="loading" :element-loading-text="loadingText">
    <div class="button-line" slot="header">
      <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
      <el-button type="primary" class="edit-button" icon="iconfont icon-add" @click="addProblem">手工加入</el-button>
    </div>
    <div class="problem">
      <div class="current-problem">
        <el-table
          border
          stripe
          ref="total"
          :data="currentQues"
          height="100%"
          @row-click="pushProblemID"
          highlight-current-row
          row-key="sort"
        >
          <el-table-column width="80" align="center">
            <template slot-scope="template" slot="header">
              <span>顺序</span>
              <i class="iconfont icon-info" @click="showMessage(template)"></i>
            </template>
            <template slot-scope="template">
              <div>
                {{ template.$index + 1 }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="问题(P)" prop="problem" header-align="center"></el-table-column>
          <el-table-column label="开始日期" align="center" prop="startDate" width="140">
            <template slot-scope="problem">
              <el-date-picker
                v-model="problem.row.startDate"
                type="datetime"
                placeholder="选择日期"
                style="width: 125px"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                @change="tableDateTimeCheck(problem.row, 'S')"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="结束日期" prop="expectDate" width="140px" align="center">
            <template slot-scope="problem">
              <el-date-picker
                v-model="problem.row.expectDate"
                type="datetime"
                placeholder="选择日期"
                style="width: 125px"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                @change="tableDateTimeCheck(problem.row, 'E')"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="expectDate" header-align="center" width="120px" class-name="button-col">
            <template slot-scope="scope">
              <i class="iconfont icon-arrow-up" @click="getUp(scope.$index)"></i>
              <i class="iconfont icon-arrow-down" @click="getDown(scope.$index)"></i>
              <el-tooltip content="停止">
                <i class="iconfont icon-stop" @click="stopProblem(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="删除">
                <i class="iconfont icon-del" @click="deleteProblem(scope.row)"></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="history-problem">
        <el-table border stripe :data="historyQues" height="100%">
          <el-table-column label="历史护理问题" prop="problem"></el-table-column>
          <el-table-column label="实际结果" width="120px" align="center" prop="outCome"></el-table-column>
          <el-table-column label="起始日期" width="110px" header-align="center" prop="startDate"></el-table-column>
          <el-table-column label="结束日期" width="110px" header-align="center" prop="endDate"></el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 评价弹出框 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="evaluateVisible"
      v-if="evaluateVisible"
      @close="evaluateEnd"
    >
      <patient-evaluation
        ref="evaluate"
        :inpatientid="inpatientinfo ? inpatientinfo.inpatientID : ''"
        :stationid="inpatientinfo ? inpatientinfo.stationID : 0"
        :all-Flag="false"
        :problemids="[onPatientProblem.patientProblemID]"
        :warnFlag="true"
      ></patient-evaluation>
    </el-dialog>
    <!-- 手工加入弹出框 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      :visible.sync="addProblemVisible"
      v-loading="addLoading"
      v-if="addProblemVisible"
      class="add-problem-dialog"
      :element-loading-text="loadingText"
      title="手工加入护理问题"
    >
      <base-layout show-header class="add-dialog">
        <div slot="header" class="add-problem-title">
          <span>照护类别:</span>
          <el-select class="select" v-model="pattern" @change="patternChange" placeholder="请选择">
            <el-option
              v-for="item in patternList"
              :key="item.typeValue"
              :label="item.description"
              :value="item.typeValue"
            ></el-option>
          </el-select>
          <span>照护元素:</span>
          <el-select class="select" @change="careElementChange" v-model="careElement" placeholder="请选择">
            <el-option
              v-for="item in careElementList"
              :key="item.typeValue"
              :label="item.description"
              :value="item.typeValue"
            ></el-option>
          </el-select>
          <label>简拼:</label>
          <el-input
            style="width: 100px"
            v-model="selectContent"
            class="select-input"
            placeholder="请输入内容"
            @keyup.native.enter="queryProblem"
          ></el-input>
          <el-button class="query-button" icon="iconfont icon-search" type="primary" @click="queryProblem">
            查询
          </el-button>
        </div>
        <div class="add-problem" slot-scope="layout" :style="{ height: layout.height + 'px' }">
          <div class="problem">
            <el-table
              height="100%"
              @select="problemChecked"
              @row-click="problemChange"
              :data="problemData"
              ref="problemTable"
              :highlight-current-row="true"
              :header-cell-class-name="disabledSelection"
              border
              stripe
            >
              <el-table-column type="selection" :width="convertPX(40)" align="center"></el-table-column>
              <el-table-column label="问题(P)" prop="problem"></el-table-column>
            </el-table>
          </div>
          <div class="problem-related">
            <el-table
              height="100%"
              ref="related"
              :highlight-current-row="true"
              :data="relatedData"
              @select="relatedCheck"
              :header-cell-class-name="disabledSelection"
              :row-class-name="tableRowClassName"
              border
              stripe
            >
              <el-table-column
                type="selection"
                :width="convertPX(40)"
                align="center"
                :selectable="
                  () => {
                    return addDetail;
                  }
                "
              ></el-table-column>
              <el-table-column label="相关因素(E)" prop="name"></el-table-column>
            </el-table>
          </div>
          <div class="problem-features">
            <el-table
              border
              stripe
              height="100%"
              ref="features"
              :highlight-current-row="true"
              :data="featureData"
              @select="featuresCheck"
              :header-cell-class-name="disabledSelection"
              :row-class-name="tableRowClassName"
            >
              <el-table-column
                type="selection"
                :width="convertPX(40)"
                align="center"
                :selectable="
                  () => {
                    return addDetail;
                  }
                "
              ></el-table-column>
              <el-table-column label="定义特征（S）" prop="description"></el-table-column>
            </el-table>
          </div>
        </div>
      </base-layout>
      <div slot="footer">
        <label>开始日期:</label>
        <el-date-picker
          class="date-length"
          type="date"
          v-model="writingTime"
          placeholder="开始时间"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
        <el-time-picker
          class="time-length"
          v-model="beginTime"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        ></el-time-picker>
        <el-button align="right" @click="addProblemVisible = false">取消</el-button>
        <el-button align="right" type="primary" @click="commitAddProblem">确定</el-button>
      </div>
    </el-dialog>
  </base-layout>
</template>
<script>
import PatientEvaluation from "@/pages/nursingEvaluation/patientEvaluation";
import { GetLastMain } from "@/api/Assess";
import { GetPatientProblems, SaveSort, DelePatientProblem, GetSignByProblemID } from "@/api/PatientProblem";
import { GetPreAddProblem, GetRelatedFactor, GetNursingProblemByPinyin, SaveAddProblem } from "@/api/Problem";
import BaseLayout from "@/components/BaseLayout";
import { GetPatterns, GetPaternElements } from "@/api/Setting";
export default {
  props: {
    inpatientinfo: {
      required: true,
    },
    lastAssessDateTime: {
      type: String,
      default: undefined,
    },
    admissionDateTime: {
      type: String,
      default: undefined,
    },
  },
  components: {
    PatientEvaluation,
    BaseLayout,
  },
  watch: {
    "inpatientinfo.inpatientID": {
      handler(newValue) {
        this.currentQues = [];
        this.historyQues = [];
        if (!newValue) return;
        this.getData();
      },
      immediate: true,
    },
  },
  data() {
    return {
      //当前护理问题
      currentQues: [],
      // 历史护理问题
      historyQues: [],
      onPatientProblem: "",
      evaluateVisible: false,
      addProblemVisible: false,
      value: [],
      //本次评估的assessListID
      assessMainID: "",
      // 照护类型集合
      patternList: [],
      // 照护类型选项
      pattern: null,
      //照护元素
      careElementList: [],
      // 照护选项
      careElement: null,
      //问题数组
      problemData: [],
      //相关因素数组
      relatedData: [],
      // 定义特征数组
      featureData: [],

      selectContent: "",
      //已选中对象
      problemCheckedArray: [],

      onRelatedArray: [],

      onFeaturesArray: [],

      addDetail: false,
      loading: false,
      loadingText: "加载中……",

      addLoading: false,
      //开始时间
      beginDate: undefined,
      //填写时间
      writingTime: undefined,
      beginTime: undefined,
      dateNowTime: undefined,
      dataNowDate: undefined,
    };
  },
  async mounted() {
    //拖拽调用
    this.rowDrop();
  },
  methods: {
    /**
     * description: 拖拽排序提醒
     * param {*} messageContent
     * return {*}
     */
    showMessage() {
      this._showMessage({
        message: "鼠标选中问题拖拽进行排序",
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 2000,
      });
    },
    /**
     * description: 拖拽调用
     * param {*}
     * return {*}
     */
    rowDrop() {
      let bodyDom = this.$refs.total.$el.querySelector(".el-table__body-wrapper tbody");
      this._tableRowDrop(bodyDom, this, "currentQues");
    },
    async getData() {
      this.currentQues = [];
      this.historyQues = [];
      this.loading = true;
      this.loadingText = "加载中……";
      let param = {
        //TODO:后端API接参拼写错误尚未修改
        inPaientID: this.inpatientinfo.inpatientID,
      };
      await GetPatientProblems(param).then((response) => {
        this.loading = false;
        this.currentQues = [];
        this.historyQues = [];
        if (this._common.isSuccess(response)) {
          if (response.data.length > 0) {
            for (let index = 0; index < response.data.length; index++) {
              const element = response.data[index];
              element.startDate = element.startDate;
              element.startTime = element.startTime.substring(0, 5);
              //当前护理问题
              if (!element.endDate && !element.endTime) {
                element.sort = this.currentQues.length + 1;
                element.expectDate = element.expectDate;
                this.currentQues.push(element);
              } else {
                element.startDate = this._datetimeUtil.formatDate(element.startDate, "yyyy-MM-dd hh:mm");
                element.startTime = this._datetimeUtil.formatDate(element.startTime, "hh:mm");
                element.endDate = this._datetimeUtil.formatDate(element.endDate, "yyyy-MM-dd");
                element.endTime = this._datetimeUtil.formatDate(element.endTime, "hh:mm");
                this.historyQues.push(element);
              }
            }
          }
        }
      });
    },
    save() {
      if (this.currentQues.length <= 0) {
        return;
      }
      for (let index = 0; index < this.currentQues.length; index++) {
        this.currentQues[index].sort = index + 1;
      }
      this.loadingText = "保存中……";
      this.loading = true;
      return SaveSort(this.currentQues).then((response) => {
        if (this._common.isSuccess(response)) {
          this._showTip("success", "保存成功！");
          this.loading = false;
          this.$emit("jump-page");
        }
      });
    },
    stopProblem(row) {
      this.onPatientProblem = row;
      this.evaluateVisible = true;
    },
    async addProblem() {
      this.writingTime = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.beginTime = this._datetimeUtil.getNowTime("hh:mm");
      // 点击手工加入置空简拼搜索输入框
      this.selectContent = "";
      if (await this.checkAddProblem()) {
        this.addProblemVisible = true;
        this.addLoading = true;
        //发出请求
        await this.getAddProblemData();
      }
    },
    async getAddProblemData() {
      await GetPatterns().then((response) => {
        this.patternList = [];
        if (this._common.isSuccess(response)) {
          if (response.data.length > 0) {
            this.patternList = response.data;
            // //放入回显显示第一个元素
            this.pattern = this.patternList[0].typeValue;
            this.patternChange(this.pattern);
          } else {
            this.addLoading = false;
          }
        } else {
          this.addLoading = false;
        }
      });
    },
    //处理照护类别更新
    async patternChange(typeValue) {
      let param = { pattern: typeValue.trim() };
      await GetPaternElements(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.length > 0) {
            this.careElementList = response.data;
            //放入回显显示第一个元素
            this.careElement = this.careElementList[0].typeValue;
            this.careElementChange(this.careElementList[0].typeValue);
          } else {
            this.addLoading = false;
          }
        } else {
          this.addLoading = false;
        }
      });
    },
    careElementChange(typeValue) {
      let param = {
        element: typeValue,
        pattern: this.pattern,
        assessMainID: this.assessMainID,
      };
      GetPreAddProblem(param).then((response) => {
        if (this._common.isSuccess(response)) {
          //选中项置空 数据数组 置空
          this.problemData = [];
          this.problemCheckedArray = [];
          this.relatedData = [];
          this.onRelatedArray = [];
          this.featureData = [];
          this.onFeaturesArray = [];
          if (response.data.length > 0) {
            this.problemData = response.data;
            this.$nextTick(() => {
              this.$refs.problemTable.setCurrentRow(this.problemData[0]);
              this.problemChange(this.problemData[0]);
            });
          } else {
            this.addLoading = false;
          }
        } else {
          this.addLoading = false;
        }
      });
    },
    async problemChange(row) {
      if (row == undefined) {
        this.relatedData = [];
        this.featureData = [];
        return;
      }
      this.onPatientProblem = row;
      let param = {
        problemID: row.id,
        specialListType: this.inpatientinfo.specialListType,
        age: this.inpatientinfo.age,
      };
      //判断焦点行是否能被否选
      this.addDetail = false;
      this.problemCheckedArray.forEach((item) => {
        if (item == this.onPatientProblem) {
          this.addDetail = true;
        }
      });
      await GetRelatedFactor(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.relatedData = response.data;
          //同步选中选项
          this.$nextTick(function () {
            let checkedRelev = this.onPatientProblem.relevantFactors;
            if (checkedRelev) {
              for (let index = 0; index < checkedRelev.length; index++) {
                const element = checkedRelev[index];
                this.relatedData.forEach((item, index) => {
                  if (item.id == element.id) {
                    this.$refs.related.toggleRowSelection(item, true);
                  }
                });
              }
            }
          });
        } else {
          this.addLoading = false;
        }
      });
      param.inpatientID = this.inpatientinfo.inpatientID;
      await GetSignByProblemID(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.featureData = response.data;
          //同步选中选项
          this.$nextTick(function () {
            let checkedAssess = this.onPatientProblem.assessLists;
            if (checkedAssess) {
              for (let index = 0; index < checkedAssess.length; index++) {
                const element = checkedAssess[index];
                this.featureData.forEach((item, index) => {
                  if (item.id == element.id) {
                    this.$refs.features.toggleRowSelection(item, true);
                  }
                });
              }
            }
          });
        } else {
          this.addLoading = false;
        }
      });
      this.addLoading = false;
    },
    async checkAddProblem() {
      this.dateNowTime = this._datetimeUtil.getNowTime("hh:mm");
      this.dataNowDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      let flag = false;
      let param = {
        inpatientID: this.inpatientinfo.inpatientID,
        index: Math.random(),
      };
      await GetLastMain(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (!response.data) {
            this._showTip("warning", "尚未护理评估,不得手动加入护理问题！");
            return;
          }
          if (response.data && response.data.tempSaveMark === "T") {
            this._showTip("warning", "有暂存护理评估,不得手动加入护理问题！");
          } else {
            //最近一次评估 当评估状态不为暂存 都可以添加护理问题
            this.assessMainID = response.data.id;
            flag = true;
            this.beginDate = response.data.assessDate;
          }
        }
      });
      return flag;
    },
    async problemChecked(selection, row) {
      await this.problemChange(row);
      this.onPatientProblem = row;
      this.$refs.problemTable.setCurrentRow(row);
      //判断是勾选还是取消
      let flag = false;
      selection.forEach((item) => {
        if (item === row) flag = true;
      });
      if (!flag) {
        //清除选项
        this.addDetail = false;
        row.relevantFactors = [];
        row.assessLists = [];
        this.$refs.related.clearSelection();
        this.$refs.features.clearSelection();
      } else {
        this.addDetail = true;
      }
      this.problemCheckedArray = selection;
    },
    relatedCheck(selection, row) {
      this.$set(this.onPatientProblem, "relevantFactors", selection);
    },
    featuresCheck(selection, row) {
      this.$set(this.onPatientProblem, "assessLists", selection);
    },
    pushProblemID(row, column, event) {
      this.onPatientProblem = row;
    },
    deleteProblem(row) {
      this._deleteConfirm("确定此护理问题吗？", (flag) => {
        if (flag) {
          // 确认删除
          let param = { patientProblemID: row.patientProblemID };
          DelePatientProblem(param).then((response) => {
            if (this._common.isSuccess(response)) {
              this._showTip("success", "删除成功！");
              this.getData();
            }
          });
        }
      });
    },
    evaluateEnd() {
      this.getData();
    },
    queryProblem() {
      if (!this.selectContent.length) {
        this._showTip("warning", "请填写拼音后查询！");
        return;
      }
      let param = {
        pinyin: this.selectContent,
        inpatientID: this.inpatientinfo.inpatientID,
      };
      GetNursingProblemByPinyin(param).then((response) => {
        this.problemData = [];
        if (this._common.isSuccess(response)) {
          response.data.map((item) => {
            Object.assign(item, { problem: item.name, id: item.indexID });
          });
          this.problemData = response.data;
          this.$refs.problemTable.setCurrentRow(this.problemData[0]);
          this.problemChange(this.problemData[0]);
        }
      });
    },
    commitAddProblem() {
      //保存时间检核
      let dateTime = this.writingTime + " " + this.beginTime;
      if (!this.dateTimeCheck(dateTime, "S")) {
        return;
      }
      //保存数据检核
      if (this.problemCheckedArray.length <= 0) {
        this._showTip("warning", "请勾选要加入的护理问题");
        return;
      }
      let commitArray = this.problemCheckedArray;
      let params = [];
      let idList = [];
      commitArray.forEach((item) => {
        let param = new Object();
        this.problemData.forEach((problem, index) => {
          problem.sort = index + 1;
        });
        if (item.assessLists && item.assessLists.length > 0) {
          item.assessLists.forEach((element) => {
            idList.push(element.id);
            param.assessLists = this._common.clone(idList);
          });
        } else {
          param.assessLists = [];
        }
        idList = [];
        if (item.relevantFactors) {
          item.relevantFactors.forEach((element) => {
            idList.push(element.id);
          });
          param.relevantFactors = this._common.clone(idList);
        } else {
          param.relevantFactors = [];
        }
        param.inpatientID = this.inpatientinfo.inpatientID;
        param.stationID = this.inpatientinfo.stationID;
        param.sort = item.sort;
        param.assessMainID = this.assessMainID;
        param.diagnoseFlag = "H";
        param.patient = {};
        param.problemID = item.id;
        param.startDate = this.writingTime;
        param.startTime = this.beginTime;
        userID: this.userID;
        params.push(param);
      });
      return SaveAddProblem(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.addProblemVisible = false;
          this._showTip("success", "手工加入成功！");

          this.getData();
        }
      });
    },

    disabledSelection(row) {
      if (row.columnIndex === 0) {
        return "DisableSelection";
      }
    },
    //向上
    getUp(index) {
      if (index == 0) return;
      //当前元素
      let item = this.currentQues[index];
      let upItem = this.currentQues[index - 1];
      item.sort--;
      upItem.sort++;
      this.$set(this.currentQues, index - 1, item);
      this.$set(this.currentQues, index, upItem);
    },
    //向下
    getDown(index) {
      if (index == this.currentQues.length - 1) return;
      let item = this.currentQues[index];
      let downItem = this.currentQues[index + 1];
      item.sort++;
      downItem.sort--;
      this.$set(this.currentQues, index + 1, item);
      this.$set(this.currentQues, index, downItem);
    },
    tableRowClassName: function (row, index) {
      if (row.row.show) {
        return "";
      }
      return "hide-show";
    },

    //表格开始与结束时间检核
    async tableDateTimeCheck(row, type) {
      //开始与结束日期检核
      if (row.expectDate < row.startDate) {
        this._showTip("warning", "开始日期不得晚于结束日期！");
        await this.getData();
        return;
      }
      let dateTime = this._datetimeUtil.formatDate(type == "S" ? row.startDate : row.expectDate, "yyyy-MM-dd hh:mm");
      if (!this.dateTimeCheck(dateTime, type)) {
        await this.getData();
        return;
      }
    },
    //时间检核 公共方法
    dateTimeCheck(dateTime, type) {
      let assessDateTime = this.lastAssessDateTime ? this.lastAssessDateTime : this.admissionDateTime;
      if (type == "S") {
        if (dateTime < assessDateTime) {
          this._showTip("warning", "开始时间不能早于护理评估时间！");
          return false;
        }
        if (dateTime > this._datetimeUtil.getNow("yyyy-MM-dd hh:mm")) {
          this._showTip("warning", "开始时间不得晚于当前时间！");
          return false;
        }
      }
      if (type == "E") {
        if (dateTime < assessDateTime) {
          this._showTip("warning", "结束时间不得早于护理评估时间！");
          return false;
        }
        if (dateTime < this._datetimeUtil.getNow("yyyy-MM-dd hh:mm")) {
          this._showTip("warning", "结束时间不得早于当前时间！");
          return false;
        }
      }
      return true;
    },
  },
};
</script>

<style lang="scss">
.nursing-problem {
  display: flex;
  height: 100%;
  .button-line {
    float: right;
  }
  .problem {
    width: 100%;
    height: 100%;
    display: flex;
    .current-problem {
      height: 100%;
      overflow: auto;
      width: 68%;
      box-sizing: border-box;
      padding: 0 3px;
      .button-col .iconfont {
        font-weight: normal;
      }
    }
    .history-problem {
      height: 100%;
      overflow: auto;
      width: 32%;
      box-sizing: border-box;
      padding: 0 3px;
    }
  }
  .add-problem-dialog {
    height: 100%;
    .el-dialog {
      min-width: 720px;
    }
    .add-problem-title {
      .select {
        width: 143px;
      }
      // .pinyin-input {
      //   display: inline-block;
      //   vertical-align: middle;
      //   // padding-bottom: 1px;
      // }
      // .query {
      //   float: right;
      // }
      .select-input {
        display: inline-block;
        width: 120px;
      }
    }
    .add-problem {
      height: 100%;
      display: flex;
      .problem,
      .problem-related,
      .problem-features {
        height: 100%;
        width: 33.3%;
        .el-table .DisableSelection .cell .el-checkbox__inner {
          display: none;
          position: relative;
        }
        .el-table .DisableSelection .cell:before {
          content: "";
          position: absolute;
        }
      }
      .hide-show {
        display: none;
      }
    }
  }
  .date-length {
    width: 115px !important;
  }
  .time-length {
    width: 80px !important;
  }
}
</style>
