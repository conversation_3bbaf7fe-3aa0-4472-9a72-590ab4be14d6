<!--
 * FilePath     : \src\pages\dictionaryMaintain\hospitalBuilding\index.vue
 * Author       : 来江禹
 * Date         : 2022-08-13 16:02
 * LastEditors  : 来江禹
 * LastEditTime : 2022-09-12 14:30
 * Description  : 楼栋维护主页面，管理对应病区的床位、楼栋、楼层、楼层位置、房间数据
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="hospital-building-index">
    <div class="hospital-building-index-header" slot="header">
      <station-selector v-model="stationID" width="160"></station-selector>
      <span class="btn-list">
        <el-button type="success" icon="iconfont icon-add" @click="add">新增</el-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
        <el-button class="print-button" @click="goPage(buildingRouter)">楼栋维护</el-button>
        <el-button class="edit-button" @click="goPage(floorRouter)">楼层维护</el-button>
        <el-button type="danger" @click="goPage(locationRouter)">楼层位置维护</el-button>
        <el-button class="room-btn" @click="goPage(roomRouter)">房间维护</el-button>
      </span>
      <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    </div>
    <div class="bed-table">
      <el-table :data="stationBuildTabList" border stripe>
        <el-table-column type="index" width="50" label="序号"></el-table-column>
        <el-table-column label="楼栋" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.buildName">{{ scope.row.buildName }}</span>
            <el-select
              v-else
              class="table-build-select"
              v-model="scope.row.buildListID"
              placeholder="请选择楼栋"
              @change="getBuildId(scope.row)"
            >
              <el-option
                v-for="(item, index) in buildList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="楼层" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.floorName">
              {{ scope.row.floorName }}
            </span>
            <el-select
              v-else
              class="table-floor-select"
              v-model="scope.row.floorListID"
              placeholder="请选择楼层"
              @change="getFloorID(scope.row)"
            >
              <el-option
                v-for="(item, index) in scope.row.floorList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="楼层位置" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.locationName">{{ scope.row.locationName }}</span>
            <el-select
              v-else
              class="table-location-select"
              v-model="scope.row.locationListID"
              placeholder="请选择楼层位置"
              @change="getLocationID(scope.row)"
            >
              <el-option
                v-for="(item, index) in scope.row.locationList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="房间" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.roomName">{{ scope.row.roomName }}</span>
            <el-select v-else class="table-room-select" v-model="scope.row.roomListID" placeholder="请选择房间">
              <el-option
                v-for="(item, index) in scope.row.roomList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="床位" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.bedNumber">{{ scope.row.bedNumber }}</span>
            <el-select
              v-else
              class="table-bed-select"
              v-model="scope.row.bedNumberListID"
              placeholder="请选择床位"
              @change="changeBednumber(scope.row.bedNumberListID)"
            >
              <el-option
                v-for="(item, index) in bedNumberList"
                :key="index"
                :label="item.BedNumber"
                :value="item.BedNumber"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" width="80px" align="left">
          <template slot-scope="scope">
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="deleteStationBuild(scope.row.bedNumber, scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
import progressView from "@/components/progressView";
import stationSelector from "@/components/selector/stationSelector";
import {
  GetFloorDatas,
  GetLoactionDatas,
  GetRoomDatas,
  GetSettingDescriptionOne,
  GetBedNumber,
  GetStationBuild,
  SaveStationBuildDatas,
  DeleteBedListDatas,
} from "@/api/WardMaintenance";
export default {
  components: {
    baseLayout,
    stationSelector,
    progressView,
  },
  data() {
    return {
      stationID: undefined,
      stationBuildTabList: [],
      floorSelectList: [],
      locationSelectList: [],
      roomSelectList: [],
      buildIndex: "",
      floorIndex: "",
      locationIndex: "",
      buildList: [],
      floorList: [],
      locationList: [],
      bedNumberList: [],
      roomList: [],
      code: undefined,
      successCode: "",
      buildingRouter: "/building",
      floorRouter: "/floor",
      locationRouter: "/location",
      roomRouter: "/room",
      //进度条开关
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  watch: {
    stationID: {
      async handler() {
        await this.getData();
        await this.getBedNumberSelect();
      },
    },
  },
  created() {
    this.refresh();
    this.stationID = this.user.stationID;
  },
  methods: {
    /**
     * description: 进度条关闭函数
     * return {*}
     */
    progressClose() {
      this.progressFlag = false;
      this.renewMessageData();
    },
    /**
     * description: 重置进度条
     * return {*}
     */
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    /**
     * description: 根据病区下拉框拉取当签页面已经维护好的数据
     * return {*}
     */
    async getData() {
      let params = {
        stationID: this.stationID,
      };
      await GetStationBuild(params).then((result) => {
        this.stationBuildTabList = [];
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            //取到已经维护在BedList表中的数据，写入表格数据中
            list.forEach((tableData) => {
              let params = {
                buildName: tableData.wardBuilding,
                floorName: tableData.wardFloor,
                locationName: tableData.bedLocation,
                roomName: tableData.roomCode,
                bedNumber: tableData.bedNumber,
              };
              this.stationBuildTabList.push(params);
            });
          }
        }
      });
    },
    /**
     * description: 刷新页面数据
     * return {*}
     */
    async refresh() {
      //把当前页面数据置空
      this.buildList = [];
      this.floorSelectList = [];
      this.locationSelectList = [];
      this.roomSelectList = [];
      //刷新时重新获取数据
      this.getBuildSelect();
      this.getSettingfloor("WardBuilding");
      this.getSettingLocation("WardBuilding");
      this.getSettingRoom("WardBuilding");
    },
    /**
     * description: 新增按钮实现新增表格行
     * return {*}
     */
    add() {
      let row = {
        buildListID: "",
        floorListID: "",
        locationListID: "",
        roomListID: "",
        bedNumber: "",
      };
      this.stationBuildTabList.push(row);
    },
    /**
     * description: 调转页面
     * param {*} path
     * return {*}
     */
    goPage(path) {
      this.$router.push({ path: path });
    },
    /**
     * description:获取对应病区床位下拉框数据
     * return {*}
     */
    async getBedNumberSelect() {
      let params = {
        StationID: this.stationID,
      };
      await GetBedNumber(params).then((result) => {
        //清空数组避免重复
        this.bedNumberList = [];
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            //查询到床位信息，写入床位列下拉框数组中
            list.forEach((bed) => {
              let params = {
                BedNumber: bed.bedNumber,
                StationID: bed.stationID,
              };
              this.bedNumberList.push(params);
            });
          }
          //如果该病区没有床位信息，弹窗提示
          if (this.bedNumberList.length == 0 && this.stationBuildTabList.length == 0) {
            this._showTip("error", "该病区没有床位信息，请选择其他病区进行维护");
          }
        }
      });
    },
    /**
     * description: 当新增下拉框选中床位时动态的更新床位下拉框数据，避免可以选择相同床位
     * param {*} index
     * return {*}
     */
    changeBednumber(index) {
      let list = this.bedNumberList;
      this.bedNumberList = [];
      list.forEach((changeBed) => {
        if (changeBed.BedNumber != index) {
          let params = {
            BedNumber: changeBed.BedNumber,
            StationID: changeBed.StationID,
          };
          this.bedNumberList.push(params);
        }
      });
    },
    /**
     * description: 获取楼栋列下拉框数据
     * return {*}
     */
    async getBuildSelect() {
      let params = {
        SettingTypeCode: "WardBuilding",
      };
      await GetSettingDescriptionOne(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            list.forEach((build) => {
              let params = {
                Description: build.description,
                TypeValue: build.typeValue,
              };
              this.buildList.push(params);
            });
          }
        }
      });
    },
    /**
     * description: 获取已经维护好的楼层数据
     * param {*} SettingTypeCode
     * return {*}
     */
    async getSettingfloor(SettingTypeCode) {
      let params = {
        SettingTypeCode: SettingTypeCode,
      };
      await GetFloorDatas(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            list.forEach((floor) => {
              let params = {
                floorName: floor.floorName,
                floorID: floor.floorID,
                floorCode: floor.floorCode,
              };
              this.floorSelectList.push(params);
            });
          }
        }
      });
    },
    /**
     * description: 获取已经维护好的楼层位置下拉框数据
     * param {*} SettingTypeCode
     * return {*}
     */
    async getSettingLocation(SettingTypeCode) {
      let params = {
        SettingTypeCode: SettingTypeCode,
      };
      await GetLoactionDatas(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            for (let index = 0; index < list.length; index++) {
              const selectList = list[index];
              let params = {
                locationName: selectList.loactionName,
                locationID: selectList.locationID,
                locationCode: selectList.locationCode,
              };
              this.locationSelectList.push(params);
            }
          }
        }
      });
    },
    /**
     * description: 获取已经维护好的房间数据
     * param {*} SettingTypeCode
     * return {*}
     */
    async getSettingRoom(SettingTypeCode) {
      let params = {
        SettingTypeCode: SettingTypeCode,
      };
      await GetRoomDatas(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let list = result.data;
          if (list != null) {
            for (let index = 0; index < list.length; index++) {
              const room = list[index];
              let params = {
                roomID: room.roomID,
                roomName: room.roomName,
                roomCode: room.roomCode,
              };
              this.roomSelectList.push(params);
            }
          }
        }
      });
    },
    /**
     * description: 获取下拉框异动数据，便于保存时写入settingTypeCode字段,并且动态获取楼层列表
     * param {*} index
     * return {*}
     */
    getBuildId(row) {
      if (!row.buildListID) {
        return;
      }
      this.buildIndex = row.buildListID;
      this.$set(row, "floorListID", "");
      this.$set(row, "locationListID", "");
      this.$set(row, "roomListID", "");
      this.getFloorSelect(row);
    },
    /**
     * description: 根据楼栋TypeValue动态的更新楼层下拉框数据
     * return {*}
     */
    getFloorSelect(row) {
      let floorList = [];
      this.floorList = [];
      this.floorSelectList.forEach((floorIndex) => {
        if (floorIndex.floorCode == "WardFloor" + "_" + this.buildIndex) {
          let params = {
            Description: floorIndex.floorName,
            TypeValue: floorIndex.floorID,
          };
          floorList.push(params);
        }
        this.$set(row, "floorList", floorList);
      });
    },
    /**
     * description: 获取楼栋下拉框选取的Typevalue,以便于选择对应的楼层
     * param {*} row
     * return {*}
     */
    getFloorID(row) {
      if (!row.floorListID) {
        return;
      }
      this.floorIndex = row.floorListID;
      this.$set(row, "locationListID", "");
      this.$set(row, "roomListID", "");
      this.getLocationSelect(row);
    },
    /**
     * description: 获取楼栋和楼层对应的楼层位置数据
     * param {*} row
     * return {*}
     */
    getLocationSelect(row) {
      let list = [];
      this.locationList = [];
      this.locationSelectList.forEach((locationIndex) => {
        if (locationIndex.locationCode == "WardPartition" + "_" + this.buildIndex + "_" + this.floorIndex) {
          let params = {
            Description: locationIndex.locationName,
            TypeValue: locationIndex.locationID,
          };
          list.push(params);
        }
        this.$set(row, "locationList", list);
      });
    },
    /**
     * description: 获取选中的楼层位置TypeValue，以便于动态选择房间列表
     * param {*} row
     * return {*}
     */
    getLocationID(row) {
      this.locationIndex = row.locationListID;
      this.$set(row, "roomListID", "");
      this.getRoomSelect(row);
    },
    /**
     * description: 根据roomCode动态的拉取数据
     * param {*} row
     * return {*}
     */
    getRoomSelect(row) {
      let list = [];
      this.roomList = [];
      this.roomSelectList.forEach((roomIndex) => {
        if (
          roomIndex.roomCode ==
          "WardRoom" + "_" + this.buildIndex + "_" + this.floorIndex + "_" + this.locationIndex
        ) {
          let params = {
            Description: roomIndex.roomName,
            TypeValue: roomIndex.roomID,
          };
          list.push(params);
        }
        this.$set(row, "roomList", list);
      });
    },
    /**
     * description: 获取当前页面数据，方便保存
     * return {*}
     */
    gettabDatas() {
      let modifyDatas = this.stationBuildTabList;
      if (modifyDatas.length == 0) {
        return undefined;
      }
      return modifyDatas;
    },
    /**
     * description: 保存页面新增数据，写入后端
     * return {*}
     */
    async save() {
      let datas = this.gettabDatas();
      let successMessage = "";
      let failMessage = "";
      for (let index = 0; index < datas.length; index++) {
        const bedData = datas[index];
        this.progressFlag = true;
        //判断是否有下拉框没有选择，当全部选择，执行保存
        if (
          bedData.buildListID &&
          bedData.floorListID &&
          bedData.locationListID &&
          bedData.roomListID &&
          bedData.bedNumberListID
        ) {
          let params = {
            BuildID: bedData.buildListID,
            FloorID: bedData.floorListID,
            LocationID: bedData.locationListID,
            RoomID: bedData.roomListID,
            BedNumber: bedData.bedNumberListID,
            StationID: this.stationID,
          };
          let messageItem = bedData.bedNumberListID + "床";
          await SaveStationBuildDatas(params).then((res) => {
            if (res.code == 1) {
              successMessage = index == 0 ? messageItem : successMessage + "," + messageItem;
            } else {
              failMessage = failMessage + " " + messageItem;
            }
          });
        }
        //配置进度条内容
        let progress = (((index + 1) / datas.length) * 100).toFixed(0);
        //配置进度条内容
        this.messageData[0].value = Number(progress);
        this.messageData[1].value = successMessage;
        this.messageData[2].value = failMessage;
        this.messageData[3].value = "";
      }
      await this.getData();
      await this.refresh();
      await this.getBedNumberSelect();
    },
    /**
     * description: 点击删除按钮删除数据
     * param {*} number
     * return {*}
     */
    deleteStationBuild(number, row) {
      if (number == "") {
        let index = this.stationBuildTabList.findIndex((bed) => bed == row);
        if (index >= 0) {
          this.stationBuildTabList.splice(index, 1);
        }
      } else {
        this._deleteConfirm("确定删除数据么？", (flag) => {
          if (flag) {
            let params = {
              StationID: this.stationID,
              BedNumber: number,
            };
            DeleteBedListDatas(params).then((res) => {
              if (this._common.isSuccess(res)) {
                this._showTip("success", "删除成功");
                this.refresh();
                this.getData();
                this.getBedNumberSelect();
              }
            });
          }
        });
      }
    },
  },
};
</script>
<style lang="scss">
.hospital-building-index {
  .hospital-building-index-header {
    .table-name-select {
      width: 150px;
      margin-right: 30px;
    }
    .btn-list {
      display: block;
      float: right;
      .room-btn {
        color: white;
        background-color: #9b25b3;
      }
    }
  }
  .batch-monitoring-content {
    position: relative;
  }
  .bed-table {
    .table-build-select,
    .table-floor-select,
    .table-location-select,
    .table-room-select {
      min-width: 96%;
    }
  }
}
</style>
