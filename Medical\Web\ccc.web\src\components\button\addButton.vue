<!--
 * FilePath     : \src\components\button\addButton.vue
 * Author       : 苏军志
 * Date         : 2023-01-03 15:24
 * LastEditors  : 张现忠
 * LastEditTime : 2023-02-21 09:25
 * Description  : 新增按钮
 * CodeIterationRecord: 2023-02-21:修复新增按钮点击后触发两次的问题 -zxz
-->
<template>
  <el-button v-bind="$attrs" v-on="$listeners" class="add-button" icon="iconfont icon-add" @click="handClick">
    <slot>新增</slot>
  </el-button>
</template>

<script>
export default {
  name: "addButton",
  methods: {
    handClick: function (...params) {
      this._common.session("actionButtonClass", "add-button");
    },
  },
};
</script>

<style>
</style>