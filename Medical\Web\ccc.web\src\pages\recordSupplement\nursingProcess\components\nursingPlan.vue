<!--
 * FilePath     : \src\pages\recordSupplement\nursingProcess\components\nursingPlan.vue
 * Author       : 郭鹏超
 * Date         : 2021-08-14 16:42
 * LastEditors  : 马超
 * LastEditTime : 2025-06-15 15:54
 * Description  : 护理计划补录
-->
<template>
  <div class="nursingPlan-supplement" v-loading="loading" :element-loading-text="loadingText">
    <el-table height="100%" :data="tabelData" class="histroy-problem" stripe border>
      <el-table-column label="评估时间" width="140px" prop="problem">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.addDate"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            type="datetime"
            style="width: 125px"
            placeholder="选择日期"
          ></el-date-picker>
        </template>
      </el-table-column>
      <el-table-column label="护理问题" width-min="400px" prop="problem"></el-table-column>
      <el-table-column label="定义特征" min-width="100" align="left">
        <template slot-scope="scope">
          <div v-html="scope.row.signAndSymptom"></div>
        </template>
      </el-table-column>
      <el-table-column label="相关因素" min-width="100" align="left">
        <template slot-scope="scope">
          <div v-html="scope.row.relatedFactor"></div>
        </template>
      </el-table-column>
      <el-table-column label="执行人" width="80px" align="center" prop="addEmployeeID"></el-table-column>
      <el-table-column label="实际结果" width="110px" align="center" prop="outCome"></el-table-column>
      <el-table-column label="评价时间" width="140px" prop="problem">
        <template v-if="scope.row.outCome" slot-scope="scope">
          <el-date-picker
            v-model="scope.row.evaluationDateTime"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:mm"
            type="datetime"
            style="width: 125px"
            placeholder="选择日期"
          ></el-date-picker>
        </template>
      </el-table-column>
      <el-table-column label="评价人" width="80px" align="center" prop="modifyPersonID">
        <template v-if="scope.row.outCome" slot-scope="scope">
          <div>
            {{ scope.row.modifyPersonID }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" header-align="center" width="65">
        <template slot-scope="scope">
          <el-tooltip content="修改">
            <i class="iconfont icon-edit" @click="problemSave(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-del" @click="deleteProblem(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { GetSupplementProblemList, UpdateProblemDate, DeleteProblem } from "@/api/NursingPlanSupplement";
import { mapGetters } from "vuex";
export default {
  props: {
    patient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  watch: {
    "patient.inpatientID": {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        this.nowRecordListID = "";
        this.GetTableData();
      },
      immediate: true,
    },
  },
  data() {
    return {
      tabelData: [],
      loading: false,
      loadingText: "",
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  methods: {
    GetTableData() {
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      this.loadingOpenOrClose(true, "加载中……");
      GetSupplementProblemList(params).then((res) => {
        this.loadingOpenOrClose(false);
        if (this._common.isSuccess(res)) {
          if (res.data && res.data.length > 0) {
            res.data.forEach((item) => {
              if (item.evaluationDate && item.evaluationTime) {
                this.$set(
                  item,
                  "evaluationDateTime",
                  this._datetimeUtil.formatDate(item.evaluationDate, "yyyy-MM-dd") +
                    " " +
                    this._datetimeUtil.formatDate(item.evaluationTime, "hh:mm")
                );
              }
            });
          }
          this.tabelData = res.data;
        }
      });
    },
    async problemSave(row) {
      let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,row.addEmployeeID)
      if (!saveButtonFlag) {
        this._showTip("warning", "非本人不可修改");
        return;
      }
      let params = {
        problemID: row.patientProblemID,
        addDate: row.addDate,
      };
      if (row.outCome) {
        params.evaluationDateTime = row.evaluationDateTime;
      }
      this.loadingOpenOrClose(true, "保存中……");
      UpdateProblemDate(params).then((res) => {
        this.loadingOpenOrClose(false);
        if (this._common.isSuccess(res)) {
          this._showTip("success", "修改成功！");
          this.GetTableData();
        }
      });
    },
    async deleteProblem(row) {
      let {disabledFlag,saveButtonFlag} = await this._common.userSelectorDisabled(this.user.userID,false,true,row.addEmployeeID)
      if (!saveButtonFlag) {
        this._showTip("warning", "非本人不可删除");
        return;
      }
      this._deleteConfirm("确定删除此护理问题吗?", (flag) => {
        if (flag) {
          let params = {
            problemID: row.patientProblemID,
          };
          this.loadingOpenOrClose(true, "删除中……");
          DeleteProblem(params).then((response) => {
            this.loadingOpenOrClose(false);
            if (this._common.isSuccess(response)) {
              this._showTip("success", "删除成功！");
              this.GetTableData();
            }
          });
        }
      });
    },
    loadingOpenOrClose(flag, text = "") {
      this.loading = flag;
      this.loadingText = text;
    },
  },
};
</script>

<style lang="scss" >
.nursingPlan-supplement {
  height: 100%;
  .el-date-editor {
    .el-input__prefix {
      display: none;
    }
    .el-input__inner {
      padding-left: 5px;
    }
  }
}
</style>