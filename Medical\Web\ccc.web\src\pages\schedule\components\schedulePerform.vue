<!--
 * FilePath     : \src\pages\schedule\components\schedulePerform.vue
 * Author       : 曹恩
 * Date         : 2021-11-14 08:28
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-15 19:42
 * Description  : 排程执行
 * CodeIterationRecord: 2022-05-22 2642-作为IT人员，我需要排程执行时，排程时间默认排程计划时间 En
-->
<template>
  <div class="schedule-perform" v-if="tempParams">
    <schedule-score
      ref="performComponents"
      :hideOperate="hideOperate"
      :notDialog="notDialog"
      v-if="tempParams.interventionType == 'S'"
      :params="tempParams"
      @close="noticeCloseDialog"
    ></schedule-score>
    <!-- 宣教类走D类，在对话框中根据类型，显示宣教资料 -->
    <schedule-monitor
      ref="performComponents"
      :hideOperate="hideOperate"
      :notDialog="notDialog"
      v-else-if="tempParams.interventionType == 'D' || tempParams.interventionType == 'T'"
      :params="tempParams"
      @close="noticeCloseDialog"
    ></schedule-monitor>
    <schedule-assess
      ref="performComponents"
      :hideOperate="hideOperate"
      :notDialog="notDialog"
      v-else-if="tempParams.interventionType == 'A'"
      :params="tempParams"
      @close="noticeCloseDialog"
    ></schedule-assess>
  </div>
</template>
<script>
import scheduleAssess from "./scheduleTypes/scheduleAssess";
import scheduleScore from "./scheduleTypes/scheduleScore";
import scheduleMonitor from "./scheduleTypes/scheduleMonitor";
import { GetOneSettingByTypeAndCode } from "@/api/Setting";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  components: {
    scheduleAssess,
    scheduleScore,
    scheduleMonitor,
  },
  props: {
    params: {
      type: Object,
      required: true,
    },
    hideOperate: {
      type: Boolean,
      default: false,
    },
    notDialog: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tempParams: undefined,
      scheduleTimeFlag: false,
    };
  },
  watch: {
    params: {
      immediate: true,
      deep: true,
      async handler(newValue) {
        if (newValue) {
          await this.init();
        }
      },
    },
  },
  mounted() {
    this.getScheduleTimeFlag();
  },
  methods: {
    noticeCloseDialog(flag, triggerParams) {
      this.$emit("result", flag, triggerParams);
    },
    /**
     * description: 初始化排程参数
     * return {*}
     */
    async init() {
      // 克隆一个对象，防止污染源对象
      let temp = this._common.clone(this.params);
      let param = { settingType: 168, settingCode: "PainScoreThreshold", index: Math.random() };
      await GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          temp.painScoreThreshold = Number(response.data.typeValue);
        }
      });
      if (!temp.performDate || !temp.performTime) {
        temp.performDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        temp.performTime = this._datetimeUtil.getNowTime("hh:mm");
      } else {
        temp.performDate = this._datetimeUtil.formatDate(temp.performDate, "yyyy-MM-dd");
        temp.performTime = this._datetimeUtil.formatDate(temp.performTime, "hh:mm");
      }
      if (temp.bringToNursingRecord && temp.bringToNursingRecord.trim().length > 0) {
        temp.bringToNursingRecord = temp.bringToNursingRecord;
      } else {
        // 默认带入护理记录
        temp.bringToNursingRecord = "1";
      }
      if (temp.bringToShift && temp.bringToShift.trim().length > 0) {
        temp.bringToShift = temp.bringToShift;
      } else {
        // 默认不带入交班
        temp.bringToShift = "0";
      }
      if (temp.performComment && temp.performComment.trim().length > 0) {
        temp.performComment = temp.performComment;
      }
      this.tempParams = temp;
    },
    getScheduleTimeFlag() {
      GetSettingSwitchByTypeCode({ SettingTypeCode: "UseScheduleDateTime" }).then((res) => {
        if (this._common.isSuccess(res)) {
          this.scheduleTimeFlag = res.data;
        }
      });
    },
    /**
     * description: 获取组件保存数据，供父组件调用
     * param {*}
     * return {*}
     */
    getSaveData() {
      let saveData = undefined;
      let performComponents = this.$refs.performComponents;
      if (performComponents && performComponents.getSaveData) {
        saveData = performComponents.getSaveData();
      }
      return saveData;
    },
  },
};
</script>
<style lang="scss">
.schedule-perform {
  height: 100%;
  width: 100%;
  background-color: #ffffff;
}
</style>
