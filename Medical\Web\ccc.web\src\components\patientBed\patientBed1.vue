<!--
 * FilePath     : \src\components\patientBed\patientBed1.vue
 * Author       : 李青原
 * Date         : 2020-05-10 16:19
 * LastEditors  : 苏军志
 * LastEditTime : 2022-10-26 17:28
 * Description  :  病人卡片组件1(通用)
 -->

<template>
  <div :class="['patient-bed1', { 'filter-mask': patientInfo.filterMaskFlag }]" :style="bedCardStyle">
    <div class="bed-header" :style="getFocus()">
      <tip-marker
        v-if="jobTipList && jobTipList.length > 0"
        class="job-tip"
        :whiteColor="focusOn"
        :width="100"
        :tipList="jobTipList"
        @jumpPage="tipJumpPage"
      ></tip-marker>
      <div class="gender" v-if="!emptyBed">
        {{ patientInfo.gender ? patientInfo.gender : "" }}
      </div>
      <div class="bed-number" :style="getNursingLevelColor()" :title="patientInfo.bedNumber">
        {{ patientInfo.bedNumber }}
      </div>
    </div>
    <div class="head-info" v-if="!emptyBed">
      <div v-if="isPrintable" class="name bed-label" :title="patientInfo.patientName" @click="bedsideCard">
        {{ patientInfo.patientName }}
      </div>
      <div v-else class="name" :title="patientInfo.patientName">{{ patientInfo.patientName }}</div>
      <div class="age" :title="patientInfo.age">{{ patientInfo.age }}</div>
    </div>
    <div class="list-info" v-if="whole && !emptyBed">
      <ul class="info-detail">
        <li>
          <div class="diagnosis" :title="patientInfo.diagnose">
            {{ patientInfo.diagnose }}
          </div>
        </li>
        <li>
          <span>住院号：</span>
          <span>{{ patientInfo.localCaseNumber }}</span>
        </li>
        <li>
          <span>入院日期：</span>
          <span v-formatTime="{ value: patientInfo.admissionDate, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
          <div class="days" :title="'入院' + patientInfo.days + '天'">
            {{ patientInfo.days }}
          </div>
        </li>
        <li>
          <span>出生日期：</span>
          <span v-formatTime="{ value: patientInfo.dateOfBirth, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
        </li>
        <li>
          <div class="physicianName" :title="patientInfo.physicianName">
            <span>医:</span>
            {{ patientInfo.physicianName }}
          </div>
          <div class="careNurse" :title="patientInfo.careNurse">
            <span>护:</span>
            {{ patientInfo.careNurse }}
          </div>
        </li>
      </ul>
    </div>

    <div class="mark-list" v-if="whole">
      <div class="mark-out" v-for="(item, index) in patientInfo.inPatientMarkStyleList" :key="index">
        <div
          v-if="item.display"
          :style="{ backgroundColor: item.color }"
          :title="item.remarkDetail ? item.remarkDetail : item.remark"
          class="mark"
        >
          {{ item.iconText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import tipMarker from "@/components/patientBed/tipMarker";

export default {
  components: { tipMarker },
  props: {
    patientInfo: {
      type: Object,
      required: true,
    },
    focusOn: { type: Boolean, default: false },
    whole: { type: Boolean, default: true },
    isPrintable: {
      type: Boolean,
      default: true,
    },
    jobTipList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      nursingColor: "#ffffff",
      emptyBed: true,
    };
  },
  computed: {
    bedCardStyle() {
      const WHOLE_HEIGHT = "181px";
      const UNWHOLE_HEIGHT = "80px";
      let style = new Object();
      style.height = this.whole ? WHOLE_HEIGHT : UNWHOLE_HEIGHT;
      if (this.focusOn) {
        let color = "#ffffff";
        if (
          !this.patientInfo.inPatientNursingLevelStyle ||
          !this.patientInfo.nursingLevel ||
          this.nursingColor == "#ffffff"
        ) {
          color = "#000000";
        }
        style.color = color;
        style.backgroundColor = this.nursingColor;
        if (
          !this.patientInfo.inPatientNursingLevelStyle ||
          !this.patientInfo.nursingLevel ||
          this.nursingColor == "#ffffff"
        ) {
          style.backgroundColor = "#dedede";
        }
      } else {
        style.color = "#000000";
      }
      return style;
    },
  },
  mounted() {
    this.emptyBed = this.patientInfo.chartNo == "" && this.patientInfo.caseNumber == "";
  },
  methods: {
    //床头卡事件
    bedsideCard() {
      this.$emit("bedside-card", this.patientInfo);
    },
    getNursingLevelColor() {
      if (!this.patientInfo) return;
      let nursingLevel = this.patientInfo.inPatientNursingLevelStyle;
      if (nursingLevel && nursingLevel.backGroundColor) {
        this.nursingColor = nursingLevel.backGroundColor;
        return { backgroundColor: nursingLevel.backGroundColor };
      } else {
        return { backgroundColor: "#ffffff" };
      }
    },
    getFocus() {
      let bgcolor = this.nursingColor;
      let color = "#ffffff";
      if (
        !this.patientInfo.inPatientNursingLevelStyle ||
        !this.patientInfo.nursingLevel ||
        this.nursingColor == "#ffffff"
      ) {
        color = "#000000";
        bgcolor = "#dedede";
      }
      return this.focusOn ? { backgroundColor: bgcolor } : { color: color };
    },
    tipJumpPage(router) {
      if (!router) {
        return;
      }
      this.$emit("tipJumpPage", router);
    },
  },
};
</script>

<style lang="scss">
.patient-bed1 {
  position: relative;
  display: inline-block;
  width: 210px;
  padding: 0;
  padding-bottom: 3px;
  vertical-align: top;
  background-color: #ffffff;
  &.filter-mask,
  &.filter-mask * {
    color: #ffffff !important;
    background-color: #e3e3e3 !important;
  }
  .base-header {
    margin-bottom: 0;
    padding: 0;
  }
  .bed-header {
    position: relative;
    .job-tip .tip-marker {
      top: -5px;
      left: 5px;
    }
    .gender {
      position: absolute;
      right: 5px;
      top: 3px;
      width: 25px;
      height: 25px;
      line-height: 25px;
      border-radius: 25px;
      font-size: 15px;
      text-align: center;
      color: #000000;
      background-color: #ffffff;
    }
    .bed-number {
      height: 32px;
      margin: 0 45px;
      text-align: center;
      font-size: 24px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .head-info {
    height: 35px;
    line-height: 35px;
    .name {
      float: left;
      padding-left: 8px;
      max-width: 130px;
      font-size: 21px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .bed-label:hover {
      text-decoration: underline;
      cursor: pointer;
    }
    .age {
      float: right;
      width: 60px;
      padding-right: 5px;
      font-size: 21px;
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .list-info {
    width: 100%;
    .info-detail {
      line-height: 18px;
      margin: 0;
      padding-left: 10px;
      list-style: none;
      font-size: 15px;
      .diagnosis {
        height: 17.6px;
        width: 170px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .days {
        float: right;
        height: 18px;
        min-width: 15px;
        margin-right: 5px;
        padding: 0 2px;
        border-radius: 3px;
        color: #000000;
        background-color: #eee;
      }
      .physicianName {
        float: left;
        height: 17.6px;
        width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .careNurse {
        float: right;
        height: 17.6px;
        width: 100px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .mark-list {
    position: absolute;
    width: 100%;
    height: 24px;
    line-height: 20px;
    bottom: 0;
    background-color: #ffffff;
    border-top: 1px solid #f3f3f3;
    cursor: default;
    text-align: right;
    .mark-out {
      display: inline;
      .mark {
        display: inline-block;
        width: 16px;
        height: 16px;
        line-height: 15px;
        margin-right: 6px;
        font-size: 12px;
        cursor: default;
        color: #ffffff;
        text-align: center;
      }
    }
  }
}
</style>
