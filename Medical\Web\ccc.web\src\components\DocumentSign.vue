<!--
 * FilePath     : \src\components\DocumentSign.vue
 * Author       : 张现忠
 * Date         : 2023-05-01 11:40
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-11 19:17
 * Description  : 文档签名组件
 * CodeIterationRecord:
-->
<template>
  <div class="document-sign" v-loading="signLoading" :element-loading-text="signLoadingText">
    <div class="sign-btn-wrapper">
      <el-tooltip v-if="isPC && showSignIcon && hasPdfFlag" content="签名">
        <!-- 因为自适应原因,此处采用行内样式：不使用行内样式,切换不同分辨率的时候,按钮的位置会发生改变 -->
        <button
          class="sign-btn iconfont icon-signature"
          style="top: 10px; right: 140px; width: 40px; height: 40px"
          @click="startCASignBefore"
        ></button>
      </el-tooltip>
      <!-- 无线签名 -->
      <el-tooltip v-if="isPC && showAnySignIcon && hasPdfFlag" content="发起签名">
        <button
          class="sign-btn iconfont icon-initiate-a-signature"
          style="top: 10px; right: 200px; width: 40px; height: 40px"
          @click="applyPDFSign"
        ></button>
      </el-tooltip>
      <el-tooltip v-if="isPC && showAnySignIcon && hasPdfFlag" content="获取签名">
        <button
          class="sign-btn iconfont icon-get-signature"
          style="top: 10px; right: 170px; width: 40px; height: 40px"
          @click="getPDFSign"
        ></button>
      </el-tooltip>
    </div>
    <iframe v-if="isPC" :src="localFileUrl" frameborder="0" :type="iframeType" scrolling="auto"></iframe>
    <pdf v-else v-for="i in numPages" :key="i" :src="localFileUrl" :page="i"></pdf>
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="补充文档内容"
      :visible.sync="showDocumentContentDialog"
      append-to-body
      custom-class="document-content-dialog"
    >
      <el-form :model="documentContentData" label-position="top" label-suffix="：">
        <el-form-item
          v-for="(documentContent, index) in showDocumentContentList"
          :key="index"
          :label="documentContent.description"
        >
          <el-input v-model="documentContentData[documentContent.settingValue]"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="saveDocumentContent">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pdf from "vue-pdf";
import { GetSettingSwitchsByTypeCodeAndValue, GetClinicSettingByTypeCode } from "@/api/Setting";
import { GetSignDocument, GetPatientDocumentByID, UploadSignedDocument } from "@/api/Document";
import NotificationSign from "@/utils/bjca/NotificationSign";
import { GetJoinPartDatasByInpatientID } from "@/api/Inpatient";
import { ApplyPDFSign, GetPDFSign } from "@/api/CertificateAuthority";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";

// 签名申请及回调API
export default {
  components: {
    pdf,
  },
  props: {
    /**
     * description: 嵌入式框架iframe的type
     * return {*} 默认类型 x-google-chrome-pdf
     */
    iframeType: {
      type: String,
      required: false,
      default: () => {
        return "application/x-google-chrome-pdf";
      },
      validator: function (val) {
        return val.startsWith("application/", 0);
      },
    },
    /**
     * description: 签名以及获取pdf的一些必要参数
     * 属性中不能存在Object对象
     * 属性：emrDocumentID,inpatientID,scorePoint(风险传值)
     * return {*}
     */
    signParams: {
      type: Object,
      required: true,
      default: undefined,
    },
    //默认告知书
    fileClass: {
      type: Number,
      required: false,
      default: 99,
    },
  },
  data() {
    return {
      //用来判断客户端类型：[true：pc ,false：mobile]
      isPC: true,
      //文件地址
      localFileUrl: undefined,
      //源文件地址 | 没有签名的文件
      originFileUrl: undefined,
      //文档签名页面的遮罩
      signLoading: false,
      //文件签名页面的遮罩上呈现的文本
      signLoadingText: "加载中……",
      //判断是否显示签名按钮图标
      showSignIcon: false,
      //记录是否存在可查看的pdf
      hasPdfFlag: false,
      //pdf 源文件base64格式
      base64Data: undefined,
      //关键字签名规则
      kwRules: undefined,
      //是否显示发起签名和获取签名按钮
      showAnySignIcon: false,
      // 签名前是否补充文档字段开关
      supplementDocumentContentFlag: false,
      // 文档对应的补充字段
      supplementDocumentContentList: [],
      // 显示文档补充字段弹窗
      showDocumentContentDialog: false,
      // 显示文档补充字段列表
      showDocumentContentList: [],
      // 文档补充字段数据
      documentContentData: {},
    };
  },
  watch: {
    signParams: {
      async handler(newVal) {
        await this.getSignDocument(this.signParams, true);
      },
      deep: true,
    },
    localFileUrl(n, o) {
      n && (this.hasPdfFlag = true) && !this.isPC && this.getNumPages(n);
    },
  },
  /**
   * description:主要用来做一些初始化判断
   * return {*}
   */
  async created() {
    //判断客户端类型,用来正常呈现文档
    this.isPC = !!(this._common && this._common.isPC());
    // 获取签名相关配置
    await this.getSettings();
  },
  async mounted() {
    // 挂载时,尝试获取签过名的文档
    await this.getSignDocument(this.signParams, true);
  },
  methods: {
    async saveDocumentContent() {
      this.showDocumentContentDialog = false;
      await this.getSignDocument(this.signParams, false, this.documentContentData);
      await this.startCASign();
    },
    /**
     * @description: 开始CA签名前判断
     */
    async startCASignBefore() {
      if (this.supplementDocumentContentFlag) {
        this.showDocumentContentList = this.supplementDocumentContentList.filter(
          (item) => item.typeValue == this.signParams.emrDocumentID
        );
        // 有需要补充的字段才弹窗
        if (this.showDocumentContentList.length) {
          this.documentContentData = {};
          this.showDocumentContentDialog = true;
          return;
        }
      }
      await this.startCASign();
    },
    /**
     * description: 开始CA签名
     * return {*}
     */
    async startCASign() {
      if (!this.signParams) {
        this._showTip("warning", "缺少签名信息,签名失败！");
        return;
      }
      let signer;
      this.signLoading = true;
      this.signLoadingText = "签名中……";
      if (!this.originFileUrl) {
        await this.getSignDocument(this.signParams, false);
      }
      let param = { inpatientID: this.signParams.inpatientID };
      await GetJoinPartDatasByInpatientID(param).then((res) => {
        if (this._common.isSuccess(res)) {
          signer = { UName: res.data.patientName, IDType: "1", IDNumber: res.data.identityID };
          return;
        }
        this.signLoading = false;
      });
      //加载pdf base64字符串
      await this.loadAndConvertPDF(this.originFileUrl);
      if (!this.base64Data) {
        this._showTip("warning", `加载和转换pdf失败${this.originFileUrl}`);
        this.signLoading = false;
        return;
      }
      let kwRuleSettings = this.kwRules?.filter((k) => k.typeValue == this.signParams.emrDocumentID);
      if (!kwRuleSettings) {
        this.signLoading = false;
        this._showTip("warning", "没有定位配置，请联系工程师添加定位配置");
        return;
      }
      let kw = kwRuleSettings.find((setting) => setting.description == "KW")?.settingValue;
      if (!kw) {
        this._showTip("warning", "没有定位配置，请联系工程师添加定位配置");
        return;
      }
      let xOffset = kwRuleSettings.find((setting) => setting.description == "XOffset")?.settingValue ?? 60;
      let yOffset = kwRuleSettings.find((setting) => setting.description == "YOffset")?.settingValue ?? 2;
      let kwRule = {
        //关键字
        kw: kw,
        kwIndex: 1,
        //左上角|x轴方法偏移量
        xOffset: xOffset,
        //左上角|y轴方向偏移量
        yOffset: yOffset,
        //签字显示的位置宽度|相对于左上角
        width: 100,
        //签名显示的位置高度|相对于左上角
        height: 50,
      };
      //签名
      let signedBase64Pdf = NotificationSign(signer, this.base64Data, { kwRule: kwRule });
      if (!signedBase64Pdf) {
        this.signLoading = false;
        return;
      }
      //上传签名文件
      let signParams = {
        inpatientID: this.signParams.inpatientID,
        eMRDocumentID: this.signParams.emrDocumentID,
        fileClass: this.fileClass,
        pdfBase64Content: signedBase64Pdf,
      };
      let success = await UploadSignedDocument(signParams);
      this.signLoading = false;
      this.signLoadingText = "加载中……";
      if (success) {
        await this.getSignDocument(this.signParams, true);
        return;
      }
      this._showTip("warning", "签名文件上传失败");
    },
    /**
     * description: 发起签名
     * return {*}
     */
    async applyPDFSign() {
      let param = {
        inpatientID: this.signParams.inpatientID,
        fileClassID: this.fileClass,
        emrDocumentID: this.signParams.emrDocumentID,
        scorePoint: this.signParams.scorePoint,
      };
      await ApplyPDFSign(param).then((res) => {
        if (this._common.isSuccess(res) && res.data) {
          this._showTip("success", "发起签名成功请在签字板签名");
        } else {
          this._showTip("warning", "发起签名失败");
        }
      });
    },
    /**
     * description: 获取签名
     * return {*}
     */
    async getPDFSign() {
      let param = {
        inpatientID: this.signParams.inpatientID,
        fileClassID: this.fileClass,
        emrDocumentID: this.signParams.emrDocumentID,
      };
      await GetPDFSign(param).then((res) => {
        if (this._common.isSuccess(res)) {
          this.localFileUrl = res.data;
        } else {
          this._showTip("warning", "获取签名失败");
        }
      });
    },
    /**
     * description: 下载文件
     * return {*}
     * param {*} url
     */
    async loadAndConvertPDF(url) {
      //防止旧数据被使用
      this.base64Data = undefined;
      let blob = await fetch(url).then(async (response) => response.blob());
      //从对象Blob中获取文件base64二进制形式
      let base64Str = await this.readFileAsBase64String(blob);
      if (!base64Str) {
        return;
      }
      this.base64Data = base64Str.replace(/^data.(.*?);base64,/, "");
    },
    /**
     * description:读取文件base64字符串
     * return {*}
     * param {*} originFile 从文件中读取
     */
    async readFileAsBase64String(blob) {
      //同步等待读取文件
      let base64Data;
      try {
        base64Data = await new Promise((resolve) => {
          let fileReader = new FileReader();
          fileReader.onload = (e) => resolve(fileReader.result);
          fileReader.readAsDataURL(blob);
        });
      } catch (error) {
        throw new Error("无法将文件解析成base64字符串 :" + error);
      }
      return base64Data;
    },
    /**
     * description: 异动端加载文档时,获取文档页数
     * return {*}
     * param {*} url
     */
    getNumPages(url) {
      this.signLoading = true;
      let loadingTask = pdf.createLoadingTask(url);
      loadingTask.promise
        .then((pdf) => {
          this.numPages = pdf._pdfInfo.numPages;
          this.signLoading = false;
        })
        .catch((err) => {
          this._showTip("warning", "pdf加载失败！");
        });
    },
    /**
     * description: 获取签名相关配置
     * return {*}
     */
    async getSettings() {
      let params = {
        settingTypeCode: "ShowCASignButton",
        typeValue: this.fileClass,
      };
      await GetSettingSwitchsByTypeCodeAndValue(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.showSignIcon = res?.data || false;
        }
      });
      params = {
        settingTypeCode: "ShowAnySignIcon",
        typeValue: this.fileClass,
      };
      await GetSettingSwitchsByTypeCodeAndValue(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.showAnySignIcon = res?.data || false;
        }
      });
      params = {
        settingTypeCode: `CertificateAuthorityKeyWordRule`,
      };
      await GetClinicSettingByTypeCode(params).then((resp) => {
        if (this._common.isSuccess(resp) && resp.data != null) {
          this.kwRules = resp.data;
        }
      });
      params = {
        settingTypeCode: "SupplementDocumentContentSwitch",
      };
      await GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.supplementDocumentContentFlag = res?.data || false;
        }
      });
      params = {
        settingTypeCode: "SupplementDocumentContent",
      };
      await GetClinicSettingByTypeCode(params).then((resp) => {
        if (this._common.isSuccess(resp) && resp.data != null) {
          this.supplementDocumentContentList = resp.data;
        }
      });
    },
    /**
     * description: 获取pdf
     * return {*}
     * param {*} params
     * param {*} signedDocFlag
     * param {*} documentContentData 补充文档内容
     */
    async getSignDocument(params, signedDocFlag, documentContentData = undefined) {
      let nurseEMRFileId;
      this.localFileUrl = undefined;
      this.originFileUrl = undefined;
      let param = {
        ...params,
        ...{ signedDocFlag: !!signedDocFlag, fileClass: this.fileClass },
        supplementDocumentContentData: documentContentData,
      };
      //获取签名文档
      await GetSignDocument(param).then((response) => {
        if (this._common.isSuccess(response) && response.data != null) {
          let view = response.data;
          nurseEMRFileId = view?.id;
          signedDocFlag && !!view.localFileUrl && (this.localFileUrl = view.localFileUrl);
          signedDocFlag || (!!view.localFileUrl && (this.originFileUrl = view.localFileUrl));
        }
      });
      if (!nurseEMRFileId) {
        return;
      }
      //从MongoDB中下载文件查看
      await GetPatientDocumentByID({ ID: nurseEMRFileId }).then((resp) => {
        if (this._common.isSuccess(resp)) {
          this.localFileUrl = resp.data.fileUrl;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.document-sign {
  height: 100%;
  width: 100%;
  iframe {
    height: 100%;
    width: 100%;
  }
  .sign-btn-wrapper {
    position: relative;
    .sign-btn {
      margin: 0;
      position: absolute;
      border-radius: 50%;
      background-color: transparent;
      border-width: 0px;
      &:hover {
        background-color: rgb(66, 70, 73);
      }
      &::before {
        color: white;
      }
    }
  }
}
.document-content-dialog.el-dialog {
  top: 20% !important;
  width: 400px;
  height: 300px;
}
</style>