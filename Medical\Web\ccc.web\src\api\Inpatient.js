/*
 * FilePath     : \src\api\Inpatient.js
 * Author       : 郭自飞
 * Date         : 2019-10-25 16:41
 * LastEditors  : 马超
 * LastEditTime : 2024-09-20 08:40
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/InPatient";
import qs from "qs";

export const urls = {
  GetPatientDataByBedNumber: baseUrl + "/GetPatientDataByBedNumber",
  GetByInpatientIDAsync: baseUrl + "/GetByInpatientIDAsync",
  GetPatientInfoList: baseUrl + "/GetPatientInfoList",
  GetPatientBasicDataByInpatientID:
    baseUrl + "/GetPatientBasicDataByInpatientID",
  GetPatientDataByChartNo: baseUrl + "/GetPatientDataByChartNo",
  GetTransferPatients: baseUrl + "/GetTransferPatients",
  GetDischargePatients: baseUrl + "/GetDischargePatients",
  GetDischargeEvaluatePatients: baseUrl + "/GetDischargeEvaluatePatients",
  GetInpatientDataList: baseUrl + "/GetInpatientDataList",
  GetPatientBedLabel: baseUrl + "/GetPatientBedLabel",
  GetByCaseNumber: baseUrl + "/GetByCaseNumber",
  GetInpatientDataByChartNoOrCaseNumber: baseUrl + "/GetInpatientDataByChartNoOrCaseNumber",
  GetInpatientDataByLocalCaseNumber: baseUrl + "/GetInpatientDataByLocalCaseNumber",
  GetLastStationIDByInpatientID: baseUrl + "/GetLastStationIDByInpatientID",
  GetEMRArchivingPatients: baseUrl + "/GetEMRArchivingPatients",
  PatientEMRFileRelease: baseUrl + "/PatientEMRFileRelease",
  PatientEMRArchiving: baseUrl + "/PatientEMRArchiving",
  GetInpatientDataViewByCaseNumber: baseUrl + "/GetInpatientDataViewByCaseNumber",
  GetJoinPartDatasByInpatientID: baseUrl + "/GetJoinPartDatasByInpatientID",
  GetPatientByDynamicParam: baseUrl + "/GetPatientByDynamicParam",
};

// 根据病人床号获取病人信息
export const GetPatientDataByBedNumber = (params) => {
  return http.get(urls.GetPatientDataByBedNumber, params);
};
// 根据病人ID获取病人信息
export const GetByInpatientIDAsync = (params) => {
  return http.get(urls.GetByInpatientIDAsync, params);
};
// 根据病人ID获取病人信息
export const GetPatientInfoList = (params) => {
  return http.get(urls.GetPatientInfoList, params);
};
// 根据病人ID获取病人信息
export const GetPatientBasicDataByInpatientID = (params) => {
  return http.get(urls.GetPatientBasicDataByInpatientID, params);
};
// 根据病案号查询病人信息
export const GetPatientDataByChartNo = (params) => {
  return http.get(urls.GetPatientDataByChartNo, params);
};
// 查询病人转科评价
export const GetTransferPatients = (params) => {
  return http.get(urls.GetTransferPatients, params);
};
//取得出院病人
export const GetDischargePatients = (params) => {
  return http.get(urls.GetDischargePatients, params);
};
//根据条件取得出院病人
export const GetDischargeEvaluatePatients = (params) => {
  return http.get(urls.GetDischargeEvaluatePatients, params);
};
// 获取病人清单
export const GetInpatientDataList = (params) => {
  return http.get(urls.GetInpatientDataList, params);
};
// 获取病人床头卡数据
export const GetPatientBedLabel = (params) => {
  return http.get(urls.GetPatientBedLabel, params);
};
//根据住院号获取病人信息
export const GetByCaseNumber = (params) => {
  return http.get(urls.GetByCaseNumber, params);
};
//取得出院病人
export const GetInpatientDataByChartNoOrCaseNumber = (params) => {
  return http.get(urls.GetInpatientDataByChartNoOrCaseNumber, params);
};
//根据LocalCaseNumber获取病人信息
export const GetInpatientDataByLocalCaseNumber = (params) => {
  return http.get(urls.GetInpatientDataByLocalCaseNumber, params);
};
//根据inpatientID获取病人最后的病区ID
export const GetLastStationIDByInpatientID = (params) => {
  return http.get(urls.GetLastStationIDByInpatientID, params);
};
//获取病案归档页面表格列表数据
export const GetEMRArchivingPatients = (params) => {
  return http.get(urls.GetEMRArchivingPatients, params);
};
//病案解档
export const PatientEMRFileRelease = (params) => {
  return http.post(urls.PatientEMRFileRelease, qs.stringify(params));
};
//病案归档
export const PatientEMRArchiving = (params) => {
  return http.post(urls.PatientEMRArchiving, qs.stringify(params));
};
//获取病人信息
export const GetInpatientDataViewByCaseNumber = (params) => {
  return http.get(urls.GetInpatientDataViewByCaseNumber, params);
};
//获取病人部分基本信息（IDCard,PatientName,）
export const GetJoinPartDatasByInpatientID = (params) => {
  return http.get(urls.GetJoinPartDatasByInpatientID, params);
};
//根据条件获取病人信息
export const GetPatientByDynamicParam = (params) => {
  return http.get(urls.GetPatientByDynamicParam, params);
};
