<!--
 * FilePath     : \src\pages\operationRecord\index.vue
 * Author       : 孟昭永
 * Date         : 2021-07-31 15:32
 * LastEditors  : 孟昭永
 * LastEditTime : 2021-08-05 11:59
 * Description  : 
-->
<template>
  <base-layout class="operation">
    <div slot="header">
      <el-button class="btn-refresh" type="primary" icon="iconfont icon-refresh" @click="init">刷新</el-button>
    </div>
    <iframe class="iframe-operation" v-if="flag" :src="url"></iframe>
  </base-layout>
</template>
<script>
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
import { GetOperationRecord } from "@/api/OperationRecord";
export default {
  components: {
    baseLayout,
  },
  computed: {
    ...mapGetters({
      currentPatient: "getCurrentPatient",
    }),
  },
  data() {
    return {
      url: undefined,
      loading: true,
      flag: true,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let params = { caseNumber: this.currentPatient.caseNumber };
      GetOperationRecord(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data) {
            this.url = "data:text/html;charset=gbk;base64," + result.data;
          }
        }
        this.loading = false;
      });
    },
  },
};
</script>
<style lang="scss">
.operation {
  .btn-refresh {
    margin: 10px 20px 0px 0px;
    float: right;
  }
  .iframe-operation {
    height: 98%;
    width: 99%;
    border: 0px;
    html,
    body {
      background-color: #ffffff;
    }
  }
}
</style>
