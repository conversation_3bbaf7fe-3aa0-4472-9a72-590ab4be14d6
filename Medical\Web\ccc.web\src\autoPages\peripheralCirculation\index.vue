<!--
 * FilePath     : \src\autoPages\peripheralCirculation\index.vue
 * Author       : 苏军志
 * Date         : 2020-09-05 11:40
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-01-06 10:09
 * Description  : 末梢血运
 * CodeIterationRecord:  2022-08-16 2876-专项增加带入护理记录选框 -杨欣欣
                        2023-03-11 3270-作为护理人员，我需要可以补录末梢血运记录，以利于记录单显示 -zxz
                        2023-06-27 3587-护理评估按钮跳转专项录入的内容评估时间跟护理评估保持一致 -杨欣欣
-->
<template>
  <specific-care
    class="peripheral-circulation"
    :drawerTitle="drawerTitle"
    v-model="showDrawerFlag"
    element-loading-text="加载中……"
    v-loading="pageLoading"
    :showRecordArr="showRecordArr"
    :informPhysicianFlag="[true, informPhysician]"
    :handOverFlag="[true, bringToShift]"
    :nursingRecordFlag="[!refillFlag, bringToNursingRecord]"
    :editFlag="showEditButton"
    :mainTableHeight="tableOneRowHeight"
    :drawerSize="refillFlag ? '80%' : ''"
    :previewFlag="previewFlag"
    @mainAdd="addOrModify"
    @maintainAdd="addOrModifyCareMain"
    @save="saveRecord"
    @cancel="drawerClose"
    @getHandOverFlag="getCheckStatus($event, 'bringToShift')"
    @getInformPhysicianFlag="getCheckStatus($event, 'informPhysician')"
    @getNursingRecordFlag="getCheckStatus($event, 'bringToNursingRecord')"
  >
    <!-- 主记录 -->
    <div slot="main-record">
      <el-table
        :data="pcRecordList"
        border
        stripe
        height="100%"
        @row-click="recordClick"
        row-class-name="main-record-row"
        header-row-class-name="main-record-herder-row"
      >
        <el-table-column label="发生科室" prop="departmentListName" :width="convertPX(210)" header-align="center" />
        <el-table-column label="发生时间" :width="convertPX(180)" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.startDate, type: 'date' }"></span>
            <span v-formatTime="{ value: scope.row.startTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="部位" prop="bodyPartName" :min-width="convertPX(200)" header-align="center" />
        <el-table-column label="执行人" prop="addEmployeeName" :width="convertPX(120)" align="center" />
        <el-table-column label="操作" :width="convertPX(95)" header-align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div class="iconfont icon-edit" @click.stop="addOrModify(scope.row)"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div class="iconfont icon-del" @click.stop="deletePC(scope.row)"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="maintain-record">
      <el-table height="100%" :data="pcCareMains" border stripe>
        <el-table-column label="类型" :width="convertPX(100)" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.recordsCode.indexOf('Start') != -1">开始评估</span>
            <span v-else>例行评估</span>
          </template>
        </el-table-column>
        <el-table-column label="时间" :width="convertPX(200)" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessDate, type: 'date' }"></span>
            <span v-formatTime="{ value: scope.row.assessTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="科室" prop="departmentListName" :width="convertPX(250)" header-align="center" />
        <el-table-column label="护理级别" prop="nursingLevel" align="center" :width="convertPX(80)" />
        <el-table-column label="皮肤温度" prop="skinTemperature" align="center" :width="convertPX(80)" />
        <el-table-column label="皮肤颜色" prop="skinColor" align="center" :width="convertPX(80)" />
        <el-table-column label="动脉搏动" prop="arterialPulsation" align="center" :width="convertPX(80)" />
        <el-table-column label="充血反应" prop="hyperemia" align="center" :width="convertPX(80)" />
        <el-table-column label="肿胀程度" prop="swellingDegree" align="center" :width="convertPX(80)" />
        <el-table-column label="主观感觉" prop="subjectiveFeeling" align="center" :width="convertPX(80)" />
        <el-table-column label="措施" prop="careIntervention" :min-width="convertPX(400)" header-align="center" />
        <el-table-column label="执行人" prop="addEmployeeName" align="center" :width="convertPX(150)" />
        <el-table-column label="操作" :width="convertPX(80)" header-align="center" fixed="right">
          <template slot-scope="scope" v-if="scope.row.recordsCode.indexOf('Start') == -1">
            <el-tooltip content="修改" placement="top">
              <i class="iconfont icon-edit" @click="addOrModifyCareMain(scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <i class="iconfont icon-del" @click="deletePCCare(scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      element-loading-text="保存中……"
      class="add-mod-pc"
    >
      <div slot="header">
        <div class="right-top">
          <span>开始日期:</span>
          <el-date-picker
            class="date-picker"
            v-model="assessDate"
            type="date"
            :clearable="false"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          />
          <el-time-picker
            class="time-picker"
            v-model="assessTime"
            :clearable="false"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
          />
          <station-selector v-model="currentStationID" label="发生病区:" width="160"></station-selector>
          <dept-selector
            label=""
            width="140"
            v-model="currentDepartmentListID"
            :stationID="currentStationID"
          ></dept-selector>
        </div>
      </div>
      <body-and-assess-layout :link="link" :bodyShowFlag="showBodyPartFlag">
        <tabs-layout :template-list="templateDatas" @change-values="changeValues" @checkTN="checkTN" />
      </body-and-assess-layout>
    </base-layout>
  </specific-care>
</template>
<script>
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
import specificCare from "@/components/specificCare";
import tabsLayout from "@/components/tabsLayout/index";
import { GetBringToShiftSetting } from "@/api/Setting.js";
import deptSelector from "@/components/selector/deptSelector";
import stationSelector from "@/components/selector/stationSelector";
import bodyAndAssessLayout from "@/components/bodyAndAssessLayout";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { GetAssessRecordsCodeByDeptID } from "@/api/Assess";
import {
  GetPCAssessView,
  SavePatientPC,
  GetPCRecordList,
  DeletePCByID,
  GetPCCareMainsByID,
  SavePatientPCCare,
  DeletePCCare,
} from "@/api/PeripheralCirculation";
export default {
  components: { baseLayout, tabsLayout, stationSelector, deptSelector, specificCare, bodyAndAssessLayout },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      //人体图链接
      link: "",
      //抽屉弹窗中的评估日期
      assessDate: undefined,
      //抽屉弹框中的评估时间
      assessTime: undefined,
      //抽屉弹框中选择的病区
      currentStationID: undefined,
      //抽屉弹框中选择的科室
      currentDepartmentListID: undefined,
      //抽屉弹窗显示标题
      drawerTitle: `末梢血运维护`,
      //评估模板加载中标志
      drawerLoading: false,
      //评估模板数据
      templateDatas: [],
      //末梢血运主记录list
      pcRecordList: [],
      //末梢血运明细记录
      pcCareMains: undefined,
      //末梢血运页面遮罩加载标志
      pageLoading: false,
      //当前页面使用的病人信息：接受父组件传递的病人信息
      patient: undefined,
      //控制抽屉弹窗的显示
      showDrawerFlag: false,
      /**
       * 接受的跳转参数
       */
      assessMainID: "",
      assessSort: 0,
      sourceID: undefined,
      sourceType: undefined,
      //措施执行跳转的排程ID
      patientScheduleMainID: "",
      //记录(依据病人信息获取到的不同评估模板信息)departmentToAssess
      recordsCodeInfo: undefined,
      //记录点击过的末梢血运主记录ID
      patientPCRecordID: undefined,
      //勾选的评估模板回调数据
      assessDatas: [],
      //记录评估模板中TN类型检查是否正常
      checkTNFlag: true,
      //记录在当前页面勾选带入交班的复选状态
      bringToShift: false,
      //记录通知医生的勾选
      informPhysician: false,
      //记录在当前页面勾选带入护理记录的复选状态
      bringToNursingRecord: false,
      //判断是否显示编辑按钮/判断是否具有编辑权限
      showEditButton: false,
      //记录带入交班的默认配置
      settingHandOver: false,
      //记录带入护理记录的默认配置
      settingBringToNursingRecord: false,
      //控制弹框显示
      showRecordArr: [true, false],
      //判断新增/修改 弹框页面是否显示身体部位图
      showBodyPartFlag: true,
      //补录标记 varchar
      refillFlag: undefined,
      //主记录表格表头加第一行的高度
      tableOneRowHeight: undefined,
      previewFlag: false,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
    }),
  },
  watch: {
    patientInfo: {
      handler(newPatient) {
        if (newPatient) {
          this.patient = newPatient;
        }
      },
      immediate: true,
    },
    patient: {
      handler(newPat) {
        if (newPat) {
          this.getRecordList();
        } else {
          this.pcRecordList = [];
        }
      },
      immediate: true,
    },
    //监听抽屉关闭，关闭人体图显示
    showDrawerFlag: {
      handler(newPat) {
        if (!newPat) {
          this.showBodyPartFlag = false;
        }
      },
    },
  },
  created() {
    /**
     * 评估串末梢血运专项传值
     * bedNumber|isDialog|num='3'|sort='3'|token|userID
     * 病情观察串末梢血运专项
     * bedNumber|isDialog|sourceID|sourceType|token|userID
     * 排程跳转末梢血运专项页面
     * completeMark|departmentListID|inpatientID|isDialog|nursingLevel|patientScheduleMainID|scheduleDate|scheduleTime|stationID
     */
    this.assessMainID = this.$route.query.num;
    this.assessSort = this.$route.query.sort;
    this.sourceID = this.$route.query.sourceID;
    this.sourceType = this.$route.query.sourceType;
    this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
    // 设置病人头是否可以切换病人
    if (this.patientScheduleMainID || this.assessMainID || this.sourceID) {
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
    //获取默认勾选带入交班和护理记录的配置
    this.GetBringHandOverSetting();
    this.getBringToNursingRecordSetting();
    if (this.supplemnentPatient && this.supplemnentPatient.inpatientID) {
      this.patient = this.supplemnentPatient;
      this.refillFlag = "*";
      return;
    }
    //获取主记录
    if (this.patient && this.patient.inpatientID) {
      this.getRecordList();
    }
  },
  methods: {
    /**
     * description: 接收评估模板发生改变的数据
     * return {*}
     * param {Object} data
     */
    changeValues(data) {
      this.assessDatas = data;
    },
    /**
     * description: TN类型录入内容检核结果
     * return {*}
     * param {Boolean} flag true:成功 false:失败
     */
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    /**
     * description: 抽屉关闭方法回调
     * return {*}
     */
    drawerClose() {
      //人体图不显示：解决连续新增主记录时，因为人体图没有重新加载,导致新增报错没有选择部位（实际页面上已经显示第一次新增时勾选的部位）
      this.showBodyPartFlag = false;
      this.drawerLoading = true;
      this.showDrawerFlag = false;
      this.templateDatas = [];
      this.assessDatas = [];
      localStorage.setItem("selectPart", JSON.stringify([]));
      localStorage.setItem("bodyPart", JSON.stringify([]));
    },
    /**
     * description: 末梢血运主记录的新增/修改弹框打开前的前置数据处理
     * return {*}
     * param {Object} item :新增：null，修改：主记录信息
     */
    async addOrModify(item) {
      //抽屉页面加载中,重复点击无效
      if (this.pageLoading) {
        return;
      }
      //设置抽屉显示的标题
      this.showBodyPartFlag = true;
      this.drawerTitle = `末梢血运维护${item ? `--${item.bodyPartName}` : ""}`;
      //显示人体图
      this.link =
        "../../static/body/mobileBody.html?type=CommonMulti&recordsCode=PeripheralCirculation&gender=" +
        this.patient.genderCode;
      if (item) {
        if (this.refillFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            item.addEmployeeID
          );
          this.previewFlag = !saveButtonFlag;
        }
        //设置按钮显示和人员可编辑状态
        this.setButtonDisplayStatus(item.addEmployeeID, item.patientPCRecordID, "PatientPCRecord");
        this.patientPCRecordID = item.patientPCRecordID;
        this.pcCareMainID = undefined;
        this.assessDate = this._datetimeUtil.formatDate(item.startDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.startTime, "hh:mm:ss");
        /*
         * selectPart：用于人体图初始化的时候,将原本原则的身体部分选项呈现到页面上，在onload加载完成后会被清除=[]
         * bodyPart:在人体图选项被选中的时候触发selected方法，然后将选择的部位信息放入bodyPart
         */
        localStorage.setItem("selectPart", item.bodyPartID);
        localStorage.setItem("bodyPart", item.bodyPartID);
        this.resetDatas(item);
      } else {
        //新增时可以操作按钮
        this.showEditButton = true;
        //新增时将主记录ID清空 |1.防止保存时，新增主记录变成修改主记录了 2.防止新增获取评估模板传CareMainID
        this.patientPCRecordID = undefined;
        this.pcCareMainID = undefined;
        this.resetDatas();
      }
      //设置维护页面中复选框的值
      this.setCheckStatus(item);
      // 获取模板
      this.getPCAssessView("PeripheralCirculationStart");
    },
    /**
     * description: 重置数据，将数据还原成默认状态
     * return {*}
     * param {Object} item 末梢血运 | 主记录/维护记录
     */
    resetDatas(item) {
      this.drawerLoading = true;
      this.showDrawerFlag = true;
      this.templateDatas = [];
      this.assessDatas = [];
      if (item) {
        this.currentStationID = item.stationID;
        this.currentDepartmentListID = item.departmentListID;
        return;
      }

      this.assessDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
      this.currentStationID = this.patient.stationID;
      this.currentDepartmentListID = this.patient.departmentListID;
      localStorage.setItem("selectPart", JSON.stringify([]));
      localStorage.setItem("bodyPart", JSON.stringify([]));
    },
    /**
     * description: 末梢血运维护记录的新增/修改弹框打开前的前置数据处理
     * return {*}
     * param {Object} item :新增：null，修改：维护记录信息
     */
    async addOrModifyCareMain(pcCare) {
      //维护记录弹窗中不显示人体图
      this.showBodyPartFlag = false;
      if (this.pageLoading) {
        return;
      }
      if (pcCare && pcCare.patientPCCareMainID) {
        if (this.refillFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            pcCare.addEmployeeID
          );
          this.previewFlag = !saveButtonFlag;
        }
        //设置按钮显示和人员可编辑状态
        await this.setButtonDisplayStatus(pcCare.addEmployeeID, pcCare.patientPCCareMainID, "PatientPCCareMain");
        this.careMain = pcCare;
        this.pcCareMainID = pcCare.patientPCCareMainID;
        this.assessDate = pcCare.assessDate;
        this.assessTime = pcCare.assessTime;
        this.numberOfAssessment = pcCare.numberOfAssessment;
        this.resetDatas(pcCare);
      } else {
        //新增维护获取评估模板
        this.showEditButton = true;
        this.careMain = undefined;
        this.pcCareMainID = "";
        this.numberOfAssessment = 1;
        this.resetDatas();
      }
      this.setCheckStatus(pcCare);
      //获取对应的评估模板
      this.getPCAssessView("PeripheralCirculationMaintain");
    },
    /**
     * description: 加载抽屉时,处理抽屉底部的复选框选中值
     * return {*}
     * param {Object} record: 没值取默认
     */
    setCheckStatus(record) {
      if (!!record) {
        this.bringToShift = record[`bringToShift`];
        this.informPhysician = record[`informPhysician`];
        this.bringToNursingRecord = record[`bringToNursingRecord`];
        return;
      }
      this.bringToShift = this.settingHandOver;
      this.informPhysician = false;
      this.bringToNursingRecord = this.settingBringToNursingRecord;
    },
    /**
     * description: 抽屉下方勾选项数据回调
     * param {Boolean} flag 回传的勾选状态
     * param {String} checkName 勾选项字段名
     * return {*}
     */
    getCheckStatus(flag, checkName) {
      this[checkName] = flag;
    },
    /**
     * description: 检查用户是否具有显示操作的权限，来确定是否显示按钮
     * return {*}
     * param {String} employeeID
     * param {String} pcID
     * param {String} recordType
     */
    async setButtonDisplayStatus(employeeID, pcID, recordType) {
      //判断是否可修改或删除该数据
      this.showEditButton = await this._common.checkActionAuthorization(this.user, employeeID);
      if (this.showEditButton) {
        let ret = await this._common.getEditAuthority(pcID, recordType);
        if (ret) {
          this.showEditButton = false;
          this._showTip("warning", ret);
        } else {
          this.showEditButton = true;
        }
      }
    },
    /**
     * description: 主记录点击
     * param {Object} row
     * return {*}
     */
    async recordClick(row) {
      //主记录是否呈现切换
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      //维护记录是否呈现切换
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      //维护记录显示
      if (this.showRecordArr[1]) {
        this.pcRecordList = [row];
        //点击主记录时记录主记录ID,用于维护记录操作
        this.patientPCRecordID = row.patientPCRecordID;
        this.tableOneRowHeight = this._common.getTableOneRowHeight();
        this.getCareMainList();
      } else {
        await this.getRecordList();
      }
    },
    /**
     * description: 获取末梢血运评估模板
     * return {*}
     * param {String} mappingType 对应departmentToAssess中的mappingType字段
     */
    async getPCAssessView(mappingType) {
      let params = {
        inpatient: this.patient.inpatientID,
        departmentListID: this.patient.departmentListID,
        mappingType: mappingType,
        age: this.patient.age,
        dateOfBirth: this.patient.dateOfBirth,
      };
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo) return;
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        dateOfBirth: this.patient.dateOfBirth,
        patientPCRecordID: this.patientPCRecordID,
        careMainID: this.pcCareMainID,
      };
      await GetPCAssessView(params).then((result) => {
        //关闭遮罩
        this.drawerLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 获取末梢血运主记录List<T>
     * return {*}
     */
    async getRecordList() {
      //关闭抽屉弹窗
      this.showDrawerFlag = false;
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      //获取末梢血运记录列表
      await GetPCRecordList(params).then((result) => {
        this.pageLoading = false;
        if (this._common.isSuccess(result)) {
          this.pcRecordList = result.data;
          //清空旧的维护记录
          this.pcCareMains = [];
          //主记录呈现
          this.$set(this.showRecordArr, 0, 1);
          //维护记录否呈现
          this.$set(this.showRecordArr, 1, 0);
        }
      });
    },
    /**
     * description: 获取末梢血运维护记录
     * return {*}
     */
    getCareMainList() {
      //关闭抽屉弹窗 |刷新数据前
      this.showDrawerFlag = false;
      let params = {
        ID: this.patientPCRecordID,
      };
      GetPCCareMainsByID(params).then((result) => {
        this.pageLoading = false;
        if (this._common.isSuccess(result)) {
          this.pcCareMains = result.data;
        }
      });
    },
    /**
     * description: 删除末梢血运主记录
     * return {*}
     * param {Object} row 末梢血运主记录
     */
    async deletePC(row) {
      //设置按钮显示和人员可编辑状态
      this.setButtonDisplayStatus(row.addEmployeeID, row.patientPCRecordID, "PatientPCRecord");
      if (!this.showEditButton) {
        return;
      }
      if (this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.addEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          DeletePCByID({ ID: row.patientPCRecordID }).then((result) => {
            this.pcRecordList = [];
            this.pcCareMains = [];
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              // 刷新数据
              this.getRecordList();
            }
          });
        }
      });
    },
    /**
     * description: 删除维护记录
     * return {*}
     * param {Object} row 维护记录
     */
    async deletePCCare(row) {
      //设置按钮显示和人员可编辑状态
      this.setButtonDisplayStatus(row.addEmployeeID, row.patientPCCareMainID, "PatientPCCareMain");
      if (!this.showEditButton) {
        return;
      }
      if (this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.addEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          DeletePCCare({ mainID: row.patientPCCareMainID }).then((result) => {
            if (this._common.isSuccess(result)) {
              this._showTip("success", "删除成功！");
              this.getCareMainList();
            }
          });
        }
      });
    },
    /**
     * description: 保存记录公共调用方法 |新增or修改保存
     * return {*}
     */
    saveRecord() {
      //必选条件检核|不满足
      if (this.checkSaveCondition()) {
        return;
      }
      if (this.recordsCodeInfo.recordsCode.includes("Start")) {
        this.recordSave();
      }
      if (this.recordsCodeInfo.recordsCode.includes("Maintain")) {
        this.careMainSave();
      }
    },
    /**
     * description: 末梢血运维护记录保存
     * return {*}
     */
    careMainSave() {
      let saveDetailData = {
        inpatientID: this.patient.inpatientID,
        patientPCCareMainID: this.pcCareMainID,
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        nursingLevel: this.patient.nursingLevelCode,
        recordsCode: this.recordsCodeInfo.recordsCode,
        pCRecordID: this.patientPCRecordID,
        numberOfAssessment: this.numberOfAssessment,
        assessDate: this.assessDate,
        assessTime: this.assessTime,
        stationID: this.currentStationID,
        departmentListID: this.currentDepartmentListID,
        details: [],
        pCImages: [],
        handoverID: this.handoverID,
        bringToShift: this.bringToShift,
        informPhysician: this.informPhysician,
        bringToNursingRecord: this.bringToNursingRecord,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        refillFlag: this.refillFlag,
      };
      //如果来源于措施执行则填入
      if (this.patientScheduleMainID) {
        saveDetailData.PatientScheduleMainID = this.patientScheduleMainID;
      }
      //获取保存的评估明细数据
      saveDetailData.Details = this.setAssessDetail();
      this.drawerLoading = true;
      SavePatientPCCare(saveDetailData).then((result) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          // 刷新数据
          this.getCareMainList();
        }
      });
    },
    /**
     * description: 末梢血运主记录保存
     * return {*}
     */
    recordSave() {
      //选择部位
      var selectPart = JSON.parse(localStorage.getItem("bodyPart"));
      if (!selectPart || selectPart.length <= 0) {
        this._showTip("warning", "请选择部位！");
        return;
      }
      this.drawerLoading = true;
      // 新增保存数据
      let saveData = {
        main: {},
        details: [],
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        nursingLevel: this.patient.nursingLevelCode,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessSort: this.assessSort,
        bringToShift: this.bringToShift,
        informPhysician: this.informPhysician,
        bringToNursingRecord: this.bringToNursingRecord,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        refillFlag: this.refillFlag,
      };
      //如果来源于措施执行则填入
      if (this.patientScheduleMainID) {
        saveData.patientScheduleMainID = this.patientScheduleMainID;
      }
      //获取保存的评估明细数据
      saveData.Details = this.setAssessDetail();
      saveData.main.patientPCRecordID = this.patientPCRecordID;
      saveData.main.assessMainID = this.assessMainID;
      saveData.main.bodyPartID = JSON.stringify(selectPart);
      saveData.main.bodyShowName = this.getBodyPartName(selectPart);
      saveData.main.inpatientID = this.patient.inpatientID;
      saveData.main.startDate = this.assessDate;
      saveData.main.startTime = this.assessTime;
      saveData.main.stationID = this.currentStationID;
      saveData.main.departmentListID = this.currentDepartmentListID;
      saveData.main.refillFlag = this.refillFlag;
      SavePatientPC(saveData).then((result) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          // 返回主页面 刷新数据
          this.getRecordList();
        }
      });
    },
    /**
     * description: 评估模板明细数据获取（用于保存末梢血运主记录和维护记录明细数据）
     * return {List<Object>}details
     */
    setAssessDetail() {
      let flag = true;
      const details = [];
      this.assessDatas.forEach((content) => {
        // 计算公式结果项目不检核
        if (!content.formula) {
          let result = this._common.checkAssessTN(content);
          if (!result.flag) {
            flag = false;
          }
        }
        let detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID,
        };
        if (content.controlerType.trim() == "C" || content.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = content.assessValue;
        }
        details.push(detail);
      });
      return details;
    },
    /**
     * description: 保存时,检查必选的参数条件是否满足
     * return {*} true:不满足 false：满足
     */
    checkSaveCondition() {
      if (this.currentStationID == "") {
        this._showTip("warning", "请选择发生病区");
        return true;
      }
      if (this.currentDepartmentListID == "") {
        this._showTip("warning", "请选择发生科室");
        return true;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return true;
      }
      if (this.assessDatas.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        return true;
      }
      return false;
    },
    /**
     * description: 根据选择的多个身体部位选项，拼接身体部位名
     * return {*}
     * param {List<Object>} selectPart
     */
    getBodyPartName(selectPart) {
      if (!selectPart || selectPart.length <= 0) {
        return "";
      }
      let name = "";
      selectPart.forEach((part) => {
        if (name) {
          name += "，" + part.bodyPartName;
        } else {
          name = part.bodyPartName;
        }
      });
      return name;
    },
    /**
     * description: 获取是否带入交班配置
     * return {*}
     */
    GetBringHandOverSetting() {
      let params = {
        special: "PC",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.bringToShift = res.data;
          this.settingHandOver = res.data;
        }
      });
    },
    /**
     * description: 获取是否带入护理记录配置
     * param {*}
     * return {*}
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "PcAutoInterventionToRecord",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.bringToNursingRecord = res.data;
          this.settingBringToNursingRecord = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.peripheral-circulation {
  height: 100%;
  width: 100%;
  .top {
    width: 100%;
    height: 100%;
    text-align: right;
  }
  .station-selector .label {
    margin-left: 0px;
  }
  .add-mod-pc {
    min-width: 720px;
    background-color: white;
    .right-top {
      text-align: left;
      min-height: 50px;
      .date-picker {
        width: 160px;
      }
      .time-picker {
        width: 140px;
      }
    }
  }
}
</style>