/*
 * FilePath     : \src\utils\i18n\lang\en\common.js
 * Author       : 苏军志
 * Date         : 2021-11-01 11:56
 * LastEditors  : 苏军志
 * LastEditTime : 2022-06-19 16:22
 * Description  : 公共部分
 */
export default {
  // 按钮显示文字
  button: {
    add: "Add",
    modify: "Modify",
    query: "Query",
    save: "Save",
    delete: "Delete",
    print: "Print",
    edit: "Edit",
    stop: "Stop",
    export: "Export",
    cancel: "No",
    confirm: "Yes",
    back: "Back"
  },
  // loading提示文字
  loadingText: {
    load: "Loading……",
    save: "Saving……",
    delete: "Deleting……",
    saveSuccess: "Save successed!",
    deleteSuccess: "Delete successsed!",
    updateSuccess: "Update successsed!"
  },
  //tooltip显示文字
  tip: {
    modify: "Modify",
    query: "Query",
    save: "Save",
    delete: "Delete",
    print: "Print",
    edit: "Edit",
    stop: "Stop",
    systemTip: "System message",
    dataEmpty: "No Data",
    operationAuthority: "No permission only if the data owner!",
    deleteConfirm: "Are you sure you want to delete data?"
  },
  // 选择框、输入框、日期时间等组件提示文字
  placeholder: {
    station: "Selecting station",
    shift: "Selecting shift",
    date: "Selecting date",
    time: "Selecting time"
  },
  // 标签显示文字
  label: {
    systemName: "CareDirect",
    shiftDate: "Shift Date:",
    shift: "Shift:",
    date: "Date:",
    time: "Time:",
    operation: "Operating",
    station: "Station:"
  }
};
