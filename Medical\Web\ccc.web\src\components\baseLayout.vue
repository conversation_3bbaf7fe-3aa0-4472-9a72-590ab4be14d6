<!--
 * FilePath     : \src\components\baseLayout.vue
 * Author       : 李青原
 * Date         : 2020-05-09 19:47
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-15 12:04
 * Description  : 
 -->
<template>
  <div class="base-layout">
    <div v-if="showHeader" class="base-header" :style="headerStyle">
      <slot name="header">Header</slot>
    </div>
    <div class="base-content">
      <!-- 加base-content-wrap为了防止插进来元素的height无效 -->
      <div class="base-content-wrap" ref="baseContentWrap">
        <slot v-bind:height="mainHeight">Main</slot>
      </div>
    </div>

    <div v-if="showFooter" class="base-footer" :style="footerStyle">
      <slot name="footer">Footer</slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    headerHeight: {
      type: String,
      default: "50px",
    },
    footerHeight: {
      type: String,
      default: "40px",
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    showFooter: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mainHeight: 0,
    };
  },
  computed: {
    headerStyle() {
      let newHeight = this._convertUtil.getHeigt(this.headerHeight, true);
      let style = {};
      if (newHeight === "auto") {
        style["line-height"] = this.convertPX(50) + "px";
      }
      style["height"] = newHeight;
      return style;
    },
    footerStyle() {
      let newHeight = this._convertUtil.getHeigt(this.footerHeight, true);
      let style = {};
      if (newHeight === "auto") {
        style["line-height"] = this.convertPX(50) + "px";
      } else {
        style["line-height"] = newHeight;
      }
      style["height"] = newHeight;
      return style;
    },
  },
  mounted() {
    this.$nextTick(() => {
      // 页面渲染完成后的回调
      if (this.$refs.baseContentWrap) {
        this.mainHeight = this.$refs.baseContentWrap.offsetHeight;
      }
    });
    window.onresize = () => {
      return (() => {
        this.$nextTick(() => {
          // 页面渲染完成后的回调
          if (this.$refs.baseContentWrap) {
            this.mainHeight = this.$refs.baseContentWrap.offsetHeight;
          }
        });
      })();
    };
  },
  methods: {
    // 页面自适应新增方法， 旧方法不删除，为了兼容未自适应的画面
    getMainHeight() {
      let mainHeight = 0;
      // 页面渲染完成后的回调
      if (this.$refs.baseContentWrap) {
        mainHeight = this.$refs.baseContentWrap.offsetHeight;
      }
      return mainHeight;
    },
  },
};
</script>

<style lang="scss">
.base-layout {
  height: 100%;
  width: 100%;
  padding: 5px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .base-header {
    line-height: 45px;
    background-color: #fff;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding: 0 10px;
  }
  .base-content {
    position: relative;
    height: 100%;
    box-sizing: border-box;
    flex: auto;
    overflow: auto;
    .base-content-wrap {
      position: absolute !important;
      height: calc(100% - 2px);
      width: 100%;
    }
  }
  .base-footer {
    box-sizing: border-box;
    text-align: right;
  }
  /* 如果自己嵌套自己，去掉内层的边距 */
  .base-content .base-layout {
    padding: 0;
  }
  .base-content .base-layout .base-content {
    width: 100%;
  }
}
</style>
