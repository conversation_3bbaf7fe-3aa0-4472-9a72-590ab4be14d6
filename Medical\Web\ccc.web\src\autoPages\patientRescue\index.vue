<template>
  <specific-care
    class="rescue-record"
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    recordTitleSlotFalg
    :nursingRecordFlag="bringToNursingRecordArr"
    :editFlag="showEditButton"
    :careMainAddFlag="careMainAddFlag"
    :recordAddFlag="recordAddFlag"
    :mainTableHeight="88"
    :drawerSize="refillFlag ? '80%' : ''"
    @mainAdd="recordAdd"
    @maintainAdd="careMainShow"
    @save="saveRescue"
    @cancel="drawerClose"
    @getHandOverFlag="receiveEmit($event, 'handOverArr', 1)"
    @getInformPhysicianFlag="receiveEmit($event, 'informPhysicianArr', 1)"
    @getNursingRecordFlag="receiveEmit($event, 'bringToNursingRecordArr', 1)"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <template slot="record-title">
      <div class="title">
        <el-radio-group v-model="showHistoryRescue" @change="radioChange">
          <el-radio-button :label="false">抢救过程</el-radio-button>
          <el-radio-button :label="true">历史记录</el-radio-button>
        </el-radio-group>
      </div>
    </template>
    <!-- 主记录 -->
    <div slot="main-record">
      <packaging-table ref="recordTable" v-model="records" :headerList="recordTableShowHeader" @rowClick="recordClick">
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="scope">
          <el-tooltip content="修改">
            <div @click.stop="recordAdd(scope.row)" class="iconfont icon-edit"></div>
          </el-tooltip>
          <el-tooltip content="停止">
            <div @click.stop="recordEnd(scope.row)" :class="['iconfont', { 'icon-stop': !scope.row.endTime }]"></div>
          </el-tooltip>
          <el-tooltip content="删除">
            <div @click.stop="deleteRecord(scope.row)" class="iconfont icon-del"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <packaging-table ref="maintainTable" v-model="careMainTableData" :headerList="careMainTableHeaderList">
        <!-- 评估类型 插槽 -->
        <template slot="assessType" slot-scope="scope">
          <span v-if="scope.row.recordsCode.includes('Start')">开始评估</span>
          <span v-else-if="scope.row.recordsCode.includes('End')">结束评估</span>
          <span v-else>例行评估</span>
        </template>
        <!-- 操作 插槽-->
        <template slot="operate" slot-scope="scope">
          <el-tooltip content="修改" placement="top" v-if="!scope.row.recordsCode.includes('Start')">
            <div class="iconfont icon-edit" @click="careMainShow(scope.row)"></div>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" v-if="!scope.row.recordsCode.includes('Start')">
            <div class="iconfont icon-del" @click="deleteCareMain(scope.row)"></div>
          </el-tooltip>
        </template>
      </packaging-table>
    </div>
    <!-- 抽屉 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
    >
      <template slot="header">
        <span class="label">时间：</span>
        <el-date-picker
          class="date-picker"
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
        />
        <el-time-picker
          class="time-picker"
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
        />
        <station-selector v-model="stationID" label="病区:" width="128" />
        <dept-selector label="" width="160" v-model="departmentListID" :stationID="stationID" />
      </template>
      <div class="base-layout-content">
        <tabs-layout
          ref="tabsLayout"
          :template-list="templateData"
          check-flag
          @button-click="buttonClick"
          @changeItem="changeItem"
          @change-values="receiveEmit($event, 'assessData')"
          @checkTN="receiveEmit($event, 'checkTNFlag')"
        >
          <div slot="customize" v-if="drugState">
            <span class="drug-soan">使用药物：</span>
            <div v-if="rescueDrugFlag">
              <order-drug v-model="drugTableData" :orderList="orderDrugOptions" />
            </div>
            <template v-else>
              <setting-drug
                ref="settingDrug"
                v-model="drugTableData"
                :channelList="channelList"
                :solventList="solventList"
                :drugList="drugList"
              />
            </template>
          </div>
        </tabs-layout>
      </div>
    </base-layout>
    <!-- 弹窗 -->
    <div class="drawer-dialog" slot="drawer-dialog">
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonName"
        :visible.sync="showButtonDialog"
        fullscreen
        custom-class="no-footer specific-care-view"
        :append-to-body="true"
      >
        <iframe v-if="showButtonDialog" ref="buttonDialog" scrolling="no" frameborder="0" width="100%" height="99%" />
      </el-dialog>
    </div>
  </specific-care>
</template>
<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import packagingTable from "@/components/table/index";
import { mapGetters } from "vuex";
import { GetAssessRecordsCodeByDeptID, GetClinicalSettingInfo, GetButtonData } from "@/api/Assess";
import { GetOrderDrugUseRescue } from "@/api/Orders";
import orderDrug from "./components/orderDrug.vue";
import settingDrug from "./components/settingDrug.vue";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import {
  AddRescueRecord,
  AddRescueCareMain,
  StopRescue,
  DeleteRescueRecord,
  DeleteRescueCareMain,
  UpdateRescueRecord,
  UpdateRescueCareMain,
  GetPatientRescueRecords,
  GetRescueAssessView,
  GetCareMainsByRecordID,
  GetRescueMedications,
} from "@/api/rescueRecord";
import { GetBringToShiftSetting } from "@/api/Setting";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { GetBringToNursingRecordFlagSetting } from "@/api/SettingDescription";
import { GetObserveTemplate } from "@/api/thrombolysis.js";
export default {
  components: {
    specificCare,
    tabsLayout,
    baseLayout,
    packagingTable,
    stationSelector,
    deptSelector,
    orderDrug,
    settingDrug,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
    recordAddFlag() {
      return this.unEndRecords.length === 0 && !this.showHistoryRescue;
    },
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      loading: false,
      patient: undefined,
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      previewFlag: false,
      showHistoryRescue: false,
      //主记录变量
      recordTableData: [],
      records: [],
      unEndRecords: [],
      endRecords: [],
      recordID: undefined,
      currentRecord: undefined,
      //维护记录变量
      careMainTableData: [],
      careMainID: undefined,
      // 新增修改标记
      isAdd: true,
      //弹窗变量
      drawerLoading: false,
      drawerLoadingText: undefined,
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      userID: undefined,
      departmentListID: undefined,
      templateData: [],
      recordsCodeInfo: {},
      assessData: [],
      checkTNFlag: true,
      recordsCode: undefined,
      handOverArr: [true, false],
      informPhysicianArr: [true, false],
      bringToNursingRecordArr: [true, false],
      settingHandOver: false,
      settingNursingRecord: false,
      settingInformPhysician: false,
      //路由变量
      sourceID: undefined,
      sourceType: undefined,
      refillFlag: "",
      showEditButton: true,
      // 动态表头
      recordTableShowHeader: [],
      recordTableHeaderList: [],
      recordTableEndHeaderList: [],
      careMainTableHeaderList: [],
      // 维护记录新增按钮开关
      careMainAddFlag: true,
      // 专项按钮跳转相关参数
      showButtonDialog: false,
      buttonAssessListID: "",
      buttonName: "",
      drugState: true,
      // 抢救药物表数据
      medicationList: [],
      drug: "",
      //使用药物数据源，开：对接医嘱，关：Clinical配置
      rescueDrugFlag: false,
      // 使用药物表格数据
      drugTableData: [],
      // 选项，配置药物
      channelList: [],
      solventList: [],
      drugList: [],
      // 选项，医嘱药物
      orderDrugOptions: [],
      inputRescueLabel: "速度:",
      //患者状况
      patientConditionAssessListID: [
        60007240, 60007250, 60007260, 60007270, 60007280, 60007290, 60007300, 60007310, 60007320, 60007330, 60007340,
        60007350, 60007360, 60007370, 6116090, 60007707,
      ],
      templateToAssessListID: {
        530: "@意识@",
        532: "@意识@",
        531: "@意识@",
        1307: "@意识@",
        1674: "@意识@",
        2136: "@意识@",
        2137: "@意识@",
        2138: "@意识@",
        1228: "@意识@",
        2139: "@意识@",
        2140: "@意识@",
        2141: "@意识@",
        4440: "@意识@",
        3197: "@意识@",
        2451: "@呼吸@",
        2115: "@呼吸@",
        60007830: "@心率@",
        60007831: "@心率@",
        60007450: "@转归@",
        60007441: "@转归@",
        60007453: "@转归@",
        60007455: "@转归@",
        60007442: "@转归@",
        60007457: "@转归@",
      },
      //观察措施模板ID
      observeTemplateAssessListID: 1984,
      //观察措施模板
      observeTemplate: "",
    };
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.refillFlag = "";
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.refillFlag = "*";
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
    showButtonDialog(newVal) {
      if (!newVal) {
        this.updateButton();
      }
    },
  },
  methods: {
    /**
     * @description: 初始化
     * @return
     */
    async init() {
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
      const isLink = this.sourceID || this.sourceType;
      // 跳转页面不允许切换病人
      this._sendBroadcast("setPatientSwitch", !isLink);
      await this.getRecordTableData();
      this.fixTable();
      await this.getTableHeaderList("Record", "recordTableHeaderList");
      this.recordTableShowHeader = this.recordTableHeaderList;
      await this.getTableHeaderList("RecordEnd", "recordTableEndHeaderList");
      await this.getTableHeaderList("Maintain", "careMainTableHeaderList");
      this.getBringHandOverSetting();
      this.getBringToNursingRecordSetting();
      this.initRescueDrug();
    },
    /**
     * @description: 获取维护记录动态列
     * @param classSub 副类别
     * @param headerList 要赋值的变量
     * @return
     */
    async getTableHeaderList(classSub, headerList) {
      let params = {
        fileClassID: 8,
        fileClassSub: classSub,
        useDescription: "1||Table",
        newSourceFlag: true,
      };
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this[headerList] = res.data;
        }
      });
    },
    /**
     * @description: 主记录筛选改变
     * @param showHistory 是否显示结束记录
     * @return
     */
    radioChange(showHistory) {
      this.fixTable();
      this.recordTableShowHeader = showHistory
        ? [...this.recordTableHeaderList, ...this.recordTableEndHeaderList]
        : this.recordTableHeaderList;
      this.records = showHistory ? this.endRecords : this.unEndRecords;
    },
    /**
     * @description: 弹窗保存按钮点击
     * @return
     */
    async saveRescue() {
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      this.recordsCodeInfo.recordsCode.includes("Start") && (await this.saveRecord());
      this.recordsCodeInfo.recordsCode.includes("Maintain") && (await this.saveCareMain());
      this.recordsCodeInfo.recordsCode.includes("End") && (await this.saveEnd());
      this.drawerLoading = false;
      this.drawerLoadingText = "";
    },

    /*-------------主记录CRUD-------------*/

    /**
     * @description: 获取主记录数据
     * @return
     */
    async getRecordTableData() {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      this.loading = true;
      //获取病人主记录列表
      await GetPatientRescueRecords(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.recordTableData = result.data;
          this.endRecords = [];
          this.unEndRecords = [];
          this.recordTableData.forEach((record) => (record.endTime ? this.endRecords : this.unEndRecords).push(record));
          this.records = this.showHistoryRescue ? this.endRecords : this.unEndRecords;
          this.$nextTick(() => this.$refs.recordTable?.doLayout());
        }
      });
    },
    /**
     * @description: 主记录新增修改
     * @param item 要保存的数据
     * @return
     */
    async recordAdd(item) {
      this.recordsCode = "RescueStart";
      this.drawerToggle(true, "抢救记录");
      this.drugState = false;
      if (item) {
        //权限检核
        await this.checkAuthor(item.patientRescueRecordID, "PatientRescueRecord", item.userID);
        if (this.refillFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            item.addEmployeeID
          );
          this.showEditButton = saveButtonFlag;
        }
        this.isAdd = false;
        this.careMainID = item.patientRescueCareMainID;
        this.assessDate = this._datetimeUtil.formatDate(item.startTime, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.startTime, "hh:mm");
        this.stationID = item.stationID;
        this.departmentListID = item.departmentListID;
        this.recordID = item.patientRescueRecordID;
        this.userID = item.userID;
      } else {
        this.showEditButton = true;
        this.isAdd = true;
        this.careMainID = "temp_" + this._common.guid();
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.userID = this.user.userID;
        this.recordID = undefined;
      }
      this.$set(this.handOverArr, 1, item?.bringToShift ?? this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, !!item?.informPhysician);
      this.$set(this.bringToNursingRecordArr, 1, item?.bringToNursingRecord ?? this.settingNursingRecord);
      await this.getAssessTemplate();
    },
    /**
     * @description: 主记录保存
     * @return
     */
    async saveRecord() {
      // 数据验证
      if (!this.saveCheck()) {
        return;
      }
      let saveDataView = this.createRecordSaveView();
      let request = this.isAdd ? AddRescueRecord(saveDataView) : UpdateRescueRecord(saveDataView);

      await request.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.isAdd ? "新增" : "修改"}成功！`);
        }
      });
      this.drawerToggle(false);
      this.fixTable();
      this.getRecordTableData();
    },
    /**
     * @description: 创建主记录保存请求View
     * @return
     */
    createRecordSaveView() {
      let saveData = {
        Details: this.getDetails(),
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        NursingLevel: this.patient.nursingLevelCode,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        NumberOfAssessment: 1,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        RefillFlag: this.refillFlag,
        Record: {
          patientRescueRecordID: this.recordID,
          InpatientID: this.patient.inpatientID,
          PatientID: this.patient.patientID,
          BedID: this.patient.bedID,
          BedNumber: this.patient.bedNumber,
          CaseNumber: this.patient.caseNumber,
          ChartNo: this.patient.chartNo,
          StartTime: this.assessDate + " " + this.assessTime,
          StationID: this.stationID,
          DepartmentListID: this.departmentListID,
          AddEmployeeID: this.userID,
        },
      };
      return saveData;
    },
    /**
     * @description: 保存检核
     * @return
     */
    saveCheck() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return false;
      }
      if (!this.departmentListID) {
        this._showTip("warning", "请选择科室");
        return false;
      }
      if (this.assessData.length === 0) {
        this._showTip("warning", "请选择或填写相关项目！");
        return false;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return false;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return false;
      }
      return true;
    },
    /**
     * @description: 主记录停止
     * @param recordRow 当前主记录行数据
     * @return
     */
    async recordEnd(recordRow) {
      //权限检核
      await this.checkAuthor(recordRow.patientRescueRecordID, "PatientRescueRecord", recordRow.userID);
      if (!this.showEditButton) {
        return;
      }
      this.isAdd = true;
      this.drugState = false;
      this.currentRecord = recordRow;
      this.recordsCode = "RescueEnd";
      this.recordID = recordRow.patientRescueRecordID;
      this.careMainID = "temp_" + this._common.guid();
      this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
      this.stationID = this.patient.stationID;
      this.departmentListID = this.patient.departmentListID;
      this.bedID = this.patient.bedID;
      this.bedNumber = this.patient.bedNumber;

      this.$set(this.handOverArr, 1, this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, this.settingNursingRecord);
      this.drawerToggle(true, "抢救停止");
      await this.getAssessTemplate();
    },
    /**
     * @description: 主记录停止保存
     * @return
     */
    async saveEnd() {
      if (!this.saveCheck()) {
        return;
      }
      let saveData = {
        RecordID: this.recordID,
        InpatientID: this.patient.inpatientID,
        PatientID: this.patient.patientID,
        CaseNumber: this.patient.caseNumber,
        ChartNo: this.patient.chartNo,
        BedID: this.bedID,
        BedNumber: this.bedNumber,
        StationID: this.stationID,
        DepartmentListID: this.departmentListID,
        NursingLevel: this.patient.nursingLevelCode,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        CareMainID: this.careMainID,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        RefillFlag: this.refillFlag,
        Details: this.getDetails(),
        SourceID: this.sourceID,
        SourceType: this.sourceType,
      };

      let request = this.isAdd ? StopRescue(saveData) : UpdateRescueCareMain(saveData);
      await request.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.isAdd ? "停止" : "修改"}成功！`);
          this.fixTable();
          this.getRecordTableData();
        }
        this.drawerToggle(false);
      });
    },
    /**
     * @description: 主记录删除
     * @param row 当前行数据
     * @return
     */
    async deleteRecord(row) {
      //权限检核
      await this.checkAuthor(row.patientRescueRecordID, "PatientRescueRecord", row.userID);
      if (!this.showEditButton) {
        return;
      }
      if (this.refillFlag && this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.addEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            RecordID: row.patientRescueRecordID,
          };
          this.loading = true;
          DeleteRescueRecord(params).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              this.fixTable();
              this._showTip("success", "删除成功！");
              this.getRecordTableData();
            }
          });
        }
      });
    },

    /*-------------维护记录CRUD-------------*/
    /**
     * @description: 点击选中主记录，展示维护记录
     * @param row 当前行数据
     * @return
     */
    async recordClick(row) {
      this.currentRecord = row;
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.records = [row];
        this.careMainAddFlag = !row.endTime;
        this.getCareMainTableData();
      } else {
        this.getRecordTableData();
      }
    },
    /**
     * @description: 根据RecordID获取维护记录
     * @return
     */
    async getCareMainTableData() {
      let params = {
        recordID: this.currentRecord.patientRescueRecordID,
      };
      this.loading = true;
      await GetCareMainsByRecordID(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.careMainTableData = result.data;
          this.$nextTick(() => this.$refs.maintainTable?.doLayout());
        }
      });
    },
    /**
     * @description: 维护记录新增修改
     * @param item 当前行数据
     * @return
     */
    async careMainShow(item) {
      this.drawerToggle(true, "抢救维护");
      this.recordID = this.currentRecord.patientRescueRecordID;
      this.getDrug();
      if (item) {
        this.drugState = item.recordsCode === "RescueMaintain";
        this.rescueDrugFlag && this.initOrderDrugList(item.drug);
        //权限检核
        await this.checkAuthor(item.patientRescueCareMainID, "PatientRescueCareMain", item.userID);
        if (this.refillFlag === "*") {
          let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
            this.user.userID,
            false,
            true,
            item.addEmployeeID
          );
          this.showEditButton = saveButtonFlag;
        }
        this.isAdd = false;
        this.careMainID = item.patientRescueCareMainID;
        this.assessDate = this._datetimeUtil.formatDate(item.assessDate, "yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.formatDate(item.assessTime, "hh:mm");
        this.stationID = item.stationID;
        this.departmentListID = item.departmentListID;
        this.recordsCode = item.recordsCode;
        this.userID = item.userID;
      } else {
        this.drugState = true;
        this.showEditButton = true;
        this.isAdd = true;
        this.careMainID = "temp_" + this._common.guid();
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowDate("hh:mm");
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.bedID = this.patient.bedID;
        this.bedNumber = this.patient.bedNumber;
        this.recordsCode = "RescueMaintain";
      }
      this.$set(this.informPhysicianArr, 1, !!item?.informPhysician);
      this.$set(this.handOverArr, 1, item?.bringToShift ?? this.settingHandOver);
      this.$set(this.bringToNursingRecordArr, 1, item?.bringToNursingRecord ?? this.settingNursingRecord);
      await this.getAssessTemplate();
      // #region 宏力专属逻辑
      item &&
        this.getMedication({
          careMainID: item.patientRescueCareMainID,
        });
      // 若新增首条维护记录，默认加一行使用药物
      !item && this.careMainTableData.length === 1 && this.$refs.settingDrug?.addRow();
      // 若最近一条评估也是例行评估，将其填写的药物带入本次
      !item &&
        this.careMainTableData.length > 1 &&
        this.getMedication(
          {
            careMainID: this.careMainTableData[0].patientRescueCareMainID,
          },
          true
        );
      //#endregion
    },
    /**
     * @description: 维护记录保存
     * @return
     */
    async saveCareMain() {
      if (!this.saveCheck()) {
        return;
      }
      let saveData = this.createCareMainSaveView();
      let request = this.isAdd ? AddRescueCareMain(saveData) : UpdateRescueCareMain(saveData);

      await request.then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", `${this.isAdd ? "新增" : "修改"}成功！`);
          this.getCareMainTableData();
        }
        this.drawerToggle(false);
      });
    },
    /**
     * @description: 维护记录保存model
     * @return
     */
    createCareMainSaveView() {
      const drugContent = this.rescueDrugFlag ? this.orderDrugHandle() : this.settingDrugHandle();
      let saveData = {
        RecordID: this.currentRecord.patientRescueRecordID,
        InpatientID: this.patient.inpatientID,
        CaseNumber: this.patient.caseNumber,
        ChartNo: this.patient.chartNo,
        PatientID: this.patient.patientID,
        CareMainID: this.careMainID,
        BedID: this.bedID,
        BedNumber: this.bedNumber,
        StationID: this.stationID,
        DepartmentListID: this.departmentListID,
        NursingLevel: this.patient.nursingLevelCode,
        AssessDate: this.assessDate,
        AssessTime: this.assessTime,
        InterventionMainID: this.recordsCodeInfo.interventionMainID,
        RecordsCode: this.recordsCodeInfo.recordsCode,
        BringToShift: this.handOverArr[1],
        InformPhysician: this.informPhysicianArr[1],
        BringToNursingRecord: this.bringToNursingRecordArr[1],
        RefillFlag: this.refillFlag,
        SourceID: this.sourceID,
        SourceType: this.sourceType,
        Details: this.getDetails(drugContent),
        patientRescueMedicationList: this.medicationList,
        drug: drugContent,
      };
      return saveData;
    },
    /**
     * @description: 维护记录删除
     * @param row 删除行
     * @return
     */
    async deleteCareMain(row) {
      // 是否仅本人操作
      await this.checkAuthor(row.patientRescueCareMainID, "PatientRescueCareMain", row.addEmployeeID);
      if (this.refillFlag && this.refillFlag === "*") {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          true,
          row.addEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可删除");
          return;
        }
      }
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (!flag) {
          return;
        }
        let param = {
          CareMainID: row.patientRescueCareMainID,
        };
        this.loading = true;
        DeleteRescueCareMain(param).then((result) => {
          this.loading = false;
          if (this._common.isSuccess(result)) {
            this._showTip("success", "删除成功！");
            if (row.recordsCode.includes("End")) {
              this.fixTable();
              this.getRecordTableData();
            } else {
              this.getCareMainTableData();
            }
          }
        });
      });
    },
    /**
     * @description: 重置选中状态
     * @return
     */
    fixTable() {
      this.showRecordArr = [true, false];
      this.currentRecord = undefined;
      this.careMainTableData = [];
    },
    /**
     * @description: 获取评估模板
     * @return
     */
    async getAssessTemplate() {
      this.drawerLoadingText = "加载中……";
      this.drawerLoading = true;
      let params = {
        mappingType: this.recordsCode,
        inpatientID: this.patient.inpatientID,
        departmentListID: this.patient.departmentListID,
        age: this.patient.age,
      };
      await GetAssessRecordsCodeByDeptID(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo?.recordsCode) {
        this.drawerToggle(false);
        this._showTip("warning", "找不到评估模板！");
        return;
      }
      //获取观察措施模板
      if (this.recordsCodeInfo.recordsCode.indexOf("Maintain") == -1) {
        await this.GetTemplate();
      }
      params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
        recordID: this.recordID,
        careMainID: this.careMainID,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        dateOfBirth: this.patient.dateOfBirth,
        inpatientID: this.patient.inpatientID,
      };
      this.templateData = [];
      await GetRescueAssessView(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.templateData = result.data;
        }
      });
      this.drawerLoading = false;
    },
    /**
     * @description: 评估组件按钮事件
     * @param content 跳转明细项
     * @return
     */
    buttonClick(content) {
      this.showButtonDialog = true;
      this.buttonName = content.itemName;
      this.buttonAssessListID = content.assessListID;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        `&sourceID=${this.getCareMainID()}` +
        `&sourceType=Rescue_${content.assessListID}` +
        "&isDialog=true";
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => this.$refs.buttonDialog.contentWindow.location.replace(url));
    },
    /**
     * @description: 添加完更新按钮数量
     * @return
     */
    async updateButton() {
      let item = await this.getButtonValue(this.buttonAssessListID);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    /**
     * @description: 更新按钮数量API
     * @param assessListID 字典ID
     * @return
     */
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessListID: assessListID,
        sourceID: this.getCareMainID(),
        sourceType: `Rescue_${assessListID}`,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    /**
     * @description: 获取维护记录CareMainID，若为新增则截取掉头部
     * @return
     */
    getCareMainID() {
      return this.careMainID?.split("_")?.[1] ?? this.careMainID;
    },
    /*-------------获取页面配置-------------*/
    /**
     * @description: 获取是否带入交班配置
     * @return
     */
    getBringHandOverSetting() {
      let params = {
        special: "Rescue",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingHandOver = res.data;
        }
      });
    },
    /**
     * @description: 获取是否带入护理记录配置
     * @return
     */
    getBringToNursingRecordSetting() {
      let params = {
        settingTypeCode: "RescueAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },

    /*-------------配合保存方法-------------*/
    /**
     * @description: 获取保存明细
     * @return
     */
    getDetails(drugContent) {
      let details = [];
      this.assessData.forEach((content) => {
        const detail = {
          assessListID: content.assessListID,
          assessListGroupID: content.assessListGroupID == 0 ? content.groupID : content.assessListGroupID,
          assessValue:
            content.controlerType.trim() == "C" || content.controlerType.trim() == "R" ? "" : content.assessValue,
          specialListType: content.specialListType,
          controlerType: content.controlerType,
        };
        content.disableGroup != -1 && details.push(detail);
      });
      // 补充使用药物明细
      details.push({
        assessListID: 1877,
        assessListGroupID: 1877,
        assessValue: drugContent,
      });
      return details;
    },
    /**
     * @description: 权限检核
     * @param id 主记录/主表ID
     * @param tableName 表名
     * @param userID 工号
     * @return
     */
    async checkAuthor(id, tableName, userID) {
      this.showEditButton = await this._common.checkActionAuthorization(this.user, userID);
      if (!this.showEditButton) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, tableName, !!this.refillFlag);
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
    },
    /*-------------专项护理组件逻辑-------------*/
    /**
     * @description: 弹窗关闭，重置参数
     * @return
     */
    drawerClose() {
      this.recordID = undefined;
      this.careMainID = undefined;
      this.templateData = [];
      this.assessData = [];
      this.showTemplateFlag = false;
      this.drugTableData = [];
    },
    /**
     * @description: 弹窗开关
     * @param flag 开关动作
     * @param title 弹窗标题
     * @return
     */
    drawerToggle(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
      !flag && (this.drugTableData = []);
    },
    /**
     * @description: 接收子组件回传的变量，更新当前组件变量值
     * @param propValue 回传的变量值
     * @param propName 待改变成员名
     * @param arrayIndex 待接收变量是数组，指定数组下标，默认不传则直接赋值
     * @return
     */
    receiveEmit(propValue, propName, arrayIndex = undefined) {
      if (arrayIndex) {
        this[propName][arrayIndex] = propValue;
      } else {
        this[propName] = propValue;
      }
    },
    /**
     * @description: 获取使用药物数据源配置
     * @return
     */
    initRescueDrug() {
      let params = {
        settingTypeCode: "RescueDrugSwitch",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.rescueDrugFlag = res.data;
        }
      });
    },
    /**
     * @description: 病人药物回显
     * @param params 请求参数
     * @param historyAutoFill 是否是自动填充历史用药
     * @return
     */
    getMedication(params, historyAutoFill = false) {
      GetRescueMedications(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.length) {
            if (historyAutoFill) {
              // 是自动带入的上一次用药数据，不应携带id
              res.data.forEach((drug) => {
                drug.id = 0;
              });
            }
            this.$set(this, "drugTableData", res.data);
            this.$forceUpdate();
          } else {
            // 宏力专属逻辑，若没有历史使用药物记录，则自动新增一行
            this.$refs.settingDrug?.addRow();
          }
        }
      });
    },
    /**
     * @description: 获取药物列表，医嘱 / ClinicalSetting
     * @return
     */
    getDrug() {
      if (this.channelList.length && this.solventList.length && this.drugList.length) {
        return;
      }
      if (this.rescueDrugFlag) {
        let params = {
          inpatientID: this.patient.inpatientID,
          isSupplement: this.refillFlag === "*",
        };
        GetOrderDrugUseRescue(params).then((res) => {
          if (this._common.isSuccess(res)) {
            this.orderDrugOptions = res.data;
          }
        });
      } else {
        const requestKv = {
          CPRRoute: (data) => (this.channelList = data),
          CPRMedicationSolvent: (data) => (this.solventList = data),
          CPRMedication: (data) => (this.drugList = data),
        };
        for (const [key, value] of Object.entries(requestKv)) {
          const params = {
            settingTypeCode: key,
          };
          GetClinicalSettingInfo(params).then((res) => {
            if (this._common.isSuccess(res)) {
              res.data.unshift([{ clinicSettingID: 0 }, { description: "" }]);
              value(res.data);
            }
          });
        }
      }
    },
    /**
     * @description: 配置药物数据处理
     * @return
     */
    settingDrugHandle() {
      this.medicationList = [];
      let drugContent = "";
      // 只要有药品ID的数据
      this.drugTableData
        .filter((drug) => drug.medicationID)
        .forEach((drug) => {
          const rescueMedication = {
            id: drug.id,
            medicationID: drug.medicationID ? drug.medicationID : undefined,
            medicationDose: drug.medicationDose ? drug.medicationDose : undefined,
            solventID: drug.solventID,
            solventDose: drug.solventDose,
            routeID: drug.routeID,
            speed: drug.routeSpeed,
          };
          this.medicationList.push(rescueMedication);
          // 药品名
          const drugNameClinicalSetting = this.drugList.find(
            (drugName) => drug.medicationID == drugName.clinicSettingID && drug.medicationID
          );
          drugContent += drugNameClinicalSetting?.description ?? "";
          // 药品剂量
          drug.medicationDose && (drugContent += `(${drug.medicationDose}毫克),`);
          // 溶剂
          const solventClinicalSetting = this.solventList.find(
            (solvent) => drug.solventID == solvent.clinicSettingID && drug.solventID
          );
          drugContent += solventClinicalSetting?.description ?? "";
          // 剂量
          drug.solventDose && (drugContent += `(${drug.solventDose}毫升),`);
          // 途径
          const channelClinicalSetting = this.channelList.find(
            (channel) => drug.routeID == channel.clinicSettingID && drug.routeID
          );
          drugContent += channelClinicalSetting?.description ?? "";
          // 速度
          drug.routeSpeed && (drugContent += `(${this.inputRescueLabel}${drug.routeSpeed})；`);
        });
      return drugContent;
    },
    /**
     * @description: 医嘱药物数据处理
     * @return
     */
    orderDrugHandle() {
      if (!this.drugTableData || !this.drugTableData.length) {
        return "";
      }
      let drugContent = "";
      this.drugTableData.forEach((drug) => {
        if (drug.orderDrug) {
          let content = drug.orderDrug;
          drug.inputRescue && (content += `(${this.inputRescueLabel}${drug.inputRescue})`);
          drugContent && (drugContent += ";");
          drugContent += content;
        }
      });
      return drugContent;
    },
    /**
     * @description: 初始化医嘱药物回显
     * @param careMainDrug
     * @return
     */
    initOrderDrugList(careMainDrug) {
      this.drugTableData = [];
      if (!careMainDrug) {
        return;
      }
      var drugList = careMainDrug.split(";");
      drugList.forEach((drug) => {
        if (drug.indexOf(this.inputRescueLabel) == -1) {
          this.drugTableData.push({
            orderGroupNo: "",
            orderDrug: drug,
            inputRescue: "",
          });
        } else {
          const order = drug.split(this.inputRescueLabel);
          this.drugTableData.push({
            orderGroupNo: "",
            orderDrug: order[0].substring(0, order[0].length - 1),
            inputRescue: order[1].substring(0, order[1].length - 1),
          });
        }
      });
    },
    /**
     * description: 获取观察措施模板
     * param {*}
     * return {*}
     */
    async GetTemplate() {
      let params = {
        recordsCode: this.recordsCodeInfo.recordsCode,
      };
      await GetObserveTemplate(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.observeTemplate = res.data;
        }
      });
    },
    /**
     * description: 返回评估模板勾选项
     * param {*} data
     * return {*}
     */
    changeItem(data) {
      if (data?.assessListID == this.observeTemplateAssessListID || !data) {
        return;
      }
      this.$nextTick(() => {
        this.fixObserveTemplate();
      });
    },
    /**
     * description: 调整观察措施默认模板
     * param {*}
     * return {*}
     */
    fixObserveTemplate() {
      //维护数据
      if (this.recordsCodeInfo.recordsCode.indexOf("Maintain") != -1) {
        return;
      }
      if (!this.assessData.length || !this.templateData.length) {
        return;
      }
      let template = this._common.clone(this.observeTemplate);
      if (!template) {
        return;
      }
      if (this.recordsCodeInfo.recordsCode.indexOf("Start") >= 0) {
        const conditionParts = [];
        Object.keys(this.patientConditionAssessListID).forEach((key) => {
          const sucAssessData = this.assessData.find(
            (item) => item.assessListID === this.patientConditionAssessListID[key]
          );
          if (sucAssessData?.itemName) {
            conditionParts.push(sucAssessData.itemName);
          }
        });
        if (conditionParts.length > 0) {
          template = template.replace("@患者状况@", conditionParts.join("、"));
        }
      }
      if (this.recordsCodeInfo.recordsCode.indexOf("End") >= 0) {
        Object.keys(this.templateToAssessListID).forEach((key) => {
          if (template.indexOf(this.templateToAssessListID[key]) != -1) {
            let sucAssessData = this.assessData.find((item) => item.assessListID == key);
            if (sucAssessData) {
              if (sucAssessData.controlerType.trim() == "C" || sucAssessData.controlerType.trim() == "R") {
                template = template.replace(this.templateToAssessListID[key], sucAssessData.itemName);
              } else {
                template = template.replace(this.templateToAssessListID[key], sucAssessData.assessValue);
              }
            }
          }
        });
      }
      //更新评估模板
      this.templateData.forEach((bookMark) => {
        bookMark.groups.forEach((item) => {
          if (item.assessListID == this.observeTemplateAssessListID) {
            this.$set(item, "assessValue", template);
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" >
.rescue-record {
  .title {
    display: flex;
    flex-direction: row;
  }
  .drawer-content {
    .base-header {
      .date-picker {
        width: 120px;
      }
      .time-picker {
        width: 80px;
      }
    }
    .base-layout-content {
      overflow: hidden;
      height: 100%;
      .drug-soan {
        color: #000000;
        text-align: right;
        font-weight: bold;
        font-size: 14px;
      }
    }
  }
}
</style>