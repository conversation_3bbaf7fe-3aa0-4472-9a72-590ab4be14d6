<!--
 * FilePath     : \src\autoPages\patientList\components\markFilter.vue
 * Author       : 苏军志
 * Date         : 2025-01-16 08:34
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-13 19:17
 * Description  : 患者标记过滤
 * CodeIterationRecord: 
 -->
<template>
  <div class="mark-filter">
    <template v-if="showGroup">
      <div class="mark-group-wrap" v-for="(groupFilterList, groupKey) in filterList" :key="groupKey">
        <div
          v-if="groupFilterList.length == 1"
          :class="['mark-group', { active: groupFilterList[0].isCheck }]"
          @click="filterData(groupFilterList[0])"
        >
          {{ groupFilterList[0].style.remark + ": " + groupFilterList[0].identifyIDCount + "人" }}
        </div>
        <template v-else>
          <div class="mark-group" @click="toggleGroup(groupKey)">
            {{ groupMap ? groupMap[groupKey] : groupKey }}
          </div>
          <template v-if="opemMarkGroups.includes(groupKey)">
            <div
              :class="['filter-item', { active: item.isCheck }]"
              v-for="(item, index) in groupFilterList"
              :key="index"
              @click="filterData(item)"
            >
              {{ item.style.remark + ": " + item.identifyIDCount + "人" }}
            </div>
            <i class="iconfont icon-back close-group" @click="toggleGroup(groupKey)"></i>
          </template>
        </template>
      </div>
    </template>
    <template v-else>
      <div
        v-for="(item, index) in filterList"
        :key="index"
        :class="['no-group-mark', { selected: item.isCheck }]"
        :style="getMarkCss(item.style, item.isCheck)"
        @click="filterData(item)"
      >
        {{ item.style.remark + ": " + item.identifyIDCount + "人" }}
      </div>
    </template>
  </div>
</template>
<script>
export default {
  props: {
    filterList: {
      type: Object | Array,
      required: true,
    },
    showGroup: {
      type: Boolean,
      default: false,
    },
    groupMap: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 打开的分组
      opemMarkGroups: [],
    };
  },
  methods: {
    /**
     * description: 获取标记样式
     * param {*} style
     * param {*} isCheck
     * return {*}
     */
    getMarkCss(style, isCheck) {
      let result = {
        backgroundColor: style.backGroundColor,
        color: style.iconColor,
      };
      // 三级护理特殊处理 使用护理级别名称进行判断
      if (style.backGroundColor.trim() == "#ffffff") {
        if (isCheck) {
          result.color = "#ffffff";
        } else {
          result.color = "#000000";
        }
      }
      return result;
    },
    /**
     * @description: 打开/关闭组
     * @param groupKey
     * @return
     */
    toggleGroup(groupKey) {
      if (this.opemMarkGroups.includes(groupKey)) {
        this.opemMarkGroups = this.opemMarkGroups.filter((group) => group != groupKey);
      } else {
        this.opemMarkGroups.push(groupKey);
      }
    },
    /**
     * @description: 过滤数据，抛出事件
     * @param filter
     * @return
     */
    filterData(filter) {
      this.$emit("filter-data", filter);
    },
  },
};
</script>
<style lang="scss">
.mark-filter {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;
  .mark-group-wrap {
    height: 30px;
    display: flex;
    align-items: center;
    margin: 4px;
    padding: 2px 0;
    border-radius: 13px;
    box-sizing: border-box;
    background-color: #eeecec;
    .mark-group {
      height: 20px;
      line-height: 15px;
      margin: 0 6px;
      color: #118d74;
      font-weight: bold;
      font-size: 12px;
      cursor: pointer;
      border: 1px solid transparent;
      border-radius: 8px;
      padding: 1px 6px;
      box-sizing: border-box;
      &.active {
        background-color: #ffffff;
        border-color: #ff7400;
        color: #ff7400;
      }
    }
    .filter-item {
      height: 22px;
      line-height: 18px;
      background-color: #ffffff;
      border-radius: 8px;
      margin: 0 3px;
      padding: 1px 5px;
      box-sizing: border-box;
      font-size: 12px;
      cursor: pointer;
      border: 1px solid transparent;
      &.active {
        border-color: #ff7400;
        color: #ff7400;
        font-weight: bold;
      }
    }
    .close-group {
      margin-right: 8px;
      color: #ff7400;
      font-weight: bold;
    }
  }
  .no-group-mark {
    padding: 1px 4px;
    font-size: 12px;
    margin: 3px;
    border-radius: 6px;
    color: #ffffff;
    border: 2px solid transparent;
    box-sizing: border-box;
    cursor: pointer;
    height: 20px;
    line-height: 14px;
    &.selected {
      border-color: #ffffff;
      box-shadow: 2px 5px 4px 1px rgba(0, 0, 0, 0.3);
      background-color: #118d74 !important;
    }
  }
}
</style>