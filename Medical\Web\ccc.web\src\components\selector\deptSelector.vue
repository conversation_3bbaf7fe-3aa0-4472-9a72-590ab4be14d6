<!--
 * FilePath     : \src\components\selector\deptSelector.vue
 * Author       : 苏军志
 * Date         : 2019-11-20 20:54
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-06-30 11:31
 * Description  : 科室下拉选择组件
 * CodeIterationRecord: 2022-09-24 改为从缓存获取科室列表 -杨欣欣
-->

<template>
  <div class="dept-selector">
    <span :class="{ label: label }">{{ label }}</span>
    <el-select
      :disabled="disabled"
      v-model="selected"
      placeholder="请选择科室"
      @change="changeValue"
      :clearable="clearable"
      :style="style"
    >
      <el-option v-for="(dept, index) in deptList" :key="index" :label="dept.value" :value="dept.id"></el-option>
    </el-select>
  </div>
</template>

<script>
import { GetDeptList } from "@/api/Department";
import { mapGetters } from "vuex";
export default {
  props: {
    label: {
      type: String,
      default: "科室：",
    },
    value: {
      type: Number,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    stationID: {
      //type: Number,
    },
    width: {
      type: String,
      default: "160px",
    },
    defaultDepartmentList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  watch: {
    stationID: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          if (!this.disabled && this.selected) {
            this.$emit("input", undefined);
          }
          this.init();
        }
      },
    },
    value: {
      immediate: true,
      handler(newVal) {
        this.selected = newVal;
      },
    },
    localDeptList() {
      // 页面有多个此组件时，后面组件请求会被拦截，需要监听缓存手动放入List
      if (!this.deptList.length) {
        if (this.stationID) {
          this.filterByStation();
          return;
        }
        this.deptList = this.localDeptList;
      }
    },
  },
  computed: {
    ...mapGetters({
      // 全部的科室列表
      localDeptList: "getDeptList",
    }),
    style() {
      return {
        width: this._convertUtil.getHeigt(this.width, true),
      };
    },
  },
  data() {
    return {
      selected: "",
      // 显示的科室列表
      deptList: [],
    };
  },
  methods: {
    init() {
      if (this.defaultDepartmentList.length) {
        this.deptList = this.defaultDepartmentList;
        return;
      }
      // 有缓存
      if (this.localDeptList && this.localDeptList.length > 0) {
        // 过滤病区关联科室
        if (this.stationID) {
          this.filterByStation();
          return;
        }
        // 没传科室，展示全部
        this.deptList = this.localDeptList;
      }
      // 无缓存
      GetDeptList().then((result) => {
        if (this._common.isSuccess(result)) {
          if (this.stationID) {
            this.filterByStation(result.data);
          }
          // 将全部科室列表缓存起来
          this.$store.commit("session/setDeptList", result.data);
        }
      });
    },
    changeValue(deptID) {
      if (!deptID) {
        this.$emit("input", undefined);
        this.$emit("change", undefined);
        return;
      }
      // 实现双向绑定
      this.$emit("input", deptID);
      this.$emit("select", deptID);
      let dept = this.deptList.find((dept) => {
        return dept.id == deptID;
      });
      if (dept) {
        this.$emit("select-item", dept);
        this.$emit("change", undefined);
      }
    },
    /**
     * description: 根据病区进行过滤
     * param {*}
     * return {*}
     */
    filterByStation(deptList) {
      if (deptList) {
        deptList.filter((deptView) => deptView.stationIDList.includes(this.stationID));
        return;
      }
      // 从全部的科室列表，过滤出需要的科室
      this.deptList = this.localDeptList.filter((deptView) => deptView.stationIDList.includes(this.stationID));
    },
  },
};
</script>
<style lang="scss">
.dept-selector {
  display: inline-block;
  .label {
    margin-left: 5px;
  }
  .el-select {
    .el-input.is-disabled {
      .el-input__inner {
        color: #606266;
      }
    }
    .el-input__inner {
      padding-left: 5px;
    }
  }
}
</style>
