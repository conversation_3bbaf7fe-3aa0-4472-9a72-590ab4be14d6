/*
 * FilePath     : \src\main.js
 * Author       : 苏军志
 * Date         : 2020-05-14 16:55
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-06-17 19:15
 * Description  : main.js
 */
import "@/utils/arraySort";
import broadcast from "@/utils/broadcast"; // 自定义属性
import common from "@/utils/common";
import convertUtil from "@/utils/convertUtil";
import datetimeUtil from "@/utils/datetimeUtil";
import decimalUtil from "@/utils/decimalUtil";
import "@/utils/bindingDirection";
import "@/utils/directives"; // 自定义指令
import "@/utils/flexible"; // 自适应
import "@/utils/message"; // 自定义指令
import navigation from "@/utils/navigation"; // 导航卫士
import "@/utils/regular";
import { initConfig } from "@/utils/setting";
import "@/utils/sortable";
import "@/utils/stomp.js"; // mq消息客户端
import "@/utils/toast"; // 自定义指令
import ElementUI from "element-ui";
import VCharts from "v-charts"; //引入V-charts
import Vue from "vue";
import "../static/iconfont/iconfont.css";
import router from "./router"; // 路由
import store from "./store"; // vuex状态管理
// 引入右键快捷菜单组件
import contentmenu from "v-contextmenu";
import "v-contextmenu/dist/index.css";
// 引入'umy-ui解决大量数据卡死
import { LoadHuiMeiJssdk } from "@/utils/huiMei";
import UmyUi from "umy-ui";
import "umy-ui/lib/theme-chalk/index.css"; // 引入样式
import Direction from "@/utils/direction";
import "../static/scss/index.scss";
// 引入i18n模块
import i18n from "@/utils/i18n/index";
import App from "./App";
//es6转码--解决低版本谷歌某些ES6新特性报错
import "babel-polyfill";
// 引入自定义按钮组件
import buttons from "@/components/button/index.js";

Vue.config.productionTip = false;
// 初始化配置
initConfig();
//初始化请求API集合
window._apiRequestList = [];
// 设置页面跳转规则
navigation.set(router, store, Vue);
// element ui 所有组件尺寸设置为small
Vue.use(ElementUI, {
  size: "small",
  i18n: (key, value) => i18n.t(key, value)
});
Vue.use(UmyUi);
Vue.use(Direction);
Vue.use(contentmenu);
Vue.use(VCharts);
// 自定义按钮组件 挂载全局
Vue.use(buttons);

//挂载自定义属性
Vue.prototype._sendBroadcast = broadcast.sendBroadcast;
Vue.prototype._receiveBroadcast = broadcast.receiveBroadcast;
Vue.prototype._common = common;
Vue.prototype._datetimeUtil = datetimeUtil;
Vue.prototype._decimalUtil = decimalUtil;
Vue.prototype._convertUtil = convertUtil;

//根据配置加载惠每jssdk
LoadHuiMeiJssdk();

new Vue({
  el: "#app",
  i18n,
  router,
  store,
  components: {
    App
  },
  template: "<App/>"
});
