<!--
 * FilePath     : d:\Documents\CareDirect\Medical\Web\ccc.web\src\autoPages\medicationClosedLoop\index.vue
 * Author       : 杨欣欣
 * Date         : 2025-05-18 15:03
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-13 17:27
 * Description  : 药品闭环信息管理
 * CodeIterationRecord: 
 -->
<template>
  <base-layout class="medication-closed-loop" headerHeight="auto" element-loading-text="加载中……" v-loading="loading">
    <template slot="header">
      预计执行日期：
      <el-date-picker
        class="date-range-picker"
        v-model="dateTimeRange"
        type="daterange"
        :clearable="false"
        unlink-panels
        range-separator="至"
        @change="queryTableData"
      />
      床号：
      <el-input
        class="bed-number"
        v-model="bedNumber"
        @keyup.enter.native="queryTableData"
        @clear="queryTableData"
        clearable
        placeholder="请输入床号"
      >
        <i slot="append" class="iconfont icon-search" @click="queryTableData" />
      </el-input>
    </template>
    <div class="main">
      <el-table ref="medicationTable" class="medication-table" :data="tableData" border stripe height="100%">
        <el-table-column>
          <template slot="header" slot-scope="scope">
            <div class="drug-header">
              <span v-if="false">{{ scope }}</span>
              <div class="drug-content">医嘱内容</div>
              <div class="drug-dosage">剂量</div>
              <div class="drug-dose">用量</div>
              <div class="drug-spec">规格</div>
            </div>
          </template>
          <template slot-scope="{ row }">
            <div v-for="(drugView, index) in row.drugViews" :key="index" class="drug-row">
              <div class="drug-content">{{ drugView.content }}</div>
              <div class="drug-dosage">{{ drugView.dosage }}</div>
              <div class="drug-dose">{{ drugView.dosePerUnit || " " }}</div>
              <div class="drug-spec">{{ drugView.specification }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="途径" prop="orderRule" width="70"></el-table-column>
        <el-table-column label="频次" prop="frequency" width="88"></el-table-column>
        <el-table-column label="预计执行时间" width="142" align="center">
          <template slot-scope="{ row }">
            <span v-formatTime="{ value: row.scheduleDateTime, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="实际执行时间" width="142" align="center">
          <template slot-scope="{ row }">
            <span v-formatTime="{ value: row.performDateTime, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="备药时间" width="142" align="center">
          <template slot-scope="{ row }">
            <span v-formatTime="{ value: row.prepareTime, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="配药时间" width="142" align="center">
          <template slot-scope="{ row }">
            <span v-formatTime="{ value: row.dispensingTime, type: 'dateTime' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="撤销配药时间" width="142" align="center">
          <template slot-scope="{ row }">
            <span v-formatTime="{ value: row.revocationTime, type: 'dateTime' }" class="revocation-time"></span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="64">
          <template slot-scope="{ row }">
            <el-tooltip content="撤销" v-show="row.canRevocation">
              <div @click.stop="revocation(row)" class="iconfont icon-revocation"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import { GetMedications, RevocationMedication } from "@/api/medicationClosedLoop";
export default {
  components: { baseLayout },
  data() {
    return {
      loading: false,
      dateTimeRange: undefined,
      bedNumber: "",
      tableData: [],
      canRevocationStatusCodes: [],
    };
  },
  async mounted() {
    this.dateTimeRange = [
      this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -3, "yyyy-MM-dd"),
      this._datetimeUtil.getNowDate(),
    ];
    await this.queryTableData();
  },
  methods: {
    /**
     * @description: 查询药品闭环信息
     * @return
     */
    async queryTableData() {
      const params = {
        startDate: this._datetimeUtil.formatDate(this.dateTimeRange[0], "yyyy-MM-dd"),
        endDate: this._datetimeUtil.formatDate(this.dateTimeRange[1], "yyyy-MM-dd"),
        bedNumber: this.bedNumber,
      };
      this.loading = true;
      const result = await GetMedications(params);
      if (this._common.isSuccess(result)) {
        this.tableData = result.data;
        this.$nextTick(() => this.$refs.medicationTable?.doLayout())
      }
      this.loading = false;
    },
    /**
     * @description: 撤销药品
     * @param row 当前行数据
     * @return
     */
    async revocation(row) {
      this._deleteConfirm("确定要撤销这组药品？", async (flag) => {
        if (!flag) {
          return;
        }
        this.loading = true;
        const params = {
          inpatientID: row.inpatientID,
          groupID: row.groupID,
        };
        const result = await RevocationMedication(params);
        if (this._common.isSuccess(result)) {
          if (result.data) {
            this._showTip("success", "撤销成功");
          } else {
            this._showTip("error", `撤销失败，${result.message}`);
          }
          this.loading = false;
        }
        await this.queryTableData();
      });
    },
  },
};
</script>

<style lang="scss">
.medication-closed-loop {
  .date-range-picker {
    width: 240px;
    margin-right: 8px;
  }
  @at-root .el-date-range-picker__header .el-picker-panel__icon-btn {
    color: #303133;
    font-weight: inherit;
    margin-top: 8px;
    padding: 1px 6px;
  }
  .bed-number.el-input {
    width: 110px;
    .el-input__inner {
      padding: 0 5px;
    }
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .drug-info {
    display: flex;
    gap: 4px;
  }
  .main {
    height: 100%;
  }
  .drug-header,
  .drug-row {
    display: flex;
    > div {
      border-right: 1px solid #cccccc;
    }
    > div:last-child {
      border-right: none;
    }
  }
  .drug-header {
    padding: 0;
    align-items: center;
    width: 100%;
    > div {
      padding: 0;
      text-align: center;
    }
  }
  .drug-row {
    padding: 4px 0;
  }
  .drug-content {
    flex: 7;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .drug-dosage,
  .drug-dose {
    flex: 2;
    text-align: center;
  }
  .drug-spec {
    flex: 3;
    text-align: center;
  }
  .revocation-time {
    color: #ff0000;
    font-weight: bold;
  }
  .icon-revocation {
    color: #ff7400;
  }
  .hidden-operate {
    display: none;
  }
}
</style>
