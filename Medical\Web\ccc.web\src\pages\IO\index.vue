<!--
 * FilePath     : \src\pages\IO\index.vue
 * Author       : 李青原
 * Date         : 2020-03-27 18:05
 * LastEditors  : 来江禹
 * LastEditTime : 2023-06-10 16:54
 * Description  : 
 -->
<template>
  <div class="io-index">
    <el-tabs v-model="activeName" @tab-click="handleClick" v-if="!this.addOnly">
      <template v-for="(tabPane, index) in tabPanes">
        <el-tab-pane :key="index" :label="tabPane.label" :name="tabPane.name"></el-tab-pane>
      </template>
    </el-tabs>
    <div :class="['io-child', { 'show-tabs': this.addOnly }]">
      <router-view ref="childPage"></router-view>
    </div>
  </div>
</template>
<script>
export default {
  computed: {
    io() {
      return this.$t("io");
    },
  },
  data() {
    return {
      activeName: "",
      addOnly: false,
      tabPanes: [],
      query: {},
    };
  },
  updated() {
    if (this.activeName && this.tabPanes && this.tabPanes.length > 0 && this.$route.name == "io") {
      let tab = this.tabPanes.find((tab) => tab.name == this.activeName);
      if (tab) {
        this.handleClick(tab);
      }
    }
  },
  mounted() {
    //可能传递patientScheduleMainID
    this.query = this.$route.query;
    if (this.query.patientScheduleMainID) {
      this.addOnly = true;
    }
    this.tabPanes = [
      {
        name: "ioRecordMaintenance",
        label: this.io.ioRecord,
      },
      {
        name: "ioRecord",
        label: this.io.ioNote,
      },
      {
        name: "drainageStatistics",
        label: this.io.ioStatistics,
      },
      {
        name: "ioStatisticalChart",
        label: this.io.ioChart,
      },
      {
        name: "ioBalanceStatistics",
        label: this.io.ioBalance,
      },
      {
        name: "ioDrainageChart",
        label: this.io.ioDrainageChart,
      }
    ];
    let tab = this.tabPanes[0];
    this.activeName = tab.name;
    this.handleClick(tab);
  },

  methods: {
    // 系统顶部刷新按钮触发
    refreshData() {
      this.$nextTick(() => {
        if (this.$refs["childPage"] && this.$refs["childPage"].refreshData) {
          this.$refs["childPage"].refreshData();
        }
      });
    },
    handleClick(tab, event) {
      if (this.$route.name == tab.name) {
        return;
      }
      this.$router.replace({
        name: tab.name,
        query: this.query,
      });
    },
  },
};
</script>

<style lang="scss">
.io-index {
  height: 100%;
  .io-child {
    height: calc(100% - 34px);
    &.show-tabs {
      height: 100%;
    }
  }
}
</style>
