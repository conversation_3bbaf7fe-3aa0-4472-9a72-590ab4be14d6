<!--
 * FilePath     : /src/pages/tube/index.vue
 * Author       : 郭鹏超
 * Date         : 2021-08-17 14:47
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-06 18:43
 * Description  : 导管专项
 * CodeIterationRecord: 增加带入护理记录选项
                        2022-7-31 修复换管时记录人员会报错  -MChao
                        2022-12-01 2869-导管跳IO改版 -杨欣欣
                        2023-06-27 3587-护理评估按钮跳转专项录入的内容评估时间跟护理评估保持一致 -杨欣欣
-->
<template>
  <specific-care
    class="patient-tube"
    v-model="showTemplateFlag"
    :recordTitleSlotFalg="true"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :handOverFlag="handOverArr"
    :informPhysicianFlag="informPhysicianArr"
    :nursingRecordFlag="bringToNursingRecordArr"
    :editFlag="checkResult"
    :careMainAddFlag="tubeDataType == '1'"
    :previewFlag="saveFlag"
    @mainAdd="tubeRecordAddOrModify"
    @maintainAdd="tubeCareMainAddOrModify"
    @save="tubeSaveSelect"
    @getMainFlag="getMainFlag"
    @cancel="drawerClose"
    @getHandOverFlag="getHandOverFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @getNursingRecordFlag="getBringToNursingRecordFlag"
    :mainTableHeight="tableOneRowHeight"
    :drawerSize="supplementFlag ? '80%' : ''"
    v-loading="loading"
    element-loading-text="加载中……"
  >
    <!-- 主记录 -->
    <div slot="record-title">
      <el-radio-group v-model="tubeDataType" @change="clearType">
        <el-radio-button label="1">现有导管</el-radio-button>
        <el-radio-button label="2">历史导管</el-radio-button>
      </el-radio-group>
    </div>
    <div slot="main-record">
      <packaging-table
        v-model="recordTableData"
        @rowClick="tubeRecordClick"
        :headerList="tubeRecordColumnList"
        row-class-name="main-record-row"
        header-row-class-name="main-record-herder-row"
        ref="recordTable"
      >
        <!-- 评估类型 插槽 -->
        <div slot="sourceType" slot-scope="tube">
          <el-tooltip v-if="tube.row.sourceFlag" :content="tube.row.sourceContent" effect="light" placement="top">
            <div :class="['flag', tube.row.sourceFlag]">
              <span v-if="tube.row.sourceFlag == 'A'">护</span>
              <span v-if="tube.row.sourceFlag == 'H'">班</span>
              <span v-if="tube.row.sourceFlag == 'O'">术</span>
            </div>
          </el-tooltip>
        </div>
        <div slot="startDateTime" slot-scope="tube">
          <span v-formatTime="{ value: tube.row.startDate, type: 'date' }"></span>
          <span v-formatTime="{ value: tube.row.startTime, type: 'time' }"></span>
        </div>
        <div slot="risk" slot-scope="tube" :style="getLevelStyle(tube.row)">
          {{ tube.row.tubeRiskLevel }}
        </div>
        <div slot="catheterSituation" slot-scope="tube">
          <el-tooltip popper-class="tooltip-popper" placement="top">
            <div slot="content" v-html="overStrikingText(tube.row.catheterSituation)"></div>
            <div class="catheter-situation" v-html="overStrikingText(tube.row.catheterSituation)"></div>
          </el-tooltip>
        </div>
        <div slot="assessDateTime" slot-scope="tube">
          <span
            :class="tube.row.maintainFlag ? 'last-maintain-alter' : ''"
            v-formatTime="{
              value: tube.row.assessDateTime,
              type: 'datetime',
            }"
          ></span>
        </div>
        <div slot="expectedChangeDay" slot-scope="tube">
          <span :class="{ 'tube-remove-reminder': tube.row.tubeRemoveReminder }">
            {{ tube.row.expectedChangeDay }}
          </span>
        </div>
        <div slot="removeDateTime" slot-scope="tube">
          <span v-formatTime="{ value: tube.row.removeDate, type: 'date' }"></span>
          <span v-formatTime="{ value: tube.row.removeTime, type: 'time' }"></span>
        </div>
        <div slot="resetFlag" slot-scope="tube">
          <span>{{ tube.row.resetFlag ? "是" : "否" }}</span>
        </div>
        <div slot="operate" slot-scope="tube">
          <el-tooltip content="拔管">
            <div class="iconfont icon-drawing" @click.stop="tubeRecordDrawing(tube.row)"></div>
          </el-tooltip>
          <el-tooltip content="换管">
            <div class="iconfont icon-change" @click.stop="tubeRecordReplace(tube.row)"></div>
          </el-tooltip>
          <el-tooltip content="删除">
            <div class="iconfont icon-del" @click.stop="tubeRecordDelete(tube.row)"></div>
          </el-tooltip>
          <div class="iconfont icon-a" v-if="tube.row.aButtonFlag" @click.stop="goPageFrom(tube.row, 'A')"></div>
          <div class="iconfont icon-b" v-if="tube.row.bButtonFlag" @click.stop="goPageFrom(tube.row, 'B')"></div>
          <el-tooltip content="告知书" v-if="tube.row.emrDocumentID != 0">
            <div class="iconfont icon-pdf" @click.stop="getNotice(tube.row.emrDocumentID)"></div>
          </el-tooltip>
          <el-tooltip content="导管标签" v-if="tube.row.showTubeLabelButtonFlag">
            <div class="iconfont icon-print" @click.stop="viewTubeLabel(tube.row)"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 维护记录 -->
    <div slot="maintain-record">
      <packaging-table v-model="tubeCareMainList" :headerList="tubeCareMainColumnList">
        <!-- 评估类型 插槽 -->
        <div slot="assessType" slot-scope="row">
          <span v-if="row.row.recordsCode.indexOf('Start') != -1">开始评估</span>
          <span v-else-if="row.row.recordsCode.indexOf('End') != -1 || row.row.recordsCode.indexOf('UEX') != -1">
            结束评估
          </span>
          <span v-else>例行评估</span>
        </div>
        <!-- 操作 插槽-->
        <div slot="operate" slot-scope="row">
          <el-tooltip v-if="row.row.recordsCode && row.row.recordsCode != 'TubeEnd'" content="修改" placement="top">
            <div class="iconfont icon-edit" @click="tubeCareMainAddOrModify(row.row)"></div>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" v-if="row.row.recordsCode.indexOf('Start') == -1">
            <div class="iconfont icon-del" @click="tubeCareMainDelete(row.row)"></div>
          </el-tooltip>
        </div>
      </packaging-table>
    </div>
    <!-- 新增修改主记录 -->
    <div
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
      v-if="drawerType == 1"
      class="tube-record-add"
    >
      <div class="tube-record-add-left">
        <div class="tube-record-add-left-top">
          <el-select @change="getTubeByTubeType" v-model="currentTubeType">
            <el-option
              v-for="(type, index) in tubeTypeList"
              :key="index"
              :label="type.tubeTypeName"
              :value="type.tubeTypeCode"
            ></el-option>
          </el-select>
        </div>
        <div class="tube-record-add-left-middle">
          <pin-yin
            @postData="getTubeByPinyin"
            inputWidth="190"
            tableName="TubeList"
            v-model="pinyinInputData"
            class="pinyin-input"
          ></pin-yin>
        </div>
        <div class="tube-record-add-left-bottom">
          <el-table
            ref="tubeData"
            :data="tubeList"
            border
            height="100%"
            @row-click="tubeClick"
            :highlight-current-row="true"
            element-loading-text="加载中……"
          >
            <el-table-column prop="tubeShortName" label="管路名称"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="tube-record-add-right">
        <div class="tube-record-add-right-header">
          <div class="where">
            插管日期：
            <el-date-picker
              v-model="tubeRecordAddSaveMainView.startDate"
              type="date"
              clearable
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              class="tube-record-add-date"
              :picker-options="checkDate"
            />
            <span class="label">插管时间：</span>
            <el-time-picker
              v-model="tubeRecordAddSaveMainView.startTime"
              clearable
              format="HH:mm"
              value-format="HH:mm"
              placeholder="选择时间"
              class="tube-record-add-time"
            />
            <span class="label">记录人员：</span>
            <nurse-selector
              :disabled="!userDisabledFlag"
              label=""
              width="150"
              v-model="tubeRecordAddSaveMainView.addEmployeeID"
              :stationID="tubeRecordAddSaveMainView.occuredStationID"
              :filterable="true"
              :allowCreate="nurseSelectCanManualEntryFlag"
            ></nurse-selector>
            <span class="label">身体部位：</span>

            <body-part
              v-if="currentTube"
              v-model="selectBodyPart"
              label=""
              type="Common"
              :recordsCode="'TubeList-' + currentTube.id"
              :gender="patient ? patient.genderCode : ''"
              width="225"
              class="tube-record-add-body"
            ></body-part>
          </div>
          <div class="where">
            插管病区：
            <station-selector
              clearable
              v-model="tubeRecordAddSaveMainView.occuredStationID"
              label=""
              width="150"
            ></station-selector>
            <span class="label">插管科室：</span>
            <dept-selector
              clearable
              label=""
              width="150"
              v-model="tubeRecordAddSaveMainView.occuredDepartmentID"
              :stationID="tubeRecordAddSaveMainView.occuredStationID"
            ></dept-selector>
            <span class="label">导管说明：</span>
            <el-input
              v-model="tubeRecordAddSaveMainView.tubeContent"
              class="tube-content"
              placeholder="请输入内容"
            ></el-input>
          </div>
        </div>
        <div class="tube-record-add-right-content">
          <tabs-layout
            ref="tabsLayout"
            :template-list="templateDatas"
            :model="type"
            @change-values="changeValues"
            @checkTN="checkTN"
            @button-click="buttonClick"
            @button-record-click="buttonRecordClick"
          />
        </div>
      </div>
    </div>
    <!-- 新增修改维护记录 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
      v-if="drawerType == 2"
      class="tube-care-main-add"
    >
      <div slot="header">
        <span class="label">日期:</span>
        <el-date-picker
          v-model="tubeCareMainSaveView.assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          class="tube-care-main-add-date"
          :disabled="careMainDisabledFalg"
          :picker-options="checkDate"
        ></el-date-picker>
        <el-time-picker
          v-model="tubeCareMainSaveView.assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          class="tube-care-main-add-time"
          :disabled="careMainDisabledFalg"
        ></el-time-picker>
        <span class="label">病区:</span>
        <station-selector
          :disabled="careMainDisabledFalg"
          v-model="tubeCareMainSaveView.stationID"
          label=""
          width="160"
        ></station-selector>
        <dept-selector
          :disabled="careMainDisabledFalg"
          label=""
          width="140"
          v-model="tubeCareMainSaveView.departmentListID"
          :stationID="tubeCareMainSaveView.stationID"
        ></dept-selector>
        <span
          v-if="
            currentTubeRecord &&
            recordsCodeInfo &&
            recordsCodeInfo.recordsCode &&
            recordsCodeInfo.recordsCode.includes('Start')
          "
          class="label"
        >
          身体部位：
        </span>
        <body-part
          v-if="
            currentTubeRecord &&
            recordsCodeInfo &&
            recordsCodeInfo.recordsCode &&
            recordsCodeInfo.recordsCode.includes('Start')
          "
          v-model="selectBodyPart"
          label=""
          type="Common"
          :recordsCode="'TubeList-' + currentTubeRecord.tubeID"
          :gender="patient ? patient.genderCode : ''"
          width="225"
          class="tube-care-main-add-body"
        ></body-part>
      </div>
      <div class="tube-care-main-add-content">
        <tabs-layout
          :template-list="templateDatas"
          :model="type"
          @change-values="changeValues"
          @checkTN="checkTN"
          @button-click="buttonClick"
          @button-record-click="buttonRecordClick"
          ref="tabsLayout"
        />
      </div>
    </base-layout>
    <!-- 拔管 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
      v-if="drawerType == 3"
      class="tube-drawing"
    >
      <div slot="header">
        <span class="label">日期:</span>
        <el-date-picker
          v-model="tubeRecordDrawingSaveView.assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          class="tube-drawing-date"
          :picker-options="checkDate"
        ></el-date-picker>
        <el-time-picker
          v-model="tubeRecordDrawingSaveView.assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          class="tube-drawing-time"
        ></el-time-picker>
        <span class="label">病区:</span>
        <station-selector v-model="tubeRecordDrawingSaveView.stationID" label="" width="160"></station-selector>
        <dept-selector
          label=""
          width="140"
          v-model="tubeRecordDrawingSaveView.departmentListID"
          :stationID="tubeRecordDrawingSaveView.stationID"
        ></dept-selector>
        <span class="label">记录人员：</span>
        <nurse-selector
          :disabled="!userDisabledFlag"
          label=""
          width="181"
          v-model="tubeRecordDrawingSaveView.userID"
          :stationID="tubeRecordDrawingSaveView.stationID"
          :filterable="true"
          :allowCreate="nurseSelectCanManualEntryFlag"
        ></nurse-selector>
      </div>
      <div class="tube-drawing-content">
        <div class="tube-drawing-content-left">
          <el-table
            @row-click="clickRemoveReason"
            :highlight-current-row="true"
            :data="tubeRecordReasonList"
            border
            stripe
            height="100%"
          >
            <el-table-column header-align="center" align="center" prop="description" label="拔管原因"></el-table-column>
          </el-table>
        </div>
        <div class="tube-drawing-content-right">
          <el-button
            v-if="currentTubeRecord && currentTubeRecord.tubeType == 'Foley'"
            @click="
              buttonClick({
                assessListID: 2529,
                itemName: '尿液评估',
                linkForm: '/ioRecordMaintain?ioType=O&ioKind=210',
              })
            "
            type="primary"
          >
            尿液评估
          </el-button>
          <el-button
            v-if="currentTubeRecord && currentTubeRecord.tubeType == 'Drainage'"
            @click="
              buttonClick({
                assessListID: 2530,
                itemName: '引流液评估',
                linkForm: '/ioRecordMaintain?ioType=O&ioKind=240',
              })
            "
            type="primary"
          >
            引流液评估
          </el-button>
          <el-button
            v-if="removeTubeShowUEXSwitch"
            @click="buttonRecordClick({ itemName: uexBrParams.recordName, brParams: uexBrParams })"
            type="primary"
          >
            非计划拔管
          </el-button>
          <tabs-layout
            :style="{
              marginTop: '5px',
              height:
                removeTubeShowUEXSwitch ||
                (currentTubeRecord &&
                  (currentTubeRecord.tubeType == 'Drainage' || currentTubeRecord.tubeType == 'Foley'))
                  ? 'calc(100% - 30px)'
                  : '100%',
            }"
            :model="type"
            :template-list="templateDatas"
            @change-values="changeValues"
            @checkTN="checkTN"
            @button-click="buttonClick"
            @button-record-click="buttonRecordClick"
            ref="tabsLayout"
          />
        </div>
      </div>
    </base-layout>
    <!-- 换管 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="drawerLoading"
      :element-loading-text="drawerLoadingText"
      v-if="drawerType == 4"
      class="tube-record-change"
    >
      <div slot="header">
        <span class="label">日期:</span>
        <el-date-picker
          v-model="tubeReplaceSaveView.assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          class="tube-record-change-date"
          :picker-options="checkDate"
        ></el-date-picker>
        <el-time-picker
          v-model="tubeReplaceSaveView.assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
          class="tube-record-change-time"
        ></el-time-picker>
        <span class="label">病区:</span>
        <station-selector v-model="tubeReplaceSaveView.stationID" label="" width="160"></station-selector>
        <dept-selector
          label=""
          width="140"
          v-model="tubeReplaceSaveView.departmentListID"
          :stationID="tubeReplaceSaveView.stationID"
        ></dept-selector>
        <span class="label">记录人员:</span>
        <nurse-selector
          :disabled="!userDisabledFlag"
          label=""
          width="180"
          v-model="tubeReplaceSaveView.userID"
          :stationID="tubeReplaceSaveView.stationID"
          :filterable="true"
          :allowCreate="nurseSelectCanManualEntryFlag"
        ></nurse-selector>
      </div>
      <div class="tube-record-change-content">
        <tabs-layout
          :template-list="templateDatas"
          :model="type"
          @change-values="changeValues"
          @checkTN="checkTN"
          @button-click="buttonClick"
          @button-record-click="buttonRecordClick"
          ref="tabsLayout"
        />
      </div>
    </base-layout>
    <div class="patient-tube-dialog" slot="drawer-dialog">
      <!--专项-->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonName"
        :visible.sync="showSpecificCareFlag"
        fullscreen
        custom-class="specific-care-view no-footer"
      >
        <iframe v-if="showSpecificCareFlag" ref="buttonDialog" width="100%" height="100%"></iframe>
      </el-dialog>
      <!-- BR -->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonRecordTitle"
        :visible.sync="showButtonRecordDialog"
        custom-class="no-footer"
      >
        <risk-component :params="componentParams" @result="result"></risk-component>
      </el-dialog>
      <!-- 告知书 -->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        title="告知书"
        custom-class="no-footer"
        fullscreen
        :visible.sync="showNotification"
        v-loading="pdfLoading"
      >
        <!-- 显示打印告知书 -->
        <iframe
          id="printIframe"
          :src="ftpPath"
          type="application/x-google-chrome-pdf"
          width="99%"
          height="98%"
          frameborder="1"
          scrolling="auto"
        />
        //导管标签预览
      </el-dialog>
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="tubeLabelDialogTitle"
        width="360px"
        custom-class="tube-label"
        :visible.sync="tubeLabelVisible"
      >
        <div>
          <div class="btn">
            <el-button type="primary" @click="printCard">标签打印</el-button>
          </div>
          <div class="qr-code">
            <img :src="imgSrc" alt="导管标签" />
          </div>
        </div>
      </el-dialog>
      <el-dialog title="导管检核" fullscreen :visible.sync="goPageDialogDrag">
        <iframe v-if="goPageDialogDrag && src" :src="src"></iframe>
      </el-dialog>
    </div>
  </specific-care>
</template>

<script>
import { GetButtonData, GetRecordsCodeInfoByRecordCode } from "@/api/Assess";
import { GetNotificationDocument } from "@/api/Document";
import { GetDynamicTableHeader } from "@/api/DynamicTableSetting";
import { GetCareMainTableHeader } from "@/api/EMRRecordField";
import { DeleteByTubeCare } from "@/api/IO";
import {
  GetBringToShiftSetting,
  GetClinicalBySettingTypeCodeAndValue,
  GetOneSettingByTypeAndCode,
  GetClinicSettingByTypeCode,
} from "@/api/Setting";
import { GetBringToNursingRecordFlagSetting, GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import {
  ChangePatientTube,
  DeleteTubeByID,
  DeleteTubeCare,
  GetIOSettingIDByTubeID,
  GetRecord,
  GetRecordIDAndCareMainIDBySourceID,
  GetRemoveReason,
  GetTube,
  GetTubeAssessView,
  GetTubeCare,
  RemovePatientTube,
  SavePatientTube,
  SaveTubeCare,
} from "@/api/Tube";
import { GetUserInfo } from "@/api/User";
import baseLayout from "@/components/BaseLayout";
import pinYin from "@/components/Pinyin";
import bodyPart from "@/components/bodyPart";
import deptSelector from "@/components/selector/deptSelector";
import nurseSelector from "@/components/selector/nurseSelector";
import stationSelector from "@/components/selector/stationSelector";
import specificCare from "@/components/specificCare";
import packagingTable from "@/components/table/index";
import tabsLayout from "@/components/tabsLayout/index";
import riskComponent from "@/pages/riskAssessment/components/RiskComponent";
import { wp } from "@/utils/web-proxy";
import { mapGetters } from "vuex";
export default {
  components: {
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    baseLayout,
    nurseSelector,
    packagingTable,
    pinYin,
    riskComponent,
    bodyPart,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      loading: false,
      patient: undefined,
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      //主记录表格表头加第一行的高度
      tableOneRowHeight: undefined,
      //主记录表格 现有导管:1 历史导管:2
      tubeDataType: 1,
      tubeRecordList: [],
      tubeRecordColumnList: [],
      historyTubeRecordList: [],
      currentTubeRecord: undefined,
      disPlayChangeDay: true,
      tubeReplaceSaveView: {},
      removeTubeShowUEXSwitch: true,
      //维护记录表格
      tubeCareMainColumnList: [],
      tubeCareMainList: [],
      //弹窗变量 置管:1 维护:2 拔管:3 换管:4
      drawerType: undefined,
      //拔管
      tubeRecordDrawingSaveView: {},
      removeRecordsCodeInfo: {},
      tubeRecordReasonList: [],
      removeSettingCode: undefined,
      removeReason: undefined,
      //置管
      tubeTypeList: [],
      tubeList: [],
      tubeCopyList: [],
      currentTubeType: "-1",
      currentTube: undefined,
      clearInput: false,
      pinyinInputData: undefined,
      tubeRecordAddSaveView: {},
      tubeRecordAddSaveMainView: {},
      selectBodyPart: {},
      //告知书
      //显示告知书窗口
      showNotification: false,
      ftpPath: "",
      pdfLoading: false,
      //维护记录
      tubeCareMainSaveView: {},
      careMainDisabledFalg: false,
      showSpecificCareFlag: false,
      buttonName: undefined,
      // BR
      // 显示BR类弹窗
      showButtonRecordDialog: false,
      // BR类标题
      buttonRecordTitle: "",
      // BR项目ID
      brAssessListID: undefined,
      //BR组件参数
      componentParams: undefined,
      //弹窗变量
      drawerLoading: false,
      drawerLoadingText: undefined,
      templateDatas: [],
      recordsCodeInfo: {},
      assessDatas: [],
      checkTNFlag: true,
      link: "",
      handOverArr: [true, false],
      informPhysicianArr: [true, false],
      bringToNursingRecordArr: [true, false],
      settingHandOver: false,
      settingNursingRecord: false,
      showEditButton: true,
      //路由变量
      patientScheduleMainID: undefined,
      assessMainID: undefined,
      assessSort: 0,
      handoverID: undefined,
      hisOperationNo: undefined,
      sourceID: undefined,
      sourceType: undefined,
      supplementFlag: false,
      checkResult: true,
      userDisabledFlag: true,
      //导管标签的Src
      imgSrc: "",
      //是否显示导管标签
      tubeLabelVisible: false,
      //导管标签请求参数
      tubeLabelRequestParams: {},
      //标签弹框标题名称
      tubeLabelDialogTitle: "",
      // 日期不得大于当前日期
      checkDate: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        },
      },
      // io项目ID，部分导管有对应io项目
      ioSettingID: undefined,
      userPhysicianID: "",
      src: "",
      goPageDialogDrag: false,
      operateWidth: "",
      // 标记新增还是修改
      type: "",
      tubeCategoryID: undefined,
      nurseSelectCanManualEntryFlag: false,
      canManualEntryStationList: [],
      saveFlag: false,
      //导管描述必填
      tubeContentRequireFlag: false,
      uexBrParams: {
        recordListID: 47,
        recordsCode: "Extubation-ERA",
        recordName: "非计划性拔管危险评估表",
        showPointFlag: true,
        recordCategoryCode: "UEX",
        recordType: "Risk",
        showStyle: "Risk",
      },
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
    }),
    /**
     * description: 表格数据切换
     * return {*}
     */
    recordTableData() {
      return this.tubeDataType == "1" ? this.tubeRecordList : this.historyTubeRecordList;
    },
  },
  watch: {
    //在院病人信息
    "patientInfo.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.supplementFlag = false;
        }
      },
      immediate: true,
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.supplementFlag = true;
          this.bringToNursingRecordArr = [false, false];
        }
      },
      immediate: true,
    },
    "patient.inpatientID": {
      handler(newVal) {
        if (newVal) {
          this.init();
        }
      },
      immediate: true,
    },
    "tubeRecordList.length": {
      handler() {
        this.calcOperateColumnWidth();
      },
    },
    "tubeRecordAddSaveMainView.occuredStationID": {
      handler(newVal) {
        if (newVal) {
          this.filterManualEntryStationList();
        }
      },
      immediate: true,
    },
    "tubeRecordDrawingSaveView.stationID": {
      handler(newVal) {
        if (newVal) {
          this.filterManualEntryStationList();
        }
      },
      immediate: true,
    },
    "tubeReplaceSaveView.stationID": {
      handler(newVal) {
        if (newVal) {
          this.filterManualEntryStationList();
        }
      },
      immediate: true,
    },
    "selectBodyPart.bodyPartCode": {
      handler(newVal) {
        if (newVal) {
          this.bodyPartCheck(newVal);
        }
      },
    },
    showSpecificCareFlag(newVal, oldVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID, false);
      }
    },
  },
  async beforeMount() {
    //配置获取
    this.getDisPlayChangeDay();
    this.getRemoveTubeShowUEXSwitch();
    this.getBringHandOverSetting();
    this.getSettingBringToNursingRecord();
    await this.getUserDisabledFlag();
    this.getRemoveInterventionID();
    this.getUserPhysicianID();
  },
  methods: {
    /**
     * description: 初始化
     * param {*}
     * return {*}
     */
    async init() {
      this.clearType();
      await this.getTubeRecordList();
      await this.getRouteData();
      this.getManualEntryFlag();
    },
    /**
     * description: 身体部位检核
     * param {*}
     * return {*}
     */
    bodyPartCheck(bodyPartID) {
      this.tubeContentRequireFlag = bodyPartID == 571;
    },
    /**
     * description: 获取主记录数据
     * param {*} type
     * return {*}
     */
    async getTubeRecordList(recordID = undefined) {
      this.tubeRecordList = [];
      this.historyTubeRecordList = [];
      this.tubeRecordColumnList = [];
      if (!this.patient) {
        return;
      }
      //获取主记录动态表格
      await this.getReocrdTableHeaderList();
      let params = {
        inpatientID: this.patient.inpatientID,
        isCurrent: this.tubeDataType == "1" ? true : false,
        tubeCategoryID: this.tubeCategoryID,
        recordID,
        index: Math.random(),
      };
      if (this.$route.query.scheduleTime) {
        params.scheduleTime = this.$route.query.scheduleTime;
      }
      this.loading = true;
      await GetRecord(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.$set(this, this.tubeDataType == "1" ? "tubeRecordList" : "historyTubeRecordList", result.data);
          this.currentTubeRecord = recordID && result.data?.length ? result.data[0] : undefined;
        }
      });
      //对于预计换管日进行判断导管是否到期，标红加粗提示
      if (params.isCurrent && this.tubeRecordList) {
        this.tubeRecordList.forEach((tube) => {
          this.$set(tube, "tubeRemoveReminder", false);
          if (tube.expectedChangeDay) {
            tube.expectedChangeDay = this._datetimeUtil.formatDate(tube.expectedChangeDay, "yyyy-MM-dd");
            let nowDate = this._datetimeUtil.getNowDate();
            if (tube.expectedChangeDay <= nowDate) {
              tube.tubeRemoveReminder = true;
            }
          }
        });
      }
    },
    /**
     * description: 获取维护记录表头
     * param {*} params
     * param {*} arrKey
     * return {*}
     */
    async getTableHeaderList() {
      if (!this.currentTubeRecord) {
        return;
      }
      let params = {
        fileClassID: 11,
        fileClassSub: this.currentTubeRecord.tubeType,
        useDescription: "1||Table",
      };
      this.tubeCareMainColumnList = [];
      await GetCareMainTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tubeCareMainColumnList = res.data;
        }
      });
    },
    /**
     * description:
     * return {*}
     */
    async getReocrdTableHeaderList() {
      let params = {
        tableType: "TubeRecordTable",
        tableSubType: this.tubeDataType == 1 ? "now" : "history",
        hospitalID: this.hospitalInfo.hospitalID,
        index: Math.random(),
      };
      await GetDynamicTableHeader(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.tubeRecordColumnList = res.data;
        }
      });
    },
    /**
     * description: 获取维护记录表格数据
     * param {*}
     * return {*}
     */
    async getTubeCareMainList() {
      if (!this.currentTubeRecord) {
        this._showTip("warning", "获取导管维护记录失败");
        return;
      }
      let params = {
        patientTubeRecordID: this.currentTubeRecord.id,
      };
      this.loading = true;
      await GetTubeCare(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.tubeCareMainList = result.data;
          //字体加粗
          if (this.tubeCareMainList.length) {
            this.tubeCareMainList.forEach((item) => {
              item.extendItem = this.overStrikingText(item.extendItem);
              item.careIntervention = this.overStrikingText(item.careIntervention);
            });
          }
        }
      });
    },
    /**
     * description: 主记录点击
     * param {*} row
     * return {*}
     */
    async tubeRecordClick(row) {
      this.$set(this.showRecordArr, 0, !this.showRecordArr[0]);
      this.$set(this.showRecordArr, 1, !this.showRecordArr[1]);
      if (this.showRecordArr[1]) {
        this.$set(this, this.tubeDataType == "1" ? "tubeRecordList" : "historyTubeRecordList", [row]);
        this.currentTubeRecord = row;
        this.currentTube = this.tubeList.find((tube) => tube.id == this.currentTubeRecord.tubeID) ?? undefined;
        this.$nextTick(() => {
          this.tableOneRowHeight = this._common.getTableOneRowHeight(
            this.$refs.recordTable?.$el,
            ".main-record-row",
            ".main-record-header-row"
          );
        });
        await this.getTableHeaderList();
        await this.getTubeCareMainList();
      }
    },

    /**
     * description: 保存按钮选择
     * param {*}
     * return {*}
     */
    async tubeSaveSelect() {
      //保存检核必选项
      if (!this.$refs.tabsLayout || !this.$refs.tabsLayout.checkRequire()) {
        return;
      }
      this.drawerLoading = true;
      this.drawerLoadingText = "保存中……";
      //主记录保存
      if (this.drawerType == 1) {
        await this.tubeRecordSave();
      }
      //维护记录保存
      if (this.drawerType == 2) {
        await this.tubeCareMainSave();
      }
      //拔管
      if (this.drawerType == 3) {
        await this.tubeRecordDrawingSave();
      }
      //换管
      if (this.drawerType == 4) {
        await this.tubeRecordReplaceSave();
      }
      this.drawerLoading = false;
    },

    /**
     * description: 主记录新增
     * param {*}
     * return {*}
     */
    async tubeRecordAddOrModify() {
      this.type = "add";
      this.drawerType = 1;
      this.tubeRecordAddSaveView = {
        patientScheduleMainID: this.patientScheduleMainID,
        nursingLevel: this.patient.nursingLevelCode,
        assessListID: this.assessListID,
        assessSort: this.assessSort,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
      };
      this.tubeRecordAddSaveMainView = {
        inpatientID: this.patient.inpatientID,
        addEmployeeID: this.user.userID,
        bodyPartID: 0,
        startDate: this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd"),
        startTime: this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm"),
        occuredDepartmentID: this.patient.departmentListID,
        occuredStationID: this.patient.stationID,
        tubeContent: "",
        assessMainID: this.assessMainID,
        handoverID: this.handoverID,
        sourceDataID: this.hisOperationNo,
      };
      this.$set(this.handOverArr, 1, this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, false);
      this.$set(this.bringToNursingRecordArr, 1, this.settingNursingRecord);
      this.openOrCloseDrawer(true, "新增导管");
      await this.getTubeTypListAndTypeList();
      //默认第一根导管
      this.currentFirstTube();
    },
    /**
     * description: 导管保存新增
     * param {*}
     * return {*}
     */
    async tubeRecordSave() {
      if (!this.currentTube) {
        this._showTip("warning", "请选择导管！");
        return;
      }

      if (this.currentTube.bodyPartFlag == "Y" && !this.selectBodyPart.bodyPartCode) {
        this._showTip("warning", "请选择部位！");
        return;
      }
      if (this.tubeContentRequireFlag && !this.tubeRecordAddSaveMainView.tubeContent) {
        this._showTip("warning", "请填写导管说明！");
        return;
      }
      if (!this.checkTNFlag) {
        this.checkTNFlag = true;
        return;
      }
      let nowMainView = {
        tubeID: this.currentTube.id,
        bodyPartID: this.selectBodyPart.bodyPartCode,
        supplementFlag: this.supplementFlag,
      };
      if (this.currentTube.reNewDay && this.currentTube.reNewDay != 0) {
        nowMainView.expectedChangeDay = this._datetimeUtil.addDate(
          this.tubeRecordAddSaveMainView.startDate,
          this.currentTube.reNewDay
        );
      }
      nowMainView = Object.assign(this.tubeRecordAddSaveMainView, nowMainView);
      let params = {
        details: this.getDetails(),
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        nursingLevel: this.patient.nursingLevelCode,
        recordsCode: this.recordsCodeInfo.recordsCode,
        bringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
      };
      params = Object.assign(this.tubeRecordAddSaveView, params);
      params.main = nowMainView;
      await SavePatientTube(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this._showTip("success", "保存成功！");
          this.openOrCloseDrawer(false);
          this.getTubeRecordList();
        }
      });
    },
    /**
     * description: 维护记录新增
     * param {*}
     * return {*}
     */
    async tubeCareMainAddOrModify(row = undefined) {
      this.type = row ? "modify" : "add";
      let { disabledFlag, saveButtonFlag } =
        this.type === "add"
          ? await this._common.userSelectorDisabled(this.user.userID, true, this.supplementFlag, "")
          : await this._common.userSelectorDisabled(this.user.userID, false, this.supplementFlag, row.addEmployeeID);
      this.saveFlag = !saveButtonFlag;
      this.drawerType = 2;
      // 时间病区是否可修改
      this.careMainDisabledFalg = this.getCareMainDisablesFlag(row);
      this.openOrCloseDrawer(true, "导管维护");
      const defaultAssessDate = this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd");
      const defaultAssessTime = this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm");
      this.tubeCareMainSaveView = {
        inpatientID: this.patient.inpatientID,
        patientTubeRecordID: this.currentTubeRecord.id,
        patientScheduleMainID: row ? row.patientScheduleMainID : this.patientScheduleMainID,
        handoverID: this.handoverID,
        nursingLevel: this.patient.nursingLevelCode,
        numberOfAssessment: row ? row.numberOfAssessment : 1,
        assessDate: row ? row.assessDate : defaultAssessDate,
        assessTime: row ? row.assessTime : defaultAssessTime,
        stationID: row ? row.stationID : this.patient.stationID,
        departmentListID: row ? row.departmentListID : this.patient.departmentListID,
        tubeType: this.currentTubeRecord.tubeType,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        patientTubeCareMainID: row ? row.patientTubeCareMainID : "temp" + this._common.guid(),
      };
      this.$set(this.handOverArr, 1, row ? row.bringToShift : this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, row && row.informPhysician ? true : false);
      this.$set(this.bringToNursingRecordArr, 1, row ? row.bringToNursingRecord : this.settingNursingRecord);
      let recordsCode = row ? row.recordsCode : this.currentTubeRecord.matainRcordsCode;
      if (recordsCode.includes("Start")) {
        this.selectBodyPart = {
          bodyPartCode: this.currentTubeRecord.bodyPartID,
          bodyPartName: this.currentTubeRecord.bodyPart,
        };
      }
      this.getAssessView(recordsCode);
    },
    /**
     * description: 维护记录保存
     * param {*}
     * return {*}
     */
    async tubeCareMainSave() {
      let startFlag = this.recordsCodeInfo.recordsCode.includes("Start");
      //主记录有身体部位 修改时没有身体部位才检核
      if (this.currentTube?.bodyPartFlag == "Y" && startFlag && !this.selectBodyPart.bodyPartCode) {
        this._showTip("warning", "请选择部位！");
        return;
      }
      let params = {
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        careDetails: this.getDetails(),
        bringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
        supplementFlag: this.supplementFlag,
        bodyPartID: this.selectBodyPart.bodyPartCode,
      };
      if (startFlag) {
        params.bodyPartID = this.selectBodyPart.bodyPartCode;
      }
      params = Object.assign(this.tubeCareMainSaveView, params);
      await SaveTubeCare(params).then((result) => {
        this.addLoading = false;
        if (this._common.isSuccess(result)) {
          this.getTubeRecordList(this.currentTubeRecord.id);
          this.getTubeCareMainList();
          this._showTip("success", "保存成功！");
          this.openOrCloseDrawer(false);
        }
      });
    },

    /**
     * description: 拔管
     * param {*} row
     * return {*}
     */
    async tubeRecordDrawing(tubeRecord) {
      this.templateDatas = [];
      this.type = "add";
      this.drawerType = 3;
      this.currentTubeRecord = tubeRecord;
      this.tubeRecordDrawingSaveView = {
        inpatientID: this.patient.inpatientID,
        patientTubeRecordID: this.currentTubeRecord.id,
        patientTubeCareMainID: "temp" + this._common.guid(),
        assessDate: this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd"),
        assessTime: this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm"),
        stationID: this.patient.stationID,
        departmentListID: this.patient.departmentListID,
        nursingLevel: this.patient.nursingLevelCode,
        userID: this.user.userID,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        patientScheduleMainID: this.patientScheduleMainID,
        handoverID: this.handoverID,
      };
      //拔管 引流管和尿管需要维护引流液 careMainID前端生成
      this.tubeCareMainSaveView.patientTubeCareMainID = this.tubeRecordDrawingSaveView.patientTubeCareMainID;
      this.$set(this.handOverArr, 1, this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, false);
      this.$set(this.bringToNursingRecordArr, 1, this.settingNursingRecord);
      this.getTubeRecordRemoveReason();
      this.openOrCloseDrawer(true, "拔管维护");
    },
    /**
     * description: 拔管保存
     * param {*}
     * return {*}
     */
    async tubeRecordDrawingSave() {
      if (!this.removeReason) {
        this._showTip("warning", "请选择拔管原因");
        return;
      }
      let params = {
        removeReason: this.removeReason.typeValue.trim(),
        careDetails: this.getDetails(),
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        bringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
        supplementFlag: this.supplementFlag,
        bedNumber: this.patient.bedNumber,
      };
      params = Object.assign(this.tubeRecordDrawingSaveView, params);
      await RemovePatientTube(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.openOrCloseDrawer(false);
          this.showRecordArr = [true, false];
        }
      });
    },

    /**
     * description: 换管
     * param {*} row
     * return {*}
     */
    async tubeRecordReplace(row) {
      this.templateDatas = [];
      this.type = "add";
      this.drawerType = 4;
      this.currentTubeRecord = row;
      this.openOrCloseDrawer(true, "换管");
      this.tubeReplaceSaveView = {
        inpatientID: this.patient.inpatientID,
        patientTubeRecordID: this.currentTubeRecord.id,
        patientScheduleMainID: this.patientScheduleMainID,
        nursingLevel: this.patient.nursingLevelCode,
        numberOfAssessment: row ? row.numberOfAssessment : 1,
        assessDate: this.$route.query.sourceAssessDate ?? this._datetimeUtil.getNowDate("yyyy-MM-dd"),
        assessTime: this.$route.query.sourceAssessTime ?? this._datetimeUtil.getNowTime("hh:mm"),
        stationID: this.patient.stationID,
        departmentListID: this.patient.departmentListID,
        tubeType: this.currentTubeRecord.tubeType,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
        userID: this.user.userID,
      };
      this.$set(this.handOverArr, 1, this.settingHandOver);
      this.$set(this.informPhysicianArr, 1, false);
      this.$set(this.bringToNursingRecordArr, 1, this.settingNursingRecord);
      this.getAssessView(this.currentTubeRecord.startRcordsCode);
    },
    /**
     * description: 换管
     * param {*}
     * return {*}
     */
    async tubeRecordReplaceSave() {
      let params = {
        interventionMainID: this.recordsCodeInfo.interventionMainID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        careDetails: this.getDetails(),
        bringToShift: this.handOverArr[1],
        informPhysician: this.informPhysicianArr[1],
        bringToNursingRecord: this.bringToNursingRecordArr[1],
        supplementFlag: this.supplementFlag,
      };
      params = Object.assign(this.tubeReplaceSaveView, params);
      ChangePatientTube(this.tubeReplaceSaveView).then((result) => {
        if (this._common.isSuccess(result)) {
          this.openOrCloseDrawer(false);
          this.clearType();
        }
      });
    },
    /**
     * description: 维护记录删除
     * param {*} row
     * return {*}
     */
    async tubeCareMainDelete(row) {
      if (this.supplementFlag) {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          false,
          this.supplementFlag,
          row.addEmployeeID
        );
        if (!saveButtonFlag) {
          this._showTip("warning", "非本人不可操作");
          return;
        }
      } else {
        await this.checkAuthor(row.patientTubeCareMainID, "PatientTubeCareMain", row.nurseID);
        if (!this.showEditButton) {
          return;
        }
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            patientTubeCareID: row.patientTubeCareMainID,
          };
          DeleteTubeCare(params).then((result) => {
            if (this._common.isSuccess(result)) {
              this.getTubeCareMainList();
              this._showTip("success", "删除成功！");
              if (this.getSourceType(row.recordsCode) == "RemoveTube") {
                this.clearType();
                return;
              }
              this.getTubeRecordList(row.patientTubeRecordID);
            }
          });
        }
      });
    },
    /**
     * description: 主记录删除
     * param {*} row
     * return {*}
     */
    async tubeRecordDelete(row) {
      let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
        this.user.userID,
        false,
        this.supplementFlag,
        row.addEmployeeID
      );
      if (!saveButtonFlag) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      //权限检核
      await this.checkAuthor(row.id, "PatientTubeRecord", row.nurseID);
      if (!this.showEditButton) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          this.loadingText = "删除中……";
          this.loading = true;
          let params = {
            ID: row.id,
          };
          DeleteTubeByID(params).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              this.clearType();
              this._showTip("success", "删除成功！");
            }
          });
        }
      });
    },

    /*-------------专项护理组件逻辑-------------*/

    //获取主记录
    getMainFlag() {
      this.currentTubeRecord = undefined;
      this.getTubeRecordList();
    },
    //组件回传交班flag
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    //通知医师标记
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    //带入护理记录标记
    getBringToNursingRecordFlag(flag) {
      this.bringToNursingRecordArr[1] = flag;
    },
    //弹窗关闭
    drawerClose() {
      //维护记录取消
      if ((this.tubeType == 2 || this.tubeType == 3) && this.tubeCareMainSaveView.patientTubeCareMainID) {
        let params = {
          patientTubeRecordID: this.recordID,
          patientTubeCareMainID: this.tempCareMainID,
        };
        DeleteByTubeCare(params).then((result) => {
          if (this._common.isSuccess(result)) {
          }
        });
      }
      this.openOrCloseDrawer(false);
    },
    //弹窗开关
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle = title;
    },

    /*-------------页面配置-------------*/
    /**
     * description: 是否显示预计换管日期
     * param {*}
     * return {*}
     */
    getDisPlayChangeDay() {
      let param = {
        settingType: 130,
        settingCode: "TubeMaintainDeday",
      };
      GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.typeValue == "False") {
            this.disPlayChangeDay = false;
          } else {
            this.disPlayChangeDay = true;
          }
        }
      });
    },
    /**
     * description: 拔除导管时是否显示非计划拔管风险评估按钮
     * param {*}
     * return {*}
     */
    getRemoveTubeShowUEXSwitch() {
      let params = {
        settingTypeCode: "RemoveTubeShowUEXSwitch",
      };
      GetSettingSwitchByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.removeTubeShowUEXSwitch = res.data;
        }
      });
    },
    /**
     * description: 加粗字体
     * param {*} text
     * return {*}
     */
    overStrikingText(text) {
      if (!text) {
        return;
      }
      text = "<b>" + text;
      text = text.replace(/：/g, "：</b>");
      text = text.replace(/；/g, "；<b>");
      return text;
    },
    /**
     * description:获取风险级别样式
     * param {*} tube
     * return {*}
     */
    getLevelStyle(tube) {
      return {
        margin: "auto",
        lineHeight: "24px",
        width: "36px",
        color: "#ffffff",
        fontSize: "14px",
        borderRadius: "4px",
        backgroundColor: tube.tubeLevelColor,
      };
    },
    /**
     * description: 恢复默认状态
     * param {*}
     * return {*}
     */
    clearType() {
      this.$set(this, "showRecordArr", [true, false]);
      this.currentTubeRecord = undefined;
    },
    /**
     * description: 权限检核
     * param {*} id
     * param {*} tableName
     * return {*}
     */
    async checkAuthor(id, tableName, userID) {
      this.checkResult = await this._common.checkActionAuthorization(this.user, userID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        this.showEditButton = false;
        return;
      }
      //判断是否可修改或删除该数据
      let ret = await this._common.getEditAuthority(id, tableName, this.supplementFlag);
      if (ret) {
        this.showEditButton = false;
        this._showTip("warning", ret);
      } else {
        this.showEditButton = true;
      }
    },
    /**
     * description: 获取拔管原因
     * param {*}
     * return {*}
     */
    async getTubeRecordRemoveReason() {
      if (!this.currentTubeRecord) {
        return;
      }
      let params = {
        tubeListID: this.currentTubeRecord.tubeID,
      };
      await GetRemoveReason(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.tubeRecordReasonList = result.data;
        }
      });
    },
    /**
     * description: 获取是否带入交班配置
     * param {*}
     * return {*}
     */
    getBringHandOverSetting() {
      let params = {
        special: "Tube",
      };
      GetBringToShiftSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.settingHandOver = res.data;
        }
      });
    },
    /**
     * description: 获取是否默认勾选带入护理记录框
     * param {*}
     * return {*}
     */
    getSettingBringToNursingRecord() {
      let params = {
        settingTypeCode: "TubeAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },
    /**
     * description: 获取是否可选择记录人员
     * param {*}
     * return {*}
     */
    async getUserDisabledFlag() {
      if (this.supplementFlag) {
        let { disabledFlag, saveButtonFlag } = await this._common.userSelectorDisabled(
          this.user.userID,
          true,
          this.supplementFlag,
          ""
        );
        this.userDisabledFlag = !disabledFlag;
      }
      let param = {
        settingType: 127,
        settingCode: "TubeMaintainer",
      };
      GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.typeValue == "False") {
            this.userDisabledFlag = false;
          } else {
            this.userDisabledFlag = true;
          }
        }
      });
    },
    checkTN(flag) {
      this.checkTNFlag = flag;
    },
    //评估组件回传数据
    changeValues(datas) {
      this.assessDatas = datas;
    },
    /**
     * description: 串专项
     * param {*} changeItem
     * return {*}
     */
    async buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      // 尿管、引流管需要获取到对应的IntakeOutPutSettingID，跳转后限定项目列表使用
      await this.setIOSettingID();
      let careMainID = this.tubeCareMainSaveView.patientTubeCareMainID;
      let currSourceType = this.getSourceType(this.recordsCodeInfo.recordsCode);
      let sourceID =
        this.drawerType == "2"
          ? this.tubeCareMainSaveView.patientTubeCareMainID &&
            this.tubeCareMainSaveView.patientTubeCareMainID.replace(/temp/g, "")
          : this.currentTubeRecord.id;
      url += (url.includes("?") ? "&" : "?") + `bedNumber=${this.patient.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        `&inpatientID=${this.patient.inpatientID}` +
        `&tubeCareMainID=${careMainID}` +
        `&tubeRecordID=${this.currentTubeRecord.id}` +
        `&sourceID=${sourceID}` +
        `&sourceType=${this.currentTubeRecord ? `${currSourceType}_${content.assessListID}` : undefined}` +
        "&isDialog=true";

      this.showSpecificCareFlag = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },
    /**
     * description: 组件按钮回写
     * param {*}
     * return {*}
     */
    //
    async updateButton(assessListID, isRisk) {
      let item = await this.getButtonValue(assessListID, isRisk);
      if (!item) {
        return;
      }
      this.$nextTick(() => {
        if (this.$refs.tabsLayout?.updateButtonItem) {
          this.$refs.tabsLayout.updateButtonItem(item);
        }
      });
    },
    /**
     * description: BR数据回显
     * param {*} assessListID
     * return {*}
     */
    async getButtonValue(assessListID, isRisk) {
      let item = undefined;
      let sourceID =
        this.drawerType == "2"
          ? this.tubeCareMainSaveView.patientTubeCareMainID &&
            this.tubeCareMainSaveView.patientTubeCareMainID.replace(/temp/g, "")
          : this.currentTubeRecord.id;
      const sourceType = this.getSourceType(this.recordsCodeInfo.recordsCode);
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        assessListID: assessListID,
        sourceID: sourceID,
        sourceType: `${sourceType}${isRisk ? "" : "_" + assessListID}`,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
    getAssessTime() {
      let assessTimeSwitch = {
        1: () => {
          return (
            this._datetimeUtil.formatDate(this.tubeRecordAddSaveMainView.startDate, "yyyy-MM-dd") +
            " " +
            this._datetimeUtil.formatDate(this.tubeRecordAddSaveMainView.startTime, "hh:mm")
          );
        },
        2: () => {
          return (
            this._datetimeUtil.formatDate(this.tubeCareMainSaveView.assessDate, "yyyy-MM-dd") +
            " " +
            this._datetimeUtil.formatDate(this.tubeCareMainSaveView.assessTime, "hh:mm")
          );
        },
        3: () => {
          return (
            this._datetimeUtil.formatDate(this.tubeRecordDrawingSaveView.assessDate, "yyyy-MM-dd") +
            " " +
            this._datetimeUtil.formatDate(this.tubeRecordDrawingSaveView.assessTime, "hh:mm")
          );
        },
        4: () => {
          return (
            this._datetimeUtil.formatDate(this.tubeReplaceSaveView.assessDate, "yyyy-MM-dd") +
            " " +
            this._datetimeUtil.formatDate(this.tubeReplaceSaveView.assessTime, "hh:mm")
          );
        },
      };
      return assessTimeSwitch[this.drawerType]();
    },
    /**
     * description: 初始化BR组件
     * return {*}
     * param {*} content BR跳转风险项
     */
    async buttonRecordClick(content) {
      this.brAssessListID = content.assessListID;
      this.buttonRecordTitle = content.itemName;
      let record = content.brParams || {};
      this.componentParams = {
        patientInfo: this.patient,
        showPoint: record.showPointFlag,
        showTime: true,
        showStyle: record.showStyle,
        showBar: record.recordType == "Risk",
        recordListID: record.recordListID,
        recordsCode: record.recordsCode,
        sourceType: this.getSourceType(this.recordsCodeInfo.recordsCode),
        sourceID: this.currentTubeRecord.id,
        assessTime: this.getAssessTime(),
      };
      this.showButtonRecordDialog = true;
    },
    /**
     * description: 风险组件回调
     * param {*} resultFlag
     * param {*} resultData
     * return {*}
     */
    result(resultFlag, resultData) {
      this.showButtonRecordDialog = false;
      if (resultFlag && this.brAssessListID) {
        // 保存成功，回显数据
        this.updateButton(this.brAssessListID, true);
      }
    },

    /**
     * description: 拔管原因选择
     * param {*} reason
     * return {*}
     */
    async clickRemoveReason(reason) {
      this.removeReason = reason;
      this.templateDatas = [];
      //给默认
      this.recordsCodeInfo = {
        recordsCode: this.removeSettingCode.typeValue ? this.removeSettingCode.typeValue : "TubeEnd",
        interventionMainID: this.removeSettingCode.settingValue ? Number(this.removeSettingCode.settingValue) : 60009,
      };
      if (reason.settingValue) {
        await this.getAssessView(reason.settingValue);
      }
    },
    /**
     * description: 获取拔管默认InterventionID
     * param {*}
     * return {*}
     */
    async getRemoveInterventionID() {
      let params = {
        settingTypeCode: "TubeInterventionID",
        typeValue: "TubeEnd",
      };
      await GetClinicalBySettingTypeCodeAndValue(params).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data && response.data.length && response.data[0].settingValue) {
            this.removeSettingCode = response.data[0];
          }
        }
      });
    },
    /**
     * description:获取评估模板
     * param {*} reasonRecordsCode
     * return {*}
     */
    async getAssessView(recordsCode) {
      this.templateDatas = [];
      this.drawerLoading = true;
      this.drawerLoadingText = "加载中……";
      let params = {
        recordcode: recordsCode,
        departmentListID: this.patient.departmentListID,
        index: Math.random(),
      };
      await GetRecordsCodeInfoByRecordCode(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.recordsCodeInfo = result.data;
        }
      });
      if (!this.recordsCodeInfo) {
        this.drawerLoading = false;
        return;
      }
      let currSourceType = this.getSourceType(this.recordsCodeInfo.recordsCode);
      params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.recordsCodeInfo.recordsCode,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        dateOfBirth: this.patient.dateOfBirth,
        sourceID: this.currentTubeRecord ? this.currentTubeRecord.id : undefined,
        sourceType: this.currentTubeRecord ? currSourceType : undefined,
        index: Math.random(),
      };
      if (this.currentTubeRecord) {
        params.patientTubeRecordID = this.currentTubeRecord.id;
      }
      if (
        this.tubeCareMainSaveView.patientTubeCareMainID &&
        !this.tubeCareMainSaveView.patientTubeCareMainID.includes("temp")
      ) {
        params.patientTubeCareMainID = this.tubeCareMainSaveView.patientTubeCareMainID;
      }
      // 维护记录 非拔换管评估模板内容明细回显 sourceID使用CareMainID
      if (this.drawerType == 2 && currSourceType == "TubeMaintain") {
        params.sourceID = this.tubeCareMainSaveView.patientTubeCareMainID;
      }
      await GetTubeAssessView(params).then((result) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(result)) {
          this.templateDatas = result.data;
        }
      });
    },
    /**
     * description: 获取保存明细
     * param {*}
     * return {*}
     */
    getDetails() {
      return this.assessDatas
        .filter((content) => content.disableGroup !== -1)
        .map((content) => {
          const assessValue = ["C", "R"].includes(content.controlerType.trim()) ? "" : content.assessValue;
          if (content.controlerType.trim() === "TN" && assessValue === "") {
            return null;
          }
          return {
            assessListID: content.assessListID,
            assessListGroupID: content.assessListGroupID,
            assessValue: assessValue,
          };
        })
        .filter((detail) => detail !== null);
    },
    /**
     * description: 获取导管类型和导管列表
     * param {*}
     * return {*}
     */
    async getTubeTypListAndTypeList() {
      //已有数据不必重复获取
      if (this.tubeCopyList && this.tubeCopyList.length > 0) {
        return;
      }
      let params = {
        stationID: this.patient.stationID,
      };
      this.drawerLoading = true;
      this.drawerLoadingText = "加载中……";
      await GetTube(params).then((result) => {
        this.drawerLoading = false;
        if (this._common.isSuccess(result)) {
          this.tubeTypeList = result.data.tubeTypes;
          this.tubeTypeList.unshift({
            tubeTypeCode: "-1",
            tubeTypeName: "全部",
          });
          this.tubeList = result.data.tubeLists;
          this.tubeCopyList = result.data.tubeLists;
        }
      });
    },
    /**
     * description: 导管列表点击
     * param {*} tube
     * return {*}
     */
    tubeClick(tube) {
      this.currentTube = tube;
      this.getBodyLink();
      this.getAssessView(this.currentTube.tubeStart);
    },
    /**
     * description: 根据导管类型筛选导管
     * param {*} value
     * return {*}
     */
    getTubeByPinyin(value) {
      let tubeList = [];
      //获取数据后匹配tubeListData所有导管数据
      for (let i = 0; i < value.length; i++) {
        tubeList.push(
          ...this.tubeCopyList.filter((item) => {
            return item.id == value[i].indexID;
          })
        );
      }
      this.tubeList = tubeList;
      // 默认选择第一行
      this.currentFirstTube();
      //置下拉框选项为全部
      if (this.currentTubeType != "-1") {
        this.currentTubeType = "-1";
        this.clearInput = true;
      } else {
        this.clearInput = false;
      }
    },
    /**
     * description: 根据拼音筛选导管
     * param {*}
     * return {*}
     */
    getTubeByTubeType() {
      if (this.clearInput) {
        this.inputData = "";
        this.clearInput = false;
        return;
      }
      this.currentTube = undefined;
      if (this.currentTubeType == "-1") {
        this.tubeList = this.tubeCopyList;
      } else {
        this.tubeList = this.tubeCopyList.filter((tube) => {
          return tube.tubeType == this.currentTubeType;
        });
      }
      // 默认选择第一行
      this.currentFirstTube();
      //置空简拼输入框
      this.pinyinInputData = "";
    },
    /**
     * description: 默认第一根导管
     * param {*}
     * return {*}
     */
    currentFirstTube() {
      this.selectBodyPart = {};
      if (this.tubeList && this.tubeList.length > 0) {
        this.currentTube = this.tubeList[0];

        this.$nextTick(() => {
          if (this.$refs.tubeData) {
            this.$refs.tubeData.setCurrentRow(this.currentTube);
            this.getBodyLink();
            this.getAssessView(this.currentTube.tubeStart);
          }
        });
      } else {
        this.currentTube = undefined;
        this.getBodyLink();
        this.recordsCodeInfo = undefined;
        this.templateDatas = undefined;
      }
    },
    /**
     * description: 获取身体部位link
     * param {*} recordsCode
     * param {*} bodyPart
     * return {*}
     */
    getBodyLink(bodyPart = {}) {
      localStorage.setItem("selectPart", JSON.stringify(bodyPart));
      localStorage.setItem("bodyPart", JSON.stringify(bodyPart));
      let tubeID = undefined;
      if (this.currentTube) {
        tubeID = this.currentTube.id;
      }
      if (this.currentTubeRecord) {
        tubeID = this.currentTubeRecord.tubeID;
      }
      if (!tubeID) {
        this.link = "";
        return;
      }
      this.link =
        "../../static/body/mobileBody.html?type=Common&recordsCode=TubeList-" +
        tubeID +
        "&gender=" +
        this.patient.genderCode;
    },
    /**
     * description: 路由数据处理
     * param {*}
     * return {*}
     */
    async getRouteData() {
      if (this.$route.query.patientScheduleMainID && this.$route.query.patientScheduleMainID != "null") {
        this.patientScheduleMainID = this.$route.query.patientScheduleMainID;
        const param = {
          type: "schedule",
          sourceID: this.patientScheduleMainID,
        };
        await this.getRecordIDAndCareMainIDBySourceID(param);
      }
      this.tubeCategoryID = this.$route.query.tubeCategoryID;
      if (this.tubeCategoryID) {
        const param = {
          type: "assess",
          tubeCategoryID: this.tubeCategoryID,
        };
        await this.getRecordIDAndCareMainIDBySourceID(param);
      }
      this.assessMainID = this.$route.query.num;
      this.assessSort = this.$route.query.sort;
      this.assessListID = this.$route.query.assessListID;
      this.handoverID = this.$route.query.handoverID;
      this.hisOperationNo = this.$route.query.hisOperationNo;
      this.sourceID = this.$route.query.sourceID;
      this.sourceType = this.$route.query.sourceType;
      // 设置切换病人
      if (this.patientScheduleMainID || this.assessMainID || this.handoverID || this.hisOperationNo || this.sourceID) {
        this._sendBroadcast("setPatientSwitch", false);
      } else {
        this._sendBroadcast("setPatientSwitch", true);
      }
    },
    /**
     * description: 获取对应主记录ID和维护记录ID
     * param {*} sourceID
     * param {*} type
     * return {*}
     */
    async getRecordIDAndCareMainIDBySourceID({ type = undefined, tubeCategoryID = 0, sourceID = undefined }) {
      let params = {
        inpatientID: this.patient.inpatientID,
        sourceID,
        type,
        tubeCategoryID,
      };
      await GetRecordIDAndCareMainIDBySourceID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.getCurrentRecordAndCareMain(res.data);
        }
      });
    },
    /**
     * description: 关联数据回显
     * param {*} data
     * return {*}
     */
    async getCurrentRecordAndCareMain(data) {
      if (!data) {
        return;
      }
      let tubeRecordList = [];
      if (!data.removeFlag) {
        tubeRecordList = this.tubeRecordList;
      } else {
        this.tubeDataType = 2;
        await this.getTubeRecordList();
        tubeRecordList = this.historyTubeRecordList;
      }
      if (tubeRecordList.length == 0) {
        return;
      }
      let sucRecord = tubeRecordList.find((record) => record.id == data.patientTubeRecordID);
      if (!sucRecord) {
        return;
      }
      await this.tubeRecordClick(sucRecord);
      if (this.tubeCareMainList.length == 0) {
        return;
      }
      let sucCareMain = this.tubeCareMainList.find(
        (careMain) => careMain.patientTubeCareMainID == data.patientTubeCareMainID
      );
      this.tubeCareMainAddOrModify(sucCareMain);
    },
    /**
     * description: BR回显所需参数
     * param {*} recordsCode
     * return {*}
     */
    getSourceType(recordsCode) {
      if (!recordsCode) {
        return "TubeMaintain";
      }
      if (recordsCode.indexOf("Maintain") != -1) {
        return "TubeMaintain";
      }
      if (recordsCode.indexOf("End") != -1 || recordsCode.indexOf("UEX") != -1) {
        return "RemoveTube";
      }
      return "";
    },
    /**
     * description: 告知书获取
     * param {*} emrCode
     * return {*}
     */
    getNotice(emrCode) {
      this.showNotification = true;
      this.pdfLoading = true;
      let param = {
        inpatientID: this.patient.inpatientID,
        emrDocumentID: emrCode,
      };
      GetNotificationDocument(param).then((response) => {
        this.pdfLoading = false;
        if (this._common.isSuccess(response)) {
          this.ftpPath = response.data;
          return;
        }
        this._showTip("warning", "获取告知单异常！");
      });
    },
    /**
     * description: 维护记录时间病区是否可修改
     * param {*} row
     * return {*}
     */
    getCareMainDisablesFlag(row) {
      if (!row) {
        return false;
      }
      if (row.recordsCode.includes("Maintain")) {
        return false;
      }
      return true;
    },
    /**
     * description: 查看导管标签
     * return {*}
     */
    viewTubeLabel(row) {
      this.imgSrc = "";
      this.tubeLabelDialogTitle = this.patient.bedNumber + "-" + this.patient.patientName + "[导管标签]";
      this.tubeLabelRequestParams = {};

      this.tubeLabelRequestParams = {
        PatientName: this.patient.patientName,
        BedNumber: this.patient.bedNumber,
        Sex: this.patient.gender,
        StationName: this.patient.stationName,
        TubeName: row.tubeName,
        LocalCaseNumber: this.patient.localCaseNumber,
        PatientTubeRecordID: row.id,
        Age: this.patient.age,
      };
      wp.print.viewTubeLabel(this.tubeLabelRequestParams, (response) => {
        if (response.success) {
          this.imgSrc = response.data;
        } else {
          this._showTip("warning", response.message ? response.message : "连接打印服务失败！请确认打印插件是否启动!");
        }
      });

      this.tubeLabelVisible = true;
    },
    /**
     * description: 标签打印 -调用web-proxy打印标签
     * return {*}
     */
    printCard() {
      wp.print.printTubeLabel(this.tubeLabelRequestParams, (response) => {
        if (!response.success) {
          this._showTip("warning", "打印失败：" + response.message);
        }
      });
    },
    /**
     * description: 获取IO项目ID
     * return {*}
     */
    async setIOSettingID() {
      let params = {
        tubeListID: this.currentTubeRecord.tubeID,
      };
      await GetIOSettingIDByTubeID(params).then((res) => {
        this.ioSettingID = res.data;
      });
    },
    /**
     * description: 获取用户physicianID,跳转页面用
     * return {*}
     */
    getUserPhysicianID() {
      let params = {
        UserId: this.user.userID,
      };
      GetUserInfo(params).then((res) => {
        if (this._common.isSuccess(res)) {
          if (res.data.length > 0) {
            this.userPhysicianID = res.data[0].physicianID;
          }
        }
      });
    },
    /**
     * description: 三管跳转
     * param {*} row
     * param {*} type
     * return {*}
     */
    goPageFrom(row, type) {
      this.src = "";
      if (type == "B") {
        if (row.url && row.id) {
          this.src = row.url + "logincode=" + this.userPhysicianID + "&ordersn=" + row.id;
        }
      } else {
        if (row.url && row.tubeClTypeCode && row.tubeClTypeName) {
          this.src =
            row.url +
            "logincode=" +
            this.userPhysicianID +
            "&ordersn=" +
            row.id +
            "&deptName=" +
            this.patientInfo.departmentListName +
            "&admissionSn=" +
            this.patientInfo.caseNumber +
            "&patientId=" +
            this.patientInfo.chartNo +
            "&inpatientId=" +
            this.patientInfo.chartNo +
            "&patientName=" +
            this.patientInfo.patientName +
            "&tubeClTypeCode=" +
            row.tubeClTypeCode +
            "&tubeClTypeName=" +
            row.tubeClTypeName;
        }
      }
      if (this.src) {
        this.goPageDialogDrag = true;
      } else {
        this._showTip("warning", "配置异常，无法跳转页面");
      }
    },
    /**
     * description: 计算操作列的实际宽度（取决于内部图标个数）
     * return {*}
     */
    calcOperateColumnWidth() {
      this.$nextTick(() => {
        const operateColumns = this.$refs.tubeRecordTable?.$el.querySelectorAll(".operate");
        if (!operateColumns?.length) {
          this.operateWidth = 160;
          return;
        }
        // 获取其下的单元格dom
        const cells = Array.from(operateColumns).map((m) => m.querySelector(".cell"));
        // 计算每一行的操作列中有多少个元素，取最大值参与乘积
        const cellChildNumbers = cells.map((m) => m.children.length);
        const maxNumbers = Math.max(...cellChildNumbers);
        this.operateWidth = 35 * maxNumbers;
      });
    },
    /**
     * description: 获取记录人员下拉框是否可以输入文字配置
     * return {*}
     */
    getManualEntryFlag() {
      let param = {
        settingTypeCode: "NurseSelectCanManualEntryFlag",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.nurseSelectCanManualEntryFlag = response.data;
          this.getManualEntryStationSetting();
        }
      });
    },
    /**
     * @description: 获取记录人员可手工输入权限配置
     * @return
     */
    getManualEntryStationSetting() {
      let params = {
        settingTypeCode: "NurseSelectCanManualEntryStation",
      };
      GetClinicSettingByTypeCode(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.canManualEntryStationList = response.data;
          this.filterManualEntryStationList();
        }
      });
    },
    /**
     * @description: 过滤记录人员可手工输入病区权限
     * @return
     */
    filterManualEntryStationList() {
      let stationID = undefined;
      if (this.drawerType == 1) {
        stationID = this.tubeRecordAddSaveMainView.occuredStationID;
      }
      if (this.drawerType == 3) {
        stationID = this.tubeRecordDrawingSaveView.stationID;
      }
      if (this.drawerType == 4) {
        stationID = this.tubeReplaceSaveView.stationID;
      }
      let filterStation = this.canManualEntryStationList.find((setting) => setting.typeValue == stationID);
      if (!filterStation) {
        this.nurseSelectCanManualEntryFlag = false;
      } else {
        this.nurseSelectCanManualEntryFlag = true;
      }
    },
  },
};
</script>

<style lang="scss" >
.patient-tube {
  .main-record-content .catheter-situation {
    overflow: hidden;
    text-overflow: ellipsis;
    // 设置多少行时算作溢出
    -webkit-line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
  .last-maintain-alter {
    color: red;
  }
  @at-root .tooltip-popper {
    max-width: 500px;
  }
  .flag {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    width: 20px;
    border-radius: 10px;
    text-align: center;
    font-size: 12px;
    color: #fff;
    font-weight: bold;
    margin-bottom: 2px;
    &.A {
      background-color: #f5c181;
    }
    &.H {
      background-color: #21e6c1;
    }
    &.O {
      background-color: #ee5577;
    }
  }
  .header-latter-selector {
    display: inline-block;
  }
  .tube-drawing {
    .tube-drawing-date {
      width: 120px;
    }
    .tube-drawing-time {
      width: 80px;
    }
    .tube-drawing-content {
      height: 100%;
      overflow: hidden;
      & > div {
        float: left;
        height: 100%;
      }
      .tube-drawing-content-left {
        width: 200px;
        margin-right: 10px;
      }
      .tube-drawing-content-right {
        width: calc(100% - 210px);
      }
    }
  }
  .tube-record-add {
    height: 100%;
    padding: 5px 0px;

    box-sizing: border-box;
    overflow: hidden;
    & > div {
      float: left;
      height: 100%;
      box-sizing: border-box;
      border-top: 1px solid #dcdfe6;
      border-bottom: 1px solid #dcdfe6;
    }
    .tube-record-add-left {
      width: 200px;
      border-right: 1px solid #dcdfe6;
      border-left: 1px solid #dcdfe6;
      padding: 3px;
      .tube-record-add-left-top {
        height: 32px;
        .el-select {
          width: 190px;
        }
      }
      .tube-record-add-left-bottom {
        height: calc(100% - 68px);
      }
    }

    .tube-record-add-right {
      width: calc(100% - 200px);
      .tube-record-add-right-header {
        background-color: #fff;
        height: 85px;
        border-bottom: 1px solid #dcdfe6;
        border-right: 1px solid #dcdfe6;
        padding: 4px 15px;
        box-sizing: border-box;
        overflow-y: hidden;
        .where {
          margin-bottom: 4px;
          white-space: nowrap;
          .tube-content {
            width: 390px;
          }
          .tube-record-add-date,
          .tube-record-add-time {
            width: 150px;
          }
          .tube-record-add-body {
            float: none;
            display: inline-block;
            width: 125px !important;
          }
        }
      }
      .tube-record-add-right-content {
        height: calc(100% - 85px);
        overflow: hidden;
        & > div {
          height: 100%;
          width: 100%;
        }
      }
    }
  }
  .tube-care-main-add {
    height: 100%;
    .tube-care-main-add-date {
      width: 120px;
    }
    .tube-care-main-add-time {
      width: 80px;
    }
    .tube-care-main-add-body {
      float: none;
      display: inline-block;
      width: 125px !important;
    }
    .tube-care-main-add-content {
      height: 100%;
      overflow: hidden;
      & > div {
        height: 100%;
      }
    }
  }
  .tube-record-change {
    height: 100%;
    .tube-record-change-date {
      width: 120px;
    }
    .tube-record-change-time {
      width: 80px;
    }
    .tube-record-change-content {
      height: 100%;
      overflow: hidden;
      & > div {
        height: 100%;
      }
    }
  }
  .patient-tube-dialog {
    .el-dialog.tube-label {
      height: 450px;
      .el-dialog__body {
        height: calc(100% - 35px);
        .btn {
          text-align: right;
          margin-bottom: 10px;
        }
        .qr-code {
          height: 100%;
          text-align: center;
          img {
            border: 1px solid #d9d9d9;
            width: 65%;
            height: 65%;
          }
        }
      }
    }
    .specific-care-view {
      background-color: #f3f3f3;
      iframe {
        height: 99%;
        border: none;
      }
    }
  }
  .icon-a:before {
    color: #409eff;
  }
  .icon-b:before {
    color: #1cc6a3;
  }
  .tube-remove-reminder {
    color: red;
    font-weight: bold;
  }
}
</style>