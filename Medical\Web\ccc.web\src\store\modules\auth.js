/*
 * FilePath     : \src\store\modules\auth.js
 * Author       : 苏军志
 * Date         : 2020-08-28 15:54
 * LastEditors  : 苏军志
 * LastEditTime : 2021-06-14 18:48
 * Description  : 
 */
import common from "@/utils/common";
import { keys } from "@/utils/setting";

export default {
  namespaced: true,
  state: {
    isLogin: common.session(keys.isLogin),
    token: common.session(keys.token)
  },
  mutations: {
    setToken(state, value) {
      if (value) {
        state.token = value;
        common.session(keys.token, value);
      }
    },
    login(state) {
      state.isLogin = true;
      common.session(keys.isLogin, true);
    },
    exit(state) {
      state.isLogin = false;
      state.token = "";
      // common.session(keys.isLogin, false);
      // common.session(keys.token, "");
      sessionStorage.clear();
    }
  }
};
