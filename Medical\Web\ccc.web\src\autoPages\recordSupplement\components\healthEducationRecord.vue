<!--
 * FilePath     : \src\autoPages\recordSupplement\components\healthEducationRecord.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-22 15:58
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-22 16:08
 * Description  : 健康教育单补录
 * CodeIterationRecord: 
 -->
<template>
  <nursing-record
    :patientInfo="patient"
    :supplementPatient="supplemnentPatient"
    data-source="healthEducationRecord"
  ></nursing-record>
</template>

<script>
import nursingRecord from "./nursingRecord";
export default {
  components: {
    nursingRecord,
  },
  props: {
    patient: {
      type: Object,
      default: () => {},
    },
    supplemnentPatient: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style>
</style>