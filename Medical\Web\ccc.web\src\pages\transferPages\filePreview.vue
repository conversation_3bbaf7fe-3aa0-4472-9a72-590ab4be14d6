<!--
 * FilePath     : \src\pages\transferPages\filePreview.vue
 * Author       : 苏军志
 * Date         : 2022-07-27 19:01
 * LastEditors  : 苏军志
 * LastEditTime : 2022-07-28 12:12
 * Description  : 文件预览画面
 * CodeIterationRecord: 
-->

<template>
  <file-preview :datas="fileList"></file-preview>
</template>

<script>
import filePreview from "@/components/FilePreview";
export default {
  components: {
    filePreview,
  },
  data() {
    return {
      fileList: [],
    };
  },

  created() {
    if (this.$route && this.$route.query.fileList) {
      if (typeof this.$route.query.fileList == "string") {
        this.fileList = JSON.parse(this.$route.query.fileList);
        return;
      }
      this.fileList = this.$route.query.fileList;
    }
  },
};
</script>