{
  "[vue]": {
    "editor.defaultFormatter": "octref.vetur"
  },
  // 方法体注释模板
  "fileheader.cursorMode": {
    "description": "",
    "param": "",
    "return": ""
  },
  // 通用配置
  "fileheader.configObj": {
    // 自动添加头部注释黑名单
    "prohibitAutoAdd": ["md"],
    "dateFormat": "YYYY-MM-DD HH:mm", // 默认格式
    "autoAdd": true, // 默认开启,
    "autoAlready": true, // 只有支持的语言才自动生成
    "wideSame": true, // 设置为true开启
    "wideNum": 13, // 字段长度 默认为13
    "functionParamsShape": "no type", // 移除参数外形`{*}`
    "moveCursor": true, // 自动移动光标到Description所在行
    "language": {
      "vue": {
        "head": "<!--",
        "middle": " * ",
        "end": " -->",
        // 函数自定义注释符号：如果有此配置 会默认使用
        "functionSymbol": {
          "head": "/**", // 统一增加几个*号
          "middle": " * @",
          "end": " */"
        }
      },
      "js": {
        "head": "/*",
        "middle": " * ",
        "end": " */",
        // 函数自定义注释符号：如果有此配置 会默认使用
        "functionSymbol": {
          "head": "/**", // 统一增加几个*号
          "middle": " * @",
          "end": " */"
        }
      },
    },
  },
  "cSpell.words": [
    "bjca",
    "controler",
    "CRRT",
    "Datas",
    "daterange",
    "deel",
    "Handon",
    "iconfont",
    "SBAR",
    "screenfull",
    "Strs",
    "Thrombolysis",
    "vuex"
  ]
}
