/*
 * FilePath     : \src\api\Orders.js
 * Author       : 郭鹏超
 * Date         : 2020-05-08 15:39
 * LastEditors  : 来江禹
 * LastEditTime : 2022-12-28 10:51
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/order";

export const urls = {
  GetOrdersByInpatientID: baseUrl + "/GetOrdersByInpatientID",
  GetOrdersTaskList: baseUrl + "/GetOrdersTaskList",
  MergePrint: baseUrl + "/MergePrint",
  OrdersTaskPrint: baseUrl + "/OrdersTaskPrint",
  OrdersTaskCancelByID: baseUrl + "/OrdersTaskCancelByID",
  GetOrderDrugUseRescue: baseUrl + "/GetOrderDrugUseRescue"
};
//通过住院序号获得病人医嘱
export const GetOrdersByInpatientID = params => {
  return http.get(urls.GetOrdersByInpatientID, params);
};
//获取医嘱任务列表
export const GetOrdersTaskList = params => {
  return http.post(urls.GetOrdersTaskList, params);
};
// 保存合并医嘱
export const MergePrint = params => {
  return http.post(urls.MergePrint, params);
};
// 回写医嘱标签打印状态
export const OrdersTaskPrint = params => {
  return http.post(urls.OrdersTaskPrint, params);
};
// 取消医嘱任务
export const OrdersTaskCancelByID = params => {
  return http.post(urls.OrdersTaskCancelByID, params);
};
// 通过住院号获取患者12小时内的医嘱
export const GetOrderDrugUseRescue = params => {
  return http.get(urls.GetOrderDrugUseRescue, params);
};
