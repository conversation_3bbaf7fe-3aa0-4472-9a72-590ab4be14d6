<!--
 * FilePath     : \src\pages\recordSupplement\components\stationDepartmentBedDate.vue
 * Author       : 来江禹
 * Date         : 2023-02-20 08:00
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-09-25 17:37
 * Description  : 病区、科室、床位、日期组件
 * CodeIterationRecord:
-->

<template>
  <div class="station-department-bed-date">
    <div v-if="switchFalg.stationSwitch">
      <label>病区：</label>
      <template>
        <el-select
          class="station-select"
          v-model.number="tempStDeptBed.stationID"
          placeholder="请选择"
          :disabled="componentsReadonly.station"
          @change="getBedListByStationId"
        >
          <el-option v-for="item in stationList" :key="item.id" :label="item.stationName" :value="item.id" />
        </el-select>
      </template>
    </div>
    <div v-if="switchFalg.departmentListSwitch">
      <label>科别：</label>
      <template>
        <el-select
          class="station-select"
          v-model.number="tempStDeptBed.departmentListID"
          placeholder="请选择"
          :disabled="componentsReadonly.department"
          @change="giveParentVal"
        >
          <el-option v-for="item in optionsOfDepartmentList" :key="item.id" :label="item.value" :value="item.id" />
        </el-select>
      </template>
    </div>
    <div v-if="switchFalg.bedNumberSwitch">
      <label>床位：</label>
      <template>
        <el-select
          class="bed-number"
          filterable
          v-model="tempStDeptBed.bedNumber"
          placeholder="请选择"
          :disabled="componentsReadonly.bed"
          @change="giveParentVal"
        >
          <el-option v-for="item in bedlist" :key="item.id" :label="item.bedNumber" :value="item.bedNumber" />
        </el-select>
      </template>
    </div>
    <div v-if="switchFalg.dateTimeSwitch">
      <label>日期：</label>
      <el-date-picker
        v-model="tempStDeptBed.assessDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        :clearable="false"
        class="date"
        placeholder="日期"
        :disabled="componentsReadonly.date"
        @change="post()"
      ></el-date-picker>
      <label>时间：</label>
      <el-time-picker
        v-model="tempStDeptBed.assessTime"
        value-format="HH:mm"
        format="HH:mm"
        class="time"
        :clearable="false"
        :disabled="componentsReadonly.date"
        @change="post()"
      ></el-time-picker>
    </div>
  </div>
</template>

<script>
import { GetStationList, GetDepartmentDataByStationID } from "@/api/Station";
import { GetBedListByStationId } from "@/api/BedList";
import common from "@/utils/common.js";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters({
      patient: "getCurrentPatient",
    }),
  },
  data() {
    return {
      bedlist: [],
      stationList: [],
      optionsOfDepartmentList: [],
      tempStDeptBed: {},
      switchFalg: {},
    };
  },
  props: {
    stDeptBed: {},
    switch: {},
    // 设置各组件是否只读
    componentsReadonly: {
      type: Object,
      default: () => {
        return {
          department: false,
          station: false,
          bed: false,
          date: false,
        };
      },
    },
    //患者经历病区
    enteredStationList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  watch: {
    stDeptBed: {
      immediate: true,
      handler(newVal, oldVal) {
        this.tempStDeptBed = common.clone(newVal);
      },
      deep: true,
    },
    switch: {
      immediate: true,
      deep: true,
      handler(newVal, oldVal) {
        this.switchFalg = common.clone(newVal);
      },
    },
    "stDeptBed.stationID": {
      handler(newVal, oldVal) {
        this.getDepartmentList();
      },
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    post() {
      this.$emit("custCkick", this.tempStDeptBed);
    },
    giveParentVal() {
      this.getBedIdByBedNumber();
      this.selectStationDepartmentBed();
      this.post();
    },
    selectStationDepartmentBed() {
      this.$emit("selectStationDepartmentBed", this.tempStDeptBed);
    },
    getBedIdByBedNumber() {
      for (var i = 0; i < this.bedlist.length; i++) {
        if (this.tempStDeptBed.bedNumber == this.bedlist[i].bedNumber) {
          this.tempStDeptBed.bedId = this.bedlist[i].id;
          break;
        }
      }
    },
    getBedListByStationId(StationID) {
      this.post();
      if (
        this.tempStDeptBed.stationID != null &&
        this.tempStDeptBed.departmentListID != null &&
        this.tempStDeptBed.bedNumber != null &&
        this.tempStDeptBed.bedId != null
      ) {
        this.giveParentVal();
      }
      this.bedlist = {};
      let params = {
        stationID: StationID,
      };
      GetBedListByStationId(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.bedlist = result.data;
        }
      });
      this.getDepartmentList();
    },
    /**
     * description: 初始化函数
     * return {*}
     */
    init() {
      this.getStationList();
      this.getDepartmentList();
      this.getBedListByStationId(this.tempStDeptBed.stationID);
      if (
        this.tempStDeptBed.stationID != null &&
        this.tempStDeptBed.departmentListID != null &&
        this.tempStDeptBed.bedNumber != null &&
        this.tempStDeptBed.bedId != null
      ) {
        this.giveParentVal();
      }
    },
    /**
     * description: 获取病区列表
     * return {*}
     */
    getStationList() {
      if (this.enteredStationList.length) {
        this.stationList = this.enteredStationList;
        return;
      }
      let params = {
        inpatientID: this.tempStDeptBed.inpatientID ? this.tempStDeptBed.inpatientID : this.patient.inpatientID,
      };
      GetStationList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.stationList = result.data;
          if (result.data[0].stationID && !this.tempStDeptBed.stationID) {
            this.tempStDeptBed.stationID = result.data[0].stationID;
          }
          if (result.data[0].departmentListID && !this.tempStDeptBed.departmentListID) {
            this.tempStDeptBed.departmentListID = result.data[0].departmentListID;
          }
          if (result.data[0].bedNumber && !this.tempStDeptBed.bedNumber) {
            this.tempStDeptBed.bedNumber = result.data[0].bedNumber;
          }
          if (result.data[0].bedId && !this.tempStDeptBed.bedId) {
            this.tempStDeptBed.bedId = result.data[0].bedId;
          }
        }
      });
    },
    /**
     * description: 获取科别列表
     * return {*}
     */
    getDepartmentList() {
      if (!this.tempStDeptBed.stationID) return;
      let stationparams = {
        ID: this.tempStDeptBed.stationID,
      };
      GetDepartmentDataByStationID(stationparams).then((result) => {
        if (this._common.isSuccess(result)) {
          this.optionsOfDepartmentList = result.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.station-department-bed-date {
  display: inline-block;
  div {
    display: inline-block;
    .station-select {
      width: 0;
      max-width: 200px !important;
      min-width: 160px !important;
    }
    .bed-number {
      width: 120px !important;
    }
    .date {
      width: 120px !important;
    }
    .time {
      width: 80px !important;
    }
  }
}
</style>
