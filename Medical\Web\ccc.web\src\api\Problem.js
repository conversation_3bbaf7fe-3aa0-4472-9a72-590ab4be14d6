/*
 * FilePath     : \src\api\Problem.js
 * Author       : 李青原
 * Date         : 2020-03-26 16:17
 * LastEditors  : 李正元
 * LastEditTime : 2020-11-11 11:12
 * Description  :
 */

import http from "../utils/ajax";
const baseUrl = "/problem";

export const urls = {
  GetPreEvaluations: baseUrl + "/GetPreEvaluations",
  GetPreAddProblem: baseUrl + "/GetPreAddProblem",
  GetRelatedFactor: baseUrl + "/GetRelatedFactor",
  GetNursingProblemByKeyword: baseUrl + "/GetNursingProblemByKeyword",
  SaveAddProblem: baseUrl + "/SaveAddProblem",
  GetByProblemIDs: baseUrl + "/GetByProblemIDs",
  GetNursingProblemByPinyin: baseUrl + "/GetNursingProblemByPinyin",
  GetClusterOrderByPinyin: baseUrl + "/GetClusterOrderByPinyin"
};

// 获取病人护理问题
export const GetPreEvaluations = params => {
  return http.get(urls.GetPreEvaluations, params);
};
// 获取病人护理问题
export const GetPreAddProblem = params => {
  return http.post(urls.GetPreAddProblem, params);
};
// 获取病人定义特征
export const GetRelatedFactor = params => {
  return http.get(urls.GetRelatedFactor, params);
};
// 获得相关因素
export const GetNursingProblemByKeyword = params => {
  return http.get(urls.GetNursingProblemByKeyword, params);
};
// 获得相关因素
export const SaveAddProblem = params => {
  return http.post(urls.SaveAddProblem, params);
};
//透过问题序号取得护理问题
export const GetByProblemIDs = params => {
  return http.post(urls.GetByProblemIDs, params);
};

//手动加入护理问题
export const GetNursingProblemByPinyin = params => {
  return http.get(urls.GetNursingProblemByPinyin, params);
};

//手动加入集束护理
export const GetClusterOrderByPinyin = params => {
  return http.get(urls.GetClusterOrderByPinyin, params);
};
