/*
 * FilePath     : \src\api\PatientCINV.js
 * Author       : 曹恩
 * Date         : 2023-09-05 10:07
 * LastEditors  : 曹恩
 * LastEditTime : 2023-09-05 10:09
 * Description  : 无呕专项接口
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientCINV";

const urls = {
  GetCINVAssessView: baseUrl + "/GetCINVAssessView",
  GetCINVTableView: baseUrl + "/GetCINVTableView",
  SaveCINVCare: baseUrl + "/SaveCINVCare",
  DeleteCINVCare: baseUrl + "/DeleteCINVCare",
  GetPatientCINVTiming: baseUrl + "/GetPatientCINVTiming"
};

//获取无呕专项评估模板
export const GetCINVAssessView = params => {
  return http.get(urls.GetCINVAssessView, params);
};
//获取无呕专项评估记录
export const GetCINVTableView = params => {
  return http.get(urls.GetCINVTableView, params);
};
//保存无呕专项评估
export const SaveCINVCare = params => {
  return http.post(urls.SaveCINVCare, params);
};
//删除无呕专项评估
export const DeleteCINVCare = params => {
  return http.post(urls.DeleteCINVCare, qs.stringify(params));
};
//获取患者当前化疗时机
export const GetPatientCINVTiming = params => {
  return http.get(urls.GetPatientCINVTiming, params);
};
