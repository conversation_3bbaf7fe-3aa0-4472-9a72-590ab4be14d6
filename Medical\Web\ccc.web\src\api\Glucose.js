/*
 * FilePath     : \ccc.web\src\api\Glucose.js
 * Author       : 李正元
 * Date         : 2020-02-19 09:27
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-03-21 10:35
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientInsulin";

export const urls = {
  Save: baseUrl + "/save",
  GetPatientInsulin: baseUrl + "/GetPatientInsulin",
  Delete: baseUrl + "/DeleteRecrod",
  GetLineChart: baseUrl + "/GetLineChart"
};

//记录保存
export const Save = params => {
  return http.post(urls.Save, params);
};
//获取病人血糖数据
export const GetPatientInsulin = params => {
  return http.get(urls.GetPatientInsulin, params);
};
//删除记录
export const Delete = params => {
  return http.post(urls.Delete, qs.stringify(params));
};
//取得折线图数据(CCC)
export const GetLineChart = params => {
  return http.get(urls.GetLineChart, params);
};
