/*
 * FilePath     : \ccc.web\src\api\measuresTheSummary.js
 * Author       : 郭鹏超
 * Date         : 2020-05-06 11:25
 * LastEditors  : 郭鹏超
 * LastEditTime : 2020-05-22 16:15
 * Description  : 
 */
import http from "../utils/ajax";
const baseUrl = "/InterventionBilling";

const urls = {
  GetInterventionsAsync: baseUrl + "/GetInterventionsAsync",
  GetInterventionDetailsAsync: baseUrl + "/GetInterventionDetailsAsync",
  GetInterventionList: baseUrl + "/GetInterventionList"
}
//获取费用检核主数据
export const GetInterventionsAsync = (params) => {
  return http.get(urls.GetInterventionsAsync, params)
}
//获取费用检核明细数据
export const GetInterventionDetailsAsync = (params) => {
  return http.get(urls.GetInterventionDetailsAsync, params)
}
//获取所有措施数据
export const GetInterventionList = () => {
  return http.get(urls.GetInterventionList)
}


