<!--
 * FilePath     : \src\pages\schedule\index.vue
 * Author       : 军志
 * Date         : 2020-07-16 18:14
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-15 18:46
 * Description  : 2021-12-07 修复排程表头人数统计无法实时更新的问题，改为使用v-slot
--> 
<template>
  <div
    class="schedule"
    v-loading="loading"
    element-loading-text="加载中……"
    tabindex="0"
    @keydown.shift.68="showAllSchedule"
  >
    <schedule-top
      :hide="hideTable"
      :stationID="user.stationID"
      :shiftTime="shiftTime"
      :shift="shiftID"
      :defaultBedNumber="bedNumber"
      @switch-all="switchAll"
      @switch-schedule="switchSchedule"
      @switch-hide="switchHide"
      @switch-perform="switchPerform"
      @change-shift-time="changeShiftTime"
      @change-shift="changeShift"
      @change-bedNumber="changeBedNumber"
    ></schedule-top>
    <div class="schedule-data-wrap" ref="scheduleDataWrap" @scroll="scrollDiv">
      <el-table
        :header-cell-style="changeHeaderColor"
        :data="selectRow ? [selectRow] : []"
        border
        stripe
        :class="[
          'schedule-data',
          'select-schedule-data',
          { 'no-select': !showSelectTable && !hideTable },
          { 'hide-table': hideTable },
          { pad: !isPC },
        ]"
      >
        <el-table-column label-class-name="header-first" width="120" show-overflow-tooltip>
          <template #header>
            床号
            <br />
            共{{ scheduleDatas.length }}人
          </template>
          <template slot-scope="schedule">
            {{ schedule.row[2] }}
          </template>
        </el-table-column>
        <template v-for="(column, index) in scheduleHeader">
          <el-table-column
            :label="column"
            label-class-name="header-cell"
            min-width="60"
            align="center"
            :key="index"
            :index="index - 3"
            v-if="index > 2"
          >
            <template slot-scope="schedule">
              <div
                :class="[
                  'column',
                  {
                    'is-select': selectRowIndex >= 0 && selectColumnIndex == schedule.column.index,
                  },
                ]"
                @click="showSchedulesSelect(schedule.row, schedule.column, schedule.$index)"
                :style="getCellStyle(schedule.row[index], column)"
              >
                {{ schedule.row[index] }}
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <el-table
        :header-cell-style="changeHeaderColor"
        ref="scheduleData"
        :data="scheduleDatas"
        border
        stripe
        v-show="!hideTable"
        height="100%"
        class="schedule-data"
      >
        <el-table-column label-class-name="header-first" width="120" show-overflow-tooltip>
          <template #header>
            床号
            <br />
            共{{ scheduleDatas.length }}人
          </template>
          <template slot-scope="schedule">
            {{ schedule.row[2] }}
          </template>
        </el-table-column>
        <template v-for="(column, index) in scheduleHeader">
          <el-table-column
            :label="column"
            label-class-name="header-cell"
            min-width="60"
            align="center"
            :key="index"
            :index="index - 3"
            v-if="index > 2"
          >
            <template slot-scope="schedule">
              <div
                :class="[
                  'column',
                  {
                    'is-select': selectRowIndex == schedule.$index && selectColumnIndex == schedule.column.index,
                  },
                ]"
                :style="getCellStyle(schedule.row[index], column)"
                @click="showSchedules(schedule.row, schedule.column, schedule.$index)"
              >
                {{ schedule.row[index] }}
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <schedule-list
        v-if="selectRowIndex >= 0 && selectColumnIndex >= 0"
        :scheduleParams="scheduleParams"
        @refresh="getPatientSchedule(false)"
      />
    </div>
  </div>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import scheduleTop from "./components/scheduleTop";
import scheduleList from "./components/scheduleList";
import { GetNowStationShiftData } from "@/api/StationShift";
import { GetPatientDataByBedNumber } from "@/api/Inpatient";
import {
  GetStationPatientScheduleByCareNurse,
  GetStationPatientSchedule,
  GetPatientScheduleByBedNum,
} from "@/api/PatientSchedule";
import { GetViewShow } from "@/api/Setting";
import { mapGetters } from "vuex";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  components: { baseLayout, scheduleTop, scheduleList },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      currentPatient: "getCurrentPatient",
    }),
  },
  data() {
    return {
      showAll: false,
      showNoSchedule: false,
      hideTable: false,
      shiftTime: "",
      shiftID: undefined,
      shiftInfo: undefined,
      bedNumber: "",
      loading: false,
      scheduleHeader: [],
      scheduleDatas: [],
      selectRowIndex: undefined,
      selectColumnIndex: undefined,
      selectRow: undefined,
      selectColumn: undefined,
      scheduleParams: {},
      showSelectTable: false,
      isPC: true,
      selectTable: undefined,
      scheduleTable: undefined,
      scroll: 0,
      // 点击某个人排程时，是否要自动隐藏其他人排程
      autoHideSchedule: true,
      notPerformFlag: false,
      scheduleTipsFlag: false,
    };
  },
  async mounted() {
    this.GetAutoHideSchedule();
    // 联动两个表格的横向滚动条
    let tables = document.getElementsByClassName("el-table__body-wrapper");
    this.selectTable = tables[0];
    this.scheduleTable = tables[1];
    this.scheduleTable.addEventListener("scroll", () => {
      this.selectTable.scrollLeft = this.scheduleTable.scrollLeft;
    });
    this.isPC = this._common.isPC();
    // 如果是右键跳转过来的
    if (this.$route.query.shortCutFlag && this.currentPatient) {
      this.bedNumber = this.currentPatient.bedNumber;
    }
    this.shiftTime = this._datetimeUtil.getNowDate("yyyy-MM-dd");
    await this.GetNowStationShiftData();
    this.getPatientSchedule(true);
    this.getScheduleTipsSwitch();
  },
  methods: {
    scrollDiv() {
      if (this.$refs.scheduleDataWrap.scrollTop - 2 >= this.selectRowIndex * 39) {
        this.showSelectTable = true;
        this.selectTable.scrollLeft = this.scheduleTable.scrollLeft;
      } else {
        this.showSelectTable = false;
      }
    },
    switchAll(flag) {
      this.showAll = flag;
      this.getPatientSchedule(true);
    },
    switchSchedule(flag) {
      this.showNoSchedule = flag;
      this.getPatientSchedule(true);
    },
    switchHide(flag) {
      this.hideTable = flag;
    },
    switchPerform(flag) {
      this.notPerformFlag = flag;
      this.showNoSchedule = flag;
      this.getPatientSchedule(true);
    },
    changeShiftTime(shiftTime) {
      this.shiftTime = shiftTime;
      this.getPatientSchedule(true);
    },
    changeShift(shift) {
      this.shiftID = shift.id;
      this.shiftInfo = shift;
      this.getPatientSchedule(true);
    },
    changeBedNumber(bedNumber) {
      this.bedNumber = bedNumber;
      this.getPatientSchedule(true);
    },
    // 获取当前班次
    async GetNowStationShiftData() {
      await GetNowStationShiftData().then((result) => {
        if (this._common.isSuccess(result)) {
          this.shiftInfo = result.data.nowShift;
          this.shiftTime = result.data.shiftDate;
        }
      });
    },
    // 获取病人排程
    async getPatientSchedule(initFlag) {
      if (this.loading) {
        return;
      }
      if (initFlag) {
        this.selectRowIndex = undefined;
        this.selectColumnIndex = undefined;
        this.selectRow = undefined;
        this.showSelectTable = false;
        this.selectColumn = undefined;
        this.scheduleParams = {};
      }
      if (!this.shiftTime) {
        return;
      }
      if (!this.shiftID) {
        return;
      }
      this.scroll = 0;
      this.loading = true;
      this.scheduleDatas = [];
      this.showSelectTable = false;
      let params = {
        scheduleDate: this.shiftTime,
        shiftID: this.shiftID,
        notPerformFlag: this.notPerformFlag,
      };
      if (this.bedNumber) {
        // 获取单病人排程
        params.stationID = this.user.stationID;
        params.bedNum = this.bedNumber;
        await GetPatientScheduleByBedNum(params).then((result) => {
          this.loading = false;
          if (this._common.isSuccess(result)) {
            if (result.data && result.data.length > 0) {
              this.scheduleHeader = result.data[0];
              result.data.splice(0, 1);
              this.scheduleDatas = result.data;
            }
          }
        });
      } else {
        params.haveSchedule = this.showNoSchedule;
        //获取全科所有病人的排程
        if (this.showAll) {
          params.stationID = this.user.stationID;
          await GetStationPatientSchedule(params).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              if (result.data && result.data.length > 0) {
                this.scheduleHeader = result.data[0];
                result.data.splice(0, 1);
                this.scheduleDatas = result.data;
              }
            }
          });
        } else {
          // 获取当前护士派班病人的排程
          await GetStationPatientScheduleByCareNurse(params).then((result) => {
            this.loading = false;
            if (this._common.isSuccess(result)) {
              if (result.data && result.data.length > 0) {
                this.scheduleHeader = result.data[0];
                result.data.splice(0, 1);
                this.scheduleDatas = result.data;
              }
            }
          });
        }
      }
      // 如果已选择了。刷新已选人的排程
      if (!initFlag && this.selectRowIndex >= 0 && this.selectColumnIndex >= 0) {
        this.selectColumnIndex = undefined;
        /*2021-11-15不能直接指定this.selectRow = this.scheduleDatas[this.selectRowIndex];
        如一开始查询时已经指定床位,这样this.selectRowIndex永远是0*
        因此重新比对次数据-正元*/
        for (let index = 0; index < this.scheduleDatas.length; index++) {
          if (this.scheduleDatas[index][0] != this.selectRow[0]) {
            continue;
          }
          this.selectRow = this.scheduleDatas[index];
        }
        //解决触发措施弹窗保存完后报错问题  --GPC
        if (!this.selectRow) {
          this.selectRow = [];
        }
        await this.showSchedules(this.selectRow, this.selectColumn, this.selectRowIndex);
      }
    },

    async showSchedulesSelect(row, column, rowIndex) {
      for (let i = 0; i < this.scheduleDatas.length; i++) {
        if (row[0] == this.scheduleDatas[i][0]) {
          await this.showSchedules(this.scheduleDatas[i], column, i);
        }
      }
    },
    // 关闭隐藏
    showAllSchedule() {
      if (this.autoHideSchedule && this.selectColumn && this.selectRow) {
        this.hideTable = false;
        // 滚动条还原
        this.$nextTick(() => {
          this.$refs.scheduleDataWrap.scrollTop = this.scroll;
        });
      }
    },
    async showSchedules(row, column, rowIndex) {
      if (this.selectRowIndex == rowIndex && this.selectColumnIndex == column.index) {
        if (this.autoHideSchedule) {
          if (this.hideTable) {
            this.showAllSchedule();
          } else {
            // 记录滚动条位置
            this.scroll = this.$refs.scheduleDataWrap.scrollTop;
            this.hideTable = true;
          }
        }
        return;
      }
      if (this.autoHideSchedule) {
        // 记录滚动条位置
        this.scroll = this.$refs.scheduleDataWrap.scrollTop;
        //点击默认打开隐藏按钮
        this.hideTable = true;
      }
      this.selectRow = row;
      this.selectColumn = column;
      this.selectRowIndex = rowIndex;
      this.selectColumnIndex = column.index;
      if (!this.patientInfo || this.patientInfo.bedNumber != row[1]) {
        let params = {
          bedNumber: row[1],
        };
        await GetPatientDataByBedNumber(params).then((result) => {
          if (this._common.isSuccess(result) && result.data) {
            this.$store.commit("session/setPatientInfo", result.data);
            let currentPatient = {
              bedNumber: result.data.bedNumber,
              inpatientID: result.data.inpatientID,
              stationID: result.data.stationID,
              caseNumber: result.data.caseNumber,
              chartNo: result.data.chartNo,
              admissionDate: result.data.admissionDate,
              localCaseNumber: result.data.localCaseNumber,
              departmentCode: result.data.departmentCode,
            };
            this.$store.commit("session/setCurrentPatient", currentPatient);
          }
        });
      }
      this.scheduleParams = {
        inpatientID: row[0],
        caseNumber: this.patientInfo.caseNumber,
        bedNumber: row[1],
        stationID: this.user.stationID,
        shiftID: this.shiftID,
        shiftName: this.shiftInfo.shiftName,
        interventionID: "",
        shiftDate: this._datetimeUtil.formatDate(this.shiftTime, "yyyy-MM-dd"),
        departmentListID: this.patientInfo.departmentListID,
        nursingLevel: this.patientInfo.nursingLevelCode,
      };
      this.scheduleParams.patientInfo =
        row[1] +
        "-" +
        this.patientInfo.patientName +
        "【" +
        this.patientInfo.gender +
        "-" +
        this.patientInfo.ageDetail +
        "】";
      let timeSpan = column.label.split("-");
      if (timeSpan && timeSpan.length == 2) {
        this.scheduleParams.startTime = timeSpan[0];
        this.scheduleParams.endTime = timeSpan[1];
      }
      //跨天 并且开始时间大于班次结束时间，执行日期加1
      if (this.shiftInfo.crossDayFlag == "*" && this.scheduleParams.startTime <= this.shiftInfo.shiftEndTime) {
        this.scheduleParams.scheduleDate = this._datetimeUtil.addDate(this.shiftTime, 1, "yyyy-MM-dd");
      } else {
        this.scheduleParams.scheduleDate = this._datetimeUtil.formatDate(this.shiftTime, "yyyy-MM-dd");
      }
      //上一班内容，跨天，日期减1
      if (timeSpan[0] > timeSpan[1]) {
        this.scheduleParams.scheduleDate = this._datetimeUtil.addDate(this.shiftTime, -1, "yyyy-MM-dd");
      }
    },
    //更改上班未执行表头颜色
    changeHeaderColor(row, column, rowIndex, columnIndex) {
      if (row.columnIndex == 1) {
        return "color:#ff0000;";
      }
    },
    //读取配置，进行参数判断
    GetAutoHideSchedule() {
      let params = {
        settingTypeCode: "ViewDispable",
        typeValue: "AutoHideSchedule",
      };
      GetViewShow(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.autoHideSchedule = response.data;
        }
      });
    },
    /**
     * description: 获取未执行排程时候提示开关
     * return {*}
     */
    async getScheduleTipsSwitch() {
      let param = {
        SettingTypeCode: "NotExecutedScheduleTips",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.scheduleTipsFlag = response.data;
        }
      });
    },
    /**
     * description: 获取当前行样式
     * param {*} rowData
     * param {*} column
     * return {*}
     */
    getCellStyle(rowData, column) {
      if (!this.scheduleTipsFlag) {
        return {};
      }
      let timeArr = column.split("-");
      let startTime = this._datetimeUtil.toDate(timeArr[0]);
      let startDateTime =
        this._datetimeUtil.formatDate(this.shiftTime, "yyyy-MM-dd") +
        " " +
        this._datetimeUtil.formatDate(startTime, "hh:mm");
      let nowTime = this._datetimeUtil.getNowDate("yyyy-MM-dd hh:mm:ss");
      let timeDifference = this._datetimeUtil.getTimeDifference(startDateTime, nowTime, undefined, "M");
      if (rowData && rowData != "0" && rowData != "0:0" && timeDifference > 60) {
        return { color: "#ff0000" };
      }
    },
  },
};
</script>
<style lang="scss">
.schedule {
  height: 100%;
  width: 100%;
  padding: 5px;
  box-sizing: border-box;
  position: relative;
  outline: none;
  .schedule-top {
    top: 0px;
    height: 50px;
    line-height: 50px;
    padding: 0 10px;
    width: 100%;
    background-color: #fff;
    z-index: 100;
    margin-bottom: 10px;
    box-sizing: border-box;
  }
  .schedule-data-wrap {
    overflow: auto;
    height: calc(100% - 60px);
    width: 100%;
    padding-right: calc(100% - 100vw);
    .schedule-data {
      td {
        height: 38px;
      }
      // 冻结表
      &.select-schedule-data {
        position: absolute;
        left: 5px;
        top: 65px;
        width: calc(100% - 27px);
        z-index: 100;
        border-bottom: none;
        &.pad {
          width: calc(100% - 10px);
        }
        &.hide-table {
          width: 100%;
          position: initial;
        }
        &.no-select {
          display: none;
        }
        .el-table__body-wrapper {
          box-shadow: 0 3px 3px rgba(0, 0, 0, 0.12);
          margin-bottom: 2px;
          &::-webkit-scrollbar {
            display: none;
          }
        }
      }
      .header-first {
        text-align: center;
      }
      .header-cell {
        background-image: none !important;
        cursor: default !important;
        &.cell {
          width: 60px;
          text-align: left;
        }
      }
      td {
        padding: 0;
        .cell > .column {
          cursor: pointer;
          height: 36px;
          line-height: 36px;
          &.is-select {
            background-image: url("../../../static/images/text-select.png");
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 40px;
          }
        }
      }
    }
    .schedule-list {
      margin-top: 10px;
      height: calc(100% - 105px);
      width: 100%;
    }
  }
}
</style>
