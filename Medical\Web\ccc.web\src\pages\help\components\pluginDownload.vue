<!--
 * FilePath     : \src\pages\help\components\pluginDownload.vue
 * Author       : 苏军志
 * Date         : 2022-09-08 15:02
 * LastEditors  : 苏军志
 * LastEditTime : 2022-09-08 17:17
 * Description  : 插件下载
 * CodeIterationRecord: 
-->
<template>
  <div class="plugin-download">
    <div class="web-proxy">
      <a :href="downloadWebProxyUrl">打印插件下载</a>
    </div>
  </div>
</template>

<script>
import { GetConfigSettingWebProxyURL } from "@/api/Setting";
export default {
  data() {
    return {
      downloadWebProxyUrl: "",
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let params = {
        settingType: "Configs",
        settingCode: "GetWebProxyDownloadUrl",
      };
      GetConfigSettingWebProxyURL(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.downloadWebProxyUrl = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.plugin-download {
  padding: 20px;
}
</style>