<!--
 * FilePath     : \src\autoPages\recordSupplement\components\nursingProcessSwitch.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-17 17:24
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-11 16:29
 * Description  : 
 * CodeIterationRecord: 
 -->
<template>
  <div class="nursing-process-switch">
    <el-tabs class="tabs" v-model="activeComponentIndex">
      <el-tab-pane
        v-for="({ description }, index) in childComponents"
        :key="index"
        :label="description"
        :name="index.toString()"
      />
    </el-tabs>
    <div class="tabs-content">
      <component
        :is="childComponents[activeComponentIndex].settingValue"
        :patient="patient"
        mode="supplement"
      ></component>
    </div>
  </div>
</template>

<script>
import assessRecord from "@/pages/recordSupplement/assessRecord/index";
import recordRiskRating from "@/pages/riskAssessment/recordRiskRating";
import nursingPlan from "@/pages/recordSupplement/nursingProcess/components/nursingPlan";
export default {
  components: {
    assessRecord,
    recordRiskRating,
    nursingPlan,
  },
  props: {
    patient: {
      type: Object,
      default: () => {},
    },
    childComponents: {
      type: Array,
      require: true,
    },
  },
  data() {
    return {
      activeComponentIndex: 0,
    };
  },
};
</script>

<style lang="scss">
.nursing-process-switch {
  height: 100%;
  .tabs {
    height: 40px;
  }
  .tabs-content {
    height: calc(100% - 40px);
  }
}
</style>