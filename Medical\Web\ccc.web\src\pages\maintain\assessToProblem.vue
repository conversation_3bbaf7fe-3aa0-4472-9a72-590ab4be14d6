<!--
 * FilePath     : \ccc.web\src\pages\maintain\assessToProblem.vue
 * Author       : 李正元
 * Date         : 2020-05-08 09:20
 * LastEditors  : 李艳奇
 * LastEditTime : 2021-05-17 19:00
 * Description  : 
 -->
<!-- 插件文档介绍 -->
<!--  -->
<template>
  <base-layout class="maintain">
    <div slot="header">
      <span>专项类别:</span>
      <el-select v-model="listType" placeholder="请选择" @change="clearQuery" class="select-width">
        <el-option v-for="item in specialListType" :key="item.id" :label="item.type" :value="item.id"></el-option>
      </el-select>
      <span>照顾类别:</span>
      <el-select v-model="pattern" placeholder="请选择" @change="getPaternElements" class="select-width">
        <el-option
          v-for="item in carePatterns"
          :key="item.id"
          :label="item.description"
          :value="item.typeValue"
        ></el-option>
      </el-select>
      <span>照顾元素:</span>
      <el-select v-model="element" placeholder="请选择" @change="getPreAddProblem" class="select-width">
        <el-option
          v-for="item in careElements"
          :key="item.id"
          :label="item.description"
          :value="item.typeValue"
        ></el-option>
      </el-select>
      <span>简拼查询:</span>
      <!-- <el-input
        v-model="input"
        placeholder="请输入内容"
        class="input-width"
        @keyup.enter.native="getByKeyWords"
      ></el-input>
      <el-button class="query-button" type="primary" icon="iconfont icon-search" @click="getByKeyWords">查询</el-button> -->
      <pinyin
        @postData="getPinyinData"
        inputWidth="185px"
        tableName="NursingProblem"
        v-model="input"
        class="pinyin-input"
      ></pinyin>
    </div>
    <div class="table-prblem">
      <el-table :data="problemList" @current-change="chooseProblem" border stripe highlight-current-row height="100%">
        <el-table-column prop="code" label="护理问题码" width="75px" align="center"></el-table-column>
        <el-table-column
          prop="problem"
          label="护理问题"
          width="140px"
          header-align="center"
          align="left"
        ></el-table-column>
      </el-table>
    </div>
    <div class="table-assess" v-loading="loading">
      <el-table :data="assessToProblemList" border stripe highlight-current-row height="100%">
        <el-table-column
          prop="assessCode"
          label="评估码"
          min-width="150px"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="description"
          label="评估内容"
          min-width="150px"
          header-align="center"
          align="left"
        ></el-table-column>
        <el-table-column
          prop="mappingType"
          label="类型"
          min-width="100px"
          header-align="center"
          align="left"
        ></el-table-column>
        <el-table-column
          prop="powerWeight"
          label="权重"
          min-width="50px"
          header-align="center"
          align="left"
        ></el-table-column>
        <el-table-column prop="departmentList" label="科室" min-width="50px" align="center"></el-table-column>
        <el-table-column prop="gender" label="性别" width="50px" align="center"></el-table-column>
        <el-table-column prop="ageBottom" label="年龄下限" width="50px" align="center"></el-table-column>
        <el-table-column prop="ageTop" label="年龄上限" width="50px" align="center"></el-table-column>
        <el-table-column prop="necessaryAssessList" label="必要条件" align="center"></el-table-column>
        <el-table-column prop="disposeAssessList" label="排除条件" align="center"></el-table-column>
        <el-table-column align="center" width="50px" label="操作">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <i class="iconfont icon-edit" @click="edit(scope.$index, scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div>
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        customClass="dialog-style"
        :title="dialogTitle"
        :visible.sync="editDialog"
      >
        <el-form :model="editForm" ref="editForm">
          <el-form-item label="权重:">
            <el-input
              v-model="editForm.powerWeight"
              class="input-width"
              @keyup.native="value = oninput(editForm.powerWeight)"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button @click="editDialog = false">取消</el-button>
          <el-button type="primary" @click="saveEdit()">保 存</el-button>
        </div>
      </el-dialog>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { GetPatterns, GetPaternElements } from "@/api/Setting";
import { GetPreAddProblem, GetByProblemIDs } from "@/api/Problem";
import { GetAssessToProblemByProblemID, UpdateAssessToProblem } from "@/api/AssessToProblem";
import pinyin from "@/components/Pinyin";
export default {
  components: {
    baseLayout,
    pinyin,
  },
  created() {
    this.init();
    this.getSpecialListType();
    this.getPatterns();
  },
  data() {
    return {
      //选择到的专项类别
      listType: 1,
      //选择到的照顾类别
      pattern: undefined,
      //选择到的照顾元素
      element: undefined,
      //查询条件
      input: undefined,
      //专项类别
      specialListType: [
        {
          id: 1,
          type: "通用",
        },
      ],
      //照顾类别
      carePatterns: [],
      //照顾元素
      careElements: [],
      //护理问题
      problemList: [],
      //评估对诊断清单
      assessToProblemList: [],
      //编辑弹窗
      editDialog: false,
      //弹窗头說明
      dialogTitle: undefined,
      //弹窗头說明主
      dialogTitleMain: undefined,
      //加载
      loading: false,
      //编辑弹框传递的数据
      editForm: [],
    };
  },
  methods: {
    init() {
      this.specialListType.push(
        {
          id: 2,
          type: "妇科",
        },
        {
          id: 3,
          type: "儿科",
        }
      );
    },
    //简拼搜索返回数据
    getPinyinData(value) {
      let params = [];
      value.forEach((item) => {
        params.push(item.indexID);
      });
      //取得照顾元素
      GetByProblemIDs(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.problemList = result.data;
        }
      });
    },
    getSpecialListType() {
      //取得专项类别
    },
    getPatterns() {
      //取得照顾类别
      GetPatterns().then((result) => {
        if (this._common.isSuccess(result)) {
          this.carePatterns = result.data;
        }
      });
      if (this.carePatterns.length == 0) {
        this.clearView();
      }
    },
    getPaternElements() {
      this.element = undefined;
      this.clearView();
      this.input = undefined;
      if (this.pattern == undefined) {
        return;
      }
      let params = {
        pattern: this.pattern,
      };
      //取得照顾元素
      GetPaternElements(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.careElements = result.data;
        }
      });
    },
    //取得诊断
    getPreAddProblem() {
      if (this.element == undefined) {
        return;
      }
      let params = {
        listType: this.listType,
        element: this.element,
      };
      this.problemList = [];
      GetPreAddProblem(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.problemList = result.data;
        }
      });
    },
    //关键字查询
    getByKeyWords() {
      if (this.input == undefined || this.input == "") {
        return;
      }
      let params = {
        filterName: this.input,
      };
      this.problemList = [];
      GetPreAddProblem(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.problemList = result.data;
        }
      });
    },
    //选择诊断
    chooseProblem(row) {
      if (row == null) {
        return;
      }
      this.dialogTitle = undefined;
      this.dialogMain = "护理诊断:" + row.problem;
      this.assessToProblemList = [];
      this.getAssessToProblemList(row.id);
    },
    //显示修改弹窗同时组出表头
    edit(index, row) {
      this.dialogTitle = this.dialogMain + "," + row.description;
      this.editDialog = true;
      this.editForm = Object.assign({}, row); //将数据传入dialog页面
      this.editForm.index = index; //传递当前index
    },
    clearView() {
      this.careElements = [];
      this.problemList = [];
      this.assessToProblemList = [];
    },
    clearQuery() {
      this.element = undefined;
      this.pattern = undefined;
      this.clearView();
    },
    oninput(num) {
      this.editForm.powerWeight = this._decimalUtil.decimalRound(num, "RoundDown", 2);
    },
    saveEdit() {
      if (this.editForm.powerWeight == undefined) {
        this._showTip("warning", "请确认输入的数据格式是否正确");
        this.editDialog = false;
        return;
      }
      let params = {
        assessToProblemID: this.editForm.assessToProblemID,
        powerWeight: this.editForm.powerWeight,
        listType: this.listType,
      };
      UpdateAssessToProblem(params).then((data) => {
        this.loading = false;
        this.editDialog = false;
        if (this._common.isSuccess(data)) {
          this._showTip("success", "保存成功");
          this.getAssessToProblemList(this.editForm.problemID);
        }
      });
    },
    getAssessToProblemList(id) {
      this.loading = true;
      let params = {
        problemID: id,
        listType: this.listType,
      };
      GetAssessToProblemByProblemID(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result)) {
          this.assessToProblemList = result.data;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.maintain {
  .select-width {
    width: 130px;
  }
  .pinyin-input {
    display: inline-block;
    vertical-align: middle;
  }
  .table-prblem {
    width: 220px;
    float: left;
    height: 100%;
  }
  .table-assess {
    width: calc(100% - 230px);
    float: right;
    height: 100%;
  }
  .dialog-style {
    width: 30%;
    height: 190px;
    transform: translate(0, 100%);
    .modify-style {
      text-align: center;
      transform: translate(0, 100%);
    }
  }
}
</style>
