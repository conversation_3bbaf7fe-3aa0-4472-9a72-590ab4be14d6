// 如果是字符串，返回日期；如果是日期，直接返回
let toDate = (value) => {
  let date;
  if (typeof value == "string" || typeof value == "number") {
    date = new Date(value);
    if (typeof value == "string") {
      // 防止相差8小时问题
      date = new Date(value.replace(/-/g, "/"));
    }
    if (date == "Invalid Date" && value.indexOf(" ") == -1) {
      // 如果时间转换失败，
      date = new Date(formatDate(new Date(), "yyyy-MM-dd") + " " + value);
    }
  } else {
    date = value;
  }
  return date;
};
// 补零
const padLeftZero = (str, length) => {
  let newStr = str;
  let zero = "";
  if (length === 2) {
    zero = "00";
  }
  if (length === 3) {
    zero = "000";
  }
  newStr = (zero + str).substring(str.length);
  return newStr;
};

// 将日期转换为指定格式的字符串
let formatDate = (value, format) => {
  var date = toDate(value);
  if (!format) {
    format = "yyyy-MM-dd hh:mm:ss";
  }
  const re = /(y+)/;
  if (re.test(format)) {
    const t = re.exec(format)?.[1];
    format = format.replace(
      t,
      String(date.getFullYear()).substring(4 - t.length)
    );
  }
  let o = {
    "M+": date.getMonth() + 1,
    "d+": date.getDate(),
    "h+": date.getHours(),
    "m+": date.getMinutes(),
    "s+": date.getSeconds(),
    "S+": date.getMilliseconds(),
  };
  for (const k in o) {
    const regx = new RegExp(`(${k})`);
    if (regx.test(format)) {
      const str = o[k];
      const t = regx.exec(format)?.[1];
      format = format.replace(
        t,
        t.length === 1 ? str : padLeftZero(String(str), t.length)
      );
    }
  }
  return format;
};

// 日期，在原有日期基础上，增加days天数，默认增加1天
let addDate = (value, days, format) => {
  if (days == undefined || days == "") {
    days = 1;
  }
  if (!format) {
    format = "yyyy-MM-dd hh:mm:ss";
  }
  var date = toDate(value);
  date.setDate(date.getDate() + days);
  return formatDate(date, format);
};

// 日期，在原有日期基础上，增加Hours小时数，默认增加1小时
let addHours = (value, hours, format) => {
  if (hours == undefined || hours == "") {
    hours = 1;
  }
  if (!format) {
    format = "yyyy-MM-dd hh:mm:ss";
  }
  var date = toDate(value);
  date = new Date(date.valueOf() + hours * 60 * 60 * 1000); // 当前时间加上4小时
  return formatDate(date, format);
};

// 日期，在原有日期基础上，增加minutes分钟数，默认增加1分钟
let addMinutes = (value, minutes, format) => {
  if (!minutes) {
    minutes = 1;
  }
  if (!format) {
    format = "yyyy-MM-dd hh:mm:ss";
  }
  var date = toDate(value);
  date = new Date(date.valueOf() + minutes * 60 * 1000);
  return formatDate(date, format);
};

// 重写Date无参数的构造函数
let _date = Date;
Date = function (...params) {
  // 只重写无参数的构造函数
  if (params.length == 0) {
    // 获取服务器时间和本地时间差值
    let timeDiff = JSON.parse(sessionStorage.getItem("timeDiff"));
    // 矫正时间并返回
    return new _date(new _date().getTime() + timeDiff);
  }
  return new _date(...params);
};
// 继承原型函数
Date.prototype = _date.prototype;
// 继承静态函数
Date.now = _date.now;
Date.parse = _date.parse;
Date.UTC = _date.UTC;
// 将原有的Date无参数构造函数放到新Date的_localDate方法
Date._localDate = function () {
  return new _date();
};

//获取年月日
let getNowDate = (format) => {
  if (!format) {
    format = "yyyy-MM-dd";
  }
  return formatDate(new Date(), format);
};
//获取时分秒
let getNowTime = (format) => {
  if (!format) {
    format = "hh:mm:ss";
  }
  return formatDate(new Date(), format);
};
//获取年月日时分秒
let getNow = (format) => {
  if (!format) {
    format = "yyyy-MM-dd hh:mm:ss";
  }
  return formatDate(new Date(), format);
};

//获取时间差
// startTime：开始时间
// endTime ：结束时间（默认为当前时间）
// timeType：开始时间和结束时间类型
//          date 日期类型（yyyy-MM-dd或yyyy-MM-dd hh:mm:ss）
//          time 时间类型（hh:mm:ss）
// returnType：时间差类型（默认为all）
//           D 相差天数
//           H 相差小时数
//           M 相差分钟数
//           S 相差秒数
//           all days + "天 " + hours + "小时 " + minutes + " 分钟" + seconds + " 秒";
// decimalPlace：返回值小数位数（默认整数）
let getTimeDifference = (
  startTime,
  endTime,
  timeType = "date",
  returnType = "all",
  decimalPlace = 0
) => {
  if (!startTime) {
    return undefined;
  }
  //结束时间不传 默认为当前日期或当前时间
  if (!endTime) {
    if (timeType == "date") {
      endTime = getNow();
    }
    if (timeType == "time") {
      endTime = getNow("hh:mm:ss");
    }
  }
  //日期格式转换
  if (timeType == "date") {
    startTime = new Date(startTime);
    endTime = new Date(endTime);
  }
  //时间格式转换
  if (timeType == "time") {
    startTime = new Date(
      formatDate(new Date(), "yyyy-MM-dd") + " " + startTime
    );
    endTime = new Date(formatDate(new Date(), "yyyy-MM-dd") + " " + endTime);
  }
  //判断时间格式是否有误
  if (startTime == "Invalid Date" || endTime == "Invalid Date") {
    return undefined;
  }
  let dateDiff = endTime - startTime;
  //返回相差天数
  if (returnType == "D") {
    return (dateDiff / (24 * 3600 * 1000)).toFixed(decimalPlace);
  }
  //返回相差小时数
  if (returnType == "H") {
    return (dateDiff / (3600 * 1000)).toFixed(decimalPlace);
  }
  //返回相差分钟数
  if (returnType == "M") {
    return (dateDiff / (60 * 1000)).toFixed(decimalPlace);
  }
  //返回相差秒数
  if (returnType == "S") {
    return (dateDiff / 1000).toFixed(decimalPlace);
  }
  //返回相差所有
  if (returnType == "all") {
    let days = Math.floor(dateDiff / (24 * 3600 * 1000));
    //计算出小时数
    let leave1 = dateDiff % (24 * 3600 * 1000); //计算天数后剩余的毫秒数
    let hours = Math.floor(leave1 / (3600 * 1000));
    //计算相差分钟数
    let leave2 = leave1 % (3600 * 1000); //计算小时数后剩余的毫秒数
    let minutes = Math.floor(leave2 / (60 * 1000));
    //计算相差秒数
    let leave3 = leave2 % (60 * 1000); //计算分钟数后剩余的毫秒数
    let seconds = Math.round(leave3 / 1000);
    return days + "天" + hours + "小时" + minutes + "分钟" + seconds + "秒";
  }
  return undefined;
};

export default {
  formatDate,
  addDate,
  addHours,
  addMinutes,
  getNowDate,
  getNowTime,
  getNow,
  toDate,
  getTimeDifference,
};
