<!--
 * FilePath     : \src\autoPages\handover\components\HandoverRichText.vue
 * Author       : 郭鹏超
 * Date         : 2023-03-24 11:17
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-25 11:23
 * Description  : 交接班SBAR富文本组件
 * CodeIterationRecord: 
 组件使用介绍：
 props:
   1)value:组件显示内容 必传 类型:Object  双向绑定
   示例:
   {
    "situation": {
        "title": "S-现状",
        "value": "<p><strong>(1)一般疾病情况</strong><br /><strong>出(离)院方式</strong><br />医嘱离院<br /><strong>饮食状况</strong><br />经口<br />普食<br /><strong>导管</strong><br />腹部胆肠前引流管1</p>",
        "type": "T",
        "wordNumber": 2000,
        "disabled": false
    },
    "bodyPartImage": {
        "title": "人体图",
        "value": "",
        "type": "I",
        "wordNumber": 2000,
        "disabled": false
    }
}
-->
<template>
  <div class="handover-rich-text">
    <div
      v-for="(item, index) in handoverRichTextViewKeyArr"
      :key="index"
      class="handover-rich-text-item"
      :style="{ width: 'calc(' + 100 / handoverRichTextViewKeyArr.length + '% - 1px)' }"
    >
      <div class="title">{{ handoverRichTextView[item].title }}</div>
      <rich-text
        v-if="handoverRichTextView[item].type == 'T'"
        :disabled="handoverRichTextView[item].disabled"
        :wordNumber="handoverRichTextView[item].wordNumber"
        v-model="handoverRichTextView[item].value"
        @getNumberBoole="checkNumber"
      ></rich-text>
      <div v-if="handoverRichTextView[item].type == 'I'" class="body-img">
        <img
          v-if="handoverRichTextView[item].value"
          :src="'data:image/jpeg;base64,' + handoverRichTextView[item].value"
        />
      </div>
    </div>
  </div>
</template>

<script>
import richText from "@/components/RichText";
export default {
  components: {
    richText,
  },
  props: {
    value: {
      type: Object,
      require: true,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      handoverRichTextView: {},
      handoverRichTextViewKeyArr: [],
    };
  },
  watch: {
    value: {
      handler(newValue) {
        this.handoverRichTextViewKeyArr = Object.keys(newValue).filter((key) => newValue[key]);
        this.handoverRichTextView = newValue;
      },
      immediate: true,
      deep: true,
    },
    handoverRichTextView: {
      handler(newValue) {
        this.$emit("input", newValue);
      },
      deep: true,
    },
  },
  methods: {
    /**
     * description: 检核字符长度
     * param {*} flag
     * return {*}
     */
    checkNumber(flag) {
      flag || this._showTip("warning", "请精简内容!");
    },
  },
};
</script>

<style lang="scss">
.handover-rich-text {
  height: 100%;
  width: 100%;
  background-color: #fff;
  .handover-rich-text-item {
    float: left;
    height: 100%;
    margin-right: 1px;
    .title {
      padding-left: 5px;
      font-size: 16px;
      height: 25px;
      border: 1px solid #dcdfe6;
      border-bottom: none;
      box-sizing: border-box;
      width: 99%;
    }
    .richText-body {
      margin: 0;
      box-sizing: border-box;
      height: calc(100% - 27px) !important;
      width: 99% !important;
      .richText-input {
        p {
          padding: 0;
        }
      }
    }
    .body-img {
      border: 1px solid #dcdfe6;
      height: calc(100% - 25px);
      width: 99%;
      box-sizing: border-box;
      img {
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
        position: relative;
        top: 35%;
      }
    }
  }
}
</style>