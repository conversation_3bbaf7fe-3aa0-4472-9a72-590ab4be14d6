<!--
 * FilePath     : \src\pages\equipmentBinding\index.vue
 * Author       : 孟昭永
 * Date         : 2022-12-08 10:00
 * LastEditors  : 孟昭永
 * LastEditTime : 2022-12-13 11:05
 * Description  : 妇幼重症系统设备绑定界面
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="equipment-binding">
    <div slot="header">床旁设备绑定由重症监护系统提供，请先确认该患者是否已维护至重症监护系统</div>
    <iframe class="iframe-binding" :src="url"></iframe>
  </base-layout>
</template>

<script>
import { mapGetters } from "vuex";
import baseLayout from "@/components/BaseLayout";
import { GetOneSettingByTypeAndCode } from "@/api/Setting";
export default {
  components: {
    baseLayout,
  },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
    }),
  },
  data() {
    return {
      url: undefined,
    };
  },
  watch: {
    patient(newPatient) {
      if (newPatient) {
        this.init();
      }
    },
  },
  create() {
    this.init();
  },
  methods: {
    init() {
      let params = {
        settingType: 235,
        settingCode: "URLInstrumentBinding",
      };
      GetOneSettingByTypeAndCode(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          var urlTemp = result.data.typeValue;
          if (this.patient && this.patient.chartNo) {
            this.url = urlTemp.replace(/hisid=/, "hisid=" + this.patient.chartNo);
          }
        }
      });
    },
  },
};
</script>

<style lang="scss">
.equipment-binding {
  .iframe-binding {
    height: 98%;
    width: 99%;
    border: 0px;
    background-color: #ffffff;
  }
}
</style>