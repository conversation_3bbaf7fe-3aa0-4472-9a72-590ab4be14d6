<!--
 * FilePath     : \src\autoComponents\NavMenu.vue
 * Author       : 苏军志
 * Date         : 2020-07-04 17:41
 * LastEditors  : 苏军志
 * LastEditTime : 2022-10-22 16:11
 * Description  : 
--> 
<template>
  <div class="nav-menu">
    <label v-for="navMenu in navMenus" :key="navMenu[menuID]">
      <el-submenu
        :popper-append-to-body="true"
        v-if="navMenu[children]"
        :key="navMenu[menuID]"
        :index="navMenu[menuID] + ''"
      >
        <template slot="title">
          <i :class="navMenu[iconName]" v-if="navMenu[parentID] === 0"></i>
          <span class="item_title">{{ navMenu[menuName] }}</span>
        </template>
        <nav-menu
          :navMenus="navMenu[children]"
          :menuID="menuID"
          :parentID="parentID"
          :menuName="menuName"
          :router="router"
          :iconName="iconName"
          :children="children"
        ></nav-menu>
      </el-submenu>
      <el-menu-item v-else :key="navMenu[menuID]" :index="navMenu[router]">
        <!-- 只有根菜单才显示图标 -->
        <i :class="navMenu[iconName]" v-if="navMenu[parentID] === 0"></i>
        <span class="item_title">{{ navMenu[menuName] }}</span>
      </el-menu-item>
    </label>
  </div>
</template>
<script>
export default {
  name: "NavMenu",
  props: {
    navMenus: {
      require: true,
    },
    menuID: {
      require: true,
      type: String,
    },
    parentID: {
      require: true,
      type: String,
    },
    menuName: {
      require: true,
      type: String,
    },
    router: {
      require: true,
      type: String,
    },
    iconName: {
      require: true,
      type: String,
    },
    sort: {
      type: String,
    },
    children: {
      require: true,
    },
  },
};
</script>
<style lang="scss">
.el-menu {
  .item_title {
    display: inline-block;
  }

  &.el-menu--collapse {
    .item_title {
      display: block;
    }
  }
}
</style>