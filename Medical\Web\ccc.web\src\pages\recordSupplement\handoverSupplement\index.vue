<!--
 * FilePath     : \ccc.web\src\pages\recordSupplement\handoverSupplement\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-03-28 09:07
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-06-06 15:06
 * Description  : 交班补录新旧功能切换页面
-->
<template>
  <base-layout header-height="auto" class="handover-record-supplement">
    <!-- 补录病人头 -->
    <search-patient-data
      slot="header"
      @selectPatientData="selectPatient"
      @change="patientInfo = undefined"
    ></search-patient-data>
    <div class="layout-wrap">
      <component v-if="patientInfo && componentName" :is="componentName" :patientInfo="patientInfo"></component>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout.vue";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData";
import handoverSupplementBak from "./handoverSupplementBak.vue";
import handoverCommon from "@/autoPages/handover/components/handoverCommon.vue";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  components: {
    searchPatientData,
    baseLayout,
    handoverSupplementBak,
    handoverCommon,
  },
  data() {
    return {
      componentName: "",
      patientInfo: undefined,
      newHandoverFlag: false,
    };
  },
  beforeMount() {
    this.getNewHandoverFlag();
  },
  methods: {
    selectPatient(val) {
      this.patientInfo = val;
      this.getComponentName();
    },
    /**
     * description: 获取新版交班开关
     * return {*}
     */
    getNewHandoverFlag() {
      let param = {
        settingTypeCode: "NewHandoverFlag",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.newHandoverFlag = response.data;
        }
      });
    },
    /**
     * description: 依配置切换新旧记录编辑
     * param {*} data
     * return {*}
     */
    getComponentName() {
      this.componentName = this.newHandoverFlag ? "handoverCommon" : "handoverSupplementBak";
    },
  },
};
</script>
<style lang="scss">
.handover-record-supplement {
  height: 100%;
  .layout-wrap {
    height: 100%;
  }
}
</style>