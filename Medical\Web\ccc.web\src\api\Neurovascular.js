/*
 * FilePath     : \ccc.mobilee:\Core3.1\Medical\Web\ccc.web\src\api\Neurovascular.js
 * Author       : 郭鹏超
 * Date         : 2022-05-14 11:13
 * LastEditors  : 郭鹏超
 * LastEditTime : 2022-05-14 11:14
 * Description  :神经血管评估
 * CodeIterationRecord:
 */
import http from "../utils/ajax";
const baseUrl = "/Neurovascular";

const urls = {
  GetNeurovascularAssesssView: baseUrl + "/GetNeurovascularAssesssView",
  NeurovascularSave: baseUrl + "/NeurovascularSave",
  GetNeurovascularTableView: baseUrl + "/GetNeurovascularTableView",
  DeleteNeurovascular: baseUrl + "/DeleteNeurovascular"
};
//获取评估模板
export const GetNeurovascularAssesssView = params => {
  return http.get(urls.GetNeurovascularAssesssView, params);
};
//神经血管保存
export const NeurovascularSave = params => {
  return http.post(urls.NeurovascularSave, params);
};
//获取神经血管表格数据
export const GetNeurovascularTableView = params => {
  return http.get(urls.GetNeurovascularTableView, params);
};
//删除神经血管
export const DeleteNeurovascular = params => {
  return http.get(urls.DeleteNeurovascular, params);
};
