<!--
 * FilePath     : \ccc.web\src\pages\formRecord\index.vue
 * Author       : 杨欣欣
 * Date         : 2022-05-31 17:00
 * LastEditors  : 张现忠
 * LastEditTime : 2024-12-31 09:42
 * Description  : 表单评估页面
 * CodeIterationRecord: 
-->
<template>
  <specific-care
    v-model="showTemplateFlag"
    :drawerTitle="drawerTitle"
    :showRecordArr="showRecordArr"
    :recordTitleSlotFalg="true"
    :handOverFlag="handOverArr"
    :nursingRecordFlag="nursingRecordArr"
    :previewFlag="!checkResult"
    :informPhysicianFlag="informPhysicianArr"
    class="patient-form"
    v-loading="loading"
    element-loading-text="加载中……"
    @mainAdd="saveRecordPrepare"
    @save="saveForm"
    @getHandOverFlag="getHandOverFlag"
    @getInformPhysicianFlag="getInformPhysicianFlag"
    @getNursingRecordFlag="getNursingRecordFlag"
    @cancel="openOrCloseDrawer(false)"
  >
    <!-- 头部区域 -->
    <div slot="record-title">
      <label>日期：</label>
      <el-date-picker
        class="date-picker"
        v-model="recordDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        @change="getTableView()"
      ></el-date-picker>
    </div>
    <!-- 表格区域 -->
    <div slot="main-record">
      <el-table :data="recordList" height="100%" border stripe>
        <el-table-column label="日期" width="110" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessDate, type: 'date' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="时间" width="60" align="center">
          <template slot-scope="scope">
            <span v-formatTime="{ value: scope.row.assessTime, type: 'time' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="patientFormType" label="表单类别" min-width="100" align="center"></el-table-column>
        <el-table-column prop="stationName" label="评估病区" min-width="70" align="center"></el-table-column>
        <el-table-column prop="departmentName" label="评估科室" min-width="70" align="center"></el-table-column>
        <el-table-column prop="recordContent" label="护理记录单内容" min-width="200"></el-table-column>
        <el-table-column prop="userName" label="评估人" min-width="40" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" width="70" align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <div @click.stop="saveRecordPrepare(scope.row)" class="iconfont icon-edit"></div>
            </el-tooltip>
            <el-tooltip content="删除">
              <div @click.stop="recordDelete(scope.row)" class="iconfont icon-del"></div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 新增/修改区域 -->
    <base-layout
      header-height="auto"
      slot="drawer-content"
      v-loading="layoutLoading"
      :element-loading-text="layoutText"
    >
      <!-- 增改区域头部 -->
      <div slot="header">
        <span class="label">评估日期:</span>
        <el-date-picker
          class="date-picker"
          v-model="assessDate"
          type="date"
          :clearable="false"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
        ></el-date-picker>
        <el-time-picker
          class="time-picker"
          v-model="assessTime"
          :clearable="false"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="选择时间"
        ></el-time-picker>
        <span class="label">评估病区:</span>
        <station-selector v-model="stationID" label="" width="160"></station-selector>
        <dept-selector label="" width="140" v-model="departmentListID" :stationID="stationID"></dept-selector>
        <span class="label">表单类别:</span>
        <el-select v-model="selectType" :disabled="!addFlag" placeholder="请选择" @change="getFormAssessTemplate">
          <el-option
            v-for="(formType, index) in formTypes"
            :key="index"
            :label="formType.description"
            :value="formType.settingValue"
          ></el-option>
        </el-select>
      </div>
      <!-- 增改区域匿名插槽部分，模板组件 -->
      <tabs-layout
        ref="tabsLayout"
        :template-list="templateDatas"
        @change-values="changeValues"
        @button-click="buttonClick"
      />
    </base-layout>
    <div slot="drawer-dialog">
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :title="buttonName"
        :visible.sync="showButtonDialog"
        fullscreen
        custom-class="no-footer"
      >
        <iframe v-if="showButtonDialog" ref="buttonDialog" width="100%" height="100%"></iframe>
      </el-dialog>
    </div>
  </specific-care>
</template>
<script>
import specificCare from "@/components/specificCare";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import tabsLayout from "@/components/tabsLayout/index";
import baseLayout from "@/components/BaseLayout";
import { mapGetters } from "vuex";
import { GetClinicSettingByTypeCode, GetBringSettingsByTypeValue } from "@/api/Setting.js";
import {
  GetPatientFormRecordView,
  GetAssessView,
  GetButtonData,
  AddPatientForm,
  UpdatePatientForm,
  DeleteRecord,
} from "@/api/PatientForm";
export default {
  components: {
    specificCare,
    stationSelector,
    deptSelector,
    tabsLayout,
    baseLayout,
  },
  props: {
    supplemnentPatient: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getPatientInfo",
      token: "getToken",
    }),
  },
  data() {
    return {
      //页面加载
      loading: false,
      layoutLoading: false,
      layoutText: undefined,
      //组件变量
      showTemplateFlag: false,
      drawerTitle: undefined,
      showRecordArr: [true, false],
      handOverArr: [true, false],
      informPhysicianArr: [true, false],
      nursingRecordArr: [false, false],
      //顶部时间变量
      recordDate: undefined,
      //评估模板变量
      formTypes: [],
      selectType: undefined,
      assessDate: undefined,
      assessTime: undefined,
      stationID: undefined,
      departmentListID: undefined,
      templateDatas: [],
      assessDatas: [],
      showButtonDialog: false,
      buttonName: "",
      addFlag: true,
      buttonAssessListID: "",
      //表格变量
      recordList: [],
      recordID: undefined,
      checkResult: true,
      //补录相关
      supplementFlag: undefined,
      patient: undefined,
    };
  },
  watch: {
    "patientInfo.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.patient = this.patientInfo;
          this.supplementFlag = undefined;
        }
      },
    },
    //补录病人信息
    "supplemnentPatient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.patient = this.supplemnentPatient;
          this.supplementFlag = "*";
        }
      },
    },
    "patient.inpatientID": {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getTableView();
        }
      },
    },
    showButtonDialog(newVal) {
      if (!newVal) {
        this.updateButton(this.buttonAssessListID);
      }
    },
  },
  mounted() {
    //跳转不允许切换病人  右键进入除外
    if (Object.keys(this.$route.query).length && !this.$route.query.shortCutFlag) {
      this._sendBroadcast("setPatientSwitch", false);
    } else {
      this._sendBroadcast("setPatientSwitch", true);
    }
    //获取表单类别配置
    this.getFormTypesBySettingTypeCode();
  },
  methods: {
    /**
     * description: 取得带入护理记录配置
     * param {*}
     * return {*}
     */
    GetBringToNursingRecord() {
      let params = {
        settingTypeCode: "PatientFormAutoInterventionToRecord",
      };
      GetBringToNursingRecordFlagSetting(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.settingNursingRecord = response.data;
        }
      });
    },
    /**
     * description: 弹窗开关函数
     * param {*} flag
     * param {*} title
     * return {*}
     */
    openOrCloseDrawer(flag, title = "") {
      this.showTemplateFlag = flag;
      this.drawerTitle =
        this.patient.bedNumber +
        "床-" +
        this.patient.patientName +
        "【" +
        this.patient.gender +
        "-" +
        (this.patient.ageDetail ? this.patient.ageDetail : "") +
        "】-- " +
        title;
    },
    /**
     * description: 获取组件选中值
     * param {*} details
     * return {*}
     */
    changeValues(details) {
      this.assessDatas = details;
    },
    /**
     * description: 组件回传交班flag
     * param {*} flag
     * return {*}
     */
    getHandOverFlag(flag) {
      this.handOverArr[1] = flag;
    },
    /**
     * description: 获取表格数据
     * param {*}
     * return {*}
     */
    getTableView() {
      if (!this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
      };
      if (this.recordDate) {
        params.date = this.recordDate;
      }
      this.loading = true;
      GetPatientFormRecordView(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.recordList = res.data;
        }
      });
    },
    /**
     * description: 记录新增或修改前的数据准备
     * param {*} record 主记录对象
     * return {*}
     */
    async saveRecordPrepare(record) {
      this.checkResult = true;
      this.templateDatas = [];
      this.addFlag = true;
      this.openOrCloseDrawer(true, record ? "修改" : "新增");
      this.$set(this.handOverArr, 1, false);
      this.$set(this.nursingRecordArr, 1, false);

      if (record) {
        //是否仅本人操作
        this.checkResult = await this._common.checkActionAuthorization(this.user, record.nurseID);
        this.addFlag = false;
        this.stationID = record.stationID;
        this.departmentListID = record.departmentListID;
        this.assessDate = record.assessDate;
        this.assessTime = record.assessTime;
        this.recordID = record.patientFormRecordID;
        this.selectType = record.fromRecordsCode;
        this.$set(this.informPhysicianArr, 1, record.informPhysician ? true : false);
        this.$set(this.handOverArr, 1, record.bringToShift);
        this.$set(this.nursingRecordArr, 1, record.bringToNursingRecord);
      } else {
        this.stationID = this.patient.stationID;
        this.departmentListID = this.patient.departmentListID;
        this.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.assessTime = this._datetimeUtil.getNowTime("hh:mm");
        this.selectType = undefined;
        this.recordID = undefined;
        this.$set(this.informPhysicianArr, 1, false);
      }

      await this.getFormAssessTemplate();
    },
    /**
     * description: 根据SettingTypeCode获取表单类别
     * param {*}
     * return {*}
     */
    async getFormTypesBySettingTypeCode() {
      let params = {
        settingTypeCode: "PatientForm",
      };
      await GetClinicSettingByTypeCode(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.formTypes = res.data;
        }
      });
    },
    /**
     * description: 获取评估模板及带入配置
     * param {*}
     * return {*}
     */
    async getFormAssessTemplate() {
      this.templateDatas = [];
      if (!this.selectType) {
        return;
      }
      let selectedForm = this.formTypes.find((item) => {
        return item.settingValue == this.selectType;
      });
      let params = {
        recordsCode: this.selectType,
        age: this.patient.age,
        gender: this.patient.genderCode,
        departmentListID: this.patient.departmentListID,
        stationID: this.patient.stationID,
        dateOfBirth: this.patient.dateOfBirth,
        inpatientID: this.patient.inpatientID,
      };
      if (selectedForm) {
        params.typeValue = selectedForm.typeValue;
      }
      if (this.recordID) {
        params.recordID = this.recordID;
      }
      this.layoutLoading = true;
      this.layoutText = "加载中……";
      await GetAssessView(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.templateDatas = res.data.layoutGroupLists;
          if (this.addFlag) {
            this.$set(this.handOverArr, 1, res.data.bringToShift);
            this.$set(this.nursingRecordArr, 1, res.data.bringToNursingRecord);
          }
          this.layoutLoading = false;
          this.layoutText = "";
        }
      });
    },
    /**
     * description: 记录保存
     * param {*}
     * return {*}
     */
    saveForm() {
      if (!this.selectType) {
        this._showTip("warning", "请选择表单类别");
        return;
      }
      if (!this.patient || Object.keys(this.selectType).length == 0) {
        return;
      }
      let params = {
        patientFormRecordID: this.recordID,
        inpatientID: this.patient.inpatientID,
        stationID: this.stationID,
        departmentListID: this.departmentListID,
        assessDate: this.assessDate,
        assessTime: this.assessTime,
        fromRecordsCode: this.selectType,
        bringToShift: this.handOverArr[1],
        bringToNursingRecord: this.nursingRecordArr[1],
        details: this.getDetails(),
        supplementFlag: this.supplementFlag,
        informPhysician: this.informPhysicianArr[1],
      };
      let selectedTypeInfo = this.formTypes.find((m) => {
        return m.settingValue === this.selectType;
      });
      if (selectedTypeInfo) {
        params.patientFormType = selectedTypeInfo.description;
      }
      if (params.details.length == 0) {
        this._showTip("warning", "请选择评估内容后保存!");
        return;
      }
      this.layoutLoading = true;
      this.layoutText = "保存中……";
      if (!this.recordID) {
        AddPatientForm(params).then((res) => {
          this.layoutLoading = false;
          this.layoutText = "";
          if (this._common.isSuccess(res)) {
            this._showTip("success", "保存成功");
            this.assessDatas = [];
            this.openOrCloseDrawer(false);
            this.getTableView();
          }
        });
      } else {
        UpdatePatientForm(params).then((res) => {
          this.layoutLoading = false;
          this.layoutText = "";
          if (this._common.isSuccess(res)) {
            this._showTip("success", "更新成功");
            this.assessDatas = [];
            this.openOrCloseDrawer(false);
            this.getTableView();
          }
        });
      }
    },
    /**
     * description: 组装保存detail数据
     * param {*}
     * return {*}
     */
    getDetails() {
      let details = [];
      if (!this.assessDatas.length) {
        return details;
      }
      if (this.$refs.tabsLayout && !this.$refs.tabsLayout.checkRequire()) {
        return details;
      }
      this.assessDatas.forEach((item) => {
        let detail = {
          assessListID: item.assessListID,
          assessListGroupID: item.assessListGroupID,
        };
        if (item.controlerType.trim() == "C" || item.controlerType.trim() == "R") {
          detail.assessValue = "";
        } else {
          detail.assessValue = item.assessValue;
        }
        details.push(detail);
      });
      return details;
    },
    /**
     * description: 记录删除
     * param {*} record 主记录对象
     * return {*}
     */
    async recordDelete(record) {
      //是否仅本人操作
      this.checkResult = await this._common.checkActionAuthorization(this.user, record.nurseID);
      if (!this.checkResult) {
        this._showTip("warning", "非本人不可操作");
        return;
      }
      if (!record || !record.patientFormRecordID) {
        return;
      }
      this._deleteConfirm("", (flag) => {
        if (flag) {
          let params = {
            recordID: record.patientFormRecordID,
            supplementFlag: this.supplementFlag,
          };
          DeleteRecord(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getTableView();
            }
          });
        }
      });
    },
    /**
     * description: 通知医师标记
     * param {*} flag
     * return {*}
     */
    getInformPhysicianFlag(flag) {
      this.informPhysicianArr[1] = flag;
    },
    /**
     * description: 带入护理记录标记回传
     * param {*} flag
     * return {*}
     */
    getNursingRecordFlag(flag) {
      this.nursingRecordArr[1] = flag;
    },
    /**
     * description: 评估模板打开专项
     * param {*} content
     * return {*}
     */
    buttonClick(content) {
      this.buttonAssessListID = content.assessListID;
      this.buttonName = content.itemName;
      let url = content.linkForm;
      if (!url) {
        return;
      }
      if (url.indexOf("?") == -1) {
        url += "?bedNumber=" + this.patient.bedNumber.replace(/\+/g, "%2B");
      } else {
        url += "&bedNumber=" + this.patient.bedNumber.replace(/\+/g, "%2B");
      }
      url += "&userID=" + this.user.userID + "&token=" + this.token + "&isDialog=true";
      this.showButtonDialog = true;
      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => {
        this.$refs.buttonDialog.contentWindow.location.replace(url);
      });
    },

    /**
     * description: 添加完更新按钮数据
     * param {*} assessListID
     * return {*}
     */
    async updateButton(assessListID) {
      let item = await this.getButtonValue(assessListID);
      if (!item) {
        return;
      }
      for (let i = 0; i < this.templateDatas.length; i++) {
        for (let j = 0; j < this.templateDatas[i].groups.length; j++) {
          var group = this.templateDatas[i].groups[j];
          if (group.controlerType.trim() == "B" || group.controlerType.trim() == "BR") {
            if (group.assessListID == assessListID) {
              this.$set(group, "assessValue", item.assessValue);
              this.$set(group, "linkForm", item.linkForm);
              return;
            }
          }
          for (let k = 0; k < group.contents.length; k++) {
            var content = group.contents[k];
            if (content.controlerType.trim() == "B" || content.controlerType.trim() == "BR") {
              if (content.assessListID == assessListID) {
                this.$set(content, "assessValue", item.assessValue);
                this.$set(content, "linkForm", item.linkForm);
                return;
              }
            }
            // 判断三阶里是否有按钮
            if (content.childList) {
              for (let m = 0; m < content.childList.length; m++) {
                var child = content.childList[m];
                if (child.controlerType.trim() == "B" || child.controlerType.trim() == "BR") {
                  if (child.assessListID == assessListID) {
                    this.$set(child, "assessValue", item.assessValue);
                    this.$set(child, "linkForm", item.linkForm);
                    return;
                  }
                }
              }
            }
          }
        }
      }
    },
    /**
     * description: 更新按钮回显数据
     * params {*}
     * return {*}
     * param {*} assessListID
     */
    async getButtonValue(assessListID) {
      let item = undefined;
      let params = {
        inpatientID: this.patient.inpatientID,
        recordsCode: this.selectType,
        assessListID: assessListID,
        sourceID: this.sourceID,
        sourceType: this.sourceType,
      };
      await GetButtonData(params).then((result) => {
        if (this._common.isSuccess(result) && result.data) {
          item = result.data;
        }
      });
      return item;
    },
  },
};
</script>
<style lang="scss" >
.patient-form {
  .date-picker {
    width: 120px;
  }
  .time-picker {
    width: 80px;
  }
}
</style>