<template>
  <div class="shift-selector">
    <span :class="{ label: label }">{{ label ? label : $t("label.shift") }}</span>
    <el-select
      :disabled="disabled"
      v-model="selected"
      :placeholder="$t('placeholder.shift')"
      @change="changeValue"
      :style="style"
    >
      <el-option
        v-for="(shift, index) in shiftList"
        :key="index"
        :label="shift.shiftName"
        :value="shift.id"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
import { GetByStationID, GetShiftByNurseID } from "@/api/StationShift";
export default {
  props: {
    label: {
      type: String,
      default: this.shift,
    },
    value: {},
    disabled: {
      type: Boolean,
      default: false,
    },
    stationID: {
      required: true,
    },
    width: {
      type: String,
      default: "120px",
    },
  },
  watch: {
    stationID: {
      immediate: true,
      async handler(newVal) {
        if (newVal) {
          if (!this.disabled && this.selected) {
            this.$emit("input", undefined);
          }
          await this.init();
        }
      },
    },
    value: {
      immediate: true,
      handler(newVal, oldVal) {
        // if (newVal) {
        this.selected = newVal;
        // }
        this.$emit("input", this.selected);
      },
    },
  },
  data() {
    return {
      selected: "",
      shiftList: [],
    };
  },
  computed: {
    style() {
      return {
        width: this._convertUtil.getHeigt(this.width, true),
      };
    },
  },
  async activated() {
    await this.init();
  },
  methods: {
    async init() {
      // 进行初始化
      if (this.stationID) {
        this.$emit("input", undefined);
        let params = {
          stationID: this.stationID,
          index: Math.random(),
        };
        await GetByStationID(params).then((result) => {
          if (this._common.isSuccess(result)) {
            this.shiftList = result.data;
          }
        });
        // 如果没传值，取当前护士默认派班班别
        if (!this.value) {
          await this.getShiftIDByNurseID();
        }
      }
    },

    getShiftIDByNurseID() {
      let params = {
        stationID: this.stationID,
        index: Math.random(),
      };
      GetShiftByNurseID(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data) {
            this.selected = result.data;
            this.changeValue(this.selected);
          }
        }
      });
      this.changeValue(this.selected);
    },
    changeValue(shiftID) {
      //抛出事件
      this.$emit("input", shiftID);
      this.$emit("select", shiftID);
      let shift = this.shiftList.find((shift) => {
        return shift.id == shiftID;
      });
      if (shift) {
        this.$emit("select-item", shift);
      }
    },
  },
};
</script>
<style lang="scss">
.shift-selector {
  display: inline-block;
  .label {
    margin-left: 5px;
  }
  .el-select {
    .el-input.is-disabled {
      .el-input__inner {
        color: #606266;
      }
    }
    .el-input__inner {
      padding-left: 5px;
    }
  }
}
</style>
