/*
 * FilePath     : \src\api\WoundRecord.js
 * Author       : 李青原
 * Date         : 2020-05-19 09:34
 * LastEditors  : 来江禹
 * LastEditTime : 2023-04-24 17:16
 * Description  :
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PatientWoundRecord";

export const urls = {
  SavePatientWound: baseUrl + "/SavePatientWound",
  GetWoundRecordList: baseUrl + "/GetWoundRecordList",
  GetWoundCareMainsByID: baseUrl + "/GetWoundCareMainsByID",
  SavePatientWoundCare: baseUrl + "/SavePatientWoundCare",
  GetWoundAssessView: baseUrl + "/GetWoundAssessView",
  GetWoundRecordsCodeInfo: baseUrl + "/GetWoundRecordsCodeInfo",
  DeleteWoundCare: baseUrl + "/DeleteWoundCare",
  GetNewWoundCode: baseUrl + "/GetNewWoundCode",
  UpdatePatientWoundRecord: baseUrl + "/UpdatePatientWoundRecord",
  DeleteWoundByID: baseUrl + "/DeleteWoundByID",
  EndWound: baseUrl + "/EndWound",
  GetWoundNumByHandover: baseUrl + "/GetWoundNumByHandover",
  DelPatientWoundDataByHandoverID: baseUrl + "/DelPatientWoundDataByHandoverID",
  GetImgPreviewData: baseUrl + "/GetImgPreviewData"
};
// 保存伤口
export const SavePatientWound = params => {
  return http.post(urls.SavePatientWound, params);
};
// 获取伤口记录列表
export const GetWoundRecordList = params => {
  return http.get(urls.GetWoundRecordList, params);
};
// 获取伤口记录列表
export const GetWoundCareMainsByID = params => {
  return http.get(urls.GetWoundCareMainsByID, params);
};
// 获取伤口记录列表
export const SavePatientWoundCare = params => {
  return http.post(urls.SavePatientWoundCare, params);
};
// 获取伤口评估项目动态呈现
export const GetWoundAssessView = param => {
  return http.get(urls.GetWoundAssessView, param);
};
// 获取伤口对应的DepartmentToAssessInfo记录
export const GetWoundRecordsCodeInfo = param => {
  return http.get(urls.GetWoundRecordsCodeInfo, param);
};
// 根据伤口评估主表ID删除伤口评估
export const DeleteWoundCare = param => {
  return http.post(urls.DeleteWoundCare, qs.stringify(param));
};
// 根据单位代码和病人在院号和身体部位获取伤口代号
export const GetNewWoundCode = param => {
  return http.get(urls.GetNewWoundCode, param);
};
// 修改伤口记录表
export const UpdatePatientWoundRecord = param => {
  return http.post(urls.UpdatePatientWoundRecord, param);
};

// 伤口删除
export const DeleteWoundByID = params => {
  return http.post(urls.DeleteWoundByID, qs.stringify(params));
};
// // 伤口停止
export const EndWound = params => {
  return http.post(urls.EndWound, params);
};

// 伤口数量
export const GetWoundNumByHandover = params => {
  return http.get(urls.GetWoundNumByHandover, params);
};

export const DelPatientWoundDataByHandoverID = params => {
  return http.get(urls.DelPatientWoundDataByHandoverID, params);
};
export const GetImgPreviewData = params =>{
  return http.get(urls.GetImgPreviewData,params);
};
