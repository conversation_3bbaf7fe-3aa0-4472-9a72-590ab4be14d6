/*
 * FilePath     : \src\utils\bjca\NotificationSign.js
 * Author       : 张现忠
 * Date         : 2023-07-01 09:18
 * LastEditors  : 来江禹
 * LastEditTime : 2025-05-08 08:41
 * Description  : 告知书签名：调用BJCA客户端完成签名
 * CodeIterationRecord:
 *    2023-07-05 3614-作为IT人员，我需要验证银川市一告知书患者签字功能，保证患者签字功能正常，以利于五级电子病历评审（7） -zxz
 */

import {
  AS_InitSign, AS_SetBusinessParam, AS_SetSignAlg, AS_SetSignerInfo,
  AS_GetBusinessString, AS_GetSignPackage, AS_SetEvidenceCollectionModel,
  AS_AddSignEvidenceData, AS_GetSignEvidenceData, AS_SetPosKW,
  AS_SetPosXY, GetErrorMessage, AS_SetPdfGenerateData, AS_GetLastError
} from "./AsAppComSync";
import showTip from "@/utils/toast";
//KWRule 默认是关键字定位
let ruleType = 0;
//签名类型（现使用pdf签名）|0 为 pdf 签名,1 为数据签名
const signType = 0;
//渠道号|后期上线可能要调整
const channel = "999999";
//工单号|后期上线可能要调整
const wono = "12345678";
//测试写入本地文件配置
//let localPdfPath = "C:\\Program Files (x86)\\BJCAClient\\AnySign\\htmldemo\\ASAppComDemo(html)\\V2PDFSignInterface\\";
/**
 * description: 告知书签名
 * return {*}
 * param {*} signer ：签名人信息{UName，IDType：签名人证件类型 1 身份证2 军官证3 护照4户口本 5 其他，IDNumber}
 * param {*} orginPdfPath：签名pdf地址
 * param {*} kwRule 签名规则
 */
export default function (signer, pdfBase64, kwRule) {
  //状态码 |签名流程中的执行状态
  let status;
  let errorMessage;
  //signType
  status = AS_InitSign(signType);
  if (status != 0 && status != '0') {
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `签名初始化失败，${errorMessage}`);
    return;
  }
  //设置工单号|/工单号，仅 pdf 签名设置
  status = AS_SetBusinessParam(1, wono);
  if (status != 0 && status != '0') {
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `签名设置工单号失败，${errorMessage}`);
    return;
  }
  //设置渠道号|现在还在使用测试配置|如果渠道号获悉，需要调整变量channel
  status = AS_SetBusinessParam(2, channel);
  if (status != 0 && status != '0') {
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `签名设置渠道号失败，${errorMessage}`);
    return;
  }
  //设置签名算法和时间戳 -默认，可以不写
  //签名算法，1，RSA 签名；2，SM2 签名。默认为2
  //是否使用时间戳，0 为不使用，1 为使用，默认为1
  status = AS_SetSignAlg(2, 1);
  if (status != 0 && status != '0') {
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `签名设置签名算法失败，${errorMessage}`);
    return;
  }
  //设置签名信息|签名人姓名，证件类别（1：身份证件），身份证号
  status = AS_SetSignerInfo(signer.UName, signer.IDType, signer.IDNumber);
  if (status != 0 && status != '0') {
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `设置签名信息失败，${errorMessage}`);
    return;
  }
  //签名定位方式
  status = setSignRule(ruleType, kwRule);
  if (status != 0 && status != '0') {
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `设置定位方式失败，${errorMessage}`);
    return;
  }
  //设置pdf原文
  status = setPdfOriginText(pdfBase64);
  if (status != 0 && status != '0') {
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `设置原文失败${errorMessage}`);
    return;
  }
  //获取签名数据
  status = getSignEvidenceData();
  if (status != 0 && status != '0') {
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `获取签名包失败${errorMessage}`);
    return;
  }
  //获取加密包
  let businessString = AS_GetBusinessString();
  if (!businessString) {
    status = AS_GetLastError();
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `获取加密包失败${errorMessage}`);
    return;
  }
  // 获取签名后的数据包-Base64  AS_GetSignPackageByDevCert
  let signPkg = AS_GetSignPackage(businessString);
  if (!signPkg) {
    status = AS_GetLastError();
    errorMessage = GetErrorMessage(status);
    showTip.showTip('warning', `获取签名数据包失败${errorMessage}`);
    return;
  }
  return signPkg;
}
/**
 * description: 采集手写板签名数据
 * return {*} 0:正常 ,其他为具体错误的错误码
 */
const getSignEvidenceData = () => {
  // 0 为只采集手写
  // 1 为采集手写和指纹，指纹必须采集
  // 2 为采集手写和指纹，指纹可以不采集s
  // 3 为采集手写和拍照
  // 4 为采集手写、指纹和拍照
  // 5 为采集手写，并优先采集指纹，指纹多次采集失败后，进行拍
  let respStatus = AS_SetEvidenceCollectionModel(2);
  //采集签名证据数据
  respStatus = AS_AddSignEvidenceData();
  if (respStatus != 0 && respStatus != '0') {
    return respStatus;
  }
  //获取采集完成的签名数据 |1 为批注图片 2 为指纹图片 3 为拍照图片 4 为手写轨迹 5 为 OCR 6 为扩展项
  var base64Str = AS_GetSignEvidenceData(4);
  if (!base64Str) {
    return AS_GetLastError();
  }
  return 0;
}
/**
 * description: 设置签名规则
 * return {*}
 * param {*} ruleType 签名规则类型
 * param {*} signRule 具体的签名参数
 */
const setSignRule = (ruleType, signRule) => {
  let errorStatusCode = 0;
  //pdf 签名定位方式 0:关键字 1:坐标2: 规则号
  switch (ruleType) {
    case 0: //"责任护士签名",1, 70, 2, 100, 50
      var kw = signRule.kwRule;
      errorStatusCode = AS_SetPosKW(kw.kw, kw.kwIndex, kw.xOffset, kw.yOffset, kw.width, kw.height);
      break;
    case 1:
      //未测试
      var xyzRule = signRule.XYZRule;
      errorStatusCode = AS_SetPosXY(xyzRule.Left, xyzRule.Bottom, xyzRule.Right, xyzRule.Top, xyzRule.Pageno);
      break;
    default:
      break;
  }
  return errorStatusCode;
}
/**
 * description: 设置业务数据|pdf原文
 * return {*}
 * param {*} pdfPath
 */
const setPdfOriginText = function (base64Str) {
  let genDataBase64 = base64Str;
  //数据原文类型| 设置文档类型 0：xml 1：html 2：pdf
  let genType = 2;
  let genPdfXsltNo = "0";
  //添加生成 PDF 的相关业务数据|0 为成功,其他值为错误码.
  var respStatus = AS_SetPdfGenerateData(genType, genDataBase64, genPdfXsltNo);
  return respStatus;

}