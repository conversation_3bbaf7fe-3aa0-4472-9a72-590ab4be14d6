<template>
  <base-layout class="add-schedule" showFooter v-loading.fullscreen.lock="loading" element-loading-text="保存中……">
    <div slot="header">
      执行日期：{{ addParams.scheduleDate }}
      <span class="label">执行时间：</span>
      <el-time-picker
        v-model="scheduleTime"
        value-format="HH:mm"
        format="HH:mm"
        :clearable="false"
        style="width: 120px"
      ></el-time-picker>
      <div class="switch">
        护理计划：
        <el-switch v-model="switchSchedule" />
      </div>
      <div class="switch">
        PRN：
        <el-switch v-model="switchPRN" />
      </div>
    </div>
    <el-row class="problem-intervention">
      <el-col :span="11">
        <el-table
          :data="addParams.problems"
          border
          stripe
          height="100%"
          ref="problem"
          class="problem"
          @row-click="getIntervention"
          :highlight-current-row="true"
        >
          <el-table-column prop="problem" label="护理问题">
            <template slot-scope="problem">
              {{ problem.row.problem }}
              <span v-if="problem.row.problemSourceInfo && problem.row.problemSourceInfo.content">
                {{ "(" + problem.row.problemSourceInfo.content + ")" }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="13">
        <el-table
          :data="showInterventions"
          border
          stripe
          height="100%"
          class="intervention"
          ref="intervention"
          @select="selectionIntervention"
        >
          <el-table-column type="selection" :width="convertPX(60)" align="center"></el-table-column>
          <el-table-column prop="interventionName" class-name="intervention-name">
            <template slot="header" slot-scope="scope">
              <span class="title">护理措施</span>
              <i class="iconfont icon-add" @click="add(scope)"></i>
            </template>
            <template slot-scope="intervention">
              <el-input v-if="intervention.row.isAdd" v-model="intervention.row.addIntervention"></el-input>
              <span v-else>
                {{ intervention.row.interventionName }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <template slot="footer">
      <el-button @click="close(false)">取消</el-button>
      <el-button type="primary" @click="onSave">确定</el-button>
    </template>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import { nurseAddSchedule, CheckAddScheduleTime } from "@/api/PatientSchedule";
import { GetPatientInterventionInfo } from "@/api/PatientProblem";
export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      scheduleTime: this._datetimeUtil.getNowTime("hh:mm"),
      switchSchedule: false,
      switchPRN: false,
      cacheInterventions: [],
      interventions: [],
      showInterventions: [],
      selectInterventions: [],
      currentPatientProblemID: undefined,
      currentProblemID: undefined,
      loading: false,
    };
  },
  props: {
    addParams: {
      type: Object,
      required: true,
    },
  },
  watch: {
    addParams: {
      immediate: true,
      handler(newValue) {
        this.init();
        if (newValue) {
          if (newValue.problems) {
            let _this = this;
            this.$nextTick(() => {
              _this.$refs.problem.setCurrentRow(newValue.problems[0]);
              _this.getIntervention(newValue.problems[0]);
            });
          }
          this.scheduleTime = newValue.scheduleTime;
        }
      },
    },
    switchSchedule() {
      this.filterInterventions();
    },
    switchPRN() {
      this.filterInterventions();
    },
  },
  methods: {
    init() {
      this.switchSchedule = false;
      this.switchPRN = false;
      this.cacheInterventions = [];
      this.interventions = [];
      this.showInterventions = [];
      this.selectInterventions = [];
      this.currentPatientProblemID = undefined;
      this.currentProblemID = undefined;
    },
    getIntervention(row) {
      if (this.currentPatientProblemID == row.patientProblemID) {
        return;
      }
      this.currentPatientProblemID = row.patientProblemID;
      this.currentProblemID = row.problemID;
      if (this.cacheInterventions && this.cacheInterventions.length > 0) {
        let temp = this.cacheInterventions.find((intervention) => {
          return intervention.patientProblemID == this.currentPatientProblemID;
        });
        if (temp) {
          this.interventions = temp.interventions;
          this.filterInterventions();
          this.$nextTick(() => {
            this.setSelect();
          });
          return;
        }
      }
      let params = {
        patientProblemID: this.currentPatientProblemID,
        problemID: this.currentProblemID,
      };
      if (this.addParams) {
        params.inpatientID = this.addParams.inpatientID;
      }
      // 获取问题对应措施
      GetPatientInterventionInfo(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.interventions = result.data;
          // 护理常规有重复措施，设定唯一值
          for (let i = 0; i < this.interventions.length; i++) {
            this.interventions[i].index = i;
          }
          this.filterInterventions();
          let temp = {
            patientProblemID: this.currentPatientProblemID,
            problemID: this.currentProblemID,
            interventions: this.interventions,
          };
          this.cacheInterventions.push(temp);
          this.$nextTick(() => {
            this.setSelect();
          });
        }
      });
    },
    // 设置已选数据回显
    setSelect() {
      for (let i = 0; i < this.selectInterventions.length; i++) {
        if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
          this.selectInterventions[i].interventions.forEach((row) => {
            for (let j = 0; j < this.showInterventions.length; j++) {
              if (
                this.showInterventions[j].interventionID == row.interventionID &&
                this.showInterventions[j].index == row.index
              ) {
                this.$refs.intervention.toggleRowSelection(this.showInterventions[j]);
              }
            }
          });
          break;
        }
      }
    },
    // 过滤数据
    filterInterventions() {
      if (!this.switchSchedule && !this.switchPRN) {
        this.showInterventions = this.interventions;
        return;
      }
      if (this.switchSchedule) {
        this.showInterventions = this.interventions.filter((intervention) => {
          return intervention.frequency;
        });
      }
      if (this.switchPRN) {
        this.showInterventions = this.interventions.filter((intervention) => {
          return intervention.frequency == "PRN" || intervention.frequency == "prn";
        });
      }
    },
    // 手动添加措施
    add() {
      // 为了确定唯一，临时添加一个guid
      let addTemp = {
        interventionID: this._common.guid(),
        isAdd: true,
        addIntervention: "",
      };
      this.interventions.push(addTemp);
      this.$nextTick(() => {
        // 默认选中
        this.$refs.intervention.toggleRowSelection(addTemp);
      });
      let isExist = false;
      for (let i = 0; i < this.selectInterventions.length; i++) {
        if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
          this.selectInterventions[i].interventions.push(addTemp);
          isExist = true;
          break;
        }
      }
      if (!isExist) {
        let temp = {
          patientProblemID: this.currentPatientProblemID,
          problemID: this.currentProblemID,
          interventions: [addTemp],
        };
        this.selectInterventions.push(temp);
      }
    },
    // 选择措施
    selectionIntervention(interventions) {
      let isExist = false;
      for (let i = 0; i < this.selectInterventions.length; i++) {
        if (this.selectInterventions[i].patientProblemID == this.currentPatientProblemID) {
          if (interventions.length == 0) {
            // 移除
            this.selectInterventions.splice(i, 1);
          } else {
            this.selectInterventions[i].interventions = interventions;
          }
          isExist = true;
          break;
        }
      }
      if (!isExist && interventions.length > 0) {
        let temp = {
          patientProblemID: this.currentPatientProblemID,
          problemID: this.currentProblemID,
          interventions: interventions,
        };
        this.selectInterventions.push(temp);
      }
    },
    onSave() {
      let params = {
        inpatientID: this.addParams.inpatientID,
        stationID: this.addParams.stationID,
        shiftID: this.addParams.shiftID,
        date: this.addParams.scheduleDate,
        time: this.scheduleTime,
      };
      this.loading = true;
      CheckAddScheduleTime(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (result.data) {
            this.save();
          } else {
            this.loading = false;
            this._showTip("warning", "请确认时间点是否大于病人入院时间及符合班别时间");
          }
        }
      });
    },
    async save() {
      let saveData = [];
      this.selectInterventions.forEach((temp) => {
        if (temp.interventions && temp.interventions.length > 0) {
          temp.interventions.forEach((intervention) => {
            let item = {
              InpatientID: this.addParams.inpatientID,
              ProblemID: intervention.problemID,
              PatientProblemID: intervention.patientProblemID ?? "",
              ScheduleDate: this.addParams.scheduleDate,
              ScheduleTime: this.scheduleTime,
            };
            item.TPRFlag = intervention.tprFlag;
            if (intervention.isAdd) {
              if (intervention.addIntervention.length > 0) {
                item.AddIntervention = intervention.addIntervention;
              } else {
                item = undefined;
              }
            } else {
              item.InterventionID = intervention.interventionID;
              item.PatientInterventionID = intervention.patientInterventionID;
            }
            if (item) {
              saveData.push(item);
            }
          });
        }
      });
      if (saveData.length > 0) {
        await nurseAddSchedule(saveData).then((result) => {
          this.loading = false;
          if (this._common.isSuccess(result) && result.data) {
            this._showTip("success", "添加成功！");
            this.close(true);
          } else {
            this._showTip("warning", "排程已存在于当前时间点!");
            this.loading = false;
            this.close(true);
          }
        });
      } else {
        this.loading = false;
        this.close(true);
      }
    },
    close(flag) {
      this.$emit("close", flag);
    },
  },
};
</script>

<style lang="scss">
.add-schedule {
  .base-header {
    .label {
      margin-left: 10px;
    }
    .switch {
      display: inline-block;
      margin-left: 10px;
    }
  }
  .problem-intervention {
    height: 100%;
    .el-col {
      height: 100%;
      td {
        padding: 5px 10px;
      }
    }
    .problem {
      td {
        cursor: pointer;
      }
    }
    .intervention {
      margin-left: 15px;
      width: calc(100% - 15px);
      th.intervention-name .cell {
        display: flex;
        .title {
          flex: auto;
          text-align: center;
        }
        .iconfont {
          margin: 0;
        }
      }
      /* 禁止全选 */
      th .el-checkbox {
        display: none;
      }
    }
  }
}
</style>
