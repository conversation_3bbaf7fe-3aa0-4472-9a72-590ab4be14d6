<!--
 * FilePath     : \src\components\imgPreview.vue
 * Author       : 来江禹
 * Date         : 2023-04-21 09:53
 * LastEditors  : 马超
 * LastEditTime : 2024-04-21 15:33
 * Description  : 图片预览业务组件
 * CodeIterationRecord:  
  参数示例：imgPreviewData: {
        recordTitle: "2023-04-14 14:00 伤口部位：左腋下压力性损伤",
        imageList: [
          {
            imageTitle: "开始评估 2023-04-14 14:00 组织类型：上皮组织；渗液：中量渗液；PUSH评分：3；",
            imageSrcList: [
              "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
              "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
            ],
          },
          {
            imageTitle: "例行评估 2023-04-14 14:20 组织类型：上皮组织；渗液：中量渗液；PUSH评分：3；",
            imageSrcList: [
              "https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg",
              "https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg",
            ],
          },
        ],
      },
    };
-->
<template>
  <div class="img-preview">
    <div class="title">
      {{ imgPreviewData.recordTitle }}
    </div>
    <div class="img-body-main">
      <div class="img-body-main-content" v-for="(item, index) in imgPreviewData.imageList" :key="index">
        <p class="img-content">{{ item.imageTitle }}</p>
        <el-image
          class="body-main-image"
          v-for="(childItem, childIndex) in item.imageSrcList"
          :key="childIndex"
          :src="childItem"
          :preview-src-list="showPreviewSrcList"
          @click.native="getPreview(previewSrcList, childIndex, index, imgPreviewData)"
        ></el-image>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    imgPreviewData: {
      type: Object,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      previewSrcList: [],
      showPreviewSrcList: [],
    };
  },
  watch: {
    imgPreviewData: {
      handler(val) {
        if (val.imageList) {
          this.previewSrcList = [];
          val.imageList.forEach((element) => {
            this.previewSrcList = this.previewSrcList.concat(element.imageSrcList);
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /**
     * description: 图片点击放大图，重新放置图片List（左右切换功能）
     * param {*} imgList
     * param {*} childIndex
     * param {*} parentIndex
     * param {*} parentData
     * return {*}
     */
    getPreview(imgList, childIndex, parentIndex, parentData) {
      this.showPreviewSrcList = [];
      let countIndex = 0;
      let beforeLength = 0;
      //取得不同组的图片数组长度，计算点击的图片的workPhoto的下表
      if (parentIndex >= 1) {
        for (let index = 0; index < parentIndex; index++) {
          const countImgList = parentData.imageList[index];
          let length = countImgList.imageSrcList?.length;
          beforeLength += length;
        }
      }
      countIndex = beforeLength + childIndex;
      if (imgList.length == 1) {
        this.showPreviewSrcList.push(imgList[0]);
      } else if (imgList.length == 0) {
        return;
      } else {
        for (let i = 0; i < imgList.length; i++) {
          this.showPreviewSrcList.push(imgList[i + countIndex]);
          if (i + countIndex >= imgList.length - 1) {
            countIndex = 0 - (i + 1);
          }
        }
      }
    },
  },
};
</script>
<style lang="scss">
.img-preview {
  height: 100%;
  .title {
    border-width: 1px 1px 0 1px;
    border-style: solid;
    border-color: #ccc;
    height: 40px;
    padding-left: 20px;
    overflow: auto;
  }
  .img-body-main {
    border: 1px solid #ccc;
    height: calc(100% - 35px);
    overflow: auto;
    .img-body-main-content {
      padding-left: 60px;
      border: 1px solid #ccc;
      .img-content {
        font-size: 18px;
        color: $base-color;
      }
      .body-main-image {
        height: 100px;
        width: 120px;
        margin: 0 15px 10px 15px;
        padding: 5px;
        border: 1px solid #666;
        &:first-child {
          margin-left: 25px;
        }
      }
    }
  }
}
</style>
