<!--
 * FilePath     : \src\pages\transferHisCommon\transferExamLabelPrint.vue
 * Author       : 杨欣欣
 * Date         : 2025-05-13 15:43
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-14 11:41
 * Description  : 检查单打印
 * CodeIterationRecord: 
 -->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { hisHisCommonUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
    }),
  },
  created() {
    this.url = hisHisCommonUrl() + "examinationRequisitionLabelPrint?token=" + this.token;
  },
};
</script>
