<!--
 * FilePath     : \src\pages\dictionaryMaintain\hospitalBuilding\room.vue
 * Author       : 来江禹
 * Date         : 2022-08-13 16:33
 * LastEditors  : 来江禹
 * LastEditTime : 2022-09-12 14:32
 * Description  : 房间维护页面，对楼栋对应楼层、楼层位置，房间进行维护管理
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="hospital-room">
    <div class="hospital-room-header" slot="header">
      <span class="btn-list">
        <el-button type="success" icon="iconfont icon-add" @click="addRoom">新增</el-button>
        <el-button type="primary" icon="iconfont icon-save-button" @click="save">保存</el-button>
        <el-button class="print-button" icon="iconfont icon-back" @click="goBack">返回</el-button>
      </span>
      <progress-view v-if="progressFlag" @closeProgress="progressClose()" :tableData="messageData"></progress-view>
    </div>
    <div class="room-table">
      <el-table :data="roomTabList" border stripe>
        <el-table-column type="index" width="50" label="序号"></el-table-column>
        <el-table-column label="楼栋" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.buildID">{{ scope.row.buildName }}</span>
            <el-select
              v-else
              class="table-build-select"
              v-model="scope.row.buildListID"
              placeholder="请选择楼栋"
              @change="getbuildId(scope.row)"
            >
              <el-option
                v-for="(item, index) in buildList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="楼层" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.floorID">
              {{ scope.row.floorName }}
            </span>
            <el-select
              v-else
              class="table-floor-select"
              v-model="scope.row.floorListID"
              placeholder="请选择楼层"
              @change="getFloorID(scope.row)"
            >
              <el-option
                v-for="(item, index) in scope.row.floorList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="楼层位置" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.locationID">{{ scope.row.locationName }}</span>
            <el-select
              v-else
              class="table-location-select"
              v-model="scope.row.locationListID"
              placeholder="请选择楼层位置"
            >
              <el-option
                v-for="(item, index) in scope.row.locationList"
                :key="index"
                :label="item.Description"
                :value="item.TypeValue"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="房间位置名称" header-align="center" align="left">
          <template slot-scope="scope">
            <span v-if="scope.row.roomID">{{ scope.row.roomName }}</span>
            <el-input
              v-else
              class="table-room-input"
              v-model="scope.row.roomListName"
              placeholder="请输入房间位置名称"
              @change="getroomName()"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" header-align="center" width="80px" align="left">
          <template slot-scope="scope">
            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="deleteRoom(scope.row.roomID, scope.row.roomCode, scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import progressView from "@/components/progressView";
import {
  GetFloorDatas,
  GetLoactionDatas,
  GetRoomDatas,
  SaveBuildDatas,
  DeleteSettingDescriptionRoomDatas,
  GetSettingDescriptionOne,
} from "@/api/WardMaintenance";
export default {
  components: {
    baseLayout,
    stationSelector,
    progressView,
  },
  data() {
    return {
      roomTabList: [],
      floorTabList: [],
      locationTabList: [],
      buildIndex: "",
      floorIndex: "",
      buildList: [],
      floorList: [],
      locationList: [],
      settingTypeCode: "WardRoom",
      code: "",
      successCode: "",
      //进度条开关
      progressFlag: false,
      //进度条配置数据
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
    };
  },
  created() {
    this.refresh();
  },
  methods: {
    /**
     * description: 进度条关闭函数
     * return {*}
     */
    progressClose() {
      this.progressFlag = false;
      this.renewMessageData();
    },
    /**
     * description: 重置进度条
     * return {*}
     */
    renewMessageData() {
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    /**
     * description: 刷新页面数据
     * return {*}
     */
    async refresh() {
      this.roomTabList = [];
      this.buildList = [];
      this.floorTabList = [];
      this.locationTabList = [];
      this.getSettingfloor("WardBuilding");
      this.getSettingLocation("WardBuilding");
      this.getSettingRoom("WardBuilding");
      this.getBuildSelect();
    },
    /**
     * description: 新增按钮实现新增表格行
     * return {*}
     */
    addRoom() {
      let row = {
        buildName: "",
        buildID: "",
        floorName: "",
        floorID: "",
        locationName: "",
        locationID: "",
        roomID: "",
        roomName: "",
        roomCode: "",
      };
      this.roomTabList.push(row);
    },

    /**
     * description: 获取楼栋列下拉框数据
     * return {*}
     */
    async getBuildSelect() {
      let params = {
        SettingTypeCode: "WardBuilding",
      };
      await GetSettingDescriptionOne(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let List = result.data;
          if (List != null) {
            List.forEach((build) => {
              let params = {
                Description: build.description,
                TypeValue: build.typeValue,
              };
              this.buildList.push(params);
            });
          }
        }
      });
    },
    /**
     * description: 获取已经维护好的楼层数据
     * param {*} SettingTypeCode
     * param {*} SettingType
     * return {*}
     */
    async getSettingfloor(SettingTypeCode) {
      let params = {
        SettingTypeCode: SettingTypeCode,
      };
      await GetFloorDatas(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let List = result.data;
          if (List != null) {
            List.forEach((floor) => {
              let params = {
                floorName: floor.floorName,
                floorID: floor.floorID,
                floorCode: floor.floorCode,
              };
              this.floorTabList.push(params);
            });
          }
        }
      });
    },
    /**
     * description: 获取已经维护好的楼层位置下拉框数据
     * param {*} SettingTypeCode
     * return {*}
     */
    async getSettingLocation(SettingTypeCode) {
      let params = {
        SettingTypeCode: SettingTypeCode,
      };
      await GetLoactionDatas(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let List = result.data;
          if (List != null) {
            for (let index = 0; index < List.length; index++) {
              const selectList = List[index];
              let params = {
                locationName: selectList.loactionName,
                locationID: selectList.locationID,
                locationCode: selectList.locationCode,
              };
              this.locationTabList.push(params);
            }
          }
        }
      });
    },
    /**
     * description: 获取当前页面表格数据
     * param {*} SettingTypeCode
     * return {*}
     */
    async getSettingRoom(SettingTypeCode) {
      let params = {
        SettingTypeCode: SettingTypeCode,
      };
      await GetRoomDatas(params).then((result) => {
        if (this._common.isSuccess(result)) {
          let List = result.data;
          if (List != null) {
            for (let index = 0; index < List.length; index++) {
              const rommList = List[index];
              let params = {
                buildName: rommList.buildName,
                buildID: rommList.buildID,
                floorName: rommList.floorName,
                floorID: rommList.floorID,
                locationName: rommList.loactionName,
                locationID: rommList.locationID,
                roomID: rommList.roomID,
                roomName: rommList.roomName,
                roomCode: rommList.roomCode,
              };
              this.roomTabList.push(params);
            }
          }
        }
      });
    },
    /**
     * description: 获取下拉框异动数据，便于保存时写入settingTypeCode字段,并且动态获取楼层列表
     * param {*} index
     * return {*}
     */
    getbuildId(row) {
      if (!row.buildListID) {
        return;
      }
      this.buildIndex = row.buildListID;
      this.$set(row, "floorListID", "");
      this.$set(row, "locationListID", "");
      this.getFloorSelect(row);
    },
    /**
     * description: 根据楼栋TypeValue动态的更新楼层下拉框数据
     * return {*}
     */
    getFloorSelect(row) {
      let floorList = [];
      this.floorList = [];
      this.floorTabList.forEach((floorIndex) => {
        if (floorIndex.floorCode == "WardFloor" + "_" + this.buildIndex) {
          let params = {
            Description: floorIndex.floorName,
            TypeValue: floorIndex.floorID,
          };
          floorList.push(params);
        }
        this.$set(row, "floorList", floorList);
      });
    },
    /**
     * description: 获取楼栋下拉框选取的Typevalue,以便于选择对应的楼层
     * param {*} row
     * return {*}
     */
    getFloorID(row) {
      if (!row.floorListID) {
        return;
      }
      this.floorIndex = row.floorListID;
      this.$set(row, "locationListID", "");
      this.getLocationSelect(row);
    },
    /**
     * description: 获取楼栋对应楼层位置下拉框数据
     * param {*} row
     * return {*}
     */
    getLocationSelect(row) {
      let List = [];
      this.locationList = [];
      this.locationTabList.forEach((locationIndex) => {
        if (locationIndex.locationCode == "WardPartition" + "_" + this.buildIndex + "_" + this.floorIndex) {
          let params = {
            Description: locationIndex.locationName,
            TypeValue: locationIndex.locationID,
          };
          List.push(params);
        }
        this.$set(row, "locationList", List);
      });
    },
    /**
     * description: 获取新增输入框数据以便于保存，实现批量保存
     * return {*}
     */
    getroomName() {
      let modifyDatas = this.roomTabList;
      if (modifyDatas.length == 0) {
        return undefined;
      }
      return modifyDatas;
    },
    /**
     * description: 保存页面新增数据，写入后端
     * return {*}
     */
    async save() {
      let datas = this.getroomName();
      let successMessage = "";
      let failMessage = "";
      for (let index = 0; index < datas.length; index++) {
        const roomData = datas[index];
        this.progressFlag = true;
        //判断下拉框输入框是否有数据没有输入，全部输入执行保存
        if (roomData.buildListID && roomData.floorListID && roomData.locationListID && roomData.roomListName) {
          //是否是空数据，用于弹窗提示
          this.code = true;
          let params = {
            SettingType: "216",
            SettingTypeCode:
              this.settingTypeCode +
              "_" +
              roomData.buildListID +
              "_" +
              roomData.floorListID +
              "_" +
              roomData.locationListID,
            Description: roomData.roomListName,
          };
          let messageItem = roomData.roomListName;
          await SaveBuildDatas(params).then((res) => {
            if (res.code == 1) {
              successMessage = index == 0 ? messageItem : successMessage + "," + messageItem;
            } else {
              failMessage = failMessage + " " + messageItem;
            }
          });
        }
        //配置进度条内容
        let progress = (((index + 1) / datas.length) * 100).toFixed(0);
        //配置进度条内容
        this.messageData[0].value = Number(progress);
        this.messageData[1].value = successMessage;
        this.messageData[2].value = failMessage;
        this.messageData[3].value = "";
      }
      await this.refresh();
    },
    /**
     * description: 删除当前行数据
     * param {*} index
     * param {*} settingTypeCode
     * return {*}
     */
    deleteRoom(index, settingTypeCode, row) {
      if (index == "" && settingTypeCode == "") {
        let index = this.roomTabList.findIndex((room) => room == row);
        if (index >= 0) {
          this.roomTabList.splice(index, 1);
        }
      } else {
        this._deleteConfirm("确定删除数据么？", (flag) => {
          if (flag) {
            let params = {
              TypeValue: index,
              SettingTypeCode: settingTypeCode,
            };
            DeleteSettingDescriptionRoomDatas(params).then((res) => {
              if (this._common.isSuccess(res)) {
                this._showTip("success", "删除成功");
                this.refresh();
              }
            });
          }
        });
      }
    },
    /**
     * description:点击返回跳转到楼栋维护主页面
     * return {*}
     */
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss">
.hospital-room {
  .hospital-room-header {
    .btn-list {
      display: block;
      float: right;
    }
  }
  .room-table {
    .table-build-select,
    .table-floor-select,
    .table-location-select,
    .table-room-input {
      min-width: 96%;
    }
  }
}
</style>
