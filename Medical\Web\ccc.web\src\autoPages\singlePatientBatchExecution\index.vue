<!--
 * FilePath     : \src\autoPages\singlePatientBatchExecution\index.vue
 * Author       : 苏军志
 * Date         : 2022-05-23 10:56
 * LastEditors  : 苏军志
 * LastEditTime : 2024-08-21 10:04
 * Description  : 单病人批量监测执行
 * CodeIterationRecord:
-->

<template>
  <div class="single-patient-batch-execution">
    <el-tabs @tab-click="handleClick" type="card" v-model="activeName" class="tabs">
      <el-tab-pane
        v-for="(tabPane, index) in tabPanes"
        :key="index"
        :label="tabPane.label"
        :name="tabPane.name"
      ></el-tab-pane>
    </el-tabs>
    <div class="view-wrap">
      <router-view ref="childPage"></router-view>
    </div>
  </div>
</template>

<script>
import { GetMenuByParentID } from "@/api/Menu";
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
    }),
  },
  watch: {
    inpatient(newValue) {
      if (newValue) {
        this.selectPatientData();
      }
    },
    $route(to, from) {
      const tab = this.tabPanes.find((tab) => tab.name === from.name);
      // 如果是从子路由跳转 父路由，则刷新子路由，防止画面显示空白
      if (tab && to.name == "singlePatientBatchExecution") {
        this.handleClick(tab);
      }
    },
  },
  data() {
    return {
      activeName: "batchExecutionSchedule",
      tabPanes: [
        {
          label: "措施执行",
          name: "batchExecutionSchedule",
        },
        {
          label: "病情观察",
          name: "batchTabs-patientObserve",
        },
        {
          label: "体温单",
          name: "vital-sign",
        },
      ],
      specificCareTabPanes: [
        {
          label: "皮肤/伤口",
          name: "batchTabs-wound",
        },
        {
          label: "导管",
          name: "batchTabs-tube",
        },
        {
          label: "出入量",
          name: "batchTabs-io",
        },
        {
          label: "血糖记录单",
          name: "batchTabs-glucose",
        },
        {
          label: "抢救",
          name: "batchTabs-rescueRecord",
        },
        {
          label: "约束",
          name: "batchTabs-restraint",
        },
        {
          label: "疼痛",
          name: "batchTabs-patientPain",
        },
        {
          label: "末梢血运",
          name: "batchTabs-peripheralCirculation",
        },
        {
          label: "泵入通路",
          name: "batchTabs-pumping",
        },
        {
          label: "造口",
          name: "batchTabs-patientStomaRecord",
        },
        {
          label: "输血",
          name: "batchTabs-bloodTransfusionRecord",
        },
        {
          label: "溶栓用药",
          name: "batchTabs-patientThrombolysis",
        },
        {
          label: "饮食营养摄入单",
          name: "batchTabs-patientDietIntake",
        },
        {
          label: "谵妄护理",
          name: "batchTabs-patientDelirium",
        },
        {
          label: "神经血管评估",
          name: "batchTabs-neurovascularAssess",
        },
        {
          label: "CRRT记录单",
          name: "batchTabs-cRRTRecord",
        },
      ],
    };
  },
  mounted() {
    this.getSpecificCareMenuList();
    if (this.inpatient) {
      this.selectPatientData();
    }
  },
  methods: {
    // 系统顶部刷新按钮触发
    refreshData() {
      this.$nextTick(() => {
        if (this.$refs["childPage"] && this.$refs["childPage"].refreshData) {
          this.$refs["childPage"].refreshData();
        }
      });
    },
    //切换病人
    selectPatientData() {
      this.activeName = "batchExecutionSchedule";
      let tab = {
        name: "batchExecutionSchedule",
      };
      this.handleClick(tab);
    },
    getSpecificCareMenuList() {
      let params = {
        menuListID: 18,
      };
      GetMenuByParentID(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.specificCareTabPanes.forEach((item) => {
            let menu = res.data.find((m) => item.name.indexOf(m.router.replace("/", "-")) != -1);
            if (menu) {
              this.tabPanes.push(item);
            }
          });
        }
      });
    },
    //点击页签
    handleClick(tab, event) {
      let url = {
        name: tab.name,
        query: {},
      };
      if (tab.name == "batchTabs-patientObserve") {
        url.query.dialogFlag = true;
      }
      this.$router.replace(url);
    },
  },
};
</script>

<style lang="scss">
.single-patient-batch-execution {
  width: 100%;
  height: 100%;
  padding: 0 8px;
  box-sizing: border-box;
  .view-wrap {
    width: 100%;
    height: calc(100% - 46px);
  }
}
</style>
