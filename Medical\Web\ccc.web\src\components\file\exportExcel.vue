<!--
 * FilePath     : \ccc.web\src\components\file\exportExcel.vue
 * Author       : 胡长攀
 * Date         : 2023-11-10 14:48
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-11-14 16:15
 * Description  :
 参数：
      exportExcelOption中包含以下参数：
      1、tableData：表格数据
      2、columnData{key：，value：}：表格数据对应的属性及名称，key：对应表格标题列的prop，value：对应表格标题列的lable
      3、mergrData：需要合并的标题的第一行
      4、merges：需要合并的行和列的集合
      5、sheetName：Excel对应的sheet名称
      6、fileName：Excel文件名
      7、buttonName：按钮的名称
 -->

<template>
  <div class="export-excel">
    <el-button type="primary" @click="exportExcelTable" class="download-button iconfont icon-download-fill">
      {{ exportExcelOption && exportExcelOption.buttonName ? exportExcelOption.buttonName : "生成Excel文件" }}
    </el-button>
  </div>
</template>
<script>
import * as XLSX from "xlsx";
export default {
  props: {
    exportExcelOption: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  methods: {
    exportExcelTable() {
      let arr = [];
      if (!this.exportExcelOption.tableData?.length) {
        return arr;
      }
      //对应表格标题列的lable
      const titleArr = Object.values(this.exportExcelOption.columnData);
      //对应表格标题列的prop
      const fieldArr = Object.keys(this.exportExcelOption.columnData);
      //处理表头顺序与传入数据顺序不一致问题 需key名前添加Index_ 示例：0_bedNumber
      fieldArr.forEach((key, index) => key.includes("_") && (fieldArr[index] = key.split("_")[1]));
      arr = this.exportExcelOption.tableData.map((obj) => {
        return fieldArr.map((field) => {
          return obj[field];
        });
      });
      arr.splice(0, 0, titleArr);
      if (this.exportExcelOption.mergrData) {
        arr.splice(0, 0, this.exportExcelOption.mergrData);
      }
      const ws = XLSX.utils.aoa_to_sheet(arr);
      if (this.exportExcelOption.merges && this.exportExcelOption.merges.length > 0) {
        ws["!merges"] = this.exportExcelOption.merges;
      }
      const wb = XLSX.utils.book_new();
      let wsrows = [{ hidden: true }];
      ws["!rows"] = wsrows;
      XLSX.utils.book_append_sheet(wb, ws, this.exportExcelOption.sheetName);
      //生成文件
      XLSX.writeFile(wb, this.exportExcelOption.fileName + ".xls");
    },
  },
};
</script>
<style lang="scss">
.export-excel {
  display: inline-block;
}
</style>
