/*
 * FilePath     : \src\api\EmployeeJob.js
 * Author       : 郭鹏超
 * Date         : 2020-07-08 19:56
 * LastEditors  : 苏军志
 * LastEditTime : 2022-03-16 18:44
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/job/nurse";
import qs from "qs";

export const urls = {
  //根据病区获取工作岗位
  GetDeptmentJob: baseUrl + "/GetDeptmentJobs",
  //获取科室人员岗位信息
  GetStationEmployeeJobs: baseUrl + "/GetStationEmployeeJobs",
  //删除用户岗位
  DeleteEmployeeJob: baseUrl + "/DeleteEmployeeJob",
  //保存用户岗位
  SaveEmployeeJob: baseUrl + "/SaveEmployeeJobs",
  CreateAttendanceByJob: baseUrl + "/CreateAttendanceByJob",
  //获取科室岗位视图，包括床位、主次责人员
  GetDeptJobView: baseUrl + "/GetDeptJobView",
  //保存单个人员岗位
  SaveSingleEmployeeJob: baseUrl + "/SaveSingleEmployeeJob",
  //删除人员岗位
  DeleteEmployeeJobByParam: baseUrl + "/DeleteEmployeeJobByParam",
  //带入前次
  BringYesterDay: baseUrl + "/BringYesterDay"
};
//获取科室岗位
export const GetDeptmentJob = () => {
  return http.get(urls.GetDeptmentJob);
};
//获取科室人员岗位信息
export const GetStationEmployeeJobs = params => {
  return http.get(urls.GetStationEmployeeJobs, params);
};

//获取科室人员岗位信息
export const DeleteEmployeeJob = params => {
  return http.get(urls.DeleteEmployeeJob, params);
};
//保存人员岗位信息
export const SaveEmployeeJob = params => {
  return http.post(urls.SaveEmployeeJob, qs.stringify(params));
};
//根据人员岗位生成派班
export const CreateAttendanceByJob = params => {
  return http.post(urls.CreateAttendanceByJob, qs.stringify(params));
};
//获取科室岗位视图，包括床位、主次责人员
export const GetDeptJobView = params => {
  return http.get(urls.GetDeptJobView, params);
};
export const SaveSingleEmployeeJob = params => {
  return http.post(urls.SaveSingleEmployeeJob, qs.stringify(params));
};
//删除人员岗位
export const DeleteEmployeeJobByParam = params => {
  return http.post(urls.DeleteEmployeeJobByParam, qs.stringify(params));
};
export const BringYesterDay = params => {
  return http.post(urls.BringYesterDay, qs.stringify(params));
};
