<!--
 * FilePath     : \src\autoPages\monitoringScheduler\component\cellTypes\dropdownCell.vue
 * Author       : 杨欣欣
 * Date         : 2024-06-24 14:36
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-09-11 09:28
 * Description  : 下拉框单元格组件
 * CodeIterationRecord: 
 -->
<template functional>
  <el-select
    class="dropdown-cell"
    :value="props.row[props.column.index].assessValue"
    :disabled="props.row[props.column.index].disabled"
    collapse-tags
    placeholder="请选择"
    :multiple="props.column.style == 'MDL'"
    clearable
    @change="$options.onChange($event, props), listeners.change(props.row)"
  >
    <el-option
      v-for="option in props.column.childColumns"
      :key="option.assessListID == 0 ? option.name : option.assessListID"
      :label="option.title"
      :value="option.assessListID == '0' ? option.name : option.assessListID.toString()"
    />
  </el-select>
</template>
<script>
export default {
  name: "dropdownCell",
  /**
   * @description: 下拉框值改变事件
   * @param
   * @return
   */
  onChange(value, props) {
    props.row[props.column.index].assessValue = value;
  },
};
</script>
<style scoped>
.dropdown-cell {
  width: 100%;
}
</style>