<!--
 * FilePath     : \src\pages\help\components\commonProblem.vue
 * Author       : 苏军志
 * Date         : 2022-09-08 15:02
 * LastEditors  : 苏军志
 * LastEditTime : 2022-09-08 16:58
 * Description  : 常见问题
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="common-problem">
    <div slot="header">
      问题查找：
      <el-input class="question-input" v-model="inputContext" placeholder="请输入问题" @keyup.enter.native="search">
        <i slot="append" class="iconfont icon-search" @click="search"></i>
      </el-input>
    </div>
    <el-table :data="questionList" border height="100%" class="problem-table">
      <el-table-column prop="questionAndAnaswerID" label="问题ID" width="70" align="center"></el-table-column>
      <el-table-column prop="question" label="问题" header-align="center" min-width="150" align="left">
        <template slot-scope="questionAndAnaswer">
          <span v-html="questionAndAnaswer.row.question"></span>
        </template>
      </el-table-column>
      <el-table-column prop="anaswer" label="解决方法" header-align="center" min-width="150" align="left">
        <template slot-scope="questionAndAnaswer">
          <span v-html="questionAndAnaswer.row.anaswer"></span>
        </template>
      </el-table-column>
      <el-table-column prop="addDate" label="新增时间" width="160" align="center">
        <template slot-scope="scope">
          <span v-formatTime="{ value: scope.row.addDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import { GetQuestionAndAnaswers } from "@/api/QuestionAndAnaswer";
export default {
  components: {
    baseLayout,
  },
  data() {
    return {
      questionList: [],
      cloneQuestionList: [],
      inputContext: "",
    };
  },

  mounted() {
    this.init();
  },
  methods: {
    init() {
      GetQuestionAndAnaswers().then((res) => {
        if (this._common.isSuccess(res)) {
          this.questionList = res.data;
          this.cloneQuestionList = this._common.clone(this.questionList);
        }
      });
    },
    //模糊查询
    search() {
      this.questionList = this.cloneQuestionList.filter((item) => {
        return item.question.indexOf(this.inputContext) != -1;
      });
    },
  },
};
</script>
<style lang="scss" >
.common-problem {
  .question-input {
    width: 200px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
}
</style>