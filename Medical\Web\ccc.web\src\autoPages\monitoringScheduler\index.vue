<!--
 * FilePath     : \src\autoPages\monitoringScheduler\index.vue
 * Author       : 杨欣欣
 * Date         : 2024-06-13 17:12
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-18 10:56
 * Description  : 单病人/批量监测类排程执行组件
 * CodeIterationRecord: 
 -->
<template>
  <base-layout class="monitoring-scheduler" v-loading="loading" :element-loading-text="loadingText">
    <monitor-schedule-header
      slot="header"
      ref="header"
      :params="queryParams"
      :tableDataLength="monitorData.length"
      :dateOptions="dateOptions"
      @change="getTableDataByHeaderParams"
      @change-date="updateMonitorDataPerformDateTime"
      @save="save"
      @open-pdf="openPDFDialog"
    />
    <div class="content" slot-scope="{ height }" :style="{ height: height + 'px' }">
      <!-- 进度条 -->
      <progress-view v-if="showProgress" @closeProgress="progressClose" :tableData="messageData" />
      <!-- 表格 -->
      <u-table
        ref="monitorDataTable"
        class="monitor-data-table"
        :data="monitorData"
        border
        striped
        :height="height"
        :row-height="40"
        use-virtual
        @selection-change="storeSelectionRows"
      >
        <u-table-column
          type="selection"
          :selectable="getRowSelectable"
          :width="convertPX(50)"
          align="center"
          fixed="left"
        />
        <!-- 行数据首列，名称列 -->
        <name-column
          :isSingle="Boolean(queryParams)"
          :firstColumn="firstColumn"
          :width="convertPX(firstColumn.width)"
        />
        <!-- 第二列开始，若干监测项目列 -->
        <monitor-column
          v-for="column in monitorColumns"
          :key="column.assessListID"
          :column="column"
          :columns="monitorColumns"
          :monitorColumnRelationMap="monitorColumnRelationMap"
          :min-width="column.width == 'auto' ? 'auto' : convertPX(column.width)"
          :convertPX="convertPX"
          @change="checkRows"
          @click-button="openButtonDialog"
          @click-input="showKeyBoard"
        />
        <!-- 执行日期列 -->
        <u-table-column
          :label="performDateColumn.title"
          class-name="perform-date"
          :width="convertPX(performDateColumn.width)"
          align="center"
          fixed="right"
        >
          <template slot-scope="{ row }">
            <el-date-picker
              v-model="getCell(row, 'performDate').value"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              type="date"
              :class="getCell(row, 'rowStatus').isPerformed ? 'saved' : 'no-save'"
              :picker-options="dateOptions"
            />
          </template>
        </u-table-column>
        <!-- 执行时间列  -->
        <u-table-column
          :label="performTimeColumn.title"
          class-name="perform-time"
          :width="convertPX(performTimeColumn.width)"
          align="center"
          fixed="right"
        >
          <template slot-scope="{ row }">
            <el-time-picker
              v-model="getCell(row, 'performTime').value"
              value-format="HH:mm"
              format="HH:mm"
              :style="{
                width: convertPX(Number(performTimeColumn.width) - 10),
              }"
              :class="getCell(row, 'rowStatus').isPerformed ? 'saved' : 'no-save'"
              @change="
                validatePerformDateTime(
                  `${_datetimeUtil.formatDate(getCell(row, 'performDate').value, 'yyyy-MM-dd')} ${
                    getCell(row, 'performTime').value
                  }`,
                  row
                )
              "
            />
          </template>
        </u-table-column>
        <!-- 通知医师列 -->
        <u-table-column :label="informPhysicianColumn.title" :width="convertPX(50)" align="center" fixed="right">
          <template slot-scope="{ row }">
            <el-checkbox v-model="getCell(row, 'informPhysician').value" @change="checkRows(row)" />
          </template>
        </u-table-column>
        <!-- 带入交班列 -->
        <u-table-column :label="bringToShiftColumn.title" :width="convertPX(50)" align="center" fixed="right">
          <template slot-scope="{ row }">
            <el-checkbox v-model="getCell(row, 'bringToShift').value" @change="checkRows(row)" />
          </template>
        </u-table-column>
        <!-- 操作列 -->
        <u-table-column
          :label="rowStatusColumn.title"
          class-name="operate-column"
          align="center"
          :width="convertPX(copyEnabled ? 180 : 104)"
          fixed="right"
        >
          <template slot-scope="{ row, $index }" v-if="getCell(row, 'rowStatus').performable">
            <template v-if="copyEnabled">
              <div class="operate">
                <el-tooltip content="复制">
                  <i class="iconfont icon-copy" @click="copy(row, $index)" />
                </el-tooltip>
              </div>
              <div class="operate">
                <el-tooltip content="粘贴" v-if="copiedRowIndex !== $index && copyCells">
                  <i class="iconfont icon-paste" @click="paste(row)" />
                </el-tooltip>
              </div>
            </template>
            <div class="operate">
              <el-tooltip content="清空">
                <i class="iconfont icon-clear" @click="clear(row, $index)" />
              </el-tooltip>
            </div>
            <div class="operate">
              <el-tooltip content="仪器数据">
                <i class="iconfont icon-clinic1" @click="openClinicDataDrawer(row)" />
              </el-tooltip>
            </div>
          </template>
        </u-table-column>
      </u-table>
      <!-- 虚拟键盘 -->
      <key-board
        v-tobody="{ id: 'key-board' }"
        :show="isShowKeyBoard"
        :output="el"
        typeName="TN"
        @hide="hideKeyBoard"
      />
      <!-- 仪器数据 -->
      <el-drawer title="仪器数据" :visible.sync="drawerVisible" direction="btt" size="55%">
        <clinic-view
          v-if="drawerVisible"
          :clinicPrams="clinicPrams"
          v-model="timeRange"
          @getSelectClinicData="setClinicDataToRow"
        />
      </el-drawer>
      <!-- 按钮弹出框 -->
      <el-dialog
        :title="buttonName"
        :close-on-click-modal="false"
        :visible.sync="buttonDialogVisible"
        @close="getButtonValue"
        fullscreen
        custom-class="specific-care-dialog"
        append-to-body
      >
        <iframe ref="buttonDialog" width="100%" height="100%" />
      </el-dialog>
      <!-- 批量监测排程表PDF -->
      <el-dialog
        title="批量监测排程表"
        :close-on-click-modal="false"
        :visible.sync="monitoringPDFDialogVisible"
        append-to-body
        fullscreen
      >
        <iframe
          :src="pdfPath"
          type="application/x-google-chrome-pdf"
          width="100%"
          height="100%"
          frameborder="1"
          scrolling="auto"
        />
      </el-dialog>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import monitorScheduleHeader from "./component/monitorScheduleHeader";
import keyBoard from "@/components/KeyBoard/KeyBoard";
import progressView from "@/components/progressView";
import clinicView from "@/pages/schedule/components/scheduleTypes/clinicView";
import { mapGetters } from "vuex";
import { GetBatchMonitorSchedules, GetButtonData, SaveMultiSchedule } from "@/api/PatientSchedule";
import {
  GetOneSettingByTypeAndCode,
  GetSettingValuesByTypeCodeAndValue,
  GetClinicalSettingValuesBySettingTypeCode,
} from "@/api/Setting";
import nameColumn from "./component/nameColumn";
import monitorColumn from "./component/monitorColumn";
// 因使用递归组件，需在此处引入所有单元格组件
import { buttonCell, checkboxCell, dropdownCell, tnCell, textCell, hiddenItem } from "./component/cellTypes/index";
export default {
  components: {
    baseLayout,
    monitorScheduleHeader,
    keyBoard,
    progressView,
    clinicView,
    nameColumn,
    monitorColumn,
    buttonCell,
    checkboxCell,
    dropdownCell,
    tnCell,
    textCell,
    hiddenItem,
    textCell,
  },
  props: {
    /**
     * @description: 数据查询参数
     * @property {string} inpatientID 病人住院序号
     * @property {string} scheduleDate 班别日期
     * @property {number} shiftID 班别ID
     * @property {string} shiftName 班别名称
     */
    queryParams: {
      type: Object,
      default: () => undefined,
    },
  },
  data() {
    return {
      //#region 表格
      // 监测列数据在row中的索引区间，左闭右开
      monitorColumnRange: [1, -5],
      firstColumn: {},
      monitorColumns: undefined,
      performDateColumn: {},
      performTimeColumn: {},
      informPhysicianColumn: {},
      bringToShiftColumn: {},
      rowStatusColumn: {},
      // 列下标偏移位置
      columnIndexMap: {
        rowStatus: 1,
        bringToShift: 2,
        informPhysician: 3,
        performTime: 4,
        performDate: 5,
      },
      monitorData: [],
      loading: false,
      loadingText: "加载中……",
      // 监测类目与下拉框联动关系
      monitorColumnRelationMap: new Map([
        ["1295", "4701"],
        ["1299", "6633240"],
      ]),
      selectedRows: undefined,
      dateOptions: {
        disabledDate(current) {
          return current.getTime() > new Date().getTime();
        },
      },
      //#endregion
      //#region 专项弹出框
      buttonName: "",
      buttonDialogVisible: false,
      buttonClickPatientInfo: undefined,
      buttonClickRow: {
        row: undefined,
        buttonCellIndex: undefined,
        cellFirstScheduleMainID: "",
        buttonInterventionDetailID: undefined,
      },
      //#endregion
      //#region 复制粘贴
      copyEnabled: true,
      isCopy: false,
      copyCells: undefined,
      copiedRowIndex: undefined,
      notCopyMonitorColumnIDs: undefined,
      //#endregion
      //#region 批量监测排程表PDF
      pdfPath: undefined,
      monitoringPDFDialogVisible: false,
      //#endregion
      //#region 虚拟键盘
      isShowKeyBoard: false,
      el: undefined,
      //#endregion
      //#region 仪器数据
      drawerVisible: false,
      timeRange: 30,
      selectClinicRow: undefined,
      clinicPrams: undefined,
      //#endregion
      //#region 进度条
      showProgress: false,
      messageData: [
        {
          label: "进度",
          value: 1,
        },
        {
          label: "保存成功",
          value: "",
        },
        {
          label: "保存失败",
          value: "",
        },
        {
          label: "提示",
          value: "",
        },
      ],
      //#endregion
      //#region 配置
      painScoreThreshold: undefined,
      //#endregion
    };
  },
  provide() {
    return {
      validateInputValueAbnormal: this.validateInputValueAbnormal,
      getIsCopy: () => this.isCopy,
      isReadOnly: this.isReadOnly,
      parent: this,
      getPainScoreThreshold: () => this.painScoreThreshold,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      patientInfo: "getCurrentPatient",
      token: "getToken",
    }),
    // 是否只读，移动端使用虚拟键盘文本框改为只读
    isReadOnly() {
      return !this._common.isPC();
    },
  },
  watch: {
    queryParams() {
      this.getTableData();
    },
  },
  created() {
    window.this = this;
    this._direction(this);
  },
  mounted() {
    this.getCopyEnableSetting();
    this.getNotCopyAssessListIDs();
    this.getSettingTimeRange();
    this.getPainScoreSetting();
    this.loading = true;
    // 单病人情况直接获取表格数据，批量监测依赖于头部数据加载完毕后的事件回调
    if (this.queryParams) {
      this.getTableData().then(() => (this.loading = false));
    }
  },
  methods: {
    //#region 表格获取
    /**
     * @description: 获取表格数据
     * @param headerParams 头部参数，批量监测查询表格使用
     * @return
     */
    async getTableData(headerParams = undefined) {
      this.monitorColumns = [];
      this.monitorData = [];
      this.firstColumn = {};
      this.rowStatusColumn = {};
      this.performDateColumn = {};
      this.performTimeColumn = {};
      this.informPhysicianColumn = {};
      this.bringToShiftColumn = {};

      let params = undefined;
      if (this.queryParams) {
        params = {
          inpatientID: this.queryParams.inpatientID,
          scheduleDate: this.queryParams.scheduleDate,
          shiftID: this.queryParams.shiftID,
          isSingle: true,
        };
      } else {
        const shiftTimes = headerParams.shiftTimes.split("-");
        params = {
          startTime: shiftTimes[0],
          endTime: shiftTimes[1],
          shiftID: headerParams.shiftID,
          stationID: headerParams.stationID,
          notPerformFlag: headerParams.showNotPerform,
          // 不展示全科 ==> 显示当前登录用户责任病人
          employeeID: headerParams.showAll ? "" : this.user.userID,
          scheduleDate: headerParams.scheduleDate,
          isSingle: false,
        };
      }
      const result = await GetBatchMonitorSchedules(params);
      if (this._common.isSuccess(result) && result.data) {
        this.configureTable(result.data);
      }
      this.copiedRowIndex = undefined;
      this.$nextTick(() => this.$refs.monitorDataTable.doLayout());
    },
    /**
     * @description: 根据头部参数获取表格数据
     * @param headerParams 头部参数
     * @return
     */
    getTableDataByHeaderParams(headerParams) {
      this.loading = true;
      this.getTableData(headerParams).then(() => (this.loading = false));
    },
    /**
     * @description: 拆解后端返回的行列数据
     * @param table
     * @return
     */
    configureTable(table) {
      const getTitleAndWidth = (column) => {
        return {
          title: column.title,
          width: column.width,
          index: column.index,
        };
      };
      this.firstColumn = table.columns[0];
      this.rowStatusColumn = getTitleAndWidth(table.columns[table.columns.length - this.columnIndexMap["rowStatus"]]);
      this.bringToShiftColumn = getTitleAndWidth(
        table.columns[table.columns.length - this.columnIndexMap["bringToShift"]]
      );
      this.informPhysicianColumn = getTitleAndWidth(
        table.columns[table.columns.length - this.columnIndexMap["informPhysician"]]
      );
      this.performTimeColumn = getTitleAndWidth(
        table.columns[table.columns.length - this.columnIndexMap["performTime"]]
      );
      this.performDateColumn = getTitleAndWidth(
        table.columns[table.columns.length - this.columnIndexMap["performDate"]]
      );
      this.monitorColumns = table.columns.slice(...this.monitorColumnRange);
      this.monitorData = table.rows;
      this.initMonitorItemValueColor(this.monitorData);
      // 某些行自动带入了仪器数据，进行初始化勾选
      table.autoAddIndexList?.forEach((rowIndex) => this.checkRows(this.monitorData[rowIndex]));
    },
    /**
     * @description: 获取单元格
     * @param row 当前行
     * @param cellName 单元格名称
     * @return
     */
    getCell(row, cellName) {
      const cells = Object.values(row);
      const cell = cells[cells.length - this.columnIndexMap[cellName]];
      return cell;
    },
    /**
     * @description: 初始化表格中的异常值颜色
     * @param monitorData 监测数据
     * @return
     */
    initMonitorItemValueColor(monitorData) {
      monitorData.forEach((row) => {
        Object.values(row).forEach((cell) => {
          if (cell.style != "TN") {
            return;
          }
          const showColumnCell = this.getRelationShowColumn(row, cell);
          this.validateInputValueAbnormal(showColumnCell ?? cell, showColumnCell ? cell : undefined);
        });
      });
    },
    /**
     * @description: 获取隐藏项关联的显示列
     * @param row 当前行
     * @param hiddenItem 隐藏项
     * @return
     */
    getRelationShowColumn(row, hiddenItem) {
      if (!this.monitorColumns.find((column) => column.index == hiddenItem.columnIndex)?.isHidden) {
        return;
      }
      const showColumnIndex = this.monitorColumns.find(
        (column) => column.relationHiddenItemIndex == hiddenItem.columnIndex
      )?.index;
      if (showColumnIndex) {
        return row[showColumnIndex];
      }
      return undefined;
    },
    //#endregion
    //#region 表格更新
    /**
     * @description: 头部执行日期时间修改后，同步更新表格所有的执行日期、时间
     * @param performDateTime 执行时间
     * @return
     */
    updateMonitorDataPerformDateTime(performDateTime) {
      const [performDate, performTime] = performDateTime.split(" ");
      this.monitorData.forEach((row) => {
        const rows = Object.values(row);
        //不更改已执行排程的执行时间
        if (this.getCell(rows, "rowStatus").isPerformed) {
          return;
        }
        rows[rows.length - this.columnIndexMap["performDate"]].value = performDate;
        rows[rows.length - this.columnIndexMap["performTime"]].value = performTime;
      });
    },
    /**
     * @description: 储存已勾选的行
     * @param rows 勾选的行
     * @return
     */
    storeSelectionRows(rows) {
      this.selectedRows = rows;
    },
    //#endregion
    //#region 操作
    /**
     * @description: 复制当前行的监测列值
     * @param row 当前行
     * @param copiedRowIndex 被复制的行下标
     * @return
     */
    copy(row, copiedRowIndex) {
      const cloneRow = this._common.clone(row);
      // 过滤掉无效列与按钮列
      const monitorData = Object.values(cloneRow)
        .slice(...this.monitorColumnRange)
        .filter((cell) => cell.style !== "B" && (cell.assessValue || cell.assessValue.toString() === "0"));
      // 如果监测类目列没有有效值，则不允许复制
      if (!monitorData.length) {
        this._showTip("warning", "没有可复制的数据");
        return;
      }
      this.isCopy = true;
      this.copyCells = monitorData;
      this.copiedRowIndex = copiedRowIndex;
    },
    /**
     * @description: 使用之前复制好的行覆盖当前行
     * @param row 当前行
     * @return
     */
    async paste(row) {
      const pasteCells = Object.values(row).slice(...this.monitorColumnRange);
      for (let index = 0; index < this.copyCells.length; index++) {
        const copyCell = this.copyCells[index];
        const predicate =
          copyCell.assessListID === "0"
            ? (cell) => cell.interventionDetailID === copyCell.interventionDetailID
            : (cell) => cell.assessListID == copyCell.assessListID;
        const cell = pasteCells.find(predicate);

        const notCopy = this.notCopyMonitorColumnIDs.find((assessListID) => assessListID == cell.assessListID);
        if (notCopy) {
          return;
        }
        if (cell.style?.endsWith("DL") && copyCell.assessValue) {
          cell.disabled = false;
        }
        cell.assessValue = copyCell.assessValue;

        // 若此项有联动项，需要立即更新DOM以触发子组件的watch、事件
        if (this.monitorColumnRelationMap.has(cell.assessListID)) {
          await this.$nextTick();
        }
        const showColumnCell = this.getRelationShowColumn(row, cell);
        this.validateInputValueAbnormal(showColumnCell ?? cell, showColumnCell ? cell : undefined);
      }
      this.checkRows(row);
      this.isCopy = false;
      this.copyCells = undefined;
      this.copiedRowIndex = undefined;
    },
    /**
     * @description: 清空行数据
     * @param row 当前行
     * @param clearedRowIndex 当前行下标
     * @return
     */
    clear(row, clearedRowIndex) {
      Object.values(row).forEach((cell) => {
        if (cell.assessValue) {
          cell.assessValue = "";
          const showColumnCell = this.getRelationShowColumn(row, cell);
          this.validateInputValueAbnormal(showColumnCell ?? cell, showColumnCell ? cell : undefined);
        }
      });
      // 如果当前行是复制行，则同时清除
      if (this.copiedRowIndex && this.copiedRowIndex === clearedRowIndex) {
        this.copyCells = undefined;
        this.copiedRowIndex = undefined;
        this.isCopy = false;
      }
    },
    //#endregion
    //#region 保存
    /**
     * @description: 保存勾选的行
     * @return
     */
    async save() {
      if (!this.selectedRows || this.selectedRows.length == 0) {
        this._showTip("warning", "请勾选需要保存的数据");
        return;
      }
      let successMessage = "";
      let failMessage = "";
      const clearCheckRows = [];
      // 打开进度条
      this.showProgress = true;
      for (let i = 0; i < this.selectedRows.length; i++) {
        const row = this.selectedRows[i];
        const values = Object.values(row);
        const { rowName, rowStatus, cells, performDate, performTime, informPhysician, bringToShift } =
          this.getValuesItem(values);
        let messageItem = rowName[0];
        // 若为批量监测，则呈现为`x床-xx:xx`
        rowName.length > 1 && (messageItem += `床-${rowName[rowName.length - 1]}`);
        // 配置进度条
        let tipMessage = "";
        let percent = (((i + 1) / this.selectedRows.length) * 100).toFixed(0);
        // 未保存过数据且所有监测项目均为空
        if (!rowStatus.isPerformed && !cells.some((cell) => cell.assessValue)) {
          tipMessage = messageItem + "请填写数据！";
          failMessage = failMessage ? `${failMessage}、${messageItem}` : messageItem;
          this.messageData[0].value = Number(percent);
          this.messageData[2].value = failMessage;
          this.messageData[3].value = tipMessage;
          continue;
        }
        const details = this.createDetails(
          cells,
          performDate,
          performTime,
          informPhysician,
          bringToShift,
          rowStatus.inpatientID
        );
        const result = await SaveMultiSchedule(details);
        if (this._common.isSuccess(result)) {
          // 保存成功在操作栏上打上保存标记，切换编辑图标用
          rowStatus.isPerformed = true;
          //保存成功拼接成功字符
          successMessage = successMessage ? `${successMessage}、${messageItem}` : messageItem;
          //保存成功取消勾选
          clearCheckRows.push({
            row,
            selected: false,
          });
        } else {
          failMessage = failMessage ? `${failMessage}、${messageItem}` : messageItem;
        }

        //配置进度条内容
        this.messageData[0].value = Number(percent);
        this.messageData[1].value = successMessage;
        this.messageData[2].value = failMessage;
      }
      this.$refs.monitorDataTable.toggleRowSelection(clearCheckRows);
    },
    /**
     * @description: 解析当前行对象值
     * @param values 对象值集合
     * @return
     */
    getValuesItem(values) {
      const rowName = values[this.firstColumn.index].split("||");
      // 获取最后一个成员
      const rowStatus = values[values.length - this.columnIndexMap["rowStatus"]];
      // 获取监测列成员
      const cells = values.slice(...this.monitorColumnRange);
      // 获取执行日期
      const performDate = values[values.length - this.columnIndexMap["performDate"]].value;
      // 获取执行时间
      const performTime = values[values.length - this.columnIndexMap["performTime"]].value;
      // 获取通知医生
      const informPhysician = values[values.length - this.columnIndexMap["informPhysician"]].value;
      // 获取带班标志
      const bringToShift = values[values.length - this.columnIndexMap["bringToShift"]].value;
      return {
        rowName,
        rowStatus,
        cells,
        performDate,
        performTime,
        informPhysician,
        bringToShift,
      };
    },
    /**
     * @description: 组装待保存的scheduleDetails
     * @param cells 单元格集合
     * @param performDate 执行日期
     * @param performTime 执行时间
     * @param informPhysician 通知医师
     * @param bringToShift 带入交班
     * @return
     */
    createDetails(cells, performDate, performTime, informPhysician, bringToShift, inpatientID) {
      const details = [];
      for (const cell of cells) {
        if ((cell.style == "T" || cell.style == "TN") && !cell.assessValue?.trim()) {
          continue;
        }
        // 一个单元格可能对应多个排程，所以明细数量为单元格的排程数量
        cell.patientScheduleMainIDs?.forEach((mainID) => {
          const detail = {
            patientScheduleMainID: mainID,
            scheduleDate: performDate,
            scheduleTime: this._datetimeUtil.formatDate(performTime, "hh:mm"),
            interventionDetailID: cell.interventionDetailID,
            assessListID: cell.assessListID,
            inpatientID: inpatientID,
            scheduleData: cell.assessValue ? cell.assessValue : "",
            temperatureStatus: cell.temperatureStatus,
            informPhysician: informPhysician,
            bringToShift: bringToShift,
          };
          if (!cell.style.endsWith("DL")) {
            details.push(detail);
            return;
          }
          const radioOptions = this.findRadioOptions(cell);
          radioOptions?.forEach((radioOption) => {
            detail.assessListID = radioOption.assessListID;
            detail.interventionDetailID = radioOption.name;
            detail.scheduleData = radioOption.title;
            details.push(this._common.clone(detail));
          });
        });
      }
      return details;
    },
    /**
     * @description: 查找单元格对应的radio选项
     * @param cell 单元格
     * @return
     */
    findRadioOptions(cell) {
      // 查找所属列
      const findColumn = (column) =>
        column.index == 0
          ? column.childColumns.find(findColumn)
          : column.index == cell.columnIndex
          ? column
          : undefined;
      let currentCellColumn;
      this.monitorColumns.some((column) => {
        currentCellColumn = findColumn(column);
        return currentCellColumn;
      });
      // 获取选项唯一表示的函数
      const getID = (radioOption) =>
        radioOption.assessListID == 0 ? radioOption.name : radioOption.assessListID.toString();
      // 根据assessValue，选取合适的过滤函数
      const predicate = Array.isArray(cell.assessValue)
        ? (radioOption) => cell.assessValue.includes(getID(radioOption))
        : (radioOption) => cell.assessValue.toString().trim() == getID(radioOption);
      // 根据列持有的子列集合成员，过滤出选中的项目
      const radioOptions = currentCellColumn?.childColumns?.filter(predicate);
      return radioOptions;
    },
    //#endregion
    //#region 私有工具方法
    /**
     * @description: 检查执行时间是否超过当前时间
     * @param performDateTime 执行日期时间
     * @param row 当前行
     * @return
     */
    validatePerformDateTime(performDateTime, row) {
      const nowDate = this._datetimeUtil.toDate(this._datetimeUtil.getNow());
      if (this._datetimeUtil.toDate(performDateTime) > nowDate) {
        this._showTip("warning", "执行时间不能超过当前时间!");
        row && (this.getCell(row, "performTime").value = this._datetimeUtil.formatDate(nowDate, "hh:mm"));
        return false;
      }
      row && this.checkRows(row);
      return true;
    },
    /**
     * @description: 当数据超出标准范围时，标红
     * @param cell 单元格数据对象
     * @param hiddenItem 隐藏项
     * @return
     */
    validateInputValueAbnormal(cell, hiddenItem) {
      const checkCell = hiddenItem || cell;
      // 需要重置复制状态，否则会导致输入内容与相关下拉项不联动
      this.isCopy = false;

      if (!checkCell.triggerCondition) {
        return;
      }
      checkCell.triggerCondition.some((condition) => {
        if (
          Number(checkCell.assessValue) >= Number(condition.LowNotify) &&
          Number(checkCell.assessValue) <= Number(condition.UpNotify)
        ) {
          checkCell.assessValueColor = "red";
          if (condition.ReTest) {
            //体温过高触发复测选框
            cell.assessValueColor = "red reduce";
            if (checkCell.temperatureStatus === 0) {
              checkCell.temperatureStatus = 2;
            }
            checkCell.showFlag = true;
          }
          return true;
        }
        cell.assessValueColor = "";
        checkCell.temperatureStatus = 0;
        checkCell.showFlag = false;
        return false;
      });
    },
    /**
     * @description: 返回勾选框是否可以勾选
     * @param row 当前行
     * @return
     */
    getRowSelectable(row) {
      if (!row) {
        return false;
      }
      const cells = Object.values(row);
      return cells[cells.length - this.columnIndexMap["rowStatus"]].performable;
    },
    /**
     * @description: 勾选所传行集合中符合条件的行
     * @param rows 需勾选的行
     * @return
     */
    checkRows(...rows) {
      const toggleSelectRows = [];
      const toToggleParam = (row) => {
        return {
          row,
          selected: true,
        };
      };
      // 过滤出可以勾选的行
      for (let index = 0; index < rows.length; index++) {
        const row = rows[index];
        if (!this.getRowSelectable(row)) {
          continue;
        }
        const cells = Object.values(row);
        const isEdit = cells[cells.length - this.columnIndexMap["rowStatus"]].isPerformed;
        // 已保存过的数据发生异动，直接勾选
        if (isEdit) {
          toggleSelectRows.push(toToggleParam(row));
          continue;
        }
        // 未保存数据，判断内部是否存在有效值
        const isChanged = cells
          .slice(...this.monitorColumnRange)
          // TODO：暂时排除D类列，因D类可能有联动默认值；已列故事优化调整联动默认值存储字段
          .some((cell) => !cell.style?.endsWith("DL") && cell.assessListID && cell.assessValue);
        isChanged && toggleSelectRows.push(toToggleParam(row));
      }
      this.$nextTick(() => toggleSelectRows.length && this.$refs.monitorDataTable.toggleRowSelection(toggleSelectRows));
    },
    //#endregion
    /**
     * @description: 打开PDF预览弹窗
     * @param pdfPath pdf路径
     * @return
     */
    openPDFDialog(pdfPath) {
      this.pdfPath = pdfPath;
      this.monitoringPDFDialogVisible = true;
    },
    //#region 专项按钮
    /**
     * @description: 点击按钮，打开专项弹窗
     * @param row 当前行
     * @param column 当前按钮所在列
     * @return
     */
    openButtonDialog(row, column) {
      if (!this.getRowSelectable(row)) {
        this._showTip("warning", "不可提前执行排程！");
        return;
      }
      // 当前单元格数据
      const cell = row[column.index];
      const { inpatientID } = this.getCell(row, "rowStatus");
      // 存储当前行的病人信息
      this.buttonClickPatientInfo = {
        inpatientID: inpatientID,
        // 单病人直接取vuex中的bedNumber，批量监测需从首列截取
        bedNumber: this.queryParams ? this.patientInfo.bedNumber : row[0].split("||")[0],
      };

      // 缓存数据，更新角标使用
      this.buttonClickRow = {
        row,
        buttonCellIndex: column.index,
        cellFirstScheduleMainID: cell.patientScheduleMainIDs[0],
        buttonInterventionDetailID: cell.interventionDetailID,
      };
      this.buttonName = column.name;
      let url = cell.linkForm;
      if (!url) {
        return;
      }
      this.buttonDialogVisible = true;
      url += `${url.includes("?") ? "&" : "?"}bedNumber=${this.buttonClickPatientInfo.bedNumber.replace(/\+/g, "%2B")}`;
      url +=
        `&userID=${this.user.userID}` +
        `&token=${this.token}` +
        "&isDialog=true" +
        `&patientScheduleMainID=${this.buttonClickRow.cellFirstScheduleMainID}` +
        `&sourceID=${this.buttonClickRow.cellFirstScheduleMainID}` +
        "&sourceType=Schedule";

      // 这样写是防止页面渲染前调用，报this.$refs.buttonDialog是undefined
      this.$nextTick(() => this.$refs.buttonDialog.contentWindow.location.replace(url));
    },
    /**
     * @description: 专项弹窗关闭时，获取按钮角标值
     * @return
     */
    getButtonValue() {
      const params = {
        inpatientID: this.buttonClickPatientInfo.inpatientID,
        nursingInterventionDetailID: this.buttonClickRow.buttonInterventionDetailID,
        sourceID: this.buttonClickRow.cellFirstScheduleMainID,
      };
      GetButtonData(params).then((res) => {
        const buttonValue = this._common.isSuccess(res) && res.data ? res.data : undefined;
        if (!buttonValue) {
          return;
        }
        const buttonCell = this.buttonClickRow.row[this.buttonClickRow.buttonCellIndex];
        this.$set(buttonCell, "assessValue", buttonValue.assessValue);
      });
    },
    //#endregion
    //#region 仪器数据
    /**
     * @description: 打开仪器数据抽屉并组装入参
     * @param row 当前行
     * @return
     */
    openClinicDataDrawer(row) {
      this.clinicPrams = {};
      this.selectClinicRow = row;

      const cells = Object.values(row);
      const patientScheduleMainIDArr = new Set([]);
      const scheduleTime = this.queryParams
        ? cells[this.firstColumn.index]
        : cells[this.firstColumn.index].split("||").slice(-1)[0];
      const { inpatientID } = this.getCell(row, "rowStatus");
      cells.slice(...this.monitorColumnRange).forEach((cell) => {
        cell.patientScheduleMainIDs?.forEach((scheduleMainID) => patientScheduleMainIDArr.add(scheduleMainID));
      });
      this.clinicPrams = {
        inpatientID: inpatientID,
        patientScheduleMainIDs: [...patientScheduleMainIDArr],
        scheduleDate: (this.queryParams || this.$refs.header.headerParameters).scheduleDate,
        scheduleTime: scheduleTime,
        shiftID: this.$refs.header.headerParameters.shiftID,
      };

      this.drawerVisible = true;
    },
    /**
     * @description: 将仪器数据导入到行中
     * @param clinicDataRow 仪器数据
     * @return
     */
    setClinicDataToRow(clinicDataRow) {
      let isAssigned = false;
      const clinicDataEntries = Object.entries(clinicDataRow);
      const monitorDataValues = Object.values(this.selectClinicRow);
      monitorDataValues.slice(...this.monitorColumnRange).forEach((cell) => {
        const clinicDataValue = !cell.style.endsWith("DL")
          ? clinicDataEntries.find(([key]) => key == cell?.assessListID)?.[1]
          : (() => {
              const options = this.monitorColumns
                .find((column) => column.assessListID == cell.assessListID)
                ?.childColumns.map((childCol) => childCol.assessListID.toString());
              return clinicDataEntries.find(([key, value]) => options.includes(key) && value)?.[0];
            })();
        if (clinicDataValue && !isAssigned) {
          isAssigned = true;
        }
        cell.assessValue = clinicDataValue ?? "";
        const showColumnCell = this.getRelationShowColumn(this.selectClinicRow, cell);
        this.validateInputValueAbnormal(showColumnCell ?? cell, showColumnCell ? cell : undefined);
      });
      if (!isAssigned) {
        this._showTip("warning", "没有可带入的仪器数据");
        this.drawerVisible = false;
        return;
      }
      // 更新当前行的执行日期时间
      const performDateCell = monitorDataValues[monitorDataValues.length - this.columnIndexMap["performDate"]];
      const performTimeCell = monitorDataValues[monitorDataValues.length - this.columnIndexMap["performTime"]];
      performDateCell.value = clinicDataRow.performDate;
      performTimeCell.value = clinicDataRow.performTime;
      this.drawerVisible = false;
    },
    //#endregion
    //#region 进度条
    /**
     * @description: 关闭进度条
     * @return
     */
    progressClose() {
      this.showProgress = false;
      this.messageData[0].value = 1;
      this.messageData[1].value = "";
      this.messageData[2].value = "";
      this.messageData[3].value = "";
    },
    //#endregion
    //#region 虚拟键盘
    /**
     * @description: 显示虚拟键盘
     * @param event
     * @return
     */
    showKeyBoard(event) {
      if (!this.isReadOnly) {
        return;
      }
      this.el = event.target;
      this.isShowKeyBoard = true;
    },
    /**
     * @description: 隐藏虚拟键盘
     * @return
     */
    hideKeyBoard() {
      this.el = undefined;
      this.isShowKeyBoard = false;
    },
    //#endregion
    //#region 配置获取
    /**
     * @description: 获取复制粘贴功能是否开启
     * @return
     */
    getCopyEnableSetting() {
      const param = { settingType: 126, settingCode: "BatchCopyFlag" };
      GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          if (response.data.typeValue == "False") {
            this.copyEnabled = false;
          }
        }
      });
    },
    /**
     * @description: 获取疼痛评分阈值
     * @return
     */
    getPainScoreSetting() {
      const param = { settingType: 168, settingCode: "PainScoreThreshold" };
      GetOneSettingByTypeAndCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.painScoreThreshold = Number(response.data.typeValue);
        }
      });
    },
    /**
     * @description: 获取仪器数据范围配置
     * @return
     */
    getSettingTimeRange() {
      const params = {
        settingTypeCode: "ClinicDataTimeRange",
        typeValue: "TimeRange",
      };
      GetSettingValuesByTypeCodeAndValue(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.timeRange = Number(result?.data ?? 30);
        }
      });
    },
    /**
     * @description: 获取不进行复制的监测类目ID
     * @return
     */
    getNotCopyAssessListIDs() {
      GetClinicalSettingValuesBySettingTypeCode({
        settingTypeCode: "MonitorNotCopy",
      }).then((res) => {
        if (this._common.isSuccess(res)) {
          this.notCopyMonitorColumnIDs = res.data;
        }
      });
    },
    //#endregion
  },
};
</script>

<style lang="scss">
.monitoring-scheduler {
  .monitor-data-table {
    .perform-date,
    .perform-time {
      .no-save {
        .el-input__inner {
          color: #ff0000;
        }
      }
      .el-input__prefix {
        display: none;
      }
      .el-date-editor {
        width: 100%;
        .el-input__inner {
          padding-left: 3px;
        }
      }
    }
    .operate-column .operate {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin: 0 3px;
    }
  }
}
.specific-care-view {
  background-color: #f3f3f3;
  iframe {
    height: 99%;
    border: none;
  }
}
</style>
