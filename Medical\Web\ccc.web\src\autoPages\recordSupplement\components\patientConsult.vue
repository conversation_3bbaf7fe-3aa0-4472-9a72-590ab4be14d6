<!--
 * FilePath     : \src\autoPages\recordSupplement\components\patientConsult.vue
 * Author       : 胡长攀
 * Date         : 2023-10-09 11:40
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-17 16:42
 * Description  : 
 -->
 <!-- TODO：补录整合后，此组件可能不再需要 -->
<template>
  <base-layout header-height="auto" class="patient-consult-supplement">
    <search-patient-data
      class="patient-info"
      slot="header"
      @selectPatientData="selectPatientData"
    ></search-patient-data>
    <div class="layout-wrap">
      <patientConsult v-if="patient" :supplementPatient="patient" />
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData";
import patientConsult from "@/pages/patientConsult/index";
export default {
  components: {
    baseLayout,
    searchPatientData,
    patientConsult,
  },
  data() {
    return {
      patient: undefined,
      componentName: "patientConsult",
    };
  },
  methods: {
    /**
     * @description: 选择患者
     * @return
     * @param patient
     */
    async selectPatientData(patient) {
      this.patient = patient;
    },
  },
};
</script>

<style lang="scss" >
.base-layout.patient-consult-supplement {
  height: 100%;
  .base-header {
    padding: 0 0 10px 0;
  }
  .layout-wrap {
    height: 100%;
    .layout-top {
      padding: 0 10px;
      .data-select {
        width: 120px;
      }
      .tip {
        margin-left: 20px;
        color: #ff0000;
      }
      .top-btn {
        float: right;
      }
    }
  }
}
</style>