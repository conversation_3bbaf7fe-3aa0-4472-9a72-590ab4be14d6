/*
 * FilePath     : \ccc.web\src\api\Handover\HandoverTransfer.js
 * Author       : 郭鹏超
 * Date         : 2023-02-24 16:00
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-03-31 16:26
 * Description  :转运交班API
 * CodeIterationRecord:
 */
import http from "../../utils/ajax";
const baseUrl = "/HandoverTransfer";
const urls = {
  GetHandOverTransferAssessTemplate:
    baseUrl + "/GetHandOverTransferAssessTemplate",
  GetHandoverTransferSBAR: baseUrl + "/GetHandoverTransferSBAR",
  SaveTransferHandoverSBAR: baseUrl + "/SaveTransferHandoverSBAR",
  GetHandoverTransferList: baseUrl + "/GetHandoverTransferList",
  DeleteHandoverTransfer: baseUrl + "/DeleteHandoverTransfer",
  SaveTransferHandoverAssess: baseUrl + "/SaveTransferHandoverAssess",
  GetTransOutStationID: baseUrl + "/GetTransOutStationID"
};
//获取交班类型
export const GetHandOverTransferAssessTemplate = params => {
  return http.get(urls.GetHandOverTransferAssessTemplate, params);
};
//获取交班页签
export const GetHandoverTransferSBAR = params => {
  return http.get(urls.GetHandoverTransferSBAR, params);
};
//sbar保存
export const SaveTransferHandoverSBAR = params => {
  return http.post(urls.SaveTransferHandoverSBAR, params);
};
//获取转运列表
export const GetHandoverTransferList = params => {
  return http.get(urls.GetHandoverTransferList, params);
};
//删除交接记录
export const DeleteHandoverTransfer = params => {
  return http.get(urls.DeleteHandoverTransfer, params);
};
//转运评估保存
export const SaveTransferHandoverAssess = params => {
  return http.post(urls.SaveTransferHandoverAssess, params);
};
//获取转出病区ID
export const GetTransOutStationID = params => {
  return http.get(urls.GetTransOutStationID, params);
};
