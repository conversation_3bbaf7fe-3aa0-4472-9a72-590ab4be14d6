<!--
 * FilePath     : \src\pages\transferPages\transferSensitiveIndicator.vue
 * Author       : 孟昭永
 * Date         : 2021-01-21 16:15
 * LastEditors  : 孟昭永
 * LastEditTime : 2021-01-21 16:16
 * Description  : 
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getStatisticsUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  created() {
    this.url =
      getStatisticsUrl() + "sensitiveIndicator?token=" + this.token + "&hospitalID=" + this.hospitalInfo.hospitalID;
  },
};
</script>
<style lang="scss">
.transfer-statistics {
  height: 100%;
  width: 100%;
}
</style>