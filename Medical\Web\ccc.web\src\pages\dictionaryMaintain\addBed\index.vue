<!--
 * FilePath     : \src\pages\dictionaryMaintain\addBed\index.vue
 * Author       : 祝仕奇
 * Date         : 2021-11-18 16:22
 * LastEditors  : 苏军志
 * LastEditTime : 2022-01-12 18:40
 * Description  : 
-->
<template>
  <base-layout class="add-bed">
    <div slot="header">
      <station-selector width="150" v-model="stationID" :label="stationLabel"></station-selector>
      <span class="top-btn">
        <el-button class="add-button" icon="el-icon-plus " @click="editRow()">新增</el-button>
        <el-button class="add-button" icon="el-icon-plus " @click="getBedsInfo">加床标记维护</el-button>
      </span>
    </div>
    <div slot-scope="layoutData">
      <el-table
        :height="layoutData.height"
        :close-on-click-modal="false"
        v-loading="loading"
        :element-loading-text="loadingText"
        :data="tableData"
        border
        stripe
      >
        <el-table-column prop="extraBedNumber" label="加床床位" align="center"></el-table-column>
        <el-table-column prop="bedNumber" label="所在床位" align="center" sortable></el-table-column>
        <el-table-column prop="modifyPersonID" label="维护人员" align="center"></el-table-column>
        <el-table-column prop="modifyDate" label="维护时间" align="center">
          <template slot-scope="scope">
            <!-- 时间格式化 -->
            <span
              v-formatTime="{
                value: scope.row.modifyDate,
                type: 'date',
                format: 'yyyy-MM-dd hh:mm',
              }"
            ></span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-tooltip content="修改">
              <i class="iconfont icon-edit" @click="editRow(scope.row)"></i>
            </el-tooltip>

            <el-tooltip content="删除">
              <i class="iconfont icon-del" @click="deleteRow(scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 加床标识维护弹框 -->
      <el-dialog
        v-loading="loading"
        v-dialogDrag
        :element-loading-text="loadingText"
        :close-on-click-modal="false"
        title="加床标识维护"
        :visible.sync="dialogVisible"
        custom-class="add-bed-dialog"
      >
        <el-table :data="bedList" border>
          <el-table-column prop="bedNumber" label="床位" min-width="40"></el-table-column>
          <el-table-column prop="additionFlag" label="是否为加床">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.additionFlag" @change="changeRow(scope.row)"></el-checkbox>
            </template>
          </el-table-column>
        </el-table>

        <span slot="footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateAdditionBed()">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 新增弹框 -->
      <el-dialog
        v-loading="loading"
        v-dialogDrag
        element-loading-text="保存中……"
        :close-on-click-modal="false"
        title="床位绑定"
        :visible.sync="dialogAdd"
        custom-class="add-bed-dialog"
      >
        <div class="bed-selector">
          <span>加床床位：</span>
          <template>
            <el-select v-model="additionSelectedBed">
              <el-option
                v-for="item in additionBed"
                :key="item.id"
                :label="item.bedNumber"
                :value="item.id"
              ></el-option>
            </el-select>
          </template>
        </div>
        <div class="bed-selector">
          <span>所在床位：</span>
          <template>
            <el-select v-model="normalSelectedBed">
              <el-option v-for="item in normalBed" :key="item.id" :label="item.bedNumber" :value="item.id"></el-option>
            </el-select>
          </template>
        </div>
        <span slot="footer">
          <el-button @click="dialogAdd = false">取消</el-button>
          <el-button type="primary" @click="bindingBedInfo()">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 修改弹框 -->
      <el-dialog
        v-loading="loading"
        v-dialogDrag
        element-loading-text="保存中……"
        :close-on-click-modal="false"
        title="床位绑定"
        :visible.sync="dialogUpdate"
        custom-class="add-bed-dialog"
      >
        <div class="bed-selector">
          <span>加床床位：</span>
          <template>
            <el-select v-model="additionSelectedBed">
              <el-option
                v-for="item in additionBed"
                :key="item.id"
                :label="item.bedNumber"
                :value="item.bedNumber"
              ></el-option>
            </el-select>
          </template>
        </div>
        <div class="bed-selector">
          <span>所在床位：</span>
          <template>
            <el-select v-model="normalSelectedBed">
              <el-option
                v-for="item in normalBed"
                :key="item.id"
                :label="item.bedNumber"
                :value="item.bedNumber"
              ></el-option>
            </el-select>
          </template>
        </div>
        <span slot="footer">
          <el-button @click="dialogUpdate = false">取消</el-button>
          <el-button type="primary" @click="bindingBedUpdate()">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout.vue";
import stationSelector from "@/components/selector/stationSelector";
import { mapGetters } from "vuex";
import { GetAllExtraBedBinding, SaveExtraBedBinding, DeleteExtraBedBinding } from "@/api/ExtraBedBinding";
import { GetBedListByStationId, SaveAdditionFlag } from "@/api/BedList";
export default {
  components: {
    baseLayout,
    stationSelector,
  },
  data() {
    return {
      loading: false,
      loadingText: "加载中……",
      stationID: undefined,
      stationLabel: "病区：",
      tableData: [],
      dialogVisible: false,
      dialogAdd: false,
      dialogUpdate: false,
      bedList: [],
      additionBedIDs: [],
      //普通床
      normalBed: [],
      //加床
      additionBed: [],
      additionSelectedBed: "",
      normalSelectedBed: "",
      extraBedBindingID: undefined,
      bedID: undefined,
      extraBedID: undefined,
    };
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  watch: {
    bedList: {
      handler() {
        this.bedGroup();
      },
      deep: true,
    },
  },
  async created() {
    this.$watch("stationID", () => {
      this.getData();
    });
    this.stationID = this.user.stationID;
    await this.getBedList();
  },
  methods: {
    async getData() {
      let params = {
        stationID: this.stationID,
      };
      this.loading = true;
      await GetAllExtraBedBinding(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.tableData = res.data;
        }
      });
    },
    async getBedList(callback) {
      let params = {
        stationID: this.stationID,
      };
      this.additionBed = [];
      this.additionBedIDs = [];
      await GetBedListByStationId(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.bedList = res.data;
          this.bedList.forEach((item) => {
            if (item.additionFlag) {
              this.additionBedIDs.push(item.bedNumber);
            }
          });
        }
      });
      if (typeof callback == "function") {
        callback();
      }
    },
    changeRow(row) {
      if (row.additionFlag) {
        this.additionBedIDs.push(row.bedNumber);
      } else {
        var index = this.additionBedIDs.findIndex((bedNumber) => bedNumber == row.bedNumber);
        if (index != -1) {
          this.additionBedIDs.splice(index, 1);
        }
      }
    },
    // 要把加床的床和普通床分开
    bedGroup() {
      // 因中山有的加床有实际床位，这里不再限制
      this.normalBed = this.bedList;
      this.additionBed = [];
      this.bedList.filter((item) => {
        if (item.additionFlag) {
          this.additionBed.push(item);
        }
        // else {
        //   this.normalBed.push(item);
        // }
      });
    },
    async updateAdditionBed() {
      var params = {
        bedNumbers: this.additionBedIDs,
        stationID: this.stationID,
      };
      this.loading = true;
      //调用api，更新这些id的additionFlag为1
      await SaveAdditionFlag(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.dialogVisible = false;
        }
      });
    },
    //新增
    async bindingBedInfo() {
      let params = {
        stationID: this.stationID,
        bedNumber: this.normalBed.find((item) => item.id == this.normalSelectedBed).bedNumber,
        bedID: this.normalSelectedBed,
        extraBedNumber: this.additionBed.find((item) => item.id == this.additionSelectedBed).bedNumber,
        extraBedID: this.additionSelectedBed,
      };
      this.loading = true;
      await SaveExtraBedBinding(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          this.dialogAdd = false;
        }
      });
      this.getData();
    },
    editRow(row) {
      if (row) {
        this.dialogUpdate = true;
        //回显
        this.normalSelectedBed = row.bedNumber;
        this.additionSelectedBed = row.extraBedNumber;
        //存值
        this.extraBedBindingID = row.extraBedBindingID;
        this.bedID = row.bedID;
        this.extraBedID = row.extraBedID;
      } else {
        this.dialogAdd = true;
        this.normalSelectedBed = "";
        this.additionSelectedBed = "";
        this.extraBedBindingID = undefined;
        this.bedID = undefined;
        this.extraBedID = undefined;
      }
    },
    getBedsInfo() {
      this.getBedList(() => {
        this.dialogVisible = true;
      });
    },
    deleteRow(row) {
      this._deleteConfirm("", (flag) => {
        let _this = this;
        if (flag) {
          this.loading = true;
          let params = {
            StationID: row.stationID,
            ExtraBedID: row.extraBedID,
          };
          DeleteExtraBedBinding(params).then((res) => {
            this.loading = false;
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功!");
              this.getData();
            }
          });
        }
      });
    },
    //修改
    async bindingBedUpdate() {
      let params = {
        extraBedBindingID: this.extraBedBindingID,
        bedNumber: this.normalSelectedBed,
        bedID: this.bedID,
        extraBedNumber: this.additionSelectedBed,
        extraBedID: this.extraBedID,
      };
      this.loading = true;
      await SaveExtraBedBinding(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.getData();
          this.additionSelectedBed = undefined;
          this.normalSelectedBed = undefined;
          this.dialogUpdate = false;
          this._showTip("success", "修改成功");
        }
        this.getData();
      });
    },
  },
};
</script>

<style lang="scss">
.add-bed {
  .top-btn {
    float: right;
  }
  .bed-selector {
    margin: 10px 0;
  }
}
.add-bed-dialog {
  width: 450px;
  height: 450px;
}
</style>