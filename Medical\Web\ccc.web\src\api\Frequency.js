/*
 * FilePath     : \ccc.web\src\api\Frequency.js
 * Author       : 苏军志
 * Date         : 2020-05-08 20:11
 * LastEditors  : 李艳奇
 * LastEditTime : 2021-03-23 09:44
 * Description  :
 */
import http from "../utils/ajax";
const baseUrl = "/Frequency";

export const urls = {
  GetFrequency: baseUrl + "/GetFrequency",
  GetTypeFrequency: baseUrl + "/GetTypeFrequency",
  GetHisFrequency:baseUrl + "GetHisFrequency"
};

// 获取措施频次
export const GetFrequency = params => {
  return http.get(urls.GetFrequency, params);
};
// 获取措施频次
export const GetTypeFrequency = params => {
  return http.get(urls.GetTypeFrequency, params);
};
//获取医嘱频次
export const GetHisFrequency = params => {
  return http.get(urls.GetHisFrequency, params);
};

