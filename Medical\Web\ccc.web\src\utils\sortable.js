/*
 * FilePath     : \ccc.web\src\utils\sortable.js
 * Author       : 郭鹏超
 * Date         : 2022-08-07 16:23
 * LastEditors  : 郭鹏超
 * LastEditTime : 2022-08-09 11:19
 * Description  :表格排序方法
 * CodeIterationRecord:
 */

import Vue from "vue";
import Sortable from "sortablejs";
/**
 * description:
 * param {*} bodyDom 要排序表格DOM
 * param {*} _this 页面this
 * param {*} tableDataName 表格数据变量名
 * return {*}
 */
let tableRowDrop = (bodyDom, _this, tableDataName) => {
  Sortable.create(bodyDom, {
    group: {
      name: "words",
      pull: true,
      put: true
    },
    //拖拽结束触发
    onSort(row) {
      //之前位置删除
      let currentRow = _this[tableDataName].splice(row.oldIndex, 1)[0];
      //将删除数据添加至新位置
      _this[tableDataName].splice(row.newIndex, 0, currentRow);
      //添加异动标记
      _this.$set(_this[tableDataName][row.newIndex], "sortFlag", true);
    }
  });
};

Vue.prototype._tableRowDrop = tableRowDrop;
export default {
  tableRowDrop
};
