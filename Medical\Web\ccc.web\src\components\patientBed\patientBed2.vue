<!--
 * FilePath     : \src\components\patientBed\patientBed2.vue
 * Author       : 苏军志
 * Date         : 2020-04-15 16:41
 * LastEditors  : 苏军志
 * LastEditTime : 2022-09-02 09:38
 * Description  : 病人卡片组件2(上海中山和厦门中山使用)
 -->
<template>
  <div :class="['patient-bed2', { 'is-select': focusOn }, { 'filter-mask': patientInfo.filterMaskFlag }]">
    <div :class="['patient-info', { 'is-danger': patientInfo.isDanger }]">
      <tip-marker
        v-if="jobTipList && jobTipList.length > 0"
        class="job-tip"
        :width="100"
        :tipList="jobTipList"
        @jumpPage="tipJumpPage"
      ></tip-marker>
      <div class="info">{{ patientInfo.bedNumber }}</div>
      <div v-if="!emptyBed">
        <div class="info">{{ patientInfo.localCaseNumber }}</div>
        <div class="info patient-name" :title="patientInfo.patientName.trim()">
          {{ patientInfo.patientName.trim() }}
        </div>
        <div class="info">
          {{ patientInfo.gender + " " + (patientInfo.age ? patientInfo.age : "") }}
        </div>
        <div class="info diagnose" :title="patientInfo.diagnose">
          {{ patientInfo.diagnose ? patientInfo.diagnose : "&nbsp;" }}
        </div>
        <div class="info-last" v-if="!emptyBed">
          <div :class="['days', { empty: !patientInfo.days || patientInfo.days == 0 }]" title="住院天数">
            {{ patientInfo.days ? patientInfo.days : 0 }}
          </div>
          <div :class="['surgery', { empty: patientInfo.surgerydays == '0' }]" title="手术天数">
            {{ patientInfo.surgerydays == 999999 || !patientInfo.surgerydays == null ? "" : patientInfo.surgerydays }}
          </div>
          <div
            class="nursing"
            v-if="patientInfo.inPatientNursingLevelStyle"
            :style="getNursingStyle(patientInfo.inPatientNursingLevelStyle)"
          >
            {{ patientInfo.inPatientNursingLevelStyle.remark }}
          </div>
        </div>
      </div>
    </div>
    <div class="patient-mark">
      <div v-for="(item, index) in patientInfo.inPatientMarkStyleList" :key="index" class="mark-item">
        <div
          v-if="item.display"
          :style="{ backgroundColor: item.color }"
          :title="item.remarkDetail ? item.remarkDetail : item.remark"
          class="mark"
        >
          {{ item.iconText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import tipMarker from "@/components/patientBed/tipMarker";
export default {
  components: { tipMarker },
  props: {
    patientInfo: {},
    focusOn: { type: Boolean, default: false },
    jobTipList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  mounted() {
    this.emptyBed = this.patientInfo.chartNo == "" && this.patientInfo.caseNumber == "";
  },
  data() {
    return {
      emptyBed: true,
    };
  },

  methods: {
    getNursingStyle(nursingStyle) {
      if (!nursingStyle) return;
      return {
        color: nursingStyle.iconColor,
        backgroundColor: nursingStyle.backGroundColor,
      };
    },
    getPatientInfo() {
      this.$emit("getPatientInfo", this.patientInfo);
    },
    tipJumpPage(router) {
      if (!router) {
        return;
      }
      this.$emit("tipJumpPage", router);
    },
  },
};
</script>

<style lang="scss">
.patient-bed2 {
  float: left;
  display: flex;
  flex-direction: column;
  width: 185px;
  height: 200px;
  padding: 3px;
  border: 3px solid #f3f3f3;
  background-color: #ffffff;
  box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
  &.is-select {
    border-color: $base-color;
    background-color: #ebf7df;
  }
  &.filter-mask,
  &.filter-mask * {
    color: #ffffff !important;
    background-color: #e3e3e3 !important;
  }
  &:hover {
    background-color: #ebf7df;
  }
  .patient-info {
    position: relative;
    flex: auto;
    height: 100%;
    box-sizing: border-box;
    border-left: 5px solid transparent;
    &.is-danger {
      border-left-color: #ff0000;
    }
    .job-tip .tip-marker {
      top: -8px;
      left: -4px;
    }
    .info {
      margin-bottom: 5px;
      margin-left: 30px;
      &.patient-name,
      &.diagnose {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .info-last {
      margin-left: 0;
      div {
        display: inline-block;
        height: 20px;
        line-height: 20px;
        padding: 3px;
        margin: 3px;
        text-align: center;
        border-radius: 5px;
        background-color: #dddddd;
        /* 基线对齐 */
        vertical-align: top;
        &.days,
        &.surgery {
          min-width: 20px;
          max-width: 45px;
          font-size: 13px;
          font-weight: 600;
        }
        &.nursing {
          min-width: 10px;
          width: 40px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &.empty {
          color: #ffffff;
          background-color: blue;
        }
      }
    }
  }
  .patient-mark {
    height: 24px;
    line-height: 20px;
    text-align: right;
    border-top: 1px solid #f3f3f3;
    .mark-item {
      display: inline-block;
      .mark {
        display: inline-block;
        width: 16px;
        height: 16px;
        line-height: 15px;
        margin: auto 6px auto 0;
        font-size: 12px;
        cursor: default;
        color: #f6f6f6;
        text-align: center;
      }
    }
  }
}
</style>
