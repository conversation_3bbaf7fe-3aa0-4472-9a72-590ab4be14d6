/*
 * FilePath     : \src\api\TextEntry.js
 * Author       : 郭鹏超
 * Date         : 2025-06-17 10:47
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-06-17 10:48
 * Description  :上下限配置API接口
 * CodeIterationRecord:
 */

import http from "../utils/ajax";
const baseUrl = "/TextEntry";

export const urls = {
  GetTextEntriesByRecordsCode: baseUrl + "/GetTextEntriesByRecordsCode",
};
//根据recordsCode获取上下限配置
export const GetTextEntriesByRecordsCode = (params) => {
  return http.get(urls.GetTextEntriesByRecordsCode, params);
};
