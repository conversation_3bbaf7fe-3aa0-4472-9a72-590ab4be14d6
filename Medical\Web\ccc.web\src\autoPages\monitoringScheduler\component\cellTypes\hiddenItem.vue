<!--
 * FilePath     : \src\autoPages\monitoringScheduler\component\cellTypes\hiddenItem.vue
 * Author       : 杨欣欣
 * Date         : 2024-06-26 11:37
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-08-27 17:59
 * Description  : 
 * CodeIterationRecord: 此组件废弃
 -->
<!--<template>
  <span v-if="row[column.relationHiddenItemIndex]" v-show="true">
    <!~~ v-formula="{
      item: hiddenItem,
      items: row,
      formulaItem: row[column.index],
      columns: columns,
    }" ~~>
    {{ expression + " = " + hiddenItem.assessValue }}
  </span>
</template>

<script>-->
import { formula } from "../../mixins/formula";
export default {
  name: "hiddenItem",
  mixins: [formula],
  inject: ["validateInputValueAbnormal"],
  props: {
    row: {
      type: Object,
      required: true,
    },
    column: {
      type: Object,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
  },
  // watch: {
  //   "cell.assessValue": {
  //     handler() {
  //       const formula = this.hiddenItem?.formula;
  //       if (!formula) {
  //         return;
  //       }
  //       const params = formula.params;
  //       this.expression = formula.expression;
  //       const cells = Object.values(this.row);
  //       for (let param of params) {
  //         cells.forEach((cell) => {
  //           let value = 0;
  //           //处理下拉框公式计算
  //           if (cell.style == "D") {
  //             const option = this.columns
  //               .find((column) => column.index == cell.columnIndex)
  //               ?.childColumns?.find((child) => child.assessListID == param.id);
  //             if (option) {
  //               option.assessListID == cell.assessValue && (value = option.linkForm?.trim() || 0);
  //               this.expression = this.expression.replace(param.key, value);
  //             }
  //           }
  //           if (cell.assessListID == param.id) {
  //             value = cell.assessValue?.trim();
  //             this.expression = this.expression.replace(param.key, value);
  //           }
  //         });
  //       }
  //       if (this.expression.indexOf("}") > 0) {
  //         this.hiddenItem.assessValue = "";
  //         return;
  //       }
  //       let newValue = "";
  //       //解决eval(expression)中expression不规范导致报错
  //       try {
  //         newValue =
  //           this._decimalUtil.decimalRound(eval(this.expression), formula.decimalRule, formula.decimalValue) + "";
  //       } catch (error) {
  //         newValue = "";
  //       }
  //       if (this.hiddenItem.assessValue != newValue) {
  //         this.hiddenItem.assessValue = newValue;
  //         this.validateInputValueAbnormal(this.cell, this.hiddenItem);
  //       }
  //       this.hiddenItem.assessValue = newValue;
  //     },
  //     immediate: true,
  //   },
  // },
  created() {
    this.hiddenItem = this.row[this.column.relationHiddenItemIndex];
    this.cell = this.row[this.column.index];

    const formula = this.hiddenItem?.formula;
        if (!formula) {
          return;
        }
        const params = formula.params;
        this.expression = formula.expression;
        const cells = Object.values(this.row);
        for (let param of params) {
          cells.forEach((cell) => {
            let value = 0;
            //处理下拉框公式计算
            if (cell.style == "D") {
              const option = this.columns
                .find((column) => column.index == cell.columnIndex)
                ?.childColumns?.find((child) => child.assessListID == param.id);
              if (option) {
                option.assessListID == cell.assessValue && (value = option.linkForm?.trim() || 0);
                this.expression = this.expression.replace(param.key, value);
              }
            }
            if (cell.assessListID == param.id) {
              value = cell.assessValue?.trim();
              this.expression = this.expression.replace(param.key, value);
            }
          });
        }
        if (this.expression.indexOf("}") > 0) {
          this.hiddenItem.assessValue = "";
          return;
        }
        let newValue = "";
        //解决eval(expression)中expression不规范导致报错
        try {
          newValue =
            this._decimalUtil.decimalRound(eval(this.expression), formula.decimalRule, formula.decimalValue) + "";
        } catch (error) {
          newValue = "";
        }
        if (this.hiddenItem.assessValue != newValue) {
          this.hiddenItem.assessValue = newValue;
          this.validateInputValueAbnormal(this.cell, this.hiddenItem);
        }
        this.hiddenItem.assessValue = newValue;
  },
  data() {
    return {
      hiddenItem: undefined,
      cell: undefined,
      expression: "",
    };
  },
};
</script>