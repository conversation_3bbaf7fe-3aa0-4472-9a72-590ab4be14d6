<!--
 * FilePath     : \src\pages\caAuthorize.vue
 * Author       : 苏军志
 * Date         : 2020-08-24 11:46
 * LastEditors  : 来江禹
 * LastEditTime : 2022-11-28 17:25
 * Description  : 电子签名授权
-->
<template>
  <div class="ca-authorize">
    <div class="wrap">
      <div class="tip">请用手机上的“协同签名”APP扫描下方二维码进行授权</div>
      <div id="qrcode" class="qrcode"></div>
      <div class="btn">
        <el-button type="primary" @click="authorize">已扫码授权</el-button>
        <el-button class="print-button" v-if="buttonSwitchFalg" @click="jumpPage">&nbsp;跳过授权&nbsp;</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import QRCode from "qrcodejs2";
import { GetAuthorize, GetSignResult } from "@/api/CertificateAuthority";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
export default {
  components: { QRCode },
  data() {
    return {
      oldUrl: "",
      qrCode: "",
      signToken: "",
      buttonSwitchFalg: true,
    };
  },
  created() {
    this.url = this.$route.query.url;
    this.init();
    this.getSettingSwitch();
  },
  methods: {
    init() {
      GetAuthorize().then((result) => {
        if (this._common.isSuccess(result)) {
          this.signToken = result.data.signToken;
          this.qrCode = result.data.qrCode;
          this.$nextTick(function () {
            this.qrcode();
          });
        }
      });
    },
    getSettingSwitch() {
      let param = {
        SettingTypeCode: "LoginCAButtonSwitch",
      };
      GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.buttonSwitchFalg = response.data;
        }
      });
    },
    authorize() {
      let params = {
        signToken: this.signToken,
      };
      GetSignResult(params).then((result) => {
        if (this._common.isSuccess(result)) {
          if (!result.data || result.data.jobStatus === "UNSIGN") {
            this._showTip("warning", "请用扫码授权！");
          } else if (result.data.jobStatus === "EXPIRE") {
            this._showTip("warning", "二维码已失效，请刷新后重试！");
            this.init();
          } else if (result.data.jobStatus === "EVOKE") {
            this._showTip("warning", "服务端终止签名，请刷新后重试！");
            this.init();
          } else {
            this.jumpPage();
          }
        }
      });
    },
    jumpPage() {
      if (this.url && this.url.length > 0) {
        this.$router.replace(this.url);
      } else {
        this.$router.replace({ path: "/patientList" });
      }
    },
    //  生成二维码
    qrcode() {
      let that = this;
      let qrcode = new QRCode("qrcode", {
        width: 180,
        height: 180, // 高度
        text: this.qrCode, // 二维码内容
      });
    },
  },
};
</script>

<style lang="scss">
.ca-authorize {
  width: 100%;
  height: 100%;
  background-color: #808080;
  padding-top: 20%;
  .wrap {
    width: 100%;
    text-align: center;
    .tip {
      margin-bottom: 10px;
      color: #ffffff;
      font-weight: 600;
    }
    .qrcode {
      padding: 10px;
      background-color: #ffffff;
      width: 180px;
      height: 180px;
      margin: 0 auto;
    }
    .btn {
      margin-top: 10px;
      margin-right: 5px;
    }
  }
}
</style>
