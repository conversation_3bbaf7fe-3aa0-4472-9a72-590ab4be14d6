<!--
 * FilePath     : \src\autoPages\patientList\index.vue
 * Author       : 李青原
 * Date         : 2020-05-13 08:10
 * LastEditors  : 来江禹
 * LastEditTime : 2025-06-26 16:29
 * Description  : 重构病人清单页面
 -->
<template>
  <base-layout class="patient-list" headerHeight="auto" v-loading="loading" element-loading-text="加载中……">
    <mark-filter
      slot="header"
      :filterList="filterList"
      :showGroup="filterMarkShowGroup"
      :groupMap="filterMarkGroup"
      @filter-data="filterPatientByMark"
    ></mark-filter>
    <div class="patient-list-wrap">
      <base-layout class="patients" headerHeight="auto">
        <div slot="header" class="header-wrap">
          <div class="sub-filter-list">
            <div class="sub-filter" v-for="(subFilter, index) in subFilterList" :key="index">
              <span class="filter-name">{{ subFilter.name }}：</span>
              <el-checkbox-group v-model="subFilterValues[index]">
                <el-checkbox
                  :style="{ '--color': item.color }"
                  v-for="(item, cindex) in subFilter.items"
                  :key="cindex"
                  :label="item.identifyID"
                >
                  {{ item.remark }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <el-select
            v-if="departmentSwitchFlag"
            placeholder="选择科室"
            class="department-select"
            clearable
            v-model="departmentID"
            @change="filterPatientData()"
          >
            <el-option
              v-for="(item, index) in departmentList"
              :key="index"
              :label="item.value"
              :value="item.id"
            ></el-option>
          </el-select>
          <el-input
            class="patient-query"
            v-model="selectBedNumber"
            @keyup.enter.native="selectPatientByBedNumber"
            placeholder="床号、姓名、病案号"
            @clear="selectPatientByBedNumber"
            clearable
          >
            <i slot="append" class="iconfont icon-search" @click="selectPatientByBedNumber"></i>
          </el-input>
          <div :class="['card-type', { 'is-select': cardType == 1 }]" @click="cardType = 1">
            <i class="iconfont icon-big-icon"></i>
            大图
          </div>
          <div :class="['card-type', { 'is-select': cardType == 2 }]" @click="cardType = 2">
            <i class="iconfont icon-small-icon"></i>
            小图
          </div>
          <div :class="['card-type', { 'is-select': cardType == 3 }]" @click="cardType = 3">
            <i class="iconfont icon-list-icon"></i>
            列表
          </div>
        </div>
        <div class="bed-list" v-show="cardType == 1 || cardType == 2">
          <component
            v-show="showPatientList && showPatientList.length > 0"
            class="bed"
            v-for="(patient, index) in showPatientList"
            :key="patient.bedNumber + patient.bedID + index"
            :is="`patientBed${patientCardStyle}`"
            :style="bedComponentStyle"
            :patientInfo="patient"
            :isPrintable="whetherToShow"
            :focus-on="patient.bedNumber === onPatient.bedNumber && patient.caseNumber === onPatient.caseNumber"
            :whole="cardType == 1"
            :showGroup="filterMarkShowGroup"
            :jobTipList="getPatientJobTip(patient)"
            @tipJumpPage="tipJumpPage($event, patient)"
            @click.native="focusOn(patient)"
            @bedside-card="bedsideCard"
            v-contextmenu:contextmenu
          ></component>
          <v-contextmenu ref="contextmenu" @contextmenu="selectPatient" class="right-menu">
            <template v-for="(menu, index) in rightMenus">
              <div v-if="menu.children" :key="index" class="submenu">
                <div class="menu-icon">
                  <i :class="menu.iconName"></i>
                </div>
                <div class="divider-line"></div>
                <v-contextmenu-submenu :title="menu.menuName" :key="index">
                  <v-contextmenu-item
                    v-for="(child, cIndex) in menu.children"
                    :key="cIndex"
                    @click="toPage(child.router)"
                  >
                    {{ child.menuName }}
                  </v-contextmenu-item>
                </v-contextmenu-submenu>
              </div>
              <v-contextmenu-item v-else :key="index" @click="toPage(menu.router)">
                <div class="menu-icon">
                  <i :class="menu.iconName"></i>
                </div>
                <div class="divider-line"></div>
                {{ menu.menuName }}
              </v-contextmenu-item>
            </template>
          </v-contextmenu>
        </div>
        <div class="list-data" v-show="cardType == 3" v-contextmenu:contextmenu>
          <el-table border stripe="" :data="showPatientList" @row-contextmenu="rightClick" height="100%">
            <el-table-column label="床位" :width="convertPX(150)" align="center">
              <template slot-scope="scope">
                {{ scope.row.bedNumber + (scope.row.bedRemark ? " - " + scope.row.bedRemark : "") }}
              </template>
            </el-table-column>
            <el-table-column prop="patientName" label="姓名" :width="convertPX(150)" align="center"></el-table-column>
            <el-table-column
              label="入院时间"
              :min-width="convertPX(120)"
              header-align="center"
              align="center"
            >
            <template slot-scope="scope" v-if="scope.row.caseNumber">
            <span v-formatTime="{ value: scope.row.admissionDate, type: 'date' }"></span>
            <span v-formatTime="{ value: scope.row.admissionTime, type: 'time' }"></span>
          </template>
          </el-table-column>
            <el-table-column prop="gender" label="性别" :width="convertPX(65)" align="center"></el-table-column>
            <el-table-column label="护理级别" :width="convertPX(120)" align="center">
              <template slot-scope="scope">
                {{
                  scope.row.inPatientNursingLevelStyle
                    ? scope.row.inPatientNursingLevelStyle.remark
                    : scope.row.nursingLevel
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="careNurse1"
              label="责任护士1"
              :width="convertPX(120)"
              header-align="center"
            ></el-table-column>
            <el-table-column
              prop="careNurse2"
              label="责任护士2"
              :width="convertPX(120)"
              header-align="center"
            ></el-table-column>
          <el-table-column
              prop="physicianName"
              label="主治医师"
              :min-width="convertPX(120)"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="chartNo"
              label="病历号"
              :width="convertPX(150)"
              header-align="center"
            ></el-table-column>
            <el-table-column label="生日" :width="convertPX(160)" align="center">
              <template slot-scope="scope">
                {{ scope.row.dateOfBirth }}
              </template>
            </el-table-column>
            <el-table-column
              prop="diagnose"
              label="诊断"
              :min-width="convertPX(200)"
              header-align="center"
            ></el-table-column>
            <el-table-column
              v-if="admWardFlag"
              prop="admWardDateTime"
              label="入科时间"
              :min-width="convertPX(80)"
              header-align="center"
              align="center"
            ></el-table-column>
          </el-table>
        </div>
        <el-dialog
          v-dialogDrag
          :close-on-click-modal="false"
          :title="patientTitle"
          custom-class="patient-label no-footer"
          :visible.sync="bedsideCardControl"
          v-if="whetherToShow"
        >
          <div>
            <div class="btn">
              <el-button type="primary" @click="printCard">标签打印</el-button>
            </div>
            <div class="qrcode">
              <img :src="imgSrc" alt="床头卡" />
            </div>
          </div>
        </el-dialog>
      </base-layout>
    </div>
  </base-layout>
</template>
<script>
import BaseLayout from "@/components/BaseLayout";
import PatientBed1 from "./components/PatientBed1";
import PatientBed2 from "./components/patientBed2";
import markFilter from "./components/markFilter";
import patientMark from "./components/patientMark";
import { GetInpatientDataList, GetPatientBedLabel } from "@/api/Inpatient";
import { GetBySettingTypeCodeByArray, GetIconCategoryDict } from "@/api/Setting";
import { GetJobTipList, QueryPatientJobTipList } from "@/api/JobTip";
import { wp } from "@/utils/web-proxy";
import { GetMenuList } from "@/api/Menu";
import { mapGetters } from "vuex";
import { GetDepartmentDataByStationID } from "@/api/Station";
import { GetSignLoginStatus } from "@/api/User";
export default {
  components: { BaseLayout, PatientBed1, PatientBed2, markFilter, patientMark },
  computed: {
    ...mapGetters({
      hospitalInfo: "getHospitalInfo",
      currentPatient: "getCurrentPatient",
      user: "getUser",
    }),
  },
  data() {
    return {
      nowPatient: undefined,
      patientList: [],
      filterList: [],
      subFilterList: [],
      subFilterValues: [],
      rightMenus: [],
      onPatient: undefined,
      cardType: 1,
      showPatientList: [],
      filterPatientList: [],
      imgRoute: "",
      bedsideCardControl: false,
      patientTitle: "",
      activeName: "bedsideCard",
      firstClickCaseNumber: "",
      clickNum: 0,
      loading: false,
      selectBedNumber: "",
      imgSrc: "",
      // 床头卡参数
      besideCardParam: {},
      careNurse: false,
      cloneFilterList: [],
      patientListMask: false,
      //是否展示床头卡
      whetherToShow: undefined,
      // 是否显示工作提醒
      showJobTip: undefined,
      jobTipList: undefined,
      patientJobTipList: [],
      // 非自适应时，床位组件宽度
      bedComponentStyle: {},
      //科室筛选下拉框列表
      departmentList: [],
      //科室筛选功能开关
      departmentSwitchFlag: false,
      //选择科室ID
      departmentID: undefined,
      //是否显示入科时间
      admWardFlag: false,
      // 患者卡片显示样式
      patientCardStyle: "1",
      // 过滤标签分组显示
      filterMarkShowGroup: true,
      // 分组字典
      filterMarkGroup: {},
    };
  },
  watch: {
    cardType() {
      this.setShowPatientList();
    },
    subFilterValues() {
      this.filterData();
    },
  },
  async created() {
    // 账号CA校验
    this.getSignLoginStatus();
    // 获取相关配置
    await this.GetBySettings();
    // 获取标记分类字典
    await this.getIconCategoryDict();
    if (this.showJobTip) {
      // 获取工作提醒清单
      await this.getJobTipList();
    }
    //  加载病人信息
    this.getPatientList();
    // 画面加载完再加载右键菜单，防止与获取左侧菜单API同时调用造成后端缓存贯穿
    this.$nextTick(async () => {
      await this.getMenu();
    });
  },
  methods: {
    /**
     * description: 系统顶部刷新按钮触发
     * return {*}
     */
    async refreshData() {
      this.loading = true;
      this.showPatientList = [];
      await this.getPatientList();
      this.loading = false;
    },
    getRefreshParams() {
      this.loading = true;
    },

    /**
     * @description: 获取标记分类字典
     */
    async getIconCategoryDict() {
      await GetIconCategoryDict().then((response) => {
        if (this._common.isSuccess(response) && response.data) {
          this.filterMarkGroup = response.data;
        }
      });
    },
    /**
     * description: 获取工作提醒清单
     * param {*}
     * return {*}
     */
    async getJobTipList() {
      let params = {
        systemCode: "CCC",
      };
      await GetJobTipList(params).then((response) => {
        if (this._common.isSuccess(response) && response.data) {
          this.jobTipList = response.data;
        }
      });
    },

    /**
     * description: 获取患者清单
     * param {*}
     * return {*}
     */
    async getPatientList() {
      this.loading = true;
      await GetInpatientDataList().then((response) => {
        this.loading = false;
        if (this._common.isSuccess(response)) {
          if (!response.data) {
            return;
          }
          this.patientList = response.data.inPatientList;
          this.showPatientList = this._common.clone(this.patientList);
          this.filterPatientList = this.showPatientList;
          if (this.showJobTip) {
            this.getPatientTipList();
          }
          this.deelMarkList(response.data.inPatientMarkStatistics);
          this.setDefaultSelectPatient();
        }
      });
    },
    /**
     * @description: 处理标记
     */
    deelMarkList(inPatientMarkStatistics) {
      if (this.filterMarkShowGroup) {
        this.filterList = this.deelMarkGroup(inPatientMarkStatistics);
      } else {
        this.filterList = inPatientMarkStatistics;
      }
      this.cloneFilterList = this._common.clone(this.filterList);
      if (this.filterMarkShowGroup) {
        this.filterList["PatientTotal"] && (this.filterList["PatientTotal"][0].isCheck = true);
      } else {
        //默认勾选第一个显示全部病人
        this.filterList?.length && (this.filterList[0].isCheck = true);
      }
    },
    /**
     * @description: 标签转换为分组
     * @param markStatistics
     * @return
     */
    deelMarkGroup(markStatistics) {
      const markStatisticsGroup = markStatistics.reduce((result, item) => {
        if (!result[item["labelCategory"]]) {
          result[item["labelCategory"]] = [];
        }
        result[item["labelCategory"]].push(item);
        return result;
      }, {});
      return markStatisticsGroup;
    },
    /**
     * @description: 设置默认选择患者
     */
    setDefaultSelectPatient() {
      // 如果缓存中有当前病人，选中
      if (this.currentPatient && this.currentPatient.caseNumber) {
        this.onPatient =
          this.patientList.find((patient) => {
            return (
              patient.caseNumber != "" &&
              patient.caseNumber != "0" &&
              patient.name != "" &&
              this.currentPatient.caseNumber == patient.caseNumber
            );
          }) ?? {};
      } else {
        // 缓存中当前没有病人，取第一个有效病人
        this.onPatient =
          this.patientList.find((patient) => {
            return patient.caseNumber != "" && patient.caseNumber != "0" && patient.name != "";
          }) ?? {};
      }
      this.focusOn(this.onPatient);
    },
    /**
     * description: 获取患者的工作提醒
     * param {*} patient
     * return {*}
     */
    getPatientJobTip(patient) {
      if (!this.patientJobTipList || this.patientJobTipList.length <= 0) {
        return [];
      }
      let patientJobTip = this.patientJobTipList.find(
        (patientJobTip) => patientJobTip.inpatientID == patient.inpatientID
      );
      if (patientJobTip) {
        return patientJobTip.jobTipList;
      }
      return [];
    },
    /**
     * description: 设置每个患者的工作提醒
     * param {*}
     * return {*}
     */
    getPatientTipList() {
      if (!this.patientList || this.patientList.length <= 0 || !this.jobTipList || this.jobTipList.length <= 0) {
        return;
      }
      this.patientJobTipList = [];
      // 获取profileMark提醒清单
      let profileMarkTipList = this.jobTipList.filter((tip) => tip.api == "GetProfileMarkTip" && tip.sourceID);
      // 获取非profileMark提醒清单
      let tipList = this.jobTipList.filter((tip) => tip.api != "GetProfileMarkTip");
      this.patientList.forEach((patient) => {
        if (patient.inpatientID) {
          // profileMark的提醒直接从patientList的inPatientMarkStyleList获取
          let patientTipList = this.getProfileMarkTip(profileMarkTipList, patient);
          // 非profileMark的提醒从后端API获取
          if (tipList && tipList.length > 0) {
            let params = {
              inpatientID: patient.inpatientID,
              stationID: patient.stationID,
              tipList: tipList,
            };
            QueryPatientJobTipList(params).then((response) => {
              if (this._common.isSuccess(response) && response.data && response.data.length > 0) {
                patientTipList = [...patientTipList, ...response.data];
              }
              // 调用自定义排序方法，正序排序
              patientTipList.sortBy("sort", "a");
              this.patientJobTipList.push({
                inpatientID: patient.inpatientID,
                jobTipList: patientTipList,
              });
            });
          }
        }
      });
    },
    /**
     * description: 从patientList的inPatientMarkStyleList获取提示数据
     * param {*} profileMarkTipList profileMark提醒清单
     * param {*} patient 病人
     * return {*}
     */
    getProfileMarkTip(profileMarkTipList, patient) {
      let patientTipList = [];
      if (
        !profileMarkTipList ||
        profileMarkTipList.length <= 0 ||
        !patient.inPatientMarkStyleList ||
        patient.inPatientMarkStyleList.length <= 0
      ) {
        return patientTipList;
      }
      profileMarkTipList.forEach((tip) => {
        let mark = patient.inPatientMarkStyleList.find((mark) => tip.sourceID == mark.identifyID);
        if (mark) {
          let patientTip = {
            tipCode: tip.tipCode,
            tipContent: tip.tipContent,
            router: tip.router,
            remark: mark.remarkDetail,
            sort: tip.sort,
          };
          patientTipList.push(patientTip);
        }
      });
      return patientTipList;
    },
    /**
     * description: 工作提醒跳转画面
     * param {*} router 画面路由
     * param {*} patient 当前患者信息
     * return {*}
     */
    tipJumpPage(router, patient) {
      if (!router) {
        return;
      }
      this.focusOn(patient);
      this.$router.push({ path: router });
    },
    /**
     * description: 患者卡片单双击事件
     * param {*} item
     * return {*}
     */
    async focusOn(item) {
      this.onPatient = item ?? {};
      this.setPatientInfo(item);
      this.clickNum++;
      // 双击事件
      setTimeout(() => {
        this.clickNum = 0;
        this.firstClickCaseNumber = undefined;
      }, 500);
      if (this.clickNum > 1) {
        if (this.firstClickCaseNumber && this.firstClickCaseNumber == item.caseNumber) {
          await this.setPatientInfo(item);
          this.clickNum = 0;
          this.firstClickCaseNumber = undefined;
          this.$router.push({ name: "patientProfile" });
        } else {
          this.clickNum = 1;
          this.firstClickCaseNumber = item.caseNumber;
        }
      } else {
        // 第一次单击
        this.setPatientInfo(item);
        this.firstClickCaseNumber = item.caseNumber;
      }
    },
    /**
     * description: 筛选患者
     * param {*} item
     * return {*}
     */
    async filterPatientByMark(item) {
      // 先处理科室过滤
      if (this.departmentID) {
        this.showPatientList = this.showPatientList.filter((patient) => {
          return patient.departmentListID == this.departmentID;
        });
      }
      // 如果是patientCount特殊处理
      if (item.recordName == "PatientCount") {
        let remark = "";
        if (!this.careNurse) {
          let nursePatient = this.patientList.filter((item) => {
            return item.careNurse1ID == this.user.userID;
          });
          if (nursePatient.length <= 0) {
            this._showTip("warning", "您尚未进行派班");
            return;
          }
          this.careNurse = true;
          remark = "责护人数";
          this.filterData();
        } else {
          this.careNurse = false;
          remark = "病人总数";
          this.showPatientList = this.patientList;
          this.filterList = this._common.clone(this.cloneFilterList);
        }
        if (this.filterMarkShowGroup) {
          this.filterList["PatientTotal"][0].isCheck = true;
          this.$set(this.filterList["PatientTotal"][0].style, "remark", remark);
        } else {
          this.filterList[0].isCheck = true;
          this.$set(this.filterList[0].style, "remark", remark);
        }
      }
      //点击了其他按钮
      else {
        //其他按钮
        if (item.isCheck) {
          item.isCheck = false;
        } else {
          //某个选项被选中
          item.isCheck = true;
          // 如果是护理级别，只能选一个
          if (item.isNursing) {
            if (this.filterMarkShowGroup) {
              for (let i = 1; i < this.filterList[item.labelCategory].length; i++) {
                if (
                  this.filterList[item.labelCategory][i].isNursing &&
                  this.filterList[item.labelCategory][i] != item
                ) {
                  this.filterList[item.labelCategory][i].isCheck = false;
                }
              }
            } else {
              for (let i = 1; i < this.filterList.length; i++) {
                if (this.filterList[i].isNursing && this.filterList[i] != item) {
                  this.filterList[i].isCheck = false;
                }
              }
            }
          }
        }
        this.filterData();
        this.setSubFilterList(item);
      }
    },
    /**
     * description: 组装筛选子项
     * param {*} item
     * return {*}
     */
    setSubFilterList(item) {
      if (!item.children) {
        return;
      }
      // 移除
      if (!item.isCheck && this.subFilterList?.length) {
        let index = this.subFilterList.findIndex((subFilter) => subFilter.identifyID == item.style.identifyID);
        if (index != -1) {
          this.subFilterList.splice(index, 1);
          this.subFilterValues.splice(index, 1);
        }
        return;
      }
      // 新增
      let childList = [];
      item.children.forEach((child) => {
        let remark = "";
        if (child?.style?.remark) {
          remark = child?.style?.remark;
          if (remark.includes("-")) {
            remark = remark.split("-")[1];
          }
        }
        childList.push({
          identifyID: child.style.identifyID,
          remark: remark,
          color: child.style.iconColor,
        });
      });
      this.subFilterList.push({
        name: item.style.remark,
        identifyID: item.style.identifyID,
        items: childList,
      });
      this.subFilterValues.push([]);
    },
    /**
     * description: 过滤患者
     * param {*}
     * return {*}
     */
    filterData() {
      this.showPatientList = [];
      let tempDatas = [];
      let filterList = this.filterList;
      if (this.filterMarkShowGroup) {
        filterList = Object.values(this.filterList).reduce((result, filter) => [...result, ...filter], []);
      }
      if (this.careNurse) {
        tempDatas = this.patientList.filter((item) => {
          return item.careNurse1ID == this.user.userID || item.careNurse2ID == this.user.userID;
        });
        this.calculationPatientCount(tempDatas, filterList);
      } else {
        tempDatas = this._common.clone(this.patientList);
      }
      for (let i = 1; i < filterList.length; i++) {
        let filterDatas = [];
        let filter = filterList[i];
        if (filter.isCheck && filter.filterKeys) {
          tempDatas.forEach((data) => {
            if (filter.isNursing) {
              //根据护理级别ID判断对象
              if (
                data.inPatientNursingLevelStyle &&
                data.inPatientNursingLevelStyle.identifyID == filter.style.identifyID
              ) {
                filterDatas.push(data);
              }
            } else {
              // 非护理级别
              let filterKeys = filter.filterKeys;
              // 如果有子选项，优先以子选项为主
              if (filter.children && this.subFilterValues?.length) {
                let keys = filter.filterKeys.filter(
                  (key) =>
                    this.subFilterValues.filter((values) => values.findIndex((value) => value == key) != -1)?.length
                );
                // 有子项，覆盖filterKeys
                if (keys?.length) {
                  filterKeys = keys;
                }
              }
              // 过滤数据
              for (let k = 0; k < filterKeys.length; k++) {
                if (!data.inPatientMarkStyleList) break;
                let mark = data.inPatientMarkStyleList.find((markStyle) => {
                  return markStyle.identifyID == filterKeys[k] && markStyle.recordName == filter.recordName;
                });
                if (mark) {
                  filterDatas.push(data);
                }
              }
            }
          });
          tempDatas = filterDatas;
        }
      }
      this.filterPatientList = tempDatas;
      this.setShowPatientList();
    },
    /**
     * description: 设置显示患者信息
     * param {*}
     * return {*}
     */
    setShowPatientList() {
      // 判断是否反灰显示
      if (this.patientListMask && this.cardType !== 3) {
        let temp = this._common.clone(this.patientList);
        for (let i = 0; i < temp.length; i++) {
          let flag = false;
          for (let j = 0; j < this.filterPatientList.length; j++) {
            if (temp[i].inpatientID == this.filterPatientList[j].inpatientID) {
              flag = true;
              break;
            }
          }
          if (!flag) {
            temp[i].filterMaskFlag = true;
          }
        }
        this.showPatientList = temp;
      } else {
        this.showPatientList = this.filterPatientList;
      }
      if (this.departmentID) {
        this.showPatientList = this.showPatientList.filter((patient) => {
          return patient.departmentListID == this.departmentID;
        });
      }
      if (this.selectBedNumber) {
        let text = this.selectBedNumber;
        let patient = this.showPatientList.filter((patient) => {
          return patient.bedNumber == text;
        });
        if (patient.length == 0) {
          for (var i = 0; i < this.showPatientList.length; i++) {
            if (text.length > 3 && this.showPatientList[i].chartNo.indexOf(text) > -1) {
              patient.push(this.showPatientList[i]);
              continue;
            }
            if (this.showPatientList[i].patientName.indexOf(text) > -1) {
              patient.push(this.showPatientList[i]);
              continue;
            }
          }
        }
        this.showPatientList = patient;
      }
    },
    /**
     * @description: 计算主责护士是当前登录护士的病人数量
     * @param newPaintList
     * @param filterList
     * @return
     */
    calculationPatientCount(newPaintList, filterList) {
      let titleList = [];
      let count = 0;
      filterList.forEach((element) => {
        count = 0;
        element.filterKeys.forEach((identifyID) => {
          count += this.getFlagCount(identifyID, newPaintList, element.isNursing);
        });
        if (count > 0) {
          element.identifyIDCount = count;
          titleList.push(element);
        }
      });
      this.filterList = this.deelMarkGroup(titleList);
    },
    /**
     * description: 计算标记数量
     * param {*} identifyID 主键
     * param {*} newPaintList
     * param {*} isNursing 是否为护理级别
     * return {*}
     */
    getFlagCount(identifyID, newPaintList, isNursing) {
      let count = 0;
      newPaintList.forEach((element) => {
        if (isNursing) {
          if (element.inPatientNursingLevelStyle && element.inPatientNursingLevelStyle.identifyID == identifyID) {
            count++;
          }
        } else {
          element.inPatientMarkStyleList.forEach((item) => {
            if (item.identifyID == identifyID) {
              count++;
            }
          });
        }
      });
      return count;
    },
    /**
     * description: 获取右键菜单
     * param {*}
     * return {*}
     */
    async getMenu() {
      let params = {
        system: "CCC",
        menuType: "PatientShortCut",
      };
      //得到病人右击选项集合
      await GetMenuList(params).then((response) => {
        if (this._common.isSuccess(response)) {
          this.rightMenus = response.data;
        }
      });
    },
    /**
     * description: 床头卡
     * param {*} patientInfo
     * return {*}
     */
    bedsideCard(patientInfo) {
      this.imgSrc = "";
      this.patientTitle = patientInfo.bedNumber + "-" + patientInfo.patientName + "[床头卡]";
      this.besideCardParam = {};
      let param = { inPatientID: patientInfo.inpatientID };
      GetPatientBedLabel(param).then((response) => {
        if (this._common.isSuccess(response)) {
          let data = response.data;
          this.besideCardParam = {
            HospitalName: data.hospitalName,
            BedNumber: data.patient.bedNumber,
            PatientName: data.patient.patientName,
            Sex: data.patient.gender,
            Age: data.patient.ageDetail,
            InPatientNo: data.patient.localCaseNumber,
            ChartNo: data.patient.chartNo,
            Doctor: data.patient.physicianName,
            Nurse: data.patient.careNurse,
            Diagnosis: data.patient.diagnose,
            DeptName: data.wardName,
            NursingClass: data.patient.nursingLevel,
            Diet: data.patient.diet,
            AllergyDrugs: data.patient.allergyDrugs,
            AdmWardDateTime: data.patient.admissionDateTimeView,
            infectiousDiseaseFlag: data.patient.infectiousDiseaseFlag,
            HospitalID: this.hospitalInfo ? this.hospitalInfo.hospitalID : "",
          };
          wp.print.viewPatienBedLabel(this.besideCardParam, (response) => {
            if (response.success) {
              this.imgSrc = response.data;
            } else {
              this._showTip(
                "warning",
                response.message ? response.message : "连接打印服务失败！请确认打印插件是否启动!"
              );
            }
          });
        }
      });
      this.bedsideCardControl = true;
    },
    /**
     * description: 打印床头卡
     * param {*}
     * return {*}
     */
    printCard() {
      wp.print.printPatienBedLabel(this.besideCardParam, (response) => {
        if (!response.success) {
          this._showTip("warning", "打印失败：" + response.message);
        }
      });
    },
    /**
     * description: 选择病人
     * param {*} vnode
     * return {*}
     */
    selectPatient(vnode) {
      // 获取当前病人
      if (vnode && vnode.child) {
        const patientInfo = vnode.child.patientInfo;
        this.setPatientInfo(patientInfo);
        this.onPatient = patientInfo ?? {};
      }
    },
    /**
     * description: 设置患者信息到缓存
     * param {*} patientInfo
     * return {*}
     */
    setPatientInfo(patientInfo) {
      if (!patientInfo) {
        return;
      }
      let currentPatient = {
        bedNumber: patientInfo.bedNumber,
        inpatientID: patientInfo.inpatientID,
        stationID: patientInfo.stationID,
        caseNumber: patientInfo.caseNumber,
        chartNo: patientInfo.chartNo,
        admissionDate: patientInfo.admissionDate,
        localCaseNumber: patientInfo.localCaseNumber,
        departmentCode: patientInfo.departmentCode,
      };
      this.$store.commit("session/setCurrentPatient", currentPatient);
      this.$store.commit("session/setPatientInfo", undefined);
    },
    /**
     * description: 右键菜单页面跳转
     * param {*} url
     * return {*}
     */
    toPage(url) {
      if (!this.onPatient || this.onPatient.caseNumber == "0") {
        this._showTip("warning", "此床号没有病人");
        this.$store.commit("session/setPatientInfo", undefined);
        return;
      }
      if (url.indexOf("?") == -1) {
        url += "?shortCutFlag=true";
      } else {
        url += "&shortCutFlag=true";
      }
      this.$router.push({ path: url });
    },
    /**
     * description: 根据床号过滤患者
     * param {*}
     * return {*}
     */
    selectPatientByBedNumber() {
      if (this.selectBedNumber == "") {
        this.showPatientList = this._common.clone(this.patientList);
        if (this.departmentID) {
          this.showPatientList = this.showPatientList.filter((patient) => {
            return patient.departmentListID == this.departmentID;
          });
          return;
        }
        return;
      }
      this.showPatientList = [];
      let text = this.selectBedNumber;
      let patient = this.patientList.filter((patient) => {
        return patient.bedNumber == text;
      });
      if (patient.length == 0) {
        for (var i = 0; i < this.patientList.length; i++) {
          if (text.length > 3 && this.patientList[i].chartNo.indexOf(text) > -1) {
            patient.push(this.patientList[i]);
            continue;
          }
          if (this.patientList[i].patientName.indexOf(text) > -1) {
            patient.push(this.patientList[i]);
            continue;
          }
        }
      }
      this.showPatientList = patient;
      if (this.departmentID) {
        this.showPatientList = this.showPatientList.filter((patient) => {
          return patient.departmentListID == this.departmentID;
        });
      }
    },
    /**
     * description: 右键
     * param {*} row
     * param {*} column
     * param {*} event
     * return {*}
     */
    rightClick(row) {
      // //取得当前病人
      const patientInfo = row;
      this.setPatientInfo(patientInfo);
      this.onPatient = patientInfo ?? {};
    },

    /**
     * description: 科室筛选展示病人数据以及筛选页签数据
     * param {*}
     * return {*}
     */
    async filterPatientData() {
      this.subFilterList = [];
      this.subFilterValues = [];
      if (!this.departmentID) {
        this.showPatientList = this._common.clone(this.patientList);
        this.filterList = this._common.clone(this.cloneFilterList);
        return;
      }
      this.showPatientList = this.patientList.filter((patient) => {
        return patient.departmentListID == this.departmentID;
      });
      this.filterList = this._common.clone(this.cloneFilterList);
      this.filterList.forEach((filter) => {
        if (filter.recordName == "PatientCount") {
          let inpatientIDs = filter.inpatientIDs.filter((id) => {
            return this.showPatientList.find((showPatient) => {
              return showPatient.inpatientID == id;
            });
          });
          filter.identifyIDCount = inpatientIDs.length;
          filter.inpatientIDs = inpatientIDs;
        } else if (filter.labelCategory == "NursingLevel") {
          let inpatientIDs = filter.inpatientIDs.filter((inpatientID) => {
            for (let index = 0; index < filter.filterKeys.length; index++) {
              return this.showPatientList.find((showPatient) => {
                if (
                  showPatient.inPatientNursingLevelStyle &&
                  showPatient.inPatientNursingLevelStyle.identifyID == filter.filterKeys[index]
                ) {
                  return inpatientID == showPatient.inpatientID;
                }
              });
            }
          });
          filter.identifyIDCount = inpatientIDs.length;
          filter.inpatientIDs = inpatientIDs;
        } else {
          let patientList = this.showPatientList.filter((showPatient) => {
            var markStyleList = showPatient.inPatientMarkStyleList;
            if (!markStyleList || markStyleList.length <= 0) {
              return false;
            }
            let mark = false;
            for (let index = 0; index < filter.filterKeys.length; index++) {
              if (markStyleList.find((m) => m.identifyID == filter.filterKeys[index])) {
                mark = true;
                break;
              }
            }
            return mark;
          });
          let inpatientIDs = Array.from(patientList, ({ inpatientIDs }) => inpatientIDs);
          filter.identifyIDCount = inpatientIDs.length;
          filter.inpatientIDs = inpatientIDs;
        }
        this.filterList = this.filterList.filter((count) => {
          return count.identifyIDCount != 0;
        });
      });
      if (this.selectBedNumber) {
        this.selectPatientByBedNumber();
      }
    },
    /**
     * description: 获取科室下拉框列表
     * param {*}
     * return {*}
     */
    async getDepartmentSelectList() {
      let param = {
        ID: this.user.stationID,
      };
      this.departmentList = [];
      await GetDepartmentDataByStationID(param).then((result) => {
        if (this._common.isSuccess(result)) {
          this.departmentList = result.data;
          if (this.departmentList.length <= 1) {
            this.departmentSwitchFlag = false;
          }
        }
      });
    },
    /**
     * description: 获取账户登陆状态
     * return {*}
     */
    getSignLoginStatus() {
      GetSignLoginStatus().then((response) => {
        if (this._common.isSuccess(response) && response.data && response.data.length > 0) {
          this._showTip("warning", response.data);
        } else {
        }
      });
    },
    /**
     * description: 获取相关配置
     * return {*}
     */
    async GetBySettings() {
      let params = {
        SettingTypeCode: "PatientListMask,ShowAdmWardFlag,RowOfBedAmount,PatientCardStyle,ViewDispable",
      };
      await GetBySettingTypeCodeByArray(params).then(async (response) => {
        let settings = response.data ?? [];
        this.patientListMask = false;
        this.admWardFlag = false;
        this.patientCardStyle = "1";
        this.filterMarkShowGroup = false;
        this.departmentSwitchFlag = false;
        this.whetherToShow = false;
        this.showJobTip = false;
        if (this._common.isSuccess(response) && settings.length) {
          // 获取筛选病人呈现方式
          if (
            settings.find((setting) => setting.settingTypeCode === "PatientListMask" && setting.typeValue === "True")
          ) {
            this.patientListMask = true;
          }
          // 获取是否在患者清单显示入科时间列
          if (
            settings.find((setting) => setting.settingTypeCode === "ShowAdmWardFlag" && setting.typeValue === "True")
          ) {
            this.admWardFlag = true;
          }
          // 获取一行显示床位数配置
          const setting = settings.find((setting) => setting.settingTypeCode === "RowOfBedAmount");
          if (setting) {
            let rowOfBedAmount = Number(setting.typeValue);
            if (rowOfBedAmount > 0) {
              this.bedComponentStyle = {
                width: `calc((100% - 13px * ${rowOfBedAmount}) / (${rowOfBedAmount} + 1))`,
              };
            }
          }
          // 获取患者卡片显示样式
          const cardSetting = settings.find((setting) => setting.settingTypeCode === "PatientCardStyle");
          if (cardSetting) {
            this.patientCardStyle = cardSetting.typeValue;
          }
          // 获取科室筛选开关配置
          if (
            settings.find(
              (setting) =>
                setting.settingTypeCode === "ViewDispable" && setting.typeValue === "FilterMarkShowGroup_True"
            )
          ) {
            this.filterMarkShowGroup = true;
          }
          // 获取科室筛选开关配置
          if (
            settings.find(
              (setting) =>
                setting.settingTypeCode === "ViewDispable" && setting.typeValue === "HomeDepartmentFilter_True"
            )
          ) {
            this.departmentSwitchFlag = true;
            await this.getDepartmentSelectList();
          }
          // 获取是否可以打开床头卡弹窗配置
          if (
            settings.find(
              (setting) => setting.settingTypeCode === "ViewDispable" && setting.typeValue === "BedInfoView_True"
            )
          ) {
            this.whetherToShow = true;
          }
          // 获取是否显示工作提醒配置
          if (
            settings.find(
              (setting) => setting.settingTypeCode === "ViewDispable" && setting.typeValue === "JobTip_True"
            )
          ) {
            this.showJobTip = true;
          }
        }
      });
    },
  },
};
</script>
<style lang="scss">
.patient-list {
  .base-header {
    background-color: #ffffff;
    line-height: 32px !important;
  }
  .patient-list-wrap {
    height: 100%;
    .patients {
      height: 100%;
      .base-header {
        background-color: #ffffff;
      }
      .base-content {
        background-color: #e3e3e3;
      }
      .bed-list {
        width: 100%;
        height: 100%;
        display: flex;
        flex-flow: row wrap;
        align-content: flex-start;
        .bed {
          margin: 8px;
        }
      }
      .list-data {
        height: 100%;
      }
      .header-wrap {
        display: flex;
        align-items: center;
        .sub-filter-list {
          flex: auto;
          min-height: 30px;
          height: auto;
          .sub-filter {
            display: flex;
            float: left;
            margin-right: 30px;
            .filter-name {
              font-size: 14px;
            }
            .el-checkbox {
              margin-right: 16px;
              .el-checkbox__label {
                padding-left: 5px;
                color: var(--color);
                font-weight: bold;
              }
              .el-checkbox__inner {
                border-color: var(--color);
              }
              .is-checked .el-checkbox__inner {
                background-color: var(--color);
              }
            }
          }
        }
        .department-select {
          width: 200px;
        }
        .patient-query {
          margin-left: 10px;
          width: 260px;
          .el-input__inner {
            padding: 0 5px !important;
          }
          .el-input-group__append {
            padding: 0 5px;
            color: #8cc63e;
          }
        }
        .card-type {
          line-height: 100%;
          display: inline-block;
          color: #5f5e5e;
          margin-left: 16px;
          font-size: 18px;
          cursor: pointer;
          &.is-select {
            color: $base-color;
          }
          .iconfont {
            font-size: 20px;
            margin: 3px -3px 0 0;
          }
        }
      }
    }
    .el-dialog.patient-label {
      height: 580px;
      width: 620px;
      .el-dialog__body {
        .btn {
          text-align: right;
          margin-bottom: 10px;
        }
        .qrcode {
          height: 100%;
          width: 100%;
          padding: 5px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            width: auto;
            height: auto;
            border: 1px solid #d9d9d9;
          }
        }
      }
    }
  }
}
.right-menu {
  background-color: #fffff8;
  padding: 0;
  .submenu {
    height: 32px;
    line-height: 28px;
    margin-right: 12px;
    &:hover {
      background-color: $base-color;
      .menu-icon,
      .v-contextmenu-submenu__title {
        color: #ffffff;
      }
    }
    .v-contextmenu-submenu__icon {
      right: -8px;
    }
    .menu-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin: 0 2px;
      color: $base-color;
    }
    .divider-line {
      position: absolute;
      display: inline-block;
      width: 0px;
      border-right: 1px dashed #7b7de3;
      height: 38px;
      left: 36px;
      margin-top: -12px;
      z-index: 100;
    }
    .v-contextmenu-submenu {
      left: 0;
      padding-top: 3px !important;
      box-sizing: border-box;
      display: inline-block;
      &.v-contextmenu-item--hover {
        background-color: transparent;
      }
      .v-contextmenu-submenu__title {
        margin-left: 15px;
      }
      .v-contextmenu {
        background-color: #fffff8;
      }
    }
  }
  .v-contextmenu-item {
    height: 32px;
    line-height: 28px;
    padding: 0 10px 0 5px !important;
    .menu-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin: 0 20px 0 -3px;
      color: $base-color;
    }
    .divider-line {
      position: absolute;
      display: inline-block;
      width: 0px;
      border-right: 1px dashed #7b7de3;
      height: 38px;
      left: 36px;
      margin-top: -12px;
    }
    &:first-child .divider-line {
      height: 12px;
      margin-top: 4px;
    }
    &.v-contextmenu-item--hover {
      background-color: $base-color;
      .menu-icon {
        color: #ffffff;
      }
    }
  }
}
</style>
