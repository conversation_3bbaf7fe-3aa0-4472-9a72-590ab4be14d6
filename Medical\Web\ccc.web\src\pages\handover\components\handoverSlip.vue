<template>
  <div class="handover-slip">
    <el-table :data="upperExhibition" :span-method="columnMerging" :show-header="false" border class="table-heder">
      <el-table-column prop="columnFirst" min-width="80" align="center"></el-table-column>
      <el-table-column prop="columnSecond" min-width="150" align="center"></el-table-column>
      <el-table-column prop="columnThird" min-width="80" align="center"></el-table-column>
      <el-table-column prop="columnFourth" min-width="150" align="center"></el-table-column>
      <el-table-column prop="columnFifth" min-width="80" align="center"></el-table-column>
      <el-table-column prop="columnSixth" min-width="150" align="center"></el-table-column>
    </el-table>
    <el-table :data="[{}]" :show-header="false" border>
      <el-table-column v-for="(item, index) in shiftData" :key="index" :min-width="shiftDataLength" align="center">
        <template>
          {{ item }}
        </template>
      </el-table-column>
    </el-table>
    <div class="fotre-car">
      <el-table
        :data="nurseTableDatas[index]"
        v-for="(item, index) in nurseTableDatas"
        :key="index"
        :show-header="false"
        border
        :style="getStyle()"
      >
        <el-table-column prop="columnFirst" min-width="70" align="center"></el-table-column>
        <el-table-column prop="columnSecond" min-width="70" align="center"></el-table-column>
        <el-table-column prop="columnThird" min-width="70" align="center"></el-table-column>
        <el-table-column prop="columnFourth" min-width="70" align="center"></el-table-column>
      </el-table>
    </div>
    <div>
      <div v-for="(item, index) in patientInformation" :key="index" class="fotre-car">
        <el-card v-for="(temporary, number) in item" :key="number" :style="getStyle()">
          <div slot="header" class="clearfix">
            <span>{{ temporary.bedAndName }}</span>
          </div>
          <span v-html="temporary.situation"></span>
          <div class="bottom clearfix">
            <time class="time">{{ temporary.handoverSign }}</time>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    handoverData: {
      require: true
    }
  },
  data() {
    return {
      upperExhibition: [],
      publicWidth: undefined,
      shiftDataLength: undefined,
      shiftData: undefined,
      patientInformation: [],
      nurseTableDatas: []
    };
  },
  watch: {
    handoverData() {
      this.upperExhibition = [];
      this.publicWidth = undefined;
      this.shiftDataLength = undefined;
      this.shiftData = undefined;
      this.patientInformation = [];
      this.nurseTableDatas = [];
      this.dataProcessing();
    }
  },
  methods: {
    dataProcessing() {
      this.upperData(this.handoverData.tableName, this.handoverData.tableTitle);
      this.nurseTableDatas = this.handoverData.nurseTableDatas;
      this.middleData(this.handoverData.shiftData);
      this.patientInformation = this.handoverData.patientInformation;
    },
    getStyle() {
      return "width:" + this.publicWidth;
    },
    //上半部分数据整理
    upperData(tableName, tableTitle) {
      let list = [];
      let template = {
        columnFirst: tableName,
        columnSecond: tableName,
        columnThird: tableName,
        columnFourth: tableName
      };
      if (tableTitle.length > 2) {
        template.columnFifth = tableName;
        template.columnSixth = tableName;
      }
      list.push(template);
      template = {
        columnFirst: tableTitle[0].cellKey,
        columnSecond: tableTitle[0].cellValue,
        columnThird: tableTitle[1].cellKey,
        columnFourth: tableTitle[1].cellValue
      };
      if (tableTitle.length > 2) {
        template.columnFifth = tableTitle[2].cellKey;
        template.columnSixth = tableTitle[2].cellValue;
      }
      list.push(template);
      this.upperExhibition = list;
    },
    //中间部分数据处理
    middleData(shiftData) {
      this.shiftData = shiftData;
      let number = shiftData.length;
      this.shiftDataLength = Math.floor(460 / number);
      this.publicWidth = 100 / number.toFixed(2) + "%";
    },
    //列合并
    columnMerging({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0) {
        if (columnIndex === 0) {
          return [1, 6];
        } else {
          return [0, 0];
        }
      }
    }
  }
};
</script>
<style lang="scss">
.handover-slip {
  height: 100%;
  .table-heder {
    text-align: center;
  }
  .fotre-car {
    display: flex;
  }
  .bottom {
    margin-top: 13px;
    line-height: 12px;
    color: red;
  }
  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }
  .clearfix:after {
    clear: both;
  }
}
</style>