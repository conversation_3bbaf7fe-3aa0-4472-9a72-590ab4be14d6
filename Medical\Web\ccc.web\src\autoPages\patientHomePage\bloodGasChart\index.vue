<!--
 * FilePath     : \src\autoPages\patientHomePage\bloodGasChart\index.vue
 * Author       : 来江禹
 * Date         : 2023-06-06 15:40
 * LastEditors  : 来江禹
 * LastEditTime : 2023-06-15 11:11
 * Description  : 
 * CodeIterationRecord: 
-->
<template>
  <base-layout class="blood-gas-chart" headerHeight="auto">
    <div class="top" slot="header">
      <span>日期时间:</span>
      <el-date-picker
        v-model="startDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="请选择开始日期"
        class="top-gas-date"
      ></el-date-picker>
      <span>-</span>
      <el-date-picker
        v-model="endDate"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        type="date"
        placeholder="请选择结束日期"
        class="top-gas-date"
      ></el-date-picker>
      <el-tooltip :content="showChart ? '显示图表' : '隐藏图表'">
        <el-checkbox-button v-model="showChart">
          <i class="iconfont icon-chart"></i>
        </el-checkbox-button>
      </el-tooltip>
      <el-tooltip :content="showTable ? '显示表格' : '隐藏表格'">
        <el-checkbox-button v-model="showTable">
          <i class="iconfont icon-list-icon"></i>
        </el-checkbox-button>
      </el-tooltip>
    </div>
    <div class="chart-data" v-if="chartData" :loading="loading" element-loading-text="加载中……">
      <ve-line
        :data="chartData"
        :grid="grid"
        :extend="extend"
        :tooltip="tooltip"
        v-if="showChart"
        :after-set-option="afterSetOption"
        :height="showTable ? convertPX(450) + 'px' : '100%'"
      ></ve-line>
      <el-table
        v-if="showTable"
        border
        stripe
        :data="chartData.rows"
        ref="chartTable"
        :height="showChart ? 'calc(100% - ' + convertPX(450) + 'px)' : '100%'"
      >
        <template v-for="(column, index) in chartData.columns">
          <el-table-column
            v-if="index == 0"
            :key="index"
            :label="column"
            align="center"
            :prop="column"
            sortable
            :sort-orders="['ascending', 'descending']"
            :sort-method="sortMethod"
            :width="getWidth(index)"
          >
            <template slot-scope="scope">
              {{ scope.row[column] }}
            </template>
          </el-table-column>
          <el-table-column v-else :key="index" :label="column" align="center">
            <template slot-scope="scope">
              {{ scope.row[column] }}
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import { setBloodChartOption, grid, tooltip, extend } from "./chartOption";
import { GetBloodGasChart } from "@/api/PatientSchedule";
export default {
  components: {
    baseLayout,
  },
  props: {
    patient: {
      type: Object,
      required: true,
      default: () => {
        return undefined;
      },
    },
  },
  data() {
    return {
      startDate: "",
      endDate: "",
      showChart: true,
      showTable: true,
      //统计数据
      chartData: {
        columns: [],
        rows: [],
      },
      grid: grid,
      tooltip: tooltip,
      extend: extend,
      tableWidth: [200, 120, 120, 120, 120, 120, 120, 120, 120, 120, 120, 120],
      loading: false,
    };
  },
  watch: {
    startDate(newVal) {
      if (newVal) {
        this.getChartData();
      }
    },
    endDate(newVal) {
      if (newVal) {
        this.getChartData();
      }
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.endDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.startDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
    },
    /**
     * description: 统计图撇配置参数
     * param {*} chart
     * return {*}
     */
    afterSetOption(chart) {
      setBloodChartOption(chart, this);
    },
    /**
     * description: 排序
     * param {*} a
     * param {*} b
     * return {*}
     */
    sortMethod(a, b) {
      return a["日期时间"] > b["日期时间"];
    },
    /**
     * description: 获取时间列宽度
     * param {*} index
     * return {*}
     */
    getWidth(index) {
      return this.convertPX(this.tableWidth[index]);
    },
    /**
     * description: 获取数据
     * return {*}
     */
    getChartData() {
      if (!this.startDate || !this.startDate || !this.patient) {
        return;
      }
      let params = {
        inpatientID: this.patient.inpatientID,
        startDate: this.startDate,
        endDate: this.endDate,
      };
      this.loading = true;
      this.chartData.rows = [];
      this.chartData.columns = [];
      GetBloodGasChart(params).then((result) => {
        this.loading = false;
        if (this._common.isSuccess(result) && result.data) {
          this.chartData = result.data;
          this.$nextTick(() => {
            this.$refs.chartTable.sort("日期时间", "descending");
            this.$refs.chartTable.doLayout();
          });
        }
      });
    },
  },
};
</script>
<style lang="scss">
.blood-gas-chart {
  .top {
    .top-gas-date {
      width: 140px;
    }
  }
  .chart-data {
    height: 100%;
    background-color: #ffffff;
    padding: 10px 0 10px 0;
    box-sizing: border-box;
  }
}
</style>
