.el-select-dropdown {
  .el-select-dropdown__list {
    padding: 5px 0;

    .el-select-dropdown__item {
      height: 32px;
      line-height: 32px;
      padding: 0 5px;
      margin: 0;

      &.selected,
      &.hover {
        background-color: #7b7de3;
        color: #ffffff;
      }

      &:not(:last-child) {
        border-bottom: 1px dashed #d9d9d9;
      }
    }
  }

  &.is-multiple {
    .el-select-dropdown__item.selected.hover {
      background-color: #7b7de3;
    }
  }
}

.el-select__tags-text {
  color: $base-color;
  font-weight: bold;
}

.el-cascader-menu__list {
  .el-cascader-node {
    height: 60px;
    line-height: 25px;

    &.hover {
      background-color: #ffffff;
      color: $base-color;
    }

    &:not(:last-child) {
      border-bottom: 1px dashed #d9d9d9;
    }
  }

  .el-cascader-node.in-active-path,
  .el-cascader-node.is-selectable.in-checked-path,
  .el-cascader-node.is-active {
    color: #ffffff;
    background-color: #7b7de3;
  }

  .el-cascader-node:hover {
    color: #ffffff;
    background-color: #7b7de3;
  }
}

.el-cascader-menu {
  .el-cascader-node {
    height: 32px;
    line-height: 32px;
  }

  .el-cascader-menu__wrap {
    height: 215px;
  }
}