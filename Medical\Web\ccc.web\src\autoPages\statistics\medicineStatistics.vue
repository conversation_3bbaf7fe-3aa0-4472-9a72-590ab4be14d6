<!--
 * FilePath     : \src\autoPages\statistics\medicineStatistics.vue
 * Author       : 来江禹
 * Date         : 2023-05-11 10:09
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-04-26 11:34
 * Description  : 医嘱给药执行率
 * CodeIterationRecord:
-->

<template>
  <div class="medicine-schedule-statistics" v-loading="staticsLoading" :element-loading-text="loadingText">
    <div class="header">
      <span>
        开始日期：
        <el-date-picker
          class="header-date"
          v-model="startDate"
          type="date"
          placeholder="选择开始日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </span>
      <span>
        结束日期:
        <el-date-picker
          class="header-date"
          v-model="endDate"
          type="date"
          placeholder="选择结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        ></el-date-picker>
      </span>
      <station-selector
        v-model="stationID"
        :userID="user.userID"
        :hospitalFlag="true"
        :width="convertPX(180) + 'px'"
      ></station-selector>
      <el-button class="query-button" icon="iconfont icon-search" @click="getStatisticsData()">查询</el-button>
      <el-button class="print-button" icon="iconfont icon-arrow-download" @click="exportToExcel()">导出EXCEL</el-button>
      <span class="header-switch">
        是否查看出院数据：
        <el-switch v-model="showNotInHospital" @change="getStatisticsData()"></el-switch>
      </span>
    </div>
    <div class="medicine-statistics-body">
      <el-radio-group class="medicine-statistics-body-radio" v-model="type" @input="getStatisticsData">
        <el-radio-button label="station">病区汇总</el-radio-button>
        <el-radio-button label="route">途径</el-radio-button>
        <el-radio-button label="attendance">责护</el-radio-button>
        <el-radio-button v-if="!showNotInHospital" label="bed">床位</el-radio-button>
      </el-radio-group>
      <el-checkbox-group
        class="body-route-checkbox"
        v-if="type == 'route'"
        :min-width="convertPX(200)"
        v-model="chooseRoute"
        @change="getStatisticsData"
      >
        <el-checkbox-button
          v-for="(item, index) in routeSelectList"
          :key="index"
          :label="item"
          :value="item"
        ></el-checkbox-button>
      </el-checkbox-group>

      <packaging-table
        v-model="statisticsTableData"
        :dynamicTableList="dynamicTableList"
        :headerList="tableHeaderList"
        :showDynamicColumnSetting="true"
        @recoverSetting="recoverDynamicTableSetting"
        @saveData="saveDynamicTableData"
        @cellClick="cellClick"
        :getCellStyle="getCellStyle"
      ></packaging-table>
    </div>
    <el-drawer
      title="给药明细"
      :visible.sync="showDetail"
      direction="btt"
      size="70%"
      :wrapperClosable="false"
      :modal="false"
      :append-to-body="true"
    >
      <iframe v-if="detailUrl" :src="detailUrl" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
    </el-drawer>
  </div>
</template>
<script>
import { getCellReportUrl } from "@/utils/setting";
import stationSelector from "@/components/selector/stationSelector";
import packagingTable from "@/components/table/index";
import { GetMedicineScheduleStatistics } from "@/api/Static";
import { mapGetters } from "vuex";
import { export_json_to_excel } from "@/vendor/Export2Excel.js";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import {
  GetDynamicTableList,
  RecoverDynamicTableSetting,
  SaveDynamicTableSettingList,
  GetDynamicTableHeader,
} from "@/api/DynamicTableSetting";
export default {
  components: {
    stationSelector,
    packagingTable,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
      hospitalInfo: "getHospitalInfo",
    }),
  },
  watch: {
    type: {
      handler(newVal) {
        this.getTableHeaderList();
        this.getDynamicTableList();
      },
    },
  },
  data() {
    return {
      startDate: "",
      endDate: "",
      stationID: undefined,
      //统计维度
      type: "station",
      //表格数据
      statisticsTableData: [],
      //途径筛选下拉框列表
      routeSelectList: [],
      //下拉框选择途径
      chooseRoute: [],
      showNotInHospital: false,
      tableHeaderList: [],
      dynamicTableQueryView: {
        tableType: "MedicineStatisticsRateTable",
        tableSubType: "station",
        userID: "",
        hospitalID: "",
      },
      staticsLoading: false,
      loadingText: "",
      dynamicTableList: [],
      // 链接列属性集合
      isLinkColumnProps:["total","executionAmount","totalRateRate","inExecutionAmount","unexecutedAmount","cancellationAmount","needScanAmount","scanAmount","startExecutionAmount","endExecutionAmount","endExecutionAmount"],
      // 抽屉相关数据
      showDetail: false,
      // 抽屉中iframe的URL
      detailUrl: "",
    };
  },
  async created() {
    this.init();
    await this.getShowAllStationSwitch();
    this.getTableHeaderList();
    this.getStatisticsData();
    this.getDynamicTableList();
  },
  methods: {
    init() {
      this.stationID = this.user.stationID;
      this.startDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.endDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
    },
    /**
     * description: 查询统计数据
     * return {*}
     */
    async getStatisticsData() {
      this.staticsLoading = true;
      this.loadingText = "加载中……";
      let params = {
        startDate: this.startDate,
        endDate: this.endDate,
        userID: this.user.userID,
        type: this.type,
        stationID: this.stationID,
        routeArr: this._common.clone(this.chooseRoute),
        showNotInHospital: this.showNotInHospital,
      };
      this.statisticsTableData = [];
      await GetMedicineScheduleStatistics(params).then((res) => {
        this.staticsLoading = false;
        if (this._common.isSuccess(res)) {
          this.statisticsTableData = res.data;
          //筛选维度为途径，并且筛选下拉框无数据，赋值
          if (this.type == "route" && !this.chooseRoute?.length) {
            this.routeSelectList = [];
            this.routeSelectList = res.data[0]?.routeList;
          }
          //切换统计维度，清空途径筛选框下拉数据
          if (this.type != "route") {
            this.chooseRoute = [];
          }
        }
      });
    },
    /**
     * description: 导出表格数据
     * return {*}
     */
    exportToExcel() {
      if (!this.statisticsTableData) {
        return;
      }
      let tableData = this.statisticsTableData;
      let header = [];
      let filterVal = [];
      this.tableHeaderList.forEach((element) => {
        header.push(element.label);
        filterVal.push(element.prop);
      });
      const data = tableData.map((v) => filterVal.map((j) => v[j]));
      export_json_to_excel(header, data, this._datetimeUtil.getNowDate("yyyyMMdd") + "给药医嘱执行情况统计");
    },
    // /**
    //  * description: 合并合计行方法
    //  * param {*} row
    //  * param {*} column
    //  * param {*} rowIndex
    //  * param {*} columnIndex
    //  * return {*}
    //  */
    // spanMethod({ row, column, rowIndex, columnIndex }) {
    //   if (this.type == "station") {
    //     return;
    //   }
    //   if (columnIndex === 0) {
    //     if (row.stationName == "合计") {
    //       return {
    //         rowspan: 1, // 不合并行
    //         colspan: 2, // 合并列
    //       };
    //     }
    //   }
    //   if (columnIndex === 1) {
    //     if (row.stationName == "合计") {
    //       return {
    //         rowspan: 0,
    //         colspan: 0,
    //       };
    //     }
    //   }
    // },
    /**
     * @description: 获取动态表头数据
     * @return
     */
    async getTableHeaderList() {
      this.staticsLoading = true;
      this.loadingText = "加载中……";
      this.dynamicTableQueryView.tableSubType = this.type;
      let params = {
        tableType: this.dynamicTableQueryView.tableType,
        tableSubType: this.type,
        userID: this.user.userID,
        hospitalID: this.hospitalInfo.hospitalID,
      };
      this.tableHeaderList = [];
      await GetDynamicTableHeader(params).then((res) => {
        this.staticsLoading = false;
        if (this._common.isSuccess(res)) {
          this.tableHeaderList = res.data;
        }
      });
    },
    /**
     * @description: 获取查询数据是否默认全院
     * @return
     */
    async getShowAllStationSwitch() {
      let param = {
        SettingTypeCode: "MedicineStatisticsShowAllStation",
      };
      await GetSettingSwitchByTypeCode(param).then((response) => {
        if (this._common.isSuccess(response) && response.data) {
          this.stationID = 999999;
        }
      });
    },
    /**
     * @description: 获取动态表格自定义配置
     * @return
     */
    async getDynamicTableList() {
      let params = {
        tableType: this.dynamicTableQueryView.tableType,
        tableSubType: this.dynamicTableQueryView.tableSubType,
        userID: this.user.userID,
        hospitalID: this.hospitalInfo.hospitalID,
      };
      await GetDynamicTableList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.dynamicTableList = res.data;
        }
      });
    },
    /**
     * @description: 恢复动态表格自定义配置
     * @return
     */
    recoverDynamicTableSetting() {
      let params = {
        tableType: this.dynamicTableQueryView.tableType,
        tableSubType: this.dynamicTableQueryView.tableSubType,
        userID: this.user.userID,
        hospitalID: this.hospitalInfo.hospitalID,
      };
      RecoverDynamicTableSetting(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.getDynamicTableList();
          this.getTableHeaderList();
          this.getStatisticsData();
        }
      });
    },
    /**
     * @description: 保存动态表格自定义数据
     * @return
     */
    saveDynamicTableData() {
      let params = [];
      this.dynamicTableList.forEach((dynamicTable, index) => {
        if (dynamicTable.chooseFlag) {
          dynamicTable.userID = this.user.userID;
        }
        dynamicTable.sort = index + 1;
        dynamicTable.modifyUserID = this.user.userID;
        params.push(dynamicTable);
      });
      SaveDynamicTableSettingList(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功！");
          this.getDynamicTableList();
          this.getTableHeaderList();
          this.getStatisticsData();
        }
      });
    },
    /**
     * @description: 单元格点击事件，打开详细报表
     * @param {Object} row 行数据
     * @param {Object} column 列数据
     */
    cellClick(row, column) {
      // 确保有列属性和行数据
      if (!column.property || !row) {
        return;
      }
      
      // 检查是否是可点击的列
      if (!this.isLinkColumnProps.includes(column.property)) {
        return;
      }
      
      // 获取病区ID
      const stationID = this.stationID == 999999 ? row.stationID : this.stationID;
      if (!stationID) {
        return;
      }
      
      // 构建抽屉中iframe的URL参数
      const params = {
        reportName: '/给药执行率明细表.cr',
        StationID: stationID,
        StartDate: this.startDate,
        EndDate: this.endDate,
        OrderRule: this.type === 'route' ? (row.route || '') : '',
        BedNumber: this.type === 'bed' ? (row.bedNumber || '') : '',
        AttendanceNurse: this.type === 'attendance' ? (row.userName || '') : '',
        InHospital: this.showNotInHospital ? 0 : 1,
        Type: column.property,
        HospitalID: this.hospitalInfo.hospitalID
      };
      
      // 构建URL
      const url = new URL(getCellReportUrl() + 'run:CCC');
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
      
      // 在抽屉中显示详情
      this.detailUrl = url.toString();
      this.showDetail = true;
    },
    /**
     * @description: 添加单元格特殊样式
     * @param cellData 单元格数据
     * @return 单元格样式
     */
    getCellStyle(cellData) {
      const { column} = cellData;
      if (this.isLinkColumnProps.includes(column.property)) {
        return { cursor: 'pointer' };
      }
    },
  },
};
</script>
<style lang="scss">
.medicine-schedule-statistics {
  height: 100%;
  .header {
    height: 30px;
    padding-left: 5px;
  }
  .print-button {
    float: right;
    margin: 10px;
  }
  .header-switch {
    margin: 0px 0px 0px 5px;
  }
  .medicine-statistics-body {
    margin-top: 5px;
    height: calc(100% - 120px);
    .medicine-statistics-body-radio {
      padding: 5px 0px 5px 5px;
      display: inline-block;
    }
    .body-route-checkbox {
      padding: 0px 0px 5px 5px;
      display: inline-block;
      vertical-align: top;
    }
  }
}
</style>