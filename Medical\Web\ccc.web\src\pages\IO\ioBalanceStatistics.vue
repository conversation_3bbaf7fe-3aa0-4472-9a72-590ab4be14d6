<!--
 * FilePath     : \src\pages\IO\ioBalanceStatistics.vue
 * Author       : 苏军志
 * Date         : 2021-04-26 10:17
 * LastEditors  : 来江禹
 * LastEditTime : 2023-10-13 09:13
 * Description  : 出入量平衡
 * CodeIterationRecord:
 * 1、2690-作为护理人员，我需要出入水量查询，每个页签都可以根据自然时间查询
-->
<template>
  <base-layout class="io-balance-statistics">
    <div slot="header">
      <span class="label">{{ ioStatistics.switchLabel }}</span>
      <el-switch v-model="queryByShift" />
      <div class="where" v-show="queryByShift">
        <span class="label">{{ ioStatistics.startDate }}</span>
        <el-date-picker
          v-model="startShiftTime"
          value-format="yyyy-MM-dd"
          type="date"
          :placeholder="placeholderDate"
          class="date-picker"
        ></el-date-picker>
        <shift-selector
          :stationID="stationID"
          @select-item="changeShift($event, 'startShift')"
          v-model="startShift"
          label="班别："
          width="70"
        ></shift-selector>
        <span class="label">{{ ioStatistics.endDate }}</span>
        <el-date-picker
          v-model="endShiftTime"
          value-format="yyyy-MM-dd"
          type="date"
          :placeholder="placeholderDate"
          class="date-picker"
        ></el-date-picker>
        <shift-selector
          :stationID="stationID"
          @select-item="changeShift($event, 'endShift')"
          v-model="endShift"
          label="班别："
          width="70"
        ></shift-selector>
      </div>
      <div class="where" v-show="!queryByShift">
        <span class="label">{{ ioRecord.startDate }}</span>
        <el-date-picker
          v-model="startDate"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          :placeholder="placeholderDate"
          class="datetime-picker"
        ></el-date-picker>
        <span class="label">{{ ioRecord.endDate }}</span>
        <el-date-picker
          v-model="endDate"
          value-format="yyyy-MM-dd HH:mm"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          :placeholder="placeholderDate"
          class="datetime-picker"
        ></el-date-picker>
      </div>
      <el-button class="query-button" icon="iconfont icon-search" @click="getIOBalanceData">
        {{ queryButton }}
      </el-button>
    </div>
    <ve-histogram
      height="100%"
      :data="chartData"
      :settings="chartSettings"
      :colors="colors"
      :after-set-option="afterSetOption"
    ></ve-histogram>
  </base-layout>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import shiftSelector from "@/components/selector/shiftSelector";
import { GetIOBalanceData } from "@/api/IO";
import { GetNowStationShiftData } from "@/api/StationShift";
import { mapGetters } from "vuex";
export default {
  components: {
    baseLayout,
    shiftSelector,
  },
  data() {
    this.colors = [];
    return {
      colors: ["#8BC34A", "#BDBDBD", "#FF4500"],
      startDate: "",
      endDate: "",
      startShiftTime: "",
      endShiftTime: "",
      startShift: "",
      endShift: "",
      stationID: undefined,
      chartData: {
        columns: [],
        rows: [],
      },
      labelMap: {},
      showLineSeries: [],
      stack: {},
      queryByShift: true,
      shiftInfo: undefined,
    };
  },
  computed: {
    ...mapGetters({
      inpatient: "getPatientInfo",
    }),
    placeholderDate() {
      return this.$t("placeholder.date");
    },
    queryButton() {
      return this.$t("button.query");
    },
    loadingText() {
      return this.$t("loadingText.load");
    },
    ioStatistics() {
      return this.$t("ioStatistics");
    },
    ioRecord() {
      return this.$t("ioRecord");
    },
    chartSettings() {
      return {
        stack: this.stack,
        labelMap: this.labelMap,
        showLine: this.showLineSeries,
      };
    },
  },
  watch: {
    inpatient(newVal) {
      if (!newVal) return;
      this.init();
    },
    queryByShift: {
      handler() {
        this.init();
      },
    },
  },
  created() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      this.clearData();
      if (this.inpatient) {
        this.stationID = this.inpatient.stationID;
      }
      if (this.queryByShift) {
        this.getShiftDate();
      } else {
        let nowDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
        this.startDate = nowDate + " 00:00";
        this.endDate = nowDate + " 23:59";
      }
      this.getIOBalanceData();
    },
    async changeShift(shift, typeName) {
      this[typeName] = shift?.id;
      await this.getIOBalanceData();
    },
    getShiftDate() {
      let flag = false;
      if (this.shiftInfo) {
        let date = this.shiftInfo.shiftDate;
        this.startShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
        this.endShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
      } else {
        flag = true;
      }
      if (flag) {
        let params = {
          stationID: this.stationID,
        };
        GetNowStationShiftData(params).then((res) => {
          if (this._common.isSuccess(res) && res.data) {
            this.shiftInfo = res.data;
            let date = this.shiftInfo.shiftDate;
            this.startShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
            this.endShiftTime = this._datetimeUtil.formatDate(date, "yyyy-MM-dd");
          }
        });
      }
    },
    // 图表设置选项钩子
    afterSetOption(chart) {
      chart.setOption({
        series: [
          {
            type: "bar",
            label: {
              show: true,
              position: "top",
            },
          },
          {
            type: "bar",
            label: {
              show: true,
              position: "bottom", //insideBottom
              color: "#757575",
            },
          },
          {
            type: "line",
            smooth: false,
          },
        ],
        xAxis: [
          {
            axisLabel: {
              margin: 20,
            },
          },
        ],
      });
    },
    clearData() {
      this.chartData = {
        columns: [],
        rows: [],
      };
      this.labelMap = {};
      this.showLineSeries = [];
      this.stack = {};
    },
    //获取病人入出量数据
    async getIOBalanceData() {
      this.clearData();
      if (!this.startShift || !this.endShift) {
        return;
      }
      if (!this.inpatient) {
        this._showTip("warning", this.ioStatistics.queryTipPatient);
        return;
      }
      let params = {
        InpatientID: this.inpatient.inpatientID,
        queryByShift: this.queryByShift,
      };
      if (this.queryByShift) {
        params.startDate = this.startShiftTime;
        params.endDate = this.endShiftTime;
        params.startShift = this.startShift;
        params.endShift = this.endShift;
      } else {
        params.startDate = this.startDate;
        params.endDate = this.endDate;
      }
      params.index = Math.random();
      await GetIOBalanceData(params).then((res) => {
        if (this._common.isSuccess(res) && res.data) {
          this.chartData = {
            columns: res.data.columns,
            rows: res.data.rows,
          };
          this.labelMap = res.data.labelMap;
          this.showLineSeries = res.data.showLineSeries;
          this.stack = res.data.stack;
        }
      });
    },
  },
};
</script>
<style lang='scss'>
.io-balance-statistics {
  height: 100%;
  .where {
    display: inline-block;
    .label {
      margin-left: 10px;
    }
    .date-picker {
      width: 110px;
    }
    .datetime-picker {
      width: 150px;
    }
  }
}
</style>