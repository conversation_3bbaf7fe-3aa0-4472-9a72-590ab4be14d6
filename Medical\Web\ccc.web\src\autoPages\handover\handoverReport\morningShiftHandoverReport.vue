<!--
 * FilePath     : \src\autoPages\handover\handoverReport\morningShiftHandoverReport.vue
 * Author       : 苏军志
 * Date         : 2025-04-28 14:43
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-07-11 17:12
 * Description  : 晨交班（新-宏力-咸阳）
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="morning-shift-handover-report" v-loading="loading" :element-loading-text="loadingText">
    <div slot="header">
      <station-selector v-model="stationID" @change="getHandoverData"></station-selector>
      <label>日期：</label>
      <el-date-picker
        class="picker-date"
        v-model="handoverDate"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        type="date"
        placeholder="选择日期"
        :picker-options="checkDate"
        @change="getHandoverData"
      ></el-date-picker>
      <label>床号：</label>
      <el-input
        @keyup.enter.native="getDataByBedNumber"
        v-model="bedNumber"
        class="search-input"
        placeholder="患者床号"
      >
        <i slot="append" class="iconfont icon-search" @click="getDataByBedNumber"></i>
      </el-input>
      <div class="top-btn">
        <el-button class="query-button" icon="iconfont icon-search" @click="summaryMorningHandover()">
          {{ buttonName }}
        </el-button>
        <el-button class="print-button" icon="iconfont icon-print" @click="printMorningHandover()">打印</el-button>
      </div>
    </div>
    <div class="morning-report-wrap">
      <div class="header-table">
        <el-table
          v-for="(viewItem, index) in handoverReportTitleView"
          :key="index"
          :data="viewItem[1]"
          :show-header="false"
          border
          v-show="viewItem[1] && viewItem[1].length > 0"
          :class="'table-' + viewItem[0]"
        >
          <template v-if="viewItem[1] && viewItem[1].length > 0">
            <el-table-column
              v-for="(item, itemIndex) in Object.keys(viewItem[1][0])"
              :key="itemIndex"
              :prop="item"
              align="center"
              :min-width="convertPX(25)"
            >
              <template slot-scope="{ row, $index }">
                <div v-if="viewItem[0] != 'signData'">{{ row[item] }}</div>
                <template v-else>
                  <div
                    v-if="$index > 0"
                    @click="getMorningHandover(item, row[item])"
                    :class="[
                      'sign-count',
                      { 'is-select': checkedRowData == row[item] },
                      { empty: !row[item].numberOrLabel },
                    ]"
                  >
                    {{ row[item].numberOrLabel }}
                  </div>
                  <el-tooltip
                    v-else
                    placement="top"
                    :disabled="!row[item].description"
                    :content="row[item].description"
                  >
                    <div>{{ row[item].numberOrLabel }}</div>
                  </el-tooltip>
                </template>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
      <el-table
        v-if="handoverList && handoverList.length"
        ref="handoverTable"
        class="morning-report-table"
        :data="handoverList"
        :show-header="false"
        border
        :row-class-name="(event) => setClassName(event, 'row')"
        :cell-class-name="(event) => setClassName(event, 'cell')"
      >
        <el-table-column
          v-for="(item, index) in Object.keys(handoverList[0])"
          :key="index"
          :width="item == 'name' ? convertPX(185) : undefined"
          header-align="center"
        >
          <template slot-scope="{ row, $index }">
            <div class="title" v-if="$index === 0" v-html="item == 'name' ? '患者信息' : row[item].situation"></div>
            <template v-else>
              <el-tooltip v-if="item == 'name'" placement="top" content="点击全屏查看患者交班情况">
                <i class="iconfont icon-fullscreen" @click="openPatientHandover($index)"></i>
              </el-tooltip>
              <template v-if="row[item].editFlag">
                <rich-text
                  v-model="row[item].situation"
                  :wordNumber="2200"
                  :size="{ height: row[item].editorHeight, width: '96%' }"
                ></rich-text>
                <div class="operate-icon" v-if="item !== 'name'">
                  <i class="iconfont icon-save" @click="saveHandover($index, item)"></i>
                </div>
              </template>
              <template v-else>
                <div v-if="item !== 'name' && row[item].handoverHeader" v-html="row[item].handoverHeader"></div>
                <div v-if="item !== 'name' && row[item].handoverSign">
                  {{ row[item].handoverSign }}
                </div>
                <div class="situation" v-html="row[item][item == 'name' ? 'bedAndName' : 'situation']"></div>
                <div class="operate-icon" v-if="item !== 'name' && row[item].handoverID">
                  <body-image v-if="row[item].handoverID" :handoverID="row[item].handoverID"></body-image>
                  <i class="iconfont icon-edit" @click="showEdit($index, index, item)"></i>
                </div>
              </template>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      v-if="showPatientHandover"
      :visible.sync="showPatientHandover"
      :close-on-click-modal="false"
      :title="patientHandoverData.patientInfo"
      fullscreen
      custom-class="patient-handover-dialog no-footer"
    >
      <div class="handover-data" v-for="(handoverData, index) in patientHandoverData.handoverDatas" :key="index">
        <div class="title">
          {{ handoverData.title }}
          <body-image v-if="handoverData.handoverID" :handoverID="handoverData.handoverID"></body-image>
        </div>
        <div class="content" :style="{ fontSize: `${handoverContentFontSize}px` }" v-html="handoverData.content"></div>
      </div>
      <div class="bottom-operate">
        <div class="icon-wrap">
          <el-tooltip placement="top" :content="handoverContentFontSize === 12 ? '已是最小字号' : '减小字号'">
            <i
              :class="['iconfont icon-reduce-font-size', { 'no-allow': handoverContentFontSize === 12 }]"
              @click="setHandoverContentFontSize('reduce')"
            ></i>
          </el-tooltip>
          <el-tooltip placement="top" content="增大字号">
            <i class="iconfont icon-increase-font-size" @click="setHandoverContentFontSize('increase')"></i>
          </el-tooltip>
          <el-tooltip placement="top" :content="handoverContentFontSize === 20 ? '已是默认字号' : '设为默认字号'">
            <i
              :class="['iconfont icon-a', { 'no-allow': handoverContentFontSize === 20 }]"
              @click="setHandoverContentFontSize()"
            ></i>
          </el-tooltip>
          <el-tooltip placement="top" :content="currentPatientIndex === 1 ? '已是第一个患者' : '上一个患者'">
            <i
              :class="['iconfont icon-back', { 'no-allow': currentPatientIndex === 1 }]"
              @click="setPatientHandover('Previous')"
            ></i>
          </el-tooltip>
          <el-tooltip
            placement="top"
            :content="currentPatientIndex === handoverList.length - 1 ? '已是最后一个患者' : '下一个患者'"
          >
            <i
              :class="['iconfont icon-back next', { 'no-allow': currentPatientIndex === handoverList.length - 1 }]"
              @click="setPatientHandover('Next')"
            ></i>
          </el-tooltip>
        </div>
      </div>
    </el-dialog>
    <!-- 打印预览对话框 -->
    <el-dialog
      v-dialogDrag
      :close-on-click-modal="false"
      title="打印预览"
      fullscreen
      :visible.sync="showPrint"
      v-loading="pdfLoading"
    >
      <iframe
        id="printIframe"
        :src="ftpPath"
        type="application/x-google-chrome-pdf"
        width="99%"
        height="98%"
        frameborder="1"
        scrolling="auto"
      />
    </el-dialog>
    <!-- 打印筛选对话框 -->
    <el-dialog title="打印交班患者" :visible.sync="printDialogVisible" :close-on-click-modal="false">
      <div class="print-preview">
        <el-table :data="printTableData" border>
          <el-table-column
            v-for="column in columnNames"
            :key="column.key"
            :prop="column.key"
            :label="column.value"
            align="center"
          >
            <template slot-scope="scope">
              <div class="print-cell" @click="handleCellClick(scope.row, column.key)">
                {{ scope.row[column.key] }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="details-section">
          <el-checkbox-group v-model="selectedDetails" @change="handleDetailsChange">
            <el-checkbox-button
              v-for="(value, key) in allDetails"
              :key="key"
              :label="key"
              :class="{ 'is-selected': isDetailSelected(key) }"
            >
              {{ value }}
            </el-checkbox-button>
          </el-checkbox-group>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="printDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePrintConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </base-layout>
</template>
<script>
import {
  GetMorningHandoverReportTitle,
  SummaryMorningHandover,
  GetMorningHandoverReportSBAR,
  UpdateMorningHandoverReport,
  GetMorningHandoverBeds,
} from "@/api/HandoverReport.js";
import { GetSettingValueByTypeCodeAsync } from "@/api/SettingDescription";
import richText from "@/components/RichText";
import baseLayout from "@/components/BaseLayout";
import bodyImage from "@/components/bodyImage";
import stationSelector from "@/components/selector/stationSelector";
import { mapGetters } from "vuex";
import common from "@/utils/common";
import { PrintHandoverSummaryPDF } from "@/api/Document.js";
export default {
  components: {
    baseLayout,
    stationSelector,
    richText,
    bodyImage,
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
  },
  inject: ["toggleScreenfull"],
  watch: {
    showPatientHandover(value) {
      !value && this.toggleScreenfull(undefined, true, "patient-handover-dialog");
    },
    printDialogVisible(value) {
      if (!value) {
        this.selectedCells.clear();
        this.selectedDetails = [];
      }
    },
  },
  async mounted() {
    this.handoverDate = this._datetimeUtil.addDate(this._datetimeUtil.getNowDate(), -1, "yyyy-MM-dd");
    this.stationID = this.user.stationID;
    await this.getHandoverTitleView();
    this.getSettingValueByTypeCode();
    this.getMorningHandover(undefined, undefined, true);
  },
  data() {
    return {
      stationID: 0,
      handoverDate: undefined,
      handoverReportTitleView: {},
      // 日期不得大于当前日期
      checkDate: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        },
      },
      handoverList: [],
      buttonName: undefined,
      checkedProp: "",
      checkedRowData: undefined,
      loadingText: "查询中……",
      loading: false,
      bedNumber: "", //床号作为查询条件
      showPatientHandover: false,
      patientHandoverData: {}, // 患者交班数据
      currentPatientIndex: undefined, // 当前患者记录序号
      handoverContentFontSize: 20, // 患者交班内容字体大小
      printDialogVisible: false,
      printTableData: [],
      columnNames: [],
      allDetails: {},
      selectedDetails: [],
      selectedCells: new Set(),
      originalData: [],
      ftpPath: "", // PDF路径
      showPrint: false, // 显示打印
      pdfLoading: false, // 读取PDF
    };
  },
  methods: {
    /**
     * description: 获取交接班数据
     * return {*}
     */
    async getHandoverData() {
      await this.getHandoverTitleView();
      this.getMorningHandover(undefined, undefined, true);
    },
    setClassName(event, type) {
      const { row, rowIndex, columnIndex } = event;
      if (type === "row") {
        if (rowIndex !== 0) {
          return `bed-number-${row.name.bedNumber}`;
        }
      } else {
        if (rowIndex !== 0 && columnIndex !== 0) {
          return `situation-${rowIndex}-${columnIndex}`;
        }
      }
    },
    getDataByBedNumber() {
      const queryBedNumber = this.$refs.handoverTable.$el.querySelector(".bed-number-" + this.bedNumber);
      if (queryBedNumber) {
        queryBedNumber.scrollIntoView({ block: "start" });
      } else {
        this._showTip("warning", "未找到该床位患者选中分类的交班数据");
      }
    },
    /**
     * description: 获取按钮名称
     * return {*}
     */
    async getSettingValueByTypeCode() {
      let param = {
        SettingTypeCode: "MoringHandoverButtonName",
      };
      await GetSettingValueByTypeCodeAsync(param).then((response) => {
        if (this._common.isSuccess(response)) {
          this.buttonName = response?.data ?? "汇总晨交班";
        }
      });
    },
    /**
     * description: 查询交班数据
     * return {*}
     */
    async getHandoverTitleView() {
      if (!this.stationID) {
        this._showTip("warning", "请选择病区");
        return;
      }
      this.handoverReportTitleView = {};
      let params = {
        stationID: this.stationID,
        startDate: this.handoverDate,
        endDate: this.handoverDate,
        showSignFlag: true,
      };
      this.loadingText = "查询中……";
      this.loading = true;
      await GetMorningHandoverReportTitle(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.handoverReportTitleView = Object.entries(res.data ?? {});
        }
      });
    },
    /**
     * description: 汇总晨交班内容
     * param {*}
     * return {*}
     */
    summaryMorningHandover() {
      let params = this.createHandoverParam();
      if (!params) {
        this._showTip("warning", "没有需要汇总的患者");
        return;
      }
      this.loadingText = "汇总中……";
      this.loading = true;
      SummaryMorningHandover(params).then(async (res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          if (res.data) {
            await this.getHandoverTitleView();
            this.getMorningHandover(undefined, undefined, true);
          }
        }
      });
    },
    /**
     * description: 创建汇总晨交班参数
     * param {*}
     * return {*}
     */
    createHandoverParam() {
      let handoverReportList = this.handoverReportTitleView.find((m) => m[0] === "signData")?.[1];
      if (!handoverReportList) {
        return undefined;
      }
      let inpatientIDArr = [];
      // 遍历所有班次的数据
      handoverReportList.forEach((shiftData) => {
        Object.values(shiftData).forEach((handover) => {
          if (handover.inpatientIDArr) {
            let childArr = handover.inpatientIDArr.length === 0 ? [] : handover.inpatientIDArr.split("||");
            if (childArr) {
              inpatientIDArr.push(...childArr);
            }
          }
        });
      });
      if (inpatientIDArr.length === 0) {
        return undefined;
      }
      let result = {
        inpatientIDs: [...new Set(inpatientIDArr)],
        handoverDate: this.handoverDate,
      };
      return result;
    },
    /**
     * description: 获取交班内容
     * param {*}
     * param {*}
     * return {*}
     */
    async getMorningHandover(prop, item, allFlag = false) {
      if (!allFlag && !item?.numberOrLabel) {
        return;
      }
      this.checkedProp = prop;
      this.checkedRowData = item;
      let params = {
        stationID: this.stationID,
        shiftDate: this.handoverDate,
        inpatientIDs: item?.inpatientIDArr?.split("||") ?? [],
        allFlag: allFlag,
        index: Math.random(),
      };
      this.handoverList = [];
      this.loadingText = "查询中……";
      this.loading = true;
      GetMorningHandoverReportSBAR(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.handoverList = res.data;
          //修复el-table表头错位
          this.$nextTick(() => {
            if (this.$refs.handoverTable) {
              this.$refs.handoverTable.doLayout();
            }
          });
        }
      });
    },
    showEdit(rowIndex, columnIndex, prop) {
      const editorHeight = this.$refs.handoverTable.$el.querySelector(
        `.situation-${rowIndex}-${columnIndex}`
      ).clientHeight;
      this.$set(this.handoverList[rowIndex], prop, {
        ...this.handoverList[rowIndex][prop],
        editorHeight: `${editorHeight}px`,
        editFlag: true,
      });
    },
    saveHandover(rowIndex, prop) {
      let params = {
        handoverID: this.handoverList[rowIndex][prop].handoverID,
        // 去除空的html标签
        situation: common.removeEmptyHtmlTags(this.handoverList[rowIndex][prop].situation ?? ""),
      };
      UpdateMorningHandoverReport(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this._showTip("success", "保存成功");
          // 更新汇总人和汇总时间，退出编辑模式
          this.$set(this.handoverList[rowIndex], prop, {
            ...this.handoverList[rowIndex][prop],
            editFlag: false,
            handoverHeader: res.data,
          });
        }
      });
    },
    openPatientHandover(rowIndex) {
      this.showPatientHandover = true;
      this.currentPatientIndex = rowIndex;
      this.setPatientHandover();
      this.toggleScreenfull(undefined, true, "patient-handover-dialog");
    },
    setPatientHandover(type) {
      if (type === "Previous") {
        if (this.currentPatientIndex === 1) {
          return;
        }
        this.currentPatientIndex--;
      } else if (type === "Next") {
        if (this.currentPatientIndex === this.handoverList.length - 1) {
          return;
        }
        this.currentPatientIndex++;
      }
      this.patientHandoverData = {
        patientInfo: "",
        handoverDatas: [],
      };
      const handoverData = this.handoverList[this.currentPatientIndex];
      Object.keys(handoverData).forEach((key) => {
        if (key === "name") {
          this.patientHandoverData.patientInfo = handoverData[key].bedAndName.replace(/<br\s*\/?>/gi, "，");
        } else {
          this.patientHandoverData.handoverDatas.push({
            title: this.handoverList[0][key].situation,
            content: handoverData[key].situation,
            handoverID: handoverData[key].handoverID,
          });
        }
      });
    },
    setHandoverContentFontSize(type) {
      if (type === "reduce") {
        //最小字体大小为12px
        if (this.handoverContentFontSize == 12) {
          return;
        }
        this.handoverContentFontSize--;
      } else if (type === "increase") {
        this.handoverContentFontSize++;
      } else {
        this.handoverContentFontSize = 20;
      }
    },
    printMorningHandover() {
      let params = {
        stationID: this.stationID,
        startDate: this.handoverDate,
        endDate: this.handoverDate,
        showSignFlag: true,
      };
      this.loading = true;
      GetMorningHandoverBeds(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.columnNames = res.data.columnNames;
          this.allDetails = res.data.allDetails;
          this.originalData = res.data.rowDataCollection;
          this.printTableData = res.data.rowDataCollection.map((row, rowIndex) => {
            const rowData = {};
            row.cellDataCollection.forEach((cell) => {
              rowData[cell.cellTitle] = cell.cellContent;
            });
            rowData.rowIndex = rowIndex;
            return rowData;
          });
          // 显示筛选对话框
          this.printDialogVisible = true;
        }
      });
    },
    handlePrintConfirm() {
      if (this.selectedDetails.length === 0) {
        this._showTip("warning", "请选择需要打印的床位");
        return;
      }
      let params = {
        handoverDate: this.handoverDate,
        stationID: this.stationID,
        inpatientIDArrString: JSON.stringify(this.selectedDetails),
        summaryByShift: true,
        recordsCode: "MorningHandoverReport",
      };

      // 先关闭筛选对话框
      this.printDialogVisible = false;
      // 清空选择
      this.selectedCells.clear();
      this.selectedDetails = [];

      // 设置加载状态
      this.pdfLoading = true;

      // 获取PDF链接
      PrintHandoverSummaryPDF(params).then((res) => {
        this.pdfLoading = false;
        if (this._common.isSuccess(res)) {
          // 获取到链接后再打开预览对话框
          this.ftpPath = res.data;
          this.showPrint = true;
        }
      });
    },
    handleCellClick(row, columnKey) {
      const cellKey = `${row.rowIndex}-${columnKey}`;
      if (this.selectedCells.has(cellKey)) {
        this.selectedCells.delete(cellKey);
      } else {
        this.selectedCells.add(cellKey);
      }
      this.updateSelectedDetails();
    },
    getCellDetails(rowIndex, columnKey) {
      if (!this.originalData || !this.originalData[rowIndex]) return [];
      const row = this.originalData[rowIndex];
      const cell = row.cellDataCollection.find((c) => c.cellTitle === columnKey);
      return cell ? cell.cellDetails : [];
    },
    updateSelectedDetails() {
      const allDetails = new Set();
      this.selectedCells.forEach((cellKey) => {
        const [rowIndex, columnKey] = cellKey.split("-");
        const cellDetails = this.getCellDetails(parseInt(rowIndex), columnKey);
        cellDetails.forEach((detail) => allDetails.add(detail));
      });
      this.selectedDetails = Array.from(allDetails);
    },
    isDetailSelected(key) {
      return this.selectedDetails.includes(key);
    },
    handleDetailsChange(selected) {
      this.selectedDetails = selected;
    },
  },
};
</script>
<style lang="scss">
.morning-shift-handover-report {
  height: 100%;
  overflow: hidden;
  label {
    margin-left: 10px;
  }
  .picker-date {
    width: 180px;
  }
  .switch {
    margin-right: 5px;
    display: inline-block;
  }
  .top-btn {
    float: right;
  }
  .search-input {
    width: 180px;
    .el-input-group__append {
      padding: 0 5px;
    }
    i {
      color: #8cc63e;
    }
  }
  .morning-report-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    .header-table {
      .table-departmentAndHandOverDate {
        border-top: none;
        border-bottom: none;
      }
      .table-signData {
        .cell {
          height: 34px;
          line-height: 30px;
          .sign-count {
            height: 100%;
            cursor: pointer;
            &.empty {
              cursor: not-allowed;
            }
            &.is-select {
              background-image: url("../../../../static/images/text-select.png");
              background-position: center center;
              background-repeat: no-repeat;
              background-size: 36px;
            }
          }
        }
      }
    }
    .morning-report-table {
      height: calc(100% - 240px);
      border-top: none;
      overflow-y: auto;
      td {
        vertical-align: top;
      }
      .title {
        text-align: center;
      }
      .cell {
        padding: 5px 5px 5px 10px !important;
        line-height: 1.2;
        .richText-body {
          margin: 5px 5px 20px 5px;
          background-color: #ffffff;
          p {
            padding: 0;
          }
        }
        .situation {
          margin-bottom: 20px;
          p {
            margin: 0;
            padding: 0;
          }
        }
        .icon-fullscreen {
          color: #ff0000;
          font-size: 20px;
          position: absolute;
          top: 5px;
          right: 5px;
        }
        .operate-icon {
          position: absolute;
          display: flex;
          bottom: 0;
          right: 5px;
          .icon-body {
            font-size: 18px;
            font-weight: bold;
          }
        }
      }
    }
  }
  .patient-handover-dialog.el-dialog {
    .handover-data {
      padding: 10px 0;
      .title {
        display: flex;
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 5px;
        .icon-body {
          font-size: 18px;
          font-weight: bold;
        }
      }
      .content {
        border: 1px solid #cccccc;
        padding: 5px 10px;
        min-height: 60px;
        p {
          margin: 0;
        }
      }
    }
    .bottom-operate {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 50px;
      padding-bottom: 5px;
      text-align: center;
      &:hover {
        .icon-wrap {
          display: inline-block;
        }
      }
      .icon-wrap {
        display: none;
        padding: 5px 20px;
        background-color: #00000070;
        box-shadow: 0px 0px 10px #00000050;
        border-radius: 30px;
        .iconfont {
          margin: 0 8px;
          font-size: 20px;
          color: #ffffff;
          &.next {
            rotate: 180deg;
          }
          &.no-allow {
            color: #cccccc;
            cursor: not-allowed;
          }
        }
      }
    }
  }
  .print-preview {
    .print-cell {
      cursor: pointer;
      &:hover {
        background-color: #f5f7fa;
      }
    }
    .el-table {
      .el-table__body tr:hover > td {
        background-color: transparent !important;
      }
    }
    .details-section {
      margin-top: 20px;
      .el-checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }
      .el-checkbox-button {
        margin-right: 10px;
        margin-bottom: 10px;
        &.is-selected {
          background-color: #409eff;
          border-color: #409eff;
          color: #fff;
        }
      }
    }
  }
  .el-dialog {
    .el-dialog__body {
      padding: 0;
    }
    iframe {
      border: none;
    }
  }
}
</style>
