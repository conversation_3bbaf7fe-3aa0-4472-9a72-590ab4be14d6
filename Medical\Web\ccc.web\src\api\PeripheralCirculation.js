/*
 * FilePath     : \src\api\PeripheralCirculation.js
 * Author       : 苏军志
 * Date         : 2020-05-28 16:40
 * LastEditors  : 苏军志
 * LastEditTime : 2020-09-07 16:11
 * Description  : 末梢血运
 */
import http from "../utils/ajax";
import qs from "qs";
const baseUrl = "/PeripheralCirculation";

export const urls = {
  GetPCRecordList: baseUrl + "/GetPCRecordList",
  SavePatientPC: baseUrl + "/SavePatientPC",
  GetPCAssessView: baseUrl + "/GetPCAssessView",
  DeletePCByID: baseUrl + "/DeletePCByID",
  GetPCCareMainsByID: baseUrl + "/GetPCCareMainsByID",
  DeletePCCare: baseUrl + "/DeletePCCare",
  SavePatientPCCare: baseUrl + "/SavePatientPCCare"
};

// 获取末梢血运记录列表
export const GetPCRecordList = params => {
  return http.get(urls.GetPCRecordList, params);
};
// 保存末梢血运记录
export const SavePatientPC = params => {
  return http.post(urls.SavePatientPC, params);
};
// 获取末梢血运评估模板
export const GetPCAssessView = params => {
  return http.get(urls.GetPCAssessView, params);
};
// 删除末梢血运记录
export const DeletePCByID = params => {
  return http.post(urls.DeletePCByID, qs.stringify(params));
};
// 获取末梢血运评估记录
export const GetPCCareMainsByID = params => {
  return http.get(urls.GetPCCareMainsByID, params);
};
// 删除末梢血运评估记录
export const DeletePCCare = params => {
  return http.post(urls.DeletePCCare, qs.stringify(params));
};
// 保存末梢血运评估记录
export const SavePatientPCCare = params => {
  return http.post(urls.SavePatientPCCare, params);
};
