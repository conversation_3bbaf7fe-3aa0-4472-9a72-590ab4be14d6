/** componentData: { type: Object, default: () => {} }, situation: { type:
Boolean, default: true }, background: { type: Boolean, default: true }, risk: {
type: Boolean, default: true }, humanPic: { type: Boolean, default: true },
recommendation: { type: Boolean, default: true }, riskUpdate: { type: Boolean,
default: true } */
<template>
  <div class="form-text">
    <div v-if="sflag" class="situation">
      <div class="font">S-现状</div>
      <RichText
        :disabled="disabled"
        :size="{ height: 'calc(100% - 27px)', width: '99%' }"
        :wordNumber="8000"
        v-model="situation"
        @getNumberBoole="getSituationNumberFlag"
      ></RichText>
    </div>
    <div v-if="bflag" class="background">
      <div class="font">B-背景</div>
      <RichText
        :disabled="disabled"
        :size="{ height: 'calc(100% - 27px)', width: '99%' }"
        :wordNumber="2000"
        v-model="background"
        @getNumberBoole="getBackgroundNumberFlag"
      ></RichText>
    </div>
    <div v-if="risk" class="risk-assessment">
      <div class="assessfont">A-评估</div>
      <RichText
        :disabled="disabled"
        v-if="editRisk"
        v-model="riskStr"
        :size="{ height: 'calc(100% - 27px)', width: '99%' }"
        :wordNumber="2000"
        @getNumberBoole="getBackgroundNumberFlag"
      ></RichText>
      <el-table v-if="!editRisk" border @row-click="getRiskDetail" :data="riskAssessResult" class="assess-table">
        <el-table-column prop="recordName" label="风险评估"></el-table-column>
        <el-table-column prop="point" label="分数" width="60"></el-table-column>
        <el-table-column prop="scoreRangeContent" label="风险等级" width="80"></el-table-column>
      </el-table>
    </div>
    <div v-if="rflag" class="advice">
      <div class="font">R-建议</div>
      <RichText
        :disabled="disabled"
        :size="{ height: 'calc(100% - 27px)', width: '99%' }"
        :wordNumber="2000"
        v-model="recommendation"
        @getNumberBoole="getRecommendationNumberFlag"
      ></RichText>
    </div>
    <div v-if="picflag" class="human-pic">
      <img v-if="humanPic" :src="'data:image/jpeg;base64,' + humanPic" />
    </div>
  </div>
</template>
<script>
import common from "@/utils/common";
import RichText from "../RichText";
export default {
  props: {
    componentData: { type: Object, default: () => {} },
    bodyPartData: { type: Array, default: () => [] },
    sflag: { type: Boolean, default: true },
    bflag: { type: Boolean, default: true },
    risk: { type: Boolean, default: true },
    editRisk: { type: Boolean, default: true },
    picflag: { type: Boolean, default: true },
    rflag: { type: Boolean, default: true },
    riskUpdate: { type: Boolean, default: true },
    disabled: { type: Boolean, default: false },
  },
  components: {
    RichText,
  },
  watch: {
    componentData: {
      handler(newV) {
        if (!newV) return;
        if (!newV.handoverInfo) {
          //避免赋值错误，如果交班信息为null，将SBAR部分赋值为""
          newV.handoverInfo = {
            situation: "",
            background: "",
            assement: "",
            recommendation: "",
            bodyPartImage: "",
          };
        }

        this.reinit(newV);
      },
      //声明监听对象时，执行一次handler方法，watch在被渲染时，传入参数已经存在，所以没有被监听到
      immediate: true,
    },
  },
  data() {
    return {
      situation: "请输入内容",
      recommendation: "请输入内容",
      background: "请输入内容",
      riskStr: "请输入内容",
      tableData: [],
      handover: {},
      riskAssessResult: [],
      humanPic: "",
      riskdialog: false,
      //是否符合字数限制
      wordNumberFlagArr: [true, true, true],
      wordNumberFlag: true,
    };
  },
  methods: {
    getValue() {
      let nowObj = common.clone(this.componentData.handoverInfo);
      this.$set(nowObj, "situation", this.situation);
      this.$set(nowObj, "background", this.background);
      this.$set(nowObj, "assement", this.riskStr);
      this.$set(nowObj, "recommendation", this.recommendation);
      this.$set(
        nowObj,
        "wordNumberFlag",
        this.wordNumberFlagArr.filter((item) => item == false).length == 0 ? true : false
      );
      // TODO:存入风险字符串
      // this.$set(nowObj, 'risk', this.recommendation);
      return nowObj;
    },
    getRiskDetail(row, column, event) {
      this.$emit("need-risk", row);
    },
    reinit(newV) {
      this.riskAssessResult = [];
      this.situation = "";
      this.background = "";
      this.humanPic = "";
      this.recommendation = "";
      this.handoverInfo = newV.handoverInfo;
      this.riskStr = newV.handoverInfo.assement ? newV.handoverInfo.assement : "";
      this.riskAssessResult = newV.riskAssessResult;
      this.situation = newV.handoverInfo.situation ? newV.handoverInfo.situation : "";
      this.background = newV.handoverInfo.background ? newV.handoverInfo.background : "";
      this.humanPic = newV.handoverInfo.bodyPartImage;
      this.recommendation = newV.handoverInfo.recommendation ? newV.handoverInfo.recommendation : "";
    },
    getlength(str) {
      ///<summary>获得字符串实际长度，中文2，英文1</summary>
      ///<param name="str">要获得长度的字符串</param>
      if (!str) return 0;
      var realLength = 0,
        len = str.length,
        charCode = -1;
      for (var i = 0; i < len; i++) {
        charCode = str.charCodeAt(i);
        if (charCode >= 0 && charCode <= 128) realLength += 1;
        else realLength += 2;
      }
      return realLength;
    },
    getSituationNumberFlag(flag) {
      this.wordNumberFlagArr[0] = flag;
    },
    getBackgroundNumberFlag(flag) {
      this.wordNumberFlagArr[1] = flag;
    },
    getRecommendationNumberFlag(flag) {
      this.wordNumberFlagArr[2] = flag;
    },
    getRiskStrNumberFlag(flag) {
      this.wordNumberFlagArr[3] = flag;
    },
  },
};
</script>

<style lang="scss">
.form-text {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow-y: auto;
  padding: 0px;
  font-size: 0;
  border: 1px solid #c4c4c4;
  box-sizing: border-box;
  .richText-body {
    margin: 0;
    .richText-input {
      p {
        padding: 0;
      }
    }
  }

  .advice,
  .situation,
  .background,
  .risk-assessment {
    font-size: 16px;
    vertical-align: top;
    display: inline-block;
    height: 100%;
    width: 20%;
    box-sizing: border-box;
    // border-left: 1px solid #c4c4c4;
    border-right: 1px solid #c4c4c4;
    overflow: auto;
    // max-height: 342px;
    // padding: 0px 10px;
    .font {
      padding-left: 5px;
      font-size: 16px;
      height: 25px;
    }
    #advice,
    #background,
    #situation,
    #risk {
      font-size: 16px;
      overflow: auto;
      height: calc(100% - 37px);
    }
    .tip {
      position: relative;
      width: 100%;
      text-align: right;
      color: #ccc;
      font-size: 10px;
    }
    .break {
      color: red;
    }
  }
  .human-pic {
    width: 20%;
    height: 100%;
    // border-left: 1px solid #c4c4c4;
    // box-sizing: border-box;
    display: inline-block;
    margin: auto;
    overflow: hidden;
    img {
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 100%;
      position: relative;
      top: 35%;
    }
    .person-pic {
      height: 100%;
      width: 100%;
      background-color: aqua;
    }
  }
  // .risk-assessment {
  //   display: inline-block;
  //   width: 20%;
  //   height: 100%;
  //   box-sizing: border-box;
  //   // border-left: 1px solid #c4c4c4;
  //   border-right: 1px solid #c4c4c4;
  //   font-size: 16px;
  //   // padding: 0 10px;
  //   overflow: auto;
  //   // .assessfont {
  //   //   padding-left: 5px;
  //   //   height: 20px;
  //   //   line-height: 20px;
  //   // }
  //   #risk {
  //     font-size: 16px;
  //     overflow: auto;
  //     height: calc(100% - 37px);
  //     box-sizing: border-box;
  //   }
  //   .assess-table {
  //     overflow: auto;
  //   }
  //   .tip {
  //     position: relative;
  //     width: 100%;
  //     text-align: right;
  //     color: #ccc;
  //     font-size: 10px;
  //   }
  //   .break {
  //     color: red;
  //   }
  // }
}
</style>
