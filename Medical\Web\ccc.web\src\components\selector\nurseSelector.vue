<template>
  <div class="nurse-selector">
    <span :class="{ label: label }">{{ label }}</span>
    <el-select
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      v-model="selected"
      placeholder="请选择护士"
      @change="changeValue"
      :style="style"
      :allow-create="allowCreate"
    >
      <el-option v-for="(nurse, index) in nurseList" :key="index" :label="nurse.name" :value="nurse.userID"></el-option>
    </el-select>
  </div>
</template>

<script>
import { GetNurse } from "@/api/User";
export default {
  props: {
    label: {
      type: String,
      default: "护士：",
    },
    value: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    stationID: {
      type: Number,
      // required: true,
    },
    width: {
      type: String,
      default: "120px",
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    allowCreate: {
      type: <PERSON>olean,
      default: false,
    },
  },
  watch: {
    stationID: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal) {
          if (!this.disabled && this.selected) {
            this.$emit("input", undefined);
          }
          this.init();
        }
      },
    },
    value: {
      immediate: true,
      handler(newVal, oldVal) {
        this.selected = newVal;
      },
    },
  },
  data() {
    return {
      selected: "",
      nurseList: [],
    };
  },
  computed: {
    style() {
      return {
        width: this._convertUtil.getHeigt(this.width, true),
      };
    },
  },
  methods: {
    init() {
      let params = {
        stationID: this.stationID,
        index: Math.random(),
      };
      GetNurse(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.nurseList = result.data;
        }
      });
    },
    changeValue(nurseID) {
      // 实现双向绑定
      this.$emit("input", nurseID);
      this.$emit("select", nurseID);
      let nurse = this.nurseList.find((nurse) => {
        return nurse.userID == nurseID;
      });
      if (nurse) {
        this.$emit("select-item", nurse);
      }
    },
  },
};
</script>
<style lang="scss">
.nurse-selector {
  display: inline-block;
  .label {
    margin-left: 5px;
  }
  .el-select {
    .el-input.is-disabled {
      .el-input__inner {
        color: #606266;
      }
    }
    .el-input__inner {
      padding-left: 5px;
    }
  }
}
</style>
