<template>
  <base-layout show-header show-footer header-height="auto" footer-height="60">
    <div slot="header" class="test-header">
      头部slot:
      <station-selector v-model="stationID" @select="selectStation" @select-item="selectStationItem"></station-selector>
      {{ stationID }}
      <dept-selector
        v-model="deptID"
        :stationID="stationID"
        @select="selectDept"
        @select-item="selectDeptItem"
      ></dept-selector>
      {{ deptID }}
      <shift-selector
        v-model="shiftID"
        :stationID="stationID"
        @select="selectShift"
        @select-item="selectShiftItem"
      ></shift-selector>
      {{ shiftID }}
      <nurse-selector
        v-model="nurseID"
        :stationID="stationID"
        @select="selectNurse"
        @select-item="selectNurseItem"
      ></nurse-selector>
      {{ nurseID }}
      <user-selector
        v-model="userID"
        clearable
        filterable
        remoteSearch
        :stationID="stationID"
        @select="selectNurse"
        @select-item="selectNurseItem"
      ></user-selector>
      {{ userID }}
      <el-button type="primary" @click="deleteConfirm">删除确认</el-button>
      <el-button type="primary" @click="testClick">测试noRepeatClick指令</el-button>
    </div>
    <div>
      默认内容slot
      <br />
      格式化时间：
      <span
        v-formatTime="{
          value: now,
          type: 'dateTime',
          format: 'yyyy-MM-dd hh:mm:ss',
        }"
      ></span>
      <br />
      <el-date-picker v-model="now" type="datetime" placeholder="选择日期时间"></el-date-picker>
      <el-date-picker v-model="now" type="date" placeholder="选择日期"></el-date-picker>
      <el-time-picker v-model="now" placeholder="任意时间点"></el-time-picker>
      <div>test1：{{ test1 }}</div>
      <div>test2：{{ test2 }}</div>
      <div>test3：{{ test3 }}</div>
      <limit-text-box v-model="text" :maxLength="40" :rows="6" width="500"></limit-text-box>
      <u-table
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="dark"
        selectTrClass="selectTr"
        style="width: 100%"
        :height="360"
        use-virtual
        showBodyOverflow="title"
        showHeaderOverflow="title"
        :row-height="30"
      >
        <u-table-column type="selection" width="55"></u-table-column>
        <u-table-column label="日期" width="120">
          <template slot-scope="scope">{{ scope.row.date }}</template>
        </u-table-column>
        <u-table-column prop="name" label="姓名" width="120"></u-table-column>
        <u-table-column prop="sex" label="性别" width="120"></u-table-column>
        <u-table-column prop="time" label="时间" width="120"></u-table-column>
        <u-table-column prop="address" label="地址" width="220"></u-table-column>
        <u-table-column
          v-for="item in columns"
          :key="item.id"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
        ></u-table-column>
      </u-table>

      <ux-grid
        border
        stripe
        highlight-current-row
        show-overflow
        ref="plxTable"
        keep-source
        height="360"
        bigDataCheckbox
        use-virtual
        :edit-config="{ trigger: 'click', mode: 'cell' }"
      >
        <ux-table-column type="checkbox" width="60"></ux-table-column>
        <ux-table-column type="index" width="60"></ux-table-column>
        <ux-table-column field="name" title="名字" min-width="100" edit-render>
          <template v-slot:edit="scope">
            <el-input v-model="scope.row.name"></el-input>
          </template>
        </ux-table-column>
        <ux-table-column field="sex" title="性别" width="80" edit-render>
          <template v-slot:edit="scope">
            <el-select v-model="scope.row.sex" @change="$refs.plxTable.updateStatus(scope)">
              <el-option v-for="item in sexList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </template>
          <template v-slot="{ row }">{{ getLabel(row.sex) }}</template>
        </ux-table-column>
        <ux-table-column field="time" title="时间" width="80" edit-render>
          <template v-slot:edit="scope">
            <el-time-select
              style="width: 100%"
              v-model="scope.row.time"
              :picker-options="{
                start: '08:30',
                step: '00:15',
                end: '18:30',
              }"
              placeholder="选择时间"
            ></el-time-select>
          </template>
        </ux-table-column>
        <ux-table-column field="address" title="地址" width="200" edit-render>
          <!--这个呢是编辑状态下的方式-->
          <template v-slot:edit="scope">
            <el-cascader v-model="scope.row.address" :options="options"></el-cascader>
          </template>
          <!--这个代表是自定义行中的显示（因为我需要对我，el-cascader选出来的信息进行转换，选出来是个数组）-->
          <template v-slot="{ row }">{{ getCascader(row.address, options) }}</template>
        </ux-table-column>
        <ux-table-column field="single" title="单身?" width="70">
          <template v-slot="scope">
            <el-switch v-model="scope.row.single"></el-switch>
          </template>
        </ux-table-column>
        <ux-table-column title="操作" width="100">
          <template v-slot="{ row }">
            <el-button @click="saveEvent(row)">保存</el-button>
          </template>
        </ux-table-column>

        <!--试试多个,先搞个v-if隐藏掉，反正可以支持大量的列，列多了我眼睛花，注释掉了哈！！！-->
        <ux-table-column
          v-for="item in columns"
          :key="item.id"
          :resizable="item.resizable"
          :field="item.prop"
          edit-render
          :title="item.label"
          :sortable="item.sortable"
          :width="item.width"
        >
          <template v-slot:edit="scope">
            <el-input v-model="scope.row.name"></el-input>
          </template>
        </ux-table-column>
      </ux-grid>
    </div>
    <div slot="footer">底部slot</div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import stationSelector from "@/components/selector/stationSelector";
import deptSelector from "@/components/selector/deptSelector";
import shiftSelector from "@/components/selector/shiftSelector";
import nurseSelector from "@/components/selector/nurseSelector";
import userSelector from "@/components/selector/userSelector";
import LimitTextBox from "@/components/LimitTextBox";
export default {
  components: {
    baseLayout,
    stationSelector,
    deptSelector,
    shiftSelector,
    nurseSelector,
    userSelector,
    LimitTextBox,
  },
  data() {
    return {
      stationID: 1,
      deptID: 0,
      shiftID: 0,
      nurseID: "0",
      userID: "",
      now: new Date(),
      test1: this._datetimeUtil.formatDate(new Date(), "yyyy-MM-dd hh:mm:ss"),
      test2: this._datetimeUtil.formatDate(new Date(), "yyyy/MM/dd hh:mm:ss"),
      test3: this._datetimeUtil.formatDate(new Date(), "yyyy年MM月dd日 hh时mm分ss秒"),
      text: "",
      tableData: [],
      columns: [],
      sexList: [
        { value: 1, label: "男" },
        { value: 2, label: "女" },
        { value: 3, label: "未知" },
      ],
      options: [
        {
          value: "zhinan",
          label: "指南",
          children: [
            {
              value: "shejiyuanze",
              label: "设计原则",
              children: [
                {
                  value: "yizhi",
                  label: "一致",
                },
                {
                  value: "fankui",
                  label: "反馈",
                },
                {
                  value: "xiaolv",
                  label: "效率",
                },
                {
                  value: "kekong",
                  label: "可控",
                },
              ],
            },
            {
              value: "daohang",
              label: "导航",
              children: [
                {
                  value: "cexiangdaohang",
                  label: "侧向导航",
                },
                {
                  value: "dingbudaohang",
                  label: "顶部导航",
                },
              ],
            },
          ],
        },
      ],
    };
  },
  created() {
    this._receiveBroadcast("test", (value) => {
      console.log(value);
      this.s = value;
    });
  },
  mounted() {
    // 延迟加载
    setTimeout(() => {
      this.columns = Array.from({ length: 20 }, (_, idx) => ({
        prop: "name",
        id: idx,
        label: "列名" + (idx + 1),
        width: 100,
        sortable: idx === 1,
        resizable: true,
      }));
      this.tableData = Array.from({ length: 10000 }, (_, idx) => ({
        id: idx + 1,
        date: "2016-05-03",
        name: "张三" + idx,
        sex: 1,
        time: "9:10",
        address: ["zhinan", "shejiyuanze", "kekong"],
        single: idx < 2,
      }));
      // 这样设置大量数据的表格
      this.$refs.plxTable.reloadData(this.tableData);
      // 获取表格实例，里面可以得到列，表格数据等等
      console.log(this.$refs.plxTable.tableExample());
    });
  },
  methods: {
    testClick() {
      return;
      // return GetSession().then(ret => {
      //   if (this._common.isSuccess(ret)) {
      //     console.log(ret.data);
      //   }
      // });
    },
    selectStation(stationID) {
      // this.stationID = stationID;
      console.log(stationID);
      this.deptID = undefined;
    },
    selectStationItem(station) {
      console.log(station);
    },
    selectDept(deptID) {
      // this.deptID = deptID;
      console.log(deptID);
    },
    selectDeptItem(dept) {
      console.log(dept);
    },
    selectShift(shiftID) {
      // this.shiftID = shiftID;
      console.log(shiftID);
    },
    selectShiftItem(shift) {
      console.log(shift);
    },
    selectNurse(nurseID) {
      // this.shiftID = shiftID;
      console.log(nurseID);
    },
    selectNurseItem(nurse) {
      console.log(nurse);
    },
    deleteConfirm() {
      this._deleteConfirm("确定删除数据么？", (flag) => {
        if (flag) {
          this._showTip("success", "确认删除");
        } else {
          this._showTip("warning", "取消删除");
        }
      });
    },
    // 点击保存
    saveEvent(row) {
      // 判断是否发生改变
      console.log(this.$refs.plxTable.isUpdateByRow(row));
      if (this.$refs.plxTable.isUpdateByRow(row)) {
        // 局部保存，并将行数据恢复到初始状态（如果 第二个参数record 为空则不改动行数据，只恢复状态）
        // 必须执行这个，不然下次点击保存，还是保存成功哦！状态没改变哦
        this.$refs.plxTable.reloadRow(row, null, null);
        // ...保存逻辑相关（后端呀，或者前端呀）
        this.$message({
          message: "恭喜你，保存成功",
          type: "success",
        });
      } else {
        this.$message({
          message: "保存失败，你没改变当前行的数据",
          type: "error",
        });
      }
    },
    // 转换男女
    getLabel(val) {
      const item = this.sexList.filter((item) => item["value"] === val)[0];
      return item ? item.label : "";
    },
    // 转换地址
    getCascader(value, list) {
      const values = value || [];
      const labels = [];
      const cascaderData = function (index, list) {
        const val = values[index];
        if (list && values.length > index) {
          list.forEach((item) => {
            if (item.value === val) {
              labels.push(item.label);
              cascaderData(++index, item.children);
            }
          });
        }
      };
      cascaderData(0, list);
      return labels.join(" / ");
    },
  },
};
</script>

<style lang="scss">
.test-header {
  text-align: left;
}
</style>
