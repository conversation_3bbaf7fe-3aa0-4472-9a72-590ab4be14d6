<!--
 * FilePath     : /src/autoPages/recordSupplement/components/specificCareSwitch.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-17 15:26
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-05 17:05
 * Description  : 专项护理补录
 * CodeIterationRecord: 
 -->
<template>
  <div class="specific-care-supplement">
    <el-tabs class="tabs" v-if="activeComponent" v-model="activeComponent">
      <el-tab-pane
        v-for="(component, index) in components"
        :key="index"
        :label="component.label"
        :name="component.name"
      />
    </el-tabs>
    <div class="tabs-content">
      <component :is="activeComponent" :supplemnentPatient="patient" :supplementPatient="patient"></component>
    </div>
  </div>
</template>
<script>
import { GetMenuByParentID } from "@/api/Menu";
import bloodTransfusionRecord from "@/pages/transfusion/index";
import wound from "@/autoPages/wound/index";
import patientDelivery from "@/autoPages/patientDelivery/index.vue";
import patientDeliveryRecord from "@/autoPages/patientDeliveryRecord/index.vue";
import patientDelirium from "@/pages/patientDelirium/index";
import patientThrombolysis from "@/pages/patientThrombolysis/index";
import babyFeedingRecord from "@/pages/patientBabyFeeding/index.vue";
import neurovascularAssess from "@/pages/PatientNeurovascular/index.vue";
import glucose from "@/pages/glucose/components/glucoseEdit";
import tube from "@/pages/tube/index.vue";
import io from "@/pages/IO/ioRecordMaintenance";
import cRRTRecord from "@/autoPages/cRRTRecord/index";
import rescueRecord from "@/autoPages/patientRescue/index";
import restraint from "@/autoPages/restraint/index";
import peripheralCirculation from "@/autoPages/peripheralCirculation/index";
import patientStomaRecord from "@/autoPages/patientStoma/index";
import patientPain from "@/autoPages/patientPain/index";
import flapCare from "@/autoPages/flap/index";
import patientSedation from "@/autoPages/sedation/index";
import patientCINV from "@/autoPages/patientCINV/index";
import arteriovenousFistula from "@/pages/arteriovenousFistula/index";
import patientDietIntake from "@/pages/patientDietIntake/index";
import pumping from "@/pages/pumping/index";
export default {
  components: {
    bloodTransfusionRecord,
    wound,
    patientDelirium,
    patientThrombolysis,
    babyFeedingRecord,
    neurovascularAssess,
    glucose,
    tube,
    io,
    cRRTRecord,
    rescueRecord,
    restraint,
    patientDelivery,
    peripheralCirculation,
    patientStomaRecord,
    patientDeliveryRecord,
    patientPain,
    flapCare,
    patientSedation,
    patientCINV,
    arteriovenousFistula,
    patientDietIntake,
    pumping,
  },
  props: {
    patient: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      activeComponent: undefined,
      components: [
        {
          label: "出入量",
          name: "io",
        },
        {
          label: "输血",
          name: "bloodTransfusionRecord",
        },
        {
          label: "伤口",
          name: "wound",
        },
        {
          label: "谵妄",
          name: "patientDelirium",
        },
        {
          label: "溶栓用药",
          name: "patientThrombolysis",
        },
        {
          label: "婴儿喂养",
          name: "babyFeedingRecord",
        },
        {
          label: "神经血管评估",
          name: "neurovascularAssess",
        },
        {
          label: "血糖",
          name: "glucose",
        },
        {
          label: "导管",
          name: "tube",
        },
        {
          label: "CRRT",
          name: "cRRTRecord",
        },
        {
          label: "抢救",
          name: "rescueRecord",
        },
        {
          label: "约束",
          name: "restraint",
        },
        {
          label: "产时记录",
          name: "patientDelivery",
        },
        {
          label: "末梢血运",
          name: "peripheralCirculation",
        },
        {
          label: "造口",
          name: "patientStomaRecord",
        },
        {
          label: "生产流程记录",
          name: "patientDeliveryRecord",
        },
        {
          label: "疼痛",
          name: "patientPain",
        },
        {
          label: "皮瓣护理",
          name: "flapCare",
        },
        {
          label: "镇静评估",
          name: "patientSedation",
        },
        {
          label: "化疗恶心呕吐",
          name: "patientCINV",
        },
        {
          label: "动静脉内瘘",
          name: "arteriovenousFistula",
        },
        {
          label: "饮食营养",
          name: "patientDietIntake",
        },
        {
          label: "泵入通路",
          name: "pumping",
        },
      ],
    };
  },
  async created() {
    await this.filterComponents();
  },
  methods: {
    /**
     * @description: 根据后端菜单配置过滤列表
     * @return 
     */
    async filterComponents() {
      let params = {
        menuListID: 18,
      };
      const res = await GetMenuByParentID(params);
      if (!this._common.isSuccess(res)) {
        this.components = [];
        return;
      }
      this.components = this.components.map((component) => {
        const menu = res.data.find((m) => m.router.substring(1) == component.name);
        if (!menu) {
          return undefined;
        }
        return {
          label: menu.menuName,
          name: component.name,
        };
      }).filter(Boolean);
      if (this.components.length) {
        this.activeComponent = this.components[0].name;
      }
    },
  },
};
</script>

<style lang="scss" >
.specific-care-supplement {
  height: 100%;
  .tabs {
    height: 40px;
  }
  .tabs-content {
    height: calc(100% - 40px);
  }
}
</style>