<!--
 * FilePath     : \ccc.web\src\pages\pumping\component\headerList.vue
 * Author       : 郭鹏超
 * Date         : 2021-09-22 10:53
 * LastEditors  : 郭鹏超
 * LastEditTime : 2022-01-24 10:30
 * Description  : 表头组件
-->
<template>
  <div>
    <template v-if="item.type == 'date'">
      <el-date-picker
        v-model="scope.row[item.prop]"
        type="date"
        :clearable="false"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 100%"
      ></el-date-picker>
    </template>
    <!-- 时间 -->
    <template v-if="item.type == 'time'">
      <el-time-picker
        v-model="scope.row[item.prop]"
        value-format="HH:mm"
        format="HH:mm"
        style="width: 100%"
      ></el-time-picker>
    </template>
    <!--  -->
    <template v-if="item.type == 'text'">
      <div v-if="item.prop == 'sourceType'">
        <span v-if="scope.row.recordsCode == 'PumpingStart'">泵入开始</span>
        <span v-if="scope.row.recordsCode == 'PumpingMaintain'">例行评估</span>
        <span v-if="scope.row.recordsCode == 'PumpingEnd'">泵入结束</span>
      </div>
      <div v-else-if="item.prop == 'sort'">{{ "通路" + scope.row[item.prop] }}</div>
      <div v-else>{{ scope.row[item.prop] }}</div>
    </template>
    <template v-if="item.type == 'select'">
      <el-select
        :disabled="scope.row.recordsCode == 'PumpingEnd'"
        style="width: 100%"
        v-model="scope.row[item.prop]"
        placeholder="请选择"
      >
        <el-option
          v-for="(item, index) in item.options"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </template>
    <template
      v-if="
        (item.type == 'input' && !item.assessListID) ||
        scope.row.details.find((m) => m.assessListID == item.assessListID)
      "
    >
      <el-input
        :disabled="item.prop == 'speed' && scope.row.recordsCode == 'PumpingEnd'"
        style="width: 100%"
        v-model="scope.row[item.prop]"
      ></el-input>
    </template>
    <template v-if="item.type == 'check'">
      <el-checkbox-group v-if="item.options" v-model="scope.row[item.prop]">
        <el-checkbox v-for="(item, index) in item.options" :key="index" :label="item.value">
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
      <el-checkbox class="one-check" v-else v-model="scope.row[item.prop]"></el-checkbox>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: {},
    },
    scope: {
      type: Object,
      default: {},
    },
  },
  methods: {
    valueCheck(item, row) {
      //泵速小于2不允许填写
      if (item.prop == "speed" && row[item.prop] && row[item.prop] < 2) {
        this.$set(row, item.prop, undefined);
        this._showTip("warning", "泵速不能小于 2 ml/h");
      }
    },
  },
};
</script>

<style>
</style>
