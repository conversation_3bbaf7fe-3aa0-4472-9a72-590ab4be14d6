/*
 * FilePath     : \ccc.web\src\api\StationUseTube.js
 * Author       : 李艳奇
 * Date         : 2020-10-26 10:09
 * LastEditors  : 李艳奇
 * LastEditTime : 2020-10-30 16:55
 * Description  : 
 */
import http from '../utils/ajax'
import qs from 'qs';
const baseUrl = '/StationUseTube'

export const urls = {
  GetTubeList: baseUrl + "/GetTubeList", //根据指定病区所有的导管
  GetUnUseTubeList: baseUrl + "/GetUnUseTubeList", //获取指定病区没有的导管
  DeleteStationUseTube: baseUrl + "/DeleteStationUseTube", //删除指定的病区
  SaveStationUseTube: baseUrl + "/SaveStationUseTube", //添加一个病区对导管
  GetStationList: "/StationList/GetStationList", //获取所有的病区
  UpdateStationUseTube: baseUrl + "/UpdateStationUseTube"
}
// 根据指定病区所有的导管
export const GetTubeList = (params) => {
  return http.get(urls.GetTubeList, params)
}

// 获取所有的病区
export const GetStationList = () => {
  return http.get(urls.GetStationList)
}

// 获取指定病区没有的导管
export const GetUnUseTubeList = (params) => {
  return http.get(urls.GetUnUseTubeList, params)
}
// 删除指定的病区
export const DeleteStationUseTube = (params) => {
  return http.post(urls.DeleteStationUseTube, qs.stringify(params))
}

// 添加一个病区对导管
export const SaveStationUseTube = (params) => {
  return http.post(urls.SaveStationUseTube, params)
}

// 排序
export const UpdateStationUseTube = (params) => {
  return http.post(urls.UpdateStationUseTube, params)
}