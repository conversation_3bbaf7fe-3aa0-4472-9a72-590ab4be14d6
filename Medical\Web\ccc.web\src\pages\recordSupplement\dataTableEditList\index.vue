<!--
 * FilePath     : \ccc.web\src\pages\recordSupplement\dataTableEditList\index.vue
 * Author       : 胡长攀
 * Date         : 2022-11-16 09:58
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-05-31 08:53
 * Description  : 
 * CodeIterationRecord: 
-->
<template>
  <base-layout headerHeight="auto" class="data-table-edit-list">
    <search-patient-data :checkFlag="false" slot="header" @selectPatientData="selectPatientData" />
    <base-layout v-if="showFlag" v-loading="loading" :element-loading-text="elementLoadingText">
      <div class="data-select" slot="header">
        <div class="left-wrap">
          <el-input
            v-model="searchData"
            placeholder="病历单或病历单类型"
            class="search-input"
            clearable
            @keyup.enter.native="searchTableData"
          >
            <i slot="append" class="iconfont icon-search" @click="searchTableData"></i>
          </el-input>
        </div>
        <div class="data-scope">
          <el-radio-group v-model="dataScope" @change="changeDataScope">
            <el-radio-button label="nowTable">正式表</el-radio-button>
            <el-radio-button label="backupTable">备份表</el-radio-button>
          </el-radio-group>
        </div>
        <div class="right-wrap">
          <el-button type="primary" @click="insertRow()" icon="iconfont icon-add">新增</el-button>
        </div>
      </div>
      <div class="data-table" slot-scope="innerLayout" :style="{ height: innerLayout.height + 'px' }">
        <ux-grid ref="dataTable" :height="innerLayout.height" show-overflow show-header :border="true" stripe>
          <ux-table-column title="病历单类型" align="center" field="fileClass" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.fileClass }}</span>
            </template>
          </ux-table-column>
          <ux-table-column title="病历单" header-align="center" field="fileClassName" min-width="150" :resizable="true">
            <template slot-scope="scope">
              <el-select v-if="!scope.row.id" v-model="scope.row.fileClass" filterable placeholder="请选择">
                <el-option
                  v-for="item in emrList"
                  :key="item.fileClassID"
                  :label="item.fileClassName"
                  :value="item.fileClassID"
                ></el-option>
              </el-select>
              <span v-else>{{ scope.row.fileClassName }}</span>
            </template>
          </ux-table-column>
          <ux-table-column title="风险序号" align="center" field="recordListID" width="100">
            <template slot-scope="scope">
              <el-input v-if="!scope.row.id" v-model="scope.row.recordListID"></el-input>
              <span v-else>{{ scope.row.recordListID }}</span>
            </template>
          </ux-table-column>
          <ux-table-column title="异动来源" align="center" field="serialNumber" min-width="150" :resizable="true">
            <template slot-scope="scope">
              <el-input v-if="!scope.row.id" v-model="scope.row.serialNumber"></el-input>
              <span v-else>{{ scope.row.serialNumber }}</span>
            </template>
          </ux-table-column>
          <ux-table-column title="记录时间" align="center" field="editDateTime" width="150">
            <template slot-scope="scope">
              <span
                v-formatTime="{ value: scope.row.editDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"
              ></span>
            </template>
          </ux-table-column>
          <ux-table-column title="抽档标志" align="center" width="80">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.dataPumpFlag" @change="changeDataPumpData(scope.row)"></el-checkbox>
            </template>
          </ux-table-column>
          <ux-table-column title="抽档时间" align="center" field="dataPumpData" width="200">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.dataPumpData"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                clearable
                class="date-picker"
                placeholder="选择日期时间"
                @change="limitTime(scope.row)"
              ></el-date-picker>
            </template>
          </ux-table-column>
          <ux-table-column title="操作" align="center" width="100" fixed="right">
            <template slot-scope="scope">
              <el-tooltip content="复制">
                <i class="iconfont icon-copy" @click="copyRowTableData(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="修改" v-if="!scope.row.isBackupFlag && scope.row.id">
                <i class="iconfont icon-edit" @click="saveRowTableData(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="保存" v-if="!scope.row.id">
                <i class="iconfont icon-save" @click="saveRowTableData(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="删除" v-if="!scope.row.isBackupFlag || !scope.row.id">
                <i class="iconfont icon-del" @click="deleteRowTableData(scope.row)"></i>
              </el-tooltip>
            </template>
          </ux-table-column>
        </ux-grid>
      </div>
    </base-layout>
    <div v-else></div>
  </base-layout>
</template>

<script>
import baseLayout from "@/components/BaseLayout";
import searchPatientData from "@/pages/recordSupplement/components/searchPatientData";
import {
  GetDataTableEditListInfosByInpatientID,
  AddDataTableEditListInfo,
  DeleteDataTableEditListInfo,
  UpdateDataTableEditListInfo,
} from "@/api/DataTableEditList";
import { GetEMRList } from "@/api/VerifyRecord";

export default {
  components: {
    baseLayout,
    searchPatientData,
  },
  data() {
    return {
      //表格数据
      tableData: [],
      //克隆表格数据
      cloneTableData: [],
      //记录单列表
      emrList: [],
      loading: false,
      elementLoadingText: "",
      //表格数据显示标记，默认不显示
      showFlag: false,
      //搜索框数据
      searchData: "",
      //当前患者id
      inpatientID: "",
      //当前患者所在病区
      stationID: "",
      //记录查找范围
      dataScope: "nowTable",
      //判断是不是备份表
      isBackupFlag: false,
    };
  },
  async mounted() {
    await this.getEMRList();
  },
  methods: {
    /**
     * description: 获取患者信息
     * param {*} inpatientID
     * return {*} 患者信息
     */
    async selectPatientData({ inpatientID, stationID }) {
      this.showFlag = true;
      this.inpatientID = inpatientID;
      this.stationID = stationID;
      await this.getDataByInpatientID(inpatientID);
    },
    /**
     * description: 添加一行
     * param {*} row
     * return {*}
     */
    insertRow(row) {
      let newRow = row || {
        editDateTime: this._datetimeUtil.formatDate(this._datetimeUtil.getNow(), "yyyy-MM-dd hh:mm"),
      };
      let table = this.$refs.dataTable;
      if (!table) {
        this._showTip("warning", "找不到引用");
        return;
      }
      //新增行, 第二个参数表示加入行的位置，为空插入第一行，-1插入最后一行
      table.insertRow(newRow);
    },
    /**
     * description: 查询患者异动记录
     * param {*} inpatientID
     * return {*} 患者异动记录
     */
    async getDataByInpatientID(inpatientID) {
      this.elementLoadingText = "加载中……";
      this.loading = true;
      let isBackupFlag = this.isBackupFlag;
      await GetDataTableEditListInfosByInpatientID({ inpatientID: inpatientID, isBackupFlag: isBackupFlag }).then(
        (res) => {
          this.loading = false;
          if (this._common.isSuccess(res)) {
            this.tableData = res.data;
            this.cloneTableData = res.data;
            this.$refs.dataTable.reloadData(this.tableData);
          }
        }
      );
      //修复el-table表头错位
      this.$nextTick(() => {
        this.$refs.dataTable.doLayout();
      });
    },
    /**
     * description: 获取对应医院的记录单
     * return {*} 记录单列表清单
     */
    async getEMRList() {
      await GetEMRList().then((res) => {
        if (this._common.isSuccess(res)) {
          this.emrList = res.data;
        }
      });
    },
    /**
     * description: 保存异动记录
     * param {*} row 行对象
     * return {*}
     */
    async saveRowTableData(row) {
      //fileClass不能为空
      if (!row.fileClass) {
        this._showTip("warning", "请选择病历单");
        return;
      }
      row.inpatientID = this.inpatientID;
      let fileClass = this.emrList.find((m) => m.fileClassID == row.fileClass);
      //判断是否有id,是否为复制
      if (!row.id && !row.isCopy) {
        row.stationID = fileClass.mergeFlag ? this.stationID : "0";
        row.tableName = "";
      }
      this.isBackupFlag = false;
      row.dataPumpFlag = row.dataPumpFlag ? "*" : "";
      //判断是否有id，有走修改逻辑，没有走添加逻辑
      let saveRequest = row.id ? UpdateDataTableEditListInfo : AddDataTableEditListInfo;
      await saveRequest(row).then(async (res) => {
        if (this._common.isSuccess(res)) {
          await this.getDataByInpatientID(this.inpatientID);
          this._showTip("success", `${row.id ? "修改" : "保存"}成功！`);
          this.dataScope = "nowTable";
        }
      });
    },
    /**
     * description: 删除异动记录
     * param {*} row 行对象
     * return {*}
     */
    async deleteRowTableData(row) {
      this._deleteConfirm("确定删除记录？", async (flag) => {
        if (flag) {
          //判断主键id是否存在
          if (!row.id) {
            //不存在，直接移除当前行
            this.$refs.dataTable.remove(row);
            this._showTip("success", "删除成功！");
          } else {
            //存在，走删除逻辑
            let id = row.id;
            await DeleteDataTableEditListInfo({ id: id }).then((res) => {
              if (this._common.isSuccess(res)) {
                this.$refs.dataTable.remove(row);
                this._showTip("success", "删除成功！");
              }
            });
          }
        }
      });
    },
    /**
     * description: 查询
     * param {*} dataScope  查询范围
     * return {*}
     */
    searchTableData() {
      let searchData = this.searchData;
      //判断输入框是否有值
      if (searchData) {
        this.tableData = this.cloneTableData.filter((item) => {
          //过滤符合条件的记录
          return item.fileClassName.includes(searchData) || item.fileClass == searchData;
        });
      } else {
        //没有值，查找全部数据
        this.tableData = this.cloneTableData;
      }
      this.$refs.dataTable.reloadData(this.tableData);
    },
    /**
     * description: 复制并创建行
     * param {*} row 复制的行对象
     * return {*}
     */
    copyRowTableData(row) {
      //克隆当前行数据
      let copyRow = this._common.clone(row);
      copyRow.isBackupFlag = false;
      copyRow.id = undefined;
      copyRow.editDateTime = this._datetimeUtil.formatDate(this._datetimeUtil.getNow(), "yyyy-MM-dd hh:mm");
      copyRow.dataPumpFlag = false;
      copyRow.dataPumpData = "";
      copyRow.isCopy = true;
      this.insertRow(copyRow);
    },
    /**
     * description: 查找不同表数据
     * return {*}
     */
    changeDataScope() {
      this.searchData = "";
      if (this.dataScope == "nowTable") {
        this.isBackupFlag = false;
        this.getDataByInpatientID(this.inpatientID);
      }
      if (this.dataScope == "backupTable") {
        this.isBackupFlag = true;
        this.getDataByInpatientID(this.inpatientID);
      }
    },
    /**
     * description: 绑定抽档时间
     * param {*} row  当前行对象
     * return {*}
     */
    changeDataPumpData(row) {
      if (row.dataPumpFlag) {
        row.dataPumpData = this._datetimeUtil.getNow();
        return;
      }
      row.dataPumpData = "";
    },
    /**
     * description: 绑定抽档标记并限制抽档时间
     * param {*} row  当前行对象
     * return {*}
     */
    limitTime(row) {
      if (row.dataPumpData) {
        if (Date.parse(row.dataPumpData) < Date.parse(row.editDateTime)) {
          row.dataPumpData = "";
          row.dataPumpFlag = false;
          this._showTip("warning", "抽档时间不能小于记录时间");
          return;
        }
        row.dataPumpFlag = true;
        return;
      }
      row.dataPumpFlag = false;
    },
  },
};
</script>

<style lang="scss">
.data-table-edit-list {
  .data-select {
    .left-wrap {
      float: left;
      .search-input {
        width: 180px;
        .el-input-group__append {
          padding: 0 5px;
          i {
            color: #8cc63e;
          }
        }
      }
      .el-input__inner {
        padding-right: 0;
      }
    }
    .data-scope {
      float: left;
      margin-left: 10px;
    }
    .right-wrap {
      float: right;
    }
  }
  .data-table {
    .date-picker {
      width: 170px;
      .el-input__suffix {
        display: block;
      }
    }
  }
}
</style>