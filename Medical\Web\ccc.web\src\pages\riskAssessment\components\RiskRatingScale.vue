<!--
 * FilePath     : \src\pages\riskAssessment\components\RiskRatingScale.vue
 * Author       : 苏军志
 * Date         : 2021-04-06 09:42
 * LastEditors  : 曹恩
 * LastEditTime : 2025-06-22 09:41
 * Description  : 风险详情页面
 * CodeIterationRecord: 2022-09-08 增加showPointFlag，判断是否展示分数 -杨欣欣
-->
<template>
  <div class="risk-rating-scale">
    <div class="component">
      <span class="from" v-if="comSupplementFlag.formSwitch">
        表单:
        <el-select
          v-model.number="nowrecordListID"
          clearable
          placeholder="请选择"
          @change="getSourceRiskRecord(), postScale()"
          style="width: 180px"
          :disabled="editFlag"
        >
          <el-option
            v-for="item in recordList"
            :key="item.recordListID"
            :label="item.recordName"
            :value="item.recordListID"
          ></el-option>
          >
        </el-select>
      </span>
      <station-Department-Bed-Date
        :stDeptBed="stationDepartmentBedDate"
        :switch="switchComponent"
        :componentsReadonly="{ date: dateTimeReadonly }"
        @custCkick="getDeptBedData"
      ></station-Department-Bed-Date>
    </div>
    <div class="total-point">
      <span v-if="showPointFlag">
        <el-tooltip :content="riskRangeStr">
          <font class="total-range" v-if="showRange">{{ riskRangeStr }}&nbsp;&nbsp;</font>
        </el-tooltip>
        <font class="total-number-font">总分&nbsp;:</font>
        <font class="total-number">&nbsp;{{ scoreDetail || totalNumber }}</font>
      </span>
    </div>
    <div :class="['risk-rating-table', { hideBar: !showBar }]">
      <el-table
        top="10px"
        height="100%"
        :cell-style="highlight"
        :data="conponentData.tableData"
        class="risk-rating-tablestyle"
        border
        stripe
      >
        <el-table-column label="评估标准" width="150">
          <template slot-scope="scope">
            <i class="iconfont icon-info" v-if="scope.row.showMessage" @click="showMessage(scope.row.showMessage)"></i>
            <span>{{ scope.row.content }}</span>
          </template>
        </el-table-column>
        <el-table-column label="选框" class-name="check">
          <template slot="header">
            <div class="risk-line">
              <div class="risk-line-point">分数</div>
              <div class="risk-line-content">选项</div>
            </div>
          </template>
          <template slot-scope="scope">
            <div class="risk-line" v-for="item in scope.row.childrenArray" :key="item.recordsFormatID">
              <!--2022-02-17 GCS评分不可选选项分数显示NT  zxz-->
              <div class="risk-line-point" v-if="showPointFlag">
                <div class="point">
                  {{ item.description && item.description != "" ? item.description : item.point }}
                </div>
              </div>
              <div class="risk-line-content">
                <i class="iconfont icon-info" @click="showDetailMessage(item)"></i>
                <div
                  v-if="item.controlerType == 'R'"
                  :class="[
                    'risk-line-input iconfont',
                    scope.row.radioCheck == item.recordsFormatID ? 'icon-radio-select' : 'icon-radio-normal',
                  ]"
                  @click="iconClick(scope.row, item, item.controlerType)"
                ></div>
                <div
                  v-if="item.controlerType == 'C'"
                  :class="[
                    'risk-line-input iconfont',
                    checkBoxfind(scope.row.checkedList, item.recordsFormatID) && scope.row.checkedList.length > 0
                      ? 'icon-checked'
                      : 'icon-no-checked',
                  ]"
                  @click="iconClick(scope.row, item, item.controlerType)"
                ></div>
                <div class="risk-line-label">
                  <label
                    v-html="item.content"
                    @click="iconClick(scope.row, item, item.controlerType)"
                    class="label"
                    :for="item.recordsFormatID"
                  ></label>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 调整当风险距riskRange存在的时候才初始化bar，否则不显示bar 例如：尿布性皮炎没有风险级距，但是显示一个arrow部分-->
    <div v-if="showBar && conponentData.riskRange && conponentData.riskRange.length > 0">
      <risk-bar
        :riskRange="conponentData.riskRange ? conponentData.riskRange : []"
        v-model="totalNumber"
        riskBalWidth="800"
        :showPointFlag="showPointFlag"
        :reverseOrderFlag="conponentData.reverseOrderFlag"
      />
    </div>
  </div>
</template>
<script>
import RiskBar from "./RiskBar";
import stationDepartmentBedDate from "@/pages/recordSupplement/components/stationDepartmentBedDate";
import { mapGetters } from "vuex";
import { GetSourceRiskRecord, GetPatientRiskDetailSource } from "@/api/PatientRiskRecord";
export default {
  props: {
    conponentData: { type: Object, require: true },
    showBar: { type: Boolean, default: true },
    listFormat: { type: Boolean, default: false },
    showRange: { type: Boolean, default: true },
    // [0]:是否显示，[1]:具体数值
    dateTimeArr: {
      type: Array,
      default: () => {
        return [false, undefined];
      },
    },
    // 设置时间是否只读
    dateTimeReadonly: {
      type: Boolean,
      default: false,
    },
    showPointFlag: {
      type: Boolean,
      default: true,
    },
    recordListID: {
      type: Number,
    },
    // recordType: {
    //   type: Number,
    // },
    recordList: {
      type: Array,
    },
    supplementFalg: {
      type: Boolean,
      default: false,
    },
    editFlag: {
      type: Boolean,
      default: false,
    },
    // 头部信息组件传递参数
    comEptBedDataList: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    RiskBar,
    stationDepartmentBedDate,
  },
  computed: {
    ...mapGetters({
      patient: "getPatientInfo",
    }),
  },
  watch: {
    dateTimeArr: {
      handler(newVal) {
        if (newVal) {
          this.assessDateTime = newVal[1];
          if (this.assessDateTime) {
            this.$set(
              this.stationDepartmentBedDate,
              "assessDate",
              this._datetimeUtil.formatDate(this.assessDateTime, "yyyy-MM-dd")
            );
            this.$set(
              this.stationDepartmentBedDate,
              "assessTime",
              this._datetimeUtil.formatDate(this.assessDateTime, "hh:mm")
            );
          }
          this.switchComponent.dateTimeSwitch = !!this.assessDateTime || this.comSupplementFlag.dateTimeSwitch;
          this.$emit("checked-collect", this.recordsFormatList, this.assessDateTime);
        }
      },
      immediate: true,
    },
    conponentData: {
      handler(newValue) {
        if (!newValue) return;
        this.riskRangeStr = "";
        if (newValue.riskRangeStr) {
          this.riskRangeStr = newValue.riskRangeStr;
        }
        this.calculateTotalNumber();
      },
      immediate: true,
    },
    recordListID: {
      immediate: true,
      handler(newVal, oldVal) {
        this.nowrecordListID = newVal;
      },
    },
    supplementFalg: {
      immediate: true,
      handler(newVal, oldVal) {
        this.comSupplementFlag = {
          formSwitch: newVal && !!this.recordListID,
          stationSwitch: newVal,
          departmentListSwitch: newVal,
          bedNumberSwitch: newVal,
          dateTimeSwitch: newVal,
        };
        if (!this.comSupplementFlag.formSwitch) {
          this.warningFlag = true;
        }
      },
    },
    //监听值的变化，调用方法向父组件传值
    stationDepartmentBedDate: {
      immediate: true,
      handler(newVal, oldVal) {
        if (newVal) {
          this.postScale();
          this.assessDateTime =
            this._datetimeUtil.formatDate(newVal.assessDate, "yyyy-MM-dd") +
            " " +
            this._datetimeUtil.formatDate(newVal.assessTime, "hh:mm");
          this.$emit("checked-collect", this.recordsFormatList, this.assessDateTime);
        }
      },
    },
    // 头部科室病区床位日期组件传参
    comEptBedDataList: {
      immediate: true,
      handler(newVal, oldVal) {
        if (this.assessDateTime) {
          newVal.assessDate = this._datetimeUtil.formatDate(this.assessDateTime, "yyyy-MM-dd");
          newVal.assessTime = this._datetimeUtil.formatDate(this.assessDateTime, "hh:mm");
        }
        if (newVal) {
          this.stationDepartmentBedDate = newVal;
          if (this.assessDateTime) {
            this.stationDepartmentBedDate.assessDate = this._datetimeUtil.formatDate(this.assessDateTime, "yyyy-MM-dd");
            this.stationDepartmentBedDate.assessTime = this._datetimeUtil.formatDate(this.assessDateTime, "hh:mm");
          }
          if (!this.stationDepartmentBedDate.assessTime) {
            this.initComponentData();
          }
        }
      },
    },
  },
  data() {
    return {
      // barArray: [],
      riskRangeStr: "",
      totalNumber: 0,
      // bar: {},
      recordsFormatList: [],
      assessDateTime: undefined,
      scoreDetail: undefined,
      //用来记录用户点击de评估项是不是NT类型的
      selectedNTFlag: false,
      //暂存病区科室及床位
      stationDepartmentBedDate: {
        stationID: "",
        departmentListID: "",
        bedNumber: "",
        bedId: "",
        assessDate: "",
        assessTime: "",
      },
      switchComponent: {
        stationSwitch: false,
        departmentListSwitch: false,
        bedNumberSwitch: false,
        dateTimeSwitch: false,
      },
      //当前表格名
      nowrecordListID: undefined,
      //补录标记
      comSupplementFlag: {
        formSwitch: false,
        stationSwitch: false,
        departmentListSwitch: false,
        bedNumberSwitch: false,
        dateTimeSwitch: false,
      },
      warningFlag: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.switchComponent.stationSwitch = this.comSupplementFlag.stationSwitch;
      this.switchComponent.departmentListSwitch = this.comSupplementFlag.departmentListSwitch;
      this.switchComponent.bedNumberSwitch = this.comSupplementFlag.bedNumberSwitch;
    },
    /**
     * description: 回调存储传参值
     * param {*} value
     * return {*}
     */
    getDeptBedData(value) {
      this.stationDepartmentBedDate = value;
    },
    /**
     * description: 触发事件，向父组件返回值
     * return {*}
     */
    postScale() {
      this.$emit("custCkick", this.nowrecordListID);
      this.$emit("deptBedClick", this.stationDepartmentBedDate);
    },
    /**
     * description: 初始化子组件传参
     * return {*}
     */
    initComponentData() {
      this.stationDepartmentBedDate.stationID = this.patient.stationID;
      this.stationDepartmentBedDate.departmentListID = this.patient.departmentListID;
      this.stationDepartmentBedDate.bedNumber = this.patient.bedNumber;
      this.stationDepartmentBedDate.assessDate = this._datetimeUtil.getNowDate("yyyy-MM-dd");
      this.stationDepartmentBedDate.assessTime = this._datetimeUtil.getNowTime("hh:mm");
    },
    /**
     * description: 切换复选框勾选
     * param {*} array
     * param {*} item
     * return {*}
     */
    checkBoxfind(array, item) {
      return Boolean(array.indexOf(item) + 1);
    },
    /**
     * description: 图标点击回调
     * param {*} row
     * param {*} item
     * param {*} type
     * return {*}
     */
    iconClick(row, item, type) {
      if (type == "R") {
        let val = item.recordsFormatID == row.radioCheck ? "" : item.recordsFormatID;
        this.$set(row, "radioCheck", val);
      } else {
        let flag = false;
        if (row.checkedList) {
          row.checkedList.forEach((element, index) => {
            if (element == item.recordsFormatID) {
              flag = true;
              row.checkedList.splice(index, 1);
            }
          });
          if (!flag) {
            row.checkedList.push(item.recordsFormatID);
          }
        }
      }
      this.calculateTotalNumber();
    },
    /**
     * description: 总分计算
     * return {*}
     */
    calculateTotalNumber() {
      let data = this.conponentData.tableData;
      if (!data) return;
      this.recordsFormatList = [];
      this.scoreDetail = undefined;
      //判断是否有选中NT类型的评估项 是：calcTotalNumberFlag =false，不计算总分 否 : true, 需要走计算总分的逻辑
      let calcTotalNumberFlag = true;
      for (let index = 0; index < data.length; index++) {
        const childArray = data[index].childrenArray;
        const checkedList = data[index].checkedList.length > 0 ? data[index].checkedList : undefined;

        let selectedElement = childArray.find((currVal) => {
          if (!currVal.description || currVal.description == "") {
            return false;
          }
          if (currVal.recordsFormatID == data[index].radioCheck) {
            return true;
          }
          if (checkedList) {
            return !!checkedList.find((val) => val == currVal);
          }
          return false;
        });

        if (selectedElement) {
          calcTotalNumberFlag = false;
          break;
        }
      }
      //处理haveCheck标记,标记某个组group被选中了
      for (let index = 0; index < data.length; index++) {
        let item = data[index];
        if (!item.radioCheck && item.checkedList.length == 0) {
          item.haveCheck = false;
        } else {
          item.haveCheck = true;
        }
      }
      //计算总分
      if (calcTotalNumberFlag) {
        let number = 0;
        for (let i = 0; i < data.length; i++) {
          number += this.calculateBlock(data[i]);
        }
        this.totalNumber = number;
      } else {
        //组装NT类型的内容显示
        this.scoreDetail = this.assembleScoreDetail(data);
      }
      this.$emit("checked-collect", this.recordsFormatList, this.assessDateTime);
    },
    /**
     * description: 拼接NT评估项被选中时，需要代替总分数显示的文本内容
     * params {*}
     * return {*} scoreDetail 组装完成的文本
     * param {*} tableData 呈现的表格样式数据，
     */
    assembleScoreDetail(tableData) {
      let scoreDetail = undefined;

      for (let index = 0; index < tableData.length; index++) {
        let number = 0;
        let item = tableData[index];
        //用于记录每一栏中 是否有NT类型的评估项被选中(不能放在循环外)
        let showNTFlag = false;
        let groupContent = undefined;
        const childArray = item.childrenArray;
        let splitStrs = item.content && item.content.split("-");
        let abbr = splitStrs.length > 1 ? splitStrs[0] : item.description || "";
        // 计算单选项
        if (item.radioCheck != "") {
          for (let j = 0; j < childArray.length; j++) {
            if (childArray[j].recordsFormatID == item.radioCheck) {
              this.listFormats(item.recordsFormatID, item.radioCheck);
              if (childArray[j].description && childArray[j].description != "") {
                showNTFlag = true;
                groupContent = childArray[j].description;
                number = undefined;
              } else {
                number = childArray[j].point;
              }
              break;
            }
          }
        }
        //计算多选项
        if (item.checkedList.length > 0) {
          for (let i = 0; i < item.checkedList.length; i++) {
            w: for (let j = 0; j < childArray.length; j++) {
              if (childArray[j].recordsFormatID == item.checkedList[i]) {
                this.listFormats(item.recordsFormatID, item.checkedList[i]);
                //没有勾选NT选项,继续计算分数
                if (!showNTFlag) {
                  if (childArray[j].description && childArray[j].description != "") {
                    showNTFlag = true;
                    groupContent = childArray[j].description;
                    //需要显示NT，本组分数不再计算
                    number = undefined;
                  }
                  number += childArray[j].point;
                }
                break w;
              }
            }
          }
        }
        let content = number || (number == 0 ? undefined : groupContent);
        //组装呈现的内容（代替《总分》显示在页面上） number 为null是要显示为0
        if (content) {
          if (scoreDetail) {
            scoreDetail += "\t," + abbr + ":" + content;
          } else {
            scoreDetail = abbr + ":" + content;
          }
        }
      }
      return scoreDetail;
    },
    /**
     * description: 计算单个项的分数；
     * param {*} item
     * return {*}
     */
    calculateBlock(item) {
      let number = 0;
      let childArray = item.childrenArray;

      // 计算单选项
      if (item.radioCheck != "") {
        for (let j = 0; j < childArray.length; j++) {
          if (childArray[j].recordsFormatID == item.radioCheck) {
            number += childArray[j].point;
            this.listFormats(item.recordsFormatID, item.radioCheck);
          }
        }
      }
      //计算多选项
      if (item.checkedList.length > 0) {
        for (let i = 0; i < item.checkedList.length; i++) {
          w: for (let j = 0; j < childArray.length; j++) {
            if (childArray[j].recordsFormatID == item.checkedList[i]) {
              this.listFormats(item.recordsFormatID, item.checkedList[i]);
              number += childArray[j].point;
              break w;
            }
          }
        }
      }
      return number;
    },
    /**
     * description: 向this.recordsFormatList中push数据
     * params {*}
     * return {*}
     * param {*} recordsFormatID
     * param {*} assessListID
     */
    listFormats(recordsFormatID, assessListID) {
      if (this.listFormat) {
        let obj = {
          assessListGroupID: recordsFormatID,
          assessListID: assessListID,
          assessValue: 1,
        };
        this.recordsFormatList.push(obj);
      } else {
        this.recordsFormatList.push(assessListID);
      }
    },
    /**
     * description: 高亮未被选中的组
     * param {*} row
     * param {*} column
     * param {*} rowIndex
     * param {*} columnIndex
     * return {*}
     */
    highlight({ row, column, rowIndex, columnIndex }) {
      return {
        backgroundColor: !row.haveCheck && columnIndex == 0 ? "#fff7ee" : "",
      };
    },
    /**
     * description: 信息提示弹窗
     * param {*} messageContent
     * return {*}
     */
    showMessage(messageContent) {
      this._showMessage({
        dangerouslyUseHTMLString: true,
        message: messageContent,
        type: "",
        customClass: "show-message",
        offset: 300,
        duration: 5000,
      });
    },
    /**
     * description: 显示风险明细提示
     * param {*} item
     * return {*}
     */
    async showDetailMessage(item) {
      let message = "说明：" + item.showMessage;
      //只显示说明
      if (!item.source) {
        this.showMessage(message);
        return;
      }
      let sourceContent = "";
      let sourceContentString = `<p class="sourceContent-text">isReplaceSourceContent</p>`;
      //显示说明+风险来源
      sourceContent = await this.getSourceContent(item);

      if (!sourceContent) {
        this.showMessage(message);
        return;
      }
      this.showMessage(
        message + sourceContentString.replace("isReplaceSourceContent", `风险因子：</br>${sourceContent}`)
      );
    },
    /**
     * description: 获取风险来源信息
     * param {*} item
     * return {*}
     */
    async getSourceContent(item) {
      const result = await GetPatientRiskDetailSource({
        source: item.source,
      });
      if (this._common.isSuccess(result) && result.data) {
        return result.data;
      }
    },
    /**
     * description: 表单切换回调，重新获取表单内容
     * return {*}
     */
    getSourceRiskRecord() {
      this.checkResult = true;
      this.saveFlag = false;
      this.addFlag = 1;
      if (!this.nowrecordListID) {
        if (!this.warningFlag) {
          this._showTip("warning", "请选择评估表！");
        }
        return;
      }
      GetSourceRiskRecord({
        RecordListID: this.nowrecordListID,
        InpatientID: this.patient?.inpatientID ?? this.stationDepartmentBedDate.inpatientID,
        stationID: this.patient?.stationID ?? this.stationDepartmentBedDate.stationID,
      }).then((response) => {
        if (this._common.isSuccess(response)) {
          this.getUpdateData(response.data);
          this.calculateTotalNumber();
        }
      });
    },
    /**
     * description: 获取风险修改数据
     * param {*} data
     * return {*}
     */
    getUpdateData(data) {
      this.clickShowPointFlag = data.showPointFlag;
      //创建tableData
      let tableData = [];
      // 创建一个数组
      var childrenArray = [];
      //创建首层对象
      var obj = {
        content: "",
        recordsFormatID: -1,
        //记录被选中的单选按钮
        radioCheck: "",
        //记录被选中的复选按钮
        checkedList: [],
        haveCheck: true,
        //2022-02-17 GCS NT类型组装显示文本需要用到该字段
        description: undefined,
      };
      // 最后数组克隆
      //创建一个判断值
      var content = "";
      // 数据处理部分
      for (let i = 0; i < data.headerExtention.length; i++) {
        let item = data.headerExtention[i];
        const recordsFormatID = item[1].recordsFormatID + "";
        // 判断是否同属于一栏
        if (item[0].content == obj.content) {
          // 同一组
          // 创建对象: checkFlag :选中标记
          let lineItem = this.creatItem(item[1], data.rows[0][recordsFormatID]);

          if (lineItem.checkFlag) {
            if (lineItem.child.controlerType == "R") {
              obj.radioCheck = lineItem.child.recordsFormatID;
            }
            if (lineItem.child.controlerType == "C") {
              obj.checkedList.push(lineItem.child.recordsFormatID);
            }
          }
          childrenArray.push(this._common.clone(lineItem.child));
          if (i == data.headerExtention.length - 1) {
            let children = this._common.clone(childrenArray);
            obj.childrenArray = children;
            let objItem = this._common.clone(obj);
            tableData.push(objItem);
          }
          continue;
        } else {
          // 不相等
          //发现不属于同一组，将上一组中的所有内容放入tableData
          let children = this._common.clone(childrenArray);
          obj.childrenArray = children;
          let objItem = this._common.clone(obj);
          tableData.push(objItem);
          //循环到新的一组，将对象还原为初始状态
          childrenArray = [];
          obj.radioCheck = "";
          obj.checkedList = [];
          let lineItem = this.creatItem(item[1], data.rows[0][recordsFormatID]);
          if (lineItem.checkFlag) {
            if (lineItem.child.controlerType == "R") {
              obj.radioCheck = lineItem.child.recordsFormatID;
            }
            if (lineItem.child.controlerType == "C") {
              obj.checkedList.push(lineItem.child.recordsFormatID);
            }
          }
          childrenArray.push(this._common.clone(lineItem.child));
          obj.content = item[0].content;
          obj.showMessage = item[0].showMessage;
          obj.description = item[0].description;
          obj.recordsFormatID = item[0].recordsFormatID;
          //最后一位存入数组
          if (i == data.headerExtention.length - 1) {
            let children = this._common.clone(childrenArray);
            obj.childrenArray = children;
            let objItem = this._common.clone(obj);
            tableData.push(objItem);
          }
        }
      }
      //清除首个无用元素
      tableData.shift();
      this.conponentData.tableData = tableData;
      this.riskRangeStr = data.scoreLimit;
    },
    creatItem(item, row) {
      let recordsFormatID = item.recordsFormatID;
      let flag = row[recordsFormatID] == "V" ? true : false;
      let childItem = {
        assessListID: item.assessListID,
        point: item.point,
        recordsFormatID: recordsFormatID,
        content: item.content,
        controlerType: row.controlerType,
        showMessage: item.showMessage,
        description: item.description,
      };
      //如果
      return { child: childItem, checkFlag: flag };
    },
  },
};
</script>
<style>
.risk-rating-scale {
  width: 100%;
  height: 100%;
}
.risk-rating-scale .risk-rating-table {
  height: calc(100% - 153px);
  overflow: auto;
}
.risk-rating-scale .risk-rating-table.hideBar {
  height: calc(100% - 70px);
}
.risk-rating-scale .risk-rating-table td.check {
  padding: 0;
}
.risk-rating-scale .risk-rating-table td.check .cell {
  padding: 1px 1 !important;
}
.risk-rating-scale .risk-rating-table .risk-rating-tablestyle .risk-line {
  display: flex;
  height: auto;
  font-size: 16px;
  border-bottom: 1px solid #cccccc;
  width: 100%;
}
.risk-rating-scale .risk-rating-table .risk-rating-tablestyle .risk-line:last-child {
  border-bottom: 0;
}
.risk-line-point {
  text-align: center;
  width: 10%;
  border-right: 1px solid #cccccc;
  box-sizing: border-box;
}
.risk-line-content {
  width: 90%;
  box-sizing: border-box;
  display: flex;
  cursor: pointer;
}
.check .risk-line-content {
  cursor: default;
}

.risk-line-label {
  line-height: 20px;
}
.label {
  cursor: pointer;
}
.total-point {
  margin-bottom: 3px;
  border: 1px solid #ebeef5;
  border-radius: 3px;
  padding-left: 8px;
  /* height: 60px; */
}
.risk-rating-tablestyle {
  height: 100%;
  width: 100%;
  font-size: 10px;
}
.risk-rating-scale input[type="radio"] {
  margin: 0;
}
.total-range {
  display: inline-block;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: text-bottom;
}
.total-number-font {
  font-size: 16px;
  color: #333;
  font-family: Arial, Verdana, serif;
  font-weight: bold;
}
.total-number {
  font-size: 20px;
  color: red;
  font-family: Arial, Verdana, serif;
  line-height: 20px;
}
.time-label,
.time {
  float: right;
}
.time-label {
  margin-top: 5px;
}
.risk-rating-table .risk-rating-table .el-table::before {
  height: 100%;
  width: 100%;
}
.risk-line-content div,
.risk-line-content i {
  cursor: pointer;
}
.risk-line-input > input {
  display: none;
}
.component {
  text-align: left;
  height: 30px;
}
.sourceContent-text {
  color: #1cc6a3;
  margin-top: 5px !important;
}
</style>
