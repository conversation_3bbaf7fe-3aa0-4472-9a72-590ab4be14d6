/*
 * FilePath     : \src\utils\autoTrack.js
 * Author       : 孟昭永
 * Date         : 2023-11-16 09:16
 * LastEditors  : 孟昭永
 * LastEditTime : 2023-11-23 11:19
 * Description  : 行为日志自动跟踪上报
 * CodeIterationRecord:
 */
import { ReportTrackPageView } from "@/api/AutoTrack";
import { GetSettingSwitchByTypeCode } from "@/api/SettingDescription";
import common from "@/utils/common";
import datetimeUtil from "@/utils/datetimeUtil";
//Web 页面浏览
let trackPageView = async to => {
  // 根据配置写操作日志;
  let param = {
    SettingTypeCode: "AutoTrackSwitch"
  };
  GetSettingSwitchByTypeCode(param).then(result => {
    if (common.isSuccess(result)) {
      if (result.data) {
        let params = {
          router: to.fullPath,
          operationDateTime: datetimeUtil.getNow()
        };
        ReportTrackPageView(params);
      }
    }
  });
};

export default {
  trackPageView
};
