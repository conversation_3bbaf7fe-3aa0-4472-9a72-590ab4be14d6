/*
 * FilePath     : \src\utils\textEntryValidate.js
 * Author       : 郭鹏超
 * Date         : 2025-06-18 09:30
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-06-18 15:22
 * Description  : textEntry验证函数
 * CodeIterationRecord:
 */

import showTip from "@/utils/toast";

/**
 * 根据规则类型验证值是否符合要求
 * @param {String} ruleType - 规则类型
 * @param {Number} threshold - 阈值
 * @param {any} value - 要验证的值
 * @returns {String|undefined} 验证失败时的错误信息，验证通过返回undefined
 */
const validateRule = (ruleType, threshold, value) => {
  let checkResultMessage;

  switch (ruleType) {
    case "lowError":
      // 下限检查：值不能小于阈值
      if (value < threshold) {
        checkResultMessage = `输入值不能小于${threshold}`;
      }
      break;

    case "upError":
      // 上限检查：值不能大于阈值
      if (value > threshold) {
        checkResultMessage = `输入值不能大于${threshold}`;
      }
      break;

    case "decimal":
      // 小数位数检查
      const valueStr = String(value);
      // 如果包含小数点，检查小数部分长度
      if (valueStr.includes(".")) {
        const decimalPart = valueStr.split(".")[1];

        // 如果小数位数超过阈值，返回错误信息
        if (decimalPart && decimalPart.length > threshold) {
          checkResultMessage = `输入值的小数位数不能超过${threshold}`;
        }
      }
      break;
  }

  return checkResultMessage;
};

/**
 * 根据文本输入规则验证输入值
 * @param {Object} row - 当前行数据
 * @param {String} keyName - 要验证的字段名称
 */
const validateByTextEntry = (row, keyName) => {
  // 如果没有文本输入规则，直接返回
  const textEntryKeys = Object.keys(row.textEntry || {});
  if (textEntryKeys.length === 0) {
    return true;
  }

  // 获取当前字段的验证规则
  const textEntry = row.textEntry[keyName];
  if (!textEntry) {
    return true;
  }

  // 获取当前字段的值
  const value = row[keyName];
  if (value === undefined || value === null) {
    return true;
  }

  // 提取验证规则相关参数
  const ruleKeys = Object.keys(textEntry);
  const checkLevel = textEntry.checkLevel ?? 2; // 验证级别，1:错误(清空输入)，2:警告(保留输入)
  const checkResultSettingMessage = textEntry.checkResultMessage ?? ""; // 自定义错误信息

  // 遍历所有规则进行验证
  for (const ruleKey of ruleKeys) {
    // 跳过无效规则或非规则字段
    if (
      (!textEntry[ruleKey] && textEntry[ruleKey] !== 0) ||
      ruleKey === "checkLevel" ||
      ruleKey === "checkResultMessage"
    ) {
      continue;
    }

    // 验证规则并获取结果信息
    const checkResultMessage = validateRule(ruleKey, textEntry[ruleKey], value);
    if (!checkResultMessage) {
      continue;
    }

    // 显示验证结果消息
    const message = checkResultSettingMessage || checkResultMessage;
    if (checkLevel === 1) {
      showTip.showTip("error", message);
      row[keyName] = undefined; // 错误级别时清空输入值
    } else {
      showTip.showTip("warning", message); // 警告级别时保留输入值
    }
  }
};
export { validateByTextEntry };
