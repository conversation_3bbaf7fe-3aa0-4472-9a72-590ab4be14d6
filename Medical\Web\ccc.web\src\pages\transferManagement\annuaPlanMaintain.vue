<!--
 * FilePath     : \src\pages\transferManagement\annuaPlanMaintain.vue
 * Author       : 郭鹏超
 * Date         : 2020-12-02 09:37
 * LastEditors  : 苏军志
 * LastEditTime : 2020-12-03 20:51
 * Description  : 串年度计划
-->
<template>
  <iframe v-if="url" :src="url" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
</template>
<script>
import { getManagementUrl } from "@/utils/setting";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      url: "",
    };
  },
  computed: {
    ...mapGetters({
      token: "getToken",
    }),
  },
  created() {
    this.url = getManagementUrl() + "annuaPlanMaintain?token=" + this.token;
  },
};
</script>